import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

import 'poll_model.dart';

/// حالة الموضوع
enum TopicStatus {
  /// مفتوح
  open,

  /// مغلق
  closed,

  /// مميز
  featured,

  /// مثبت
  pinned,

  /// مثبت ومميز
  pinnedAndFeatured,

  /// مغلق ومثبت
  closedAndPinned,

  /// محذوف
  deleted,
}

/// نوع الموضوع
enum TopicType {
  /// عادي
  normal,

  /// إعلان
  announcement,

  /// استطلاع
  poll,

  /// سؤال
  question,

  /// مقال
  article,

  /// طلب عقار
  propertyRequest,

  /// مثبت
  pinned,

  /// مميز
  featured,
}

/// أولوية الموضوع
enum TopicPriority {
  /// منخفضة
  low,

  /// متوسطة
  medium,

  /// عالية
  high,

  /// عاجلة
  urgent,
}

/// نموذج موضوع المنتدى
class TopicModel extends Equatable {
  /// معرف الموضوع
  final String id;

  /// معرف الفئة
  final String categoryId;

  /// اسم الفئة
  final String categoryName;

  /// عنوان الموضوع
  final String title;

  /// محتوى الموضوع
  final String content;

  /// معرف المستخدم
  final String userId;

  /// اسم المستخدم
  final String userName;

  /// صورة المستخدم
  final String? userImage;

  /// حالة الموضوع
  final TopicStatus status;

  /// نوع الموضوع
  final TopicType type;

  /// أولوية الموضوع
  final TopicPriority priority;

  /// عدد المشاهدات
  final int viewsCount;

  /// عدد الردود
  final int repliesCount;

  /// عدد الإعجابات
  final int likesCount;

  /// عدد المشاركات
  final int sharesCount;

  /// عدد الإشارات المرجعية
  final int bookmarksCount;

  /// قائمة المستخدمين الذين أعجبوا بالموضوع
  final List<String>? likedBy;

  /// قائمة المستخدمين الذين أضافوا الموضوع للإشارات المرجعية
  final List<String>? bookmarkedBy;

  /// قائمة المستخدمين الذين يتابعون الموضوع
  final List<String>? followedBy;

  /// تاريخ إنشاء الموضوع
  final DateTime createdAt;

  /// تاريخ آخر تحديث للموضوع
  final DateTime updatedAt;

  /// تاريخ انتهاء الموضوع (للاستطلاعات والإعلانات)
  final DateTime? expiryDate;

  /// معرف آخر مستخدم رد على الموضوع
  final String? lastReplyUserId;

  /// اسم آخر مستخدم رد على الموضوع
  final String? lastReplyUserName;

  /// صورة آخر مستخدم رد على الموضوع
  final String? lastReplyUserImage;

  /// تاريخ آخر رد على الموضوع
  final DateTime? lastReplyDate;

  /// الوسوم المرتبطة بالموضوع
  final List<String>? tags;

  /// الصور المرفقة بالموضوع
  final List<String>? images;

  /// المرفقات الأخرى
  final List<Map<String, dynamic>>? attachments;

  /// خيارات الاستطلاع (للمواضيع من نوع استطلاع) - للتوافق مع الإصدار القديم
  final List<Map<String, dynamic>>? pollOptions;

  /// عدد الأصوات في الاستطلاع - للتوافق مع الإصدار القديم
  final int pollVotesCount;

  /// قائمة المستخدمين الذين صوتوا في الاستطلاع - للتوافق مع الإصدار القديم
  final List<String>? pollVotedBy;

  /// استطلاعات الرأي المرتبطة بالموضوع
  final List<PollModel>? polls;

  /// ما إذا كان الموضوع محلولاً (للمواضيع من نوع سؤال)
  final bool isSolved;

  /// معرف المشاركة التي تمثل الحل
  final String? solutionPostId;

  /// ما إذا كان الموضوع متعلقاً بعقار
  final bool isRelatedToEstate;

  /// معرف العقار المرتبط
  final String? relatedEstateId;

  /// عنوان العقار المرتبط
  final String? relatedEstateTitle;

  /// صورة العقار المرتبط
  final String? relatedEstateImage;

  /// موقع الموضوع (للمواضيع المتعلقة بمواقع محددة)
  final Map<String, dynamic>? location;

  /// ما إذا كان الموضوع يحتوي على محتوى للبالغين
  final bool isAdultContent;

  /// ما إذا كان الموضوع مختاراً من قبل فريق الإدارة
  final bool isStaffPicked;

  /// درجة التطابق في نتائج البحث
  final double? searchScore;

  /// تفاعلات المستخدمين مع الموضوع
  final Map<String, List<String>>? reactions;

  const TopicModel({
    required this.id,
    required this.categoryId,
    required this.categoryName,
    required this.title,
    required this.content,
    required this.userId,
    required this.userName,
    this.userImage,
    required this.status,
    this.type = TopicType.normal,
    this.priority = TopicPriority.medium,
    this.viewsCount = 0,
    this.repliesCount = 0,
    this.likesCount = 0,
    this.sharesCount = 0,
    this.bookmarksCount = 0,
    this.likedBy,
    this.bookmarkedBy,
    this.followedBy,
    required this.createdAt,
    required this.updatedAt,
    this.expiryDate,
    this.lastReplyUserId,
    this.lastReplyUserName,
    this.lastReplyUserImage,
    this.lastReplyDate,
    this.tags,
    this.images,
    this.attachments,
    this.pollOptions,
    this.pollVotesCount = 0,
    this.pollVotedBy,
    this.polls,
    this.isSolved = false,
    this.solutionPostId,
    this.isRelatedToEstate = false,
    this.relatedEstateId,
    this.relatedEstateTitle,
    this.relatedEstateImage,
    this.location,
    this.isAdultContent = false,
    this.isStaffPicked = false,
    this.searchScore,
    this.reactions,
  });

  /// إنشاء نسخة معدلة من الموضوع
  TopicModel copyWith({
    String? id,
    String? categoryId,
    String? categoryName,
    String? title,
    String? content,
    String? userId,
    String? userName,
    String? userImage,
    TopicStatus? status,
    TopicType? type,
    TopicPriority? priority,
    int? viewsCount,
    int? repliesCount,
    int? likesCount,
    int? sharesCount,
    int? bookmarksCount,
    List<String>? likedBy,
    List<String>? bookmarkedBy,
    List<String>? followedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? expiryDate,
    String? lastReplyUserId,
    String? lastReplyUserName,
    String? lastReplyUserImage,
    DateTime? lastReplyDate,
    List<String>? tags,
    List<String>? images,
    List<Map<String, dynamic>>? attachments,
    List<Map<String, dynamic>>? pollOptions,
    int? pollVotesCount,
    List<String>? pollVotedBy,
    List<PollModel>? polls,
    bool? isSolved,
    String? solutionPostId,
    bool? isRelatedToEstate,
    String? relatedEstateId,
    String? relatedEstateTitle,
    String? relatedEstateImage,
    Map<String, dynamic>? location,
    bool? isAdultContent,
    bool? isStaffPicked,
    double? searchScore,
    Map<String, List<String>>? reactions,
  }) {
    return TopicModel(
      id: id ?? this.id,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      title: title ?? this.title,
      content: content ?? this.content,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      status: status ?? this.status,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      viewsCount: viewsCount ?? this.viewsCount,
      repliesCount: repliesCount ?? this.repliesCount,
      likesCount: likesCount ?? this.likesCount,
      sharesCount: sharesCount ?? this.sharesCount,
      bookmarksCount: bookmarksCount ?? this.bookmarksCount,
      likedBy: likedBy ?? this.likedBy,
      bookmarkedBy: bookmarkedBy ?? this.bookmarkedBy,
      followedBy: followedBy ?? this.followedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      expiryDate: expiryDate ?? this.expiryDate,
      lastReplyUserId: lastReplyUserId ?? this.lastReplyUserId,
      lastReplyUserName: lastReplyUserName ?? this.lastReplyUserName,
      lastReplyUserImage: lastReplyUserImage ?? this.lastReplyUserImage,
      lastReplyDate: lastReplyDate ?? this.lastReplyDate,
      tags: tags ?? this.tags,
      images: images ?? this.images,
      attachments: attachments ?? this.attachments,
      pollOptions: pollOptions ?? this.pollOptions,
      pollVotesCount: pollVotesCount ?? this.pollVotesCount,
      pollVotedBy: pollVotedBy ?? this.pollVotedBy,
      polls: polls ?? this.polls,
      isSolved: isSolved ?? this.isSolved,
      solutionPostId: solutionPostId ?? this.solutionPostId,
      isRelatedToEstate: isRelatedToEstate ?? this.isRelatedToEstate,
      relatedEstateId: relatedEstateId ?? this.relatedEstateId,
      relatedEstateTitle: relatedEstateTitle ?? this.relatedEstateTitle,
      relatedEstateImage: relatedEstateImage ?? this.relatedEstateImage,
      location: location ?? this.location,
      isAdultContent: isAdultContent ?? this.isAdultContent,
      isStaffPicked: isStaffPicked ?? this.isStaffPicked,
      searchScore: searchScore ?? this.searchScore,
      reactions: reactions ?? this.reactions);
  }

  /// تحويل الموضوع إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'title': title,
      'content': content,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'status': status.index,
      'type': type.index,
      'priority': priority.index,
      'viewsCount': viewsCount,
      'repliesCount': repliesCount,
      'likesCount': likesCount,
      'sharesCount': sharesCount,
      'bookmarksCount': bookmarksCount,
      'likedBy': likedBy,
      'bookmarkedBy': bookmarkedBy,
      'followedBy': followedBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'expiryDate': expiryDate != null ? Timestamp.fromDate(expiryDate!) : null,
      'lastReplyUserId': lastReplyUserId,
      'lastReplyUserName': lastReplyUserName,
      'lastReplyUserImage': lastReplyUserImage,
      'lastReplyDate':
          lastReplyDate != null ? Timestamp.fromDate(lastReplyDate!) : null,
      'tags': tags,
      'images': images,
      'attachments': attachments,
      'pollOptions': pollOptions,
      'pollVotesCount': pollVotesCount,
      'pollVotedBy': pollVotedBy,
      'polls': polls?.map((poll) => poll.toMap()).toList(),
      'isSolved': isSolved,
      'solutionPostId': solutionPostId,
      'isRelatedToEstate': isRelatedToEstate,
      'relatedEstateId': relatedEstateId,
      'relatedEstateTitle': relatedEstateTitle,
      'relatedEstateImage': relatedEstateImage,
      'location': location,
      'isAdultContent': isAdultContent,
      'isStaffPicked': isStaffPicked,
      'reactions': reactions,
    };
  }

  /// إنشاء موضوع من خريطة
  factory TopicModel.fromMap(Map<String, dynamic> map) {
    return TopicModel(
      id: map['id'] ?? '',
      categoryId: map['categoryId'] ?? '',
      categoryName: map['categoryName'] ?? '',
      title: map['title'] ?? '',
      content: map['content'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'],
      status: map['status'] != null && map['status'] < TopicStatus.values.length
          ? TopicStatus.values[map['status']]
          : TopicStatus.open,
      type: map['type'] != null && map['type'] < TopicType.values.length
          ? TopicType.values[map['type']]
          : TopicType.normal,
      priority: map['priority'] != null &&
              map['priority'] < TopicPriority.values.length
          ? TopicPriority.values[map['priority']]
          : TopicPriority.medium,
      viewsCount: map['viewsCount'] ?? 0,
      repliesCount: map['repliesCount'] ?? 0,
      likesCount: map['likesCount'] ?? 0,
      sharesCount: map['sharesCount'] ?? 0,
      bookmarksCount: map['bookmarksCount'] ?? 0,
      likedBy:
          map['likedBy'] != null ? List<String>.from(map['likedBy']) : null,
      bookmarkedBy: map['bookmarkedBy'] != null
          ? List<String>.from(map['bookmarkedBy'])
          : null,
      followedBy: map['followedBy'] != null
          ? List<String>.from(map['followedBy'])
          : null,
      createdAt: map['createdAt'] is Timestamp
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp
          ? (map['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
      expiryDate: map['expiryDate'] is Timestamp
          ? (map['expiryDate'] as Timestamp).toDate()
          : null,
      lastReplyUserId: map['lastReplyUserId'],
      lastReplyUserName: map['lastReplyUserName'],
      lastReplyUserImage: map['lastReplyUserImage'],
      lastReplyDate: map['lastReplyDate'] is Timestamp
          ? (map['lastReplyDate'] as Timestamp).toDate()
          : null,
      tags: map['tags'] != null ? List<String>.from(map['tags']) : null,
      images: map['images'] != null ? List<String>.from(map['images']) : null,
      attachments: map['attachments'] != null
          ? List<Map<String, dynamic>>.from(
              map['attachments'].map((x) => Map<String, dynamic>.from(x)))
          : null,
      pollOptions: map['pollOptions'] != null
          ? List<Map<String, dynamic>>.from(
              map['pollOptions'].map((x) => Map<String, dynamic>.from(x)))
          : null,
      pollVotesCount: map['pollVotesCount'] ?? 0,
      pollVotedBy: map['pollVotedBy'] != null
          ? List<String>.from(map['pollVotedBy'])
          : null,
      polls: map['polls'] != null
          ? List<PollModel>.from(
              map['polls'].map((x) => PollModel.fromMap(x)))
          : null,
      isSolved: map['isSolved'] ?? false,
      solutionPostId: map['solutionPostId'],
      isRelatedToEstate: map['isRelatedToEstate'] ?? false,
      relatedEstateId: map['relatedEstateId'],
      relatedEstateTitle: map['relatedEstateTitle'],
      relatedEstateImage: map['relatedEstateImage'],
      location: map['location'] != null
          ? Map<String, dynamic>.from(map['location'])
          : null,
      isAdultContent: map['isAdultContent'] ?? false,
      isStaffPicked: map['isStaffPicked'] ?? false,
      searchScore: map['searchScore']?.toDouble(),
      reactions: map['reactions'] != null
          ? Map<String, List<String>>.from(map['reactions'].map(
              (key, value) => MapEntry(key, List<String>.from(value))))
          : null);
  }

  /// إنشاء موضوع من وثيقة فايربيز
  factory TopicModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return TopicModel(
        id: doc.id,
        categoryId: '',
        categoryName: '',
        title: '',
        content: '',
        userId: '',
        userName: '',
        status: TopicStatus.open,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());
    }
    return TopicModel.fromMap({...data, 'id': doc.id});
  }

  @override
  List<Object?> get props => [
        id,
        categoryId,
        categoryName,
        title,
        content,
        userId,
        userName,
        userImage,
        status,
        type,
        priority,
        viewsCount,
        repliesCount,
        likesCount,
        sharesCount,
        bookmarksCount,
        polls,
        likedBy,
        bookmarkedBy,
        followedBy,
        createdAt,
        updatedAt,
        expiryDate,
        lastReplyUserId,
        lastReplyUserName,
        lastReplyUserImage,
        lastReplyDate,
        tags,
        images,
        attachments,
        pollOptions,
        pollVotesCount,
        pollVotedBy,
        isSolved,
        solutionPostId,
        isRelatedToEstate,
        relatedEstateId,
        relatedEstateTitle,
        relatedEstateImage,
        location,
        isAdultContent,
        isStaffPicked,
        searchScore,
        reactions,
      ];
}
