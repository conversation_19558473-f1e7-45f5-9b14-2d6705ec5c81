import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../entities/estate.dart';
import 'premium_ads_algorithm_service.dart';

/// خدمة إدارة الإعلانات المدفوعة
class PremiumAdsManagerService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final PremiumAdsAlgorithmService _algorithmService = PremiumAdsAlgorithmService();

  /// تفعيل ميزة مدفوعة للإعلان
  Future<bool> activatePremiumFeature({
    required String adId,
    required String featureType,
    required int durationDays,
    required double price,
    String? paymentId,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من وجود الإعلان وملكيته
      final adDoc = await _firestore.collection('estates').doc(adId).get();
      if (!adDoc.exists) {
        throw Exception('الإعلان غير موجود');
      }

      final adData = adDoc.data()!;
      if (adData['userId'] != user.uid) {
        throw Exception('ليس لديك صلاحية لتعديل هذا الإعلان');
      }

      final now = DateTime.now();
      final endDate = now.add(Duration(days: durationDays));

      // تحديث الإعلان بالميزة المدفوعة
      final updateData = <String, dynamic>{
        featureType: true,
        '${featureType}StartDate': Timestamp.fromDate(now),
        '${featureType}EndDate': Timestamp.fromDate(endDate),
        'updatedAt': Timestamp.fromDate(now),
      };

      await _firestore.collection('estates').doc(adId).update(updateData);

      // تسجيل المعاملة
      await _logPremiumTransaction(
        adId: adId,
        userId: user.uid,
        featureType: featureType,
        durationDays: durationDays,
        price: price,
        paymentId: paymentId);

      // إنشاء إحصائيات للإعلان إذا لم تكن موجودة
      await _initializeAdStats(adId);

      return true;
    } catch (e) {
      print('Error activating premium feature: $e');
      rethrow;
    }
  }

  /// إلغاء تفعيل ميزة مدفوعة
  Future<bool> deactivatePremiumFeature({
    required String adId,
    required String featureType,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من ملكية الإعلان
      final adDoc = await _firestore.collection('estates').doc(adId).get();
      if (!adDoc.exists) return false;

      final adData = adDoc.data()!;
      if (adData['userId'] != user.uid) return false;

      // إلغاء تفعيل الميزة
      await _firestore.collection('estates').doc(adId).update({
        featureType: false,
        '${featureType}EndDate': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      print('Error deactivating premium feature: $e');
      return false;
    }
  }

  /// فحص وتحديث الإعلانات المنتهية الصلاحية
  Future<void> checkAndUpdateExpiredAds() async {
    try {
      final now = Timestamp.now();

      // البحث عن الإعلانات المنتهية الصلاحية
      final expiredAdsQuery = await _firestore
          .collection('estates')
          .where('endDate', isLessThan: now)
          .where('isAvailable', isEqualTo: true)
          .get();

      final batch = _firestore.batch();

      for (final doc in expiredAdsQuery.docs) {
        final adData = doc.data();

        // إلغاء تفعيل جميع الميزات المدفوعة
        batch.update(doc.reference, {
          'isAvailable': false,
          'vipBadge': false,
          'isFeatured': false,
          'kuwaitCornersPin': false,
          'pinnedOnHome': false,
          'movingAd': false,
          'expiredAt': now,
          'updatedAt': now,
        });

        // إشعار المالك بانتهاء الإعلان
        await _sendExpirationNotification(
          adData['userId'],
          doc.id,
          adData['title']);
      }

      await batch.commit();

      // فحص الميزات المدفوعة المنتهية الصلاحية
      await _checkExpiredPremiumFeatures();

    } catch (e) {
      print('Error checking expired ads: $e');
    }
  }

  /// فحص الميزات المدفوعة المنتهية الصلاحية
  Future<void> _checkExpiredPremiumFeatures() async {
    try {
      final now = Timestamp.now();
      final features = ['vipBadge', 'isFeatured', 'kuwaitCornersPin', 'pinnedOnHome', 'movingAd'];

      for (final feature in features) {
        final expiredQuery = await _firestore
            .collection('estates')
            .where('${feature}EndDate', isLessThan: now)
            .where(feature, isEqualTo: true)
            .get();

        final batch = _firestore.batch();

        for (final doc in expiredQuery.docs) {
          batch.update(doc.reference, {
            feature: false,
            'updatedAt': now,
          });

          // إشعار بانتهاء الميزة
          await _sendFeatureExpirationNotification(
            doc.data()['userId'],
            doc.id,
            feature);
        }

        await batch.commit();
      }
    } catch (e) {
      print('Error checking expired premium features: $e');
    }
  }

  /// الحصول على الإعلانات المدفوعة للمستخدم
  Future<List<Estate>> getUserPremiumAds(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('estates')
          .where('userId', isEqualTo: userId)
          .where('isAvailable', isEqualTo: true)
          .get();

      final premiumAds = <Estate>[];

      for (final doc in snapshot.docs) {
        final estate = _mapToEstate(doc.data(), doc.id);

        // التحقق من وجود ميزات مدفوعة نشطة
        if (_hasPremiumFeatures(estate)) {
          premiumAds.add(estate);
        }
      }

      return premiumAds;
    } catch (e) {
      print('Error getting user premium ads: $e');
      return [];
    }
  }

  /// التحقق من وجود ميزات مدفوعة
  bool _hasPremiumFeatures(Estate estate) {
    return estate.vipBadge == true ||
           estate.isFeatured == true ||
           estate.kuwaitCornersPin == true ||
           estate.pinnedOnHome == true ||
           estate.movingAd == true;
  }

  /// الحصول على إحصائيات الإعلانات المدفوعة
  Future<Map<String, dynamic>> getPremiumAdsStats(String userId) async {
    try {
      final userAds = await getUserPremiumAds(userId);

      int totalViews = 0;
      int totalClicks = 0;
      int totalInquiries = 0;
      int totalFavorites = 0;
      double totalSpent = 0;

      for (final ad in userAds) {
        // جلب إحصائيات الإعلان
        final statsDoc = await _firestore
            .collection('adStats')
            .doc(ad.id)
            .get();

        if (statsDoc.exists) {
          final stats = statsDoc.data()!;
          totalViews += (stats['views'] as num?)?.toInt() ?? 0;
          totalClicks += (stats['clicks'] as num?)?.toInt() ?? 0;
          totalInquiries += (stats['inquiries'] as num?)?.toInt() ?? 0;
          totalFavorites += (stats['favorites'] as num?)?.toInt() ?? 0;
        }

        // حساب المبلغ المنفق
        final transactionsSnapshot = await _firestore
            .collection('premiumTransactions')
            .where('adId', isEqualTo: ad.id)
            .where('userId', isEqualTo: userId)
            .get();

        for (final transaction in transactionsSnapshot.docs) {
          totalSpent += (transaction.data()['price'] as num?)?.toDouble() ?? 0;
        }
      }

      return {
        'totalAds': userAds.length,
        'totalViews': totalViews,
        'totalClicks': totalClicks,
        'totalInquiries': totalInquiries,
        'totalFavorites': totalFavorites,
        'totalSpent': totalSpent,
        'averageViewsPerAd': userAds.isNotEmpty ? totalViews / userAds.length : 0,
        'averageClicksPerAd': userAds.isNotEmpty ? totalClicks / userAds.length : 0,
        'ctr': totalViews > 0 ? (totalClicks / totalViews) * 100 : 0,
      };
    } catch (e) {
      print('Error getting premium ads stats: $e');
      return {};
    }
  }

  /// تجديد ميزة مدفوعة
  Future<bool> renewPremiumFeature({
    required String adId,
    required String featureType,
    required int additionalDays,
    required double price,
    String? paymentId,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final adDoc = await _firestore.collection('estates').doc(adId).get();
      if (!adDoc.exists) return false;

      final adData = adDoc.data()!;
      if (adData['userId'] != user.uid) return false;

      // حساب تاريخ الانتهاء الجديد
      final currentEndDate = (adData['${featureType}EndDate'] as Timestamp?)?.toDate() ?? DateTime.now();
      final newEndDate = currentEndDate.add(Duration(days: additionalDays));

      // تحديث تاريخ الانتهاء
      await _firestore.collection('estates').doc(adId).update({
        '${featureType}EndDate': Timestamp.fromDate(newEndDate),
        'updatedAt': Timestamp.now(),
      });

      // تسجيل معاملة التجديد
      await _logPremiumTransaction(
        adId: adId,
        userId: user.uid,
        featureType: featureType,
        durationDays: additionalDays,
        price: price,
        paymentId: paymentId,
        isRenewal: true);

      return true;
    } catch (e) {
      print('Error renewing premium feature: $e');
      return false;
    }
  }

  /// ترقية ميزة مدفوعة إلى مستوى أعلى
  Future<bool> upgradePremiumFeature({
    required String adId,
    required String fromFeature,
    required String toFeature,
    required double additionalPrice,
    String? paymentId,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final adDoc = await _firestore.collection('estates').doc(adId).get();
      if (!adDoc.exists) return false;

      final adData = adDoc.data()!;
      if (adData['userId'] != user.uid) return false;

      // الحصول على تاريخ الانتهاء الحالي
      final currentEndDate = (adData['${fromFeature}EndDate'] as Timestamp?)?.toDate() ?? DateTime.now();

      // إلغاء الميزة القديمة وتفعيل الجديدة
      await _firestore.collection('estates').doc(adId).update({
        fromFeature: false,
        toFeature: true,
        '${toFeature}StartDate': Timestamp.now(),
        '${toFeature}EndDate': Timestamp.fromDate(currentEndDate),
        'updatedAt': Timestamp.now(),
      });

      // تسجيل معاملة الترقية
      await _logPremiumTransaction(
        adId: adId,
        userId: user.uid,
        featureType: toFeature,
        durationDays: 0, // ترقية وليس تجديد
        price: additionalPrice,
        paymentId: paymentId,
        isUpgrade: true,
        fromFeature: fromFeature);

      return true;
    } catch (e) {
      print('Error upgrading premium feature: $e');
      return false;
    }
  }

  /// تسجيل معاملة مدفوعة
  Future<void> _logPremiumTransaction({
    required String adId,
    required String userId,
    required String featureType,
    required int durationDays,
    required double price,
    String? paymentId,
    bool isRenewal = false,
    bool isUpgrade = false,
    String? fromFeature,
  }) async {
    try {
      await _firestore.collection('premiumTransactions').add({
        'adId': adId,
        'userId': userId,
        'featureType': featureType,
        'durationDays': durationDays,
        'price': price,
        'paymentId': paymentId,
        'isRenewal': isRenewal,
        'isUpgrade': isUpgrade,
        'fromFeature': fromFeature,
        'timestamp': Timestamp.now(),
        'status': 'completed',
      });
    } catch (e) {
      print('Error logging premium transaction: $e');
    }
  }

  /// إنشاء إحصائيات للإعلان
  Future<void> _initializeAdStats(String adId) async {
    try {
      final statsDoc = await _firestore.collection('adStats').doc(adId).get();

      if (!statsDoc.exists) {
        await _firestore.collection('adStats').doc(adId).set({
          'views': 0,
          'clicks': 0,
          'inquiries': 0,
          'favorites': 0,
          'createdAt': Timestamp.now(),
          'lastViewed': null,
          'lastClicked': null,
          'lastInquiry': null,
          'lastFavorited': null,
        });
      }
    } catch (e) {
      print('Error initializing ad stats: $e');
    }
  }

  /// إرسال إشعار انتهاء الإعلان
  Future<void> _sendExpirationNotification(String userId, String adId, String adTitle) async {
    try {
      await _firestore.collection('notifications').add({
        'userId': userId,
        'type': 'ad_expired',
        'title': 'انتهت صلاحية إعلانك',
        'message': 'انتهت صلاحية إعلان "$adTitle". يمكنك تجديده من صفحة إعلاناتي.',
        'data': {
          'adId': adId,
          'adTitle': adTitle,
        },
        'isRead': false,
        'createdAt': Timestamp.now(),
      });
    } catch (e) {
      print('Error sending expiration notification: $e');
    }
  }

  /// إرسال إشعار انتهاء ميزة مدفوعة
  Future<void> _sendFeatureExpirationNotification(String userId, String adId, String feature) async {
    try {
      final featureNames = {
        'vipBadge': 'شارة VIP',
        'isFeatured': 'الإعلان المميز',
        'kuwaitCornersPin': 'التثبيت في الأقسام',
        'pinnedOnHome': 'التثبيت في الرئيسية',
        'movingAd': 'الإعلان المتحرك',
      };

      final featureName = featureNames[feature] ?? feature;

      await _firestore.collection('notifications').add({
        'userId': userId,
        'type': 'feature_expired',
        'title': 'انتهت صلاحية ميزة مدفوعة',
        'message': 'انتهت صلاحية ميزة "$featureName" في إعلانك. يمكنك تجديدها للحصول على مزيد من الظهور.',
        'data': {
          'adId': adId,
          'feature': feature,
          'featureName': featureName,
        },
        'isRead': false,
        'createdAt': Timestamp.now(),
      });
    } catch (e) {
      print('Error sending feature expiration notification: $e');
    }
  }

  /// الحصول على تقرير أداء الإعلانات المدفوعة
  Future<Map<String, dynamic>> getPremiumAdsPerformanceReport(String userId) async {
    try {
      final userAds = await getUserPremiumAds(userId);
      final Map<String, dynamic> report = {
        'totalAds': userAds.length,
        'featureBreakdown': <String, int>{},
        'performanceMetrics': <String, dynamic>{},
        'topPerformingAds': <Map<String, dynamic>>[],
        'recommendations': <String>[],
      };

      // تحليل توزيع الميزات
      for (final ad in userAds) {
        if (ad.vipBadge == true) {
          report['featureBreakdown']['vip'] = (report['featureBreakdown']['vip'] ?? 0) + 1;
        }
        if (ad.isFeatured == true) {
          report['featureBreakdown']['featured'] = (report['featureBreakdown']['featured'] ?? 0) + 1;
        }
        if (ad.kuwaitCornersPin == true) {
          report['featureBreakdown']['pinned'] = (report['featureBreakdown']['pinned'] ?? 0) + 1;
        }
        if (ad.pinnedOnHome == true) {
          report['featureBreakdown']['pinnedHome'] = (report['featureBreakdown']['pinnedHome'] ?? 0) + 1;
        }
        if (ad.movingAd == true) {
          report['featureBreakdown']['moving'] = (report['featureBreakdown']['moving'] ?? 0) + 1;
        }
      }

      // حساب مقاييس الأداء
      final stats = await getPremiumAdsStats(userId);
      report['performanceMetrics'] = stats;

      // العثور على أفضل الإعلانات أداءً
      final topAds = await _getTopPerformingAds(userAds);
      report['topPerformingAds'] = topAds;

      // إنشاء توصيات
      final recommendations = _generateRecommendations(stats, userAds);
      report['recommendations'] = recommendations;

      return report;
    } catch (e) {
      print('Error generating performance report: $e');
      return {};
    }
  }

  /// الحصول على أفضل الإعلانات أداءً
  Future<List<Map<String, dynamic>>> _getTopPerformingAds(List<Estate> ads) async {
    final List<Map<String, dynamic>> topAds = [];

    for (final ad in ads) {
      final statsDoc = await _firestore.collection('adStats').doc(ad.id).get();

      if (statsDoc.exists) {
        final stats = statsDoc.data()!;
        final views = (stats['views'] as num?)?.toInt() ?? 0;
        final clicks = (stats['clicks'] as num?)?.toInt() ?? 0;
        final inquiries = (stats['inquiries'] as num?)?.toInt() ?? 0;

        topAds.add({
          'id': ad.id,
          'title': ad.title,
          'views': views,
          'clicks': clicks,
          'inquiries': inquiries,
          'ctr': views > 0 ? (clicks / views) * 100 : 0,
          'score': views + (clicks * 2) + (inquiries * 5),
        });
      }
    }

    // ترتيب حسب النقاط
    topAds.sort((a, b) => (b['score'] as num).compareTo(a['score'] as num));

    return topAds.take(5).toList();
  }

  /// إنشاء توصيات لتحسين الأداء
  List<String> _generateRecommendations(Map<String, dynamic> stats, List<Estate> ads) {
    final List<String> recommendations = [];

    final totalViews = stats['totalViews'] as int? ?? 0;
    final totalClicks = stats['totalClicks'] as int? ?? 0;
    final ctr = stats['ctr'] as double? ?? 0;

    if (totalViews < 100) {
      recommendations.add('قم بترقية إعلاناتك إلى VIP لزيادة المشاهدات');
    }

    if (ctr < 2) {
      recommendations.add('حسّن عناوين إعلاناتك وأضف صور عالية الجودة لزيادة معدل النقر');
    }

    if (ads.length < 3) {
      recommendations.add('أضف المزيد من الإعلانات لزيادة فرص الوصول للعملاء');
    }

    if (totalClicks > 0 && (stats['totalInquiries'] as int? ?? 0) == 0) {
      recommendations.add('تأكد من وضوح معلومات التواصل في إعلاناتك');
    }

    return recommendations;
  }

  /// تحويل البيانات من Firestore إلى Estate object
  Estate _mapToEstate(Map<String, dynamic> data, String id) {
    return Estate(
      id: id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      location: data['location'] ?? '',
      photoUrls: List<String>.from(data['photoUrls'] ?? []),
      isFeatured: data['isFeatured'] ?? false,
      planType: data['planType'] ?? 'free',
      startDate: data['startDate'] != null
          ? DateTime.parse(data['startDate'])
          : null,
      endDate: data['endDate'] != null
          ? DateTime.parse(data['endDate'])
          : null,
      createdAt: data['createdAt'] != null
          ? DateTime.parse(data['createdAt'])
          : DateTime.now(),
      mainCategory: data['mainCategory'],
      subCategory: data['subCategory'],
      postedByUserType: data['postedByUserType'],
      hidePhone: data['hidePhone'] ?? false,
      extraPhones: List<String>.from(data['extraPhones'] ?? []),
      shareLocation: data['shareLocation'] ?? false,
      lat: data['lat']?.toDouble(),
      lng: data['lng']?.toDouble(),
      hasCentralAC: data['hasCentralAC'] ?? false,
      hasSecurity: data['hasSecurity'],
      allowPets: data['allowPets'],
      hasElevator: data['hasElevator'],
      hasSwimmingPool: data['hasSwimmingPool'],
      hasMaidRoom: data['hasMaidRoom'] ?? false,
      hasGarage: data['hasGarage'] ?? false,
      hasBalcony: data['hasBalcony'],
      isFullyFurnished: data['isFullyFurnished'],
      rebound: data['rebound'],
      numberOfRooms: data['numberOfRooms'],
      internalLocation: data['internalLocation'],
      salon: data['salon'],
      area: data['area']?.toDouble(),
      floorNumber: data['floorNumber'],
      numberOfBathrooms: data['numberOfBathrooms'],
      buildingAge: data['buildingAge'],
      numberOfFloors: data['numberOfFloors'],
      propertyType: data['propertyType'],
      autoRepublish: data['autoRepublish'] ?? false,
      kuwaitCornersPin: data['kuwaitCornersPin'] ?? false,
      movingAd: data['movingAd'] ?? false,
      vipBadge: data['vipBadge'] ?? false,
      pinnedOnHome: data['pinnedOnHome'] ?? false,
      discountCode: data['discountCode'],
      advertiserImage: data['advertiserImage'],
      advertiserName: data['advertiserName'],
      advertiserEmail: data['advertiserEmail'],
      advertiserRegistrationDate: data['advertiserRegistrationDate'] != null
          ? DateTime.parse(data['advertiserRegistrationDate'])
          : null,
      advertiserAdsCount: data['advertiserAdsCount'],
      ownerId: data['ownerId'],
      originalEstateId: data['originalEstateId'],
      isOriginal: data['isOriginal'] ?? true,
      copiedBy: List<String>.from(data['copiedBy'] ?? []),
      isPaymentVerified: data['isPaymentVerified'] ?? false,
      viewsCount: data['viewsCount'],
      inquiriesCount: data['inquiriesCount'],
      favoritesCount: data['favoritesCount'],
      latitude: data['latitude']?.toDouble(),
      longitude: data['longitude']?.toDouble(),
      rooms: data['rooms'],
      bathrooms: data['bathrooms'],
      floors: data['floors'],
      purpose: data['purpose'],
      hasGarden: data['hasGarden'],
      hasPool: data['hasPool'],
      hasDriverRoom: data['hasDriverRoom'],
      hasPrivateEntrance: data['hasPrivateEntrance'],
      hasEquippedKitchen: data['hasEquippedKitchen'],
      isAvailable: data['isAvailable'] ?? true);
  }
}
