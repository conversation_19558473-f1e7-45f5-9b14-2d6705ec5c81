import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart' as quill;

/// عارض النصوص المنسقة
class RichTextViewer extends StatefulWidget {
  /// النص المنسق
  final String content;

  /// ارتفاع العارض
  final double? height;

  /// عرض العارض
  final double? width;

  /// لون الخلفية
  final Color? backgroundColor;

  /// نمط النص
  final TextStyle? textStyle;

  /// تباعد النص
  final EdgeInsetsGeometry? padding;

  const RichTextViewer({
    super.key,
    required this.content,
    this.height,
    this.width,
    this.backgroundColor,
    this.textStyle,
    this.padding,
  });

  @override
  State<RichTextViewer> createState() => _RichTextViewerState();
}

class _RichTextViewerState extends State<RichTextViewer> {
  late quill.QuillController _controller;

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  @override
  void didUpdateWidget(RichTextViewer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.content != widget.content) {
      _initializeController();
    }
  }

  /// تهيئة المتحكم
  void _initializeController() {
    try {
      // محاولة تحليل النص كـ JSON
      _controller = quill.QuillController(
        document: quill.Document.fromJson(
          _parseJson(widget.content)),
        selection: const TextSelection.collapsed(offset: 0));
    } catch (e) {
      // إذا فشل التحليل، استخدم النص العادي
      _controller = quill.QuillController(
        document: quill.Document.fromJson(
          [
            {"insert": widget.content}
          ]),
        selection: const TextSelection.collapsed(offset: 0));
    }
  }

  /// تحليل النص كـ JSON
  List<dynamic> _parseJson(String content) {
    try {
      if (content.startsWith('[') && content.endsWith(']')) {
        return quill.jsonDecode(content);
      } else {
        return [
          {"insert": content}
        ];
      }
    } catch (e) {
      return [
        {"insert": content}
      ];
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      width: widget.width,
      padding: widget.padding ?? const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.transparent,
        borderRadius: BorderRadius.circular(8)),
      child: quill.QuillEditor(
        controller: _controller,
        scrollController: ScrollController(),
        scrollable: true,
        focusNode: FocusNode(),
        autoFocus: false,
        readOnly: true,
        expands: false,
        padding: EdgeInsets.zero,
        customStyles: quill.DefaultStyles(
          paragraph: quill.DefaultTextBlockStyle(
            widget.textStyle ??
                const TextStyle(
                  fontSize: 14,
                  height: 1.5,
                  color: Colors.black87),
            const quill.VerticalSpacing(0, 0),
            const quill.VerticalSpacing(0, 0),
            null))));
  }
}
