# تطبيق Krea - منصة العقارات الكويتية المتقدمة

<div align="center">

![Krea Logo](assets/icons/app_icon.png)

**منصة العقارات الأكثر تطوراً في دولة الكويت**

[![Flutter](https://img.shields.io/badge/Flutter-3.6.1-blue.svg)](https://flutter.dev/)
[![Firebase](https://img.shields.io/badge/Firebase-Latest-orange.svg)](https://firebase.google.com/)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-1.0.0-green.svg)](pubspec.yaml)

[الموقع الرسمي](https://krea.app) • [سياسة الخصوصية](https://privacy.krea.app) • [الدعم الفني](mailto:<EMAIL>)

**تطوير: Codnet Moroccan**

</div>

---

## 📋 نظرة عامة

تطبيق **Krea** هو منصة عقارية متقدمة تم تطويرها بواسطة فريق **Codnet Moroccan** خصيصاً للسوق الكويتي. يوفر التطبيق نظاماً شاملاً لإدارة العقارات مع ميزات متقدمة للبحث والتفاعل الاجتماعي وإدارة الأعمال.

### 🎯 الهدف الرئيسي
إنشاء منصة عقارية متكاملة تجمع بين سهولة الاستخدام والتقنيات المتقدمة لخدمة جميع أطراف السوق العقاري الكويتي.

### 🌟 المميزات المطبقة فعلياً
- **نظام مستخدمين متقدم**: 4 أنواع مستخدمين مع صلاحيات مخصصة
- **منتدى عقاري تفاعلي**: نظام مناقشات متقدم مع استطلاعات وتفاعلات
- **نظام دفع ومض**: تكامل كامل مع خدمة الدفع الكويتية
- **إدارة العقارات المتقدمة**: نسخ الإعلانات، إعلانات مدفوعة، تحليلات
- **نظام طلبات العقارات**: للباحثين مع إمكانية الرد من المعلنين
- **إدارة المشاريع والعملاء**: أدوات CRM متكاملة للشركات
- **نظام الولاء والمكافآت**: برنامج نقاط شامل مع مكافآت حقيقية

---

## 👥 أنواع المستخدمين

### 1. 🔍 الباحثون عن العقارات (Seekers)
**الوصف**: المستخدمون الذين يبحثون عن عقارات للشراء أو الإيجار

**الصلاحيات المطبقة**:
- البحث في جميع العقارات مع فلترة متقدمة
- إنشاء طلبات عقارات مخصصة (PropertyRequestPage)
- حفظ العقارات في المفضلة (FavoritesPage)
- التواصل مع المعلنين عبر الواتساب والهاتف
- المشاركة في المنتدى العقاري مع نظام التفاعلات

**الصفحات المطبقة**:
- الصفحة الرئيسية (HomePage) مع البانرات والفلترة
- البحث المتقدم (AdvancedSearchPage) والفلترة الذكية
- تفاصيل العقارات مع معرض الصور
- صفحة المفضلة (FavoritesPage)
- إنشاء طلبات العقارات (CreatePropertyRequestPage)
- المنتدى العقاري مع الاستطلاعات والتفاعلات

### 2. 💼 المستثمرون/الوكلاء العقاريون (Agents)
**الوصف**: الوسطاء العقاريون والمستثمرون الذين يتاجرون في العقارات

**الصلاحيات المطبقة**:
- جميع صلاحيات الباحثين
- إضافة وإدارة الإعلانات العقارية (ImprovedAdCreationEntry)
- نسخ إعلانات الإيجار وإعادة نشرها (CopiedEstateDetailsPage)
- عرض طلبات العقارات والرد عليها (PropertyRequestsPage)
- نظام دفع ومض للإعلانات المدفوعة
- إدارة العملاء والمواعيد

**الصفحات المطبقة**:
- جميع صفحات الباحثين
- إضافة وتعديل العقارات مع نظام الدفع
- إدارة العقارات الخاصة (AllEstatesPage)
- نسخ العقارات للإيجار فقط
- عرض والرد على طلبات العقارات
- إدارة العملاء والمشاريع

### 3. 🏠 ملاك العقارات (Owners)
**الوصف**: أصحاب العقارات الذين يرغبون في بيع أو تأجير ممتلكاتهم

**الصلاحيات المطبقة**:
- جميع صلاحيات الباحثين
- إضافة وإدارة عقاراتهم الشخصية
- عرض طلبات العقارات والرد عليها
- تتبع أداء إعلاناتهم مع الإحصائيات
- نظام دفع ومض للترقية

**الصفحات المطبقة**:
- جميع صفحات الباحثين
- إضافة وتعديل العقارات الشخصية
- لوحة تحكم المالك مع الإحصائيات
- طلبات العقارات (عرض والرد)
- تحليلات أداء العقارات

### 4. 🏢 الشركات العقارية (Companies)
**الوصف**: الشركات المتخصصة في الخدمات العقارية والتطوير

**الصلاحيات المطبقة**:
- جميع صلاحيات المستثمرين (بدون المفضلة)
- إدارة شاملة للمشاريع العقارية
- إدارة فرق العمل والموظفين (TeamMembers)
- الوصول للتقارير التفصيلية والتحليلات
- إدارة قاعدة عملاء واسعة مع CRM متكامل

**الصفحات المطبقة**:
- الصفحة الرئيسية والبحث المتقدم
- إدارة العقارات والمشاريع
- إدارة الفرق والموظفين
- إدارة العملاء والتفاعلات
- التقارير والتحليلات المتقدمة
- لوحة تحكم الشركة مع KPIs

---

## 🏘️ أنواع العقارات المدعومة (المطبقة فعلياً)

### التصنيفات الرئيسية المطبقة
1. **عقار للبيع** - العقارات المعروضة للبيع
2. **عقار للايجار** - العقارات المتاحة للإيجار (مع نظام النسخ للمستثمرين)
3. **عقار للبدل** - العقارات المعروضة للمبادلة
4. **عقار دولي** - العقارات خارج الكويت
5. **تجاري** - العقارات التجارية والاستثمارية

### أنواع العقارات المطبقة
- **شقة** - الشقق السكنية بجميع أحجامها
- **بيت** - البيوت والمنازل السكنية (تم تغيير "فيلا" إلى "منزل")
- **اراضي** - الأراضي السكنية والتجارية والاستثمارية
- **مكتب** - المكاتب التجارية والإدارية
- **محل** - المحلات التجارية والمتاجر
- **مخزن** - المخازن والمستودعات (تم تغيير "مستودع" إلى "مخزن")
- **مزارع** - المزارع والأراضي الزراعية
- **عمارة** - العمارات السكنية والتجارية

### فئات البحث السريع المطبقة
- **الكل** - جميع العقارات
- **للبيع** - فلترة حسب التصنيف الرئيسي
- **للإيجار** - فلترة حسب التصنيف الرئيسي
- **للبدل** - فلترة حسب التصنيف الرئيسي
- **منازل** - البحث في التصنيف الفرعي عن "بيت"
- **شقق** - البحث في التصنيف الفرعي عن "شقة"
- **أراضي** - البحث في التصنيف الفرعي عن "اراضي"
- **تجاري** - العقارات التجارية
- **مكاتب** - البحث في التصنيف الفرعي عن "مكتب"
- **دولي** - العقارات الدولية

### المناطق المدعومة (محافظات الكويت)
- **الكويت العاصمة**
- **حولي**
- **الفروانية**
- **مبارك الكبير**
- **الأحمدي**
- **الجهراء**

---

## 🛠️ التقنيات المستخدمة

### إطار العمل الأساسي
- **Flutter 3.6.1** - تطوير التطبيقات متعددة المنصات
- **Dart** - لغة البرمجة الأساسية

### قاعدة البيانات والخدمات السحابية
- **Firebase Core** - الخدمات الأساسية
- **Cloud Firestore** - قاعدة البيانات الرئيسية
- **Firebase Auth** - نظام المصادقة والتحقق
- **Firebase Storage** - تخزين الملفات والصور
- **Firebase Messaging** - الإشعارات الفورية
- **Firebase Analytics** - تحليلات الاستخدام
- **Firebase Database** - قاعدة البيانات الفورية
- **Firebase Dynamic Links** - الروابط الديناميكية

### إدارة الحالة والتنقل
- **Flutter BLoC** - إدارة حالة التطبيق
- **Provider** - إدارة الحالة المحلية
- **Go Router** - نظام التنقل المتقدم

### واجهة المستخدم والتصميم
- **Google Fonts (Cairo)** - الخط الأساسي للتطبيق
- **Flutter ScreenUtil** - التصميم المتجاوب
- **Shimmer** - تأثيرات التحميل
- **Lottie** - الرسوم المتحركة
- **Cached Network Image** - تحسين عرض الصور

### الخدمات الخارجية والدفع
- **خدمة ومض الكويتية** - نظام الدفع الآمن
- **Google Maps** - الخرائط والمواقع
- **Google Sign In** - تسجيل الدخول بجوجل

### الأمان والتشفير
- **Flutter Secure Storage** - التخزين الآمن
- **Local Auth** - المصادقة البيومترية
- **Encrypt** - تشفير البيانات الحساسة
- **Crypto** - العمليات التشفيرية

### الأدوات والمساعدات
- **Image Picker/Cropper** - اختيار وتعديل الصور
- **File Picker** - اختيار الملفات
- **Share Plus** - مشاركة المحتوى
- **URL Launcher** - فتح الروابط الخارجية
- **Connectivity Plus** - مراقبة الاتصال
- **Device Info Plus** - معلومات الجهاز

---

## 📱 الصفحات والمكونات المطبقة فعلياً

### صفحات المصادقة والبداية المطبقة
- **صفحة البداية (SplashPage)** - شاشة التحميل مع شعار التطبيق
- **صفحة تسجيل الدخول** - مع دعم Google Sign-In ونظام التحقق
- **صفحة التحقق من المستخدم** - تحديد نوع المستخدم والصلاحيات

### الصفحات الأساسية المطبقة
- **الصفحة الرئيسية (HomePage)** - عرض العقارات مع البانرات والفلترة السريعة
- **صفحة جميع العقارات (AllEstatesPage)** - عرض شامل للعقارات مع التحميل التدريجي
- **صفحة تفاصيل العقار** - عرض تفصيلي مع معرض الصور والتواصل
- **صفحة البحث المتقدم (AdvancedSearchPage)** - فلترة شاملة ومتقدمة
- **صفحة النتائج المفلترة (FilteredResultsPage)** - عرض نتائج البحث والفلترة
- **صفحة اختيار الفئات المحسنة (ImprovedCategorySelectionPage)** - اختيار فئات العقارات

### صفحات إدارة العقارات المطبقة
- **صفحة إضافة عقار محسنة (ImprovedAdCreationEntry)** - إنشاء إعلانات مع نظام الدفع
- **صفحة تفاصيل العقار المنسوخ (CopiedEstateDetailsPage)** - عرض وإدارة العقارات المنسوخة
- **صفحة المفضلة (FavoritesPage)** - إدارة العقارات المفضلة

### صفحات طلبات العقارات المطبقة
- **صفحة إنشاء طلب عقار (CreatePropertyRequestPage)** - للباحثين لإنشاء طلبات مخصصة
- **صفحة طلبات العقارات (PropertyRequestsPage)** - عرض وإدارة الطلبات للمعلنين

### صفحات المنتدى العقاري المطبقة
- **صفحة المنتدى الرئيسية** - عرض الموضوعات والفئات
- **صفحة إنشاء موضوع** - إضافة موضوعات جديدة مع الاستطلاعات
- **صفحة تفاصيل الموضوع** - عرض المناقشات والردود مع التفاعلات
- **صفحة فئات المنتدى** - تصنيفات المنتدى العقاري

### المكونات المطبقة
- **CustomAppBar** - شريط التطبيق المخصص
- **CustomDrawer** - القائمة الجانبية المخصصة حسب نوع المستخدم
- **PremiumEstateCard** - بطاقة العقارات المميزة
- **EstateCard** - بطاقة العقارات العادية
- **LoadingWidget** - مكون التحميل مع Shimmer
- **EmptyStateWidget** - مكون الحالة الفارغة
- **LazyLoadingWidget** - التحميل التدريجي للبيانات
- **ResponsiveLayout** - التخطيط المتجاوب للشاشات المختلفة

---

## 🔧 الخدمات والمكونات التقنية المطبقة

### خدمات الدفع والمعاملات المطبقة
- **WamdPaymentService** - خدمة الدفع عبر ومض الكويتية مع المراجعة اليدوية
- **Payment Transaction Management** - إدارة حالات الدفع والتحقق من الصحة
- **Admin Notification System** - إشعارات الإدارة للمراجعة والتحقق

### خدمات إدارة الحالة والتخزين المطبقة
- **EnhancedCacheService** - خدمة التخزين المؤقت المحسنة للعقارات
- **EnhancedStateManagementService** - إدارة حالة التطبيق المتقدمة
- **Estate BLoC** - إدارة حالة العقارات مع التحميل التدريجي
- **Property Request Provider** - مزود حالة طلبات العقارات

### خدمات العقارات والمحتوى المطبقة
- **Estate Factory & Converter** - مصنع ومحول كيانات العقارات
- **Smart Filter Model** - نموذج الفلترة الذكية للبحث
- **User Interface Customization Service** - تخصيص الواجهة حسب نوع المستخدم
- **Responsive Utils** - أدوات التصميم المتجاوب

### خدمات المنتدى والتفاعل المطبقة
- **Forum Topic Model** - نموذج موضوعات المنتدى مع الاستطلاعات
- **Topic Status & Priority Management** - إدارة حالة وأولوية الموضوعات
- **User Reactions System** - نظام تفاعلات المستخدمين (إعجاب، مشاركة، إشارة مرجعية)
- **Poll & Voting System** - نظام الاستطلاعات والتصويت

### خدمات التحسين والأداء المطبقة
- **Lazy Loading Widget** - التحميل التدريجي للبيانات
- **Cached Network Image** - تحسين عرض الصور مع التخزين المؤقت
- **Shimmer Loading Effects** - تأثيرات التحميل المتقدمة
- **Staggered Animations** - الرسوم المتحركة المتدرجة

### خدمات المستخدمين والصلاحيات المطبقة
- **User Type Management** - إدارة أنواع المستخدمين الأربعة
- **User Verification Status** - حالات التحقق من المستخدمين
- **Subscription Management** - إدارة الاشتراكات وانتهاء الصلاحية
- **User Statistics Tracking** - تتبع إحصائيات المستخدمين (إعلانات، مفضلة، مشاهدات)

---

## 🎨 التصميم وتجربة المستخدم

### نظام الألوان
- **اللون الأساسي**: تدرجات اللون الأخضر (#4CAF50)
- **الألوان الثانوية**: متناسقة مع الهوية البصرية الكويتية
- **الوضع المظلم**: دعم كامل للوضع الليلي مع ألوان محسنة

### الخطوط والطباعة
- **الخط الأساسي**: Cairo من Google Fonts لجميع النصوص
- **اتجاه النص**: من اليمين إلى اليسار (RTL) للعربية
- **اللغة**: العربية بشكل أساسي مع دعم محدود للإنجليزية

### المكونات التفاعلية
- **الرسوم المتحركة**: تأثيرات سلسة ومتدرجة باستخدام Lottie
- **الأشكال الهندسية**: عناصر تصميمية متحركة في الخلفيات
- **الأيقونات**: مجموعة شاملة من الأيقونات المخصصة والمتجاوبة

### التصميم المتجاوب
- **دعم جميع أحجام الشاشات**: من الهواتف الصغيرة إلى الأجهزة اللوحية
- **تخطيط مرن**: يتكيف مع اتجاه الشاشة (عمودي/أفقي)
- **كثافة المحتوى**: تحسين استغلال المساحة حسب حجم الشاشة

---

## 💳 نظام الدفع ومض المطبق فعلياً

### خدمة الدفع المطبقة (WamdPaymentService)
- **مزود الخدمة**: خدمة ومض الكويتية (+965 9929 8821)
- **طريقة الدفع**: تحويل سريع عبر ومض أو تطبيق البنك
- **رقم ومض**: 99298821
- **المراجعة**: نظام مراجعة يدوية خلال 24-48 ساعة

### آلية الدفع المطبقة
1. **إنشاء طلب دفع**: createPaymentRequest() مع تفاصيل العقار والمبلغ
2. **تعليمات الدفع**: عرض خطوات التحويل عبر ومض
3. **تأكيد المستخدم**: confirmPayment() بعد إتمام التحويل
4. **المراجعة الإدارية**: إشعار للإدارة للتحقق من الدفع
5. **التفعيل**: تفعيل الإعلان بعد التحقق من الدفع

### حالات الدفع المطبقة
- **pending**: في انتظار الدفع
- **pending_review**: في انتظار المراجعة الإدارية
- **verified**: تم التحقق والتفعيل
- **cancelled**: ملغي من المستخدم
- **expired**: منتهي الصلاحية (24 ساعة)

### ميزات النظام المطبقة
- **انتهاء الصلاحية**: 24 ساعة لإتمام الدفع
- **إشعارات الإدارة**: تنبيهات تلقائية للمراجعة
- **تتبع المدفوعات**: getUserPayments() لعرض تاريخ المدفوعات
- **تنظيف تلقائي**: cleanupExpiredPayments() للمدفوعات المنتهية
- **أمان متقدم**: التحقق من هوية المستخدم وصلاحية الطلب

---

## 🔐 الأمان والخصوصية

### حماية البيانات
- **التشفير**: AES-256 لجميع البيانات الحساسة
- **التخزين الآمن**: Flutter Secure Storage للمعلومات الحساسة
- **المصادقة الثنائية**: دعم TOTP و SMS للحسابات المهمة
- **جلسات آمنة**: انتهاء صلاحية الجلسات تلقائياً

### الخصوصية
- **سياسة خصوصية شاملة**: متوافقة مع القوانين الكويتية والدولية
- **شروط الاستخدام**: واضحة ومفصلة باللغة العربية
- **موافقة المستخدم**: موافقة صريحة على جمع واستخدام البيانات
- **حق النسيان**: إمكانية حذف البيانات الشخصية نهائياً

### مراقبة الأمان
- **مراقبة النشاط المشبوه**: كشف المحاولات غير المشروعة تلقائياً
- **سجلات الأمان**: تتبع جميع العمليات الحساسة مع الطوابع الزمنية
- **النسخ الاحتياطية**: نسخ احتياطية يومية مع تشفير كامل
- **استجابة الحوادث**: خطة استجابة سريعة للثغرات الأمنية

---

## 📊 التحليلات والتقارير

### تحليلات المستخدمين
- **إحصائيات الاستخدام**: عدد المستخدمين النشطين يومياً وشهرياً
- **سلوك المستخدم**: تتبع التفاعل مع الصفحات والميزات
- **التحويلات**: معدلات التحويل من زائر إلى مستخدم مسجل
- **الاحتفاظ**: معدلات عودة المستخدمين واستمرارية الاستخدام

### تحليلات العقارات
- **أداء الإعلانات**: مشاهدات، استفسارات، معدلات التحويل
- **اتجاهات السوق**: تحليل أسعار العقارات حسب المنطقة والنوع
- **تقارير مخصصة**: تقارير حسب الفترة الزمنية والمنطقة الجغرافية
- **مؤشرات الطلب**: تحليل الطلب على أنواع العقارات المختلفة

### تحليلات الأعمال
- **الإيرادات**: تتبع الإيرادات من الاشتراكات والإعلانات المدفوعة
- **النمو**: معدلات نمو المستخدمين والمحتوى والإيرادات
- **الأداء**: مؤشرات الأداء الرئيسية (KPIs) للأعمال
- **التنبؤات**: توقعات النمو والإيرادات المستقبلية

---

## 🚀 التحسينات والأداء

### تحسين الأداء
- **التخزين المؤقت**: Redis للبيانات المتكررة وتحسين سرعة الاستجابة
- **ضغط الصور**: تحسين تلقائي للصور مع الحفاظ على الجودة
- **التحميل التدريجي**: Lazy Loading للمحتوى حسب الحاجة
- **CDN**: شبكة توزيع المحتوى لتسريع تحميل الصور والملفات

### تحسين تجربة المستخدم
- **واجهة سريعة الاستجابة**: تفاعل فوري مع إجراءات المستخدم
- **تحديثات فورية**: تحديث الحالة بدون إعادة تحميل الصفحة
- **رسوم متحركة سلسة**: انتقالات طبيعية بين الصفحات والحالات
- **حالات التحميل**: مؤشرات تحميل جذابة وواضحة

### مراقبة الأداء
- **مراقبة الأخطاء**: Crashlytics لكشف وإصلاح الأخطاء تلقائياً
- **مراقبة السرعة**: قياس أوقات الاستجابة وتحسينها
- **تحليل الاستخدام**: Firebase Analytics لفهم سلوك المستخدمين
- **تنبيهات الأداء**: إشعارات فورية عند تدهور الأداء

---

## 👨‍💻 فريق التطوير - Codnet Moroccan

### نبذة عن الفريق
**Codnet Moroccan** هو فريق تطوير متخصص في بناء التطبيقات المتقدمة والحلول التقنية المبتكرة. يتمتع الفريق بخبرة واسعة في تطوير التطبيقات متعددة المنصات باستخدام أحدث التقنيات.

### خبرات الفريق
- **تطوير التطبيقات**: Flutter, React Native, Native Development
- **الخدمات السحابية**: Firebase, AWS, Google Cloud
- **قواعد البيانات**: Firestore, MongoDB, PostgreSQL
- **التصميم والواجهات**: UI/UX Design, Material Design
- **الأمان والحماية**: Authentication, Encryption, Security Best Practices
- **التحليلات**: Analytics, Performance Monitoring, Data Visualization

### اللغات المدعومة
- **العربية**: اللغة الأساسية للتطوير والدعم
- **الفرنسية**: دعم كامل للتواصل والتطوير
- **الإنجليزية**: دعم تقني ومراجع فنية

### التواصل مع الفريق
- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: www.codnet.ma
- **LinkedIn**: Codnet Moroccan
- **GitHub**: @codnet-moroccan

---

## 📞 الدعم والتواصل

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +965 9929 8821
- **العنوان**: دولة الكويت
- **ساعات العمل**: الأحد - الخميس، 9:00 ص - 6:00 م

### الدعم الفني
- **ساعات العمل**: 24/7 للمشاكل الحرجة والطوارئ
- **وقت الاستجابة**: خلال ساعة للمشاكل الحرجة، 24 ساعة للاستفسارات العامة
- **اللغات المدعومة**: العربية (أساسي) والإنجليزية والفرنسية
- **قنوات الدعم**: البريد الإلكتروني، الهاتف، الدردشة المباشرة
- **فريق التطوير**: Codnet Moroccan - خبراء في تطوير التطبيقات المتقدمة

### التحديثات والصيانة
- **تحديثات دورية**: إصدارات جديدة شهرياً مع ميزات وتحسينات
- **صيانة مجدولة**: إشعار مسبق 48 ساعة للصيانة المخططة
- **إصلاحات عاجلة**: استجابة فورية للمشاكل الحرجة خلال ساعات
- **نسخ احتياطية**: نسخ احتياطية يومية مع إمكانية الاستعادة السريعة

---

## 📄 الترخيص والحقوق

### حقوق الطبع والنشر
© 2025 Krea. جميع الحقوق محفوظة.

### الترخيص
هذا التطبيق محمي بحقوق الطبع والنشر وهو ملكية خاصة لشركة Krea. الاستخدام مقيد بشروط الترخيص المحددة.

### شروط الاستخدام
- يُمنع نسخ أو توزيع أو تعديل التطبيق بدون إذن مكتوب صريح
- الاستخدام مقتصر على الأغراض المشروعة والقانونية فقط
- الشركة غير مسؤولة عن سوء الاستخدام أو الأضرار الناتجة عنه
- يحق للشركة تعديل الشروط في أي وقت مع إشعار المستخدمين

---

## 🔄 التحديثات والتطوير المستقبلي

### الميزات القادمة (2025)
- **الجولات الافتراضية**: تقنية VR/AR لاستكشاف العقارات عن بُعد
- **الذكاء الاصطناعي**: توصيات ذكية للعقارات حسب تفضيلات المستخدم
- **التكامل مع IoT**: ربط الأجهزة الذكية في العقارات للمراقبة والتحكم
- **البلوك تشين**: توثيق العقود والمعاملات بتقنية البلوك تشين

### التحسينات المخططة
- **تحسين الأداء**: تسريع التطبيق بنسبة 40% وتقليل استهلاك البطارية
- **ميزات جديدة**: إضافة أدوات تحليل متقدمة للسوق العقاري
- **توسيع الخدمات**: دعم دول مجلس التعاون الخليجي الأخرى
- **تطوير الواجهة**: تحديث شامل للتصميم وتحسين تجربة المستخدم

### خارطة الطريق
- **Q1 2025**: إطلاق الجولات الافتراضية وتحسين الأداء
- **Q2 2025**: تطبيق الذكاء الاصطناعي والتوصيات الذكية
- **Q3 2025**: التوسع الإقليمي لدول الخليج
- **Q4 2025**: تقنيات البلوك تشين والعقود الذكية

---

<div align="center">

**تطبيق Krea - حيث تلتقي التقنية بالعقارات**

[تحميل التطبيق](https://play.google.com/store/apps/details?id=com.krea.app) • [App Store](https://apps.apple.com/app/krea/id123456789)

---

*تم إنشاء هذا التوثيق بواسطة فريق Codnet Moroccan*

</div>
