import 'package:flutter/material.dart';

/// زر مخصص للتطبيق
class CustomButton extends StatelessWidget {
  /// نص الزر
  final String text;

  /// دالة يتم استدعاؤها عند الضغط على الزر
  final VoidCallback onPressed;

  /// عرض الزر
  final double? width;

  /// ارتفاع الزر
  final double? height;

  /// لون الزر
  final Color? color;

  /// لون النص
  final Color? textColor;

  /// حجم النص
  final double? fontSize;

  /// نصف قطر الحواف
  final double? borderRadius;

  /// أيقونة الزر
  final IconData? icon;

  /// موضع الأيقونة
  final bool iconLeading;

  /// المسافة بين الأيقونة والنص
  final double iconSpacing;

  /// إنشاء زر مخصص
  const CustomButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.width,
    this.height = 48.0,
    this.color,
    this.textColor,
    this.fontSize = 16.0,
    this.borderRadius = 8.0,
    this.icon,
    this.iconLeading = true,
    this.iconSpacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    final buttonColor = color ?? Theme.of(context).primaryColor;
    final buttonTextColor = textColor ?? Colors.white;

    Widget buttonContent;

    if (icon != null) {
      final iconWidget = Icon(
        icon,
        color: buttonTextColor,
        size: fontSize! * 1.2);

      final textWidget = Text(
        text,
        style: TextStyle(
          color: buttonTextColor,
          fontSize: fontSize,
          fontWeight: FontWeight.bold));

      if (iconLeading) {
        buttonContent = Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            iconWidget,
            SizedBox(width: iconSpacing),
            textWidget,
          ]);
      } else {
        buttonContent = Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            textWidget,
            SizedBox(width: iconSpacing),
            iconWidget,
          ]);
      }
    } else {
      buttonContent = Text(
        text,
        style: TextStyle(
          color: buttonTextColor,
          fontSize: fontSize,
          fontWeight: FontWeight.bold));
    }

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius!)),
          padding: const EdgeInsets.symmetric(horizontal: 16.0)),
        child: buttonContent));
  }
}
