<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Kuwait Corners - جاري التوجيه</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f5f5f5;
        color: #333;
        text-align: center;
        height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      .container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .logo {
        width: 120px;
        height: 120px;
        margin: 20px auto;
        display: block;
      }
      h1 {
        color: #2c3e50;
        margin-bottom: 20px;
      }
      p {
        font-size: 18px;
        line-height: 1.6;
        margin-bottom: 30px;
      }
      .loader {
        border: 5px solid #f3f3f3;
        border-radius: 50%;
        border-top: 5px solid #3498db;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
        margin: 20px auto;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      .btn {
        display: inline-block;
        background-color: #3498db;
        color: white;
        padding: 12px 30px;
        text-decoration: none;
        border-radius: 30px;
        font-weight: bold;
        margin: 10px;
        transition: background-color 0.3s;
      }
      .btn:hover {
        background-color: #2980b9;
      }
      .btn-primary {
        background-color: #2ecc71;
      }
      .btn-primary:hover {
        background-color: #27ae60;
      }
      .store-badges {
        display: flex;
        justify-content: center;
        margin-top: 30px;
        flex-wrap: wrap;
      }
      .store-badge {
        margin: 10px;
        max-width: 180px;
      }
      #manual-redirect {
        display: none;
        margin-top: 30px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <img
        src="https://firebasestorage.googleapis.com/v0/b/kuwait-corners.appspot.com/o/app_assets%2Flogo.png?alt=media"
        alt="Kuwait Corners Logo"
        class="logo"
      />
      <h1>جاري فتح تطبيق Kuwait Corners</h1>
      <p>إذا لم يتم فتح التطبيق تلقائيًا، يرجى النقر على الزر أدناه.</p>

      <div class="loader" id="loader"></div>

      <div id="manual-redirect">
        <a href="#" id="open-app-btn" class="btn btn-primary">فتح التطبيق</a>

        <div class="store-badges">
          <p>أو قم بتنزيل التطبيق:</p>
          <a
            href="https://play.google.com/store/apps/details?id=com.example.kuwait_corners"
            id="play-store-link"
            target="_blank"
          >
            <img
              src="https://play.google.com/intl/en_us/badges/static/images/badges/ar_badge_web_generic.png"
              alt="Google Play"
              class="store-badge"
            />
          </a>
          <a
            href="https://apps.apple.com/app/id123456789"
            id="app-store-link"
            target="_blank"
          >
            <img
              src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg"
              alt="App Store"
              class="store-badge"
            />
          </a>
        </div>
      </div>
    </div>

    <script>
      // استخراج رمز الإحالة من URL
      const urlParams = new URLSearchParams(window.location.search);
      const referralCode = urlParams.get("code") || "";

      // إنشاء معرف فريد للمستخدم المُحال
      function generateReferralId() {
        return (
          "ref_" +
          Math.random().toString(36).substring(2, 15) +
          Math.random().toString(36).substring(2, 15) +
          "_" +
          Date.now()
        );
      }

      // الحصول على معرف الإحالة أو إنشاء واحد جديد
      function getReferralId() {
        let referralId = localStorage.getItem("referral_id");
        if (!referralId) {
          referralId = generateReferralId();
          localStorage.setItem("referral_id", referralId);
        }
        return referralId;
      }

      // تسجيل بيانات الإحالة في Firebase
      function logReferralAttempt(action, storeType = null) {
        // يمكن استبدال هذا برمز يرسل البيانات إلى Firebase أو خادم آخر
        const referralId = getReferralId();
        const timestamp = new Date().toISOString();
        const deviceInfo = {
          platform: isAndroid ? "android" : isIOS ? "ios" : "web",
          userAgent: navigator.userAgent,
          language: navigator.language,
          screenWidth: window.screen.width,
          screenHeight: window.screen.height,
        };

        const referralData = {
          referralCode,
          referralId,
          timestamp,
          action,
          storeType,
          deviceInfo,
        };

        console.log("Referral data:", referralData);

        // حفظ البيانات في localStorage للاستخدام لاحقًا
        try {
          const referralHistory = JSON.parse(
            localStorage.getItem("referral_history") || "[]"
          );
          referralHistory.push(referralData);
          localStorage.setItem(
            "referral_history",
            JSON.stringify(referralHistory)
          );

          // حفظ رمز الإحالة ومعرف الإحالة في localStorage
          localStorage.setItem("pending_referral_code", referralCode);
          localStorage.setItem("pending_referral_id", referralId);

          // إضافة معلمات الإحالة إلى روابط المتاجر
          updateStoreLinks(referralCode, referralId);
        } catch (e) {
          console.error("Error saving referral data:", e);
        }

        // يمكن إضافة كود هنا لإرسال البيانات إلى خادم
      }

      // تحديد نوع الجهاز
      const isAndroid = /android/i.test(navigator.userAgent);
      const isIOS =
        /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

      // روابط المتاجر الأساسية
      const PLAY_STORE_BASE =
        "https://play.google.com/store/apps/details?id=com.example.kuwait_corners";
      const APP_STORE_BASE = "https://apps.apple.com/app/id123456789";

      // تحديث روابط المتاجر بمعلمات الإحالة
      function updateStoreLinks(code, id) {
        const playStoreLink = `${PLAY_STORE_BASE}&referrer=utm_source%3Dreferral%26utm_medium%3Dapp%26utm_campaign%3Dreferral%26utm_content%3D${code}%26referral_id%3D${id}`;
        const appStoreLink = `${APP_STORE_BASE}?pt=123456&ct=${code}&mt=${id}`;

        document.getElementById("play-store-link").href = playStoreLink;
        document.getElementById("app-store-link").href = appStoreLink;

        return { playStoreLink, appStoreLink };
      }

      // روابط التطبيق
      function getAppLinks(code, id) {
        return {
          appSchemeLink: `kuwaitcorners://referral?code=${code}&id=${id}`,
          fallbackLink: `https://kuwaitcorners.com/referral?code=${code}&id=${id}`,
        };
      }

      // تسجيل محاولة الإحالة الأولية
      logReferralAttempt("page_view");

      // الحصول على روابط محدثة
      const referralId = getReferralId();
      const { playStoreLink, appStoreLink } = updateStoreLinks(
        referralCode,
        referralId
      );
      const { appSchemeLink, fallbackLink } = getAppLinks(
        referralCode,
        referralId
      );

      // محاولة فتح التطبيق
      function openApp() {
        if (isAndroid) {
          // تسجيل محاولة الفتح
          logReferralAttempt("open_app_android");

          // استخدام Intent URL لـ Android
          // هذا يحاول فتح التطبيق، وإذا لم يكن موجودًا، يفتح متجر Play
          window.location.href = `intent://referral?code=${referralCode}&id=${referralId}#Intent;scheme=kuwaitcorners;package=com.example.kuwait_corners;S.browser_fallback_url=${encodeURIComponent(
            playStoreLink
          )};end`;

          // تسجيل توجيه المتجر بعد فترة قصيرة
          setTimeout(() => {
            logReferralAttempt("store_redirect_android", "play_store");
          }, 1500);
        } else if (isIOS) {
          // تسجيل محاولة الفتح
          logReferralAttempt("open_app_ios");

          // محاولة فتح التطبيق على iOS
          // سنستخدم setTimeout للتحقق مما إذا تم فتح التطبيق
          const now = Date.now();
          setTimeout(function () {
            // إذا لم يتم فتح التطبيق (لم يغادر المستخدم الصفحة)، نفتح App Store
            if (Date.now() - now < 1500) {
              logReferralAttempt("store_redirect_ios", "app_store");
              window.location.href = appStoreLink;
            }
          }, 1000);

          // محاولة فتح التطبيق
          window.location.href = appSchemeLink;
        } else {
          // للأجهزة الأخرى، نعرض خيارات التنزيل
          logReferralAttempt("show_manual_options");
          showManualRedirect();
        }
      }

      // عرض خيارات التوجيه اليدوي
      function showManualRedirect() {
        document.getElementById("loader").style.display = "none";
        document.getElementById("manual-redirect").style.display = "block";
      }

      // تكوين زر فتح التطبيق
      document
        .getElementById("open-app-btn")
        .addEventListener("click", function (e) {
          e.preventDefault();
          logReferralAttempt("manual_open_click");
          openApp();
        });

      // تكوين أزرار المتاجر
      document
        .getElementById("play-store-link")
        .addEventListener("click", function (e) {
          logReferralAttempt("manual_store_click", "play_store");
        });

      document
        .getElementById("app-store-link")
        .addEventListener("click", function (e) {
          logReferralAttempt("manual_store_click", "app_store");
        });

      // محاولة فتح التطبيق تلقائيًا بعد 1 ثانية
      setTimeout(function () {
        openApp();

        // عرض خيارات التوجيه اليدوي بعد 2 ثوانٍ
        setTimeout(showManualRedirect, 2000);
      }, 1000);
    </script>
  </body>
</html>
