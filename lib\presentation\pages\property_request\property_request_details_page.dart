// lib/presentation/pages/property_request/property_request_details_page.dart
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../domain/models/property_request/property_request_model.dart';
import '../../providers/property_request_provider.dart';
import '../../utils/loading_state.dart';
import '../../widgets/enhanced_progress_indicator.dart';
import '../../widgets/property_request/property_offer_card.dart';
import 'create_property_offer_page.dart';
import 'edit_property_request_page.dart';

import '../../../core/theme/app_colors.dart';

/// صفحة تفاصيل طلب العقار
class PropertyRequestDetailsPage extends StatefulWidget {
  /// معرف طلب العقار
  final String requestId;

  const PropertyRequestDetailsPage({
    super.key,
    required this.requestId,
  });

  @override
  State<PropertyRequestDetailsPage> createState() => _PropertyRequestDetailsPageState();
}

class _PropertyRequestDetailsPageState extends State<PropertyRequestDetailsPage> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  bool _isCurrentUserOwner = false;
  bool _isCurrentUserSeeker = false;

  @override
  void initState() {
    super.initState();

    // تحميل تفاصيل طلب العقار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
      provider.loadPropertyRequest(widget.requestId);
      provider.loadRequestOffers(widget.requestId);

      // التحقق من نوع المستخدم الحالي
      _checkCurrentUser();
    });
  }

  /// التحقق من نوع المستخدم الحالي
  Future<void> _checkCurrentUser() async {
    final user = _auth.currentUser;
    if (user == null) {
      return;
    }

    final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
    await provider.loadPropertyRequest(widget.requestId);

    if (provider.currentRequest != null) {
      setState(() {
        _isCurrentUserOwner = provider.currentRequest!.userId == user.uid;
      });
    }

    // التحقق من نوع المستخدم
    try {
      final userDoc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
      final userData = userDoc.data();

      if (userData != null) {
        setState(() {
          _isCurrentUserSeeker = userData['type'] == 1; // 1 is the index for seeker user type
        });
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// حذف طلب العقار
  Future<void> _deleteRequest() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        content: Text(
          'هل أنت متأكد من حذف هذا الطلب؟',
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo())),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(
                color: Colors.red))),
        ]));

    if (confirmed == true) {
      final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
      final success = await provider.deletePropertyRequest(widget.requestId);

      if (success && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم حذف الطلب بنجاح',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.green));
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل حذف الطلب',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.orangeBackground,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppColors.orangeGradient,
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryOrange.withValues(alpha: 0.3),
                blurRadius: 10,
                offset: const Offset(0, 4)),
            ]),
          child: AppBar(
            title: Text(
              'تفاصيل الطلب',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 18)),
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
            iconTheme: const IconThemeData(color: Colors.white),
            actions: [
              // زر الحذف (للمالك فقط)
              if (_isCurrentUserOwner)
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                      width: 1)),
                  child: IconButton(
                    icon: const Icon(
                      Icons.delete_rounded,
                      color: Colors.white,
                      size: 20),
                    onPressed: _deleteRequest,
                    tooltip: 'حذف الطلب')),
              // زر التعديل (للمالك فقط)
              if (_isCurrentUserOwner)
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1)),
                  child: IconButton(
                    icon: const Icon(
                      Icons.edit_rounded,
                      color: Colors.white,
                      size: 20),
                    onPressed: () {
                      final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
                      if (provider.currentRequest != null) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => EditPropertyRequestPage(
                              request: provider.currentRequest!)));
                      }
                    },
                    tooltip: 'تعديل الطلب')),
            ]))),
      body: Consumer<PropertyRequestProvider>(
        builder: (context, provider, child) {
          final requestState = provider.currentRequestState;
          final request = provider.currentRequest;
          final offersState = provider.offersState;
          final offers = provider.offers;

          if (requestState == LoadingState.loading) {
            return Container(
              decoration: const BoxDecoration(
                gradient: AppColors.lightOrangeGradient),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // مؤشر التحميل العصري
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.white, AppColors.orangeCardBackground],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight),
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryOrange.withValues(alpha: 0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10)),
                        ]),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // دائرة التحميل الخارجية
                          SizedBox(
                            width: 80,
                            height: 80,
                            child: CircularProgressIndicator(
                              strokeWidth: 3,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primaryOrange))),
                          // أيقونة في المركز
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppColors.primaryOrange,
                              borderRadius: BorderRadius.circular(20)),
                            child: const Icon(
                              Icons.description_rounded,
                              color: Colors.white,
                              size: 24)),
                        ])),
                    const SizedBox(height: 24),
                    // نص التحميل
                    Text(
                      'جاري تحميل تفاصيل الطلب...',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary)),
                    const SizedBox(height: 8),
                    Text(
                      'يرجى الانتظار',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: AppColors.textSecondary)),
                  ])));
          }

          if (requestState == LoadingState.error) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 48,
                    color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ أثناء تحميل تفاصيل الطلب',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      provider.loadPropertyRequest(widget.requestId);
                    },
                    child: Text(
                      'إعادة المحاولة',
                      style: GoogleFonts.cairo())),
                ]));
          }

          if (requestState == LoadingState.empty || request == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.search_off,
                    size: 48,
                    color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    'الطلب غير موجود',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      'العودة',
                      style: GoogleFonts.cairo())),
                ]));
          }

          return Container(
            decoration: const BoxDecoration(
              gradient: AppColors.lightOrangeGradient),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // بطاقة تفاصيل الطلب العصرية
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.white, AppColors.orangeCardBackground],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight),
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primaryOrange.withValues(alpha: 0.2),
                          blurRadius: 20,
                          offset: const Offset(0, 10)),
                      ],
                      border: Border.all(
                        color: AppColors.primaryOrange.withValues(alpha: 0.2),
                        width: 1)),
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                        // رأس البطاقة - العنوان والحالة
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    request.title,
                                    style: GoogleFonts.cairo(
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textPrimary,
                                      height: 1.3)),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.access_time_rounded,
                                        size: 16,
                                        color: AppColors.textSecondary),
                                      const SizedBox(width: 4),
                                      Text(
                                        'تم النشر ${DateFormat('yyyy/MM/dd').format(request.createdAt)}',
                                        style: GoogleFonts.cairo(
                                          fontSize: 12,
                                          color: AppColors.textSecondary)),
                                    ]),
                                ])),
                            const SizedBox(width: 12),
                            // حالة الطلب
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    _getStatusColor(request.status),
                                    _getStatusColor(request.status).withValues(alpha: 0.8),
                                  ]),
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: _getStatusColor(request.status).withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2)),
                                ]),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    _getStatusIcon(request.status),
                                    size: 14,
                                    color: Colors.white),
                                  const SizedBox(width: 4),
                                  Text(
                                    _getStatusText(request.status),
                                    style: GoogleFonts.cairo(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white)),
                                ])),
                          ]),
                        const SizedBox(height: 20),

                        // وصف الطلب
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.primaryOrange.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.primaryOrange.withValues(alpha: 0.1),
                              width: 1)),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.description_rounded,
                                    size: 18,
                                    color: AppColors.primaryOrange),
                                  const SizedBox(width: 8),
                                  Text(
                                    'وصف الطلب',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.primaryOrange)),
                                ]),
                              const SizedBox(height: 12),
                              Text(
                                request.description,
                                style: GoogleFonts.cairo(
                                  fontSize: 15,
                                  height: 1.6,
                                  color: AppColors.textPrimary)),
                            ])),
                        const SizedBox(height: 24),

                        // قسم تفاصيل الطلب العصري
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primaryOrange.withValues(alpha: 0.1),
                                blurRadius: 15,
                                offset: const Offset(0, 5)),
                            ],
                            border: Border.all(
                              color: AppColors.primaryOrange.withValues(alpha: 0.1),
                              width: 1)),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // عنوان القسم
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      gradient: AppColors.orangeGradient,
                                      borderRadius: BorderRadius.circular(12)),
                                    child: const Icon(
                                      Icons.info_rounded,
                                      color: Colors.white,
                                      size: 20)),
                                  const SizedBox(width: 12),
                                  Text(
                                    'تفاصيل الطلب',
                                    style: GoogleFonts.cairo(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textPrimary)),
                                ]),
                              const SizedBox(height: 20),

                              // شبكة التفاصيل
                              Column(
                                children: [
                                  // الصف الأول - نوع العقار والمنطقة
                                  Row(
                                    children: [
                                      Expanded(
                                        child: _buildModernDetailCard(
                                          'نوع العقار',
                                          request.propertyType,
                                          Icons.home_rounded,
                                          AppColors.primaryOrange)),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: _buildModernDetailCard(
                                          'المنطقة',
                                          request.preferredLocations.isNotEmpty
                                              ? request.preferredLocations.first
                                              : 'غير محدد',
                                          Icons.location_on_rounded,
                                          Colors.blue)),
                                    ]),
                                  const SizedBox(height: 12),

                                  // الصف الثاني - السعر والمساحة
                                  Row(
                                    children: [
                                      Expanded(
                                        child: _buildModernDetailCard(
                                          'السعر',
                                          _getPriceRange(request.minPrice, request.maxPrice),
                                          Icons.attach_money_rounded,
                                          Colors.green)),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: _buildModernDetailCard(
                                          'المساحة',
                                          request.minArea != null
                                              ? '${request.minArea} م² أو أكثر'
                                              : 'غير محدد',
                                          Icons.square_foot_rounded,
                                          Colors.purple)),
                                    ]),
                                  const SizedBox(height: 12),

                                  // الصف الثالث - الغرف والحمامات
                                  Row(
                                    children: [
                                      Expanded(
                                        child: _buildModernDetailCard(
                                          'الغرف',
                                          request.minRooms != null
                                              ? '${request.minRooms} أو أكثر'
                                              : 'غير محدد',
                                          Icons.bedroom_parent_rounded,
                                          Colors.orange)),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: _buildModernDetailCard(
                                          'الحمامات',
                                          request.minBathrooms != null
                                              ? '${request.minBathrooms} أو أكثر'
                                              : 'غير محدد',
                                          Icons.bathroom_rounded,
                                          Colors.teal)),
                                    ]),
                                ]),
                            ])),
                        const SizedBox(height: 20),

                        // قسم المميزات المطلوبة
                        if (_hasAnyFeatures(request))
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primaryOrange.withValues(alpha: 0.1),
                                  blurRadius: 15,
                                  offset: const Offset(0, 5)),
                              ],
                              border: Border.all(
                                color: AppColors.primaryOrange.withValues(alpha: 0.1),
                                width: 1)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // عنوان القسم
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        gradient: AppColors.orangeGradient,
                                        borderRadius: BorderRadius.circular(12)),
                                      child: const Icon(
                                        Icons.star_rounded,
                                        color: Colors.white,
                                        size: 20)),
                                    const SizedBox(width: 12),
                                    Text(
                                      'المميزات المطلوبة',
                                      style: GoogleFonts.cairo(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.textPrimary)),
                                  ]),
                                const SizedBox(height: 16),

                                // شبكة المميزات
                                Wrap(
                                  spacing: 12,
                                  runSpacing: 12,
                                  children: [
                                    if (request.hasCentralAC)
                                      _buildModernFeatureChip('تكييف مركزي', Icons.ac_unit_rounded),
                                    if (request.hasMaidRoom)
                                      _buildModernFeatureChip('غرفة خادمة', Icons.person_rounded),
                                    if (request.hasGarage)
                                      _buildModernFeatureChip('مرآب', Icons.garage_rounded),
                                    if (request.hasSwimmingPool)
                                      _buildModernFeatureChip('مسبح', Icons.pool_rounded),
                                    if (request.hasElevator)
                                      _buildModernFeatureChip('مصعد', Icons.elevator_rounded),
                                    if (request.isFullyFurnished)
                                      _buildModernFeatureChip('مفروش بالكامل', Icons.chair_rounded),
                                  ]),
                              ])),
                        const SizedBox(height: 16),

                        // متطلبات إضافية
                        if (request.additionalRequirements != null &&
                            request.additionalRequirements!.isNotEmpty) ...[
                          Text(
                            'متطلبات إضافية',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          Text(
                            request.additionalRequirements!,
                            style: GoogleFonts.cairo(
                              fontSize: 14)),
                          const SizedBox(height: 16),
                        ],

                        // تاريخ الحاجة
                        if (request.neededBy != null) ...[
                          _buildDetailRow(
                            'مطلوب بحلول',
                            DateFormat('yyyy/MM/dd').format(request.neededBy!),
                            Icons.calendar_today),
                          const SizedBox(height: 16),
                        ],

                        // معلومات المستخدم
                        const Divider(),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 20,
                              backgroundColor: Colors.grey.shade200,
                              backgroundImage: request.userImage != null
                                  ? NetworkImage(request.userImage!)
                                  : null,
                              child: request.userImage == null
                                  ? const Icon(Icons.person, color: Colors.grey)
                                  : null),
                            const SizedBox(width: 8),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  request.userName,
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.bold)),
                                Text(
                                  'تم النشر ${DateFormat('yyyy/MM/dd').format(request.createdAt)}',
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: Colors.grey)),
                              ]),
                          ]),
                      ]))),

                const SizedBox(height: 24),

                // العروض
                Text(
                  'العروض (${offers.length})',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),

                if (offersState == LoadingState.loading && offers.isEmpty)
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: EnhancedProgressIndicator(
                        currentStep: 1,
                        totalSteps: 1,
                        stepTitles: ['جاري التحميل'])))
                else if (offersState == LoadingState.error && offers.isEmpty)
                  Center(
                    child: Column(
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 32,
                          color: Colors.red),
                        const SizedBox(height: 8),
                        Text(
                          'حدث خطأ أثناء تحميل العروض',
                          style: GoogleFonts.cairo(
                            fontSize: 14)),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () {
                            provider.loadRequestOffers(widget.requestId);
                          },
                          child: Text(
                            'إعادة المحاولة',
                            style: GoogleFonts.cairo())),
                      ]))
                else if (offersState == LoadingState.empty || offers.isEmpty)
                  Center(
                    child: Column(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          size: 32,
                          color: Colors.grey),
                        const SizedBox(height: 8),
                        Text(
                          'لا توجد عروض حتى الآن',
                          style: GoogleFonts.cairo(
                            fontSize: 14)),
                      ]))
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: offers.length,
                    itemBuilder: (context, index) {
                      final offer = offers[index];
                      return PropertyOfferCard(
                        offer: offer,
                        isOwner: _isCurrentUserOwner,
                        onAccept: _isCurrentUserOwner
                            ? () async {
                                final confirmed = await showDialog<bool>(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    title: Text(
                                      'تأكيد قبول العرض',
                                      style: GoogleFonts.cairo(
                                        fontWeight: FontWeight.bold)),
                                    content: Text(
                                      'هل أنت متأكد من قبول هذا العرض؟ سيتم إغلاق الطلب وإنشاء محادثة مع صاحب العرض.',
                                      style: GoogleFonts.cairo()),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Navigator.pop(context, false),
                                        child: Text(
                                          'إلغاء',
                                          style: GoogleFonts.cairo())),
                                      TextButton(
                                        onPressed: () => Navigator.pop(context, true),
                                        child: Text(
                                          'قبول',
                                          style: GoogleFonts.cairo(
                                            color: Colors.green))),
                                    ]));

                                if (confirmed == true) {
                                  final success = await provider.acceptOffer(
                                    offer.id,
                                    widget.requestId);

                                  if (success && mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'تم قبول العرض بنجاح',
                                          style: GoogleFonts.cairo()),
                                        backgroundColor: Colors.green));
                                  } else if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'فشل قبول العرض',
                                          style: GoogleFonts.cairo()),
                                        backgroundColor: Colors.red));
                                  }
                                }
                              }
                            : null,
                        onReject: _isCurrentUserOwner
                            ? () async {
                                final confirmed = await showDialog<bool>(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    title: Text(
                                      'تأكيد رفض العرض',
                                      style: GoogleFonts.cairo(
                                        fontWeight: FontWeight.bold)),
                                    content: Text(
                                      'هل أنت متأكد من رفض هذا العرض؟',
                                      style: GoogleFonts.cairo()),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Navigator.pop(context, false),
                                        child: Text(
                                          'إلغاء',
                                          style: GoogleFonts.cairo())),
                                      TextButton(
                                        onPressed: () => Navigator.pop(context, true),
                                        child: Text(
                                          'رفض',
                                          style: GoogleFonts.cairo(
                                            color: Colors.red))),
                                    ]));

                                if (confirmed == true) {
                                  final success = await provider.rejectOffer(
                                    offer.id,
                                    widget.requestId);

                                  if (success && mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'تم رفض العرض بنجاح',
                                          style: GoogleFonts.cairo()),
                                        backgroundColor: Colors.green));
                                  } else if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'فشل رفض العرض',
                                          style: GoogleFonts.cairo()),
                                        backgroundColor: Colors.red));
                                  }
                                }
                              }
                            : null);
                    }),

                const SizedBox(height: 100), // مساحة للزر العائم
              ])));
        }),
      floatingActionButton: Consumer<PropertyRequestProvider>(
        builder: (context, provider, child) {
          final request = provider.currentRequest;

          // عرض زر إضافة عرض فقط إذا كان الطلب مفتوحًا وليس المستخدم الحالي هو صاحب الطلب وليس باحث عن عقار
          if (request != null &&
              request.status == RequestStatus.open &&
              !_isCurrentUserOwner &&
              !_isCurrentUserSeeker) {
            return FloatingActionButton.extended(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CreatePropertyOfferPage(
                      requestId: widget.requestId,
                      requestTitle: request.title)));
              },
              backgroundColor: Colors.green,
              icon: const Icon(Icons.add),
              label: Text(
                'إضافة عرض',
                style: GoogleFonts.cairo()));
          }

          return const SizedBox.shrink();
        }));
  }

  /// بناء صف تفاصيل
  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 18,
          color: Colors.grey.shade600),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade600)),
              Text(
                value,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ])),
      ]);
  }

  /// التحقق من وجود أي ميزات
  bool _hasAnyFeatures(PropertyRequestModel request) {
    return request.hasCentralAC ||
        request.hasMaidRoom ||
        request.hasGarage ||
        request.hasSwimmingPool ||
        request.hasElevator ||
        request.isFullyFurnished;
  }

  /// بناء بطاقة تفصيل عصرية
  Widget _buildModernDetailCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.05),
            color.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 16)),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: color))),
            ]),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary),
            maxLines: 2,
            overflow: TextOverflow.ellipsis),
        ]));
  }

  /// بناء شريحة ميزة عصرية
  Widget _buildModernFeatureChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryOrange.withValues(alpha: 0.1),
            AppColors.lightOrange.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primaryOrange.withValues(alpha: 0.3),
          width: 1)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: AppColors.primaryOrange.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8)),
            child: Icon(
              icon,
              size: 14,
              color: AppColors.primaryOrange)),
          const SizedBox(width: 6),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: AppColors.darkOrange)),
        ]));
  }

  /// الحصول على نص حالة الطلب
  String _getStatusText(RequestStatus status) {
    switch (status) {
      case RequestStatus.open:
        return 'مفتوح';
      case RequestStatus.closed:
        return 'مغلق';
      case RequestStatus.resolved:
        return 'تم الحل';
    }
  }

  /// الحصول على لون حالة الطلب
  Color _getStatusColor(RequestStatus status) {
    switch (status) {
      case RequestStatus.open:
        return Colors.green;
      case RequestStatus.closed:
        return Colors.red;
      case RequestStatus.resolved:
        return Colors.blue;
    }
  }

  /// الحصول على أيقونة حالة الطلب
  IconData _getStatusIcon(RequestStatus status) {
    switch (status) {
      case RequestStatus.open:
        return Icons.check_circle_rounded;
      case RequestStatus.closed:
        return Icons.cancel_rounded;
      case RequestStatus.resolved:
        return Icons.task_alt_rounded;
    }
  }

  /// الحصول على نطاق السعر
  String _getPriceRange(double? minPrice, double? maxPrice) {
    if (minPrice != null && maxPrice != null) {
      return '${minPrice.toStringAsFixed(0)} - ${maxPrice.toStringAsFixed(0)} د.ك';
    } else if (minPrice != null) {
      return 'من ${minPrice.toStringAsFixed(0)} د.ك';
    } else if (maxPrice != null) {
      return 'حتى ${maxPrice.toStringAsFixed(0)} د.ك';
    }
    return '';
  }
}
