import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';

import '../../domain/entities/conversation.dart';
import '../../domain/entities/message.dart';
import '../../domain/repositories/messaging_repository.dart';
import '../services/realtime_notification_service.dart';

/// خدمة المراسلة
class MessagingService {
  final MessagingRepository _messagingRepository;
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final RealtimeNotificationService _notificationService;
  final Uuid _uuid = const Uuid();

  MessagingService({
    required MessagingRepository messagingRepository,
    FirebaseAuth? auth,
    FirebaseFirestore? firestore,
    FirebaseStorage? storage,
    RealtimeNotificationService? notificationService,
  })  : _messagingRepository = messagingRepository,
        _auth = auth ?? FirebaseAuth.instance,
        _firestore = firestore ?? FirebaseFirestore.instance,
        _storage = storage ?? FirebaseStorage.instance,
        _notificationService =
            notificationService ?? RealtimeNotificationService();

  /// إنشاء محادثة جديدة بين مستخدمين
  Future<String?> createConversationWithUser(String recipientId) async {
    final user = _auth.currentUser;
    if (user == null) {
      return null;
    }

    // التحقق من وجود محادثة سابقة
    final existingConversation =
        await _messagingRepository.getConversationBetweenUsers(
      user.uid,
      recipientId);

    if (existingConversation != null) {
      return existingConversation.id;
    }

    // الحصول على معلومات المستخدم الحالي
    final currentUserDoc =
        await _firestore.collection('users').doc(user.uid).get();
    final currentUserData = currentUserDoc.data();
    final currentUserName =
        currentUserData?['fullName'] as String? ?? user.displayName ?? 'مستخدم';
    final currentUserImage =
        currentUserData?['profileImage'] as String? ?? user.photoURL;

    // الحصول على معلومات المستخدم المستلم
    final recipientDoc =
        await _firestore.collection('users').doc(recipientId).get();
    final recipientData = recipientDoc.data();
    final recipientName = recipientData?['fullName'] as String? ?? 'مستخدم';
    final recipientImage = recipientData?['profileImage'] as String?;

    // إنشاء عنوان المحادثة
    final title = 'محادثة مع $recipientName';

    // إنشاء المحادثة
    final conversation = Conversation(
      id: '',
      title: title,
      type: ConversationType.private,
      status: ConversationStatus.active,
      participantIds: [user.uid, recipientId],
      participantNames: [currentUserName, recipientName],
      participantImages: [currentUserImage, recipientImage],
      createdAt: DateTime.now(),
      createdBy: user.uid,
      updatedAt: DateTime.now());

    return _messagingRepository.createConversation(conversation);
  }

  /// إنشاء محادثة متعلقة بعقار
  Future<String?> createConversationForEstate(String estateId, String ownerId,
      String estateTitle, String? estateImage) async {
    final user = _auth.currentUser;
    if (user == null) {
      return null;
    }

    // التحقق من أن المستخدم ليس هو مالك العقار
    if (user.uid == ownerId) {
      return null;
    }

    // التحقق من وجود محادثة سابقة
    final existingConversation =
        await _messagingRepository.getConversationForEstate(
      estateId,
      user.uid);

    if (existingConversation != null) {
      return existingConversation.id;
    }

    // الحصول على معلومات المستخدم الحالي
    final currentUserDoc =
        await _firestore.collection('users').doc(user.uid).get();
    final currentUserData = currentUserDoc.data();
    final currentUserName =
        currentUserData?['fullName'] as String? ?? user.displayName ?? 'مستخدم';
    final currentUserImage =
        currentUserData?['profileImage'] as String? ?? user.photoURL;

    // الحصول على معلومات المالك
    final ownerDoc = await _firestore.collection('users').doc(ownerId).get();
    final ownerData = ownerDoc.data();
    final ownerName = ownerData?['fullName'] as String? ?? 'مستخدم';
    final ownerImage = ownerData?['profileImage'] as String?;

    // إنشاء عنوان المحادثة
    final title = 'استفسار عن: $estateTitle';

    // إنشاء المحادثة
    final conversation = Conversation(
      id: '',
      title: title,
      type: ConversationType.estate,
      status: ConversationStatus.active,
      participantIds: [user.uid, ownerId],
      participantNames: [currentUserName, ownerName],
      participantImages: [currentUserImage, ownerImage],
      createdAt: DateTime.now(),
      createdBy: user.uid,
      updatedAt: DateTime.now(),
      estateId: estateId,
      estateTitle: estateTitle,
      estateImage: estateImage);

    return _messagingRepository.createConversation(conversation);
  }

  /// إنشاء محادثة متعلقة بعرض على طلب عقار
  Future<String?> createConversationForPropertyOffer(
      String requestId, String offerId, String ownerId, String requesterId, String requestTitle, String offerTitle) async {
    final user = _auth.currentUser;
    if (user == null) {
      return null;
    }

    // التحقق من أن المستخدم هو مالك الطلب أو مقدم العرض
    if (user.uid != ownerId && user.uid != requesterId) {
      return null;
    }

    // التحقق من وجود محادثة سابقة
    final existingConversation =
        await _messagingRepository.getConversationBetweenUsers(
      ownerId,
      requesterId);

    if (existingConversation != null &&
        existingConversation.propertyRequestId == requestId &&
        existingConversation.propertyOfferId == offerId) {
      return existingConversation.id;
    }

    // الحصول على معلومات مالك الطلب
    final requesterDoc = await _firestore.collection('users').doc(requesterId).get();
    final requesterData = requesterDoc.data();
    final requesterName = requesterData?['fullName'] as String? ?? 'مستخدم';
    final requesterImage = requesterData?['profileImage'] as String?;

    // الحصول على معلومات مقدم العرض
    final ownerDoc = await _firestore.collection('users').doc(ownerId).get();
    final ownerData = ownerDoc.data();
    final ownerName = ownerData?['fullName'] as String? ?? 'مستخدم';
    final ownerImage = ownerData?['profileImage'] as String?;

    // إنشاء عنوان المحادثة
    final title = 'محادثة حول عرض: $offerTitle';

    // إنشاء المحادثة
    final conversation = Conversation(
      id: '',
      title: title,
      type: ConversationType.propertyOffer,
      status: ConversationStatus.active,
      participantIds: [requesterId, ownerId],
      participantNames: [requesterName, ownerName],
      participantImages: [requesterImage, ownerImage],
      createdAt: DateTime.now(),
      createdBy: user.uid,
      updatedAt: DateTime.now(),
      propertyRequestId: requestId,
      propertyRequestTitle: requestTitle,
      propertyOfferId: offerId);

    return _messagingRepository.createConversation(conversation);
  }

  /// إنشاء محادثة متعلقة بطلب عقار
  Future<String?> createConversationForPropertyRequest(
      String requestId, String ownerId, String requestTitle) async {
    final user = _auth.currentUser;
    if (user == null) {
      return null;
    }

    // التحقق من أن المستخدم ليس هو مالك الطلب
    if (user.uid == ownerId) {
      return null;
    }

    // التحقق من وجود محادثة سابقة
    final existingConversation =
        await _messagingRepository.getConversationBetweenUsers(
      user.uid,
      ownerId);

    if (existingConversation != null &&
        existingConversation.propertyRequestId == requestId) {
      return existingConversation.id;
    }

    // الحصول على معلومات المستخدم الحالي
    final currentUserDoc =
        await _firestore.collection('users').doc(user.uid).get();
    final currentUserData = currentUserDoc.data();
    final currentUserName =
        currentUserData?['fullName'] as String? ?? user.displayName ?? 'مستخدم';
    final currentUserImage =
        currentUserData?['profileImage'] as String? ?? user.photoURL;

    // الحصول على معلومات المالك
    final ownerDoc = await _firestore.collection('users').doc(ownerId).get();
    final ownerData = ownerDoc.data();
    final ownerName = ownerData?['fullName'] as String? ?? 'مستخدم';
    final ownerImage = ownerData?['profileImage'] as String?;

    // إنشاء عنوان المحادثة
    final title = 'استفسار عن طلب: $requestTitle';

    // إنشاء المحادثة
    final conversation = Conversation(
      id: '',
      title: title,
      type: ConversationType.propertyRequest,
      status: ConversationStatus.active,
      participantIds: [user.uid, ownerId],
      participantNames: [currentUserName, ownerName],
      participantImages: [currentUserImage, ownerImage],
      createdAt: DateTime.now(),
      createdBy: user.uid,
      updatedAt: DateTime.now(),
      propertyRequestId: requestId,
      propertyRequestTitle: requestTitle);

    return _messagingRepository.createConversation(conversation);
  }

  /// الحصول على محادثات المستخدم
  Future<List<Conversation>> getUserConversations() async {
    final user = _auth.currentUser;
    if (user == null) {
      return [];
    }

    return _messagingRepository.getUserConversations(user.uid);
  }

  /// الحصول على محادثة بواسطة المعرف
  Future<Conversation?> getConversationById(String conversationId) {
    return _messagingRepository.getConversationById(conversationId);
  }

  /// إرسال رسالة نصية
  Future<String?> sendTextMessage(String conversationId, String content) async {
    final user = _auth.currentUser;
    if (user == null) {
      return null;
    }

    // الحصول على معلومات المحادثة
    final conversation =
        await _messagingRepository.getConversationById(conversationId);
    if (conversation == null) {
      return null;
    }

    // التحقق من أن المستخدم مشارك في المحادثة
    if (!conversation.participantIds.contains(user.uid)) {
      return null;
    }

    // الحصول على معلومات المستخدم
    final userDoc = await _firestore.collection('users').doc(user.uid).get();
    final userData = userDoc.data();
    final userName =
        userData?['fullName'] as String? ?? user.displayName ?? 'مستخدم';
    final userImage = userData?['profileImage'] as String? ?? user.photoURL;

    // تحديد المستلم (المستخدم الآخر في المحادثة)
    final recipientId = conversation.participantIds.firstWhere(
      (id) => id != user.uid,
      orElse: () => '');

    if (recipientId.isEmpty) {
      return null;
    }

    // الحصول على معلومات المستلم
    final recipientDoc =
        await _firestore.collection('users').doc(recipientId).get();
    final recipientData = recipientDoc.data();
    final recipientName = recipientData?['fullName'] as String? ?? 'مستخدم';
    final recipientImage = recipientData?['profileImage'] as String?;

    // إنشاء الرسالة
    final message = Message(
      id: '',
      conversationId: conversationId,
      senderId: user.uid,
      senderName: userName,
      senderImage: userImage,
      receiverId: recipientId,
      receiverName: recipientName,
      receiverImage: recipientImage,
      content: content,
      type: MessageType.text,
      status: MessageStatus.sent,
      timestamp: DateTime.now());

    // إرسال الرسالة
    final messageId = await _messagingRepository.sendMessage(message);

    // إرسال إشعار للمستلم
    await _notificationService.sendNotification(
      recipientId: recipientId,
      type: RealtimeNotificationType.newMessage,
      title: 'رسالة جديدة من $userName',
      body: content.length > 50 ? '${content.substring(0, 50)}...' : content,
      data: {
        'conversationId': conversationId,
        'messageId': messageId,
        'senderId': user.uid,
        'senderName': userName,
      });

    return messageId;
  }

  /// إرسال رسالة صورة
  Future<String?> sendImageMessage(String conversationId, File image) async {
    final user = _auth.currentUser;
    if (user == null) {
      return null;
    }

    // الحصول على معلومات المحادثة
    final conversation =
        await _messagingRepository.getConversationById(conversationId);
    if (conversation == null) {
      return null;
    }

    // التحقق من أن المستخدم مشارك في المحادثة
    if (!conversation.participantIds.contains(user.uid)) {
      return null;
    }

    // رفع الصورة
    final fileName = '${_uuid.v4()}.jpg';
    final ref = _storage.ref().child('messages/$conversationId/$fileName');
    await ref.putFile(image);
    final imageUrl = await ref.getDownloadURL();

    // الحصول على معلومات المستخدم
    final userDoc = await _firestore.collection('users').doc(user.uid).get();
    final userData = userDoc.data();
    final userName =
        userData?['fullName'] as String? ?? user.displayName ?? 'مستخدم';
    final userImage = userData?['profileImage'] as String? ?? user.photoURL;

    // تحديد المستلم (المستخدم الآخر في المحادثة)
    final recipientId = conversation.participantIds.firstWhere(
      (id) => id != user.uid,
      orElse: () => '');

    if (recipientId.isEmpty) {
      return null;
    }

    // الحصول على معلومات المستلم
    final recipientDoc =
        await _firestore.collection('users').doc(recipientId).get();
    final recipientData = recipientDoc.data();
    final recipientName = recipientData?['fullName'] as String? ?? 'مستخدم';
    final recipientImage = recipientData?['profileImage'] as String?;

    // إنشاء الرسالة
    final message = Message(
      id: '',
      conversationId: conversationId,
      senderId: user.uid,
      senderName: userName,
      senderImage: userImage,
      receiverId: recipientId,
      receiverName: recipientName,
      receiverImage: recipientImage,
      content: imageUrl,
      type: MessageType.image,
      status: MessageStatus.sent,
      timestamp: DateTime.now());

    // إرسال الرسالة
    final messageId = await _messagingRepository.sendMessage(message);

    // إرسال إشعار للمستلم
    await _notificationService.sendNotification(
      recipientId: recipientId,
      type: RealtimeNotificationType.newMessage,
      title: 'رسالة جديدة من $userName',
      body: 'أرسل لك صورة',
      data: {
        'conversationId': conversationId,
        'messageId': messageId,
        'senderId': user.uid,
        'senderName': userName,
      });

    return messageId;
  }

  /// إرسال رسالة عقار
  Future<String?> sendEstateMessage(
      String conversationId, String estateId, String estateTitle) async {
    final user = _auth.currentUser;
    if (user == null) {
      return null;
    }

    // الحصول على معلومات المحادثة
    final conversation =
        await _messagingRepository.getConversationById(conversationId);
    if (conversation == null) {
      return null;
    }

    // التحقق من أن المستخدم مشارك في المحادثة
    if (!conversation.participantIds.contains(user.uid)) {
      return null;
    }

    // الحصول على معلومات المستخدم
    final userDoc = await _firestore.collection('users').doc(user.uid).get();
    final userData = userDoc.data();
    final userName =
        userData?['fullName'] as String? ?? user.displayName ?? 'مستخدم';
    final userImage = userData?['profileImage'] as String? ?? user.photoURL;

    // تحديد المستلم (المستخدم الآخر في المحادثة)
    final recipientId = conversation.participantIds.firstWhere(
      (id) => id != user.uid,
      orElse: () => '');

    if (recipientId.isEmpty) {
      return null;
    }

    // الحصول على معلومات المستلم
    final recipientDoc =
        await _firestore.collection('users').doc(recipientId).get();
    final recipientData = recipientDoc.data();
    final recipientName = recipientData?['fullName'] as String? ?? 'مستخدم';
    final recipientImage = recipientData?['profileImage'] as String?;

    // إنشاء الرسالة
    final message = Message(
      id: '',
      conversationId: conversationId,
      senderId: user.uid,
      senderName: userName,
      senderImage: userImage,
      receiverId: recipientId,
      receiverName: recipientName,
      receiverImage: recipientImage,
      content: estateId,
      type: MessageType.estate,
      status: MessageStatus.sent,
      timestamp: DateTime.now(),
      metadata: {
        'estateId': estateId,
        'estateTitle': estateTitle,
      });

    // إرسال الرسالة
    final messageId = await _messagingRepository.sendMessage(message);

    // إرسال إشعار للمستلم
    await _notificationService.sendNotification(
      recipientId: recipientId,
      type: RealtimeNotificationType.newMessage,
      title: 'رسالة جديدة من $userName',
      body: 'شارك معك عقار: $estateTitle',
      data: {
        'conversationId': conversationId,
        'messageId': messageId,
        'senderId': user.uid,
        'senderName': userName,
      });

    return messageId;
  }

  /// الحصول على رسائل محادثة
  Future<List<Message>> getConversationMessages(String conversationId,
      {int limit = 20, String? lastMessageId}) async {
    final user = _auth.currentUser;
    if (user == null) {
      return [];
    }

    // الحصول على معلومات المحادثة
    final conversation =
        await _messagingRepository.getConversationById(conversationId);
    if (conversation == null) {
      return [];
    }

    // التحقق من أن المستخدم مشارك في المحادثة
    if (!conversation.participantIds.contains(user.uid)) {
      return [];
    }

    // الحصول على الرسائل
    final result = await _messagingRepository.getConversationMessagesPaginated(
      conversationId: conversationId,
      limit: limit,
      lastMessageId: lastMessageId);

    return result['messages'] as List<Message>;
  }

  /// تعيين الرسالة كمقروءة
  Future<void> markMessageAsRead(String messageId) async {
    final user = _auth.currentUser;
    if (user == null) {
      return;
    }

    // الحصول على معلومات الرسالة
    final message = await _messagingRepository.getMessageById(messageId);
    if (message == null) {
      return;
    }

    // التحقق من أن المستخدم هو المستلم
    if (message.receiverId != user.uid) {
      return;
    }

    // تعيين الرسالة كمقروءة
    await _messagingRepository.markMessageAsRead(messageId, user.uid);
  }

  /// تعيين جميع رسائل المحادثة كمقروءة
  Future<void> markConversationAsRead(String conversationId) async {
    final user = _auth.currentUser;
    if (user == null) {
      return;
    }

    // الحصول على معلومات المحادثة
    final conversation =
        await _messagingRepository.getConversationById(conversationId);
    if (conversation == null) {
      return;
    }

    // التحقق من أن المستخدم مشارك في المحادثة
    if (!conversation.participantIds.contains(user.uid)) {
      return;
    }

    // تعيين جميع الرسائل كمقروءة
    await _messagingRepository.markMessagesAsRead(conversationId, user.uid);
  }

  /// تعيين المستخدم كيكتب حالياً
  Future<void> setTyping(String conversationId, bool isTyping) async {
    final user = _auth.currentUser;
    if (user == null) {
      return;
    }

    // الحصول على معلومات المحادثة
    final conversation =
        await _messagingRepository.getConversationById(conversationId);
    if (conversation == null) {
      return;
    }

    // التحقق من أن المستخدم مشارك في المحادثة
    if (!conversation.participantIds.contains(user.uid)) {
      return;
    }

    // تعيين حالة الكتابة
    await _messagingRepository.setParticipantTyping(
        conversationId, user.uid, isTyping);
  }

  /// حذف رسالة
  Future<bool> deleteMessage(String messageId) async {
    final user = _auth.currentUser;
    if (user == null) {
      return false;
    }

    // الحصول على معلومات الرسالة
    final message = await _messagingRepository.getMessageById(messageId);
    if (message == null) {
      return false;
    }

    // التحقق من أن المستخدم هو المرسل
    if (message.senderId != user.uid) {
      return false;
    }

    // حذف الرسالة
    await _messagingRepository.deleteMessage(messageId);
    return true;
  }

  /// الاستماع للتغييرات في محادثات المستخدم
  Stream<List<Conversation>> listenToUserConversations() {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.value([]);
    }

    return _messagingRepository.listenToUserConversations(user.uid);
  }

  /// الاستماع للتغييرات في رسائل محادثة
  Stream<List<Message>> listenToConversationMessages(String conversationId) {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.value([]);
    }

    return _messagingRepository.listenToConversationMessages(conversationId);
  }

  /// الاستماع للتغييرات في حالة الكتابة للمشاركين
  Stream<List<String>> listenToTypingParticipants(String conversationId) {
    return _messagingRepository.listenToTypingParticipants(conversationId);
  }
}
