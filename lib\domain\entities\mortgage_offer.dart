import 'package:equatable/equatable.dart';

/// كيان عرض التمويل العقاري
class MortgageOffer extends Equatable {
  final String id;
  final String bankId;
  final String bankName;
  final String bankLogoUrl;
  final String mortgageType;
  final double interestRate;
  final double annualPercentageRate;
  final double loanAmount;
  final double downPayment;
  final int loanTerm;
  final double monthlyPayment;
  final double totalPayment;
  final double totalInterest;
  final double processingFee;
  final double earlyPaymentFee;
  final List<String> requiredDocuments;
  final Map<String, dynamic>? additionalFeatures;
  final Map<String, dynamic>? conditions;
  final DateTime expiryDate;

  /// إنشاء كيان عرض التمويل العقاري
  const MortgageOffer({
    required this.id,
    required this.bankId,
    required this.bankName,
    required this.bankLogoUrl,
    required this.mortgageType,
    required this.interestRate,
    required this.annualPercentageRate,
    required this.loanAmount,
    required this.downPayment,
    required this.loanTerm,
    required this.monthlyPayment,
    required this.totalPayment,
    required this.totalInterest,
    required this.processingFee,
    required this.earlyPaymentFee,
    required this.requiredDocuments,
    this.additionalFeatures,
    this.conditions,
    required this.expiryDate,
  });

  /// إنشاء كيان عرض التمويل العقاري من JSON
  factory MortgageOffer.fromJson(Map<String, dynamic> json) {
    return MortgageOffer(
      id: json['id'] as String,
      bankId: json['bankId'] as String,
      bankName: json['bankName'] as String,
      bankLogoUrl: json['bankLogoUrl'] as String,
      mortgageType: json['mortgageType'] as String,
      interestRate: json['interestRate'] as double,
      annualPercentageRate: json['annualPercentageRate'] as double,
      loanAmount: json['loanAmount'] as double,
      downPayment: json['downPayment'] as double,
      loanTerm: json['loanTerm'] as int,
      monthlyPayment: json['monthlyPayment'] as double,
      totalPayment: json['totalPayment'] as double,
      totalInterest: json['totalInterest'] as double,
      processingFee: json['processingFee'] as double,
      earlyPaymentFee: json['earlyPaymentFee'] as double,
      requiredDocuments: List<String>.from(json['requiredDocuments']),
      additionalFeatures: json['additionalFeatures'] as Map<String, dynamic>?,
      conditions: json['conditions'] as Map<String, dynamic>?,
      expiryDate: DateTime.parse(json['expiryDate'] as String));
  }

  /// تحويل كيان عرض التمويل العقاري إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bankId': bankId,
      'bankName': bankName,
      'bankLogoUrl': bankLogoUrl,
      'mortgageType': mortgageType,
      'interestRate': interestRate,
      'annualPercentageRate': annualPercentageRate,
      'loanAmount': loanAmount,
      'downPayment': downPayment,
      'loanTerm': loanTerm,
      'monthlyPayment': monthlyPayment,
      'totalPayment': totalPayment,
      'totalInterest': totalInterest,
      'processingFee': processingFee,
      'earlyPaymentFee': earlyPaymentFee,
      'requiredDocuments': requiredDocuments,
      'additionalFeatures': additionalFeatures,
      'conditions': conditions,
      'expiryDate': expiryDate.toIso8601String(),
    };
  }

  /// نسخ كيان عرض التمويل العقاري مع تعديل بعض الخصائص
  MortgageOffer copyWith({
    String? id,
    String? bankId,
    String? bankName,
    String? bankLogoUrl,
    String? mortgageType,
    double? interestRate,
    double? annualPercentageRate,
    double? loanAmount,
    double? downPayment,
    int? loanTerm,
    double? monthlyPayment,
    double? totalPayment,
    double? totalInterest,
    double? processingFee,
    double? earlyPaymentFee,
    List<String>? requiredDocuments,
    Map<String, dynamic>? additionalFeatures,
    Map<String, dynamic>? conditions,
    DateTime? expiryDate,
  }) {
    return MortgageOffer(
      id: id ?? this.id,
      bankId: bankId ?? this.bankId,
      bankName: bankName ?? this.bankName,
      bankLogoUrl: bankLogoUrl ?? this.bankLogoUrl,
      mortgageType: mortgageType ?? this.mortgageType,
      interestRate: interestRate ?? this.interestRate,
      annualPercentageRate: annualPercentageRate ?? this.annualPercentageRate,
      loanAmount: loanAmount ?? this.loanAmount,
      downPayment: downPayment ?? this.downPayment,
      loanTerm: loanTerm ?? this.loanTerm,
      monthlyPayment: monthlyPayment ?? this.monthlyPayment,
      totalPayment: totalPayment ?? this.totalPayment,
      totalInterest: totalInterest ?? this.totalInterest,
      processingFee: processingFee ?? this.processingFee,
      earlyPaymentFee: earlyPaymentFee ?? this.earlyPaymentFee,
      requiredDocuments: requiredDocuments ?? this.requiredDocuments,
      additionalFeatures: additionalFeatures ?? this.additionalFeatures,
      conditions: conditions ?? this.conditions,
      expiryDate: expiryDate ?? this.expiryDate);
  }

  /// حساب نسبة القرض إلى القيمة
  double getLoanToValueRatio() {
    final propertyValue = loanAmount + downPayment;
    return loanAmount / propertyValue;
  }

  /// التحقق مما إذا كان العرض ساري المفعول
  bool isValid() {
    return DateTime.now().isBefore(expiryDate);
  }

  /// الحصول على المدة المتبقية لصلاحية العرض بالأيام
  int getRemainingDays() {
    final now = DateTime.now();
    return expiryDate.difference(now).inDays;
  }

  @override
  List<Object?> get props => [
        id,
        bankId,
        bankName,
        bankLogoUrl,
        mortgageType,
        interestRate,
        annualPercentageRate,
        loanAmount,
        downPayment,
        loanTerm,
        monthlyPayment,
        totalPayment,
        totalInterest,
        processingFee,
        earlyPaymentFee,
        requiredDocuments,
        additionalFeatures,
        conditions,
        expiryDate,
      ];
}
