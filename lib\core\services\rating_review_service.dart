import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// أنواع العناصر القابلة للتقييم
enum RatableItemType {
  /// عقار
  estate,

  /// مستخدم
  user,

  /// وكيل عقاري
  agent,

  /// شركة عقارية
  company,

  /// موضوع منتدى
  forumTopic,

  /// مشاركة منتدى
  forumPost,
}

/// نموذج بيانات التقييم
class RatingModel {
  /// معرف التقييم
  final String id;

  /// معرف العنصر المقيم
  final String itemId;

  /// نوع العنصر المقيم
  final RatableItemType itemType;

  /// معرف المستخدم المقيم
  final String userId;

  /// اسم المستخدم المقيم
  final String userName;

  /// صورة المستخدم المقيم
  final String? userPhotoUrl;

  /// قيمة التقييم (1-5)
  final double rating;

  /// نص المراجعة
  final String? review;

  /// تاريخ التقييم
  final DateTime createdAt;

  /// تاريخ آخر تحديث
  final DateTime updatedAt;

  /// ما إذا كان التقييم معتمد
  final bool isApproved;

  /// ردود على المراجعة
  final List<ReplyModel> replies;

  RatingModel({
    required this.id,
    required this.itemId,
    required this.itemType,
    required this.userId,
    required this.userName,
    this.userPhotoUrl,
    required this.rating,
    this.review,
    required this.createdAt,
    required this.updatedAt,
    required this.isApproved,
    required this.replies,
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory RatingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    final repliesData = data['replies'] as List<dynamic>? ?? [];
    final replies = repliesData
        .map((replyData) =>
            ReplyModel.fromMap(replyData as Map<String, dynamic>))
        .toList();

    return RatingModel(
      id: doc.id,
      itemId: data['itemId'] ?? '',
      itemType: _getRatableItemTypeFromString(data['itemType'] ?? 'estate'),
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userPhotoUrl: data['userPhotoUrl'],
      rating: (data['rating'] as num?)?.toDouble() ?? 0.0,
      review: data['review'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      isApproved: data['isApproved'] ?? false,
      replies: replies);
  }

  /// تحويل النموذج إلى Map لتخزينه في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'itemId': itemId,
      'itemType': itemType.toString().split('.').last,
      'userId': userId,
      'userName': userName,
      'userPhotoUrl': userPhotoUrl,
      'rating': rating,
      'review': review,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isApproved': isApproved,
      'replies': replies.map((reply) => reply.toMap()).toList(),
    };
  }

  /// الحصول على نوع العنصر القابل للتقييم من النص
  static RatableItemType _getRatableItemTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'user':
        return RatableItemType.user;
      case 'agent':
        return RatableItemType.agent;
      case 'company':
        return RatableItemType.company;
      case 'forumTopic':
        return RatableItemType.forumTopic;
      case 'forumPost':
        return RatableItemType.forumPost;
      default:
        return RatableItemType.estate;
    }
  }
}

/// نموذج بيانات الرد على المراجعة
class ReplyModel {
  /// معرف المستخدم المجيب
  final String userId;

  /// اسم المستخدم المجيب
  final String userName;

  /// صورة المستخدم المجيب
  final String? userPhotoUrl;

  /// نص الرد
  final String content;

  /// تاريخ الرد
  final DateTime createdAt;

  /// ما إذا كان الرد من المالك
  final bool isOwner;

  ReplyModel({
    required this.userId,
    required this.userName,
    this.userPhotoUrl,
    required this.content,
    required this.createdAt,
    required this.isOwner,
  });

  /// إنشاء نموذج من Map
  factory ReplyModel.fromMap(Map<String, dynamic> data) {
    return ReplyModel(
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userPhotoUrl: data['userPhotoUrl'],
      content: data['content'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      isOwner: data['isOwner'] ?? false);
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'userPhotoUrl': userPhotoUrl,
      'content': content,
      'createdAt': Timestamp.fromDate(createdAt),
      'isOwner': isOwner,
    };
  }
}

/// نموذج بيانات ملخص التقييمات
class RatingSummaryModel {
  /// معرف العنصر
  final String itemId;

  /// نوع العنصر
  final RatableItemType itemType;

  /// متوسط التقييم
  final double averageRating;

  /// عدد التقييمات
  final int ratingsCount;

  /// توزيع التقييمات (1-5)
  final Map<int, int> ratingDistribution;

  RatingSummaryModel({
    required this.itemId,
    required this.itemType,
    required this.averageRating,
    required this.ratingsCount,
    required this.ratingDistribution,
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory RatingSummaryModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    final distributionData =
        data['ratingDistribution'] as Map<String, dynamic>? ?? {};
    final ratingDistribution = {
      1: distributionData['1'] as int? ?? 0,
      2: distributionData['2'] as int? ?? 0,
      3: distributionData['3'] as int? ?? 0,
      4: distributionData['4'] as int? ?? 0,
      5: distributionData['5'] as int? ?? 0,
    };

    return RatingSummaryModel(
      itemId: doc.id,
      itemType: RatingModel._getRatableItemTypeFromString(
          data['itemType'] ?? 'estate'),
      averageRating: (data['averageRating'] as num?)?.toDouble() ?? 0.0,
      ratingsCount: data['ratingsCount'] as int? ?? 0,
      ratingDistribution: ratingDistribution);
  }

  /// تحويل النموذج إلى Map لتخزينه في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'itemType': itemType.toString().split('.').last,
      'averageRating': averageRating,
      'ratingsCount': ratingsCount,
      'ratingDistribution': {
        '1': ratingDistribution[1],
        '2': ratingDistribution[2],
        '3': ratingDistribution[3],
        '4': ratingDistribution[4],
        '5': ratingDistribution[5],
      },
    };
  }

  /// الحصول على نسبة التقييمات لكل نجمة
  Map<int, double> getRatingPercentages() {
    final percentages = <int, double>{};

    if (ratingsCount > 0) {
      for (int i = 1; i <= 5; i++) {
        percentages[i] = (ratingDistribution[i] ?? 0) / ratingsCount;
      }
    } else {
      for (int i = 1; i <= 5; i++) {
        percentages[i] = 0.0;
      }
    }

    return percentages;
  }
}

/// خدمة التقييمات والمراجعات
class RatingReviewService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  RatingReviewService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  /// إضافة تقييم جديد
  Future<String?> addRating({
    required String itemId,
    required RatableItemType itemType,
    required double rating,
    String? review,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لإضافة تقييم');
      }

      // التحقق من عدم وجود تقييم سابق
      final existingRating = await _getUserRatingForItem(itemId);
      if (existingRating != null) {
        throw Exception('لديك تقييم سابق لهذا العنصر');
      }

      // الحصول على معلومات المستخدم
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      final userData = userDoc.data();

      final userName =
          userData?['name'] as String? ?? user.displayName ?? 'مستخدم';
      final userPhotoUrl = userData?['photoURL'] as String? ?? user.photoURL;

      // إنشاء التقييم
      final newRating = RatingModel(
        id: '',
        itemId: itemId,
        itemType: itemType,
        userId: user.uid,
        userName: userName,
        userPhotoUrl: userPhotoUrl,
        rating: rating,
        review: review,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isApproved:
            true, // يمكن تغييره إلى false إذا كان التقييم يحتاج إلى موافقة
        replies: []);

      // حفظ التقييم في Firestore
      final docRef =
          await _firestore.collection('ratings').add(newRating.toFirestore());

      // تحديث ملخص التقييمات
      await _updateRatingSummary(itemId, itemType, rating, true);

      return docRef.id;
    } catch (e) {
      return null;
    }
  }

  /// تحديث تقييم موجود
  Future<bool> updateRating({
    required String ratingId,
    required double rating,
    String? review,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // الحصول على التقييم الحالي
      final ratingDoc =
          await _firestore.collection('ratings').doc(ratingId).get();
      if (!ratingDoc.exists) {
        return false;
      }

      final currentRating = RatingModel.fromFirestore(ratingDoc);

      // التحقق من أن المستخدم هو صاحب التقييم
      if (currentRating.userId != user.uid) {
        return false;
      }

      // تحديث ملخص التقييمات
      await _updateRatingSummary(
        currentRating.itemId,
        currentRating.itemType,
        rating,
        false,
        oldRating: currentRating.rating);

      // تحديث التقييم
      await _firestore.collection('ratings').doc(ratingId).update({
        'rating': rating,
        'review': review,
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      return false;
    }
  }

  /// حذف تقييم
  Future<bool> deleteRating(String ratingId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // الحصول على التقييم
      final ratingDoc =
          await _firestore.collection('ratings').doc(ratingId).get();
      if (!ratingDoc.exists) {
        return false;
      }

      final rating = RatingModel.fromFirestore(ratingDoc);

      // التحقق من أن المستخدم هو صاحب التقييم
      if (rating.userId != user.uid) {
        return false;
      }

      // تحديث ملخص التقييمات
      await _updateRatingSummary(
        rating.itemId,
        rating.itemType,
        0,
        false,
        oldRating: rating.rating,
        isDelete: true);

      // حذف التقييم
      await _firestore.collection('ratings').doc(ratingId).delete();

      return true;
    } catch (e) {
      return false;
    }
  }

  /// إضافة رد على مراجعة
  Future<bool> addReply({
    required String ratingId,
    required String content,
    required bool isOwner,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // الحصول على معلومات المستخدم
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      final userData = userDoc.data();

      final userName =
          userData?['name'] as String? ?? user.displayName ?? 'مستخدم';
      final userPhotoUrl = userData?['photoURL'] as String? ?? user.photoURL;

      // إنشاء الرد
      final reply = ReplyModel(
        userId: user.uid,
        userName: userName,
        userPhotoUrl: userPhotoUrl,
        content: content,
        createdAt: DateTime.now(),
        isOwner: isOwner);

      // إضافة الرد إلى التقييم
      await _firestore.collection('ratings').doc(ratingId).update({
        'replies': FieldValue.arrayUnion([reply.toMap()]),
      });

      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على تقييمات عنصر
  Future<List<RatingModel>> getItemRatings(String itemId,
      {int limit = 10}) async {
    try {
      final snapshot = await _firestore
          .collection('ratings')
          .where('itemId', isEqualTo: itemId)
          .where('isApproved', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => RatingModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على ملخص تقييمات عنصر
  Future<RatingSummaryModel?> getItemRatingSummary(String itemId) async {
    try {
      final doc =
          await _firestore.collection('ratingSummaries').doc(itemId).get();

      if (!doc.exists) {
        return null;
      }

      return RatingSummaryModel.fromFirestore(doc);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على تقييم المستخدم الحالي لعنصر
  Future<RatingModel?> _getUserRatingForItem(String itemId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return null;
      }

      final snapshot = await _firestore
          .collection('ratings')
          .where('itemId', isEqualTo: itemId)
          .where('userId', isEqualTo: user.uid)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) {
        return null;
      }

      return RatingModel.fromFirestore(snapshot.docs.first);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على تقييم المستخدم الحالي لعنصر
  Future<RatingModel?> getUserRatingForItem(String itemId) async {
    return _getUserRatingForItem(itemId);
  }

  /// الحصول على تقييمات المستخدم الحالي
  Future<List<RatingModel>> getCurrentUserRatings({int limit = 10}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return [];
      }

      final snapshot = await _firestore
          .collection('ratings')
          .where('userId', isEqualTo: user.uid)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => RatingModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على تقييمات مستخدم محدد
  Future<List<RatingModel>> getUserRatings(String userId,
      {int limit = 10}) async {
    try {
      final snapshot = await _firestore
          .collection('ratings')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => RatingModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// الإعجاب بتقييم
  Future<bool> likeRating(String ratingId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // إضافة معرف المستخدم إلى قائمة المعجبين
      await _firestore.collection('ratings').doc(ratingId).update({
        'likedBy': FieldValue.arrayUnion([user.uid]),
        'likesCount': FieldValue.increment(1),
      });

      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء الإعجاب بتقييم
  Future<bool> unlikeRating(String ratingId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // إزالة معرف المستخدم من قائمة المعجبين
      await _firestore.collection('ratings').doc(ratingId).update({
        'likedBy': FieldValue.arrayRemove([user.uid]),
        'likesCount': FieldValue.increment(-1),
      });

      return true;
    } catch (e) {
      return false;
    }
  }

  /// الإبلاغ عن تقييم
  Future<bool> reportRating(String ratingId, String reason) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // إضافة تقرير الإبلاغ
      await _firestore.collection('ratingReports').add({
        'ratingId': ratingId,
        'userId': user.uid,
        'reason': reason,
        'createdAt': Timestamp.now(),
        'status': 'pending', // pending, reviewed, dismissed
      });

      // تحديث حالة التقييم
      await _firestore.collection('ratings').doc(ratingId).update({
        'isReported': true,
      });

      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على تقييمات عنصر مع التحميل المتدرج
  Future<Map<String, dynamic>> getItemRatingsPaginated(
    String itemId, {
    int limit = 10,
    String? lastRatingId,
  }) async {
    try {
      Query query = _firestore
          .collection('ratings')
          .where('itemId', isEqualTo: itemId)
          .where('isApproved', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastRatingId != null) {
        final lastDoc =
            await _firestore.collection('ratings').doc(lastRatingId).get();

        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final snapshot = await query.get();
      final ratings =
          snapshot.docs.map((doc) => RatingModel.fromFirestore(doc)).toList();

      String? lastId;
      if (ratings.isNotEmpty) {
        lastId = ratings.last.id;
      }

      return {
        'ratings': ratings,
        'lastRatingId': lastId,
        'hasMore': ratings.length >= limit,
      };
    } catch (e) {
      return {
        'ratings': <RatingModel>[],
        'lastRatingId': null,
        'hasMore': false,
      };
    }
  }

  /// تحديث ملخص التقييمات
  Future<void> _updateRatingSummary(
    String itemId,
    RatableItemType itemType,
    double newRating,
    bool isNewRating, {
    double? oldRating,
    bool isDelete = false,
  }) async {
    try {
      final docRef = _firestore.collection('ratingSummaries').doc(itemId);

      // الحصول على ملخص التقييمات الحالي
      final doc = await docRef.get();

      if (!doc.exists) {
        // إنشاء ملخص جديد إذا لم يكن موجودًا
        if (isNewRating && !isDelete) {
          final ratingDistribution = {
            1: newRating == 1 ? 1 : 0,
            2: newRating == 2 ? 1 : 0,
            3: newRating == 3 ? 1 : 0,
            4: newRating == 4 ? 1 : 0,
            5: newRating == 5 ? 1 : 0,
          };

          final summary = RatingSummaryModel(
            itemId: itemId,
            itemType: itemType,
            averageRating: newRating,
            ratingsCount: 1,
            ratingDistribution: ratingDistribution);

          await docRef.set(summary.toFirestore());
        }
      } else {
        // تحديث ملخص موجود
        final summary = RatingSummaryModel.fromFirestore(doc);

        if (isDelete) {
          // حذف تقييم
          final oldRatingInt = oldRating!.round();
          final newRatingsCount = summary.ratingsCount - 1;

          final newDistribution =
              Map<int, int>.from(summary.ratingDistribution);
          newDistribution[oldRatingInt] =
              (newDistribution[oldRatingInt] ?? 0) - 1;

          double newAverage = 0;
          if (newRatingsCount > 0) {
            double sum = 0;
            for (int i = 1; i <= 5; i++) {
              sum += i * (newDistribution[i] ?? 0);
            }
            newAverage = sum / newRatingsCount;
          }

          await docRef.update({
            'averageRating': newAverage,
            'ratingsCount': newRatingsCount,
            'ratingDistribution.$oldRatingInt': FieldValue.increment(-1),
          });
        } else if (isNewRating) {
          // إضافة تقييم جديد
          final newRatingInt = newRating.round();
          final newRatingsCount = summary.ratingsCount + 1;

          final newDistribution =
              Map<int, int>.from(summary.ratingDistribution);
          newDistribution[newRatingInt] =
              (newDistribution[newRatingInt] ?? 0) + 1;

          double sum = summary.averageRating * summary.ratingsCount + newRating;
          double newAverage = sum / newRatingsCount;

          await docRef.update({
            'averageRating': newAverage,
            'ratingsCount': newRatingsCount,
            'ratingDistribution.$newRatingInt': FieldValue.increment(1),
          });
        } else {
          // تحديث تقييم موجود
          final oldRatingInt = oldRating!.round();
          final newRatingInt = newRating.round();

          if (oldRatingInt != newRatingInt) {
            final newDistribution =
                Map<int, int>.from(summary.ratingDistribution);
            newDistribution[oldRatingInt] =
                (newDistribution[oldRatingInt] ?? 0) - 1;
            newDistribution[newRatingInt] =
                (newDistribution[newRatingInt] ?? 0) + 1;

            double sum = summary.averageRating * summary.ratingsCount -
                oldRating +
                newRating;
            double newAverage = sum / summary.ratingsCount;

            final batch = _firestore.batch();

            batch.update(docRef, {
              'averageRating': newAverage,
              'ratingDistribution.$oldRatingInt': FieldValue.increment(-1),
              'ratingDistribution.$newRatingInt': FieldValue.increment(1),
            });

            await batch.commit();
          }
        }
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }
}
