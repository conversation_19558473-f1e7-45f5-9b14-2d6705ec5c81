import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/category_model.dart';
import '../../../domain/models/forum/filter_options_model.dart';
import '../../../presentation/providers/auth_provider.dart' as app_auth;
import '../../../presentation/providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/empty_view.dart';
import '../../widgets/forum/modern_topics_list.dart';
import '../../widgets/forum/modern_filter_panel.dart';
import 'create_edit_topic_page.dart';
import 'modern_topic_detail_page.dart';

/// صفحة قائمة المواضيع
class TopicsListPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/topics';

  /// معرف الفئة
  final String? categoryId;

  /// عنوان الصفحة
  final String? title;

  /// خيارات الفلترة الأولية
  final ForumFilterOptionsModel? initialFilterOptions;

  const TopicsListPage({
    super.key,
    this.categoryId,
    this.title,
    this.initialFilterOptions,
  });

  @override
  State<TopicsListPage> createState() => _TopicsListPageState();
}

class _TopicsListPageState extends State<TopicsListPage> {
  late ForumFilterOptionsModel _filterOptions;
  bool _isFilterPanelVisible = false;
  final ScrollController _scrollController = ScrollController();
  CategoryModel? _category;

  @override
  void initState() {
    super.initState();
    _filterOptions = widget.initialFilterOptions ??
        ForumFilterOptionsModel(
          categoryId: widget.categoryId);
    _scrollController.addListener(_scrollListener);
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  /// استماع لحدث التمرير
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      if (forumProvider.topicsState != LoadingState.loading &&
          forumProvider.hasMoreTopics) {
        forumProvider.fetchMoreTopics(_filterOptions);
      }
    }
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    
    // تحميل الفئات إذا لم تكن محملة
    if (forumProvider.categories.isEmpty) {
      await forumProvider.fetchCategories();
    }
    
    // تحميل الفئة الحالية
    if (widget.categoryId != null) {
      _category = forumProvider.categories.firstWhere(
        (category) => category.id == widget.categoryId,
        orElse: () => forumProvider.categories.first);
    }
    
    // تحميل المواضيع
    await forumProvider.fetchTopics(_filterOptions);
  }

  /// تحديث خيارات الفلترة
  void _updateFilterOptions(ForumFilterOptionsModel newOptions) {
    setState(() {
      _filterOptions = newOptions;
      _isFilterPanelVisible = false;
    });
    
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    forumProvider.fetchTopics(_filterOptions);
  }

  /// إنشاء موضوع جديد
  void _createNewTopic() {
    Navigator.pushNamed(
      context,
      CreateEditTopicPage.routeName,
      arguments: {
        'categoryId': widget.categoryId,
      }).then((result) {
      if (result == true) {
        _loadData();
      }
    });
  }

  /// فتح تفاصيل الموضوع
  void _openTopicDetails(String topicId) {
    Navigator.pushNamed(
      context,
      ModernTopicDetailPage.routeName,
      arguments: topicId).then((_) {
      // تحديث القائمة عند العودة
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      forumProvider.fetchTopics(_filterOptions);
    });
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);
    final forumProvider = Provider.of<ForumProvider>(context);
    final isLoggedIn = authProvider.isLoggedIn;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? _category?.name ?? 'المواضيع'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              setState(() {
                _isFilterPanelVisible = !_isFilterPanelVisible;
              });
            }),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // فتح صفحة البحث
            }),
        ]),
      body: Column(
        children: [
          // لوحة الفلترة
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isFilterPanelVisible ? null : 0,
            child: Visibility(
              visible: _isFilterPanelVisible,
              child: ModernFilterPanel(
                initialFilterOptions: _filterOptions,
                onApplyFilters: _updateFilterOptions,
                onClose: () {
                  setState(() {
                    _isFilterPanelVisible = false;
                  });
                }))),
          
          // معلومات الفئة
          if (_category != null) _buildCategoryInfo(),
          
          // قائمة المواضيع
          Expanded(
            child: _buildTopicsList(forumProvider)),
        ]),
      floatingActionButton: isLoggedIn
          ? FloatingActionButton(
              onPressed: _createNewTopic,
              backgroundColor: AppColors.primary,
              child: const Icon(Icons.add))
          : null);
  }

  /// بناء معلومات الفئة
  Widget _buildCategoryInfo() {
    if (_category == null) {
      return const SizedBox.shrink();
    }
    
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey.shade50,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.category,
                color: _category!.getColorObject()),
              const SizedBox(width: 8),
              Text(
                _category!.name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16)),
              const Spacer(),
              Text(
                '${_category!.topicsCount} موضوع',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12)),
              const SizedBox(width: 8),
              Text(
                '${_category!.postsCount} مشاركة',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12)),
            ]),
          if (_category!.description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              _category!.description,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14)),
          ],
        ]));
  }

  /// بناء قائمة المواضيع
  Widget _buildTopicsList(ForumProvider forumProvider) {
    if (forumProvider.topicsState == LoadingState.loading &&
        forumProvider.topics.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (forumProvider.topicsState == LoadingState.error) {
      return ErrorView(
        message: 'حدث خطأ أثناء تحميل المواضيع',
        onRetry: _loadData);
    }

    if (forumProvider.topics.isEmpty) {
      return EmptyView(
        message: 'لا توجد مواضيع',
        actionLabel: 'إنشاء موضوع جديد',
        onAction: Provider.of<app_auth.AuthProvider>(context).isLoggedIn
            ? _createNewTopic
            : null);
    }

    return ModernTopicsList(
      topics: forumProvider.topics,
      scrollController: _scrollController,
      isLoadingMore: forumProvider.topicsState == LoadingState.loading &&
          forumProvider.topics.isNotEmpty,
      onTopicTap: _openTopicDetails,
      onLikeTap: (topicId) {
        final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
        if (authProvider.isLoggedIn && authProvider.user != null) {
          forumProvider.likeTopic(topicId, authProvider.user!.uid);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
        }
      },
      onBookmarkTap: (topicId) {
        final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
        if (authProvider.isLoggedIn && authProvider.user != null) {
          forumProvider.bookmarkTopic(topicId, authProvider.user!.uid);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
        }
      },
      onShareTap: (topicId) {
        final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
        final userId = authProvider.isLoggedIn ? authProvider.user!.uid : '';
        forumProvider.shareTopic(topicId, userId);
      },
      currentUserId: Provider.of<app_auth.AuthProvider>(context).user?.uid);
  }
}
