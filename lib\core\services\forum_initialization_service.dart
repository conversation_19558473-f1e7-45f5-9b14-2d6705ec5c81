import 'dart:developer' as developer;

import 'package:cloud_firestore/cloud_firestore.dart';

import '../../domain/models/forum/badge_model.dart';
import '../../domain/models/forum/achievement_model.dart';


/// خدمة تهيئة المنتدى
class ForumInitializationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PropertyIntegrationService _propertyIntegrationService = PropertyIntegrationService();

  /// تهيئة المنتدى بالكامل
  Future<void> initializeForum() async {
    try {
      developer.log('بدء تهيئة المنتدى...');

      await Future.wait([
        _createDefaultCategories(),
        _createPropertyCategories(),
        _initializeBadges(),
        _initializeAchievements(),
        _setupDefaultSettings(),
      ]);

      developer.log('تم تهيئة المنتدى بنجاح');
    } catch (e) {
      developer.log('خطأ في تهيئة المنتدى: $e');
    }
  }

  /// إنشاء الفئات الافتراضية
  Future<void> _createDefaultCategories() async {
    try {
      final defaultCategories = [
        {
          'id': 'general_discussion',
          'name': 'النقاش العام',
          'description': 'مناقشات عامة حول مختلف المواضيع',
          'icon': 'chat',
          'color': '#2196F3',
          'order': 1,
          'type': 0, // CategoryType.general
          'isPropertyRelated': false,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'announcements',
          'name': 'الإعلانات',
          'description': 'إعلانات مهمة من إدارة التطبيق',
          'icon': 'announcement',
          'color': '#FF5722',
          'order': 2,
          'type': 1, // CategoryType.announcement
          'isPropertyRelated': false,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'help_support',
          'name': 'المساعدة والدعم',
          'description': 'طلب المساعدة والدعم الفني',
          'icon': 'help',
          'color': '#4CAF50',
          'order': 3,
          'type': 2, // CategoryType.support
          'isPropertyRelated': false,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'feedback',
          'name': 'الملاحظات والاقتراحات',
          'description': 'شاركنا ملاحظاتك واقتراحاتك لتحسين التطبيق',
          'icon': 'feedback',
          'color': '#9C27B0',
          'order': 4,
          'type': 0, // CategoryType.general
          'isPropertyRelated': false,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
      ];

      for (final categoryData in defaultCategories) {
        await _firestore
            .collection('forum_categories')
            .doc(categoryData['id'] as String)
            .set(categoryData, SetOptions(merge: true));
      }

      developer.log('تم إنشاء الفئات الافتراضية');
    } catch (e) {
      developer.log('خطأ في إنشاء الفئات الافتراضية: $e');
    }
  }

  /// إنشاء فئات العقارات
  Future<void> _createPropertyCategories() async {
    try {
      await _propertyIntegrationService.createPropertyCategories();
      developer.log('تم إنشاء فئات العقارات');
    } catch (e) {
      developer.log('خطأ في إنشاء فئات العقارات: $e');
    }
  }

  /// تهيئة الشارات
  Future<void> _initializeBadges() async {
    try {
      final badges = BadgeModel.getAvailableBadges();
      
      for (final badge in badges) {
        await _firestore
            .collection('forum_badges')
            .doc(badge.id)
            .set(badge.toMap(), SetOptions(merge: true));
      }

      developer.log('تم تهيئة ${badges.length} شارة');
    } catch (e) {
      developer.log('خطأ في تهيئة الشارات: $e');
    }
  }

  /// تهيئة الإنجازات
  Future<void> _initializeAchievements() async {
    try {
      final achievements = AchievementModel.getAvailableAchievements();
      
      for (final achievement in achievements) {
        await _firestore
            .collection('forum_achievements')
            .doc(achievement.id)
            .set(achievement.toMap(), SetOptions(merge: true));
      }

      developer.log('تم تهيئة ${achievements.length} إنجاز');
    } catch (e) {
      developer.log('خطأ في تهيئة الإنجازات: $e');
    }
  }

  /// إعداد الإعدادات الافتراضية
  Future<void> _setupDefaultSettings() async {
    try {
      final defaultSettings = {
        'forum_enabled': true,
        'polls_enabled': true,
        'rewards_enabled': true,
        'property_integration_enabled': true,
        'moderation_enabled': true,
        'max_topics_per_day': 10,
        'max_posts_per_day': 50,
        'min_points_to_create_poll': 50,
        'points_for_topic': 10,
        'points_for_post': 5,
        'points_for_like_received': 2,
        'points_for_like_given': 1,
        'points_for_daily_login': 3,
        'points_for_poll_creation': 8,
        'points_for_poll_vote': 2,
        'auto_moderation_enabled': true,
        'spam_detection_enabled': true,
        'profanity_filter_enabled': true,
        'max_images_per_topic': 5,
        'max_images_per_post': 3,
        'image_max_size_mb': 5,
        'allowed_image_types': ['jpg', 'jpeg', 'png', 'gif'],
        'topic_title_min_length': 10,
        'topic_title_max_length': 200,
        'topic_content_min_length': 20,
        'topic_content_max_length': 10000,
        'post_content_min_length': 5,
        'post_content_max_length': 5000,
        'poll_question_min_length': 10,
        'poll_question_max_length': 200,
        'poll_option_min_length': 2,
        'poll_option_max_length': 100,
        'poll_max_options': 10,
        'poll_min_options': 2,
        'default_poll_duration_days': 7,
        'max_poll_duration_days': 30,
        'notification_enabled': true,
        'email_notifications_enabled': false,
        'push_notifications_enabled': true,
        'mention_notifications_enabled': true,
        'reply_notifications_enabled': true,
        'like_notifications_enabled': true,
        'follow_notifications_enabled': true,
        'bookmark_notifications_enabled': false,
        'report_notifications_enabled': true,
        'achievement_notifications_enabled': true,
        'badge_notifications_enabled': true,
        'level_up_notifications_enabled': true,
        'created_at': Timestamp.now(),
        'updated_at': Timestamp.now(),
      };

      await _firestore
          .collection('forum_settings')
          .doc('default')
          .set(defaultSettings, SetOptions(merge: true));

      developer.log('تم إعداد الإعدادات الافتراضية');
    } catch (e) {
      developer.log('خطأ في إعداد الإعدادات الافتراضية: $e');
    }
  }

  /// إنشاء موضوع ترحيبي
  Future<void> createWelcomeTopic() async {
    try {
      final welcomeTopicData = {
        'id': 'welcome_topic',
        'title': 'مرحباً بكم في منتدى كريا',
        'content': '''
مرحباً بكم في منتدى كريا للعقارات!

هذا المنتدى مخصص لمناقشة كل ما يتعلق بالعقارات في الكويت، من البيع والشراء إلى الاستثمار والاستشارات القانونية.

## ما يمكنكم فعله هنا:

### 🏠 مناقشة العقارات
- شاركوا آراءكم حول العقارات المختلفة
- اطلبوا المشورة من الخبراء
- ناقشوا أسعار السوق والاتجاهات

### 📊 الاستطلاعات والتصويت
- شاركوا في الاستطلاعات حول السوق العقاري
- أنشئوا استطلاعاتكم الخاصة
- صوتوا على القضايا المهمة

### 🏆 نظام المكافآت
- احصلوا على نقاط مقابل مشاركاتكم
- اكسبوا شارات وإنجازات
- ارتقوا في المستويات

### 🔗 التكامل مع العقارات
- ربط المواضيع بعقارات محددة
- البحث في المواضيع حسب نوع العقار والموقع
- اقتراحات العقارات ذات الصلة

## قواعد المنتدى:
1. احترموا الآراء المختلفة
2. لا تنشروا محتوى مسيء أو غير لائق
3. تأكدوا من صحة المعلومات قبل نشرها
4. استخدموا الفئات المناسبة لمواضيعكم
5. لا تنشروا إعلانات تجارية بدون إذن

نتمنى لكم تجربة ممتعة ومفيدة في منتدانا!

فريق كريا
        ''',
        'categoryId': 'announcements',
        'userId': 'system',
        'userName': 'إدارة كريا',
        'userAvatar': null,
        'type': 2, // TopicType.announcement
        'status': 0, // TopicStatus.active
        'isRelatedToEstate': false,
        'relatedEstateId': null,
        'relatedEstateTitle': null,
        'relatedEstateImage': null,
        'location': null,
        'tags': ['ترحيب', 'قواعد', 'إرشادات'],
        'images': [],
        'polls': [],
        'viewsCount': 0,
        'repliesCount': 0,
        'likesCount': 0,
        'sharesCount': 0,
        'bookmarksCount': 0,
        'reportsCount': 0,
        'lastActivityAt': Timestamp.now(),
        'isPinned': true,
        'isFeatured': true,
        'isLocked': false,
        'isHidden': false,
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      };

      await _firestore
          .collection('forum_topics')
          .doc('welcome_topic')
          .set(welcomeTopicData, SetOptions(merge: true));

      developer.log('تم إنشاء الموضوع الترحيبي');
    } catch (e) {
      developer.log('خطأ في إنشاء الموضوع الترحيبي: $e');
    }
  }

  /// فحص وإصلاح البيانات
  Future<void> validateAndRepairData() async {
    try {
      developer.log('بدء فحص وإصلاح البيانات...');

      // فحص الفئات
      await _validateCategories();
      
      // فحص المواضيع
      await _validateTopics();
      
      // فحص المشاركات
      await _validatePosts();
      
      // فحص الإحصائيات
      await _validateStatistics();

      developer.log('تم فحص وإصلاح البيانات بنجاح');
    } catch (e) {
      developer.log('خطأ في فحص وإصلاح البيانات: $e');
    }
  }

  /// فحص الفئات
  Future<void> _validateCategories() async {
    try {
      final snapshot = await _firestore.collection('forum_categories').get();
      
      for (final doc in snapshot.docs) {
        final data = doc.data();
        bool needsUpdate = false;
        final updates = <String, dynamic>{};

        // التأكد من وجود الحقول المطلوبة
        if (!data.containsKey('isActive')) {
          updates['isActive'] = true;
          needsUpdate = true;
        }
        
        if (!data.containsKey('order')) {
          updates['order'] = 999;
          needsUpdate = true;
        }
        
        if (!data.containsKey('isPropertyRelated')) {
          updates['isPropertyRelated'] = false;
          needsUpdate = true;
        }

        if (needsUpdate) {
          await doc.reference.update(updates);
        }
      }

      developer.log('تم فحص ${snapshot.docs.length} فئة');
    } catch (e) {
      developer.log('خطأ في فحص الفئات: $e');
    }
  }

  /// فحص المواضيع
  Future<void> _validateTopics() async {
    try {
      final snapshot = await _firestore
          .collection('forum_topics')
          .limit(100)
          .get();
      
      for (final doc in snapshot.docs) {
        final data = doc.data();
        bool needsUpdate = false;
        final updates = <String, dynamic>{};

        // التأكد من وجود الحقول المطلوبة
        if (!data.containsKey('viewsCount')) {
          updates['viewsCount'] = 0;
          needsUpdate = true;
        }
        
        if (!data.containsKey('repliesCount')) {
          updates['repliesCount'] = 0;
          needsUpdate = true;
        }
        
        if (!data.containsKey('likesCount')) {
          updates['likesCount'] = 0;
          needsUpdate = true;
        }

        if (!data.containsKey('isRelatedToEstate')) {
          updates['isRelatedToEstate'] = false;
          needsUpdate = true;
        }

        if (needsUpdate) {
          await doc.reference.update(updates);
        }
      }

      developer.log('تم فحص ${snapshot.docs.length} موضوع');
    } catch (e) {
      developer.log('خطأ في فحص المواضيع: $e');
    }
  }

  /// فحص المشاركات
  Future<void> _validatePosts() async {
    try {
      final snapshot = await _firestore
          .collection('forum_posts')
          .limit(100)
          .get();
      
      for (final doc in snapshot.docs) {
        final data = doc.data();
        bool needsUpdate = false;
        final updates = <String, dynamic>{};

        // التأكد من وجود الحقول المطلوبة
        if (!data.containsKey('likesCount')) {
          updates['likesCount'] = 0;
          needsUpdate = true;
        }
        
        if (!data.containsKey('repliesCount')) {
          updates['repliesCount'] = 0;
          needsUpdate = true;
        }

        if (needsUpdate) {
          await doc.reference.update(updates);
        }
      }

      developer.log('تم فحص ${snapshot.docs.length} مشاركة');
    } catch (e) {
      developer.log('خطأ في فحص المشاركات: $e');
    }
  }

  /// فحص الإحصائيات
  Future<void> _validateStatistics() async {
    try {
      final usersSnapshot = await _firestore
          .collection('user_statistics')
          .limit(50)
          .get();
      
      for (final doc in usersSnapshot.docs) {
        final data = doc.data();
        bool needsUpdate = false;
        final updates = <String, dynamic>{};

        // التأكد من وجود الحقول المطلوبة
        if (!data.containsKey('points')) {
          updates['points'] = 0;
          needsUpdate = true;
        }
        
        if (!data.containsKey('level')) {
          updates['level'] = 'مبتدئ';
          needsUpdate = true;
        }

        if (needsUpdate) {
          await doc.reference.update(updates);
        }
      }

      developer.log('تم فحص ${usersSnapshot.docs.length} إحصائية مستخدم');
    } catch (e) {
      developer.log('خطأ في فحص الإحصائيات: $e');
    }
  }
}
