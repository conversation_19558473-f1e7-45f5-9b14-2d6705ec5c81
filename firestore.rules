rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // قواعد عامة - منع الوصول غير المصرح به
    match /{document=**} {
      allow read, write: if false;
    }

    // قواعد المستخدمين المحسنة
    match /users/{userId} {
      // القراءة: للمستخدم نفسه أو للقراءة العامة للملفات الشخصية المحدودة
      allow read: if request.auth != null &&
        (request.auth.uid == userId || isPublicProfileRead());

      // الكتابة: فقط للمستخدم نفسه مع التحقق من صحة البيانات
      allow write: if request.auth != null &&
        request.auth.uid == userId &&
        isValidUserData();

      // قواعد فرعية للبيانات الحساسة
      match /private/{document} {
        allow read, write: if request.auth != null &&
          request.auth.uid == userId;
      }

      // قواعد الإعدادات
      match /settings/{document} {
        allow read, write: if request.auth != null &&
          request.auth.uid == userId;
      }
    }

    // قواعد طلبات العقارات
    match /property_requests/{requestId} {
      // القراءة: متاحة لجميع المستخدمين المسجلين
      allow read: if request.auth != null;

      // الإنشاء: فقط للباحثين عن عقارات
      allow create: if request.auth != null
        && isValidUser()
        && isSeekerUser()
        && isValidPropertyRequest();

      // التحديث: فقط لصاحب الطلب
      allow update: if request.auth != null
        && request.auth.uid == resource.data.userId
        && isValidPropertyRequestUpdate();

      // الحذف: فقط لصاحب الطلب أو المشرفين
      allow delete: if request.auth != null
        && (request.auth.uid == resource.data.userId || isAdmin());
    }

    // قواعد عروض العقارات
    match /property_offers/{offerId} {
      // القراءة: متاحة لصاحب الطلب ومقدم العرض
      allow read: if request.auth != null
        && (request.auth.uid == resource.data.userId
            || request.auth.uid == getRequestOwner(resource.data.requestId));

      // الإنشاء: فقط للمستثمرين وأصحاب العقارات
      allow create: if request.auth != null
        && isValidUser()
        && canCreateOffer()
        && isValidPropertyOffer();

      // التحديث: فقط لمقدم العرض
      allow update: if request.auth != null
        && request.auth.uid == resource.data.userId
        && isValidPropertyOfferUpdate();

      // الحذف: فقط لمقدم العرض
      allow delete: if request.auth != null
        && request.auth.uid == resource.data.userId;
    }

    // قواعد التعليقات
    match /property_request_comments/{commentId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && isValidComment();
      allow update, delete: if request.auth != null
        && request.auth.uid == resource.data.userId;
    }

    // قواعد المحادثات
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null
        && request.auth.uid in resource.data.participantIds;
    }

    // قواعد الرسائل
    match /messages/{messageId} {
      allow read: if request.auth != null
        && isParticipantInConversation(resource.data.conversationId);
      allow create: if request.auth != null
        && isParticipantInConversation(request.resource.data.conversationId)
        && isValidMessage();
    }

    // قواعد العقارات المحسنة
    match /estates/{estateId} {
      // القراءة: متاحة للجميع المسجلين للعقارات المدفوعة فقط
      allow read: if request.auth != null &&
        (resource.data.isPaymentVerified == true ||
         resource.data.userId == request.auth.uid ||
         isAdmin());

      // الإنشاء: للمستخدمين المصرح لهم فقط
      allow create: if request.auth != null &&
        isValidUser() &&
        canCreateEstate() &&
        isValidEstateData();

      // التحديث: فقط لصاحب العقار أو المشرفين
      allow update: if request.auth != null &&
        (resource.data.userId == request.auth.uid || isAdmin()) &&
        isValidEstateUpdate();

      // الحذف: فقط لصاحب العقار أو المشرفين
      allow delete: if request.auth != null &&
        (resource.data.userId == request.auth.uid || isAdmin());
    }

    // قواعد المشاريع
    match /projects/{projectId} {
      allow read: if request.auth != null &&
        (request.auth.uid in resource.data.memberIds ||
         resource.data.managerId == request.auth.uid ||
         isAdmin());

      allow create: if request.auth != null &&
        isValidUser() &&
        isValidProjectData();

      allow update: if request.auth != null &&
        (resource.data.managerId == request.auth.uid || isAdmin()) &&
        isValidProjectUpdate();

      allow delete: if request.auth != null &&
        (resource.data.managerId == request.auth.uid || isAdmin());

      // قواعد المهام الفرعية
      match /tasks/{taskId} {
        allow read: if request.auth != null &&
          (request.auth.uid in get(/databases/$(database)/documents/projects/$(projectId)).data.memberIds ||
           isAdmin());

        allow write: if request.auth != null &&
          (request.auth.uid in get(/databases/$(database)/documents/projects/$(projectId)).data.memberIds ||
           isAdmin()) &&
          isValidTaskData();
      }

      // قواعد الوثائق الفرعية
      match /documents/{documentId} {
        allow read: if request.auth != null &&
          (request.auth.uid in get(/databases/$(database)/documents/projects/$(projectId)).data.memberIds ||
           isAdmin());

        allow write: if request.auth != null &&
          (request.auth.uid in get(/databases/$(database)/documents/projects/$(projectId)).data.memberIds ||
           isAdmin());
      }

      // قواعد المعالم الفرعية
      match /milestones/{milestoneId} {
        allow read: if request.auth != null &&
          (request.auth.uid in get(/databases/$(database)/documents/projects/$(projectId)).data.memberIds ||
           isAdmin());

        allow write: if request.auth != null &&
          (request.auth.uid in get(/databases/$(database)/documents/projects/$(projectId)).data.memberIds ||
           isAdmin());
      }
    }

    // قواعد العملاء
    match /clients/{clientId} {
      allow read: if request.auth != null &&
        (resource.data.ownerId == request.auth.uid ||
         isAdmin() ||
         isCompanyManager());

      allow create: if request.auth != null &&
        isValidUser() &&
        isValidClientData();

      allow update: if request.auth != null &&
        (resource.data.ownerId == request.auth.uid ||
         isAdmin()) &&
        isValidClientData();

      allow delete: if request.auth != null &&
        (resource.data.ownerId == request.auth.uid || isAdmin());

      // قواعد التفاعلات الفرعية
      match /interactions/{interactionId} {
        allow read: if request.auth != null &&
          (get(/databases/$(database)/documents/clients/$(clientId)).data.ownerId == request.auth.uid ||
           isAdmin());

        allow write: if request.auth != null &&
          (get(/databases/$(database)/documents/clients/$(clientId)).data.ownerId == request.auth.uid ||
           isAdmin());
      }

      // قواعد المواعيد الفرعية
      match /appointments/{appointmentId} {
        allow read: if request.auth != null &&
          (get(/databases/$(database)/documents/clients/$(clientId)).data.ownerId == request.auth.uid ||
           isAdmin());

        allow write: if request.auth != null &&
          (get(/databases/$(database)/documents/clients/$(clientId)).data.ownerId == request.auth.uid ||
           isAdmin());
      }
    }

    // قواعد التفاعلات مع العملاء
    match /client_interactions/{interactionId} {
      allow read: if request.auth != null &&
        (resource.data.agentId == request.auth.uid ||
         isAdmin() ||
         isCompanyManager());

      allow create: if request.auth != null &&
        isValidUser();

      allow update: if request.auth != null &&
        (resource.data.agentId == request.auth.uid || isAdmin());

      allow delete: if request.auth != null &&
        (resource.data.agentId == request.auth.uid || isAdmin());
    }

    // قواعد المواعيد
    match /appointments/{appointmentId} {
      allow read: if request.auth != null &&
        (resource.data.agentId == request.auth.uid ||
         resource.data.clientId == request.auth.uid ||
         isAdmin());

      allow create: if request.auth != null &&
        isValidUser();

      allow update: if request.auth != null &&
        (resource.data.agentId == request.auth.uid || isAdmin());

      allow delete: if request.auth != null &&
        (resource.data.agentId == request.auth.uid || isAdmin());
    }

    // قواعد التقارير والتحليلات
    match /analytics/{document} {
      allow read: if request.auth != null &&
        (isAdmin() || isCompanyManager());

      allow write: if request.auth != null && isAdmin();
    }

    // قواعد النسخ الاحتياطية
    match /backups/{backupId} {
      allow read, write: if request.auth != null && isAdmin();
    }

    // قواعد سجلات الأمان
    match /security_logs/{logId} {
      allow read: if request.auth != null && isAdmin();
      allow write: if request.auth != null && isSystemService();
    }

    // الدوال المساعدة المحسنة
    function isValidUser() {
      return request.auth.uid != null
        && exists(/databases/$(database)/documents/users/$(request.auth.uid))
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isPaymentVerified == true;
    }

    function isPublicProfileRead() {
      // السماح بقراءة البيانات العامة فقط (الاسم، الصورة، التقييم)
      return request.auth != null;
    }

    function isValidUserData() {
      let data = request.resource.data;
      return data.keys().hasAll(['name', 'email', 'type']) &&
        data.name is string && data.name.size() > 0 && data.name.size() <= 100 &&
        data.email is string && data.email.matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/) &&
        data.type is number && data.type >= 0 && data.type <= 4;
    }

    function canCreateEstate() {
      let userData = get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
      return userData.type in [2, 3, 4] && // investor, owner, company
        userData.isVerified == true;
    }

    function isValidEstateData() {
      let data = request.resource.data;
      return data.keys().hasAll(['title', 'description', 'price', 'area', 'propertyType', 'userId']) &&
        data.title is string && data.title.size() > 0 && data.title.size() <= 200 &&
        data.description is string && data.description.size() > 0 && data.description.size() <= 2000 &&
        data.price is number && data.price > 0 && data.price <= 10000000 &&
        data.area is string && data.area.size() > 0 &&
        data.propertyType is string && data.propertyType.size() > 0 &&
        data.userId == request.auth.uid &&
        data.createdAt == request.time;
    }

    function isValidEstateUpdate() {
      let data = request.resource.data;
      let allowedFields = ['title', 'description', 'price', 'area', 'propertyType', 'rooms', 'bathrooms', 'features', 'isActive', 'updatedAt'];
      return data.diff(resource.data).affectedKeys().hasOnly(allowedFields) &&
        data.updatedAt == request.time &&
        data.userId == resource.data.userId; // لا يمكن تغيير المالك
    }

    function isValidProjectData() {
      let data = request.resource.data;
      return data.keys().hasAll(['name', 'description', 'managerId']) &&
        data.name is string && data.name.size() > 0 && data.name.size() <= 200 &&
        data.description is string && data.description.size() > 0 &&
        data.managerId == request.auth.uid;
    }

    function isValidProjectUpdate() {
      let data = request.resource.data;
      let allowedFields = ['name', 'description', 'status', 'memberIds', 'updatedAt'];
      return data.diff(resource.data).affectedKeys().hasOnly(allowedFields) &&
        data.updatedAt == request.time;
    }

    function isValidTaskData() {
      let data = request.resource.data;
      return data.keys().hasAll(['title', 'description', 'projectId', 'createdBy']) &&
        data.title is string && data.title.size() > 0 && data.title.size() <= 200 &&
        data.description is string && data.description.size() > 0 &&
        data.createdBy == request.auth.uid;
    }

    function isCompanyManager() {
      let userData = get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
      return userData.type == 4; // نوع شركة عقارية
    }

    function isSystemService() {
      // للخدمات النظام فقط - يجب تنفيذها عبر Cloud Functions
      return request.auth.token.firebase.sign_in_provider == 'custom' &&
        request.auth.token.admin == true;
    }

    function isSeekerUser() {
      let userData = get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
      return userData.type == 0 || userData.type == 1; // seeker types
    }

    function canCreateOffer() {
      let userData = get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
      return userData.type == 2 || userData.type == 3 || userData.type == 4; // investor, owner, company
    }

    function isAdmin() {
      let userData = get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
      return userData.role == 'admin' || userData.role == 'moderator';
    }

    function isValidPropertyRequest() {
      let data = request.resource.data;
      return data.keys().hasAll(['title', 'description', 'propertyType', 'userId'])
        && data.title is string && data.title.size() > 0 && data.title.size() <= 100
        && data.description is string && data.description.size() > 0 && data.description.size() <= 1000
        && data.propertyType is string && data.propertyType.size() > 0
        && data.userId == request.auth.uid
        && data.status == 0 // RequestStatus.open
        && data.createdAt == request.time
        && data.updatedAt == request.time;
    }

    function isValidPropertyRequestUpdate() {
      let data = request.resource.data;
      return data.diff(resource.data).affectedKeys().hasOnly(['title', 'description', 'propertyType', 'minPrice', 'maxPrice', 'preferredLocations', 'minRooms', 'minBathrooms', 'minArea', 'hasCentralAC', 'hasMaidRoom', 'hasGarage', 'hasSwimmingPool', 'hasElevator', 'isFullyFurnished', 'additionalRequirements', 'neededBy', 'status', 'updatedAt'])
        && data.updatedAt == request.time
        && data.userId == resource.data.userId; // لا يمكن تغيير المالك
    }

    function isValidPropertyOffer() {
      let data = request.resource.data;
      return data.keys().hasAll(['requestId', 'userId', 'title', 'description', 'price'])
        && data.title is string && data.title.size() > 0 && data.title.size() <= 100
        && data.description is string && data.description.size() > 0 && data.description.size() <= 1000
        && data.price is number && data.price > 0
        && data.userId == request.auth.uid
        && data.status == 0 // OfferStatus.pending
        && data.createdAt == request.time
        && data.updatedAt == request.time;
    }

    function isValidPropertyOfferUpdate() {
      let data = request.resource.data;
      return data.diff(resource.data).affectedKeys().hasOnly(['title', 'description', 'price', 'address', 'rooms', 'bathrooms', 'area', 'hasCentralAC', 'hasMaidRoom', 'hasGarage', 'hasSwimmingPool', 'hasElevator', 'isFullyFurnished', 'features', 'status', 'updatedAt'])
        && data.updatedAt == request.time
        && data.userId == resource.data.userId;
    }

    function isValidComment() {
      let data = request.resource.data;
      return data.keys().hasAll(['requestId', 'userId', 'content'])
        && data.content is string && data.content.size() > 0 && data.content.size() <= 500
        && data.userId == request.auth.uid
        && data.createdAt == request.time;
    }

    function isValidMessage() {
      let data = request.resource.data;
      return data.keys().hasAll(['conversationId', 'senderId', 'content', 'type'])
        && data.senderId == request.auth.uid
        && data.content is string && data.content.size() > 0 && data.content.size() <= 1000
        && data.timestamp == request.time;
    }

    function getRequestOwner(requestId) {
      return get(/databases/$(database)/documents/property_requests/$(requestId)).data.userId;
    }

    function isParticipantInConversation(conversationId) {
      let conversation = get(/databases/$(database)/documents/conversations/$(conversationId)).data;
      return request.auth.uid in conversation.participantIds;
    }
  }
}
