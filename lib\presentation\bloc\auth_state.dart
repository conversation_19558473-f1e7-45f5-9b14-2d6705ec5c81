import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Base state class for authentication.
/// Extends [Equatable] to facilitate value comparison between states.
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// The initial state when the authentication process has not started.
class AuthInitial extends AuthState {}

/// State representing a loading state during an authentication operation.
class AuthLoading extends AuthState {}

/// State representing a successful authentication.
/// Contains the authenticated [User], their [userType], and an optional [welcomeMessage].
class Authenticated extends AuthState {
  final User user;
  final String userType;
  final String? welcomeMessage;

  const Authenticated(this.user, this.userType, {this.welcomeMessage});

  @override
  List<Object?> get props => [user, userType, welcomeMessage];
}

/// State representing that the user is not authenticated.
class Unauthenticated extends AuthState {}

/// State representing an authentication error.
/// Contains an error [message] detailing the issue.
class AuthError extends AuthState {
  final String message;

  const AuthError(this.message);

  @override
  List<Object?> get props => [message];
}

/// State representing a successful signup operation.
/// Contains a success [message] to be displayed.
class SignupSuccess extends AuthState {
  final String message;

  const SignupSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// State representing that a password reset email has been successfully sent.
class PasswordResetEmailSent extends AuthState {}

/// State representing a failure in sending the password reset email.
/// Contains an error [message] detailing the issue.
class PasswordResetFailed extends AuthState {
  final String message;

  const PasswordResetFailed(this.message);

  @override
  List<Object?> get props => [message];
}

/// State representing that a verification email has been successfully sent.
class VerificationEmailSent extends AuthState {}

/// State representing a failure in sending the verification email.
/// Contains an error [message] detailing the issue.
class VerificationEmailFailed extends AuthState {
  final String message;

  const VerificationEmailFailed(this.message);

  @override
  List<Object?> get props => [message];
}
