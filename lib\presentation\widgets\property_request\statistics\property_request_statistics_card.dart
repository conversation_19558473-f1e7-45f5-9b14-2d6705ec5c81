// lib/presentation/widgets/property_request/statistics/property_request_statistics_card.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// بطاقة إحصائيات طلبات العقارات
class PropertyRequestStatisticsCard extends StatelessWidget {
  /// عنوان البطاقة
  final String title;

  /// قيمة الإحصائية
  final String value;

  /// أيقونة البطاقة
  final IconData icon;

  /// لون البطاقة
  final Color color;

  /// إنشاء بطاقة إحصائيات طلبات العقارات
  const PropertyRequestStatisticsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis)),
              ]),
            const SizedBox(height: 12),
            Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color)),
          ])));
  }
}
