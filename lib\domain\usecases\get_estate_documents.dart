import '../entities/estate_document.dart';
import '../repositories/document_repository.dart';

/// حالة استخدام للحصول على مستندات العقار
class GetEstateDocuments {
  final DocumentRepository repository;

  GetEstateDocuments(this.repository);

  /// الحصول على مستندات العقار
  /// [estateId] هو معرف العقار
  /// [publicOnly] إذا كان true، يتم إرجاع المستندات العامة فقط
  /// يعيد قائمة بمستندات العقار
  Future<List<EstateDocument>> call(String estateId, {bool publicOnly = false}) async {
    if (estateId.isEmpty) {
      throw Exception('معرف العقار مطلوب');
    }
    
    return await repository.getEstateDocuments(estateId, publicOnly: publicOnly);
  }
}
