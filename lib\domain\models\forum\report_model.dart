import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نموذج التقرير
class ReportModel extends Equatable {
  /// معرف التقرير
  final String id;

  /// معرف المستخدم الذي قدم التقرير
  final String reporterId;

  /// نوع المحتوى المبلغ عنه (موضوع، مشاركة، مستخدم)
  final ReportContentType contentType;

  /// معرف المحتوى المبلغ عنه
  final String contentId;

  /// سبب التقرير
  final String reason;

  /// وصف إضافي للتقرير
  final String? description;

  /// حالة التقرير
  final ReportStatus status;

  /// معرف المشرف الذي عالج التقرير
  final String? handledBy;

  /// تاريخ معالجة التقرير
  final DateTime? handledAt;

  /// ملاحظات المشرف
  final String? adminNotes;

  /// تاريخ إنشاء التقرير
  final DateTime createdAt;

  /// Constructor
  const ReportModel({
    required this.id,
    required this.reporterId,
    required this.contentType,
    required this.contentId,
    required this.reason,
    this.description,
    required this.status,
    this.handledBy,
    this.handledAt,
    this.adminNotes,
    required this.createdAt,
  });

  /// Factory constructor from Map
  factory ReportModel.fromMap(Map<String, dynamic> map) {
    return ReportModel(
      id: map['id'] ?? '',
      reporterId: map['reporterId'] ?? '',
      contentType: _parseContentType(map['contentType']),
      contentId: map['contentId'] ?? '',
      reason: map['reason'] ?? '',
      description: map['description'],
      status: _parseStatus(map['status']),
      handledBy: map['handledBy'],
      handledAt: (map['handledAt'] as Timestamp?)?.toDate(),
      adminNotes: map['adminNotes'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now());
  }

  /// Factory constructor from Firestore
  factory ReportModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return ReportModel.empty();
    }
    return ReportModel.fromMap({...data, 'id': doc.id});
  }

  /// Factory constructor for empty report
  factory ReportModel.empty() {
    return ReportModel(
      id: '',
      reporterId: '',
      contentType: ReportContentType.topic,
      contentId: '',
      reason: '',
      status: ReportStatus.pending,
      createdAt: DateTime.now());
  }

  /// Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'reporterId': reporterId,
      'contentType': contentType.toString().split('.').last,
      'contentId': contentId,
      'reason': reason,
      'description': description,
      'status': status.toString().split('.').last,
      'handledBy': handledBy,
      'handledAt': handledAt != null ? Timestamp.fromDate(handledAt!) : null,
      'adminNotes': adminNotes,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  /// Copy with
  ReportModel copyWith({
    String? id,
    String? reporterId,
    ReportContentType? contentType,
    String? contentId,
    String? reason,
    String? description,
    ReportStatus? status,
    String? handledBy,
    DateTime? handledAt,
    String? adminNotes,
    DateTime? createdAt,
  }) {
    return ReportModel(
      id: id ?? this.id,
      reporterId: reporterId ?? this.reporterId,
      contentType: contentType ?? this.contentType,
      contentId: contentId ?? this.contentId,
      reason: reason ?? this.reason,
      description: description ?? this.description,
      status: status ?? this.status,
      handledBy: handledBy ?? this.handledBy,
      handledAt: handledAt ?? this.handledAt,
      adminNotes: adminNotes ?? this.adminNotes,
      createdAt: createdAt ?? this.createdAt);
  }

  /// تحليل نوع المحتوى
  static ReportContentType _parseContentType(dynamic value) {
    if (value == null) {
      return ReportContentType.topic;
    }

    if (value is ReportContentType) {
      return value;
    }

    final typeStr = value.toString().toLowerCase();

    if (typeStr.contains('topic')) {
      return ReportContentType.topic;
    } else if (typeStr.contains('post')) {
      return ReportContentType.post;
    } else if (typeStr.contains('user')) {
      return ReportContentType.user;
    } else {
      return ReportContentType.topic;
    }
  }

  /// تحليل حالة التقرير
  static ReportStatus _parseStatus(dynamic value) {
    if (value == null) {
      return ReportStatus.pending;
    }

    if (value is ReportStatus) {
      return value;
    }

    final statusStr = value.toString().toLowerCase();

    if (statusStr.contains('pending')) {
      return ReportStatus.pending;
    } else if (statusStr.contains('reviewing')) {
      return ReportStatus.reviewing;
    } else if (statusStr.contains('resolved')) {
      return ReportStatus.resolved;
    } else if (statusStr.contains('rejected')) {
      return ReportStatus.rejected;
    } else if (statusStr.contains('deleted')) {
      return ReportStatus.deleted;
    } else {
      return ReportStatus.pending;
    }
  }

  /// الحصول على اسم نوع المحتوى
  String getContentTypeName() {
    switch (contentType) {
      case ReportContentType.topic:
        return 'موضوع';
      case ReportContentType.post:
        return 'مشاركة';
      case ReportContentType.user:
        return 'مستخدم';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على اسم حالة التقرير
  String getStatusName() {
    switch (status) {
      case ReportStatus.pending:
        return 'قيد الانتظار';
      case ReportStatus.reviewing:
        return 'قيد المراجعة';
      case ReportStatus.resolved:
        return 'تم الحل';
      case ReportStatus.rejected:
        return 'مرفوض';
      case ReportStatus.deleted:
        return 'تم الحذف';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على لون حالة التقرير
  int getStatusColor() {
    switch (status) {
      case ReportStatus.pending:
        return 0xFFFF9800; // برتقالي
      case ReportStatus.reviewing:
        return 0xFF4CAF50; // أخضر (تغيير من الأزرق)
      case ReportStatus.resolved:
        return 0xFF4CAF50; // أخضر
      case ReportStatus.rejected:
        return 0xFFF44336; // أحمر
      case ReportStatus.deleted:
        return 0xFF9C27B0; // بنفسجي
      default:
        return 0xFF607D8B; // رمادي
    }
  }

  @override
  List<Object?> get props => [
        id,
        reporterId,
        contentType,
        contentId,
        reason,
        description,
        status,
        handledBy,
        handledAt,
        adminNotes,
        createdAt,
      ];
}

/// نوع المحتوى المبلغ عنه
enum ReportContentType {
  /// موضوع
  topic,

  /// مشاركة
  post,

  /// مستخدم
  user,
}

/// حالة التقرير
enum ReportStatus {
  /// قيد الانتظار
  pending,

  /// قيد المراجعة
  reviewing,

  /// تم الحل
  resolved,

  /// مرفوض
  rejected,

  /// تم الحذف
  deleted,
}
