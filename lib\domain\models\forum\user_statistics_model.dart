import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نموذج إحصائيات مستخدم المنتدى
class UserStatisticsModel extends Equatable {
  /// معرف المستخدم
  final String userId;

  /// اسم المستخدم
  final String userName;

  /// صورة المستخدم
  final String? userImage;

  /// تاريخ الانضمام
  final DateTime joinDate;

  /// تاريخ آخر نشاط
  final DateTime lastActivityDate;

  /// عدد المواضيع
  final int topicsCount;

  /// عدد المشاركات
  final int postsCount;

  /// إجمالي مشاهدات المواضيع
  final int totalTopicViews;

  /// عدد مشاهدات المستخدم للمواضيع
  final int viewsCount;

  /// إجمالي إعجابات المواضيع
  final int totalTopicLikes;

  /// إجمالي إعجابات المشاركات
  final int totalPostLikes;

  /// إجمالي الإعجابات التي قام بها
  final int totalLikesGiven;

  /// عدد المواضيع المميزة
  final int featuredTopicsCount;

  /// عدد المواضيع المثبتة
  final int pinnedTopicsCount;

  /// عدد المواضيع المحلولة
  final int solvedTopicsCount;

  /// عدد أفضل الإجابات
  final int bestAnswersCount;

  /// عدد مشاركات المواضيع
  final int topicSharesCount;

  /// عدد الإشارات المرجعية
  final int topicBookmarksCount;

  /// متوسط المشاركات لكل موضوع
  final double averagePostsPerTopic;

  /// متوسط المشاهدات لكل موضوع
  final double averageViewsPerTopic;

  /// متوسط الإعجابات لكل موضوع
  final double averageLikesPerTopic;

  /// معدل النشاط اليومي
  final double dailyActivityRate;

  /// معدل النشاط الأسبوعي
  final double weeklyActivityRate;

  /// معدل النشاط الشهري
  final double monthlyActivityRate;

  /// النقاط
  final int points;

  /// المستوى
  final String level;

  /// الشارات
  final List<String> badges;

  /// الإنجازات
  final List<Map<String, dynamic>>? achievements;

  /// تاريخ آخر تحديث للإحصائيات
  final DateTime updatedAt;

  UserStatisticsModel({
    required this.userId,
    required this.userName,
    this.userImage,
    required this.joinDate,
    required this.lastActivityDate,
    this.topicsCount = 0,
    this.postsCount = 0,
    this.totalTopicViews = 0,
    this.viewsCount = 0,
    this.totalTopicLikes = 0,
    this.totalPostLikes = 0,
    this.totalLikesGiven = 0,
    this.featuredTopicsCount = 0,
    this.pinnedTopicsCount = 0,
    this.solvedTopicsCount = 0,
    this.bestAnswersCount = 0,
    this.topicSharesCount = 0,
    this.topicBookmarksCount = 0,
    this.averagePostsPerTopic = 0.0,
    this.averageViewsPerTopic = 0.0,
    this.averageLikesPerTopic = 0.0,
    this.dailyActivityRate = 0.0,
    this.weeklyActivityRate = 0.0,
    this.monthlyActivityRate = 0.0,
    this.points = 0,
    required this.level,
    this.badges = const [],
    this.achievements,
    DateTime? updatedAt,
  }) : updatedAt = updatedAt ?? DateTime.now();

  /// إنشاء نسخة معدلة من إحصائيات المستخدم
  UserStatisticsModel copyWith({
    String? userId,
    String? userName,
    String? userImage,
    DateTime? joinDate,
    DateTime? lastActivityDate,
    int? topicsCount,
    int? postsCount,
    int? totalTopicViews,
    int? viewsCount,
    int? totalTopicLikes,
    int? totalPostLikes,
    int? totalLikesGiven,
    int? featuredTopicsCount,
    int? pinnedTopicsCount,
    int? solvedTopicsCount,
    int? bestAnswersCount,
    int? topicSharesCount,
    int? topicBookmarksCount,
    double? averagePostsPerTopic,
    double? averageViewsPerTopic,
    double? averageLikesPerTopic,
    double? dailyActivityRate,
    double? weeklyActivityRate,
    double? monthlyActivityRate,
    int? points,
    String? level,
    List<String>? badges,
    List<Map<String, dynamic>>? achievements,
    DateTime? updatedAt,
  }) {
    return UserStatisticsModel(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      joinDate: joinDate ?? this.joinDate,
      lastActivityDate: lastActivityDate ?? this.lastActivityDate,
      topicsCount: topicsCount ?? this.topicsCount,
      postsCount: postsCount ?? this.postsCount,
      totalTopicViews: totalTopicViews ?? this.totalTopicViews,
      viewsCount: viewsCount ?? this.viewsCount,
      totalTopicLikes: totalTopicLikes ?? this.totalTopicLikes,
      totalPostLikes: totalPostLikes ?? this.totalPostLikes,
      totalLikesGiven: totalLikesGiven ?? this.totalLikesGiven,
      featuredTopicsCount: featuredTopicsCount ?? this.featuredTopicsCount,
      pinnedTopicsCount: pinnedTopicsCount ?? this.pinnedTopicsCount,
      solvedTopicsCount: solvedTopicsCount ?? this.solvedTopicsCount,
      bestAnswersCount: bestAnswersCount ?? this.bestAnswersCount,
      topicSharesCount: topicSharesCount ?? this.topicSharesCount,
      topicBookmarksCount: topicBookmarksCount ?? this.topicBookmarksCount,
      averagePostsPerTopic: averagePostsPerTopic ?? this.averagePostsPerTopic,
      averageViewsPerTopic: averageViewsPerTopic ?? this.averageViewsPerTopic,
      averageLikesPerTopic: averageLikesPerTopic ?? this.averageLikesPerTopic,
      dailyActivityRate: dailyActivityRate ?? this.dailyActivityRate,
      weeklyActivityRate: weeklyActivityRate ?? this.weeklyActivityRate,
      monthlyActivityRate: monthlyActivityRate ?? this.monthlyActivityRate,
      points: points ?? this.points,
      level: level ?? this.level,
      badges: badges ?? this.badges,
      achievements: achievements ?? this.achievements,
      updatedAt: updatedAt ?? DateTime.now());
  }

  /// تحويل إحصائيات المستخدم إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'joinDate': Timestamp.fromDate(joinDate),
      'lastActivityDate': Timestamp.fromDate(lastActivityDate),
      'topicsCount': topicsCount,
      'postsCount': postsCount,
      'totalTopicViews': totalTopicViews,
      'viewsCount': viewsCount,
      'totalTopicLikes': totalTopicLikes,
      'totalPostLikes': totalPostLikes,
      'totalLikesGiven': totalLikesGiven,
      'featuredTopicsCount': featuredTopicsCount,
      'pinnedTopicsCount': pinnedTopicsCount,
      'solvedTopicsCount': solvedTopicsCount,
      'bestAnswersCount': bestAnswersCount,
      'topicSharesCount': topicSharesCount,
      'topicBookmarksCount': topicBookmarksCount,
      'averagePostsPerTopic': averagePostsPerTopic,
      'averageViewsPerTopic': averageViewsPerTopic,
      'averageLikesPerTopic': averageLikesPerTopic,
      'dailyActivityRate': dailyActivityRate,
      'weeklyActivityRate': weeklyActivityRate,
      'monthlyActivityRate': monthlyActivityRate,
      'points': points,
      'level': level,
      'badges': badges,
      'achievements': achievements,
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// إنشاء إحصائيات مستخدم من خريطة
  factory UserStatisticsModel.fromMap(Map<String, dynamic> map) {
    return UserStatisticsModel(
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'],
      joinDate: map['joinDate'] is Timestamp
          ? (map['joinDate'] as Timestamp).toDate()
          : DateTime.now(),
      lastActivityDate: map['lastActivityDate'] is Timestamp
          ? (map['lastActivityDate'] as Timestamp).toDate()
          : DateTime.now(),
      topicsCount: map['topicsCount'] ?? 0,
      postsCount: map['postsCount'] ?? 0,
      totalTopicViews: map['totalTopicViews'] ?? 0,
      viewsCount: map['viewsCount'] ?? 0,
      totalTopicLikes: map['totalTopicLikes'] ?? 0,
      totalPostLikes: map['totalPostLikes'] ?? 0,
      totalLikesGiven: map['totalLikesGiven'] ?? 0,
      featuredTopicsCount: map['featuredTopicsCount'] ?? 0,
      pinnedTopicsCount: map['pinnedTopicsCount'] ?? 0,
      solvedTopicsCount: map['solvedTopicsCount'] ?? 0,
      bestAnswersCount: map['bestAnswersCount'] ?? 0,
      topicSharesCount: map['topicSharesCount'] ?? 0,
      topicBookmarksCount: map['topicBookmarksCount'] ?? 0,
      averagePostsPerTopic: (map['averagePostsPerTopic'] ?? 0.0).toDouble(),
      averageViewsPerTopic: (map['averageViewsPerTopic'] ?? 0.0).toDouble(),
      averageLikesPerTopic: (map['averageLikesPerTopic'] ?? 0.0).toDouble(),
      dailyActivityRate: (map['dailyActivityRate'] ?? 0.0).toDouble(),
      weeklyActivityRate: (map['weeklyActivityRate'] ?? 0.0).toDouble(),
      monthlyActivityRate: (map['monthlyActivityRate'] ?? 0.0).toDouble(),
      points: map['points'] ?? 0,
      level: map['level'] ?? 'مبتدئ',
      badges: map['badges'] != null ? List<String>.from(map['badges']) : [],
      achievements: map['achievements'] != null
          ? List<Map<String, dynamic>>.from(
              map['achievements'].map((x) => Map<String, dynamic>.from(x)))
          : null,
      updatedAt: map['updatedAt'] is Timestamp
          ? (map['updatedAt'] as Timestamp).toDate()
          : DateTime.now());
  }

  /// إنشاء إحصائيات مستخدم من وثيقة فايربيز
  factory UserStatisticsModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return UserStatisticsModel(
        userId: doc.id,
        userName: '',
        joinDate: DateTime.now(),
        lastActivityDate: DateTime.now(),
        level: 'مبتدئ');
    }
    return UserStatisticsModel.fromMap({...data, 'userId': doc.id});
  }

  @override
  List<Object?> get props => [
        userId,
        userName,
        userImage,
        joinDate,
        lastActivityDate,
        topicsCount,
        postsCount,
        totalTopicViews,
        viewsCount,
        totalTopicLikes,
        totalPostLikes,
        totalLikesGiven,
        featuredTopicsCount,
        pinnedTopicsCount,
        solvedTopicsCount,
        bestAnswersCount,
        topicSharesCount,
        topicBookmarksCount,
        averagePostsPerTopic,
        averageViewsPerTopic,
        averageLikesPerTopic,
        dailyActivityRate,
        weeklyActivityRate,
        monthlyActivityRate,
        points,
        level,
        badges,
        achievements,
        updatedAt,
      ];
}
