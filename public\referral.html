<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Krea - دعوة للانضمام</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      :root {
        --primary: #2e7d32;
        --primary-light: #4caf50;
        --primary-dark: #1b5e20;
        --secondary: #8d6e63;
        --secondary-light: #bcaaa4;
        --secondary-dark: #5d4037;
        --background: #fafafa;
        --card-background: #ffffff;
        --text-primary: #212121;
        --text-secondary: #757575;
        --success: #4caf50;
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: "Cairo", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        min-height: 100vh;
        background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
        color: var(--text-primary);
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .page-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .container {
        width: 100%;
        max-width: 500px;
        background-color: var(--card-background);
        border-radius: 16px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        position: relative;
        overflow: hidden;
        transition: transform 0.3s ease;
      }

      .container:hover {
        transform: translateY(-5px);
      }

      .container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(
          to right,
          var(--primary-light),
          var(--primary),
          var(--primary-dark)
        );
      }

      .logo-container {
        width: 120px;
        height: 120px;
        margin: 0 auto 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }

      .logo {
        width: 100px;
        height: 100px;
        object-fit: contain;
        border-radius: 50%;
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
        background-color: white;
        padding: 10px;
        transition: transform 0.3s ease;
      }

      .logo:hover {
        transform: scale(1.05);
      }

      h1 {
        color: var(--primary);
        margin-bottom: 15px;
        font-size: 28px;
        font-weight: 700;
      }

      p {
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 20px;
        color: var(--text-secondary);
      }

      .btn {
        display: inline-block;
        background: linear-gradient(
          to right,
          var(--primary),
          var(--primary-dark)
        );
        color: white;
        padding: 14px 24px;
        text-decoration: none;
        border-radius: 30px;
        font-weight: 600;
        margin: 15px 0;
        text-align: center;
        cursor: pointer;
        border: none;
        box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
        transition: all 0.3s ease;
        font-family: "Cairo", sans-serif;
        font-size: 16px;
        min-width: 180px;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(46, 125, 50, 0.3);
      }

      .btn:active {
        transform: translateY(1px);
      }

      .button-group {
        display: flex;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
      }

      .btn-share {
        background: linear-gradient(
          to right,
          var(--secondary),
          var(--secondary-dark)
        );
        box-shadow: 0 4px 10px rgba(141, 110, 99, 0.2);
      }

      .btn-share:hover {
        box-shadow: 0 6px 15px rgba(141, 110, 99, 0.3);
      }

      .store-badges {
        display: flex;
        justify-content: center;
        margin-top: 25px;
        flex-wrap: wrap;
        gap: 15px;
      }

      .store-badge {
        transition: transform 0.3s ease;
        display: block;
      }

      .store-badge:hover {
        transform: translateY(-3px);
      }

      .code-container {
        margin: 25px 0;
      }

      .code-box {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 12px;
        font-size: 22px;
        font-weight: bold;
        margin: 15px auto;
        max-width: 80%;
        border: 1px dashed var(--primary-light);
        color: var(--primary-dark);
        letter-spacing: 1px;
        position: relative;
        overflow: hidden;
      }

      .code-box::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(255, 255, 255, 0.8),
          transparent
        );
        transform: translateX(-100%);
      }

      .code-box:hover::before {
        animation: shine 1.5s infinite;
      }

      @keyframes shine {
        100% {
          transform: translateX(100%);
        }
      }

      .success-message {
        display: none;
        background-color: rgba(76, 175, 80, 0.1);
        color: var(--success);
        padding: 12px;
        border-radius: 8px;
        margin-top: 10px;
        font-weight: 600;
        border-right: 4px solid var(--success);
        animation: fadeIn 0.3s ease;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .footer-note {
        margin-top: 30px;
        font-size: 14px;
        color: var(--text-secondary);
        padding: 10px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
      }

      @media (max-width: 480px) {
        .container {
          padding: 20px;
        }

        .logo {
          width: 80px;
          height: 80px;
        }

        h1 {
          font-size: 24px;
        }

        .code-box {
          font-size: 18px;
          max-width: 100%;
        }
      }
    </style>
  </head>
  <body>
    <div class="page-wrapper">
      <div class="container">
        <div class="logo-container">
          <img
            src="https://firebasestorage.googleapis.com/v0/b/real-estate-998a9.appspot.com/o/app_assets%2Flogo.png?alt=media"
            alt="Krea Logo"
            class="logo"
            onerror="this.src='https://via.placeholder.com/100x100?text=KREA'"
          />
        </div>
        <h1>تمت دعوتك للانضمام إلى Krea!</h1>
        <p>
          تطبيق Krea هو الوجهة الأولى للعقارات في الكويت. انضم الآن واستفد من
          مزايا حصرية!
        </p>

        <div id="referral-info" class="code-container">
          <p>استخدم رمز الإحالة التالي عند التسجيل:</p>
          <div class="code-box" id="referral-code">LOADING...</div>
          <div class="button-group">
            <button onclick="copyCode()" class="btn">نسخ الرمز</button>
            <button onclick="shareReferral()" class="btn btn-share">
              مشاركة الرابط
            </button>
          </div>
          <div id="copy-success" class="success-message">
            تم نسخ الرمز بنجاح!
          </div>
          <div id="share-success" class="success-message">
            تم نسخ رابط الدعوة للمشاركة!
          </div>
        </div>

        <p>لتنزيل التطبيق، اختر متجر التطبيقات المناسب:</p>

        <div class="store-badges">
          <a href="#" id="play-store-link" class="store-badge">
            <img
              src="https://play.google.com/intl/en_us/badges/static/images/badges/ar_badge_web_generic.png"
              alt="Google Play"
              width="160"
            />
          </a>
          <a href="#" id="app-store-link" class="store-badge">
            <img
              src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg"
              alt="App Store"
              width="140"
            />
          </a>
        </div>

        <p class="footer-note">
          بعد تنزيل التطبيق، قم بالتسجيل واستخدم رمز الإحالة للحصول على مزايا
          حصرية!
        </p>
      </div>
    </div>

    <script>
      // استخراج رمز الإحالة من URL
      const urlParams = new URLSearchParams(window.location.search);
      const referralCode = urlParams.get("code") || "";
      const referralId = urlParams.get("id") || "";

      // عرض رمز الإحالة
      if (referralCode) {
        document.getElementById("referral-code").textContent = referralCode;
      } else {
        document.getElementById("referral-info").style.display = "none";
      }

      // تكوين روابط المتاجر مع معلمات التتبع
      const PLAY_STORE_BASE =
        "https://play.google.com/store/apps/details?id=com.krea.app";
      const APP_STORE_BASE = "https://apps.apple.com/app/krea/id123456789";

      const playStoreLink = `${PLAY_STORE_BASE}&referrer=utm_source%3Dreferral%26utm_medium%3Dapp%26utm_campaign%3Dreferral%26utm_content%3D${referralCode}%26referral_id%3D${referralId}`;
      const appStoreLink = `${APP_STORE_BASE}?pt=123456&ct=${referralCode}&mt=${referralId}`;

      document.getElementById("play-store-link").href = playStoreLink;
      document.getElementById("app-store-link").href = appStoreLink;

      // حفظ رمز الإحالة في localStorage
      if (referralCode) {
        try {
          localStorage.setItem("pending_referral_code", referralCode);
          if (referralId) {
            localStorage.setItem("pending_referral_id", referralId);
          }

          // محاولة فتح التطبيق مباشرة (للأجهزة التي لديها التطبيق مثبت)
          setTimeout(function () {
            try {
              window.location.href = `krea://referral?code=${referralCode}&id=${referralId}`;
            } catch (e) {
              // تجاهل الخطأ
            }
          }, 1000);
        } catch (e) {
          // تجاهل الخطأ
        }
      }

      // نسخ الرمز إلى الحافظة
      function copyCode() {
        const codeElement = document.getElementById("referral-code");
        const code = codeElement.textContent;
        const successMessage = document.getElementById("copy-success");

        navigator.clipboard
          .writeText(code)
          .then(() => {
            successMessage.style.display = "block";
            setTimeout(() => {
              successMessage.style.display = "none";
            }, 3000);
          })
          .catch((err) => {
            console.error("فشل في نسخ النص: ", err);

            // طريقة بديلة للنسخ
            const textArea = document.createElement("textarea");
            textArea.value = code;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand("copy");
            document.body.removeChild(textArea);

            successMessage.style.display = "block";
            setTimeout(() => {
              successMessage.style.display = "none";
            }, 3000);
          });
      }

      // مشاركة رابط الدعوة
      function shareReferral() {
        const referralUrl = window.location.href;
        const successMessage = document.getElementById("share-success");

        // تحضير نص المشاركة
        const shareTitle = "Krea - دعوة للانضمام";
        const shareText =
          "انضم إلى Krea واحصل على مزايا حصرية!\n\nاستخدم رمز الإحالة الخاص بي: " +
          referralCode;

        // إذا كان API مشاركة الويب متاحًا
        if (navigator.share) {
          navigator
            .share({
              title: shareTitle,
              text: shareText,
              url: referralUrl,
            })
            .then(() => {
              console.log("تمت المشاركة بنجاح");
            })
            .catch((error) => {
              console.error("خطأ في المشاركة:", error);
              copyReferralLink();
            });
        } else {
          // إذا لم يكن API المشاركة متاحًا، انسخ الرابط
          copyReferralLink();
        }

        // نسخ رابط الدعوة إلى الحافظة
        function copyReferralLink() {
          navigator.clipboard
            .writeText(referralUrl)
            .then(() => {
              successMessage.style.display = "block";
              setTimeout(() => {
                successMessage.style.display = "none";
              }, 3000);
            })
            .catch((err) => {
              console.error("فشل في نسخ الرابط: ", err);

              // طريقة بديلة للنسخ
              const textArea = document.createElement("textarea");
              textArea.value = referralUrl;
              document.body.appendChild(textArea);
              textArea.select();
              document.execCommand("copy");
              document.body.removeChild(textArea);

              successMessage.style.display = "block";
              setTimeout(() => {
                successMessage.style.display = "none";
              }, 3000);
            });
        }
      }

      // تسجيل بيانات الإحالة
      function logReferral() {
        // يمكن إضافة كود هنا لإرسال بيانات الإحالة إلى خادم
        console.log("Referral viewed", {
          referralCode,
          referralId,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
        });
      }

      // تسجيل مشاهدة الإحالة
      logReferral();
    </script>
  </body>
</html>
