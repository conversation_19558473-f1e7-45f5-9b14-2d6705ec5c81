// lib/presentation/pages/property_request/create_property_request_page.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/services/notification_service.dart';
import '../../../domain/models/notification_model.dart' as domain;
import '../../../domain/models/property_request/property_request_model.dart';
import '../../providers/property_request_provider.dart';
import 'package:provider/provider.dart';

import '../../widgets/enhanced_progress_indicator.dart';
import '../../widgets/property_request/property_type_selector.dart';
import '../../widgets/property_request/location_selector.dart';

/// صفحة إنشاء طلب عقار جديد
class CreatePropertyRequestPage extends StatefulWidget {
  const CreatePropertyRequestPage({super.key});

  @override
  State<CreatePropertyRequestPage> createState() => _CreatePropertyRequestPageState();
}

class _CreatePropertyRequestPageState extends State<CreatePropertyRequestPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _minPriceController = TextEditingController();
  final _maxPriceController = TextEditingController();
  final _minRoomsController = TextEditingController();
  final _minBathroomsController = TextEditingController();
  final _minAreaController = TextEditingController();
  final _additionalRequirementsController = TextEditingController();

  String _selectedPropertyType = '';
  List<String> _selectedLocations = [];
  DateTime? _neededByDate;
  bool _hasCentralAC = false;
  bool _hasMaidRoom = false;
  bool _hasGarage = false;
  bool _hasSwimmingPool = false;
  bool _hasElevator = false;
  bool _isFullyFurnished = false;

  final List<File> _selectedImages = [];
  bool _isLoading = false;
  bool _isUserTypeValid = false;

  @override
  void initState() {
    super.initState();

    // التحقق من نوع المستخدم
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkUserType();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _minPriceController.dispose();
    _maxPriceController.dispose();
    _minRoomsController.dispose();
    _minBathroomsController.dispose();
    _minAreaController.dispose();
    _additionalRequirementsController.dispose();
    super.dispose();
  }

  /// التحقق من نوع المستخدم
  Future<void> _checkUserType() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _showErrorDialog('يجب تسجيل الدخول لإنشاء طلب عقار');
      return;
    }

    try {
      final userDoc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
      final userData = userDoc.data();

      if (userData == null) {
        _showErrorDialog('بيانات المستخدم غير موجودة');
        return;
      }

      final userType = userData['type'];
      final userTypeString = userData['userType'] as String?;

      // التحقق من نوع المستخدم - يجب أن يكون باحث عن عقار
      bool isSeeker = userType == 1 ||
                     userTypeString == 'seeker' ||
                     userTypeString == 'user' ||
                     userTypeString == 'property_seeker';

      if (!isSeeker) {
        _showErrorDialog('يمكن فقط للباحثين عن عقارات إنشاء طلبات عقارية');
        return;
      }

      setState(() {
        _isUserTypeValid = true;
      });
    } catch (e) {
      _showErrorDialog('حدث خطأ أثناء التحقق من نوع المستخدم');
    }
  }

  /// عرض حوار الخطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'خطأ',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        content: Text(
          message,
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: Text(
              'حسناً',
              style: GoogleFonts.cairo())),
        ]));
  }

  /// اختيار الصور
  Future<void> _pickImages() async {
    final picker = ImagePicker();
    final pickedFiles = await picker.pickMultiImage();

    if (pickedFiles.isNotEmpty) {
      setState(() {
        _selectedImages.addAll(
          pickedFiles.map((file) => File(file.path)).toList());
      });
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.camera);

    if (pickedFile != null) {
      setState(() {
        _selectedImages.add(File(pickedFile.path));
      });
    }
  }

  /// حذف صورة
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// اختيار تاريخ الحاجة
  Future<void> _selectNeededByDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _neededByDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.green)),
          child: child!);
      });

    if (pickedDate != null) {
      setState(() {
        _neededByDate = pickedDate;
      });
    }
  }

  /// إنشاء طلب العقار
  Future<void> _createPropertyRequest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedPropertyType.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يرجى اختيار نوع العقار',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
      return;
    }

    if (_selectedLocations.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يرجى اختيار منطقة واحدة على الأقل',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _showErrorDialog('يجب تسجيل الدخول لإنشاء طلب عقار');
        return;
      }

      // الحصول على بيانات المستخدم
      final userDoc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
      final userData = userDoc.data() ?? {};
      final userName = userData['fullName'] ?? user.displayName ?? 'مستخدم';
      final userImage = userData['profileImage'] ?? user.photoURL;

      // إنشاء نموذج طلب العقار المتقدم
      final request = PropertyRequestModel(
        id: '', // سيتم تعيينه في الخدمة
        title: _titleController.text,
        description: _descriptionController.text,
        userId: user.uid,
        userName: userName,
        userImage: userImage,
        propertyType: _selectedPropertyType,
        minPrice: _minPriceController.text.isNotEmpty
            ? double.parse(_minPriceController.text)
            : null,
        maxPrice: _maxPriceController.text.isNotEmpty
            ? double.parse(_maxPriceController.text)
            : null,
        preferredLocations: _selectedLocations,
        minRooms: _minRoomsController.text.isNotEmpty
            ? int.parse(_minRoomsController.text)
            : null,
        minBathrooms: _minBathroomsController.text.isNotEmpty
            ? int.parse(_minBathroomsController.text)
            : null,
        minArea: _minAreaController.text.isNotEmpty
            ? double.parse(_minAreaController.text)
            : null,
        hasCentralAC: _hasCentralAC,
        hasMaidRoom: _hasMaidRoom,
        hasGarage: _hasGarage,
        hasSwimmingPool: _hasSwimmingPool,
        hasElevator: _hasElevator,
        isFullyFurnished: _isFullyFurnished,
        additionalRequirements: _additionalRequirementsController.text.isNotEmpty
            ? _additionalRequirementsController.text
            : null,
        neededBy: _neededByDate,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());

      // استخدام Provider لإنشاء الطلب
      if (!mounted) return;
      final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
      final createdRequest = await provider.createPropertyRequest(
        request,
        images: _selectedImages.isNotEmpty ? _selectedImages : null);

      if (createdRequest != null && mounted) {
        // عرض إشعار فوري
        NotificationService.showInAppNotification(
          context,
          title: 'تم إنشاء طلب العقار بنجاح!',
          body: 'سيتم إشعارك عند وصول عروض جديدة على طلبك',
          type: domain.NotificationType.newPropertyRequestOffer,
          duration: const Duration(seconds: 4),
        );

        Navigator.pop(context, true); // إرجاع true عند النجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إنشاء الطلب بنجاح',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.green));
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل إنشاء الطلب',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ: $e',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isUserTypeValid) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            'إنشاء طلب عقار',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold)),
          centerTitle: true),
        body: Center(
          child: EnhancedProgressIndicator(
            currentStep: 1,
            totalSteps: 1,
            stepTitles: ['جاري التحميل'])));
    }

    return Scaffold(
      backgroundColor: AppColors.orangeBackground,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.orangeGradient,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(30),
              bottomRight: Radius.circular(30)))),
        title: Text(
          'إنشاء طلب عقار جديد',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white)),
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white)),
      body: _isLoading
          ? Container(
              decoration: const BoxDecoration(
                gradient: AppColors.lightOrangeGradient),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryOrange.withValues(alpha: 0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 10)),
                    ]),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: AppColors.orangeGradient,
                          borderRadius: BorderRadius.circular(50)),
                        child: const Icon(
                          Icons.home_work,
                          color: Colors.white,
                          size: 32)),
                      const SizedBox(height: 20),
                      Text(
                        'جاري إنشاء طلبك...',
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary)),
                      const SizedBox(height: 8),
                      Text(
                        'يرجى الانتظار قليلاً',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: AppColors.textSecondary)),
                    ]))))
          : Container(
              decoration: const BoxDecoration(
                gradient: AppColors.lightOrangeGradient),
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان الطلب
                      Container(
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primaryOrange.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4)),
                          ]),
                        child: TextFormField(
                          controller: _titleController,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            color: AppColors.textPrimary),
                          decoration: InputDecoration(
                            labelText: 'عنوان الطلب',
                            labelStyle: GoogleFonts.cairo(
                              color: AppColors.primaryOrange,
                              fontWeight: FontWeight.w600),
                            hintText: 'مثال: أبحث عن شقة في السالمية',
                            hintStyle: GoogleFonts.cairo(
                              color: AppColors.textSecondary),
                            prefixIcon: Container(
                              margin: const EdgeInsets.all(12),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: AppColors.orangeGradient,
                                borderRadius: BorderRadius.circular(10)),
                              child: const Icon(
                                Icons.title,
                                color: Colors.white,
                                size: 20)),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(16),
                              borderSide: BorderSide.none),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16)),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال عنوان الطلب';
                            }
                            return null;
                          })),

                      // وصف الطلب
                      Container(
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primaryOrange.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4)),
                          ]),
                        child: TextFormField(
                          controller: _descriptionController,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            color: AppColors.textPrimary),
                          decoration: InputDecoration(
                            labelText: 'وصف الطلب',
                            labelStyle: GoogleFonts.cairo(
                              color: AppColors.primaryOrange,
                              fontWeight: FontWeight.w600),
                            hintText: 'اكتب تفاصيل أكثر عن العقار الذي تبحث عنه',
                            hintStyle: GoogleFonts.cairo(
                              color: AppColors.textSecondary),
                            prefixIcon: Container(
                              margin: const EdgeInsets.all(12),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: AppColors.orangeGradient,
                                borderRadius: BorderRadius.circular(10)),
                              child: const Icon(
                                Icons.description,
                                color: Colors.white,
                                size: 20)),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(16),
                              borderSide: BorderSide.none),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16)),
                          maxLines: 4,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال وصف الطلب';
                            }
                            return null;
                          })),

                    // نوع العقار
                    Text(
                      'نوع العقار',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    PropertyTypeSelector(
                      selectedType: _selectedPropertyType,
                      onTypeSelected: (type) {
                        setState(() {
                          _selectedPropertyType = type;
                        });
                      }),
                    const SizedBox(height: 16),

                    // المناطق المفضلة
                    Text(
                      'المناطق المفضلة',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    LocationSelector(
                      selectedLocations: _selectedLocations,
                      onLocationsChanged: (locations) {
                        setState(() {
                          _selectedLocations = locations;
                        });
                      }),
                    const SizedBox(height: 16),

                    // نطاق السعر
                    Text(
                      'نطاق السعر (د.ك)',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _minPriceController,
                            decoration: InputDecoration(
                              labelText: 'الحد الأدنى',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8))),
                            keyboardType: TextInputType.number)),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _maxPriceController,
                            decoration: InputDecoration(
                              labelText: 'الحد الأقصى',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8))),
                            keyboardType: TextInputType.number)),
                      ]),
                    const SizedBox(height: 16),

                    // عدد الغرف والحمامات
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _minRoomsController,
                            decoration: InputDecoration(
                              labelText: 'الحد الأدنى لعدد الغرف',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8))),
                            keyboardType: TextInputType.number)),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _minBathroomsController,
                            decoration: InputDecoration(
                              labelText: 'الحد الأدنى لعدد الحمامات',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8))),
                            keyboardType: TextInputType.number)),
                      ]),
                    const SizedBox(height: 16),

                    // المساحة
                    TextFormField(
                      controller: _minAreaController,
                      decoration: InputDecoration(
                        labelText: 'الحد الأدنى للمساحة (متر مربع)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8))),
                      keyboardType: TextInputType.number),
                    const SizedBox(height: 16),

                    // المميزات
                    Text(
                      'المميزات المطلوبة',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 16,
                      runSpacing: 8,
                      children: [
                        _buildFeatureCheckbox(
                          'تكييف مركزي',
                          _hasCentralAC,
                          (value) {
                            setState(() {
                              _hasCentralAC = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'غرفة خادمة',
                          _hasMaidRoom,
                          (value) {
                            setState(() {
                              _hasMaidRoom = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مرآب',
                          _hasGarage,
                          (value) {
                            setState(() {
                              _hasGarage = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مسبح',
                          _hasSwimmingPool,
                          (value) {
                            setState(() {
                              _hasSwimmingPool = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مصعد',
                          _hasElevator,
                          (value) {
                            setState(() {
                              _hasElevator = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مفروش بالكامل',
                          _isFullyFurnished,
                          (value) {
                            setState(() {
                              _isFullyFurnished = value ?? false;
                            });
                          }),
                      ]),
                    const SizedBox(height: 16),

                    // متطلبات إضافية
                    TextFormField(
                      controller: _additionalRequirementsController,
                      decoration: InputDecoration(
                        labelText: 'متطلبات إضافية',
                        hintText: 'أي متطلبات أخرى ترغب في إضافتها',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8))),
                      maxLines: 3),
                    const SizedBox(height: 16),

                    // تاريخ الحاجة
                    InkWell(
                      onTap: _selectNeededByDate,
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'مطلوب بحلول',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8)),
                          suffixIcon: const Icon(Icons.calendar_today)),
                        child: Text(
                          _neededByDate != null
                              ? DateFormat('yyyy/MM/dd').format(_neededByDate!)
                              : 'اختر تاريخ',
                          style: GoogleFonts.cairo()))),
                    const SizedBox(height: 16),

                    // الصور
                    Text(
                      'الصور (اختياري)',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton.icon(
                          onPressed: _pickImages,
                          icon: const Icon(Icons.photo_library),
                          label: Text(
                            'اختيار من المعرض',
                            style: GoogleFonts.cairo()),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white)),
                        const SizedBox(width: 16),
                        ElevatedButton.icon(
                          onPressed: _takePhoto,
                          icon: const Icon(Icons.camera_alt),
                          label: Text(
                            'التقاط صورة',
                            style: GoogleFonts.cairo()),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white)),
                      ]),
                    const SizedBox(height: 8),
                    if (_selectedImages.isNotEmpty)
                      SizedBox(
                        height: 100,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: _selectedImages.length,
                          itemBuilder: (context, index) {
                            return Stack(
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    image: DecorationImage(
                                      image: FileImage(_selectedImages[index]),
                                      fit: BoxFit.cover))),
                                Positioned(
                                  top: 0,
                                  right: 8,
                                  child: InkWell(
                                    onTap: () => _removeImage(index),
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: const BoxDecoration(
                                        color: Colors.red,
                                        shape: BoxShape.circle),
                                      child: const Icon(
                                        Icons.close,
                                        size: 16,
                                        color: Colors.white)))),
                              ]);
                          })),
                    const SizedBox(height: 24),

                      // زر الإنشاء العصري
                      Container(
                        margin: const EdgeInsets.symmetric(vertical: 20),
                        decoration: BoxDecoration(
                          gradient: AppColors.orangeGradient,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primaryOrange.withValues(alpha: 0.4),
                              blurRadius: 15,
                              offset: const Offset(0, 8)),
                          ]),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: _createPropertyRequest,
                            borderRadius: BorderRadius.circular(20),
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(vertical: 18),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(10)),
                                    child: const Icon(
                                      Icons.send_rounded,
                                      color: Colors.white,
                                      size: 24)),
                                  const SizedBox(width: 12),
                                  Text(
                                    'إنشاء الطلب',
                                    style: GoogleFonts.cairo(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white)),
                                ]))))),
                  ])))));
  }

  /// بناء مربع اختيار الميزة
  Widget _buildFeatureCheckbox(
    String label,
    bool value,
    ValueChanged<bool?> onChanged) {
    return SizedBox(
      width: 150,
      child: CheckboxListTile(
        title: Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 14)),
        value: value,
        onChanged: onChanged,
        contentPadding: EdgeInsets.zero,
        controlAffinity: ListTileControlAffinity.leading,
        dense: true,
        activeColor: Colors.green));
  }
}
