// lib/domain/repositories/property_request_repository.dart
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/property_request/property_offer_model.dart';
import '../models/property_request/property_request_model.dart';

/// واجهة مستودع طلبات العقارات
abstract class PropertyRequestRepository {
  /// الحصول على جميع طلبات العقارات
  Future<List<PropertyRequestModel>> getAllPropertyRequests({
    int limit = 10,
    DocumentSnapshot? startAfter,
    String? sortBy,
    bool descending = true,
    Map<String, dynamic>? filters,
  });
  
  /// الحصول على طلب عقار محدد بواسطة المعرف
  Future<PropertyRequestModel?> getPropertyRequestById(String requestId);
  
  /// إنشاء طلب عقار جديد
  Future<PropertyRequestModel> createPropertyRequest(
    PropertyRequestModel request, {
    List<File>? images,
  });
  
  /// تحديث طلب عقار موجود
  Future<void> updatePropertyRequest(
    PropertyRequestModel request, {
    List<File>? newImages,
    List<String>? imagesToDelete,
  });
  
  /// حذف طلب عقار
  Future<void> deletePropertyRequest(String requestId);
  
  /// تحديث حالة طلب عقار
  Future<void> updatePropertyRequestStatus(String requestId, RequestStatus status);
  
  /// زيادة عدد مشاهدات طلب عقار
  Future<void> incrementPropertyRequestViews(String requestId);
  
  /// الحصول على طلبات العقارات الخاصة بمستخدم محدد
  Future<List<PropertyRequestModel>> getUserPropertyRequests(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  });
  
  /// الحصول على طلبات العقارات الأكثر مشاهدة
  Future<List<PropertyRequestModel>> getMostViewedPropertyRequests({int limit = 10});
  
  /// الحصول على طلبات العقارات الأحدث
  Future<List<PropertyRequestModel>> getLatestPropertyRequests({int limit = 10});
  
  /// البحث في طلبات العقارات
  Future<List<PropertyRequestModel>> searchPropertyRequests(
    String query, {
    int limit = 20,
    Map<String, dynamic>? filters,
  });
  
  /// الحصول على عروض طلب عقار محدد
  Future<List<PropertyOfferModel>> getOffersByRequest(
    String requestId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  });
  
  /// الحصول على عرض محدد بواسطة المعرف
  Future<PropertyOfferModel?> getOfferById(String offerId);
  
  /// إنشاء عرض جديد
  Future<PropertyOfferModel> createOffer(
    PropertyOfferModel offer, {
    List<File>? images,
  });
  
  /// تحديث عرض موجود
  Future<void> updateOffer(
    PropertyOfferModel offer, {
    List<File>? newImages,
    List<String>? imagesToDelete,
  });
  
  /// حذف عرض
  Future<void> deleteOffer(String offerId);
  
  /// تحديث حالة عرض
  Future<void> updateOfferStatus(String offerId, OfferStatus status);
  
  /// الحصول على العروض الخاصة بمستخدم محدد
  Future<List<PropertyOfferModel>> getUserOffers(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  });
  
  /// الاستماع للتغييرات في طلبات العقارات
  Stream<List<PropertyRequestModel>> listenToPropertyRequests({
    int limit = 10,
    String? sortBy,
    bool descending = true,
    Map<String, dynamic>? filters,
  });
  
  /// الاستماع للتغييرات في طلب عقار محدد
  Stream<PropertyRequestModel?> listenToPropertyRequest(String requestId);
  
  /// الاستماع للتغييرات في عروض طلب عقار محدد
  Stream<List<PropertyOfferModel>> listenToRequestOffers(
    String requestId, {
    int limit = 20,
  });
  
  /// الاستماع للتغييرات في عرض محدد
  Stream<PropertyOfferModel?> listenToOffer(String offerId);
}
