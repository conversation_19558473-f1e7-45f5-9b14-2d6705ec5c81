import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';

class SupportPage extends StatefulWidget {
  const SupportPage({super.key});

  @override
  State<SupportPage> createState() => _SupportPageState();
}

class _SupportPageState extends State<SupportPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _subjectController = TextEditingController();
  final _messageController = TextEditingController();

  String _selectedIssueType = 'استفسار عام';
  bool _isSubmitting = false;
  bool _isSubmitted = false;

  final List<String> _issueTypes = [
    'استفسار عام',
    'مشكلة تقنية',
    'اقتراح تحسين',
    'مشكلة في الدفع',
    'مشكلة في الحساب',
    'أخرى',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _subjectController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSubmitting = true;
      });

      // محاكاة إرسال البيانات إلى الخادم
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isSubmitting = false;
        _isSubmitted = true;
      });

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.',
              style: GoogleFonts.cairo()),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 3)));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الدعم الفني',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
        elevation: 0,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white),
      body: _isSubmitted ? _buildSuccessView() : _buildFormView());
  }

  Widget _buildSuccessView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                shape: BoxShape.circle),
              child: Icon(
                Icons.check_circle,
                size: 64,
                color: AppColors.success)),
            const SizedBox(height: 24),
            Text(
              'تم إرسال رسالتك بنجاح!',
              style: GoogleFonts.cairo(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800)),
            const SizedBox(height: 16),
            Text(
              'شكراً لتواصلك معنا. سيقوم فريق الدعم الفني بالرد عليك في أقرب وقت ممكن.',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey.shade600),
              textAlign: TextAlign.center),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _isSubmitted = false;
                  _nameController.clear();
                  _emailController.clear();
                  _subjectController.clear();
                  _messageController.clear();
                  _selectedIssueType = 'استفسار عام';
                });
              },
              icon: const Icon(Icons.add),
              label: Text(
                'إرسال رسالة أخرى',
                style: GoogleFonts.cairo()),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)))),
          ])));
  }

  Widget _buildFormView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الاتصال
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.info.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'معلومات الاتصال',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.info)),
                const SizedBox(height: 8),
                _buildContactItem(
                  Icons.email,
                  'البريد الإلكتروني:',
                  '<EMAIL>'),
                _buildContactItem(
                  Icons.phone,
                  'الهاتف:',
                  '+965 1234 5678'),
                _buildContactItem(
                  Icons.access_time,
                  'ساعات العمل:',
                  'الأحد - الخميس: 9:00 ص - 5:00 م'),
              ])),

          const SizedBox(height: 24),

          // نموذج الاتصال
          Text(
            'إرسال رسالة',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.primary)),
          const SizedBox(height: 16),
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الاسم
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'الاسم',
                    prefixIcon: const Icon(Icons.person),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12))),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال الاسم';
                    }
                    return null;
                  }),
                const SizedBox(height: 16),

                // البريد الإلكتروني
                TextFormField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    labelText: 'البريد الإلكتروني',
                    prefixIcon: const Icon(Icons.email),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12))),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال البريد الإلكتروني';
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                      return 'الرجاء إدخال بريد إلكتروني صحيح';
                    }
                    return null;
                  }),
                const SizedBox(height: 16),

                // نوع المشكلة
                DropdownButtonFormField<String>(
                  value: _selectedIssueType,
                  decoration: InputDecoration(
                    labelText: 'نوع المشكلة',
                    prefixIcon: const Icon(Icons.category),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12))),
                  items: _issueTypes.map((String type) {
                    return DropdownMenuItem<String>(
                      value: type,
                      child: Text(type));
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedIssueType = newValue;
                      });
                    }
                  }),
                const SizedBox(height: 16),

                // الموضوع
                TextFormField(
                  controller: _subjectController,
                  decoration: InputDecoration(
                    labelText: 'الموضوع',
                    prefixIcon: const Icon(Icons.subject),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12))),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال الموضوع';
                    }
                    return null;
                  }),
                const SizedBox(height: 16),

                // الرسالة
                TextFormField(
                  controller: _messageController,
                  maxLines: 5,
                  decoration: InputDecoration(
                    labelText: 'الرسالة',
                    alignLabelWithHint: true,
                    prefixIcon: const Padding(
                      padding: EdgeInsets.only(bottom: 80),
                      child: Icon(Icons.message)),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12))),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال الرسالة';
                    }
                    if (value.length < 10) {
                      return 'الرسالة قصيرة جداً';
                    }
                    return null;
                  }),
                const SizedBox(height: 24),

                // زر الإرسال
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton.icon(
                    onPressed: _isSubmitting ? null : _submitForm,
                    icon: _isSubmitting
                        ? Container(
                            width: 24,
                            height: 24,
                            padding: const EdgeInsets.all(2.0),
                            child: const CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 3))
                        : const Icon(Icons.send),
                    label: Text(
                      _isSubmitting ? 'جاري الإرسال...' : 'إرسال',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12))))),
              ])),
        ]));
  }

  Widget _buildContactItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: AppColors.info, size: 20),
          const SizedBox(width: 8),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700)),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                color: Colors.grey.shade800))),
        ]));
  }
}
