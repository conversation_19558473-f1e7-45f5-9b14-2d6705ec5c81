// lib/presentation/pages/property_request/create_property_offer_page.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/property_request/property_offer_model.dart';
import '../../providers/property_request_provider.dart';

/// صفحة إنشاء عرض لطلب عقار
class CreatePropertyOfferPage extends StatefulWidget {
  /// معرف طلب العقار
  final String requestId;

  /// عنوان طلب العقار
  final String requestTitle;

  const CreatePropertyOfferPage({
    super.key,
    required this.requestId,
    required this.requestTitle,
  });

  @override
  State<CreatePropertyOfferPage> createState() => _CreatePropertyOfferPageState();
}

class _CreatePropertyOfferPageState extends State<CreatePropertyOfferPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _addressController = TextEditingController();
  final _roomsController = TextEditingController();
  final _bathroomsController = TextEditingController();
  final _areaController = TextEditingController();

  bool _hasCentralAC = false;
  bool _hasMaidRoom = false;
  bool _hasGarage = false;
  bool _hasSwimmingPool = false;
  bool _hasElevator = false;
  bool _isFullyFurnished = false;

  final List<File> _selectedImages = [];
  bool _isLoading = false;
  bool _isUserTypeValid = false;

  @override
  void initState() {
    super.initState();

    // التحقق من نوع المستخدم
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkUserType();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _addressController.dispose();
    _roomsController.dispose();
    _bathroomsController.dispose();
    _areaController.dispose();
    super.dispose();
  }

  /// التحقق من نوع المستخدم
  Future<void> _checkUserType() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _showErrorDialog('يجب تسجيل الدخول لإنشاء عرض');
      return;
    }

    try {
      final userDoc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
      final userData = userDoc.data();

      if (userData == null) {
        _showErrorDialog('بيانات المستخدم غير موجودة');
        return;
      }

      final userType = userData['type'];

      if (userType == 1) { // 1 is the index for seeker user type
        _showErrorDialog('لا يمكن للباحثين عن عقارات إنشاء عروض');
        return;
      }

      setState(() {
        _isUserTypeValid = true;
      });
    } catch (e) {
      _showErrorDialog('حدث خطأ أثناء التحقق من نوع المستخدم');
    }
  }

  /// عرض حوار الخطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'خطأ',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        content: Text(
          message,
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: Text(
              'حسناً',
              style: GoogleFonts.cairo())),
        ]));
  }

  /// اختيار الصور
  Future<void> _pickImages() async {
    final picker = ImagePicker();
    final pickedFiles = await picker.pickMultiImage();

    if (pickedFiles.isNotEmpty) {
      setState(() {
        _selectedImages.addAll(
          pickedFiles.map((file) => File(file.path)).toList());
      });
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.camera);

    if (pickedFile != null) {
      setState(() {
        _selectedImages.add(File(pickedFile.path));
      });
    }
  }

  /// حذف صورة
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// إنشاء العرض
  Future<void> _createOffer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedImages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يرجى إضافة صورة واحدة على الأقل',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _showErrorDialog('يجب تسجيل الدخول لإنشاء عرض');
        return;
      }

      // إنشاء نموذج العرض
      final offer = PropertyOfferModel(
        id: '', // سيتم تعيينه في المستودع
        requestId: widget.requestId,
        requestTitle: widget.requestTitle,
        userId: user.uid,
        userName: user.displayName ?? 'مستخدم',
        userImage: user.photoURL,
        userType: '0', // 0 for owner
        title: _titleController.text,
        description: _descriptionController.text,
        price: double.parse(_priceController.text),
        address: _addressController.text,
        rooms: int.parse(_roomsController.text),
        bathrooms: int.parse(_bathroomsController.text),
        area: double.parse(_areaController.text),
        hasCentralAC: _hasCentralAC,
        hasMaidRoom: _hasMaidRoom,
        hasGarage: _hasGarage,
        hasSwimmingPool: _hasSwimmingPool,
        hasElevator: _hasElevator,
        isFullyFurnished: _isFullyFurnished,
        status: OfferStatus.pending,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());

      // إنشاء العرض
      final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
      final createdOffer = await provider.createOffer(
        offer,
        images: _selectedImages);

      if (createdOffer != null && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إنشاء العرض بنجاح',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.green));
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل إنشاء العرض',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ: $e',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isUserTypeValid) {
      return Scaffold(
        backgroundColor: AppColors.orangeBackground,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.orangeGradient,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30)))),
          title: Text(
            'إنشاء عرض عقاري',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.white)),
          centerTitle: true,
          iconTheme: const IconThemeData(color: Colors.white)),
        body: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.lightOrangeGradient),
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryOrange.withValues(alpha: 0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 10)),
                ]),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // مؤشر التحميل العصري
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          gradient: AppColors.orangeGradient,
                          borderRadius: BorderRadius.circular(40))),
                      const SizedBox(
                        width: 100,
                        height: 100,
                        child: CircularProgressIndicator(
                          strokeWidth: 4,
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryOrange))),
                      const Icon(
                        Icons.verified_user_rounded,
                        color: Colors.white,
                        size: 32),
                    ]),
                  const SizedBox(height: 24),
                  Text(
                    'جاري التحقق من الصلاحيات',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary)),
                  const SizedBox(height: 8),
                  Text(
                    'يرجى الانتظار قليلاً...',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: AppColors.textSecondary)),
                ])))));
    }

    return Scaffold(
      backgroundColor: AppColors.orangeBackground,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.orangeGradient,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(30),
              bottomRight: Radius.circular(30)))),
        title: Text(
          'إنشاء عرض عقاري',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white)),
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white)),
      body: _isLoading
          ? Container(
              decoration: const BoxDecoration(
                gradient: AppColors.lightOrangeGradient),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryOrange.withValues(alpha: 0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 10)),
                    ]),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // مؤشر التحميل العصري
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              gradient: AppColors.orangeGradient,
                              borderRadius: BorderRadius.circular(40))),
                          const SizedBox(
                            width: 100,
                            height: 100,
                            child: CircularProgressIndicator(
                              strokeWidth: 4,
                              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryOrange))),
                          const Icon(
                            Icons.local_offer_rounded,
                            color: Colors.white,
                            size: 32),
                        ]),
                      const SizedBox(height: 24),
                      Text(
                        'جاري إنشاء عرضك',
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary)),
                      const SizedBox(height: 8),
                      Text(
                        'يرجى الانتظار قليلاً...',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: AppColors.textSecondary)),
                    ]))))
          : Container(
              decoration: const BoxDecoration(
                gradient: AppColors.lightOrangeGradient),
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // بطاقة معلومات الطلب
                      Container(
                        margin: const EdgeInsets.only(bottom: 24),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppColors.primaryOrange.withValues(alpha: 0.1),
                              AppColors.lightOrange.withValues(alpha: 0.05),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: AppColors.primaryOrange.withValues(alpha: 0.3),
                            width: 1)),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                gradient: AppColors.orangeGradient,
                                borderRadius: BorderRadius.circular(15)),
                              child: const Icon(
                                Icons.assignment,
                                color: Colors.white,
                                size: 24)),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'عرض لطلب:',
                                    style: GoogleFonts.cairo(
                                      fontSize: 12,
                                      color: AppColors.textSecondary,
                                      fontWeight: FontWeight.w600)),
                                  const SizedBox(height: 4),
                                  Text(
                                    widget.requestTitle,
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textPrimary)),
                                ])),
                          ])),

                      // عنوان العرض
                      Container(
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primaryOrange.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4)),
                          ]),
                        child: TextFormField(
                          controller: _titleController,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            color: AppColors.textPrimary),
                          decoration: InputDecoration(
                            labelText: 'عنوان العرض',
                            labelStyle: GoogleFonts.cairo(
                              color: AppColors.primaryOrange,
                              fontWeight: FontWeight.w600),
                            hintText: 'مثال: شقة فاخرة في السالمية',
                            hintStyle: GoogleFonts.cairo(
                              color: AppColors.textSecondary),
                            prefixIcon: Container(
                              margin: const EdgeInsets.all(12),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: AppColors.orangeGradient,
                                borderRadius: BorderRadius.circular(10)),
                              child: const Icon(
                                Icons.title,
                                color: Colors.white,
                                size: 20)),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(16),
                              borderSide: BorderSide.none),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16)),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال عنوان العرض';
                            }
                            return null;
                          })),

                      // وصف العرض
                      Container(
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primaryOrange.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4)),
                          ]),
                        child: TextFormField(
                          controller: _descriptionController,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            color: AppColors.textPrimary),
                          decoration: InputDecoration(
                            labelText: 'وصف العرض',
                            labelStyle: GoogleFonts.cairo(
                              color: AppColors.primaryOrange,
                              fontWeight: FontWeight.w600),
                            hintText: 'اكتب تفاصيل أكثر عن العقار الذي تعرضه',
                            hintStyle: GoogleFonts.cairo(
                              color: AppColors.textSecondary),
                            prefixIcon: Container(
                              margin: const EdgeInsets.all(12),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: AppColors.orangeGradient,
                                borderRadius: BorderRadius.circular(10)),
                              child: const Icon(
                                Icons.description,
                                color: Colors.white,
                                size: 20)),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(16),
                              borderSide: BorderSide.none),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16)),
                          maxLines: 4,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال وصف العرض';
                            }
                            return null;
                          })),

                      // السعر
                      Container(
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primaryOrange.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4)),
                          ]),
                        child: TextFormField(
                          controller: _priceController,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            color: AppColors.textPrimary),
                          decoration: InputDecoration(
                            labelText: 'السعر (د.ك)',
                            labelStyle: GoogleFonts.cairo(
                              color: AppColors.primaryOrange,
                              fontWeight: FontWeight.w600),
                            hintText: 'أدخل السعر بالدينار الكويتي',
                            hintStyle: GoogleFonts.cairo(
                              color: AppColors.textSecondary),
                            prefixIcon: Container(
                              margin: const EdgeInsets.all(12),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: AppColors.orangeGradient,
                                borderRadius: BorderRadius.circular(10)),
                              child: const Icon(
                                Icons.attach_money,
                                color: Colors.white,
                                size: 20)),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(16),
                              borderSide: BorderSide.none),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16)),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال السعر';
                            }
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال سعر صحيح';
                            }
                            return null;
                          })),

                    // العنوان
                    TextFormField(
                      controller: _addressController,
                      decoration: InputDecoration(
                        labelText: 'العنوان',
                        hintText: 'مثال: السالمية، شارع سالم المبارك، بناية 10',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8))),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال العنوان';
                        }
                        return null;
                      }),
                    const SizedBox(height: 16),

                    // عدد الغرف والحمامات
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _roomsController,
                            decoration: InputDecoration(
                              labelText: 'عدد الغرف',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8))),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال عدد الغرف';
                              }
                              if (int.tryParse(value) == null) {
                                return 'يرجى إدخال رقم صحيح';
                              }
                              return null;
                            })),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _bathroomsController,
                            decoration: InputDecoration(
                              labelText: 'عدد الحمامات',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8))),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال عدد الحمامات';
                              }
                              if (int.tryParse(value) == null) {
                                return 'يرجى إدخال رقم صحيح';
                              }
                              return null;
                            })),
                      ]),
                    const SizedBox(height: 16),

                    // المساحة
                    TextFormField(
                      controller: _areaController,
                      decoration: InputDecoration(
                        labelText: 'المساحة (متر مربع)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8))),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال المساحة';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال رقم صحيح';
                        }
                        return null;
                      }),
                    const SizedBox(height: 16),

                    // المميزات
                    Text(
                      'المميزات المتوفرة',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 16,
                      runSpacing: 8,
                      children: [
                        _buildFeatureCheckbox(
                          'تكييف مركزي',
                          _hasCentralAC,
                          (value) {
                            setState(() {
                              _hasCentralAC = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'غرفة خادمة',
                          _hasMaidRoom,
                          (value) {
                            setState(() {
                              _hasMaidRoom = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مرآب',
                          _hasGarage,
                          (value) {
                            setState(() {
                              _hasGarage = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مسبح',
                          _hasSwimmingPool,
                          (value) {
                            setState(() {
                              _hasSwimmingPool = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مصعد',
                          _hasElevator,
                          (value) {
                            setState(() {
                              _hasElevator = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مفروش بالكامل',
                          _isFullyFurnished,
                          (value) {
                            setState(() {
                              _isFullyFurnished = value ?? false;
                            });
                          }),
                      ]),
                    const SizedBox(height: 16),

                    // الصور
                    Text(
                      'صور العقار',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton.icon(
                          onPressed: _pickImages,
                          icon: const Icon(Icons.photo_library),
                          label: Text(
                            'اختيار من المعرض',
                            style: GoogleFonts.cairo()),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white)),
                        const SizedBox(width: 16),
                        ElevatedButton.icon(
                          onPressed: _takePhoto,
                          icon: const Icon(Icons.camera_alt),
                          label: Text(
                            'التقاط صورة',
                            style: GoogleFonts.cairo()),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white)),
                      ]),
                    const SizedBox(height: 8),
                    if (_selectedImages.isNotEmpty)
                      SizedBox(
                        height: 100,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: _selectedImages.length,
                          itemBuilder: (context, index) {
                            return Stack(
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    image: DecorationImage(
                                      image: FileImage(_selectedImages[index]),
                                      fit: BoxFit.cover))),
                                Positioned(
                                  top: 0,
                                  right: 8,
                                  child: InkWell(
                                    onTap: () => _removeImage(index),
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: const BoxDecoration(
                                        color: Colors.red,
                                        shape: BoxShape.circle),
                                      child: const Icon(
                                        Icons.close,
                                        size: 16,
                                        color: Colors.white)))),
                              ]);
                          })),
                    const SizedBox(height: 24),

                    // زر الإنشاء
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _createOffer,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8))),
                        child: Text(
                          'إرسال العرض',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold)))),
                  ])))));
  }

  /// بناء مربع اختيار الميزة
  Widget _buildFeatureCheckbox(
    String label,
    bool value,
    ValueChanged<bool?> onChanged) {
    return SizedBox(
      width: 150,
      child: CheckboxListTile(
        title: Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 14)),
        value: value,
        onChanged: onChanged,
        contentPadding: EdgeInsets.zero,
        controlAffinity: ListTileControlAffinity.leading,
        dense: true,
        activeColor: Colors.green));
  }
}
