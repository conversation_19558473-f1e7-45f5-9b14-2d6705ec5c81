import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../domain/entities/market_prediction.dart';
import '../../domain/repositories/analytics_repository.dart';

/// خدمة تنبؤات السوق
class MarketPredictionService {
  final AnalyticsRepository _analyticsRepository;

  /// إنشاء خدمة تنبؤات السوق
  MarketPredictionService(this._analyticsRepository);

  /// الحصول على تنبؤ السوق بواسطة المعرف
  Future<MarketPrediction?> getMarketPredictionById(String predictionId) {
    return _analyticsRepository.getMarketPredictionById(predictionId);
  }

  /// الحصول على تنبؤات السوق
  Future<List<MarketPrediction>> getMarketPredictions({
    PredictionType? type,
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketPredictions(
      type: type,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تنبؤات السوق بالتحميل المتدرج
  Future<Map<String, dynamic>> getMarketPredictionsPaginated({
    PredictionType? type,
    PredictionPeriod? period,
    String? area,
    String? propertyType,
    int limit = 20,
    String? lastPredictionId,
  }) {
    return _analyticsRepository.getMarketPredictionsPaginated(
      type: type,
      period: period,
      area: area,
      propertyType: propertyType,
      limit: limit,
      lastPredictionId: lastPredictionId);
  }

  /// الحصول على أحدث تنبؤات السوق
  Future<List<MarketPrediction>> getLatestMarketPredictions({int limit = 5}) {
    return _analyticsRepository.getLatestMarketPredictions(limit: limit);
  }

  /// الحصول على تنبؤات أسعار البيع
  Future<List<MarketPrediction>> getSalePricePredictions({
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketPredictions(
      type: PredictionType.salePrices,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تنبؤات أسعار الإيجار
  Future<List<MarketPrediction>> getRentalPricePredictions({
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketPredictions(
      type: PredictionType.rentalPrices,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تنبؤات العرض
  Future<List<MarketPrediction>> getSupplyPredictions({
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketPredictions(
      type: PredictionType.supply,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تنبؤات الطلب
  Future<List<MarketPrediction>> getDemandPredictions({
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketPredictions(
      type: PredictionType.demand,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تنبؤات عدد الصفقات
  Future<List<MarketPrediction>> getTransactionsPredictions({
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketPredictions(
      type: PredictionType.transactions,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تنبؤات العائد الاستثماري
  Future<List<MarketPrediction>> getInvestmentReturnPredictions({
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketPredictions(
      type: PredictionType.investmentReturn,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تنبؤات معدل النمو
  Future<List<MarketPrediction>> getGrowthRatePredictions({
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketPredictions(
      type: PredictionType.growthRate,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تنبؤات معدل الإشغال
  Future<List<MarketPrediction>> getOccupancyRatePredictions({
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketPredictions(
      type: PredictionType.occupancyRate,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تنبؤات السوق للمناطق المختلفة
  Future<Map<String, MarketPrediction>> getAreasPredictions({
    required PredictionType type,
    required PredictionPeriod period,
    required List<String> areas,
    String? propertyType,
  }) async {
    final result = <String, MarketPrediction>{};

    for (final area in areas) {
      final predictions = await _analyticsRepository.getMarketPredictions(
        type: type,
        period: period,
        area: area,
        propertyType: propertyType);

      if (predictions.isNotEmpty) {
        result[area] = predictions.first;
      }
    }

    return result;
  }

  /// الحصول على تنبؤات السوق لأنواع العقارات المختلفة
  Future<Map<String, MarketPrediction>> getPropertyTypesPredictions({
    required PredictionType type,
    required PredictionPeriod period,
    String? area,
    required List<String> propertyTypes,
  }) async {
    final result = <String, MarketPrediction>{};

    for (final propertyType in propertyTypes) {
      final predictions = await _analyticsRepository.getMarketPredictions(
        type: type,
        period: period,
        area: area,
        propertyType: propertyType);

      if (predictions.isNotEmpty) {
        result[propertyType] = predictions.first;
      }
    }

    return result;
  }

  /// الحصول على مقارنة تنبؤات السوق
  Future<Map<String, dynamic>> getMarketPredictionsComparison({
    required PredictionType type,
    required PredictionPeriod period,
    List<String>? areas,
    List<String>? propertyTypes,
  }) async {
    final result = <String, dynamic>{};

    if (areas != null && areas.isNotEmpty) {
      final areasPredictions = await getAreasPredictions(
        type: type,
        period: period,
        areas: areas,
        propertyType:
            propertyTypes?.isNotEmpty == true ? propertyTypes!.first : null);

      result['areas'] = areasPredictions;
    }

    if (propertyTypes != null && propertyTypes.isNotEmpty) {
      final propertyTypesPredictions = await getPropertyTypesPredictions(
        type: type,
        period: period,
        area: areas?.isNotEmpty == true ? areas!.first : null,
        propertyTypes: propertyTypes);

      result['propertyTypes'] = propertyTypesPredictions;
    }

    return result;
  }

  /// الحصول على أفضل المناطق للاستثمار
  Future<List<Map<String, dynamic>>> getTopInvestmentAreas({int limit = 5}) {
    return _analyticsRepository.getTopInvestmentAreas(limit: limit);
  }

  /// الحصول على أفضل أنواع العقارات للاستثمار
  Future<List<Map<String, dynamic>>> getTopInvestmentPropertyTypes(
      {int limit = 5}) {
    return _analyticsRepository.getTopInvestmentPropertyTypes(limit: limit);
  }

  /// إنشاء رسم بياني خطي للتنبؤات
  LineChartData createPredictionLineChart(MarketPrediction prediction) {
    final spots = <FlSpot>[];
    final dates = <String>[];

    // استخراج البيانات
    final predictionData = prediction.data['predictions'] as List<dynamic>;
    for (int i = 0; i < predictionData.length; i++) {
      final value = predictionData[i]['value'] as double;
      spots.add(FlSpot(i.toDouble(), value));
      dates.add(predictionData[i]['date'] as String);
    }

    // إضافة نقطة القيمة الحالية
    spots.insert(0, FlSpot(0, prediction.currentValue));
    dates.insert(0, 'الآن');

    return LineChartData(
      gridData: FlGridData(show: true),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 22,
            getTitlesWidget: (value, meta) {
              final index = value.toInt();
              String text = '';
              if (index >= 0 && index < dates.length && index % 2 == 0) {
                text = dates[index];
              }

              return SideTitleWidget(
                axisSide: meta.axisSide,
                space: 8,
                child: Text(
                  text,
                  style: const TextStyle(
                    color: Color(0xff68737d),
                    fontWeight: FontWeight.bold,
                    fontSize: 12)));
            })),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 28,
            getTitlesWidget: (value, meta) {
              return SideTitleWidget(
                axisSide: meta.axisSide,
                space: 12,
                child: Text(
                  value.toInt().toString(),
                  style: const TextStyle(
                    color: Color(0xff67727d),
                    fontWeight: FontWeight.bold,
                    fontSize: 12)));
            }))),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: const Color(0xff37434d), width: 1)),
      minX: 0,
      maxX: spots.length.toDouble() - 1,
      minY: 0,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          color: prediction.trend == PredictionTrend.up
              ? Colors.green
              : prediction.trend == PredictionTrend.down
                  ? Colors.red
                  : Colors.blue,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true,
            getDotPainter: (spot, percent, barData, index) {
              final dotColor = prediction.trend == PredictionTrend.up
                  ? Colors.green
                  : prediction.trend == PredictionTrend.down
                      ? Colors.red
                      : Colors.blue;
              return FlDotCirclePainter(
                radius: index == 0 ? 6 : 4,
                color: index == 0 ? Colors.white : dotColor,
                strokeWidth: 2,
                strokeColor: dotColor);
            }),
          belowBarData: BarAreaData(
            show: true,
            color: prediction.trend == PredictionTrend.up
                ? Colors.green.withAlpha(76)
                : prediction.trend == PredictionTrend.down
                    ? Colors.red.withAlpha(76)
                    : Colors.blue.withAlpha(76))),
      ],
      extraLinesData: ExtraLinesData(
        horizontalLines: [
          HorizontalLine(
            y: prediction.currentValue,
            color: Colors.grey,
            strokeWidth: 1,
            dashArray: [5, 5]),
        ]));
  }

  /// إنشاء رسم بياني شريطي لمقارنة التنبؤات
  BarChartData createPredictionsComparisonBarChart(
      Map<String, MarketPrediction> predictions) {
    final items = predictions.entries.toList();

    return BarChartData(
      alignment: BarChartAlignment.spaceAround,
      maxY: items
              .map((e) => e.value.predictedValue)
              .reduce((a, b) => a > b ? a : b) *
          1.2,
      barTouchData: BarTouchData(
        enabled: true,
        touchTooltipData: BarTouchTooltipData(
          tooltipBgColor: Colors.blueGrey,
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            final item = items[groupIndex];
            final name = item.key;
            final prediction = item.value;

            return BarTooltipItem(
              '$name\n',
              const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14),
              children: <TextSpan>[
                TextSpan(
                  text:
                      'الحالي: ${prediction.currentValue.toStringAsFixed(2)}\n',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12)),
                TextSpan(
                  text:
                      'المتوقع: ${prediction.predictedValue.toStringAsFixed(2)}\n',
                  style: const TextStyle(
                    color: Colors.yellow,
                    fontSize: 14,
                    fontWeight: FontWeight.w500)),
                TextSpan(
                  text:
                      'التغيير: ${prediction.changePercentage.toStringAsFixed(2)}%',
                  style: TextStyle(
                    color: prediction.changePercentage >= 0
                        ? Colors.green
                        : Colors.red,
                    fontSize: 12)),
              ]);
          })),
      titlesData: FlTitlesData(
        show: true,
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            getTitlesWidget: (double value, TitleMeta meta) {
              final index = value.toInt();
              String text = '';
              if (index >= 0 && index < items.length) {
                text = items[index].key;
              }

              return SideTitleWidget(
                axisSide: meta.axisSide,
                space: 16,
                child: Text(
                  text,
                  style: const TextStyle(
                    color: Color(0xff68737d),
                    fontWeight: FontWeight.bold,
                    fontSize: 12)));
            })),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 28,
            getTitlesWidget: (value, meta) {
              return SideTitleWidget(
                axisSide: meta.axisSide,
                space: 8,
                child: Text(
                  value.toInt().toString(),
                  style: const TextStyle(
                    color: Color(0xff67727d),
                    fontWeight: FontWeight.bold,
                    fontSize: 12)));
            }))),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: const Color(0xff37434d), width: 1)),
      barGroups: List.generate(
        items.length,
        (index) {
          final prediction = items[index].value;
          final currentValue = prediction.currentValue;
          final predictedValue = prediction.predictedValue;

          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: currentValue,
                color: Colors.grey,
                width: 15,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6))),
              BarChartRodData(
                toY: predictedValue,
                color: prediction.changePercentage >= 0
                    ? Colors.green
                    : Colors.red,
                width: 15,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6))),
            ]);
        }));
  }
}
