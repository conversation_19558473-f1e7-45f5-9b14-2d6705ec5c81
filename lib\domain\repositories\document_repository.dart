import 'dart:io';
import '../entities/estate_document.dart';

/// واجهة مستودع المستندات
abstract class DocumentRepository {
  /// الحصول على مستندات العقار
  Future<List<EstateDocument>> getEstateDocuments(String estateId, {bool publicOnly = false});
  
  /// الحصول على مستند بواسطة المعرف
  Future<EstateDocument?> getDocumentById(String documentId);
  
  /// إضافة مستند جديد
  Future<String> addDocument(EstateDocument document, File file);
  
  /// تحديث مستند موجود
  Future<void> updateDocument(EstateDocument document, {File? newFile});
  
  /// حذف مستند
  Future<void> deleteDocument(String documentId);
  
  /// تحميل ملف مستند
  Future<String> uploadDocumentFile(File file, String estateId, String documentType);
  
  /// تنزيل ملف مستند
  Future<File> downloadDocumentFile(String fileUrl, String localPath);
  
  /// الحصول على مستندات المستخدم
  Future<List<EstateDocument>> getUserDocuments(String userId);
  
  /// تغيير حالة المستند (عام/خاص)
  Future<void> toggleDocumentVisibility(String documentId, bool isPublic);
}
