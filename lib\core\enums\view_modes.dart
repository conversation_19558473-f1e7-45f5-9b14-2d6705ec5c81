/// نمط عرض الشبكة
enum GridViewMode {
  /// عرض قائمة
  list,
  /// عرض شبكة
  grid
}

/// نمط عرض الفئات
enum CategoryViewMode {
  /// عرض قائمة
  list,
  /// عرض شبكة
  grid,
  /// عرض معرض
  carousel
}

/// نمط عرض المواضيع
enum TopicViewMode {
  /// عرض قائمة
  list,
  /// عرض شبكة
  grid,
  /// عرض مضغوط
  compact
}

/// أنماط عرض المشاركات
enum PostViewMode {
  /// عرض قائمة
  list,

  /// عرض شبكة
  grid,

  /// عرض مضغوط
  compact,
}

/// أنماط عرض الإشعارات
enum NotificationViewMode {
  /// عرض قائمة
  list,

  /// عرض مضغوط
  compact,
}

/// أنماط عرض الإحصائيات
enum StatisticsViewMode {
  /// عرض قائمة
  list,

  /// عرض بطاقات
  cards,

  /// عرض رسوم بيانية
  charts,
}

/// أنماط عرض الشارات
enum BadgeViewMode {
  /// عرض شبكة
  grid,

  /// عرض قائمة
  list,
}

/// أنماط عرض الإنجازات
enum AchievementViewMode {
  /// عرض قائمة
  list,

  /// عرض بطاقات
  cards,
}

/// أنماط عرض المستخدمين
enum UserViewMode {
  /// عرض قائمة
  list,

  /// عرض شبكة
  grid,
}

/// أنماط عرض التقارير
enum ReportViewMode {
  /// عرض قائمة
  list,

  /// عرض بطاقات
  cards,
}
