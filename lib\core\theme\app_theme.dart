import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';

/// A class that contains the application's theme configurations for both light and dark modes.
class AppTheme {
  /// Light theme configuration.
  static ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    primaryColor: AppColors.primary,
    primaryColorLight: AppColors.primaryLight,
    primaryColorDark: AppColors.primaryDark,
    scaffoldBackgroundColor: AppColors.background,
    cardColor: AppColors.cardBackground,
    dividerColor: AppColors.border,
    colorScheme: ColorScheme.light(
      primary: AppColors.primary,
      secondary: AppColors.secondary,
      surface: AppColors.cardBackground,
      surfaceTint: AppColors.background,
      error: AppColors.error),
    // Apply the Cairo font family to the default light text theme as default for entire app.
    textTheme: GoogleFonts.cairoTextTheme(
      ThemeData(brightness: Brightness.light).textTheme).apply(
      fontFamily: GoogleFonts.cairo().fontFamily).copyWith(
      // Customize all text styles with Cairo font
      displayLarge: GoogleFonts.cairo(
        fontSize: 22,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary),
      displayMedium: GoogleFonts.cairo(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary),
      displaySmall: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary),
      headlineLarge: GoogleFonts.cairo(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary),
      headlineMedium: GoogleFonts.cairo(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary),
      headlineSmall: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary),
      titleLarge: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary),
      titleMedium: GoogleFonts.cairo(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: AppColors.textPrimary),
      titleSmall: GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppColors.textPrimary),
      bodyLarge: GoogleFonts.cairo(
        fontSize: 16,
        color: AppColors.textPrimary),
      bodyMedium: GoogleFonts.cairo(
        fontSize: 14,
        color: AppColors.textSecondary),
      bodySmall: GoogleFonts.cairo(
        fontSize: 12,
        color: AppColors.textSecondary),
      labelLarge: GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppColors.textPrimary),
      labelMedium: GoogleFonts.cairo(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: AppColors.textSecondary),
      labelSmall: GoogleFonts.cairo(
        fontSize: 11,
        fontWeight: FontWeight.w500,
        color: AppColors.textSecondary)),
    // Configure the style for elevated buttons.
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.buttonPrimary,
        foregroundColor: AppColors.textOnDark,
        textStyle: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold))),
    // تخصيص أزرار النص مع خط Cairo
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primary,
        textStyle: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold))),

    // تخصيص الأزرار المحددة مع خط Cairo
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primary,
        side: const BorderSide(color: AppColors.primary),
        textStyle: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.w600),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8)))),

    // تخصيص شريط التطبيق - لون أخضر متناسق
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: GoogleFonts.cairo(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.white),
      toolbarTextStyle: GoogleFonts.cairo(
        fontSize: 16,
        color: Colors.white),
      iconTheme: const IconThemeData(
        color: Colors.white,
        size: 24),
      actionsIconTheme: const IconThemeData(
        color: Colors.white,
        size: 24)),
    // تخصيص بطاقات العرض - تحسين المظهر مع حدود أخف وزوايا أكثر انسيابية
    cardTheme: CardTheme(
      color: AppColors.cardBackground,
      elevation: 1, // تقليل الارتفاع لمظهر أكثر بساطة
      shadowColor: Colors.black.withAlpha(26), // ظل أخف (alpha 26 ≈ 10%)
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12), // زوايا أكثر انحناءً
        side: BorderSide(color: AppColors.border, width: 0.5), // حدود أخف
      )),
    // تخصيص حقول الإدخال
    inputDecorationTheme: InputDecorationTheme(
      fillColor: AppColors.cardBackground,
      filled: true,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.border)),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.border)),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.primary, width: 2)),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.error)),
      labelStyle: GoogleFonts.cairo(
        color: AppColors.textSecondary,
        fontSize: 14),
      hintStyle: GoogleFonts.cairo(
        color: AppColors.textLight,
        fontSize: 14),
      helperStyle: GoogleFonts.cairo(
        color: AppColors.textSecondary,
        fontSize: 12),
      errorStyle: GoogleFonts.cairo(
        color: AppColors.error,
        fontSize: 12)));

  /// Dark theme configuration.
  static ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    primaryColor: AppColors.primary,
    primaryColorLight: AppColors.primaryLight,
    primaryColorDark: AppColors.primaryDark,
    scaffoldBackgroundColor: AppColors.darkBackground,
    cardColor: AppColors.darkCardBackground,
    dividerColor: AppColors.borderDark,
    colorScheme: ColorScheme.dark(
      primary: AppColors.primary,
      secondary: AppColors.secondary,
      surface: AppColors.darkCardBackground,
      surfaceTint: AppColors.darkBackground,
      error: AppColors.error),
    // Apply the Cairo font family to the default dark text theme.
    textTheme: GoogleFonts.cairoTextTheme(
      ThemeData(brightness: Brightness.dark).textTheme).apply(
      fontFamily: GoogleFonts.cairo().fontFamily).copyWith(
      // Customize all text styles with Cairo font for dark mode
      displayLarge: GoogleFonts.cairo(
        fontSize: 22,
        fontWeight: FontWeight.bold,
        color: AppColors.textOnDark),
      displayMedium: GoogleFonts.cairo(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppColors.textOnDark),
      displaySmall: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.textOnDark),
      headlineLarge: GoogleFonts.cairo(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: AppColors.textOnDark),
      headlineMedium: GoogleFonts.cairo(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppColors.textOnDark),
      headlineSmall: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.textOnDark),
      titleLarge: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.textOnDark),
      titleMedium: GoogleFonts.cairo(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: AppColors.textOnDark),
      titleSmall: GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppColors.textOnDark),
      bodyLarge: GoogleFonts.cairo(
        fontSize: 16,
        color: AppColors.textOnDark),
      bodyMedium: GoogleFonts.cairo(
        fontSize: 14,
        color: AppColors.textSecondaryOnDark),
      bodySmall: GoogleFonts.cairo(
        fontSize: 12,
        color: AppColors.textSecondaryOnDark),
      labelLarge: GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppColors.textOnDark),
      labelMedium: GoogleFonts.cairo(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: AppColors.textSecondaryOnDark),
      labelSmall: GoogleFonts.cairo(
        fontSize: 11,
        fontWeight: FontWeight.w500,
        color: AppColors.textSecondaryOnDark)),
    // Configure the style for elevated buttons in dark mode.
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.buttonPrimary,
        foregroundColor: AppColors.textOnDark,
        textStyle: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold))),
    // تخصيص أزرار النص في الوضع الداكن
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primaryLight,
        textStyle: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold))),
    // تخصيص أزرار الحدود في الوضع الداكن
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primaryLight,
        side: BorderSide(color: AppColors.primaryLight),
        textStyle: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold))),
    // تخصيص شريط التطبيق في الوضع الداكن - لون أخضر متناسق
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: GoogleFonts.cairo(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.white),
      iconTheme: const IconThemeData(
        color: Colors.white,
        size: 24),
      actionsIconTheme: const IconThemeData(
        color: Colors.white,
        size: 24)),
    // تخصيص بطاقات العرض في الوضع الداكن - تحسين المظهر مع حدود أخف وزوايا أكثر انسيابية
    cardTheme: CardTheme(
      color: AppColors.darkCardBackground,
      elevation: 1, // تقليل الارتفاع لمظهر أكثر بساطة
      shadowColor: Colors.black.withAlpha(51), // ظل أخف (alpha 51 ≈ 20%)
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12), // زوايا أكثر انحناءً
        side: BorderSide(color: AppColors.borderDark, width: 0.5), // حدود أخف
      )),
    // تخصيص حقول الإدخال في الوضع الداكن
    inputDecorationTheme: InputDecorationTheme(
      fillColor: AppColors.darkCardBackground,
      filled: true,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.borderDark)),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.borderDark)),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.primaryLight, width: 2)),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.error)),
      labelStyle: GoogleFonts.cairo(
        color: AppColors.textSecondaryOnDark,
        fontSize: 14),
      hintStyle: GoogleFonts.cairo(
        color: AppColors.textSecondaryOnDark,
        fontSize: 14),
      helperStyle: GoogleFonts.cairo(
        color: AppColors.textSecondaryOnDark,
        fontSize: 12),
      errorStyle: GoogleFonts.cairo(
        color: AppColors.error,
        fontSize: 12)));
}
