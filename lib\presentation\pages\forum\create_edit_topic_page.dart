import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/services/notification_service.dart';
import '../../../domain/models/forum/topic_model.dart';
import '../../../domain/models/notification_model.dart' as domain;
import '../../../presentation/providers/auth_provider.dart' as app_auth;
import '../../../presentation/providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/forum/rich_text_editor.dart';

/// صفحة إنشاء وتحرير الموضوع
class CreateEditTopicPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/create-topic';

  /// معرف الموضوع (للتحرير)
  final String? topicId;

  const CreateEditTopicPage({
    super.key,
    this.topicId,
  });

  @override
  State<CreateEditTopicPage> createState() => _CreateEditTopicPageState();
}

class _CreateEditTopicPageState extends State<CreateEditTopicPage> {
  final TextEditingController _titleController = TextEditingController();
  String _content = '';
  String? _selectedCategoryId;
  List<String> _selectedTags = [];
  final List<File> _selectedImages = [];
  List<String> _existingImages = [];
  bool _isLoading = false;
  bool _isRelatedToEstate = false;
  String? _relatedEstateId;
  TopicType _topicType = TopicType.normal;
  bool _isAdultContent = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    // تحميل الفئات
    if (forumProvider.categories.isEmpty) {
      await forumProvider.fetchCategories();
    }

    // تحميل الموضوع للتحرير
    if (widget.topicId != null) {
      setState(() {
        _isLoading = true;
      });

      await forumProvider.fetchTopic(widget.topicId!);
      final topic = forumProvider.currentTopic;

      if (topic != null) {
        setState(() {
          _titleController.text = topic.title;
          _content = topic.content;
          _selectedCategoryId = topic.categoryId;
          _selectedTags = topic.tags ?? [];
          _existingImages = topic.images ?? [];
          _isRelatedToEstate = topic.isRelatedToEstate;
          _relatedEstateId = topic.relatedEstateId;
          _topicType = topic.type;
          _isAdultContent = topic.isAdultContent;
        });
      }

      setState(() {
        _isLoading = false;
      });
    }
  }

  /// حفظ الموضوع
  Future<void> _saveTopic() async {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال عنوان الموضوع')));
      return;
    }

    if (_content.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال محتوى الموضوع')));
      return;
    }

    if (_selectedCategoryId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار فئة الموضوع')));
      return;
    }

    final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
    if (!authProvider.isLoggedIn || authProvider.user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      final category = forumProvider.categories.firstWhere(
        (category) => category.id == _selectedCategoryId,
        orElse: () => forumProvider.categories.first);

      // تحميل الصور الجديدة
      final List<String> uploadedImageUrls = [];
      for (final image in _selectedImages) {
        final imageUrl = await forumProvider.uploadImage(image);
        if (imageUrl != null) {
          uploadedImageUrls.add(imageUrl);
        }
      }

      // دمج الصور الموجودة والجديدة
      final allImages = [..._existingImages, ...uploadedImageUrls];

      if (widget.topicId != null) {
        // تحديث الموضوع
        await forumProvider.updateTopic(
          widget.topicId!,
          _titleController.text.trim(),
          _content.trim(),
          _selectedCategoryId!,
          category.name,
          _topicType,
          _selectedTags,
          allImages,
          _isRelatedToEstate,
          _relatedEstateId,
          _isAdultContent);
      } else {
        // إنشاء موضوع جديد
        await forumProvider.createTopic(
          _titleController.text.trim(),
          _content.trim(),
          _selectedCategoryId!,
          category.name,
          _topicType,
          _selectedTags,
          allImages,
          _isRelatedToEstate,
          _relatedEstateId,
          _isAdultContent);
      }

      if (mounted) {
        // عرض إشعار فوري
        NotificationService.showInAppNotification(
          context,
          title: widget.topicId != null ? 'تم تحديث الموضوع' : 'تم إنشاء الموضوع',
          body: widget.topicId != null
              ? 'تم تحديث موضوعك بنجاح'
              : 'تم إنشاء موضوعك الجديد بنجاح',
          type: domain.NotificationType.forumUpdate,
          duration: const Duration(seconds: 3),
        );

        Navigator.pop(context, true);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: $e')));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// اختيار صورة
  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _selectedImages.add(File(pickedFile.path));
      });
    }
  }

  /// إزالة صورة
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// إزالة صورة موجودة
  void _removeExistingImage(int index) {
    setState(() {
      _existingImages.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.topicId != null ? 'تحرير الموضوع' : 'موضوع جديد'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveTopic,
            child: const Text('حفظ')),
        ]),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الموضوع
                  TextField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'عنوان الموضوع',
                      border: OutlineInputBorder()),
                    maxLength: 100),
                  const SizedBox(height: 16),

                  // فئة الموضوع
                  _buildCategorySelector(),
                  const SizedBox(height: 16),

                  // نوع الموضوع
                  _buildTopicTypeSelector(),
                  const SizedBox(height: 16),

                  // محتوى الموضوع
                  const Text(
                    'محتوى الموضوع',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 300,
                    child: RichTextEditor(
                      initialText: _content,
                      onTextChanged: (html, plainText) {
                        setState(() {
                          _content = html;
                        });
                      },
                      onImageAdded: (file) {
                        setState(() {
                          _selectedImages.add(file);
                        });
                      })),
                  const SizedBox(height: 16),

                  // الوسوم
                  _buildTagsSelector(),
                  const SizedBox(height: 16),

                  // الصور
                  _buildImagesSection(),
                  const SizedBox(height: 16),

                  // خيارات إضافية
                  _buildAdditionalOptions(),
                ])));
  }

  /// بناء محدد الفئة
  Widget _buildCategorySelector() {
    final forumProvider = Provider.of<ForumProvider>(context);
    final categories = forumProvider.categories;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'فئة الموضوع',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedCategoryId,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8)),
          hint: const Text('اختر فئة'),
          items: categories.map((category) {
            return DropdownMenuItem<String>(
              value: category.id,
              child: Text(category.name));
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategoryId = value;
            });
          }),
      ]);
  }

  /// بناء محدد نوع الموضوع
  Widget _buildTopicTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نوع الموضوع',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildTopicTypeChip(TopicType.normal, 'عادي', Icons.chat),
            _buildTopicTypeChip(TopicType.question, 'سؤال', Icons.help),
            _buildTopicTypeChip(TopicType.article, 'مقال', Icons.article),
            _buildTopicTypeChip(TopicType.poll, 'استطلاع', Icons.poll),
            _buildTopicTypeChip(TopicType.announcement, 'إعلان', Icons.campaign),
          ]),
      ]);
  }

  /// بناء شريحة نوع الموضوع
  Widget _buildTopicTypeChip(TopicType type, String label, IconData icon) {
    final isSelected = _topicType == type;

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: isSelected ? Colors.white : Colors.grey.shade700),
          const SizedBox(width: 4),
          Text(label),
        ]),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _topicType = type;
        });
      },
      backgroundColor: Colors.grey.shade200,
      selectedColor: AppColors.primary,
      checkmarkColor: Colors.white,
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.grey.shade700));
  }

  /// بناء محدد الوسوم
  Widget _buildTagsSelector() {
    final TextEditingController tagController = TextEditingController();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الوسوم',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: tagController,
                decoration: const InputDecoration(
                  labelText: 'أضف وسماً',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8)))),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                final tag = tagController.text.trim();
                if (tag.isNotEmpty && !_selectedTags.contains(tag)) {
                  setState(() {
                    _selectedTags.add(tag);
                    tagController.clear();
                  });
                }
              },
              child: const Text('إضافة')),
          ]),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _selectedTags.map((tag) {
            return Chip(
              label: Text(tag),
              deleteIcon: const Icon(Icons.close, size: 16),
              onDeleted: () {
                setState(() {
                  _selectedTags.remove(tag);
                });
              });
          }).toList()),
      ]);
  }

  /// بناء قسم الصور
  Widget _buildImagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'الصور',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            TextButton.icon(
              onPressed: _pickImage,
              icon: const Icon(Icons.add_photo_alternate),
              label: const Text('إضافة صورة')),
          ]),
        const SizedBox(height: 8),
        if (_existingImages.isNotEmpty) ...[
          const Text('الصور الحالية:'),
          const SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _existingImages.length,
              itemBuilder: (context, index) {
                return Stack(
                  children: [
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          image: NetworkImage(_existingImages[index]),
                          fit: BoxFit.cover))),
                    Positioned(
                      top: 4,
                      right: 12,
                      child: InkWell(
                        onTap: () => _removeExistingImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16)))),
                  ]);
              })),
          const SizedBox(height: 16),
        ],
        if (_selectedImages.isNotEmpty) ...[
          const Text('الصور الجديدة:'),
          const SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Stack(
                  children: [
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          image: FileImage(_selectedImages[index]),
                          fit: BoxFit.cover))),
                    Positioned(
                      top: 4,
                      right: 12,
                      child: InkWell(
                        onTap: () => _removeImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16)))),
                  ]);
              })),
        ],
      ]);
  }

  /// بناء الخيارات الإضافية
  Widget _buildAdditionalOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'خيارات إضافية',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        SwitchListTile(
          title: const Text('محتوى للبالغين'),
          subtitle: const Text('يحتوي على محتوى غير مناسب للأطفال'),
          value: _isAdultContent,
          onChanged: (value) {
            setState(() {
              _isAdultContent = value;
            });
          }),
        SwitchListTile(
          title: const Text('متعلق بعقار'),
          subtitle: const Text('هذا الموضوع متعلق بعقار محدد'),
          value: _isRelatedToEstate,
          onChanged: (value) {
            setState(() {
              _isRelatedToEstate = value;
            });
          }),
        if (_isRelatedToEstate) ...[
          const SizedBox(height: 8),
          // هنا يمكن إضافة محدد العقارات
        ],
      ]);
  }
}
