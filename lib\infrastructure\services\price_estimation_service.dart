import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/entities/price_estimation.dart';
import '../../domain/entities/estate.dart';
import '../../domain/repositories/analytics_repository.dart';
import '../../domain/repositories/estate_repository.dart';

/// خدمة تقدير الأسعار
class PriceEstimationService {
  final AnalyticsRepository _analyticsRepository;
  final EstateRepository _estateRepository;
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;

  /// إنشاء خدمة تقدير الأسعار
  PriceEstimationService(
    this._analyticsRepository,
    this._estateRepository, {
    FirebaseAuth? auth,
    FirebaseFirestore? firestore,
  })  : _auth = auth ?? FirebaseAuth.instance,
        _firestore = firestore ?? FirebaseFirestore.instance;

  /// تقدير سعر عقار
  Future<PriceEstimation> estimatePrice({
    String? userId,
    PriceEstimationType? type,
    String? estateId,
    required String area,
    required String propertyType,
    required double size,
    int? rooms,
    int? bathrooms,
    int? age,
    int? floor,
    bool? isFurnished,
    bool? isRenovated,
    List<String>? features,
  }) async {
    // الحصول على معرف المستخدم الحالي إذا لم يتم تحديده
    final currentUser = _auth.currentUser;
    final effectiveUserId = userId ?? currentUser?.uid;

    if (effectiveUserId == null) {
      throw Exception('يجب تسجيل الدخول لتقدير السعر');
    }

    // تحديد نوع التقدير إذا لم يتم تحديده
    final effectiveType = type ??
        (estateId != null
            ? PriceEstimationType.sale
            : PriceEstimationType.sale);

    // الحصول على بيانات العقار إذا تم تحديد معرف
    if (estateId != null) {
      final estateBase = await _estateRepository.getEstateById(estateId);
      if (estateBase == null) {
        throw Exception('العقار غير موجود');
      }
      // We don't actually use the estate object here, so we can skip the conversion
    }

    // استخدام نموذج الذكاء الاصطناعي لتقدير السعر
    final aiEstimation = await _getAIPriceEstimation(
      area: area,
      propertyType: propertyType,
      size: size,
      rooms: rooms,
      bathrooms: bathrooms,
      age: age,
      floor: floor,
      isFurnished: isFurnished,
      isRenovated: isRenovated,
      features: features);

    // الحصول على إحصائيات المنطقة
    final areaStats = await _getAreaPriceStatistics(area, propertyType);

    // الحصول على العقارات المشابهة
    final similarEstates = await _findSimilarEstates(
      area: area,
      propertyType: propertyType,
      size: size,
      rooms: rooms,
      bathrooms: bathrooms);

    // حساب درجة الثقة في التقدير
    final confidence = _calculateConfidence(
      area: area,
      propertyType: propertyType,
      similarEstatesCount: similarEstates.length,
      aiConfidence: aiEstimation['confidence'] as double);

    // إنشاء كائن تقدير السعر
    final estimation = PriceEstimation(
      id: '',
      userId: effectiveUserId,
      estateId: estateId,
      type: effectiveType,
      area: area,
      propertyType: propertyType,
      size: size,
      rooms: rooms,
      bathrooms: bathrooms,
      age: age,
      floor: floor,
      isFurnished: isFurnished,
      isRenovated: isRenovated,
      features: features,
      estimatedValue:
          aiEstimation['estimatedPrice'] as double, // Required parameter
      minValue: aiEstimation['minPrice'] as double, // Required parameter
      maxValue: aiEstimation['maxPrice'] as double, // Required parameter
      accuracy: EstimationAccuracy.medium, // Required parameter
      factors: {}, // Required parameter
      estimatedAt: DateTime.now(), // Required parameter
      expiresAt: DateTime.now().add(Duration(days: 90)), // Required parameter
      metadata: {
        'aiModel': 'RealEstateAI v2.0',
        'areaStatistics': areaStats,
        'similarEstatesIds': similarEstates.map((e) => e.id).toList(),
      },
      // Optional parameters for compatibility
      estimatedPrice: aiEstimation['estimatedPrice'] as double,
      minPrice: aiEstimation['minPrice'] as double,
      maxPrice: aiEstimation['maxPrice'] as double,
      confidence: confidence,
      similarEstatesCount: similarEstates.length);

    // حفظ التقدير في قاعدة البيانات
    // TODO: Implement saveEstimation in AnalyticsRepository
    // For now, just return the estimation
    return estimation;
  }

  /// الحصول على تقدير السعر باستخدام الذكاء الاصطناعي
  Future<Map<String, dynamic>> _getAIPriceEstimation({
    required String area,
    required String propertyType,
    required double size,
    int? rooms,
    int? bathrooms,
    int? age,
    int? floor,
    bool? isFurnished,
    bool? isRenovated,
    List<String>? features,
  }) async {
    // في التطبيق الحقيقي، سيتم استخدام نموذج تعلم آلي لتقدير السعر
    // هنا نقوم بمحاكاة النتائج

    // الحصول على متوسط أسعار المنطقة
    final areaStats = await _getAreaPriceStatistics(area, propertyType);
    final avgPricePerSqm = areaStats['avgPricePerSqm'] as double;

    // حساب السعر الأساسي
    double basePrice = avgPricePerSqm * size;

    // تعديل السعر بناءً على عدد الغرف
    if (rooms != null) {
      basePrice *= (1 + (rooms - 2) * 0.05);
    }

    // تعديل السعر بناءً على عدد الحمامات
    if (bathrooms != null) {
      basePrice *= (1 + (bathrooms - 1) * 0.03);
    }

    // تعديل السعر بناءً على عمر العقار
    if (age != null) {
      basePrice *= (1 - min(age, 20) * 0.01);
    }

    // تعديل السعر بناءً على الطابق
    if (floor != null && floor > 0) {
      basePrice *= (1 + min(floor, 10) * 0.005);
    }

    // تعديل السعر بناءً على التأثيث
    if (isFurnished == true) {
      basePrice *= 1.1;
    }

    // تعديل السعر بناءً على التجديد
    if (isRenovated == true) {
      basePrice *= 1.05;
    }

    // تعديل السعر بناءً على الميزات
    if (features != null && features.isNotEmpty) {
      basePrice *= (1 + features.length * 0.01);
    }

    // إضافة عامل عشوائي للتنوع
    final random = Random();
    final randomFactor = 0.9 + random.nextDouble() * 0.2;
    basePrice *= randomFactor;

    // حساب نطاق السعر
    final minPrice = basePrice * 0.9;
    final maxPrice = basePrice * 1.1;

    // توليد عوامل تأثير السعر
    final factors = _generatePriceFactors(
      area: area,
      propertyType: propertyType,
      size: size,
      rooms: rooms,
      bathrooms: bathrooms,
      age: age,
      floor: floor,
      isFurnished: isFurnished,
      isRenovated: isRenovated,
      features: features);

    return {
      'estimatedPrice': basePrice,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'confidence': 0.85, // درجة ثقة افتراضية
      'factors': factors,
    };
  }

  /// الحصول على تقدير السعر بواسطة المعرف
  Future<PriceEstimation?> getPriceEstimationById(String estimationId) {
    return _analyticsRepository.getPriceEstimationById(estimationId);
  }

  /// الحصول على تقديرات الأسعار للمستخدم
  Future<List<PriceEstimation>> getUserPriceEstimations(String userId) {
    return _analyticsRepository.getUserPriceEstimations(userId);
  }

  /// الحصول على تقديرات الأسعار للمستخدم بالتحميل المتدرج
  Future<Map<String, dynamic>> getUserPriceEstimationsPaginated({
    required String userId,
    int limit = 20,
    String? lastEstimationId,
  }) {
    return _analyticsRepository.getUserPriceEstimationsPaginated(
      userId: userId,
      limit: limit,
      lastEstimationId: lastEstimationId);
  }

  /// تقدير سعر عقار موجود
  Future<PriceEstimation> estimateExistingEstatePrice({
    String? userId,
    required String estateId,
  }) async {
    final estateBase = await _estateRepository.getEstateById(estateId);

    if (estateBase == null) {
      throw Exception('العقار غير موجود');
    }

    // We don't actually use the estate object here, so we can skip the conversion

    // استخراج بيانات العقار من الكائن
    // في التطبيق الحقيقي، يجب التأكد من وجود هذه الخصائص في كائن العقار
    return estimatePrice(
      userId: userId,
      type: PriceEstimationType.sale,
      estateId: estateId,
      area: 'الرياض', // قيمة افتراضية، يجب استبدالها بالقيمة الفعلية من العقار
      propertyType:
          'شقة', // قيمة افتراضية، يجب استبدالها بالقيمة الفعلية من العقار
      size: 100.0, // قيمة افتراضية، يجب استبدالها بالقيمة الفعلية من العقار
      rooms: 2, // قيمة افتراضية، يجب استبدالها بالقيمة الفعلية من العقار
      bathrooms: 1, // قيمة افتراضية، يجب استبدالها بالقيمة الفعلية من العقار
      age: 5, // قيمة افتراضية، يجب استبدالها بالقيمة الفعلية من العقار
      floor: 1, // قيمة افتراضية، يجب استبدالها بالقيمة الفعلية من العقار
      isFurnished:
          false, // قيمة افتراضية، يجب استبدالها بالقيمة الفعلية من العقار
      isRenovated:
          false, // قيمة افتراضية، يجب استبدالها بالقيمة الفعلية من العقار
      features: [
        'مطبخ',
        'تكييف'
      ], // قيمة افتراضية، يجب استبدالها بالقيمة الفعلية من العقار
    );
  }

  /// البحث عن عقارات مشابهة
  Future<List<Estate>> _findSimilarEstates({
    required String area,
    required String propertyType,
    required double size,
    int? rooms,
    int? bathrooms,
  }) async {
    // في التطبيق الحقيقي، سيتم البحث في قاعدة البيانات
    // هنا نقوم بمحاكاة النتائج

    final random = Random();
    final similarCount = random.nextInt(10) + 1;
    final similarEstates = <Estate>[];

    // محاكاة العقارات المشابهة
    for (int i = 0; i < similarCount; i++) {
      final similarSize = size * (0.9 + random.nextDouble() * 0.2);
      // Calculate similar rooms and bathrooms for future use
      // final similarRooms =
      //     rooms != null ? max(1, rooms + (random.nextInt(3) - 1)) : null;
      // final similarBathrooms = bathrooms != null
      //     ? max(1, bathrooms + (random.nextInt(3) - 1))
      //     : null;
      final similarPrice = (3000 + random.nextDouble() * 2000) * similarSize;

      // في التطبيق الحقيقي، يجب التأكد من توافق هذه البيانات مع بنية كائن العقار
      // هنا نستخدم بيانات افتراضية للمحاكاة
      // ملاحظة: قد تختلف بنية كائن العقار في التطبيق الفعلي
      final estate = Estate(
        id: 'similar_${i + 1}',
        title: 'عقار مشابه ${i + 1}',
        description: 'عقار مشابه للتقدير',
        ownerId: 'system',
        price: similarPrice,
        photoUrls: [
          'https://example.com/photo1.jpg',
          'https://example.com/photo2.jpg'
        ],
        isFeatured: false,
        planType: 'free',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(Duration(days: 30)),
        location: area,
        createdAt: DateTime.now().subtract(Duration(days: random.nextInt(90))));

      similarEstates.add(estate);
    }

    return similarEstates;
  }

  /// الحصول على إحصائيات أسعار المنطقة
  Future<Map<String, dynamic>> _getAreaPriceStatistics(
      String area, String propertyType) async {
    // في التطبيق الحقيقي، سيتم استخدام بيانات حقيقية
    // هنا نقوم بمحاكاة النتائج

    final random = Random();

    // قائمة بمتوسط الأسعار لكل متر مربع في المناطق المختلفة
    final areaPrices = {
      'الرياض': 3000 + random.nextDouble() * 2000,
      'جدة': 2800 + random.nextDouble() * 1800,
      'الدمام': 2500 + random.nextDouble() * 1500,
      'مكة': 3200 + random.nextDouble() * 2200,
      'المدينة': 2700 + random.nextDouble() * 1700,
      'الخبر': 2900 + random.nextDouble() * 1900,
      'الظهران': 3100 + random.nextDouble() * 2100,
      'أبها': 2400 + random.nextDouble() * 1400,
      'الطائف': 2300 + random.nextDouble() * 1300,
      'تبوك': 2200 + random.nextDouble() * 1200,
    };

    // قائمة بمعاملات التعديل لأنواع العقارات المختلفة
    final propertyTypeFactors = {
      'شقة': 0.9,
      'منزل': 1.2,
      'أرض': 0.8,
      'عمارة': 1.5,
      'مكتب': 1.1,
      'محل تجاري': 1.3,
      'مخزن': 0.7,
    };

    // الحصول على متوسط سعر المتر المربع للمنطقة
    final areaPrice = areaPrices[area] ?? 3000.0;

    // تعديل السعر بناءً على نوع العقار
    final typeFactor = propertyTypeFactors[propertyType] ?? 1.0;
    final avgPricePerSqm = areaPrice * typeFactor;

    // إحصائيات إضافية
    final minPricePerSqm = avgPricePerSqm * 0.8;
    final maxPricePerSqm = avgPricePerSqm * 1.2;
    final priceGrowth = -0.05 + random.nextDouble() * 0.1;
    final transactionsCount = 50 + random.nextInt(200);
    final avgDaysOnMarket = 30 + random.nextInt(60);

    return {
      'avgPricePerSqm': avgPricePerSqm,
      'minPricePerSqm': minPricePerSqm,
      'maxPricePerSqm': maxPricePerSqm,
      'priceGrowth': priceGrowth,
      'transactionsCount': transactionsCount,
      'avgDaysOnMarket': avgDaysOnMarket,
    };
  }

  /// حساب درجة الثقة في التقدير
  double _calculateConfidence({
    required String area,
    required String propertyType,
    required int similarEstatesCount,
    required double aiConfidence,
  }) {
    // في التطبيق الحقيقي، سيتم حساب درجة الثقة بناءً على عوامل متعددة
    // هنا نقوم بمحاكاة النتائج

    // قائمة بدرجات الثقة للمناطق المختلفة
    final areaConfidence = {
      'الرياض': 0.9,
      'جدة': 0.85,
      'الدمام': 0.8,
      'مكة': 0.85,
      'المدينة': 0.8,
      'الخبر': 0.85,
      'الظهران': 0.85,
      'أبها': 0.75,
      'الطائف': 0.75,
      'تبوك': 0.7,
    };

    // قائمة بدرجات الثقة لأنواع العقارات المختلفة
    final propertyTypeConfidence = {
      'شقة': 0.9,
      'فيلا': 0.85,
      'أرض': 0.75,
      'عمارة': 0.8,
      'مكتب': 0.8,
      'محل تجاري': 0.75,
      'مستودع': 0.7,
    };

    // الحصول على درجة الثقة للمنطقة ونوع العقار
    final areaFactor = areaConfidence[area] ?? 0.7;
    final typeFactor = propertyTypeConfidence[propertyType] ?? 0.7;

    // تعديل درجة الثقة بناءً على عدد العقارات المشابهة
    final similarFactor = min(1.0, similarEstatesCount / 10.0);

    // حساب درجة الثقة النهائية
    return (areaFactor * 0.3 +
        typeFactor * 0.2 +
        similarFactor * 0.2 +
        aiConfidence * 0.3);
  }

  /// توليد عوامل تأثير السعر
  List<Map<String, dynamic>> _generatePriceFactors({
    required String area,
    required String propertyType,
    required double size,
    int? rooms,
    int? bathrooms,
    int? age,
    int? floor,
    bool? isFurnished,
    bool? isRenovated,
    List<String>? features,
  }) {
    final factors = <Map<String, dynamic>>[];

    // عامل المنطقة
    factors.add({
      'name': 'المنطقة',
      'value': area,
      'impact': 0.3,
      'description': 'تؤثر المنطقة بشكل كبير على سعر العقار',
    });

    // عامل نوع العقار
    factors.add({
      'name': 'نوع العقار',
      'value': propertyType,
      'impact': 0.2,
      'description': 'يختلف السعر باختلاف نوع العقار',
    });

    // عامل المساحة
    factors.add({
      'name': 'المساحة',
      'value': size,
      'impact': 0.25,
      'description': 'المساحة هي العامل الأساسي في تحديد السعر',
    });

    // عامل عدد الغرف
    if (rooms != null) {
      factors.add({
        'name': 'عدد الغرف',
        'value': rooms,
        'impact': 0.1,
        'description': 'يزيد السعر بزيادة عدد الغرف',
      });
    }

    // عامل عدد الحمامات
    if (bathrooms != null) {
      factors.add({
        'name': 'عدد الحمامات',
        'value': bathrooms,
        'impact': 0.05,
        'description': 'يزيد السعر بزيادة عدد الحمامات',
      });
    }

    // عامل عمر العقار
    if (age != null) {
      factors.add({
        'name': 'عمر العقار',
        'value': age,
        'impact': -0.1,
        'description': 'ينخفض السعر بزيادة عمر العقار',
      });
    }

    // عامل الطابق
    if (floor != null) {
      factors.add({
        'name': 'الطابق',
        'value': floor,
        'impact': 0.03,
        'description': 'يزيد السعر قليلاً بارتفاع الطابق',
      });
    }

    // عامل التأثيث
    if (isFurnished == true) {
      factors.add({
        'name': 'التأثيث',
        'value': 'مؤثث',
        'impact': 0.1,
        'description': 'العقارات المؤثثة أعلى سعراً',
      });
    }

    // عامل التجديد
    if (isRenovated == true) {
      factors.add({
        'name': 'التجديد',
        'value': 'مجدد',
        'impact': 0.05,
        'description': 'العقارات المجددة أعلى سعراً',
      });
    }

    // عامل الميزات
    if (features != null && features.isNotEmpty) {
      factors.add({
        'name': 'الميزات الإضافية',
        'value': features.join(', '),
        'impact': 0.05,
        'description': 'الميزات الإضافية ترفع قيمة العقار',
      });
    }

    return factors;
  }

  /// تقدير العائد الاستثماري لعقار
  Future<double> estimateInvestmentReturn({
    required String area,
    required String propertyType,
    required double purchasePrice,
    required double monthlyRent,
    required double annualExpenses,
    int holdingPeriod = 5, // فترة الاحتفاظ بالعقار بالسنوات
  }) async {
    // حساب العائد الاستثماري السنوي
    final annualRent = monthlyRent * 12;
    final annualNetIncome = annualRent - annualExpenses;
    // Calculate annual ROI for future use
    // final annualROI = (annualNetIncome / purchasePrice) * 100;

    // الحصول على معدل النمو المتوقع للمنطقة ونوع العقار
    final marketData = await _getMarketGrowthRate(area, propertyType);
    final annualGrowthRate = marketData['annualGrowthRate'] as double;

    // حساب القيمة المتوقعة للعقار بعد فترة الاحتفاظ
    final expectedValue =
        purchasePrice * Math.pow(1 + (annualGrowthRate / 100), holdingPeriod);

    // حساب الربح الرأسمالي
    final capitalGain = expectedValue - purchasePrice;

    // حساب إجمالي الدخل من الإيجار خلال فترة الاحتفاظ
    final totalRentalIncome = annualNetIncome * holdingPeriod;

    // حساب إجمالي العائد
    final totalReturn = (capitalGain + totalRentalIncome) / purchasePrice * 100;

    // حساب متوسط العائد السنوي
    final averageAnnualReturn = totalReturn / holdingPeriod;

    return averageAnnualReturn;
  }

  /// الحصول على معدل النمو المتوقع للمنطقة ونوع العقار
  Future<Map<String, dynamic>> _getMarketGrowthRate(
    String area,
    String propertyType) async {
    // في التطبيق الحقيقي، هذه البيانات ستأتي من تحليلات السوق
    // هنا نستخدم بيانات افتراضية للتوضيح

    final snapshot = await FirebaseFirestore.instance
        .collection('marketData')
        .where('area', isEqualTo: area)
        .where('propertyType', isEqualTo: propertyType)
        .limit(1)
        .get();

    if (snapshot.docs.isNotEmpty) {
      return snapshot.docs.first.data();
    }

    // قيم افتراضية إذا لم تتوفر بيانات
    return {
      'annualGrowthRate': 5.0, // معدل نمو سنوي افتراضي 5%
      'rentalYield': 7.0, // عائد إيجار افتراضي 7%
      'vacancyRate': 5.0, // معدل الشواغر الافتراضي 5%
      'averageHoldingPeriod': 7, // متوسط فترة الاحتفاظ بالعقار 7 سنوات
    };
  }

  /// تقدير تكلفة البناء
  Future<Map<String, dynamic>> estimateConstructionCost({
    required String area,
    required String propertyType,
    required double size,
    required String constructionQuality, // منخفضة، متوسطة، عالية، فاخرة
  }) async {
    // الحصول على تكلفة البناء لكل متر مربع حسب المنطقة ونوع العقار وجودة البناء
    final costData =
        await _getConstructionCostData(area, propertyType, constructionQuality);

    final baseCostPerSqm = costData['baseCostPerSqm'] as double;
    final additionalCosts = costData['additionalCosts'] as Map<String, dynamic>;

    // حساب التكلفة الأساسية
    final baseCost = baseCostPerSqm * size;

    // حساب التكاليف الإضافية
    double totalAdditionalCost = 0;
    additionalCosts.forEach((key, value) {
      totalAdditionalCost += (value as double);
    });

    // إجمالي التكلفة
    final totalCost = baseCost + totalAdditionalCost;

    // تفاصيل التكلفة
    final costBreakdown = <String, dynamic>{
      'baseCost': baseCost,
      'additionalCosts': additionalCosts,
      'totalCost': totalCost,
      'costPerSqm': totalCost / size,
    };

    return {
      'estimatedCost': totalCost,
      'minCost': totalCost * 0.9, // تقدير الحد الأدنى للتكلفة
      'maxCost': totalCost * 1.2, // تقدير الحد الأقصى للتكلفة
      'costBreakdown': costBreakdown,
      'constructionTimeEstimate': _estimateConstructionTime(size, propertyType),
    };
  }

  /// الحصول على بيانات تكلفة البناء
  Future<Map<String, dynamic>> _getConstructionCostData(
    String area,
    String propertyType,
    String constructionQuality) async {
    // في التطبيق الحقيقي، هذه البيانات ستأتي من قاعدة البيانات
    // هنا نستخدم بيانات افتراضية للتوضيح

    final snapshot = await FirebaseFirestore.instance
        .collection('constructionCosts')
        .where('area', isEqualTo: area)
        .where('propertyType', isEqualTo: propertyType)
        .where('quality', isEqualTo: constructionQuality)
        .limit(1)
        .get();

    if (snapshot.docs.isNotEmpty) {
      return snapshot.docs.first.data();
    }

    // قيم افتراضية إذا لم تتوفر بيانات
    double baseCostPerSqm;
    switch (constructionQuality) {
      case 'منخفضة':
        baseCostPerSqm = 1500;
        break;
      case 'متوسطة':
        baseCostPerSqm = 2000;
        break;
      case 'عالية':
        baseCostPerSqm = 3000;
        break;
      case 'فاخرة':
        baseCostPerSqm = 5000;
        break;
      default:
        baseCostPerSqm = 2000;
    }

    return {
      'baseCostPerSqm': baseCostPerSqm,
      'additionalCosts': {
        'تصميم': baseCostPerSqm * 0.05,
        'تراخيص': baseCostPerSqm * 0.03,
        'إشراف': baseCostPerSqm * 0.07,
        'مرافق': baseCostPerSqm * 0.1,
        'تشطيبات': baseCostPerSqm * 0.2,
      },
    };
  }

  /// تقدير وقت البناء
  Map<String, dynamic> _estimateConstructionTime(
      double size, String propertyType) {
    // تقدير وقت البناء بالأشهر
    int baseTime;

    switch (propertyType) {
      case 'فيلا':
        baseTime = 12;
        break;
      case 'شقة':
        baseTime = 6;
        break;
      case 'مبنى تجاري':
        baseTime = 18;
        break;
      default:
        baseTime = 12;
    }

    // تعديل الوقت بناءً على الحجم
    final sizeMultiplier =
        size / 300; // افتراض أن 300 متر مربع هو الحجم المتوسط
    final estimatedMonths = (baseTime * sizeMultiplier).round();

    return {
      'estimatedMonths': estimatedMonths,
      'minMonths': (estimatedMonths * 0.8).round(),
      'maxMonths': (estimatedMonths * 1.5).round(),
    };
  }

  /// تقدير تكلفة الصيانة
  Future<Map<String, dynamic>> estimateMaintenanceCost({
    required String propertyType,
    required double size,
    required int age,
    required String condition, // ممتازة، جيدة، متوسطة، سيئة
  }) async {
    // الحصول على تكلفة الصيانة لكل متر مربع حسب نوع العقار وعمره وحالته
    final costData =
        await _getMaintenanceCostData(propertyType, age, condition);

    final baseCostPerSqm = costData['baseCostPerSqm'] as double;
    final maintenanceItems =
        costData['maintenanceItems'] as Map<String, dynamic>;

    // حساب التكلفة الأساسية
    final baseCost = baseCostPerSqm * size;

    // تفاصيل تكلفة الصيانة
    final costBreakdown = <String, double>{};
    maintenanceItems.forEach((key, value) {
      costBreakdown[key] = (value as double) * size;
    });

    // إجمالي التكلفة
    final totalCost = costBreakdown.values.reduce((a, b) => a + b);

    return {
      'estimatedCost': totalCost,
      'minCost': totalCost * 0.8, // تقدير الحد الأدنى للتكلفة
      'maxCost': totalCost * 1.3, // تقدير الحد الأقصى للتكلفة
      'costBreakdown': costBreakdown,
      'annualMaintenanceCost': totalCost * 0.1, // تقدير تكلفة الصيانة السنوية
      'recommendedMaintenanceSchedule':
          _getRecommendedMaintenanceSchedule(age, condition),
    };
  }

  /// الحصول على بيانات تكلفة الصيانة
  Future<Map<String, dynamic>> _getMaintenanceCostData(
    String propertyType,
    int age,
    String condition) async {
    // في التطبيق الحقيقي، هذه البيانات ستأتي من قاعدة البيانات
    // هنا نستخدم بيانات افتراضية للتوضيح

    final snapshot = await FirebaseFirestore.instance
        .collection('maintenanceCosts')
        .where('propertyType', isEqualTo: propertyType)
        .where('ageRange', isEqualTo: _getAgeRange(age))
        .where('condition', isEqualTo: condition)
        .limit(1)
        .get();

    if (snapshot.docs.isNotEmpty) {
      return snapshot.docs.first.data();
    }

    // قيم افتراضية إذا لم تتوفر بيانات
    double baseCostPerSqm;

    // تحديد التكلفة الأساسية بناءً على عمر العقار وحالته
    if (age < 5) {
      switch (condition) {
        case 'ممتازة':
          baseCostPerSqm = 50;
          break;
        case 'جيدة':
          baseCostPerSqm = 100;
          break;
        case 'متوسطة':
          baseCostPerSqm = 200;
          break;
        case 'سيئة':
          baseCostPerSqm = 400;
          break;
        default:
          baseCostPerSqm = 150;
      }
    } else if (age < 15) {
      switch (condition) {
        case 'ممتازة':
          baseCostPerSqm = 150;
          break;
        case 'جيدة':
          baseCostPerSqm = 250;
          break;
        case 'متوسطة':
          baseCostPerSqm = 400;
          break;
        case 'سيئة':
          baseCostPerSqm = 600;
          break;
        default:
          baseCostPerSqm = 300;
      }
    } else {
      switch (condition) {
        case 'ممتازة':
          baseCostPerSqm = 300;
          break;
        case 'جيدة':
          baseCostPerSqm = 450;
          break;
        case 'متوسطة':
          baseCostPerSqm = 600;
          break;
        case 'سيئة':
          baseCostPerSqm = 900;
          break;
        default:
          baseCostPerSqm = 500;
      }
    }

    // تحديد بنود الصيانة ونسبتها من التكلفة الإجمالية
    final maintenanceItems = <String, double>{};

    if (propertyType == 'فيلا' || propertyType == 'شقة') {
      maintenanceItems['سباكة'] = baseCostPerSqm * 0.15;
      maintenanceItems['كهرباء'] = baseCostPerSqm * 0.2;
      maintenanceItems['دهانات'] = baseCostPerSqm * 0.25;
      maintenanceItems['أرضيات'] = baseCostPerSqm * 0.15;
      maintenanceItems['نوافذ وأبواب'] = baseCostPerSqm * 0.1;
      maintenanceItems['مطبخ وحمامات'] = baseCostPerSqm * 0.15;
    } else {
      maintenanceItems['سباكة'] = baseCostPerSqm * 0.15;
      maintenanceItems['كهرباء'] = baseCostPerSqm * 0.2;
      maintenanceItems['دهانات'] = baseCostPerSqm * 0.2;
      maintenanceItems['أرضيات'] = baseCostPerSqm * 0.15;
      maintenanceItems['نوافذ وأبواب'] = baseCostPerSqm * 0.1;
      maintenanceItems['واجهات'] = baseCostPerSqm * 0.2;
    }

    return {
      'baseCostPerSqm': baseCostPerSqm,
      'maintenanceItems': maintenanceItems,
    };
  }

  /// الحصول على نطاق العمر
  String _getAgeRange(int age) {
    if (age < 5) {
      return '0-5';
    } else if (age < 10) {
      return '5-10';
    } else if (age < 15) {
      return '10-15';
    } else if (age < 20) {
      return '15-20';
    } else {
      return '20+';
    }
  }

  /// الحصول على جدول الصيانة الموصى به
  Map<String, String> _getRecommendedMaintenanceSchedule(
      int age, String condition) {
    final schedule = <String, String>{};

    if (age < 5) {
      schedule['دهانات'] = 'كل 3-5 سنوات';
      schedule['سباكة'] = 'فحص سنوي';
      schedule['كهرباء'] = 'فحص سنوي';
      schedule['تكييف'] = 'صيانة كل 6 أشهر';
      schedule['مطبخ'] = 'صيانة كل 2-3 سنوات';
    } else if (age < 15) {
      schedule['دهانات'] = 'كل 2-3 سنوات';
      schedule['سباكة'] = 'فحص كل 6 أشهر';
      schedule['كهرباء'] = 'فحص كل 6 أشهر';
      schedule['تكييف'] = 'صيانة كل 4 أشهر';
      schedule['مطبخ'] = 'صيانة سنوية';
      schedule['أرضيات'] = 'فحص كل 2-3 سنوات';
    } else {
      schedule['دهانات'] = 'كل 1-2 سنوات';
      schedule['سباكة'] = 'فحص كل 3 أشهر';
      schedule['كهرباء'] = 'فحص كل 3 أشهر';
      schedule['تكييف'] = 'صيانة كل 3 أشهر';
      schedule['مطبخ'] = 'صيانة كل 6 أشهر';
      schedule['أرضيات'] = 'فحص سنوي';
      schedule['نوافذ وأبواب'] = 'فحص سنوي';
    }

    // تعديل الجدول بناءً على حالة العقار
    if (condition == 'سيئة') {
      schedule['تقييم شامل'] = 'فوراً';
      schedule['إعادة تأهيل'] = 'موصى بها';
    }

    return schedule;
  }
}

/// فئة للعمليات الرياضية
class Math {
  /// حساب القوة
  static double pow(double x, int y) {
    double result = 1.0;
    for (int i = 0; i < y; i++) {
      result *= x;
    }
    return result;
  }
}
