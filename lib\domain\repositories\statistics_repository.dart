import '../entities/user_statistics.dart';

/// واجهة مستودع الإحصائيات
abstract class StatisticsRepository {
  /// الحصول على إحصائيات المستخدم
  Future<UserStatistics> getUserStatistics(String userId);
  
  /// الحصول على إحصائيات المستخدم لفترة زمنية محددة
  Future<UserStatistics> getUserStatisticsForPeriod(
    String userId, 
    DateTime startDate, 
    DateTime endDate);
  
  /// الحصول على إحصائيات الإعلانات
  Future<Map<String, dynamic>> getAdsStatistics(String userId);
  
  /// الحصول على إحصائيات المشاهدات
  Future<Map<String, dynamic>> getViewsStatistics(String userId);
  
  /// الحصول على إحصائيات الاتصالات
  Future<Map<String, dynamic>> getContactsStatistics(String userId);
  
  /// الحصول على إحصائيات المبيعات/الإيجارات
  Future<Map<String, dynamic>> getSalesRentalsStatistics(String userId);
  
  /// الحصول على إحصائيات العملاء
  Future<Map<String, dynamic>> getClientsStatistics(String userId);
  
  /// الحصول على إحصائيات المواعيد
  Future<Map<String, dynamic>> getAppointmentsStatistics(String userId);
  
  /// الحصول على إحصائيات العروض
  Future<Map<String, dynamic>> getOffersStatistics(String userId);
  
  /// الحصول على إحصائيات التقييمات
  Future<Map<String, dynamic>> getReviewsStatistics(String userId);
  
  /// الحصول على إحصائيات حسب نوع العقار
  Future<Map<String, int>> getStatsByPropertyType(String userId);
  
  /// الحصول على إحصائيات حسب المنطقة
  Future<Map<String, int>> getStatsByLocation(String userId);
  
  /// الحصول على إحصائيات حسب الشهر
  Future<Map<String, dynamic>> getStatsByMonth(String userId, int year);
  
  /// الحصول على تقرير أداء المستخدم
  Future<Map<String, dynamic>> getUserPerformanceReport(String userId);
  
  /// الحصول على تقرير أداء الفريق (لشركات العقارات)
  Future<Map<String, dynamic>> getTeamPerformanceReport(String companyId);
  
  /// الحصول على تقرير أداء الحملات التسويقية (لشركات العقارات)
  Future<Map<String, dynamic>> getMarketingCampaignsReport(String companyId);
  
  /// الحصول على تقرير تحليل السوق
  Future<Map<String, dynamic>> getMarketAnalysisReport(String location);
  
  /// الحصول على تقرير تقييم العقار
  Future<Map<String, dynamic>> getPropertyValuationReport(
    String propertyType,
    String location,
    double area);
}
