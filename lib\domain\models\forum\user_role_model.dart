import 'package:flutter/material.dart';

/// نموذج دور المستخدم في المنتدى
class UserRoleModel {
  /// معرف الدور
  final String id;

  /// اسم الدور
  final String name;

  /// وصف الدور
  final String description;

  /// مستوى الصلاحيات (أعلى رقم = صلاحيات أكثر)
  final int permissionLevel;

  /// لون الدور
  final Color color;

  /// أيقونة الدور
  final IconData icon;

  /// صلاحيات الدور
  final UserRolePermissions permissions;

  /// Constructor
  const UserRoleModel({
    required this.id,
    required this.name,
    required this.description,
    required this.permissionLevel,
    required this.color,
    required this.icon,
    required this.permissions,
  });

  /// الحصول على دور بواسطة المعرف
  static UserRoleModel? getRoleById(String id) {
    try {
      return roles.firstWhere((role) => role.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على دور بواسطة مستوى الصلاحيات
  static UserRoleModel getRoleByPermissionLevel(int level) {
    for (int i = roles.length - 1; i >= 0; i--) {
      if (level >= roles[i].permissionLevel) {
        return roles[i];
      }
    }
    return roles.first; // دور المستخدم العادي
  }

  /// قائمة الأدوار
  static const List<UserRoleModel> roles = [
    // مستخدم عادي
    UserRoleModel(
      id: 'user',
      name: 'مستخدم',
      description: 'مستخدم عادي في المنتدى',
      permissionLevel: 0,
      color: Colors.grey,
      icon: Icons.person,
      permissions: UserRolePermissions(
        canCreateTopics: true,
        canReplyToTopics: true,
        canEditOwnContent: true,
        canDeleteOwnContent: true,
        canLikeContent: true,
        canReportContent: true,
        canFollowUsers: true,
        canFollowTopics: true,
        canParticipateInPolls: true,
        canViewStatistics: true)),

    // مستخدم مميز
    UserRoleModel(
      id: 'premium_user',
      name: 'مستخدم مميز',
      description: 'مستخدم مميز في المنتدى',
      permissionLevel: 10,
      color: Colors.green, // تغيير من الأزرق إلى الأخضر
      icon: Icons.verified_user,
      permissions: UserRolePermissions(
        canCreateTopics: true,
        canReplyToTopics: true,
        canEditOwnContent: true,
        canDeleteOwnContent: true,
        canLikeContent: true,
        canReportContent: true,
        canFollowUsers: true,
        canFollowTopics: true,
        canParticipateInPolls: true,
        canViewStatistics: true,
        canCreatePolls: true,
        canUploadFiles: true,
        canCreateFeaturedTopics: true,
        canCustomizeProfile: true)),

    // مساهم
    UserRoleModel(
      id: 'contributor',
      name: 'مساهم',
      description: 'مساهم نشط في المنتدى',
      permissionLevel: 20,
      color: Colors.green,
      icon: Icons.star,
      permissions: UserRolePermissions(
        canCreateTopics: true,
        canReplyToTopics: true,
        canEditOwnContent: true,
        canDeleteOwnContent: true,
        canLikeContent: true,
        canReportContent: true,
        canFollowUsers: true,
        canFollowTopics: true,
        canParticipateInPolls: true,
        canViewStatistics: true,
        canCreatePolls: true,
        canUploadFiles: true,
        canCreateFeaturedTopics: true,
        canCustomizeProfile: true,
        canPinOwnTopics: true,
        canCreateAnnouncements: true,
        canSeeReportedContent: true)),

    // مشرف
    UserRoleModel(
      id: 'moderator',
      name: 'مشرف',
      description: 'مشرف في المنتدى',
      permissionLevel: 50,
      color: Colors.orange,
      icon: Icons.shield,
      permissions: UserRolePermissions(
        canCreateTopics: true,
        canReplyToTopics: true,
        canEditOwnContent: true,
        canDeleteOwnContent: true,
        canLikeContent: true,
        canReportContent: true,
        canFollowUsers: true,
        canFollowTopics: true,
        canParticipateInPolls: true,
        canViewStatistics: true,
        canCreatePolls: true,
        canUploadFiles: true,
        canCreateFeaturedTopics: true,
        canCustomizeProfile: true,
        canPinOwnTopics: true,
        canCreateAnnouncements: true,
        canSeeReportedContent: true,
        canEditOtherContent: true,
        canDeleteOtherContent: true,
        canPinAnyTopic: true,
        canBanUsers: true,
        canApproveContent: true,
        canManageCategories: false,
        canManageRoles: false,
        canAccessAdminPanel: false)),

    // مدير
    UserRoleModel(
      id: 'admin',
      name: 'مدير',
      description: 'مدير المنتدى',
      permissionLevel: 90,
      color: Colors.red,
      icon: Icons.admin_panel_settings,
      permissions: UserRolePermissions(
        canCreateTopics: true,
        canReplyToTopics: true,
        canEditOwnContent: true,
        canDeleteOwnContent: true,
        canLikeContent: true,
        canReportContent: true,
        canFollowUsers: true,
        canFollowTopics: true,
        canParticipateInPolls: true,
        canViewStatistics: true,
        canCreatePolls: true,
        canUploadFiles: true,
        canCreateFeaturedTopics: true,
        canCustomizeProfile: true,
        canPinOwnTopics: true,
        canCreateAnnouncements: true,
        canSeeReportedContent: true,
        canEditOtherContent: true,
        canDeleteOtherContent: true,
        canPinAnyTopic: true,
        canBanUsers: true,
        canApproveContent: true,
        canManageCategories: true,
        canManageRoles: true,
        canAccessAdminPanel: true,
        canManageSettings: true,
        canExportData: true)),

    // مالك
    UserRoleModel(
      id: 'owner',
      name: 'مالك',
      description: 'مالك المنتدى',
      permissionLevel: 100,
      color: Colors.purple,
      icon: Icons.security,
      permissions: UserRolePermissions(
        canCreateTopics: true,
        canReplyToTopics: true,
        canEditOwnContent: true,
        canDeleteOwnContent: true,
        canLikeContent: true,
        canReportContent: true,
        canFollowUsers: true,
        canFollowTopics: true,
        canParticipateInPolls: true,
        canViewStatistics: true,
        canCreatePolls: true,
        canUploadFiles: true,
        canCreateFeaturedTopics: true,
        canCustomizeProfile: true,
        canPinOwnTopics: true,
        canCreateAnnouncements: true,
        canSeeReportedContent: true,
        canEditOtherContent: true,
        canDeleteOtherContent: true,
        canPinAnyTopic: true,
        canBanUsers: true,
        canApproveContent: true,
        canManageCategories: true,
        canManageRoles: true,
        canAccessAdminPanel: true,
        canManageSettings: true,
        canExportData: true,
        canManageAdmins: true,
        canDeleteForum: true)),
  ];
}

/// صلاحيات دور المستخدم
class UserRolePermissions {
  // صلاحيات أساسية
  final bool canCreateTopics;
  final bool canReplyToTopics;
  final bool canEditOwnContent;
  final bool canDeleteOwnContent;
  final bool canLikeContent;
  final bool canReportContent;
  final bool canFollowUsers;
  final bool canFollowTopics;
  final bool canParticipateInPolls;
  final bool canViewStatistics;

  // صلاحيات المستخدم المميز
  final bool canCreatePolls;
  final bool canUploadFiles;
  final bool canCreateFeaturedTopics;
  final bool canCustomizeProfile;

  // صلاحيات المساهم
  final bool canPinOwnTopics;
  final bool canCreateAnnouncements;
  final bool canSeeReportedContent;

  // صلاحيات المشرف
  final bool canEditOtherContent;
  final bool canDeleteOtherContent;
  final bool canPinAnyTopic;
  final bool canBanUsers;
  final bool canApproveContent;

  // صلاحيات المدير
  final bool canManageCategories;
  final bool canManageRoles;
  final bool canAccessAdminPanel;
  final bool canManageSettings;
  final bool canExportData;

  // صلاحيات المالك
  final bool canManageAdmins;
  final bool canDeleteForum;

  /// Constructor
  const UserRolePermissions({
    this.canCreateTopics = false,
    this.canReplyToTopics = false,
    this.canEditOwnContent = false,
    this.canDeleteOwnContent = false,
    this.canLikeContent = false,
    this.canReportContent = false,
    this.canFollowUsers = false,
    this.canFollowTopics = false,
    this.canParticipateInPolls = false,
    this.canViewStatistics = false,
    this.canCreatePolls = false,
    this.canUploadFiles = false,
    this.canCreateFeaturedTopics = false,
    this.canCustomizeProfile = false,
    this.canPinOwnTopics = false,
    this.canCreateAnnouncements = false,
    this.canSeeReportedContent = false,
    this.canEditOtherContent = false,
    this.canDeleteOtherContent = false,
    this.canPinAnyTopic = false,
    this.canBanUsers = false,
    this.canApproveContent = false,
    this.canManageCategories = false,
    this.canManageRoles = false,
    this.canAccessAdminPanel = false,
    this.canManageSettings = false,
    this.canExportData = false,
    this.canManageAdmins = false,
    this.canDeleteForum = false,
  });
}
