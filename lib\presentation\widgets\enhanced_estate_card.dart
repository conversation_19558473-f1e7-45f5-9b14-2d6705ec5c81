import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';

/// بطاقة عرض العقار المحسنة
/// تعرض معلومات العقار بطريقة جذابة وتفاعلية
class EnhancedEstateCard extends StatelessWidget {
  /// معرف العقار
  final String id;
  
  /// عنوان العقار
  final String title;
  
  /// وصف العقار
  final String description;
  
  /// سعر العقار
  final double price;
  
  /// موقع العقار
  final String location;
  
  /// قائمة بروابط صور العقار
  final List<String> imageUrls;
  
  /// عدد الغرف
  final int? rooms;
  
  /// المساحة بالمتر المربع
  final double? area;
  
  /// ما إذا كان العقار مميز
  final bool isFeatured;
  
  /// ما إذا كان العقار جديد (أقل من 3 أيام)
  final bool isNew;
  
  /// تاريخ نشر الإعلان
  final DateTime publishDate;
  
  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final Function()? onTap;
  
  /// دالة يتم استدعاؤها عند النقر على زر المفضلة
  final Function(bool)? onFavoriteToggle;
  
  /// ما إذا كان العقار في المفضلة
  final bool isFavorite;

  const EnhancedEstateCard({
    super.key,
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.location,
    required this.imageUrls,
    required this.publishDate,
    this.rooms,
    this.area,
    this.isFeatured = false,
    this.isNew = false,
    this.onTap,
    this.onFavoriteToggle,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    final currencyFormatter = NumberFormat("#,##0", "ar");
    final timeAgo = _getTimeAgo(publishDate);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2)),
          ]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة العقار مع الشارات
            Stack(
              children: [
                // صورة العقار
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                  child: AspectRatio(
                    aspectRatio: 16 / 9,
                    child: imageUrls.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: imageUrls.first,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: Colors.grey[200],
                              child: const Center(
                                child: CircularProgressIndicator())),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey[200],
                              child: const Icon(Icons.error)))
                        : Container(
                            color: Colors.grey[200],
                            child: const Icon(Icons.home, size: 50, color: Colors.grey)))),
                
                // شارات العقار (مميز، جديد)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (isFeatured)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.amber,
                            borderRadius: BorderRadius.circular(4)),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.star, size: 14, color: Colors.white),
                              SizedBox(width: 4),
                              Text(
                                "مميز",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold)),
                            ])),
                      if (isNew)
                        Container(
                          margin: const EdgeInsets.only(top: 4),
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green,
                            borderRadius: BorderRadius.circular(4)),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.fiber_new, size: 14, color: Colors.white),
                              SizedBox(width: 4),
                              Text(
                                "جديد",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold)),
                            ])),
                    ])),
                
                // زر المفضلة
                Positioned(
                  top: 8,
                  left: 8,
                  child: GestureDetector(
                    onTap: () {
                      if (onFavoriteToggle != null) {
                        onFavoriteToggle!(!isFavorite);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2)),
                        ]),
                      child: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: isFavorite ? Colors.red : Colors.grey,
                        size: 20)))),
                
                // عدد الصور
                if (imageUrls.length > 1)
                  Positioned(
                    bottom: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(4)),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.photo_library, size: 14, color: Colors.white),
                          const SizedBox(width: 4),
                          Text(
                            "${imageUrls.length}",
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold)),
                        ]))),
              ]),
            
            // معلومات العقار
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // السعر
                  Text(
                    "${currencyFormatter.format(price)} د.ك",
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87)),
                  
                  const SizedBox(height: 8),
                  
                  // العنوان
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis),
                  
                  const SizedBox(height: 4),
                  
                  // الموقع
                  Row(
                    children: [
                      const Icon(Icons.location_on, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          location,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis)),
                    ]),
                  
                  const SizedBox(height: 8),
                  
                  // المواصفات (الغرف، المساحة)
                  Row(
                    children: [
                      if (rooms != null) ...[
                        const Icon(Icons.bedroom_parent, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          "$rooms غرف",
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey)),
                        const SizedBox(width: 16),
                      ],
                      if (area != null) ...[
                        const Icon(Icons.square_foot, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          "$area م²",
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey)),
                      ],
                      const Spacer(),
                      // وقت النشر
                      Text(
                        timeAgo,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey)),
                    ]),
                ])),
          ])));
  }
  
  /// حساب الوقت المنقضي منذ نشر الإعلان
  String _getTimeAgo(DateTime publishDate) {
    final now = DateTime.now();
    final difference = now.difference(publishDate);
    
    if (difference.inDays > 30) {
      return "منذ ${(difference.inDays / 30).floor()} شهر";
    } else if (difference.inDays > 0) {
      return "منذ ${difference.inDays} يوم";
    } else if (difference.inHours > 0) {
      return "منذ ${difference.inHours} ساعة";
    } else if (difference.inMinutes > 0) {
      return "منذ ${difference.inMinutes} دقيقة";
    } else {
      return "الآن";
    }
  }
}
