import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';
import 'package:kuwait_corners/presentation/bloc/estate_bloc.dart';
import 'package:kuwait_corners/presentation/bloc/estate_event.dart';
import 'package:kuwait_corners/presentation/bloc/estate_state.dart';
import 'package:kuwait_corners/presentation/widgets/estate_card.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';

/// صفحة عرض العقارات على الخريطة
class MapViewPage extends StatefulWidget {
  /// قائمة العقارات التي سيتم عرضها على الخريطة
  final List<Estate>? estates;

  /// عنوان الصفحة
  final String title;

  const MapViewPage({
    super.key,
    this.estates,
    this.title = "عرض العقارات على الخريطة",
  });

  @override
  State<MapViewPage> createState() => _MapViewPageState();
}

class _MapViewPageState extends State<MapViewPage> {
  @override
  void initState() {
    super.initState();

    // إذا لم يتم تمرير قائمة عقارات، قم بتحميلها من الـ Bloc
    if (widget.estates == null) {
      context.read<EstateBloc>().add(FetchPaginatedEstates(refresh: true));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.primary),
      body: widget.estates != null
          ? _buildEstatesList(widget.estates!)
          : BlocConsumer<EstateBloc, EstateState>(
              listener: (context, state) {
                // ليس هناك حاجة لأي إجراء هنا
              },
              builder: (context, state) {
                if (state is EstateLoading) {
                  return const LoadingWidget();
                } else if (state is PaginatedEstatesLoaded) {
                  return _buildEstatesList(state.estates);
                } else if (state is EstateError) {
                  return Center(
                    child: Text("خطأ: ${state.message}"));
                }
                return const Center(
                  child: Text("جاري تحميل العقارات..."));
              }));
  }

  /// بناء قائمة العقارات
  Widget _buildEstatesList(List<Estate> estates) {
    if (estates.isEmpty) {
      return const Center(
        child: Text("لا توجد عقارات متاحة للعرض على الخريطة"));
    }

    return ListView.builder(
      itemCount: estates.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        final estate = estates[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4)),
              ]),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: EstateCard(estate: estate))));
      });
  }
}
