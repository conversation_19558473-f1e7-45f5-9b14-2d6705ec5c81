// lib/presentation/widgets/improved_ad_creation_progress.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// مؤشر تقدم محسن لعملية إنشاء الإعلان
/// يعرض الخطوات المختلفة لعملية إنشاء الإعلان بتصميم عصري وجذاب
class ImprovedAdCreationProgress extends StatelessWidget {
  /// الخطوة الحالية (1-5)
  final int currentStep;

  /// قائمة بأسماء الخطوات المحسنة (5 خطوات بدلاً من 7)
  final List<String> stepNames = [
    "التصنيف",
    "الصور",
    "التفاصيل",
    "الإعدادات",
    "المعاينة",
  ];

  /// قائمة بأيقونات الخطوات
  final List<IconData> stepIcons = [
    Icons.category_outlined,
    Icons.image_outlined,
    Icons.description_outlined,
    Icons.settings_outlined,
    Icons.preview_outlined,
  ];

  /// ما إذا كان يمكن التنقل بين الخطوات
  final bool allowNavigation;

  /// دالة يتم استدعاؤها عند النقر على خطوة
  final Function(int)? onStepTap;

  ImprovedAdCreationProgress({
    super.key,
    required this.currentStep,
    this.allowNavigation = true,
    this.onStepTap,
  }) : assert(currentStep >= 1 && currentStep <= 5, "الخطوة يجب أن تكون بين 1 و 5");

  @override
  Widget build(BuildContext context) {
    // ألوان التصميم
    final primaryColor = Theme.of(context).primaryColor;
    final completedColor = primaryColor;
    final currentColor = primaryColor;
    final pendingColor = Colors.grey.shade300;
    final textColor = Colors.grey.shade800;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 5,
            offset: const Offset(0, -2)),
        ]),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مؤشر التقدم مع الأيقونات
          Row(
            children: List.generate(5, (index) {
              final stepNumber = index + 1;
              final isCompleted = stepNumber < currentStep;
              final isCurrent = stepNumber == currentStep;
              final isPending = stepNumber > currentStep;

              // تحديد لون الخطوة
              final color = isCompleted
                  ? completedColor
                  : isCurrent
                      ? currentColor
                      : pendingColor;

              return Expanded(
                child: Column(
                  children: [
                    // خط التقدم (لا يظهر للخطوة الأولى)
                    if (index > 0)
                      Container(
                        height: 3,
                        margin: const EdgeInsets.only(bottom: 8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              index < currentStep ? completedColor : pendingColor,
                              index < currentStep - 1 ? completedColor : pendingColor,
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight)))
                    else
                      const SizedBox(height: 11), // للمحاذاة

                    // دائرة الخطوة مع الأيقونة
                    GestureDetector(
                      onTap: allowNavigation && onStepTap != null && (isCompleted || isCurrent)
                          ? () => onStepTap!(stepNumber)
                          : null,
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isCurrent
                              ? color
                              : isCompleted
                                  ? color.withAlpha(204) // 0.8 * 255
                                  : color.withAlpha(77), // 0.3 * 255
                          shape: BoxShape.circle,
                          boxShadow: isCurrent
                              ? [
                                  BoxShadow(
                                    color: color.withAlpha(77), // 0.3 * 255
                                    blurRadius: 8,
                                    spreadRadius: 2)
                                ]
                              : null),
                        child: Center(
                          child: isCompleted
                              ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 20)
                              : Icon(
                                  stepIcons[index],
                                  color: isCurrent ? Colors.white : color,
                                  size: 20)))),

                    // اسم الخطوة
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        stepNames[index],
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                          color: isCurrent ? currentColor : textColor),
                        textAlign: TextAlign.center)),
                  ]));
            })),

          const SizedBox(height: 8),

          // معلومات الخطوة الحالية
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "الخطوة $currentStep من 5",
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: textColor)),
              Text(
                stepNames[currentStep - 1],
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: currentColor)),
            ]),
        ]));
  }
}
