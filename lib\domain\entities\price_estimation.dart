import 'package:cloud_firestore/cloud_firestore.dart';

/// نوع تقدير السعر
enum PriceEstimationType {
  /// تقدير سعر البيع
  sale,

  /// تقدير سعر الإيجار
  rent,

  /// تقدير العائد الاستثماري
  investment,

  /// تقدير تكلفة البناء
  construction,

  /// تقدير تكلفة الصيانة
  maintenance,
}

/// مستوى دقة التقدير
enum EstimationAccuracy {
  /// دقة منخفضة
  low,

  /// دقة متوسطة
  medium,

  /// دقة عالية
  high,

  /// دقة عالية جداً
  veryHigh,
}

/// كيان تقدير السعر
class PriceEstimation {
  /// معرف التقدير
  final String id;

  /// معرف المستخدم
  final String userId;

  /// نوع التقدير
  final PriceEstimationType type;

  /// معرف العقار (إذا كان التقدير لعقار موجود)
  final String? estateId;

  /// المنطقة
  final String area;

  /// نوع العقار
  final String propertyType;

  /// المساحة (بالمتر المربع)
  final double size;

  /// عدد الغرف
  final int? rooms;

  /// عدد الحمامات
  final int? bathrooms;

  /// عمر العقار (بالسنوات)
  final int? age;

  /// الطابق
  final int? floor;

  /// ما إذا كان العقار مفروش
  final bool? isFurnished;

  /// ما إذا كان العقار مجدد
  final bool? isRenovated;

  /// المميزات الإضافية
  final List<String>? features;

  /// القيمة المقدرة
  final double estimatedValue;

  /// الحد الأدنى للقيمة المقدرة
  final double minValue;

  /// الحد الأقصى للقيمة المقدرة
  final double maxValue;

  /// مستوى دقة التقدير
  final EstimationAccuracy accuracy;

  /// العوامل المؤثرة في التقدير
  final Map<String, double> factors;

  /// العقارات المشابهة
  final List<Map<String, dynamic>>? similarProperties;

  /// تاريخ التقدير
  final DateTime estimatedAt;

  /// تاريخ انتهاء صلاحية التقدير
  final DateTime expiresAt;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  /// إنشاء كيان تقدير السعر
  PriceEstimation(
      {required this.id,
      required this.userId,
      required this.type,
      this.estateId,
      required this.area,
      required this.propertyType,
      required this.size,
      this.rooms,
      this.bathrooms,
      this.age,
      this.floor,
      this.isFurnished,
      this.isRenovated,
      this.features,
      required this.estimatedValue,
      required this.minValue,
      required this.maxValue,
      required this.accuracy,
      required this.factors,
      this.similarProperties,
      required this.estimatedAt,
      required this.expiresAt,
      this.metadata,
      double? estimatedPrice, // For compatibility with other parts of the code
      double? minPrice, // For compatibility with other parts of the code
      double? maxPrice, // For compatibility with other parts of the code
      double? confidence, // For compatibility with other parts of the code
      int? similarEstatesCount // For compatibility with other parts of the code
      });

  /// إنشاء كيان تقدير السعر من JSON
  factory PriceEstimation.fromJson(Map<String, dynamic> json) {
    return PriceEstimation(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: PriceEstimationType.values.firstWhere(
        (e) => e.toString() == 'PriceEstimationType.${json['type']}',
        orElse: () => PriceEstimationType.sale),
      estateId: json['estateId'] as String?,
      area: json['area'] as String,
      propertyType: json['propertyType'] as String,
      size: (json['size'] as num).toDouble(),
      rooms: json['rooms'] as int?,
      bathrooms: json['bathrooms'] as int?,
      age: json['age'] as int?,
      floor: json['floor'] as int?,
      isFurnished: json['isFurnished'] as bool?,
      isRenovated: json['isRenovated'] as bool?,
      features:
          json['features'] != null ? List<String>.from(json['features']) : null,
      estimatedValue: (json['estimatedValue'] as num).toDouble(),
      minValue: (json['minValue'] as num).toDouble(),
      maxValue: (json['maxValue'] as num).toDouble(),
      accuracy: EstimationAccuracy.values.firstWhere(
        (e) => e.toString() == 'EstimationAccuracy.${json['accuracy']}',
        orElse: () => EstimationAccuracy.medium),
      factors: Map<String, double>.from(json['factors'] ?? {}),
      similarProperties: json['similarProperties'] != null
          ? List<Map<String, dynamic>>.from(json['similarProperties'])
          : null,
      estimatedAt: (json['estimatedAt'] as Timestamp).toDate(),
      expiresAt: (json['expiresAt'] as Timestamp).toDate(),
      metadata: json['metadata'] as Map<String, dynamic>?);
  }

  /// تحويل كيان تقدير السعر إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.toString().split('.').last,
      'estateId': estateId,
      'area': area,
      'propertyType': propertyType,
      'size': size,
      'rooms': rooms,
      'bathrooms': bathrooms,
      'age': age,
      'floor': floor,
      'isFurnished': isFurnished,
      'isRenovated': isRenovated,
      'features': features,
      'estimatedValue': estimatedValue,
      'minValue': minValue,
      'maxValue': maxValue,
      'accuracy': accuracy.toString().split('.').last,
      'factors': factors,
      'similarProperties': similarProperties,
      'estimatedAt': estimatedAt,
      'expiresAt': expiresAt,
      'metadata': metadata,
    };
  }

  /// نسخ كيان تقدير السعر مع تعديل بعض الخصائص
  PriceEstimation copyWith({
    String? id,
    String? userId,
    PriceEstimationType? type,
    String? estateId,
    String? area,
    String? propertyType,
    double? size,
    int? rooms,
    int? bathrooms,
    int? age,
    int? floor,
    bool? isFurnished,
    bool? isRenovated,
    List<String>? features,
    double? estimatedValue,
    double? minValue,
    double? maxValue,
    EstimationAccuracy? accuracy,
    Map<String, double>? factors,
    List<Map<String, dynamic>>? similarProperties,
    DateTime? estimatedAt,
    DateTime? expiresAt,
    Map<String, dynamic>? metadata,
  }) {
    return PriceEstimation(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      estateId: estateId ?? this.estateId,
      area: area ?? this.area,
      propertyType: propertyType ?? this.propertyType,
      size: size ?? this.size,
      rooms: rooms ?? this.rooms,
      bathrooms: bathrooms ?? this.bathrooms,
      age: age ?? this.age,
      floor: floor ?? this.floor,
      isFurnished: isFurnished ?? this.isFurnished,
      isRenovated: isRenovated ?? this.isRenovated,
      features: features ?? this.features,
      estimatedValue: estimatedValue ?? this.estimatedValue,
      minValue: minValue ?? this.minValue,
      maxValue: maxValue ?? this.maxValue,
      accuracy: accuracy ?? this.accuracy,
      factors: factors ?? this.factors,
      similarProperties: similarProperties ?? this.similarProperties,
      estimatedAt: estimatedAt ?? this.estimatedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      metadata: metadata ?? this.metadata);
  }
}
