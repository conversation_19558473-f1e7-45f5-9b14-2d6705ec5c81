import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../../domain/entities/estate.dart';

/// خدمة تخزين مؤقت محسنة مع إدارة ذكية للبيانات
class EnhancedCacheService {
  static final EnhancedCacheService _instance = EnhancedCacheService._internal();

  factory EnhancedCacheService() => _instance;

  EnhancedCacheService._internal();

  late Box<dynamic> _cacheBox;
  late SharedPreferences _prefs;

  // إعدادات التخزين المؤقت
  static const Duration _defaultCacheDuration = Duration(hours: 1);
  static const Duration _estatesCacheDuration = Duration(minutes: 30);
  static const Duration _searchCacheDuration = Duration(minutes: 15);
  static const Duration _userDataCacheDuration = Duration(days: 7);

  // مفاتيح التخزين المؤقت
  static const String _estatesPrefix = 'estates_';
  static const String _searchPrefix = 'search_';
  static const String _userPrefix = 'user_';
  static const String _metadataPrefix = 'meta_';

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await Hive.initFlutter();
    _cacheBox = await Hive.openBox('enhanced_cache');
    _prefs = await SharedPreferences.getInstance();
  }

  /// تخزين العقارات مع البيانات الوصفية
  Future<void> cacheEstates({
    required String key,
    required List<Estate> estates,
    required bool hasMore,
    int? currentPage,
    String? category,
    String? searchQuery,
    Duration? duration,
  }) async {
    final cacheKey = '$_estatesPrefix$key';
    final metadata = CacheMetadata(
      key: cacheKey,
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(duration ?? _estatesCacheDuration),
      dataType: CacheDataType.estates,
      metadata: {
        'hasMore': hasMore,
        'currentPage': currentPage,
        'category': category,
        'searchQuery': searchQuery,
        'count': estates.length,
      });

    // تخزين البيانات
    final estatesData = estates.map((e) => _estateToMap(e)).toList();
    await _cacheBox.put(cacheKey, estatesData);

    // تخزين البيانات الوصفية
    await _cacheBox.put('$_metadataPrefix$cacheKey', metadata.toMap());
  }

  /// استرجاع العقارات من التخزين المؤقت
  Future<CachedEstatesResult?> getCachedEstates(String key) async {
    final cacheKey = '$_estatesPrefix$key';

    // فحص البيانات الوصفية أولاً
    final metadataMap = _cacheBox.get('$_metadataPrefix$cacheKey');
    if (metadataMap == null) return null;

    final metadata = CacheMetadata.fromMap(metadataMap);

    // فحص انتهاء الصلاحية
    if (metadata.isExpired) {
      await _removeCacheEntry(cacheKey);
      return null;
    }

    // استرجاع البيانات
    final estatesData = _cacheBox.get(cacheKey);
    if (estatesData == null) return null;

    try {
      final estates = (estatesData as List)
          .map((e) => _estateFromMap(e as Map<String, dynamic>))
          .where((estate) => estate != null)
          .cast<Estate>()
          .toList();

      return CachedEstatesResult(
        estates: estates,
        hasMore: metadata.metadata?['hasMore'] ?? false,
        currentPage: metadata.metadata?['currentPage'],
        category: metadata.metadata?['category'],
        searchQuery: metadata.metadata?['searchQuery'],
        cachedAt: metadata.createdAt);
    } catch (e) {
      // في حالة خطأ في التحويل، إزالة البيانات التالفة
      await _removeCacheEntry(cacheKey);
      return null;
    }
  }

  /// تخزين نتائج البحث
  Future<void> cacheSearchResults({
    required String query,
    required List<Estate> results,
    Duration? duration,
  }) async {
    final cacheKey = '$_searchPrefix${_sanitizeKey(query)}';
    await cacheEstates(
      key: cacheKey.replaceFirst(_estatesPrefix, ''),
      estates: results,
      hasMore: false,
      searchQuery: query,
      duration: duration ?? _searchCacheDuration);
  }

  /// استرجاع نتائج البحث
  Future<List<Estate>?> getCachedSearchResults(String query) async {
    final cacheKey = '$_searchPrefix${_sanitizeKey(query)}';
    final result = await getCachedEstates(cacheKey.replaceFirst(_estatesPrefix, ''));
    return result?.estates;
  }

  /// تخزين بيانات المستخدم
  Future<void> cacheUserData({
    required String userId,
    required Map<String, dynamic> userData,
    Duration? duration,
  }) async {
    final cacheKey = '$_userPrefix$userId';
    final metadata = CacheMetadata(
      key: cacheKey,
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(duration ?? _userDataCacheDuration),
      dataType: CacheDataType.userData);

    await _cacheBox.put(cacheKey, userData);
    await _cacheBox.put('$_metadataPrefix$cacheKey', metadata.toMap());
  }

  /// استرجاع بيانات المستخدم
  Future<Map<String, dynamic>?> getCachedUserData(String userId) async {
    final cacheKey = '$_userPrefix$userId';

    final metadataMap = _cacheBox.get('$_metadataPrefix$cacheKey');
    if (metadataMap == null) return null;

    final metadata = CacheMetadata.fromMap(metadataMap);
    if (metadata.isExpired) {
      await _removeCacheEntry(cacheKey);
      return null;
    }

    return _cacheBox.get(cacheKey);
  }

  /// مسح البيانات المنتهية الصلاحية
  Future<void> clearExpiredCache() async {
    final keys = _cacheBox.keys.toList();
    final expiredKeys = <String>[];

    for (final key in keys) {
      if (key.toString().startsWith(_metadataPrefix)) {
        final metadataMap = _cacheBox.get(key);
        if (metadataMap != null) {
          final metadata = CacheMetadata.fromMap(metadataMap);
          if (metadata.isExpired) {
            expiredKeys.add(key.toString());
            expiredKeys.add(key.toString().replaceFirst(_metadataPrefix, ''));
          }
        }
      }
    }

    for (final key in expiredKeys) {
      await _cacheBox.delete(key);
    }
  }

  /// مسح جميع البيانات المخزنة
  Future<void> clearAllCache() async {
    await _cacheBox.clear();
  }

  /// مسح جميع البيانات (alias للتوافق مع PropertyRequestService)
  Future<void> clearAll() async {
    await clearAllCache();
  }

  /// حفظ البيانات العامة مع انتهاء صلاحية
  Future<void> setData<T>(String key, T data, {Duration? expiry}) async {
    final metadata = CacheMetadata(
      key: key,
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(expiry ?? _defaultCacheDuration),
      dataType: CacheDataType.generic);

    await _cacheBox.put(key, data);
    await _cacheBox.put('$_metadataPrefix$key', metadata.toMap());
  }

  /// استرجاع البيانات العامة مع التحقق من انتهاء الصلاحية
  Future<T?> getData<T>(String key, {Duration? expiry}) async {
    final metadataMap = _cacheBox.get('$_metadataPrefix$key');
    if (metadataMap == null) return null;

    final metadata = CacheMetadata.fromMap(metadataMap);
    if (metadata.isExpired) {
      await _removeCacheEntry(key);
      return null;
    }

    final data = _cacheBox.get(key);
    return data as T?;
  }

  /// الحصول على حجم التخزين المؤقت
  Future<int> getCacheSize() async {
    try {
      int totalSize = 0;
      final keys = _cacheBox.keys.toList();

      for (final key in keys) {
        final data = _cacheBox.get(key);
        if (data != null) {
          // تقدير تقريبي لحجم البيانات
          final jsonString = jsonEncode(data);
          totalSize += jsonString.length;
        }
      }

      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// مسح تخزين مؤقت محدد
  Future<void> clearCacheByPrefix(String prefix) async {
    final keys = _cacheBox.keys
        .where((key) => key.toString().startsWith(prefix))
        .toList();

    for (final key in keys) {
      await _cacheBox.delete(key);
      await _cacheBox.delete('$_metadataPrefix$key');
    }
  }

  /// إزالة مدخل تخزين مؤقت محدد
  Future<void> _removeCacheEntry(String key) async {
    await _cacheBox.delete(key);
    await _cacheBox.delete('$_metadataPrefix$key');
  }

  /// تنظيف مفتاح التخزين المؤقت
  String _sanitizeKey(String key) {
    return key.replaceAll(RegExp(r'[^\w\-_]'), '_').toLowerCase();
  }

  /// الحصول على إحصائيات التخزين المؤقت
  Future<CacheStatistics> getCacheStatistics() async {
    final keys = _cacheBox.keys.toList();
    int totalEntries = 0;
    int expiredEntries = 0;
    int estatesEntries = 0;
    int searchEntries = 0;
    int userDataEntries = 0;

    for (final key in keys) {
      final keyStr = key.toString();
      if (keyStr.startsWith(_metadataPrefix)) {
        totalEntries++;
        final metadataMap = _cacheBox.get(key);
        if (metadataMap != null) {
          final metadata = CacheMetadata.fromMap(metadataMap);
          if (metadata.isExpired) expiredEntries++;

          switch (metadata.dataType) {
            case CacheDataType.estates:
              estatesEntries++;
              break;
            case CacheDataType.search:
              searchEntries++;
              break;
            case CacheDataType.userData:
              userDataEntries++;
              break;
            case CacheDataType.generic:
              // البيانات العامة - لا نحتاج لعدها بشكل منفصل
              break;
          }
        }
      }
    }

    return CacheStatistics(
      totalEntries: totalEntries,
      expiredEntries: expiredEntries,
      estatesEntries: estatesEntries,
      searchEntries: searchEntries,
      userDataEntries: userDataEntries);
  }

  /// تنظيف الموارد
  void dispose() {
    _cacheBox.close();
  }

  /// تحويل Estate إلى Map
  Map<String, dynamic> _estateToMap(Estate estate) {
    return {
      'id': estate.id,
      'title': estate.title,
      'description': estate.description,
      'price': estate.price,
      'location': estate.location,
      'photoUrls': estate.photoUrls,
      'isFeatured': estate.isFeatured,
      'planType': estate.planType,
      'startDate': estate.startDate?.toIso8601String(),
      'endDate': estate.endDate?.toIso8601String(),
      'createdAt': estate.createdAt.toIso8601String(),
      'mainCategory': estate.mainCategory,
      'subCategory': estate.subCategory,
      'postedByUserType': estate.postedByUserType,
      'hidePhone': estate.hidePhone,
      'extraPhones': estate.extraPhones,
      'shareLocation': estate.shareLocation,
      'lat': estate.lat,
      'lng': estate.lng,
      'hasCentralAC': estate.hasCentralAC,
      'hasSecurity': estate.hasSecurity,
      'allowPets': estate.allowPets,
      'hasElevator': estate.hasElevator,
      'hasSwimmingPool': estate.hasSwimmingPool,
      'hasMaidRoom': estate.hasMaidRoom,
      'hasGarage': estate.hasGarage,
      'hasBalcony': estate.hasBalcony,
      'isFullyFurnished': estate.isFullyFurnished,
      'rebound': estate.rebound,
      'numberOfRooms': estate.numberOfRooms,
      'internalLocation': estate.internalLocation,
      'salon': estate.salon,
      'area': estate.area,
      'floorNumber': estate.floorNumber,
      'numberOfBathrooms': estate.numberOfBathrooms,
      'buildingAge': estate.buildingAge,
      'numberOfFloors': estate.numberOfFloors,
      'propertyType': estate.propertyType,
      'autoRepublish': estate.autoRepublish,
      'kuwaitCornersPin': estate.kuwaitCornersPin,
      'movingAd': estate.movingAd,
      'vipBadge': estate.vipBadge,
      'pinnedOnHome': estate.pinnedOnHome,
      'discountCode': estate.discountCode,
      'advertiserImage': estate.advertiserImage,
      'advertiserName': estate.advertiserName,
      'advertiserEmail': estate.advertiserEmail,
      'advertiserRegistrationDate': estate.advertiserRegistrationDate?.toIso8601String(),
      'advertiserAdsCount': estate.advertiserAdsCount,
      'ownerId': estate.ownerId,
      'originalEstateId': estate.originalEstateId,
      'isOriginal': estate.isOriginal,
      'copiedBy': estate.copiedBy,
      'isPaymentVerified': estate.isPaymentVerified,
      'viewsCount': estate.viewsCount,
      'inquiriesCount': estate.inquiriesCount,
      'favoritesCount': estate.favoritesCount,
      'latitude': estate.latitude,
      'longitude': estate.longitude,
      'rooms': estate.rooms,
      'bathrooms': estate.bathrooms,
      'floors': estate.floors,
      'purpose': estate.purpose,
      'usageType': estate.usageType,
      'hasGarden': estate.hasGarden,
      'hasPool': estate.hasPool,
      'hasDriverRoom': estate.hasDriverRoom,
      'hasPrivateEntrance': estate.hasPrivateEntrance,
      'hasEquippedKitchen': estate.hasEquippedKitchen,
      'isAvailable': estate.isAvailable,
      'isCopied': estate.isCopied,
      'copiedAt': estate.copiedAt?.toIso8601String(),
      'isPaidAd': estate.isPaidAd,
    };
  }

  /// تحويل Map إلى Estate
  Estate? _estateFromMap(Map<String, dynamic> map) {
    try {
      return Estate(
        id: map['id'] ?? '',
        title: map['title'] ?? '',
        description: map['description'] ?? '',
        price: (map['price'] ?? 0.0).toDouble(),
        location: map['location'] ?? '',
        photoUrls: List<String>.from(map['photoUrls'] ?? []),
        isFeatured: map['isFeatured'] ?? false,
        planType: map['planType'] ?? 'free',
        startDate: map['startDate'] != null
            ? DateTime.parse(map['startDate'])
            : null,
        endDate: map['endDate'] != null
            ? DateTime.parse(map['endDate'])
            : null,
        createdAt: map['createdAt'] != null
            ? DateTime.parse(map['createdAt'])
            : DateTime.now(),
        mainCategory: map['mainCategory'],
        subCategory: map['subCategory'],
        postedByUserType: map['postedByUserType'],
        hidePhone: map['hidePhone'] ?? false,
        extraPhones: List<String>.from(map['extraPhones'] ?? []),
        shareLocation: map['shareLocation'] ?? false,
        lat: map['lat']?.toDouble(),
        lng: map['lng']?.toDouble(),
        hasCentralAC: map['hasCentralAC'] ?? false,
        hasSecurity: map['hasSecurity'],
        allowPets: map['allowPets'],
        hasElevator: map['hasElevator'],
        hasSwimmingPool: map['hasSwimmingPool'],
        hasMaidRoom: map['hasMaidRoom'] ?? false,
        hasGarage: map['hasGarage'] ?? false,
        hasBalcony: map['hasBalcony'],
        isFullyFurnished: map['isFullyFurnished'],
        rebound: map['rebound'],
        numberOfRooms: map['numberOfRooms'],
        internalLocation: map['internalLocation'],
        salon: map['salon'],
        area: map['area']?.toDouble(),
        floorNumber: map['floorNumber'],
        numberOfBathrooms: map['numberOfBathrooms'],
        buildingAge: map['buildingAge'],
        numberOfFloors: map['numberOfFloors'],
        propertyType: map['propertyType'],
        autoRepublish: map['autoRepublish'] ?? false,
        kuwaitCornersPin: map['kuwaitCornersPin'] ?? false,
        movingAd: map['movingAd'] ?? false,
        vipBadge: map['vipBadge'] ?? false,
        pinnedOnHome: map['pinnedOnHome'] ?? false,
        discountCode: map['discountCode'],
        advertiserImage: map['advertiserImage'],
        advertiserName: map['advertiserName'],
        advertiserEmail: map['advertiserEmail'],
        advertiserRegistrationDate: map['advertiserRegistrationDate'] != null
            ? DateTime.parse(map['advertiserRegistrationDate'])
            : null,
        advertiserAdsCount: map['advertiserAdsCount'],
        ownerId: map['ownerId'],
        originalEstateId: map['originalEstateId'],
        isOriginal: map['isOriginal'] ?? true,
        copiedBy: List<String>.from(map['copiedBy'] ?? []),
        isPaymentVerified: map['isPaymentVerified'] ?? false,
        viewsCount: map['viewsCount'],
        inquiriesCount: map['inquiriesCount'],
        favoritesCount: map['favoritesCount'],
        latitude: map['latitude']?.toDouble(),
        longitude: map['longitude']?.toDouble(),
        rooms: map['rooms'],
        bathrooms: map['bathrooms'],
        floors: map['floors'],
        purpose: map['purpose'],
        usageType: map['usageType'],
        hasGarden: map['hasGarden'],
        hasPool: map['hasPool'],
        hasDriverRoom: map['hasDriverRoom'],
        hasPrivateEntrance: map['hasPrivateEntrance'],
        hasEquippedKitchen: map['hasEquippedKitchen'],
        isAvailable: map['isAvailable'] ?? true,
        isCopied: map['isCopied'] ?? false,
        copiedAt: map['copiedAt'] != null
            ? DateTime.parse(map['copiedAt'])
            : null,
        isPaidAd: map['isPaidAd'] ?? false);
    } catch (e) {
      print('خطأ في تحويل Map إلى Estate: $e');
      return null;
    }
  }
}

/// نتيجة العقارات المخزنة مؤقتاً
class CachedEstatesResult {
  final List<Estate> estates;
  final bool hasMore;
  final int? currentPage;
  final String? category;
  final String? searchQuery;
  final DateTime cachedAt;

  CachedEstatesResult({
    required this.estates,
    required this.hasMore,
    this.currentPage,
    this.category,
    this.searchQuery,
    required this.cachedAt,
  });
}

/// البيانات الوصفية للتخزين المؤقت
class CacheMetadata {
  final String key;
  final DateTime createdAt;
  final DateTime expiresAt;
  final CacheDataType dataType;
  final Map<String, dynamic>? metadata;

  CacheMetadata({
    required this.key,
    required this.createdAt,
    required this.expiresAt,
    required this.dataType,
    this.metadata,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);

  Map<String, dynamic> toMap() {
    return {
      'key': key,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'dataType': dataType.index,
      'metadata': metadata,
    };
  }

  factory CacheMetadata.fromMap(Map<String, dynamic> map) {
    return CacheMetadata(
      key: map['key'] ?? '',
      createdAt: DateTime.parse(map['createdAt']),
      expiresAt: DateTime.parse(map['expiresAt']),
      dataType: CacheDataType.values[map['dataType'] ?? 0],
      metadata: map['metadata']);
  }
}

/// أنواع البيانات المخزنة
enum CacheDataType {
  estates,
  search,
  userData,
  generic,
}

/// إحصائيات التخزين المؤقت
class CacheStatistics {
  final int totalEntries;
  final int expiredEntries;
  final int estatesEntries;
  final int searchEntries;
  final int userDataEntries;

  CacheStatistics({
    required this.totalEntries,
    required this.expiredEntries,
    required this.estatesEntries,
    required this.searchEntries,
    required this.userDataEntries,
  });

  @override
  String toString() {
    return 'CacheStatistics(total: $totalEntries, expired: $expiredEntries, estates: $estatesEntries, search: $searchEntries, userData: $userDataEntries)';
  }
}
