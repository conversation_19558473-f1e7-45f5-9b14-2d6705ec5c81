import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../domain/models/forum/badge_model.dart';

/// بطاقة الشارة
class BadgeCard extends StatelessWidget {
  /// نموذج الشارة
  final BadgeModel badge;

  /// حجم البطاقة
  final BadgeCardSize size;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;

  /// ما إذا كانت الشارة مقفلة
  final bool isLocked;

  /// تاريخ الحصول على الشارة
  final DateTime? earnedDate;

  const BadgeCard({
    super.key,
    required this.badge,
    this.size = BadgeCardSize.medium,
    this.onTap,
    this.isLocked = false,
    this.earnedDate,
  });

  @override
  Widget build(BuildContext context) {
    switch (size) {
      case BadgeCardSize.small:
        return _buildSmallCard(context);
      case BadgeCardSize.medium:
        return _buildMediumCard(context);
      case BadgeCardSize.large:
        return _buildLargeCard(context);
    }
  }

  /// بناء بطاقة صغيرة
  Widget _buildSmallCard(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 80,
        height: 100,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isLocked
                ? Colors.grey.shade300
                : badge.color.withValues(alpha: 0.5)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 5,
              offset: const Offset(0, 2)),
          ]),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الشارة
            Stack(
              alignment: Alignment.center,
              children: [
                _buildBadgeIcon(size: 40),
                if (isLocked)
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      shape: BoxShape.circle),
                    child: const Icon(
                      Icons.lock,
                      color: Colors.white,
                      size: 16)),
              ]),
            const SizedBox(height: 8),
            
            // اسم الشارة
            Text(
              badge.name,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: isLocked
                    ? Colors.grey.shade500
                    : badge.color),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis),
            
            // شارة نادرة
            if (badge.isRare && !isLocked)
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.amber.shade100,
                  borderRadius: BorderRadius.circular(8)),
                child: Text(
                  'نادرة',
                  style: TextStyle(
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                    color: Colors.amber.shade800))),
          ])));
  }

  /// بناء بطاقة متوسطة
  Widget _buildMediumCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isLocked
              ? Colors.grey.shade300
              : badge.color.withValues(alpha: 0.5),
          width: 1)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // أيقونة الشارة
              Stack(
                alignment: Alignment.center,
                children: [
                  _buildBadgeIcon(size: 50),
                  if (isLocked)
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle),
                      child: const Icon(
                        Icons.lock,
                        color: Colors.white,
                        size: 20)),
                ]),
              const SizedBox(width: 16),
              
              // معلومات الشارة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم الشارة
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            badge.name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: isLocked
                                  ? Colors.grey.shade500
                                  : badge.color,
                              fontSize: 16),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis)),
                        
                        // شارة نادرة
                        if (badge.isRare && !isLocked)
                          Container(
                            margin: const EdgeInsets.only(left: 8),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.amber.shade100,
                              borderRadius: BorderRadius.circular(12)),
                            child: Text(
                              'نادرة',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.amber.shade800))),
                      ]),
                    const SizedBox(height: 4),
                    
                    // وصف الشارة
                    Text(
                      badge.description,
                      style: TextStyle(
                        color: isLocked
                            ? Colors.grey.shade500
                            : Colors.grey.shade700,
                        fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis),
                    
                    // تاريخ الحصول على الشارة
                    if (earnedDate != null && !isLocked) ...[
                      const SizedBox(height: 4),
                      Text(
                        'تم الحصول عليها في ${_formatDate(earnedDate!)}',
                        style: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 12,
                          fontStyle: FontStyle.italic)),
                    ],
                  ])),
            ]))));
  }

  /// بناء بطاقة كبيرة
  Widget _buildLargeCard(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isLocked
              ? Colors.grey.shade300
              : badge.color.withValues(alpha: 0.5),
          width: 2)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // أيقونة الشارة
              Stack(
                alignment: Alignment.center,
                children: [
                  _buildBadgeIcon(size: 80),
                  if (isLocked)
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle),
                      child: const Icon(
                        Icons.lock,
                        color: Colors.white,
                        size: 32)),
                ]),
              const SizedBox(height: 16),
              
              // اسم الشارة
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    badge.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isLocked
                          ? Colors.grey.shade500
                          : badge.color,
                      fontSize: 18),
                    textAlign: TextAlign.center),
                  
                  // شارة نادرة
                  if (badge.isRare && !isLocked)
                    Container(
                      margin: const EdgeInsets.only(left: 8),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.amber.shade100,
                        borderRadius: BorderRadius.circular(12)),
                      child: Text(
                        'نادرة',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.amber.shade800))),
                ]),
              const SizedBox(height: 8),
              
              // وصف الشارة
              Text(
                badge.description,
                style: TextStyle(
                  color: isLocked
                      ? Colors.grey.shade500
                      : Colors.grey.shade700,
                  fontSize: 16),
                textAlign: TextAlign.center),
              
              // تاريخ الحصول على الشارة
              if (earnedDate != null && !isLocked) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8),
                  decoration: BoxDecoration(
                    color: badge.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20)),
                  child: Text(
                    'تم الحصول عليها في ${_formatDate(earnedDate!)}',
                    style: TextStyle(
                      color: badge.color,
                      fontWeight: FontWeight.bold))),
              ],
            ]))));
  }

  /// بناء أيقونة الشارة
  Widget _buildBadgeIcon({required double size}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(size / 2),
      child: CachedNetworkImage(
        imageUrl: badge.imageUrl,
        width: size,
        height: size,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: Colors.grey.shade200,
          child: Center(
            child: SizedBox(
              width: size / 2,
              height: size / 2,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  isLocked
                      ? Colors.grey.shade400
                      : badge.color))))),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey.shade200,
          child: Icon(
            Icons.error,
            size: size / 2,
            color: Colors.grey.shade400))));
    }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }
}

/// حجم بطاقة الشارة
enum BadgeCardSize {
  /// صغير
  small,

  /// متوسط
  medium,

  /// كبير
  large,
}
