// lib/presentation/widgets/plan_comparison_table.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/models/plan_model.dart';

/// جدول مقارنة الباقات
/// يعرض مقارنة بين الباقات المختلفة
class PlanComparisonTable extends StatelessWidget {
  /// قائمة الباقات
  final List<PlanModel> plans;
  
  /// معرف الباقة المحددة
  final String? selectedPlanId;
  
  /// دالة يتم استدعاؤها عند اختيار باقة
  final Function(PlanModel)? onPlanSelected;

  const PlanComparisonTable({
    super.key,
    required this.plans,
    this.selectedPlanId,
    this.onPlanSelected,
  });

  @override
  Widget build(BuildContext context) {
    // ترتيب الباقات حسب السعر (من الأقل إلى الأعلى)
    final sortedPlans = List<PlanModel>.from(plans)
      ..sort((a, b) => a.price.compareTo(b.price));
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان الجدول
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            "مقارنة الباقات",
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800))),
        
        const SizedBox(height: 8),
        
        // جدول المقارنة
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: DataTable(
            headingRowColor: WidgetStateProperty.all(Colors.grey.shade100),
            columnSpacing: 24,
            horizontalMargin: 16,
            headingRowHeight: 56,
            dataRowMinHeight: 48,
            dataRowMaxHeight: 64,
            border: TableBorder.all(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(12)),
            columns: [
              DataColumn(
                label: Text(
                  "الميزات",
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800))),
              ...sortedPlans.map((plan) => DataColumn(
                label: _buildPlanHeader(context, plan))),
            ],
            rows: [
              // صف السعر
              DataRow(
                cells: [
                  DataCell(Text(
                    "السعر",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800))),
                  ...sortedPlans.map((plan) => DataCell(
                    Text(
                      "${plan.price.toStringAsFixed(3)} د.ك",
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        color: plan.color)))),
                ]),
              
              // صف المدة
              DataRow(
                cells: [
                  DataCell(Text(
                    "المدة",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800))),
                  ...sortedPlans.map((plan) => DataCell(
                    Text(
                      "${plan.durationDays} يوم",
                      style: GoogleFonts.cairo()))),
                ]),
              
              // صف عدد الإعلانات
              DataRow(
                cells: [
                  DataCell(Text(
                    "عدد الإعلانات",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800))),
                  ...sortedPlans.map((plan) => DataCell(
                    Text(
                      "${plan.maxAds} إعلان",
                      style: GoogleFonts.cairo()))),
                ]),
              
              // صف عدد الصور
              DataRow(
                cells: [
                  DataCell(Text(
                    "الصور لكل إعلان",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800))),
                  ...sortedPlans.map((plan) => DataCell(
                    Text(
                      "${plan.maxImagesPerAd} صورة",
                      style: GoogleFonts.cairo()))),
                ]),
              
              // صف مدة الإعلان
              DataRow(
                cells: [
                  DataCell(Text(
                    "مدة عرض الإعلان",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800))),
                  ...sortedPlans.map((plan) => DataCell(
                    Text(
                      "${plan.adDurationDays} يوم",
                      style: GoogleFonts.cairo()))),
                ]),
              
              // صفوف الميزات
              DataRow(
                cells: [
                  DataCell(Text(
                    "إعادة نشر تلقائي",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800))),
                  ...sortedPlans.map((plan) => DataCell(
                    _buildFeatureCell(plan.isFeatureAllowed('autoRepublish')))),
                ]),
              
              DataRow(
                cells: [
                  DataCell(Text(
                    "دبوس كويت كورنرز",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800))),
                  ...sortedPlans.map((plan) => DataCell(
                    _buildFeatureCell(plan.isFeatureAllowed('kuwaitCornersPin')))),
                ]),
              
              DataRow(
                cells: [
                  DataCell(Text(
                    "إعلان متحرك",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800))),
                  ...sortedPlans.map((plan) => DataCell(
                    _buildFeatureCell(plan.isFeatureAllowed('movingAd')))),
                ]),
              
              DataRow(
                cells: [
                  DataCell(Text(
                    "شارة VIP",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800))),
                  ...sortedPlans.map((plan) => DataCell(
                    _buildFeatureCell(plan.isFeatureAllowed('vipBadge')))),
                ]),
              
              DataRow(
                cells: [
                  DataCell(Text(
                    "تثبيت في الصفحة الرئيسية",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800))),
                  ...sortedPlans.map((plan) => DataCell(
                    _buildFeatureCell(plan.isFeatureAllowed('pinnedOnHome')))),
                ]),
            ])),
      ]);
  }
  
  /// بناء رأس الباقة
  Widget _buildPlanHeader(BuildContext context, PlanModel plan) {
    final isSelected = plan.id == selectedPlanId;
    
    return InkWell(
      onTap: onPlanSelected != null ? () => onPlanSelected!(plan) : null,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8),
        decoration: isSelected
            ? BoxDecoration(
                color: plan.color.withAlpha(30),
                borderRadius: BorderRadius.circular(8))
            : null,
        child: Column(
          children: [
            Text(
              plan.nameAr,
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
                color: plan.color)),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: plan.color,
                size: 16),
          ])));
  }
  
  /// بناء خلية ميزة
  Widget _buildFeatureCell(bool isAllowed) {
    return isAllowed
        ? const Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 20)
        : const Icon(
            Icons.cancel,
            color: Colors.red,
            size: 20);
  }
}
