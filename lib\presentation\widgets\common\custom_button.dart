import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';

/// زر مخصص للتطبيق
class CustomButton extends StatelessWidget {
  /// نص الزر
  final String text;

  /// دالة يتم استدعاؤها عند النقر على الزر
  final VoidCallback? onPressed;

  /// لون الزر
  final Color? color;

  /// لون النص
  final Color? textColor;

  /// حجم النص
  final double? fontSize;

  /// عرض الزر
  final double? width;

  /// ارتفاع الزر
  final double? height;

  /// نصف قطر الحواف
  final double? borderRadius;

  /// سماكة الخط
  final FontWeight? fontWeight;

  /// أيقونة الزر (اختياري)
  final IconData? icon;

  /// ما إذا كان الزر معطل
  final bool isDisabled;

  /// ما إذا كان الزر في حالة تحميل
  final bool isLoading;

  /// نوع الزر (أساسي، ثانوي، إلخ)
  final ButtonType type;

  /// حجم الزر
  final ButtonSize size;

  /// المسافة الداخلية
  final EdgeInsetsGeometry? padding;

  /// المسافة الخارجية
  final EdgeInsetsGeometry? margin;

  /// إنشاء زر مخصص
  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.color,
    this.textColor,
    this.fontSize,
    this.width,
    this.height,
    this.borderRadius,
    this.fontWeight,
    this.icon,
    this.isDisabled = false,
    this.isLoading = false,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان حسب نوع الزر
    final buttonColor = _getButtonColor();
    final buttonTextColor = _getTextColor();
    
    // تحديد الحجم حسب نوع الزر
    final buttonHeight = height ?? _getButtonHeight();
    final buttonWidth = width;
    final buttonFontSize = fontSize ?? _getButtonFontSize();
    final buttonFontWeight = fontWeight ?? FontWeight.bold;
    final buttonBorderRadius = borderRadius ?? 8.0;
    
    // تحديد المسافة الداخلية
    final buttonPadding = padding ?? _getButtonPadding();
    
    return Container(
      width: buttonWidth,
      height: buttonHeight,
      margin: margin,
      child: ElevatedButton(
        onPressed: (isDisabled || isLoading) ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          foregroundColor: buttonTextColor,
          padding: buttonPadding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonBorderRadius),
            side: type == ButtonType.outline
                ? BorderSide(color: AppColors.primary)
                : BorderSide.none),
          elevation: type == ButtonType.flat ? 0 : 2,
          disabledBackgroundColor: type == ButtonType.outline
              ? Colors.transparent
              : Colors.grey.shade300,
          disabledForegroundColor: Colors.grey.shade600),
        child: _buildButtonContent(buttonTextColor, buttonFontSize, buttonFontWeight)));
  }

  /// بناء محتوى الزر (نص وأيقونة)
  Widget _buildButtonContent(Color textColor, double fontSize, FontWeight fontWeight) {
    if (isLoading) {
      return SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            type == ButtonType.outline ? AppColors.primary : Colors.white)));
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: fontSize + 2),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: fontWeight,
              color: textColor)),
        ]);
    }

    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: textColor));
  }

  /// الحصول على لون الزر حسب النوع
  Color _getButtonColor() {
    if (isDisabled) {
      return type == ButtonType.outline
          ? Colors.transparent
          : Colors.grey.shade300;
    }

    switch (type) {
      case ButtonType.primary:
        return color ?? AppColors.primary;
      case ButtonType.secondary:
        return color ?? Colors.grey.shade200;
      case ButtonType.success:
        return color ?? Colors.green;
      case ButtonType.danger:
        return color ?? Colors.red;
      case ButtonType.warning:
        return color ?? Colors.orange;
      case ButtonType.info:
        return color ?? Colors.blue;
      case ButtonType.light:
        return color ?? Colors.white;
      case ButtonType.dark:
        return color ?? Colors.black87;
      case ButtonType.outline:
        return color ?? Colors.transparent;
      case ButtonType.flat:
        return color ?? Colors.transparent;
    }
  }

  /// الحصول على لون النص حسب النوع
  Color _getTextColor() {
    if (isDisabled) {
      return Colors.grey.shade600;
    }

    if (textColor != null) {
      return textColor!;
    }

    switch (type) {
      case ButtonType.primary:
        return Colors.white;
      case ButtonType.secondary:
        return Colors.black87;
      case ButtonType.success:
        return Colors.white;
      case ButtonType.danger:
        return Colors.white;
      case ButtonType.warning:
        return Colors.white;
      case ButtonType.info:
        return Colors.white;
      case ButtonType.light:
        return Colors.black87;
      case ButtonType.dark:
        return Colors.white;
      case ButtonType.outline:
        return AppColors.primary;
      case ButtonType.flat:
        return AppColors.primary;
    }
  }

  /// الحصول على ارتفاع الزر حسب الحجم
  double _getButtonHeight() {
    switch (size) {
      case ButtonSize.small:
        return 36;
      case ButtonSize.medium:
        return 48;
      case ButtonSize.large:
        return 56;
    }
  }

  /// الحصول على حجم الخط حسب حجم الزر
  double _getButtonFontSize() {
    switch (size) {
      case ButtonSize.small:
        return 12;
      case ButtonSize.medium:
        return 14;
      case ButtonSize.large:
        return 16;
    }
  }

  /// الحصول على المسافة الداخلية حسب حجم الزر
  EdgeInsetsGeometry _getButtonPadding() {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
    }
  }
}

/// أنواع الأزرار
enum ButtonType {
  /// زر أساسي
  primary,

  /// زر ثانوي
  secondary,

  /// زر نجاح
  success,

  /// زر خطر
  danger,

  /// زر تحذير
  warning,

  /// زر معلومات
  info,

  /// زر فاتح
  light,

  /// زر داكن
  dark,

  /// زر محيط
  outline,

  /// زر مسطح
  flat,
}

/// أحجام الأزرار
enum ButtonSize {
  /// صغير
  small,

  /// متوسط
  medium,

  /// كبير
  large,
}
