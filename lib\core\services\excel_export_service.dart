import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import 'package:intl/intl.dart';

/// خدمة تصدير التقارير إلى Excel
class ExcelExportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// تصدير تقرير المبيعات
  static Future<List<Map<String, dynamic>>> getSalesReportData() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    final snapshot = await _firestore
        .collection('estates')
        .where('companyId', isEqualTo: currentUser.uid)
        .where('status', isEqualTo: 'sold')
        .get();

    return snapshot.docs.map((doc) {
      final data = doc.data();
      return {
        'العقار': data['title'] ?? 'غير محدد',
        'السعر': '${data['price'] ?? 0} د.ك',
        'المنطقة': data['area'] ?? 'غير محدد',
        'النوع': data['subCategory'] ?? 'غير محدد',
        'تاريخ البيع': data['soldAt'] != null
            ? DateFormat('yyyy-MM-dd').format((data['soldAt'] as Timestamp).toDate())
            : 'غير محدد',
        'العمولة': '${((data['price'] ?? 0) * 0.03).toStringAsFixed(0)} د.ك',
      };
    }).toList();
  }

  /// تصدير تقرير العقارات
  static Future<List<Map<String, dynamic>>> getPropertiesReportData() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    final snapshot = await _firestore
        .collection('estates')
        .where('companyId', isEqualTo: currentUser.uid)
        .get();

    return snapshot.docs.map((doc) {
      final data = doc.data();
      return {
        'العقار': data['title'] ?? 'غير محدد',
        'السعر': '${data['price'] ?? 0} د.ك',
        'المنطقة': data['area'] ?? 'غير محدد',
        'النوع': data['subCategory'] ?? 'غير محدد',
        'الحالة': _getStatusLabel(data['status'] ?? 'available'),
        'تاريخ الإضافة': data['createdAt'] != null
            ? DateFormat('yyyy-MM-dd').format((data['createdAt'] as Timestamp).toDate())
            : 'غير محدد',
        'المساحة': '${data['size'] ?? 0} م²',
        'الغرف': data['rooms'] ?? 0,
      };
    }).toList();
  }

  /// تصدير تقرير العملاء
  static Future<List<Map<String, dynamic>>> getClientsReportData({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    Query query = _firestore
        .collection('clients')
        .where('agentId', isEqualTo: currentUser.uid);

    // تطبيق الفلتر الزمني إذا تم تحديده
    if (startDate != null && endDate != null) {
      query = query
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('createdAt', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
    }

    final snapshot = await query.get();

    return snapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>?;
      if (data == null) return <String, dynamic>{};

      return {
        'الاسم': data['name'] ?? 'غير محدد',
        'الهاتف': data['phone'] ?? 'غير محدد',
        'البريد الإلكتروني': data['email'] ?? 'غير محدد',
        'النوع': _getClientTypeLabel(data['type'] ?? 'unknown'),
        'الحالة': _getClientStatusLabel(data['status'] ?? 'active'),
        'الاهتمام': data['interest'] ?? 'غير محدد',
        'الميزانية': '${data['budget'] ?? 0} د.ك',
        'تاريخ التسجيل': data['createdAt'] != null
            ? DateFormat('yyyy-MM-dd').format((data['createdAt'] as Timestamp).toDate())
            : 'غير محدد',
        'VIP': data['isVip'] == true ? 'نعم' : 'لا',
      };
    }).where((item) => item.isNotEmpty).toList();
  }

  /// تصدير التقرير المالي
  static Future<List<Map<String, dynamic>>> getFinancialReportData() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    // جمع بيانات المبيعات
    final salesSnapshot = await _firestore
        .collection('estates')
        .where('companyId', isEqualTo: currentUser.uid)
        .where('status', isEqualTo: 'sold')
        .get();

    // جمع بيانات الإيجارات
    final rentalsSnapshot = await _firestore
        .collection('estates')
        .where('companyId', isEqualTo: currentUser.uid)
        .where('status', isEqualTo: 'rented')
        .get();

    List<Map<String, dynamic>> financialData = [];

    // إضافة بيانات المبيعات
    for (var doc in salesSnapshot.docs) {
      final data = doc.data();
      final price = (data['price'] ?? 0).toDouble();
      final commission = price * 0.03; // 3% عمولة

      financialData.add({
        'النوع': 'مبيعات',
        'العقار': data['title'] ?? 'غير محدد',
        'القيمة': '${price.toStringAsFixed(0)} د.ك',
        'العمولة': '${commission.toStringAsFixed(0)} د.ك',
        'التاريخ': data['soldAt'] != null
            ? DateFormat('yyyy-MM-dd').format((data['soldAt'] as Timestamp).toDate())
            : 'غير محدد',
      });
    }

    // إضافة بيانات الإيجارات
    for (var doc in rentalsSnapshot.docs) {
      final data = doc.data();
      final price = (data['price'] ?? 0).toDouble();
      final commission = price * 0.05; // 5% عمولة

      financialData.add({
        'النوع': 'إيجارات',
        'العقار': data['title'] ?? 'غير محدد',
        'القيمة': '${price.toStringAsFixed(0)} د.ك',
        'العمولة': '${commission.toStringAsFixed(0)} د.ك',
        'التاريخ': data['rentedAt'] != null
            ? DateFormat('yyyy-MM-dd').format((data['rentedAt'] as Timestamp).toDate())
            : 'غير محدد',
      });
    }

    return financialData;
  }

  /// إنشاء ومشاركة ملف Excel لتقرير واحد
  static Future<void> createAndShareExcel(String reportName, List<Map<String, dynamic>> data) async {
    if (data.isEmpty) {
      throw Exception('لا توجد بيانات للتصدير');
    }

    final xlsio.Workbook workbook = xlsio.Workbook();
    final xlsio.Worksheet worksheet = workbook.worksheets[0];
    worksheet.name = reportName;

    // إعداد العناوين
    final headers = data.first.keys.toList();
    for (int i = 0; i < headers.length; i++) {
      final cell = worksheet.getRangeByIndex(1, i + 1);
      cell.setText(headers[i]);
      cell.cellStyle.backColor = '#4CAF50';
      cell.cellStyle.fontColor = '#FFFFFF';
      cell.cellStyle.bold = true;
    }

    // إضافة البيانات
    for (int row = 0; row < data.length; row++) {
      final rowData = data[row];
      for (int col = 0; col < headers.length; col++) {
        final cell = worksheet.getRangeByIndex(row + 2, col + 1);
        cell.setText(rowData[headers[col]]?.toString() ?? '');
      }
    }

    // ضبط عرض الأعمدة
    for (int i = 1; i <= headers.length; i++) {
      worksheet.autoFitColumn(i);
    }

    // حفظ ومشاركة الملف
    await _saveAndShareWorkbook(workbook, reportName);
  }

  /// إنشاء ومشاركة ملف Excel متعدد الأوراق
  static Future<void> createAndShareMultiSheetExcel(Map<String, List<Map<String, dynamic>>> allReportsData) async {
    final xlsio.Workbook workbook = xlsio.Workbook();

    // حذف الورقة الافتراضية
    workbook.worksheets.clear();

    bool hasData = false;
    for (var entry in allReportsData.entries) {
      final reportName = entry.key;
      final data = entry.value;

      if (data.isEmpty) continue;

      final worksheet = workbook.worksheets.add();
      worksheet.name = reportName;

      // إعداد العناوين
      final headers = data.first.keys.toList();
      for (int i = 0; i < headers.length; i++) {
        final cell = worksheet.getRangeByIndex(1, i + 1);
        cell.setText(headers[i]);
        cell.cellStyle.backColor = '#4CAF50';
        cell.cellStyle.fontColor = '#FFFFFF';
        cell.cellStyle.bold = true;
      }

      // إضافة البيانات
      for (int row = 0; row < data.length; row++) {
        final rowData = data[row];
        for (int col = 0; col < headers.length; col++) {
          final cell = worksheet.getRangeByIndex(row + 2, col + 1);
          cell.setText(rowData[headers[col]]?.toString() ?? '');
        }
      }

      // ضبط عرض الأعمدة
      for (int i = 1; i <= headers.length; i++) {
        worksheet.autoFitColumn(i);
      }

      hasData = true;
    }

    if (!hasData) {
      throw Exception('لا توجد بيانات للتصدير');
    }

    // حفظ ومشاركة الملف
    await _saveAndShareWorkbook(workbook, 'جميع التقارير');
  }

  /// حفظ ومشاركة ملف Excel
  static Future<void> _saveAndShareWorkbook(xlsio.Workbook workbook, String fileName) async {
    final List<int> bytes = workbook.saveAsStream();
    workbook.dispose();

    final directory = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final file = File('${directory.path}/${fileName}_$timestamp.xlsx');
    await file.writeAsBytes(bytes);

    await Share.shareXFiles(
      [XFile(file.path)],
      text: '$fileName - ${DateFormat('yyyy-MM-dd').format(DateTime.now())}',
    );
  }

  /// تسميات الحالات
  static String _getStatusLabel(String status) {
    switch (status) {
      case 'available': return 'متاح';
      case 'sold': return 'مباع';
      case 'rented': return 'مؤجر';
      case 'pending': return 'قيد المراجعة';
      default: return 'غير محدد';
    }
  }

  /// تسميات أنواع العملاء
  static String _getClientTypeLabel(String type) {
    switch (type) {
      case 'buyer': return 'مشتري';
      case 'seller': return 'بائع';
      case 'renter': return 'مستأجر';
      case 'landlord': return 'مؤجر';
      default: return 'غير محدد';
    }
  }

  /// تسميات حالات العملاء
  static String _getClientStatusLabel(String status) {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'lead': return 'عميل محتمل';
      case 'converted': return 'تم التحويل';
      default: return 'غير محدد';
    }
  }
}
