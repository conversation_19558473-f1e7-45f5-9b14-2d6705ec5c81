import 'package:kuwait_corners/core/services/loyalty_program_service.dart';
import '../entities/estate.dart';
import '../repositories/estate_repository.dart';

/// Use case class for updating an existing estate record.
/// This class delegates the update operation to the EstateRepository.
class UpdateEstate {
  /// The repository used to perform estate-related operations.
  final EstateRepository repository;

  /// The loyalty program service used to add points for updating an estate.
  final LoyaltyProgramService loyaltyService;

  /// Constructs an [UpdateEstate] instance with the provided [repository] and optional [loyaltyService].
  UpdateEstate(this.repository, {LoyaltyProgramService? loyaltyService})
      : loyaltyService = loyaltyService ?? LoyaltyProgramService();

  /// Calls the repository's [updateEstate] method to update the provided [estate].
  Future<void> call(Estate estate) async {
    try {
      // Implementación actual: usar updateEstateLegacy para compatibilidad
      await repository.updateEstateLegacy(estate);

      // Añadir puntos por actualizar un anuncio
      await loyaltyService.addPointsForUpdatingAd();
    } catch (e) {
      // Registrar el error pero no propagarlo
      print("Error updating estate: $e");
      rethrow;
    }
  }
}
