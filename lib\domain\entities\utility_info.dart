import 'package:equatable/equatable.dart';

/// كيان معلومات المرافق
class UtilityInfo extends Equatable {
  final List<UtilityProvider> waterProviders;
  final List<UtilityProvider> electricityProviders;
  final List<UtilityProvider> gasProviders;
  final List<UtilityProvider> internetProviders;
  final List<UtilityProvider> sewageProviders;
  final Map<String, dynamic> serviceQualityScores;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان معلومات المرافق
  const UtilityInfo({
    required this.waterProviders,
    required this.electricityProviders,
    required this.gasProviders,
    required this.internetProviders,
    required this.sewageProviders,
    required this.serviceQualityScores,
    this.additionalInfo,
  });

  /// إنشاء كيان معلومات المرافق من JSON
  factory UtilityInfo.fromJson(Map<String, dynamic> json) {
    return UtilityInfo(
      waterProviders: (json['waterProviders'] as List)
          .map((provider) => UtilityProvider.fromJson(provider))
          .toList(),
      electricityProviders: (json['electricityProviders'] as List)
          .map((provider) => UtilityProvider.fromJson(provider))
          .toList(),
      gasProviders: (json['gasProviders'] as List)
          .map((provider) => UtilityProvider.fromJson(provider))
          .toList(),
      internetProviders: (json['internetProviders'] as List)
          .map((provider) => UtilityProvider.fromJson(provider))
          .toList(),
      sewageProviders: (json['sewageProviders'] as List)
          .map((provider) => UtilityProvider.fromJson(provider))
          .toList(),
      serviceQualityScores: json['serviceQualityScores'] as Map<String, dynamic>,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان معلومات المرافق إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'waterProviders': waterProviders.map((provider) => provider.toJson()).toList(),
      'electricityProviders': electricityProviders.map((provider) => provider.toJson()).toList(),
      'gasProviders': gasProviders.map((provider) => provider.toJson()).toList(),
      'internetProviders': internetProviders.map((provider) => provider.toJson()).toList(),
      'sewageProviders': sewageProviders.map((provider) => provider.toJson()).toList(),
      'serviceQualityScores': serviceQualityScores,
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان معلومات المرافق مع تعديل بعض الخصائص
  UtilityInfo copyWith({
    List<UtilityProvider>? waterProviders,
    List<UtilityProvider>? electricityProviders,
    List<UtilityProvider>? gasProviders,
    List<UtilityProvider>? internetProviders,
    List<UtilityProvider>? sewageProviders,
    Map<String, dynamic>? serviceQualityScores,
    Map<String, dynamic>? additionalInfo,
  }) {
    return UtilityInfo(
      waterProviders: waterProviders ?? this.waterProviders,
      electricityProviders: electricityProviders ?? this.electricityProviders,
      gasProviders: gasProviders ?? this.gasProviders,
      internetProviders: internetProviders ?? this.internetProviders,
      sewageProviders: sewageProviders ?? this.sewageProviders,
      serviceQualityScores: serviceQualityScores ?? this.serviceQualityScores,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// الحصول على مزودي الخدمة حسب النوع
  List<UtilityProvider> getProvidersByType(String type) {
    switch (type) {
      case 'water':
        return waterProviders;
      case 'electricity':
        return electricityProviders;
      case 'gas':
        return gasProviders;
      case 'internet':
        return internetProviders;
      case 'sewage':
        return sewageProviders;
      default:
        return [];
    }
  }

  /// الحصول على درجة جودة الخدمة حسب النوع
  int getServiceQualityScore(String type) {
    return serviceQualityScores[type] as int? ?? 0;
  }

  /// الحصول على متوسط درجة جودة الخدمة
  double getAverageServiceQualityScore() {
    final scores = serviceQualityScores.values.map((score) => score as int).toList();
    if (scores.isEmpty) {
      return 0.0;
    }
    return scores.reduce((a, b) => a + b) / scores.length;
  }

  @override
  List<Object?> get props => [
        waterProviders,
        electricityProviders,
        gasProviders,
        internetProviders,
        sewageProviders,
        serviceQualityScores,
        additionalInfo,
      ];
}

/// كيان مزود خدمة المرافق
class UtilityProvider extends Equatable {
  final String id;
  final String name;
  final String type;
  final String logoUrl;
  final String website;
  final String phoneNumber;
  final String email;
  final double rating;
  final int reviewsCount;
  final Map<String, dynamic> coverage;
  final Map<String, dynamic> pricing;
  final Map<String, dynamic>? serviceDetails;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان مزود خدمة المرافق
  const UtilityProvider({
    required this.id,
    required this.name,
    required this.type,
    required this.logoUrl,
    required this.website,
    required this.phoneNumber,
    required this.email,
    required this.rating,
    required this.reviewsCount,
    required this.coverage,
    required this.pricing,
    this.serviceDetails,
    this.additionalInfo,
  });

  /// إنشاء كيان مزود خدمة المرافق من JSON
  factory UtilityProvider.fromJson(Map<String, dynamic> json) {
    return UtilityProvider(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      logoUrl: json['logoUrl'] as String,
      website: json['website'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String,
      rating: json['rating'] as double,
      reviewsCount: json['reviewsCount'] as int,
      coverage: json['coverage'] as Map<String, dynamic>,
      pricing: json['pricing'] as Map<String, dynamic>,
      serviceDetails: json['serviceDetails'] as Map<String, dynamic>?,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان مزود خدمة المرافق إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'logoUrl': logoUrl,
      'website': website,
      'phoneNumber': phoneNumber,
      'email': email,
      'rating': rating,
      'reviewsCount': reviewsCount,
      'coverage': coverage,
      'pricing': pricing,
      'serviceDetails': serviceDetails,
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان مزود خدمة المرافق مع تعديل بعض الخصائص
  UtilityProvider copyWith({
    String? id,
    String? name,
    String? type,
    String? logoUrl,
    String? website,
    String? phoneNumber,
    String? email,
    double? rating,
    int? reviewsCount,
    Map<String, dynamic>? coverage,
    Map<String, dynamic>? pricing,
    Map<String, dynamic>? serviceDetails,
    Map<String, dynamic>? additionalInfo,
  }) {
    return UtilityProvider(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      logoUrl: logoUrl ?? this.logoUrl,
      website: website ?? this.website,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      coverage: coverage ?? this.coverage,
      pricing: pricing ?? this.pricing,
      serviceDetails: serviceDetails ?? this.serviceDetails,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// الحصول على وصف نوع الخدمة
  String getTypeDescription() {
    switch (type) {
      case 'water':
        return 'مياه';
      case 'electricity':
        return 'كهرباء';
      case 'gas':
        return 'غاز';
      case 'internet':
        return 'إنترنت';
      case 'sewage':
        return 'صرف صحي';
      default:
        return 'مرفق';
    }
  }

  /// التحقق مما إذا كانت الخدمة متوفرة في المنطقة
  bool isAvailableInArea(String areaCode) {
    final areas = coverage['areas'] as List<dynamic>?;
    return areas?.contains(areaCode) ?? false;
  }

  /// الحصول على سعر الخدمة
  double getServicePrice(String serviceType) {
    final prices = pricing['prices'] as Map<String, dynamic>?;
    return prices?[serviceType] as double? ?? 0.0;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        logoUrl,
        website,
        phoneNumber,
        email,
        rating,
        reviewsCount,
        coverage,
        pricing,
        serviceDetails,
        additionalInfo,
      ];
}
