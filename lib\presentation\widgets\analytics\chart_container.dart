import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/theme/app_colors.dart';

/// حاوية الرسم البياني
class ChartContainer extends StatelessWidget {
  final String title;
  final Widget chart;
  final double height;
  final String? subtitle;
  final VoidCallback? onTap;

  /// إنشاء حاوية الرسم البياني
  const ChartContainer({
    super.key,
    required this.title,
    required this.chart,
    this.height = 200,
    this.subtitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الرسم البياني
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
              if (onTap != null)
                IconButton(
                  icon: const Icon(Icons.fullscreen, size: 20),
                  onPressed: onTap,
                  tooltip: 'عرض بالحجم الكامل'),
            ]),

          // العنوان الفرعي
          if (subtitle != null)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                subtitle!,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600]))),

          const SizedBox(height: 16),

          // الرسم البياني
          SizedBox(
            height: height,
            child: chart),
        ]));
  }
}

/// حاوية الرسم البياني الخطي
class LineChartContainer extends StatelessWidget {
  final String title;
  final List<FlSpot> spots;
  final double height;
  final String? subtitle;
  final Color color;
  final bool showDots;
  final bool showArea;
  final VoidCallback? onTap;

  /// إنشاء حاوية الرسم البياني الخطي
  const LineChartContainer({
    super.key,
    required this.title,
    required this.spots,
    this.height = 200,
    this.subtitle,
    this.color = AppColors.primary,
    this.showDots = true,
    this.showArea = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ChartContainer(
      title: title,
      subtitle: subtitle,
      height: height,
      onTap: onTap,
      chart: LineChart(
        LineChartData(
          gridData: FlGridData(show: true),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 22)),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 28))),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: const Color(0xff37434d), width: 1)),
          minX: 0,
          maxX: spots.length.toDouble() - 1,
          minY: 0,
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: color,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: showDots),
              belowBarData: BarAreaData(
                show: showArea,
                color: color.withAlpha(76))),
          ])));
  }
}

/// حاوية الرسم البياني الشريطي
class BarChartContainer extends StatelessWidget {
  final String title;
  final List<double> values;
  final List<String> labels;
  final double height;
  final String? subtitle;
  final Color color;
  final VoidCallback? onTap;

  /// إنشاء حاوية الرسم البياني الشريطي
  const BarChartContainer({
    super.key,
    required this.title,
    required this.values,
    required this.labels,
    this.height = 200,
    this.subtitle,
    this.color = AppColors.primary,
    this.onTap,
  }) : assert(values.length == labels.length,
            'يجب أن يكون عدد القيم مساويًا لعدد التسميات');

  @override
  Widget build(BuildContext context) {
    return ChartContainer(
      title: title,
      subtitle: subtitle,
      height: height,
      onTap: onTap,
      chart: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: values.reduce((a, b) => a > b ? a : b) * 1.2,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              // tooltipBgColor parameter has been renamed in newer versions
              // tooltipBgColor: Colors.blueGrey,
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                return BarTooltipItem(
                  '${labels[groupIndex]}\n',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14),
                  children: <TextSpan>[
                    TextSpan(
                      text: values[groupIndex].toString(),
                      style: const TextStyle(
                        color: Colors.yellow,
                        fontSize: 16,
                        fontWeight: FontWeight.w500)),
                  ]);
              })),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 22)),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 28))),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: const Color(0xff37434d), width: 1)),
          barGroups: List.generate(
            values.length,
            (index) {
              return BarChartGroupData(
                x: index,
                barRods: [
                  BarChartRodData(
                    toY: values[index],
                    color: color,
                    width: 22,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(6),
                      topRight: Radius.circular(6))),
                ]);
            }))));
  }
}

/// حاوية الرسم البياني الدائري
class PieChartContainer extends StatelessWidget {
  final String title;
  final List<double> values;
  final List<String> labels;
  final double height;
  final String? subtitle;
  final List<Color>? colors;
  final VoidCallback? onTap;

  /// إنشاء حاوية الرسم البياني الدائري
  const PieChartContainer({
    super.key,
    required this.title,
    required this.values,
    required this.labels,
    this.height = 200,
    this.subtitle,
    this.colors,
    this.onTap,
  }) : assert(values.length == labels.length,
            'يجب أن يكون عدد القيم مساويًا لعدد التسميات');

  @override
  Widget build(BuildContext context) {
    final defaultColors = [
      AppColors.primary,
      AppColors.error,
      AppColors.success,
      AppColors.warning,
      AppColors.primaryDark,
      AppColors.secondary,
      AppColors.primaryLight,
      Colors.orange,
      Colors.purple,
      Colors.amber,
    ];

    return ChartContainer(
      title: title,
      subtitle: subtitle,
      height: height,
      onTap: onTap,
      chart: PieChart(
        PieChartData(
          borderData: FlBorderData(show: false),
          sectionsSpace: 0,
          centerSpaceRadius: 40,
          sections: List.generate(
            values.length,
            (index) {
              final color = colors != null && index < colors!.length
                  ? colors![index]
                  : defaultColors[index % defaultColors.length];

              return PieChartSectionData(
                color: color,
                value: values[index],
                title: '${labels[index]}\n${values[index].toStringAsFixed(1)}%',
                radius: 50,
                titleStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white));
            }))));
  }
}
