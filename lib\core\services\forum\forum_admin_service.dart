import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../../../domain/models/forum/user_role_model.dart';
import '../../../domain/models/forum/category_model.dart';

/// خدمة إدارة المنتدى
class ForumAdminService {
  /// مرجع Firestore
  final FirebaseFirestore _firestore;

  /// مرجع Firebase Storage
  final FirebaseStorage _storage;

  /// Constructor
  ForumAdminService({
    FirebaseFirestore? firestore,
    FirebaseStorage? storage,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _storage = storage ?? FirebaseStorage.instance;

  /// التحقق من صلاحيات المستخدم
  Future<bool> checkUserPermission(String userId, String permission) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final userData = userDoc.data();
      
      if (userData == null) {
        return false;
      }
      
      final roleId = userData['forumRole'] as String? ?? 'user';
      final role = UserRoleModel.getRoleById(roleId);
      
      if (role == null) {
        return false;
      }
      
      // التحقق من الصلاحية المطلوبة
      switch (permission) {
        case 'canCreateTopics':
          return role.permissions.canCreateTopics;
        case 'canReplyToTopics':
          return role.permissions.canReplyToTopics;
        case 'canEditOwnContent':
          return role.permissions.canEditOwnContent;
        case 'canDeleteOwnContent':
          return role.permissions.canDeleteOwnContent;
        case 'canLikeContent':
          return role.permissions.canLikeContent;
        case 'canReportContent':
          return role.permissions.canReportContent;
        case 'canFollowUsers':
          return role.permissions.canFollowUsers;
        case 'canFollowTopics':
          return role.permissions.canFollowTopics;
        case 'canParticipateInPolls':
          return role.permissions.canParticipateInPolls;
        case 'canViewStatistics':
          return role.permissions.canViewStatistics;
        case 'canCreatePolls':
          return role.permissions.canCreatePolls;
        case 'canUploadFiles':
          return role.permissions.canUploadFiles;
        case 'canCreateFeaturedTopics':
          return role.permissions.canCreateFeaturedTopics;
        case 'canCustomizeProfile':
          return role.permissions.canCustomizeProfile;
        case 'canPinOwnTopics':
          return role.permissions.canPinOwnTopics;
        case 'canCreateAnnouncements':
          return role.permissions.canCreateAnnouncements;
        case 'canSeeReportedContent':
          return role.permissions.canSeeReportedContent;
        case 'canEditOtherContent':
          return role.permissions.canEditOtherContent;
        case 'canDeleteOtherContent':
          return role.permissions.canDeleteOtherContent;
        case 'canPinAnyTopic':
          return role.permissions.canPinAnyTopic;
        case 'canBanUsers':
          return role.permissions.canBanUsers;
        case 'canApproveContent':
          return role.permissions.canApproveContent;
        case 'canManageCategories':
          return role.permissions.canManageCategories;
        case 'canManageRoles':
          return role.permissions.canManageRoles;
        case 'canAccessAdminPanel':
          return role.permissions.canAccessAdminPanel;
        case 'canManageSettings':
          return role.permissions.canManageSettings;
        case 'canExportData':
          return role.permissions.canExportData;
        case 'canManageAdmins':
          return role.permissions.canManageAdmins;
        case 'canDeleteForum':
          return role.permissions.canDeleteForum;
        default:
          return false;
      }
    } catch (e) {
      print('فشل في التحقق من صلاحيات المستخدم: $e');
      return false;
    }
  }

  /// تغيير دور المستخدم
  Future<void> changeUserRole(String adminId, String userId, String roleId) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canManageRoles');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لتغيير أدوار المستخدمين');
      }
      
      // التحقق من وجود الدور
      final role = UserRoleModel.getRoleById(roleId);
      if (role == null) {
        throw Exception('الدور غير موجود');
      }
      
      // تحديث دور المستخدم
      await _firestore.collection('users').doc(userId).update({
        'forumRole': roleId,
        'forumRoleUpdatedAt': FieldValue.serverTimestamp(),
        'forumRoleUpdatedBy': adminId,
      });
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: 'تغيير دور المستخدم',
        details: 'تم تغيير دور المستخدم $userId إلى $roleId',
        targetId: userId,
        targetType: 'user');
    } catch (e) {
      print('فشل في تغيير دور المستخدم: $e');
      throw Exception('فشل في تغيير دور المستخدم: $e');
    }
  }

  /// حظر مستخدم
  Future<void> banUser(String adminId, String userId, String reason, int banDurationDays) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canBanUsers');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لحظر المستخدمين');
      }
      
      // حساب تاريخ انتهاء الحظر
      final banEndDate = DateTime.now().add(Duration(days: banDurationDays));
      
      // تحديث حالة المستخدم
      await _firestore.collection('users').doc(userId).update({
        'isBanned': true,
        'banReason': reason,
        'banStartDate': FieldValue.serverTimestamp(),
        'banEndDate': Timestamp.fromDate(banEndDate),
        'bannedBy': adminId,
      });
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: 'حظر مستخدم',
        details: 'تم حظر المستخدم $userId لمدة $banDurationDays يوم. السبب: $reason',
        targetId: userId,
        targetType: 'user');
    } catch (e) {
      print('فشل في حظر المستخدم: $e');
      throw Exception('فشل في حظر المستخدم: $e');
    }
  }

  /// إلغاء حظر مستخدم
  Future<void> unbanUser(String adminId, String userId) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canBanUsers');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لإلغاء حظر المستخدمين');
      }
      
      // تحديث حالة المستخدم
      await _firestore.collection('users').doc(userId).update({
        'isBanned': false,
        'banEndDate': FieldValue.serverTimestamp(),
        'unbannedBy': adminId,
        'unbannedAt': FieldValue.serverTimestamp(),
      });
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: 'إلغاء حظر مستخدم',
        details: 'تم إلغاء حظر المستخدم $userId',
        targetId: userId,
        targetType: 'user');
    } catch (e) {
      print('فشل في إلغاء حظر المستخدم: $e');
      throw Exception('فشل في إلغاء حظر المستخدم: $e');
    }
  }

  /// إنشاء فئة جديدة
  Future<String> createCategory(String adminId, CategoryModel category) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canManageCategories');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لإدارة الفئات');
      }
      
      // إنشاء الفئة
      final docRef = await _firestore.collection('forum_categories').add(category.toMap());
      
      // تحديث معرف الفئة
      await docRef.update({'id': docRef.id});
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: 'إنشاء فئة',
        details: 'تم إنشاء فئة جديدة: ${category.name}',
        targetId: docRef.id,
        targetType: 'category');
      
      return docRef.id;
    } catch (e) {
      print('فشل في إنشاء الفئة: $e');
      throw Exception('فشل في إنشاء الفئة: $e');
    }
  }

  /// تحديث فئة
  Future<void> updateCategory(String adminId, CategoryModel category) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canManageCategories');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لإدارة الفئات');
      }
      
      // تحديث الفئة
      await _firestore.collection('forum_categories').doc(category.id).update(category.toMap());
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: 'تحديث فئة',
        details: 'تم تحديث الفئة: ${category.name}',
        targetId: category.id,
        targetType: 'category');
    } catch (e) {
      print('فشل في تحديث الفئة: $e');
      throw Exception('فشل في تحديث الفئة: $e');
    }
  }

  /// حذف فئة
  Future<void> deleteCategory(String adminId, String categoryId) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canManageCategories');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لإدارة الفئات');
      }
      
      // الحصول على اسم الفئة قبل الحذف
      final categoryDoc = await _firestore.collection('forum_categories').doc(categoryId).get();
      final categoryName = categoryDoc.data()?['name'] ?? 'غير معروف';
      
      // حذف الفئة
      await _firestore.collection('forum_categories').doc(categoryId).delete();
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: 'حذف فئة',
        details: 'تم حذف الفئة: $categoryName',
        targetId: categoryId,
        targetType: 'category');
    } catch (e) {
      print('فشل في حذف الفئة: $e');
      throw Exception('فشل في حذف الفئة: $e');
    }
  }

  /// تثبيت موضوع
  Future<void> pinTopic(String adminId, String topicId, bool isPinned) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canPinAnyTopic');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لتثبيت المواضيع');
      }
      
      // تحديث الموضوع
      await _firestore.collection('forum_topics').doc(topicId).update({
        'isPinned': isPinned,
        'pinnedBy': isPinned ? adminId : null,
        'pinnedAt': isPinned ? FieldValue.serverTimestamp() : null,
      });
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: isPinned ? 'تثبيت موضوع' : 'إلغاء تثبيت موضوع',
        details: isPinned ? 'تم تثبيت الموضوع: $topicId' : 'تم إلغاء تثبيت الموضوع: $topicId',
        targetId: topicId,
        targetType: 'topic');
    } catch (e) {
      print('فشل في تثبيت الموضوع: $e');
      throw Exception('فشل في تثبيت الموضوع: $e');
    }
  }

  /// تمييز موضوع
  Future<void> featureTopic(String adminId, String topicId, bool isFeatured) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canEditOtherContent');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لتمييز المواضيع');
      }
      
      // تحديث الموضوع
      await _firestore.collection('forum_topics').doc(topicId).update({
        'isFeatured': isFeatured,
        'featuredBy': isFeatured ? adminId : null,
        'featuredAt': isFeatured ? FieldValue.serverTimestamp() : null,
      });
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: isFeatured ? 'تمييز موضوع' : 'إلغاء تمييز موضوع',
        details: isFeatured ? 'تم تمييز الموضوع: $topicId' : 'تم إلغاء تمييز الموضوع: $topicId',
        targetId: topicId,
        targetType: 'topic');
    } catch (e) {
      print('فشل في تمييز الموضوع: $e');
      throw Exception('فشل في تمييز الموضوع: $e');
    }
  }

  /// حذف موضوع
  Future<void> deleteTopic(String adminId, String topicId, String reason) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canDeleteOtherContent');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لحذف المواضيع');
      }
      
      // الحصول على معلومات الموضوع قبل الحذف
      final topicDoc = await _firestore.collection('forum_topics').doc(topicId).get();
      final topicData = topicDoc.data();
      final topicTitle = topicData?['title'] ?? 'غير معروف';
      final authorId = topicData?['authorId'] ?? 'غير معروف';
      
      // حذف الموضوع (تحديثه كمحذوف بدلاً من حذفه فعلياً)
      await _firestore.collection('forum_topics').doc(topicId).update({
        'isDeleted': true,
        'deletedBy': adminId,
        'deletedAt': FieldValue.serverTimestamp(),
        'deleteReason': reason,
      });
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: 'حذف موضوع',
        details: 'تم حذف الموضوع: $topicTitle. السبب: $reason',
        targetId: topicId,
        targetType: 'topic',
        affectedUserId: authorId);
    } catch (e) {
      print('فشل في حذف الموضوع: $e');
      throw Exception('فشل في حذف الموضوع: $e');
    }
  }

  /// حذف مشاركة
  Future<void> deletePost(String adminId, String postId, String reason) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canDeleteOtherContent');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لحذف المشاركات');
      }
      
      // الحصول على معلومات المشاركة قبل الحذف
      final postDoc = await _firestore.collection('forum_posts').doc(postId).get();
      final postData = postDoc.data();
      final topicId = postData?['topicId'] ?? 'غير معروف';
      final authorId = postData?['authorId'] ?? 'غير معروف';
      
      // حذف المشاركة (تحديثها كمحذوفة بدلاً من حذفها فعلياً)
      await _firestore.collection('forum_posts').doc(postId).update({
        'isDeleted': true,
        'deletedBy': adminId,
        'deletedAt': FieldValue.serverTimestamp(),
        'deleteReason': reason,
      });
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: 'حذف مشاركة',
        details: 'تم حذف المشاركة: $postId. السبب: $reason',
        targetId: postId,
        targetType: 'post',
        affectedUserId: authorId,
        relatedId: topicId);
    } catch (e) {
      print('فشل في حذف المشاركة: $e');
      throw Exception('فشل في حذف المشاركة: $e');
    }
  }

  /// معالجة تقرير
  Future<void> handleReport(String adminId, String reportId, String action, String notes) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canSeeReportedContent');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لمعالجة التقارير');
      }
      
      // الحصول على معلومات التقرير
      final reportDoc = await _firestore.collection('forum_reports').doc(reportId).get();
      final reportData = reportDoc.data();
      
      if (reportData == null) {
        throw Exception('التقرير غير موجود');
      }
      
      final reportType = reportData['contentType'] as String? ?? 'غير معروف';
      final contentId = reportData['contentId'] as String? ?? 'غير معروف';
      final reporterId = reportData['reporterId'] as String? ?? 'غير معروف';
      
      // تحديث حالة التقرير
      await _firestore.collection('forum_reports').doc(reportId).update({
        'status': action,
        'handledBy': adminId,
        'handledAt': FieldValue.serverTimestamp(),
        'adminNotes': notes,
      });
      
      // إذا كان الإجراء هو حذف المحتوى
      if (action == 'deleted') {
        if (reportType == 'topic') {
          await deleteTopic(adminId, contentId, 'تم الحذف بناءً على تقرير: ${reportData['reason']}');
        } else if (reportType == 'post') {
          await deletePost(adminId, contentId, 'تم الحذف بناءً على تقرير: ${reportData['reason']}');
        }
      }
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: 'معالجة تقرير',
        details: 'تم معالجة التقرير: $reportId. الإجراء: $action. الملاحظات: $notes',
        targetId: reportId,
        targetType: 'report',
        affectedUserId: reporterId,
        relatedId: contentId);
    } catch (e) {
      print('فشل في معالجة التقرير: $e');
      throw Exception('فشل في معالجة التقرير: $e');
    }
  }

  /// تصدير بيانات المنتدى
  Future<String> exportForumData(String adminId, List<String> collections) async {
    try {
      // التحقق من صلاحيات المدير
      final hasPermission = await checkUserPermission(adminId, 'canExportData');
      if (!hasPermission) {
        throw Exception('ليس لديك صلاحية لتصدير البيانات');
      }
      
      // إنشاء مرجع للملف في Firebase Storage
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'forum_export_$timestamp.json';
      final storageRef = _storage.ref().child('exports').child(fileName);
      
      // تجميع البيانات
      final exportData = <String, dynamic>{};
      
      for (final collection in collections) {
        final snapshot = await _firestore.collection(collection).get();
        exportData[collection] = snapshot.docs.map((doc) => doc.data()).toList();
      }
      
      // تحويل البيانات إلى JSON
      final jsonData = jsonEncode(exportData);
      
      // رفع الملف
      await storageRef.putString(jsonData, format: PutStringFormat.raw);
      
      // الحصول على رابط التنزيل
      final downloadUrl = await storageRef.getDownloadURL();
      
      // تسجيل العملية
      await _logAdminAction(
        adminId: adminId,
        action: 'تصدير بيانات',
        details: 'تم تصدير بيانات المنتدى. المجموعات: ${collections.join(', ')}',
        targetId: fileName,
        targetType: 'export');
      
      return downloadUrl;
    } catch (e) {
      print('فشل في تصدير البيانات: $e');
      throw Exception('فشل في تصدير البيانات: $e');
    }
  }

  /// تسجيل إجراء إداري
  Future<void> _logAdminAction({
    required String adminId,
    required String action,
    required String details,
    required String targetId,
    required String targetType,
    String? affectedUserId,
    String? relatedId,
  }) async {
    try {
      await _firestore.collection('forum_admin_logs').add({
        'adminId': adminId,
        'action': action,
        'details': details,
        'targetId': targetId,
        'targetType': targetType,
        'affectedUserId': affectedUserId,
        'relatedId': relatedId,
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('فشل في تسجيل الإجراء الإداري: $e');
    }
  }

  /// الحصول على سجل الإجراءات الإدارية
  Future<List<Map<String, dynamic>>> getAdminLogs({
    int limit = 50,
    String? adminId,
    String? action,
    String? targetType,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query query = _firestore.collection('forum_admin_logs')
          .orderBy('timestamp', descending: true);
      
      if (adminId != null) {
        query = query.where('adminId', isEqualTo: adminId);
      }
      
      if (action != null) {
        query = query.where('action', isEqualTo: action);
      }
      
      if (targetType != null) {
        query = query.where('targetType', isEqualTo: targetType);
      }
      
      if (startDate != null) {
        query = query.where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      
      if (endDate != null) {
        query = query.where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }
      
      query = query.limit(limit);
      
      final snapshot = await query.get();
      
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('فشل في الحصول على سجل الإجراءات الإدارية: $e');
      throw Exception('فشل في الحصول على سجل الإجراءات الإدارية: $e');
    }
  }
}

/// تحويل البيانات إلى JSON
String jsonEncode(Map<String, dynamic> data) {
  // هذه دالة مبسطة لتحويل البيانات إلى JSON
  // في التطبيق الحقيقي، يجب استخدام مكتبة dart:convert
  return data.toString();
}
