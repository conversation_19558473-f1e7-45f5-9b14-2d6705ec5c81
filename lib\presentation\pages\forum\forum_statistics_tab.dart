import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/user_statistics_model.dart';
import '../../../domain/models/forum/user_level_model.dart';
import '../../../domain/models/forum/badge_model.dart';
import '../../providers/forum_provider.dart';
import '../../providers/auth_provider.dart' as app_auth;
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';

/// علامة تبويب إحصائيات المنتدى
class ForumStatisticsTab extends StatefulWidget {
  const ForumStatisticsTab({super.key});

  @override
  State<ForumStatisticsTab> createState() => _ForumStatisticsTabState();
}

class _ForumStatisticsTabState extends State<ForumStatisticsTab>
    with AutomaticKeepAliveClientMixin {
  int _selectedTabIndex = 0;

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Consumer2<ForumProvider, app_auth.AuthProvider>(
      builder: (context, forumProvider, authProvider, child) {
        if (!authProvider.isLoggedIn) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.lock,
                  size: 64,
                  color: Colors.grey.shade400),
                SizedBox(height: 16),
                Text(
                  'يجب تسجيل الدخول لعرض الإحصائيات',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade700)),
                SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/login');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8))),
                  child: Text('تسجيل الدخول')),
              ]));
        }

        if (forumProvider.userStatisticsState == LoadingState.loading) {
          return Center(child: LoadingIndicator());
        }

        if (forumProvider.userStatisticsState == LoadingState.error) {
          return ErrorView(
            message: 'حدث خطأ في تحميل الإحصائيات',
            onRetry: () => forumProvider.fetchUserStatistics(
              authProvider.user!.uid));
        }

        final userStats = forumProvider.userStatistics;
        if (userStats == null) {
          return Center(
            child: Text('لا توجد إحصائيات متاحة'));
        }

        return DefaultTabController(
          length: 3,
          child: Column(
            children: [
              // ملخص المستوى والنقاط
              _buildLevelSummary(userStats),

              // شريط التبويب
              Container(
                color: Colors.white,
                child: TabBar(
                  onTap: (index) {
                    setState(() {
                      _selectedTabIndex = index;
                    });
                  },
                  indicatorColor: AppColors.primary,
                  labelColor: AppColors.primary,
                  unselectedLabelColor: Colors.grey,
                  tabs: [
                    Tab(text: 'الملخص'),
                    Tab(text: 'النشاطات'),
                    Tab(text: 'الإنجازات'),
                  ])),

              // محتوى التبويب
              Expanded(
                child: TabBarView(
                  children: [
                    _buildSummaryTab(userStats),
                    _buildActivitiesTab(userStats),
                    _buildAchievementsTab(userStats, authProvider.user!.uid),
                  ])),
            ]));
      });
  }

  /// بناء ملخص المستوى والنقاط
  Widget _buildLevelSummary(UserStatisticsModel userStats) {
    final currentLevel = UserLevelModel.getCurrentLevel(userStats.points);
    final nextLevel = UserLevelModel.getNextLevel(userStats.points);

    double progress = 0.0;
    if (nextLevel != null) {
      final pointsForCurrentLevel = currentLevel.requiredPoints;
      final pointsForNextLevel = nextLevel.requiredPoints;
      final pointsRange = pointsForNextLevel - pointsForCurrentLevel;
      final userPointsInRange = userStats.points - pointsForCurrentLevel;
      progress = userPointsInRange / pointsRange;
    } else {
      progress = 1.0; // أعلى مستوى
    }

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withAlpha(204),
          ])),
      child: Column(
        children: [
          Row(
            children: [
              // صورة المستوى
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(25),
                      blurRadius: 8,
                      offset: Offset(0, 2)),
                  ]),
                child: Center(
                  child: Text(
                    currentLevel.icon.codePoint.toString(),
                    style: TextStyle(
                      fontSize: 24)))),
              SizedBox(width: 16),

              // معلومات المستوى
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      currentLevel.name,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18)),
                    SizedBox(height: 4),
                    Text(
                      '${userStats.points} نقطة',
                      style: TextStyle(
                        color: Colors.white.withAlpha(230),
                        fontSize: 14)),
                    SizedBox(height: 8),

                    // شريط التقدم
                    LinearPercentIndicator(
                      lineHeight: 8,
                      percent: progress,
                      backgroundColor: Colors.white.withAlpha(77),
                      progressColor: Colors.white,
                      barRadius: Radius.circular(4),
                      padding: EdgeInsets.zero),

                    // المستوى التالي
                    if (nextLevel != null)
                      Padding(
                        padding: EdgeInsets.only(top: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'المستوى التالي: ${nextLevel.name}',
                              style: TextStyle(
                                color: Colors.white.withAlpha(230),
                                fontSize: 12)),
                            Text(
                              'متبقي ${nextLevel.requiredPoints - userStats.points} نقطة',
                              style: TextStyle(
                                color: Colors.white.withAlpha(230),
                                fontSize: 12)),
                          ])),
                  ])),
            ]),
        ]));
  }

  /// بناء علامة تبويب الملخص
  Widget _buildSummaryTab(UserStatisticsModel userStats) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: widget)),
            children: [
              // الشارات
              _buildBadgesSection(userStats),

              SizedBox(height: 24),

              // إحصائيات المنتدى
              _buildForumStatsSection(userStats),

              SizedBox(height: 24),

              // رسم بياني للنشاط
              _buildActivityChart(userStats),

              SizedBox(height: 24),

              // زر عرض إحصائيات المنتدى الكاملة
              _buildViewFullStatisticsButton(),
            ]))));
  }

  /// بناء قسم الشارات
  Widget _buildBadgesSection(UserStatisticsModel userStats) {
    final badges = userStats.badges
        .map((badgeId) => BadgeModel.getBadgeById(badgeId))
        .where((badge) => badge != null)
        .cast<BadgeModel>()
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الشارات',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        SizedBox(height: 16),
        if (badges.isEmpty)
          Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'لم تحصل على أي شارات بعد',
                style: TextStyle(
                  color: Colors.grey.shade600))))
        else
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: badges.map((badge) {
              return Column(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: badge.getColorObject().withAlpha(25),
                      border: Border.all(
                        color: badge.getColorObject(),
                        width: 2)),
                    child: Center(
                      child: Text(
                        badge.icon.codePoint.toString(),
                        style: TextStyle(
                          fontSize: 24,
                          color: badge.getColorObject())))),
                  SizedBox(height: 8),
                  Text(
                    badge.name,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold)),
                ]);
            }).toList()),
      ]);
  }

  /// بناء قسم إحصائيات المنتدى
  Widget _buildForumStatsSection(UserStatisticsModel userStats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات المنتدى',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'المواضيع',
              userStats.topicsCount.toString(),
              Icons.topic_outlined,
              AppColors.primary, // تغيير من الأزرق إلى الأخضر
            ),
            _buildStatCard(
              'المشاركات',
              userStats.postsCount.toString(),
              Icons.forum_outlined,
              AppColors.success, // استخدام لون النجاح الأخضر
            ),
            _buildStatCard(
              'الإعجابات المستلمة',
              (userStats.totalTopicLikes + userStats.totalPostLikes).toString(),
              Icons.favorite_outline,
              Colors.red),
            _buildStatCard(
              'الإعجابات المرسلة',
              userStats.totalLikesGiven.toString(),
              Icons.thumb_up_outlined,
              Colors.orange),
          ]),
      ]);
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 28),
            SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color)),
            SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700)),
          ])));
  }

  /// بناء رسم بياني للنشاط
  Widget _buildActivityChart(UserStatisticsModel userStats) {
    // بيانات مثال للرسم البياني
    final activityData = [
      FlSpot(0, userStats.topicsCount.toDouble()),
      FlSpot(1, userStats.postsCount.toDouble()),
      FlSpot(2, userStats.totalLikesGiven.toDouble()),
      FlSpot(
          3, (userStats.totalTopicLikes + userStats.totalPostLikes).toDouble()),
      FlSpot(
          4,
          userStats.viewsCount.toDouble() /
              10), // تقسيم على 10 للتناسب مع الرسم البياني
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نشاط المنتدى',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        SizedBox(height: 16),
        Container(
          height: 200,
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 10,
                offset: Offset(0, 5)),
            ]),
          child: LineChart(
            LineChartData(
              gridData: FlGridData(show: false),
              titlesData: FlTitlesData(
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false)),
                rightTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false)),
                topTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false)),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      String text = '';
                      switch (value.toInt()) {
                        case 0:
                          text = 'مواضيع';
                          break;
                        case 1:
                          text = 'ردود';
                          break;
                        case 2:
                          text = 'إعجابات';
                          break;
                        case 3:
                          text = 'تفاعلات';
                          break;
                        case 4:
                          text = 'مشاهدات';
                          break;
                      }
                      return Padding(
                        padding: EdgeInsets.only(top: 8),
                        child: Text(
                          text,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600)));
                    }))),
              borderData: FlBorderData(show: false),
              lineBarsData: [
                LineChartBarData(
                  spots: activityData,
                  isCurved: true,
                  color: AppColors.primary,
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(show: true),
                  belowBarData: BarAreaData(
                    show: true,
                    color: AppColors.primary.withAlpha(25))),
              ]))),
      ]);
  }

  /// بناء علامة تبويب النشاطات
  Widget _buildActivitiesTab(UserStatisticsModel userStats) {
    // TODO: تنفيذ علامة تبويب النشاطات
    return Center(
      child: Text('قريباً'));
  }

  /// بناء علامة تبويب الإنجازات
  Widget _buildAchievementsTab(UserStatisticsModel userStats, String userId) {
    // TODO: تنفيذ علامة تبويب الإنجازات
    return Center(
      child: Text('قريباً'));
  }

  /// بناء زر عرض إحصائيات المنتدى الكاملة
  Widget _buildViewFullStatisticsButton() {
    return Center(
      child: ElevatedButton.icon(
        onPressed: () {
          Navigator.pushNamed(context, '/forum/statistics');
        },
        icon: Icon(Icons.bar_chart),
        label: Text('عرض إحصائيات المنتدى الكاملة'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8)))));
  }
}
