import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../core/theme/app_colors.dart';
import '../../domain/entities/estate.dart';
import '../../domain/services/estate_copy_service.dart';
import '../../domain/services/paid_ads_service.dart';

/// صفحة إدارة العقارات المنسوخة للمستثمرين
class CopiedEstatesManagementPage extends StatefulWidget {
  const CopiedEstatesManagementPage({super.key});

  @override
  State<CopiedEstatesManagementPage> createState() => _CopiedEstatesManagementPageState();
}

class _CopiedEstatesManagementPageState extends State<CopiedEstatesManagementPage>
    with SingleTickerProviderStateMixin {
  final EstateCopyService _copyService = EstateCopyService();
  final PaidAdsService _paidAdsService = PaidAdsService();

  late TabController _tabController;
  List<Estate> _copiedEstates = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل العقارات المنسوخة
      _copiedEstates = await _copyService.getCopiedEstates('current_user_id');

      // تحميل الإحصائيات
      _statistics = await _copyService.getCopyStatistics('current_user_id');
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل البيانات: $e'),
          backgroundColor: Colors.red));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          'العقارات المنسوخة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white)),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
          tabs: const [
            Tab(text: 'الإحصائيات'),
            Tab(text: 'العقارات النشطة'),
            Tab(text: 'العقارات المنتهية'),
          ])),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildStatisticsTab(),
                _buildActiveEstatesTab(),
                _buildExpiredEstatesTab(),
              ]));
  }

  /// بناء تبويب الإحصائيات
  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(),
          const SizedBox(height: 24),
          _buildRecentActivity(),
        ]));
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatisticsCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي المنسوخ',
                '${_statistics['totalCopied'] ?? 0}',
                Icons.content_copy,
                Colors.blue)),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'الإعلانات النشطة',
                '${_statistics['activeAds'] ?? 0}',
                Icons.trending_up,
                Colors.green)),
          ]),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'الإعلانات المنتهية',
                '${_statistics['expiredAds'] ?? 0}',
                Icons.schedule,
                Colors.orange)),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'إجمالي الإنفاق',
                '${(_statistics['totalRevenue'] ?? 0.0).toStringAsFixed(1)} د.ك',
                Icons.attach_money,
                Colors.purple)),
          ]),
      ]);
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8)),
                  child: Icon(Icons.arrow_upward, color: color, size: 16)),
              ]),
            const SizedBox(height: 12),
            Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color)),
            const SizedBox(height: 4),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey[600])),
          ])));
  }

  /// بناء النشاط الأخير
  Widget _buildRecentActivity() {
    final recentEstates = _copiedEstates.take(3).toList();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'النشاط الأخير',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            if (recentEstates.isEmpty)
              Center(
                child: Column(
                  children: [
                    Icon(Icons.content_copy, size: 48, color: Colors.grey[400]),
                    const SizedBox(height: 8),
                    Text(
                      'لم تقم بنسخ أي عقارات بعد',
                      style: GoogleFonts.cairo(
                        color: Colors.grey[600])),
                  ]))
            else
              ...recentEstates.map((estate) => _buildActivityItem(estate)),
          ])));
  }

  /// بناء عنصر النشاط
  Widget _buildActivityItem(Estate estate) {
    final timeAgo = _getTimeAgo(estate.copiedAt ?? DateTime.now());
    final isActive = estate.adExpiryDate?.isAfter(DateTime.now()) ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isActive ? Colors.green.withValues(alpha: 0.2) : Colors.orange.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20)),
            child: Icon(
              isActive ? Icons.check_circle : Icons.schedule,
              color: isActive ? Colors.green : Colors.orange,
              size: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  estate.title,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w600,
                    fontSize: 14),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
                const SizedBox(height: 4),
                Text(
                  'نُسخ $timeAgo',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey[600])),
              ])),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isActive ? Colors.green : Colors.orange,
              borderRadius: BorderRadius.circular(12)),
            child: Text(
              isActive ? 'نشط' : 'منتهي',
              style: GoogleFonts.cairo(
                fontSize: 10,
                color: Colors.white,
                fontWeight: FontWeight.w600))),
        ]));
  }

  /// بناء تبويب العقارات النشطة
  Widget _buildActiveEstatesTab() {
    final activeEstates = _copiedEstates.where((estate) =>
        estate.adExpiryDate?.isAfter(DateTime.now()) ?? false).toList();

    return _buildEstatesList(activeEstates, 'لا توجد عقارات نشطة');
  }

  /// بناء تبويب العقارات المنتهية
  Widget _buildExpiredEstatesTab() {
    final expiredEstates = _copiedEstates.where((estate) =>
        estate.adExpiryDate?.isBefore(DateTime.now()) ?? true).toList();

    return _buildEstatesList(expiredEstates, 'لا توجد عقارات منتهية');
  }

  /// بناء قائمة العقارات
  Widget _buildEstatesList(List<Estate> estates, String emptyMessage) {
    if (estates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.home_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey[600])),
          ]));
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: estates.length,
        itemBuilder: (context, index) {
          final estate = estates[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            child: _buildCopiedEstateCard(estate));
        }));
  }

  /// بناء بطاقة العقار المنسوخ
  Widget _buildCopiedEstateCard(Estate estate) {
    final isActive = estate.adExpiryDate?.isAfter(DateTime.now()) ?? false;
    final daysRemaining = isActive
        ? estate.adExpiryDate!.difference(DateTime.now()).inDays
        : 0;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          // شريط الحالة
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isActive ? Colors.green : Colors.orange,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      isActive ? Icons.check_circle : Icons.schedule,
                      color: Colors.white,
                      size: 16),
                    const SizedBox(width: 8),
                    Text(
                      isActive ? 'إعلان نشط' : 'إعلان منتهي',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 12)),
                  ]),
                if (isActive)
                  Text(
                    'باقي $daysRemaining يوم',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 12)),
              ])),

          // محتوى البطاقة
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // معلومات العقار
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // صورة العقار
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        width: 80,
                        height: 80,
                        color: Colors.grey[300],
                        child: estate.photoUrls.isNotEmpty
                            ? Image.network(
                                estate.photoUrls.first,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    Icon(Icons.home, color: Colors.grey[600]))
                            : Icon(Icons.home, color: Colors.grey[600]))),
                    const SizedBox(width: 12),

                    // تفاصيل العقار
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            estate.title,
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.bold,
                              fontSize: 14),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis),
                          const SizedBox(height: 4),
                          Text(
                            '${NumberFormat('#,###').format(estate.price)} د.ك',
                            style: GoogleFonts.cairo(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                              fontSize: 14)),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(Icons.location_on, size: 12, color: Colors.grey),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  estate.location,
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: Colors.grey[600]),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis)),
                            ]),
                        ])),
                  ]),

                const SizedBox(height: 16),

                // أزرار الإجراءات
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _viewOriginalEstate(estate),
                        icon: Icon(Icons.visibility, size: 16),
                        label: Text(
                          'العقار الأصلي',
                          style: GoogleFonts.cairo(fontSize: 12)),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 8)))),
                    const SizedBox(width: 8),
                    if (!isActive)
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _renewAd(estate),
                          icon: Icon(Icons.refresh, size: 16),
                          label: Text(
                            'تجديد',
                            style: GoogleFonts.cairo(fontSize: 12)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 8)))),
                    if (isActive)
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _extendAd(estate),
                          icon: Icon(Icons.add_circle, size: 16),
                          label: Text(
                            'تمديد',
                            style: GoogleFonts.cairo(fontSize: 12)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 8)))),
                  ]),
              ])),
        ]));
  }

  /// عرض العقار الأصلي
  void _viewOriginalEstate(Estate copiedEstate) async {
    try {
      final originalEstate = await _copyService.getOriginalEstate(copiedEstate.id);
      if (originalEstate != null) {
        // الانتقال لصفحة تفاصيل العقار الأصلي
        Navigator.pushNamed(
          context,
          '/estate-details',
          arguments: originalEstate);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('العقار الأصلي غير موجود'),
            backgroundColor: Colors.orange));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في عرض العقار الأصلي: $e'),
          backgroundColor: Colors.red));
    }
  }

  /// تجديد الإعلان
  void _renewAd(Estate estate) {
    // الانتقال لصفحة تجديد الإعلان
    Navigator.pushNamed(
      context,
      '/renew-ad',
      arguments: estate);
  }

  /// تمديد الإعلان
  void _extendAd(Estate estate) {
    // الانتقال لصفحة تمديد الإعلان
    Navigator.pushNamed(
      context,
      '/extend-ad',
      arguments: estate);
  }

  /// حساب الوقت المنقضي
  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
