// lib/presentation/widgets/premium_estate_card.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:kuwait_corners/domain/entities/estate.dart';
import 'package:kuwait_corners/presentation/widgets/estate_details_page.dart';
import '../../domain/services/favorites_service.dart';
// import '../../domain/services/property_comparison_service.dart'; // معلق مؤقتاً
import '../../domain/services/estate_copy_service.dart';
import '../../domain/services/user_interface_customization_service.dart';
import '../../domain/entities/user.dart';
import '../../core/constants/user_types.dart';
import 'package:firebase_auth/firebase_auth.dart';
// تعليق الاستيراد حتى نجد المسار الصحيح
// import 'package:kuwait_corners/presentation/pages/estate_detail_page.dart';

/// نوع الإعلان المميز
enum PremiumCardType {
  /// عادي
  normal,

  /// مميز (ذهبي)
  featured,

  /// مثبت
  pinned,

  /// VIP
  vip,

  /// متحرك
  moving,

  /// مثبت في الصفحة الرئيسية
  pinnedOnHome,
}

/// بطاقة عرض العقار المميزة مع دعم تمييز الإعلانات
class PremiumEstateCard extends StatefulWidget {
  /// نموذج العقار
  final Estate estate;

  /// نوع البطاقة
  final PremiumCardType cardType;

  /// ما إذا كان هذا القسم هو قسم إدارة العقارات
  final bool isManageSection;

  /// دالة يتم استدعاؤها عند النقر على زر التعديل
  final VoidCallback? onEdit;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;

  /// حجم البطاقة
  final Size? size;

  /// ما إذا كانت البطاقة في عرض أفقي
  final bool isHorizontal;

  const PremiumEstateCard({
    super.key,
    required this.estate,
    this.cardType = PremiumCardType.normal,
    this.isManageSection = false,
    this.onEdit,
    this.onTap,
    this.size,
    this.isHorizontal = true,
  });

  @override
  State<PremiumEstateCard> createState() => _PremiumEstateCardState();
}

class _PremiumEstateCardState extends State<PremiumEstateCard> {
  // الخدمات
  final FavoritesService _favoritesService = FavoritesService();
  // final PropertyComparisonService _comparisonService = PropertyComparisonService(); // معلق مؤقتاً

  @override
  void initState() {
    super.initState();
    // تم إزالة تأثير النبض بناءً على طلب المستخدم
  }

  @override
  void dispose() {
    // No animation controller to dispose
    super.dispose();
  }

  /// دالة مساعدة لعرض الصورة مع fallback
  Widget _buildEstateImage(String imageUrl) {
    if (imageUrl.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: imageUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: Colors.grey.shade200,
          child: const Center(
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey))))),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey.shade200,
          child: const Icon(Icons.error, color: Colors.grey)));
    } else if (imageUrl.isNotEmpty) {
      return Image.file(
        File(imageUrl),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Container(
          color: Colors.grey.shade200,
          child: const Icon(Icons.error, color: Colors.grey)));
    } else {
      return Container(
        color: Colors.grey[300],
        alignment: Alignment.center,
        child: const Icon(Icons.image_not_supported));
    }
  }

  @override
  Widget build(BuildContext context) {
    // اختر أول رابط من القائمة إذا كانت غير فارغة
    final String? firstImageUrl =
        widget.estate.photoUrls.isNotEmpty ? widget.estate.photoUrls.first : null;

    // تحديد ألوان وتأثيرات البطاقة حسب نوعها
    final cardDecoration = _getCardDecoration();

    // تحديد حجم البطاقة
    final cardWidth = widget.size?.width ?? (widget.isHorizontal ? 260.0 : double.infinity);
    final cardHeight = widget.size?.height ?? (widget.isHorizontal ? 280.0 : 320.0);

    // بناء البطاقة (تم إزالة تأثير النبض)
    return GestureDetector(
      onTap: () {
        if (widget.onTap != null) {
          widget.onTap!();
        } else {
          // عند الضغط، انتقل لصفحة تفاصيل الإعلان
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => EstateDetailsPage(estate: widget.estate)));
        }
      },
      child: Container(
        width: cardWidth,
        height: cardHeight,
        margin: const EdgeInsets.only(right: 12, bottom: 8),
        decoration: cardDecoration,
        clipBehavior: Clip.antiAlias,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة العقار
            _buildEstateImageSection(context, firstImageUrl),

            // معلومات العقار الأساسية
            Expanded(child: _buildInfoSection()),
          ])));
  }

  /// بناء قسم صورة العقار
  Widget _buildEstateImageSection(BuildContext context, String? firstImageUrl) {
    return Stack(
      children: [
        // الصورة
        SizedBox(
          height: widget.isHorizontal ? 140 : 160,
          width: double.infinity,
          child: firstImageUrl != null && firstImageUrl.isNotEmpty
              ? Hero(
                  tag: 'estate_image_${widget.estate.id}',
                  child: _buildEstateImage(firstImageUrl))
              : Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.indigo.withAlpha(13),
                        Colors.indigo.withAlpha(38),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter)),
                  alignment: Alignment.center,
                  child: Icon(
                    Icons.home_outlined,
                    size: 36,
                    color: Colors.indigo.withAlpha(77)))),

        // شارات الإعلان
        _buildBadges(),

        // أزرار المفضلة والمقارنة
        _buildActionButtons(),

        // زر التعديل إذا كان في قسم إدارة العقارات
        if (widget.isManageSection && widget.onEdit != null)
          Positioned(
            top: 8,
            left: 8,
            child: InkWell(
              onTap: widget.onEdit,
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2)),
                  ]),
                child: const Icon(
                  Icons.edit,
                  size: 16,
                  color: Colors.indigo)))),
      ]);
  }

  /// بناء قسم معلومات العقار
  Widget _buildInfoSection() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان العقار
          Text(
            widget.estate.title,
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              fontSize: 15,
              height: 1.2,
              color: _getTitleColor()),
            maxLines: 1,
            overflow: TextOverflow.ellipsis),
          const SizedBox(height: 6),

          // السعر
          Text(
            "${widget.estate.price.toStringAsFixed(0)} د.ك",
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w700,
              fontSize: 16,
              color: _getPriceColor())),
          const SizedBox(height: 6),

          // الموقع
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: 14,
                color: Colors.grey[600]),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  widget.estate.location,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey[600]),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis)),
            ]),

          // المساحة وعدد الغرف إذا كانت متوفرة
          if (widget.estate.area != null || widget.estate.numberOfRooms != null)
            Padding(
              padding: const EdgeInsets.only(top: 6),
              child: Row(
                children: [
                  if (widget.estate.area != null) ...[
                    Icon(
                      Icons.square_foot,
                      size: 14,
                      color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      "${widget.estate.area!.toStringAsFixed(0)} م²",
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey[600])),
                    const SizedBox(width: 12),
                  ],
                  if (widget.estate.numberOfRooms != null) ...[
                    Icon(
                      Icons.bedroom_parent,
                      size: 14,
                      color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      "${widget.estate.numberOfRooms} غرف",
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey[600])),
                  ],
                ])),
        ]));
  }

  /// بناء أزرار المفضلة والمقارنة
  Widget _buildActionButtons() {
    return Positioned(
      bottom: 8,
      left: 8,
      child: Row(
        children: [
          // زر المفضلة (فقط للمستخدمين غير الشركات العقارية)
          FutureBuilder<bool>(
            future: _canShowFavoriteButton(),
            builder: (context, canShowSnapshot) {
              final canShowFavorite = canShowSnapshot.data ?? false;
              if (!canShowFavorite) return const SizedBox.shrink();

              return FutureBuilder<bool>(
                future: _favoritesService.isFavorite(widget.estate.id),
                builder: (context, snapshot) {
                  final isFavorite = snapshot.data ?? false;
                  return GestureDetector(
                    onTap: () => _toggleFavorite(),
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: isFavorite ? Colors.red : Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2)),
                        ]),
                      child: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        size: 16,
                        color: isFavorite ? Colors.white : Colors.red)));
                });
            }),

          // إضافة مسافة فقط إذا كان زر المفضلة يظهر
          FutureBuilder<bool>(
            future: _canShowFavoriteButton(),
            builder: (context, snapshot) {
              final canShowFavorite = snapshot.data ?? false;
              return canShowFavorite ? const SizedBox(width: 8) : const SizedBox.shrink();
            }),

          // تم إزالة زر المقارنة

          // زر النسخ (للمستثمرين فقط)
          FutureBuilder<bool>(
            future: _canShowCopyButton(),
            builder: (context, snapshot) {
              final canShowCopy = snapshot.data ?? false;
              if (!canShowCopy) return const SizedBox.shrink();

              return GestureDetector(
                onTap: () => _showCopyDialog(),
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2)),
                    ]),
                  child: const Icon(
                    Icons.content_copy,
                    size: 16,
                    color: Colors.purple)));
            }),
        ]));
  }

  /// تبديل حالة المفضلة
  Future<void> _toggleFavorite() async {
    try {
      final isFavorite = await _favoritesService.isFavorite(widget.estate.id);

      if (isFavorite) {
        await _favoritesService.removeFromFavorites(widget.estate.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إزالة العقار من المفضلة'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 2)));
        }
      } else {
        await _favoritesService.addToFavorites(widget.estate.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة العقار للمفضلة'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2)));
        }
      }

      // تحديث الواجهة
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red));
      }
    }
  }

  // تم إزالة دالة المقارنة

  /// التحقق من إمكانية عرض زر المفضلة
  Future<bool> _canShowFavoriteButton() async {
    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      final userTypeString = userType.toString().split('.').last;

      // الشركات العقارية لا يمكنها الوصول للمفضلة
      return UserTypeConstants.canAccessFavorites(userTypeString);
    } catch (e) {
      // في حالة الخطأ، نخفي الزر للأمان
      return false;
    }
  }

  /// التحقق من إمكانية عرض زر النسخ
  Future<bool> _canShowCopyButton() async {
    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();

      // فقط المستثمرين (الوكلاء) يمكنهم النسخ
      if (userType != UserType.agent) return false;

      // لا يمكن نسخ العقارات المنسوخة
      if (widget.estate.isCopied) return false;

      // التحقق من نوع العقار - فقط العقارات للإيجار يمكن نسخها
      if (widget.estate.mainCategory != 'عقار للايجار') return false;

      // التحقق من إمكانية النسخ باستخدام الخدمة
      final copyService = EstateCopyService();
      final currentUserId = FirebaseAuth.instance.currentUser?.uid;
      if (currentUserId == null) return false;

      return await copyService.canCopyEstate(widget.estate.id, currentUserId);
    } catch (e) {
      return false;
    }
  }

  /// عرض مربع حوار نسخ العقار
  void _showCopyDialog() {
    Navigator.pushNamed(
      context,
      '/copy-estate',
      arguments: widget.estate);
  }

  /// بناء شارات الإعلان
  Widget _buildBadges() {
    return Positioned(
      top: 8,
      right: 8,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // شارة VIP
          if (widget.cardType == PremiumCardType.vip || widget.estate.vipBadge == true)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFD4AF37), Color(0xFFF9F295), Color(0xFFD4AF37)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2)),
                ]),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.workspace_premium,
                    size: 14,
                    color: Colors.white),
                  const SizedBox(width: 4),
                  Text(
                    "VIP",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: Colors.white)),
                ])),

          // شارة مميز
          if (widget.cardType == PremiumCardType.featured || widget.estate.isFeatured)
            Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.amber,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2)),
                ]),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.star,
                    size: 14,
                    color: Colors.white),
                  const SizedBox(width: 4),
                  Text(
                    "مميز",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: Colors.white)),
                ])),

          // شارة مثبت
          if (widget.cardType == PremiumCardType.pinned || widget.estate.kuwaitCornersPin == true)
            Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2)),
                ]),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.push_pin,
                    size: 14,
                    color: Colors.white),
                  const SizedBox(width: 4),
                  Text(
                    "مثبت",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: Colors.white)),
                ])),
        ]));
  }

  /// الحصول على زخرفة البطاقة حسب نوعها
  BoxDecoration _getCardDecoration() {
    switch (widget.cardType) {
      case PremiumCardType.featured:
        return BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFFFFDF5),
              Color(0xFFFFF8E1),
            ]),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.amber.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4)),
          ],
          border: Border.all(
            color: Colors.amber.withOpacity(0.5),
            width: 1.5));

      case PremiumCardType.vip:
        return BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFFAFAFF),
              Color(0xFFF5F5FF),
            ]),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFD4AF37).withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4)),
          ],
          border: Border.all(
            color: const Color(0xFFD4AF37),
            width: 1.5));

      case PremiumCardType.pinned:
        return BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF5F9FF),
              Color(0xFFE3F2FD),
            ]),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4)),
          ],
          border: Border.all(
            color: Colors.blue.withOpacity(0.5),
            width: 1.5));

      case PremiumCardType.pinnedOnHome:
        return BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF5FFF9),
              Color(0xFFE0F2F1),
            ]),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.teal.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4)),
          ],
          border: Border.all(
            color: Colors.teal.withOpacity(0.5),
            width: 1.5));

      case PremiumCardType.moving:
      case PremiumCardType.normal:
      default:
        return BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFFF8F9FA),
              const Color(0xFFE8EAF6),
            ]),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 4)),
          ]);
    }
  }

  /// الحصول على لون العنوان حسب نوع البطاقة
  Color _getTitleColor() {
    switch (widget.cardType) {
      case PremiumCardType.featured:
        return Colors.amber.shade800;
      case PremiumCardType.vip:
        return const Color(0xFF8E44AD);
      case PremiumCardType.pinned:
        return Colors.blue.shade800;
      case PremiumCardType.pinnedOnHome:
        return Colors.teal.shade800;
      case PremiumCardType.moving:
      case PremiumCardType.normal:
      default:
        return const Color(0xFF303F9F);
    }
  }

  /// الحصول على لون السعر حسب نوع البطاقة
  Color _getPriceColor() {
    switch (widget.cardType) {
      case PremiumCardType.featured:
        return Colors.amber.shade700;
      case PremiumCardType.vip:
        return const Color(0xFF8E44AD);
      case PremiumCardType.pinned:
        return Colors.blue.shade700;
      case PremiumCardType.pinnedOnHome:
        return Colors.teal.shade700;
      case PremiumCardType.moving:
      case PremiumCardType.normal:
      default:
        return Colors.indigo;
    }
  }
}
