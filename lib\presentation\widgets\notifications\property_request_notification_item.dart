// lib/presentation/widgets/notifications/property_request_notification_item.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/services/realtime_notification_service.dart';

/// عنصر إشعار طلب عقار
class PropertyRequestNotificationItem extends StatelessWidget {
  /// نموذج الإشعار
  final RealtimeNotificationModel notification;

  /// دالة عند النقر على الإشعار
  final VoidCallback? onTap;

  /// دالة عند تحديد الإشعار كمقروء
  final Function(String notificationId)? onMarkAsRead;

  const PropertyRequestNotificationItem({
    super.key,
    required this.notification,
    this.onTap,
    this.onMarkAsRead,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onMarkAsRead != null && !notification.isRead) {
          onMarkAsRead!(notification.id);
        }

        _handleNotificationTap(context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: notification.isRead ? Colors.transparent : Colors.green.withAlpha(13),
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.shade200,
              width: 1))),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildNotificationIcon(),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    notification.title,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      fontSize: 14)),
                  const SizedBox(height: 4),
                  Text(
                    notification.body,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade700),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),
                  const SizedBox(height: 4),
                  Text(
                    _formatDate(notification.timestamp),
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      color: Colors.grey)),
                ])),
            if (!notification.isRead)
              Container(
                width: 10,
                height: 10,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle)),
          ])));
  }

  /// بناء أيقونة الإشعار
  Widget _buildNotificationIcon() {
    IconData iconData;
    Color iconColor;

    switch (notification.type) {
      case RealtimeNotificationType.newPropertyOffer:
        iconData = Icons.local_offer;
        iconColor = Colors.green;
        break;
      case RealtimeNotificationType.propertyOfferAccepted:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case RealtimeNotificationType.propertyOfferRejected:
        iconData = Icons.cancel;
        iconColor = Colors.red;
        break;
      case RealtimeNotificationType.propertyRequestStatusUpdate:
        iconData = Icons.update;
        iconColor = Colors.blue;
        break;
      default:
        iconData = Icons.notifications;
        iconColor = Colors.green;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: iconColor.withAlpha(25),
        shape: BoxShape.circle),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20));
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم ${DateFormat('HH:mm').format(date)}';
    } else if (difference.inDays == 1) {
      return 'الأمس ${DateFormat('HH:mm').format(date)}';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return DateFormat('yyyy/MM/dd').format(date);
    }
  }

  /// معالجة النقر على الإشعار
  void _handleNotificationTap(BuildContext context) {
    if (onTap != null) {
      onTap!();
      return;
    }

    final data = notification.data;

    switch (notification.type) {
      case RealtimeNotificationType.newPropertyOffer:
        if (data.containsKey('requestId') && data['requestId'] != null) {
          Navigator.pushNamed(
            context,
            AppRoutes.propertyRequestDetails,
            arguments: data['requestId']);
        }
        break;

      case RealtimeNotificationType.propertyOfferAccepted:
        if (data.containsKey('conversationId') && data['conversationId'] != null) {
          // الانتقال إلى المحادثة
          Navigator.pushNamed(
            context,
            AppRoutes.conversations,
            arguments: data['conversationId']);
        } else if (data.containsKey('requestId') && data['requestId'] != null) {
          // الانتقال إلى تفاصيل الطلب
          Navigator.pushNamed(
            context,
            AppRoutes.propertyRequestDetails,
            arguments: data['requestId']);
        }
        break;

      case RealtimeNotificationType.propertyOfferRejected:
        if (data.containsKey('requestId') && data['requestId'] != null) {
          // الانتقال إلى تفاصيل الطلب
          Navigator.pushNamed(
            context,
            AppRoutes.propertyRequestDetails,
            arguments: data['requestId']);
        }
        break;

      case RealtimeNotificationType.propertyRequestStatusUpdate:
        if (data.containsKey('requestId') && data['requestId'] != null) {
          // الانتقال إلى تفاصيل الطلب
          Navigator.pushNamed(
            context,
            AppRoutes.propertyRequestDetails,
            arguments: data['requestId']);
        }
        break;

      default:
        // لا شيء
        break;
    }
  }
}
