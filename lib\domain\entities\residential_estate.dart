import 'estate_base.dart';

/// نموذج للعقارات السكنية
class ResidentialEstate extends EstateBase {
  // خصائص العقارات السكنية
  final double? area; // المساحة بالمتر المربع
  final int? numberOfRooms; // عدد الغرف
  final int? numberOfBathrooms; // عدد الحمامات
  final int? numberOfLivingRooms; // عدد الصالونات
  final int? numberOfFloors; // عدد الطوابق
  final int? floorNumber; // رقم الطابق
  final int? buildingAge; // عمر البناء بالسنوات

  // المرافق والميزات
  final bool hasCentralAC; // تكييف مركزي
  final bool hasMaidRoom; // غرفة خادمة
  final bool hasGarage; // كراج
  final bool hasSwimmingPool; // مسبح
  final bool hasGarden; // حديقة
  final bool hasElevator; // مصعد
  final bool hasBalcony; // شرفة
  final bool isFullyFurnished; // مفروش بالكامل
  final bool isSemilyFurnished; // مفروش جزئياً
  final bool hasPrivateEntrance; // مدخل خاص
  final bool hasSecurity; // أمن
  final bool hasGym; // صالة رياضية

  // معلومات إضافية
  final String? facingDirection; // الاتجاه (شمال، جنوب، شرق، غرب)
  final String? finishingType; // نوع التشطيب (سوبر لوكس، لوكس، عادي)
  final String? viewType; // نوع الإطلالة (بحر، حديقة، شارع)
  final String? furnishingDetails; // تفاصيل الفرش
  final String? paymentMethod; // طريقة الدفع (كاش، أقساط)
  final String? contractType; // نوع العقد (تمليك، إيجار)
  final String? propertyType; // نوع العقار (شقة، منزل، دوبلكس)

  // معلومات الإيجار (إذا كان للإيجار)
  final String? rentalPeriod; // فترة الإيجار (يومي، شهري، سنوي)
  final bool? isRentalWithOption; // إيجار منتهي بالتمليك
  final double? insuranceAmount; // مبلغ التأمين
  final double? commissionAmount; // مبلغ العمولة

  const ResidentialEstate({
    required super.id,
    required super.title,
    required super.description,
    required super.price,
    required super.location,
    required super.photoUrls,
    required super.isFeatured,
    required super.status,
    super.governorate,
    super.city,
    super.district,
    super.block,
    super.latitude,
    super.longitude,
    super.shareLocation,
    required super.mainCategory,
    super.subCategory,
    required super.ownerId,
    super.advertiserName,
    super.advertiserPhone,
    super.advertiserImage,
    super.advertiserType,
    super.advertiserJoinDate,
    super.advertiserAdsCount,
    super.hidePhone,
    super.extraPhones,
    required super.createdAt,
    super.updatedAt,
    super.startDate,
    super.endDate,
    super.viewsCount,
    super.favoritesCount,
    super.contactCount,
    super.subscriptionPlan,
    super.autoRepublish,
    super.isPinned,
    super.isPromoted,
    super.isVIP,
    super.isVerified,
    super.isPaymentVerified,
    super.originalEstateId,
    super.isOriginal,
    super.copiedBy,
    super.floorPlanUrl,
    super.virtualTourUrl,
    super.videoUrl,
    super.documents,
    this.area,
    this.numberOfRooms,
    this.numberOfBathrooms,
    this.numberOfLivingRooms,
    this.numberOfFloors,
    this.floorNumber,
    this.buildingAge,
    this.hasCentralAC = false,
    this.hasMaidRoom = false,
    this.hasGarage = false,
    this.hasSwimmingPool = false,
    this.hasGarden = false,
    this.hasElevator = false,
    this.hasBalcony = false,
    this.isFullyFurnished = false,
    this.isSemilyFurnished = false,
    this.hasPrivateEntrance = false,
    this.hasSecurity = false,
    this.hasGym = false,
    this.facingDirection,
    this.finishingType,
    this.viewType,
    this.furnishingDetails,
    this.paymentMethod,
    this.contractType,
    this.propertyType,
    this.rentalPeriod,
    this.isRentalWithOption,
    this.insuranceAmount,
    this.commissionAmount,
  });

  @override
  String getEstateType() {
    return 'residential';
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'location': location,
      'photoUrls': photoUrls,
      'isFeatured': isFeatured,
      'status': status,
      'governorate': governorate,
      'city': city,
      'district': district,
      'block': block,
      'latitude': latitude,
      'longitude': longitude,
      'shareLocation': shareLocation,
      'mainCategory': mainCategory,
      'subCategory': subCategory,
      'ownerId': ownerId,
      'advertiserName': advertiserName,
      'advertiserPhone': advertiserPhone,
      'advertiserImage': advertiserImage,
      'advertiserType': advertiserType,
      'advertiserJoinDate': EstateBase.dateTimeToTimestamp(advertiserJoinDate),
      'advertiserAdsCount': advertiserAdsCount,
      'hidePhone': hidePhone,
      'extraPhones': extraPhones,
      'createdAt': EstateBase.dateTimeToTimestamp(createdAt),
      'updatedAt': EstateBase.dateTimeToTimestamp(updatedAt),
      'startDate': EstateBase.dateTimeToTimestamp(startDate),
      'endDate': EstateBase.dateTimeToTimestamp(endDate),
      'viewsCount': viewsCount,
      'favoritesCount': favoritesCount,
      'contactCount': contactCount,
      'subscriptionPlan': subscriptionPlan,
      'autoRepublish': autoRepublish,
      'isPinned': isPinned,
      'isPromoted': isPromoted,
      'isVIP': isVIP,
      'isVerified': isVerified,
      'isPaymentVerified': isPaymentVerified,
      'originalEstateId': originalEstateId,
      'isOriginal': isOriginal,
      'copiedBy': copiedBy,
      'floorPlanUrl': floorPlanUrl,
      'virtualTourUrl': virtualTourUrl,
      'videoUrl': videoUrl,
      'documents': documents,
      'estateType': 'residential',
      'area': area,
      'numberOfRooms': numberOfRooms,
      'numberOfBathrooms': numberOfBathrooms,
      'numberOfLivingRooms': numberOfLivingRooms,
      'numberOfFloors': numberOfFloors,
      'floorNumber': floorNumber,
      'buildingAge': buildingAge,
      'hasCentralAC': hasCentralAC,
      'hasMaidRoom': hasMaidRoom,
      'hasGarage': hasGarage,
      'hasSwimmingPool': hasSwimmingPool,
      'hasGarden': hasGarden,
      'hasElevator': hasElevator,
      'hasBalcony': hasBalcony,
      'isFullyFurnished': isFullyFurnished,
      'isSemilyFurnished': isSemilyFurnished,
      'hasPrivateEntrance': hasPrivateEntrance,
      'hasSecurity': hasSecurity,
      'hasGym': hasGym,
      'facingDirection': facingDirection,
      'finishingType': finishingType,
      'viewType': viewType,
      'furnishingDetails': furnishingDetails,
      'paymentMethod': paymentMethod,
      'contractType': contractType,
      'propertyType': propertyType,
      'rentalPeriod': rentalPeriod,
      'isRentalWithOption': isRentalWithOption,
      'insuranceAmount': insuranceAmount,
      'commissionAmount': commissionAmount,
    };
  }

  @override
  ResidentialEstate copyWithBase({
    String? id,
    String? title,
    String? description,
    double? price,
    String? location,
    List<String>? photoUrls,
    bool? isFeatured,
    String? status,
    String? governorate,
    String? city,
    String? district,
    String? block,
    double? latitude,
    double? longitude,
    bool? shareLocation,
    String? mainCategory,
    String? subCategory,
    String? ownerId,
    String? advertiserName,
    String? advertiserPhone,
    String? advertiserImage,
    String? advertiserType,
    DateTime? advertiserJoinDate,
    int? advertiserAdsCount,
    bool? hidePhone,
    List<String>? extraPhones,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? startDate,
    DateTime? endDate,
    int? viewsCount,
    int? favoritesCount,
    int? contactCount,
    String? subscriptionPlan,
    bool? autoRepublish,
    bool? isPinned,
    bool? isPromoted,
    bool? isVIP,
    bool? isVerified,
    bool? isPaymentVerified,
    String? originalEstateId,
    bool? isOriginal,
    List<String>? copiedBy,
    String? floorPlanUrl,
    String? virtualTourUrl,
    String? videoUrl,
    List<Map<String, dynamic>>? documents,
  }) {
    return copyWith(
      id: id,
      title: title,
      description: description,
      price: price,
      location: location,
      photoUrls: photoUrls,
      isFeatured: isFeatured,
      status: status,
      governorate: governorate,
      city: city,
      district: district,
      block: block,
      latitude: latitude,
      longitude: longitude,
      shareLocation: shareLocation,
      mainCategory: mainCategory,
      subCategory: subCategory,
      ownerId: ownerId,
      advertiserName: advertiserName,
      advertiserPhone: advertiserPhone,
      advertiserImage: advertiserImage,
      advertiserType: advertiserType,
      advertiserJoinDate: advertiserJoinDate,
      advertiserAdsCount: advertiserAdsCount,
      hidePhone: hidePhone,
      extraPhones: extraPhones,
      createdAt: createdAt,
      updatedAt: updatedAt,
      startDate: startDate,
      endDate: endDate,
      viewsCount: viewsCount,
      favoritesCount: favoritesCount,
      contactCount: contactCount,
      subscriptionPlan: subscriptionPlan,
      autoRepublish: autoRepublish,
      isPinned: isPinned,
      isPromoted: isPromoted,
      isVIP: isVIP,
      isVerified: isVerified,
      isPaymentVerified: isPaymentVerified,
      originalEstateId: originalEstateId,
      isOriginal: isOriginal,
      copiedBy: copiedBy,
      floorPlanUrl: floorPlanUrl,
      virtualTourUrl: virtualTourUrl,
      videoUrl: videoUrl,
      documents: documents);
  }

  /// إنشاء نسخة معدلة من العقار السكني
  ResidentialEstate copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    String? location,
    List<String>? photoUrls,
    bool? isFeatured,
    String? status,
    String? governorate,
    String? city,
    String? district,
    String? block,
    double? latitude,
    double? longitude,
    bool? shareLocation,
    String? mainCategory,
    String? subCategory,
    String? ownerId,
    String? advertiserName,
    String? advertiserPhone,
    String? advertiserImage,
    String? advertiserType,
    DateTime? advertiserJoinDate,
    int? advertiserAdsCount,
    bool? hidePhone,
    List<String>? extraPhones,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? startDate,
    DateTime? endDate,
    int? viewsCount,
    int? favoritesCount,
    int? contactCount,
    String? subscriptionPlan,
    bool? autoRepublish,
    bool? isPinned,
    bool? isPromoted,
    bool? isVIP,
    bool? isVerified,
    bool? isPaymentVerified,
    String? originalEstateId,
    bool? isOriginal,
    List<String>? copiedBy,
    String? floorPlanUrl,
    String? virtualTourUrl,
    String? videoUrl,
    List<Map<String, dynamic>>? documents,
    double? area,
    int? numberOfRooms,
    int? numberOfBathrooms,
    int? numberOfLivingRooms,
    int? numberOfFloors,
    int? floorNumber,
    int? buildingAge,
    bool? hasCentralAC,
    bool? hasMaidRoom,
    bool? hasGarage,
    bool? hasSwimmingPool,
    bool? hasGarden,
    bool? hasElevator,
    bool? hasBalcony,
    bool? isFullyFurnished,
    bool? isSemilyFurnished,
    bool? hasPrivateEntrance,
    bool? hasSecurity,
    bool? hasGym,
    String? facingDirection,
    String? finishingType,
    String? viewType,
    String? furnishingDetails,
    String? paymentMethod,
    String? contractType,
    String? propertyType,
    String? rentalPeriod,
    bool? isRentalWithOption,
    double? insuranceAmount,
    double? commissionAmount,
  }) {
    return ResidentialEstate(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      location: location ?? this.location,
      photoUrls: photoUrls ?? this.photoUrls,
      isFeatured: isFeatured ?? this.isFeatured,
      status: status ?? this.status,
      governorate: governorate ?? this.governorate,
      city: city ?? this.city,
      district: district ?? this.district,
      block: block ?? this.block,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      shareLocation: shareLocation ?? this.shareLocation,
      mainCategory: mainCategory ?? this.mainCategory,
      subCategory: subCategory ?? this.subCategory,
      ownerId: ownerId ?? this.ownerId,
      advertiserName: advertiserName ?? this.advertiserName,
      advertiserPhone: advertiserPhone ?? this.advertiserPhone,
      advertiserImage: advertiserImage ?? this.advertiserImage,
      advertiserType: advertiserType ?? this.advertiserType,
      advertiserJoinDate: advertiserJoinDate ?? this.advertiserJoinDate,
      advertiserAdsCount: advertiserAdsCount ?? this.advertiserAdsCount,
      hidePhone: hidePhone ?? this.hidePhone,
      extraPhones: extraPhones ?? this.extraPhones,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      viewsCount: viewsCount ?? this.viewsCount,
      favoritesCount: favoritesCount ?? this.favoritesCount,
      contactCount: contactCount ?? this.contactCount,
      subscriptionPlan: subscriptionPlan ?? this.subscriptionPlan,
      autoRepublish: autoRepublish ?? this.autoRepublish,
      isPinned: isPinned ?? this.isPinned,
      isPromoted: isPromoted ?? this.isPromoted,
      isVIP: isVIP ?? this.isVIP,
      isVerified: isVerified ?? this.isVerified,
      isPaymentVerified: isPaymentVerified ?? this.isPaymentVerified,
      originalEstateId: originalEstateId ?? this.originalEstateId,
      isOriginal: isOriginal ?? this.isOriginal,
      copiedBy: copiedBy ?? this.copiedBy,
      floorPlanUrl: floorPlanUrl ?? this.floorPlanUrl,
      virtualTourUrl: virtualTourUrl ?? this.virtualTourUrl,
      videoUrl: videoUrl ?? this.videoUrl,
      documents: documents ?? this.documents,
      area: area ?? this.area,
      numberOfRooms: numberOfRooms ?? this.numberOfRooms,
      numberOfBathrooms: numberOfBathrooms ?? this.numberOfBathrooms,
      numberOfLivingRooms: numberOfLivingRooms ?? this.numberOfLivingRooms,
      numberOfFloors: numberOfFloors ?? this.numberOfFloors,
      floorNumber: floorNumber ?? this.floorNumber,
      buildingAge: buildingAge ?? this.buildingAge,
      hasCentralAC: hasCentralAC ?? this.hasCentralAC,
      hasMaidRoom: hasMaidRoom ?? this.hasMaidRoom,
      hasGarage: hasGarage ?? this.hasGarage,
      hasSwimmingPool: hasSwimmingPool ?? this.hasSwimmingPool,
      hasGarden: hasGarden ?? this.hasGarden,
      hasElevator: hasElevator ?? this.hasElevator,
      hasBalcony: hasBalcony ?? this.hasBalcony,
      isFullyFurnished: isFullyFurnished ?? this.isFullyFurnished,
      isSemilyFurnished: isSemilyFurnished ?? this.isSemilyFurnished,
      hasPrivateEntrance: hasPrivateEntrance ?? this.hasPrivateEntrance,
      hasSecurity: hasSecurity ?? this.hasSecurity,
      hasGym: hasGym ?? this.hasGym,
      facingDirection: facingDirection ?? this.facingDirection,
      finishingType: finishingType ?? this.finishingType,
      viewType: viewType ?? this.viewType,
      furnishingDetails: furnishingDetails ?? this.furnishingDetails,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      contractType: contractType ?? this.contractType,
      propertyType: propertyType ?? this.propertyType,
      rentalPeriod: rentalPeriod ?? this.rentalPeriod,
      isRentalWithOption: isRentalWithOption ?? this.isRentalWithOption,
      insuranceAmount: insuranceAmount ?? this.insuranceAmount,
      commissionAmount: commissionAmount ?? this.commissionAmount);
  }

  /// التحقق من صحة بيانات العقار السكني
  @override
  Map<String, String> validate() {
    final errors = super.validate();

    if (area != null && area! <= 0) {
      errors['area'] = 'يجب إدخال مساحة صحيحة';
    } else if (area != null && area! > 10000) {
      errors['area'] = 'المساحة كبيرة جداً، يرجى التحقق';
    }

    if (numberOfRooms != null && numberOfRooms! <= 0) {
      errors['numberOfRooms'] = 'يجب إدخال عدد غرف صحيح';
    } else if (numberOfRooms != null && numberOfRooms! > 50) {
      errors['numberOfRooms'] = 'عدد الغرف كبير جداً، يرجى التحقق';
    }

    if (numberOfBathrooms != null && numberOfBathrooms! <= 0) {
      errors['numberOfBathrooms'] = 'يجب إدخال عدد حمامات صحيح';
    } else if (numberOfBathrooms != null && numberOfBathrooms! > 20) {
      errors['numberOfBathrooms'] = 'عدد الحمامات كبير جداً، يرجى التحقق';
    }

    if (buildingAge != null && buildingAge! < 0) {
      errors['buildingAge'] = 'يجب إدخال عمر بناء صحيح';
    } else if (buildingAge != null && buildingAge! > 100) {
      errors['buildingAge'] = 'عمر البناء كبير جداً، يرجى التحقق';
    }

    if (propertyType == null || propertyType!.isEmpty) {
      errors['propertyType'] = 'يجب تحديد نوع العقار';
    }

    return errors;
  }

  /// إنشاء عقار سكني من Map
  factory ResidentialEstate.fromMap(Map<String, dynamic> map) {
    return ResidentialEstate(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      location: map['location'] ?? '',
      photoUrls: List<String>.from(map['photoUrls'] ?? []),
      isFeatured: map['isFeatured'] ?? false,
      status: map['status'] ?? 'متاح',
      governorate: map['governorate'],
      city: map['city'],
      district: map['district'],
      block: map['block'],
      latitude: map['latitude'],
      longitude: map['longitude'],
      shareLocation: map['shareLocation'] ?? false,
      mainCategory: map['mainCategory'] ?? 'residential',
      subCategory: map['subCategory'],
      ownerId: map['ownerId'] ?? '',
      advertiserName: map['advertiserName'],
      advertiserPhone: map['advertiserPhone'],
      advertiserImage: map['advertiserImage'],
      advertiserType: map['advertiserType'],
      advertiserJoinDate:
          EstateBase.timestampToDateTime(map['advertiserJoinDate']),
      advertiserAdsCount: map['advertiserAdsCount'],
      hidePhone: map['hidePhone'] ?? false,
      extraPhones: List<String>.from(map['extraPhones'] ?? []),
      createdAt:
          EstateBase.timestampToDateTime(map['createdAt']) ?? DateTime.now(),
      updatedAt: EstateBase.timestampToDateTime(map['updatedAt']),
      startDate: EstateBase.timestampToDateTime(map['startDate']),
      endDate: EstateBase.timestampToDateTime(map['endDate']),
      viewsCount: map['viewsCount'] ?? 0,
      favoritesCount: map['favoritesCount'] ?? 0,
      contactCount: map['contactCount'] ?? 0,
      subscriptionPlan: map['subscriptionPlan'] ?? 'free',
      autoRepublish: map['autoRepublish'] ?? false,
      isPinned: map['isPinned'] ?? false,
      isPromoted: map['isPromoted'] ?? false,
      isVIP: map['isVIP'] ?? false,
      isVerified: map['isVerified'] ?? false,
      isPaymentVerified: map['isPaymentVerified'] ?? false,
      originalEstateId: map['originalEstateId'],
      isOriginal: map['isOriginal'] ?? true,
      copiedBy:
          map['copiedBy'] != null ? List<String>.from(map['copiedBy']) : null,
      floorPlanUrl: map['floorPlanUrl'],
      virtualTourUrl: map['virtualTourUrl'],
      videoUrl: map['videoUrl'],
      documents: map['documents'] != null
          ? List<Map<String, dynamic>>.from(map['documents'])
          : null,
      area: map['area'],
      numberOfRooms: map['numberOfRooms'],
      numberOfBathrooms: map['numberOfBathrooms'],
      numberOfLivingRooms: map['numberOfLivingRooms'],
      numberOfFloors: map['numberOfFloors'],
      floorNumber: map['floorNumber'],
      buildingAge: map['buildingAge'],
      hasCentralAC: map['hasCentralAC'] ?? false,
      hasMaidRoom: map['hasMaidRoom'] ?? false,
      hasGarage: map['hasGarage'] ?? false,
      hasSwimmingPool: map['hasSwimmingPool'] ?? false,
      hasGarden: map['hasGarden'] ?? false,
      hasElevator: map['hasElevator'] ?? false,
      hasBalcony: map['hasBalcony'] ?? false,
      isFullyFurnished: map['isFullyFurnished'] ?? false,
      isSemilyFurnished: map['isSemilyFurnished'] ?? false,
      hasPrivateEntrance: map['hasPrivateEntrance'] ?? false,
      hasSecurity: map['hasSecurity'] ?? false,
      hasGym: map['hasGym'] ?? false,
      facingDirection: map['facingDirection'],
      finishingType: map['finishingType'],
      viewType: map['viewType'],
      furnishingDetails: map['furnishingDetails'],
      paymentMethod: map['paymentMethod'],
      contractType: map['contractType'],
      propertyType: map['propertyType'],
      rentalPeriod: map['rentalPeriod'],
      isRentalWithOption: map['isRentalWithOption'],
      insuranceAmount: map['insuranceAmount'],
      commissionAmount: map['commissionAmount']);
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        price,
        location,
        photoUrls,
        isFeatured,
        status,
        governorate,
        city,
        district,
        block,
        latitude,
        longitude,
        shareLocation,
        mainCategory,
        subCategory,
        ownerId,
        advertiserName,
        advertiserPhone,
        advertiserImage,
        advertiserType,
        advertiserJoinDate,
        advertiserAdsCount,
        hidePhone,
        extraPhones,
        createdAt,
        updatedAt,
        startDate,
        endDate,
        viewsCount,
        favoritesCount,
        contactCount,
        subscriptionPlan,
        autoRepublish,
        isPinned,
        isPromoted,
        isVIP,
        isVerified,
        originalEstateId,
        isOriginal,
        copiedBy,
        floorPlanUrl,
        virtualTourUrl,
        videoUrl,
        documents,
        area,
        numberOfRooms,
        numberOfBathrooms,
        numberOfLivingRooms,
        numberOfFloors,
        floorNumber,
        buildingAge,
        hasCentralAC,
        hasMaidRoom,
        hasGarage,
        hasSwimmingPool,
        hasGarden,
        hasElevator,
        hasBalcony,
        isFullyFurnished,
        isSemilyFurnished,
        hasPrivateEntrance,
        hasSecurity,
        hasGym,
        facingDirection,
        finishingType,
        viewType,
        furnishingDetails,
        paymentMethod,
        contractType,
        propertyType,
        rentalPeriod,
        isRentalWithOption,
        insuranceAmount,
        commissionAmount,
      ];
}
