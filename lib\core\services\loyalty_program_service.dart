import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// مستويات برنامج الولاء
enum LoyaltyLevel {
  /// المستوى البرونزي (0-99 نقطة)
  bronze,

  /// المستوى الفضي (100-499 نقطة)
  silver,

  /// المستوى الذهبي (500-999 نقطة)
  gold,

  /// المستوى البلاتيني (1000-2499 نقطة)
  platinum,

  /// المستوى الماسي (2500-4999 نقطة)
  diamond,

  /// المستوى VIP (5000+ نقطة)
  vip
}

/// نموذج بيانات برنامج الولاء
class LoyaltyProgramData {
  /// معرف المستخدم
  final String userId;

  /// عدد النقاط الحالية
  final int points;

  /// إجمالي النقاط المكتسبة
  final int totalPointsEarned;

  /// إجمالي النقاط المستخدمة
  final int totalPointsRedeemed;

  /// مستوى الولاء
  final LoyaltyLevel level;

  /// تاريخ آخر تحديث
  final DateTime lastUpdated;

  /// تاريخ انتهاء صلاحية النقاط
  final DateTime pointsExpiryDate;

  /// سجل النقاط
  final List<Map<String, dynamic>> pointsHistory;

  LoyaltyProgramData({
    required this.userId,
    required this.points,
    required this.totalPointsEarned,
    required this.totalPointsRedeemed,
    required this.level,
    required this.lastUpdated,
    required this.pointsExpiryDate,
    required this.pointsHistory,
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory LoyaltyProgramData.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return LoyaltyProgramData(
      userId: doc.id,
      points: data['points'] ?? 0,
      totalPointsEarned: data['totalPointsEarned'] ?? 0,
      totalPointsRedeemed: data['totalPointsRedeemed'] ?? 0,
      level: _getLevelFromPoints(data['points'] ?? 0),
      lastUpdated: (data['lastUpdated'] as Timestamp).toDate(),
      pointsExpiryDate: (data['pointsExpiryDate'] as Timestamp).toDate(),
      pointsHistory:
          List<Map<String, dynamic>>.from(data['pointsHistory'] ?? []));
  }

  /// تحويل النموذج إلى Map لتخزينه في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'points': points,
      'totalPointsEarned': totalPointsEarned,
      'totalPointsRedeemed': totalPointsRedeemed,
      'level': level.toString(),
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'pointsExpiryDate': Timestamp.fromDate(pointsExpiryDate),
      'pointsHistory': pointsHistory,
    };
  }

  /// الحصول على مستوى الولاء من عدد النقاط
  static LoyaltyLevel _getLevelFromPoints(int points) {
    if (points >= 5000) {
      return LoyaltyLevel.vip;
    } else if (points >= 2500) {
      return LoyaltyLevel.diamond;
    } else if (points >= 1000) {
      return LoyaltyLevel.platinum;
    } else if (points >= 500) {
      return LoyaltyLevel.gold;
    } else if (points >= 100) {
      return LoyaltyLevel.silver;
    } else {
      return LoyaltyLevel.bronze;
    }
  }

  /// الحصول على النسبة المئوية للتقدم نحو المستوى التالي
  double getProgressToNextLevel() {
    switch (level) {
      case LoyaltyLevel.bronze:
        return points / 100; // 0-99 نقطة
      case LoyaltyLevel.silver:
        return (points - 100) / 400; // 100-499 نقطة
      case LoyaltyLevel.gold:
        return (points - 500) / 500; // 500-999 نقطة
      case LoyaltyLevel.platinum:
        return (points - 1000) / 1500; // 1000-2499 نقطة
      case LoyaltyLevel.diamond:
        return (points - 2500) / 2500; // 2500-4999 نقطة
      case LoyaltyLevel.vip:
        return 1.0; // أعلى مستوى
    }
  }

  /// الحصول على عدد النقاط المطلوبة للوصول إلى المستوى التالي
  int getPointsToNextLevel() {
    switch (level) {
      case LoyaltyLevel.bronze:
        return 100 - points; // 100 نقطة للمستوى الفضي
      case LoyaltyLevel.silver:
        return 500 - points; // 500 نقطة للمستوى الذهبي
      case LoyaltyLevel.gold:
        return 1000 - points; // 1000 نقطة للمستوى البلاتيني
      case LoyaltyLevel.platinum:
        return 2500 - points; // 2500 نقطة للمستوى الماسي
      case LoyaltyLevel.diamond:
        return 5000 - points; // 5000 نقطة للمستوى VIP
      case LoyaltyLevel.vip:
        return 0; // أعلى مستوى
    }
  }

  /// الحصول على اسم المستوى التالي
  String getNextLevelName() {
    switch (level) {
      case LoyaltyLevel.bronze:
        return 'فضي';
      case LoyaltyLevel.silver:
        return 'ذهبي';
      case LoyaltyLevel.gold:
        return 'بلاتيني';
      case LoyaltyLevel.platinum:
        return 'ماسي';
      case LoyaltyLevel.diamond:
        return 'VIP';
      case LoyaltyLevel.vip:
        return 'VIP';
    }
  }

  /// إنشاء نسخة جديدة مع تحديث بعض الحقول
  LoyaltyProgramData copyWith({
    String? userId,
    int? points,
    int? totalPointsEarned,
    int? totalPointsRedeemed,
    LoyaltyLevel? level,
    DateTime? lastUpdated,
    DateTime? pointsExpiryDate,
    List<Map<String, dynamic>>? pointsHistory,
  }) {
    // حساب المستوى الجديد بناءً على النقاط إذا تم تحديثها
    final newPoints = points ?? this.points;
    final newLevel =
        level ?? (points != null ? _getLevelFromPoints(newPoints) : this.level);

    return LoyaltyProgramData(
      userId: userId ?? this.userId,
      points: newPoints,
      totalPointsEarned: totalPointsEarned ?? this.totalPointsEarned,
      totalPointsRedeemed: totalPointsRedeemed ?? this.totalPointsRedeemed,
      level: newLevel,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      pointsExpiryDate: pointsExpiryDate ?? this.pointsExpiryDate,
      pointsHistory: pointsHistory ?? this.pointsHistory);
  }
}

/// خدمة برنامج الولاء
class LoyaltyProgramService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // تخزين مؤقت للبيانات
  LoyaltyProgramData? _cachedLoyaltyData;
  DateTime? _lastCacheTime;

  // مدة صلاحية التخزين المؤقت (5 دقائق)
  static const Duration _cacheDuration = Duration(minutes: 5);

  // تم استبدال هذا بتخزين تاريخ آخر تسجيل دخول في Firestore

  /// الحصول على بيانات برنامج الولاء للمستخدم الحالي
  /// إذا لم يكن للمستخدم بيانات برنامج ولاء، يتم إنشاؤها تلقائيًا
  /// مع إضافة نقاط للمستخدمين القدامى بناءً على نشاطهم السابق
  Future<LoyaltyProgramData?> getCurrentUserLoyaltyData(
      {bool forceRefresh = false}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return null;
      }

      // استخدام البيانات المخزنة مؤقتًا إذا كانت حديثة
      if (!forceRefresh &&
          _cachedLoyaltyData != null &&
          _lastCacheTime != null) {
        final cacheAge = DateTime.now().difference(_lastCacheTime!);
        if (cacheAge < _cacheDuration &&
            _cachedLoyaltyData!.userId == user.uid) {
          return _cachedLoyaltyData;
        }
      }

      final doc =
          await _firestore.collection('loyaltyProgram').doc(user.uid).get();

      if (!doc.exists) {
        // هذا مستخدم ليس لديه بيانات برنامج ولاء (قد يكون مستخدمًا قديمًا)
        // سنقوم بإنشاء بيانات برنامج الولاء له ومنحه نقاطًا بناءً على نشاطه السابق
        return await _createLoyaltyDataForExistingUser(user.uid);
      }

      final loyaltyData = LoyaltyProgramData.fromFirestore(doc);

      // تحديث التخزين المؤقت
      _cachedLoyaltyData = loyaltyData;
      _lastCacheTime = DateTime.now();

      return loyaltyData;
    } catch (e) {
      return null;
    }
  }

  /// إنشاء بيانات برنامج الولاء لمستخدم موجود
  /// يتم منح المستخدم نقاطًا بناءً على نشاطه السابق
  Future<LoyaltyProgramData> _createLoyaltyDataForExistingUser(
      String userId) async {
    try {
      // جمع البيانات عن نشاط المستخدم السابق
      int initialPoints = 0;
      List<Map<String, dynamic>> pointsHistory = [];

      // 1. التحقق من وجود المستخدم وتاريخ إنشاء الحساب
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        // إضافة نقاط لإنشاء الحساب (50 نقطة)
        initialPoints += 50;
        pointsHistory.add({
          'date': Timestamp.now(),
          'points': 50,
          'type': 'earn',
          'reason': 'إنشاء حساب (مستخدم قديم)',
          'balance': 50,
        });

        // التحقق من تأكيد البريد الإلكتروني
        final isEmailVerified = userDoc.data()?['isEmailVerified'] ?? false;
        if (isEmailVerified) {
          initialPoints += 20;
          pointsHistory.add({
            'date': Timestamp.now(),
            'points': 20,
            'type': 'earn',
            'reason': 'تأكيد البريد الإلكتروني (مستخدم قديم)',
            'balance': initialPoints,
          });
        }

        // التحقق من اكتمال الملف الشخصي
        final hasFullProfile =
            _checkIfProfileIsComplete(userDoc.data() as Map<String, dynamic>);
        if (hasFullProfile) {
          initialPoints += 30;
          pointsHistory.add({
            'date': Timestamp.now(),
            'points': 30,
            'type': 'earn',
            'reason': 'اكتمال الملف الشخصي (مستخدم قديم)',
            'balance': initialPoints,
          });
        }
      }

      // 2. التحقق من الإعلانات السابقة
      final estatesQuery = await _firestore
          .collection('estates')
          .where('userId', isEqualTo: userId)
          .get();

      if (estatesQuery.docs.isNotEmpty) {
        // إضافة نقاط لكل إعلان (10 نقاط لكل إعلان، بحد أقصى 100 نقطة)
        final estatesCount = estatesQuery.docs.length;
        final estatesPoints = estatesCount > 10 ? 100 : estatesCount * 10;

        if (estatesPoints > 0) {
          initialPoints += estatesPoints;
          pointsHistory.add({
            'date': Timestamp.now(),
            'points': estatesPoints,
            'type': 'earn',
            'reason': 'الإعلانات السابقة (مستخدم قديم)',
            'balance': initialPoints,
          });
        }
      }

      // 3. التحقق من المشتريات السابقة
      final purchasesQuery = await _firestore
          .collection('subscriptions')
          .where('userId', isEqualTo: userId)
          .get();

      if (purchasesQuery.docs.isNotEmpty) {
        // إضافة نقاط لكل عملية شراء (50 نقطة لكل عملية، بحد أقصى 200 نقطة)
        final purchasesCount = purchasesQuery.docs.length;
        final purchasesPoints = purchasesCount > 4 ? 200 : purchasesCount * 50;

        if (purchasesPoints > 0) {
          initialPoints += purchasesPoints;
          pointsHistory.add({
            'date': Timestamp.now(),
            'points': purchasesPoints,
            'type': 'earn',
            'reason': 'المشتريات السابقة (مستخدم قديم)',
            'balance': initialPoints,
          });
        }
      }

      // إضافة 5 نقاط لتسجيل الدخول اليوم
      initialPoints += 5;
      pointsHistory.add({
        'date': Timestamp.now(),
        'points': 5,
        'type': 'earn',
        'reason': 'تسجيل دخول يومي',
        'balance': initialPoints,
      });

      // إنشاء بيانات برنامج الولاء
      final loyaltyLevel =
          LoyaltyProgramData._getLevelFromPoints(initialPoints);
      final newData = LoyaltyProgramData(
        userId: userId,
        points: initialPoints,
        totalPointsEarned: initialPoints,
        totalPointsRedeemed: 0,
        level: loyaltyLevel,
        lastUpdated: DateTime.now(),
        pointsExpiryDate: DateTime.now().add(const Duration(days: 365)),
        pointsHistory: pointsHistory);

      // حفظ البيانات في Firestore
      await _firestore
          .collection('loyaltyProgram')
          .doc(userId)
          .set(newData.toFirestore());

      // تحديث التخزين المؤقت
      _cachedLoyaltyData = newData;
      _lastCacheTime = DateTime.now();

      return newData;
    } catch (e) {
      // في حالة حدوث خطأ، نعود إلى البيانات الافتراضية
      final defaultData = LoyaltyProgramData(
        userId: userId,
        points: 55, // 50 لإنشاء الحساب + 5 لتسجيل الدخول
        totalPointsEarned: 55,
        totalPointsRedeemed: 0,
        level: LoyaltyLevel.bronze,
        lastUpdated: DateTime.now(),
        pointsExpiryDate: DateTime.now().add(const Duration(days: 365)),
        pointsHistory: [
          {
            'date': Timestamp.now(),
            'points': 50,
            'type': 'earn',
            'reason': 'إنشاء حساب (مستخدم قديم)',
            'balance': 50,
          },
          {
            'date': Timestamp.now(),
            'points': 5,
            'type': 'earn',
            'reason': 'تسجيل دخول يومي',
            'balance': 55,
          }
        ]);

      // حفظ البيانات في Firestore
      await _firestore
          .collection('loyaltyProgram')
          .doc(userId)
          .set(defaultData.toFirestore());

      // تحديث التخزين المؤقت
      _cachedLoyaltyData = defaultData;
      _lastCacheTime = DateTime.now();

      return defaultData;
    }
  }

  /// التحقق مما إذا كان الملف الشخصي مكتملاً
  bool _checkIfProfileIsComplete(Map<String, dynamic> userData) {
    // التحقق من وجود الحقول الأساسية
    final requiredFields = [
      'fullNameOrCompanyName',
      'phone',
      'address',
    ];

    for (final field in requiredFields) {
      final value = userData[field];
      if (value == null || (value is String && value.isEmpty)) {
        return false;
      }
    }

    return true;
  }

  /// الحصول على بيانات برنامج الولاء كتدفق (stream)
  Stream<LoyaltyProgramData?> getLoyaltyDataStream() {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.value(null);
    }

    return _firestore
        .collection('loyaltyProgram')
        .doc(user.uid)
        .snapshots()
        .map((doc) {
      if (!doc.exists) {
        return null;
      }

      final loyaltyData = LoyaltyProgramData.fromFirestore(doc);

      // تحديث التخزين المؤقت
      _cachedLoyaltyData = loyaltyData;
      _lastCacheTime = DateTime.now();

      return loyaltyData;
    });
  }

  /// إضافة نقاط للمستخدم
  /// يمكن تحديد معرف المستخدم المراد إضافة النقاط له، وإلا سيتم استخدام المستخدم الحالي
  Future<bool> addPoints(int points, String reason, {String? userId}) async {
    try {
      if (points <= 0) {
        return false; // لا يمكن إضافة نقاط سالبة أو صفرية
      }

      // تحديد معرف المستخدم
      String targetUserId;
      if (userId != null) {
        // استخدام المعرف المحدد
        targetUserId = userId;
      } else {
        // استخدام المستخدم الحالي
        final user = _auth.currentUser;
        if (user == null) {
          return false;
        }
        targetUserId = user.uid;
      }

      // الحصول على بيانات برنامج الولاء الحالية
      DocumentSnapshot? loyaltyDoc;
      try {
        loyaltyDoc = await _firestore
            .collection('loyaltyProgram')
            .doc(targetUserId)
            .get();
      } catch (e) {
        return false;
      }

      int currentPoints = 0;
      if (loyaltyDoc.exists) {
        final data = loyaltyDoc.data() as Map<String, dynamic>;
        currentPoints = data['points'] ?? 0;
      } else {
        // إنشاء بيانات برنامج الولاء للمستخدم إذا لم تكن موجودة
        await _createLoyaltyDataForExistingUser(targetUserId);
        loyaltyDoc = await _firestore
            .collection('loyaltyProgram')
            .doc(targetUserId)
            .get();
        if (loyaltyDoc.exists) {
          final data = loyaltyDoc.data() as Map<String, dynamic>;
          currentPoints = data['points'] ?? 0;
        }
      }

      // إنشاء سجل جديد في تاريخ النقاط
      final historyEntry = {
        'date': Timestamp.now(),
        'points': points,
        'type': 'earn',
        'reason': reason,
        'balance': currentPoints + points,
      };

      // تحديث بيانات برنامج الولاء
      final updateTime = Timestamp.now();
      await _firestore.collection('loyaltyProgram').doc(targetUserId).update({
        'points': FieldValue.increment(points),
        'totalPointsEarned': FieldValue.increment(points),
        'lastUpdated': updateTime,
        'pointsHistory': FieldValue.arrayUnion([historyEntry]),
      });

      // تحديث التخزين المؤقت إذا كان المستخدم هو المستخدم الحالي
      if (_cachedLoyaltyData != null &&
          _cachedLoyaltyData!.userId == targetUserId) {
        _cachedLoyaltyData = _cachedLoyaltyData!.copyWith(
          points: _cachedLoyaltyData!.points + points,
          totalPointsEarned: _cachedLoyaltyData!.totalPointsEarned + points,
          lastUpdated: DateTime.now(),
          pointsHistory: [..._cachedLoyaltyData!.pointsHistory, historyEntry]);
        _lastCacheTime = DateTime.now();
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// استخدام نقاط المستخدم
  Future<bool> redeemPoints(int points, String reason) async {
    try {
      if (points <= 0) {
        return false; // لا يمكن استخدام نقاط سالبة أو صفرية
      }

      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // الحصول على بيانات برنامج الولاء الحالية
      final loyaltyData = await getCurrentUserLoyaltyData();
      if (loyaltyData == null) {
        return false;
      }

      // التحقق من وجود نقاط كافية
      if (loyaltyData.points < points) {
        return false;
      }

      // إنشاء سجل جديد في تاريخ النقاط
      final historyEntry = {
        'date': Timestamp.now(),
        'points': -points,
        'type': 'redeem',
        'reason': reason,
        'balance': loyaltyData.points - points,
      };

      // تحديث بيانات برنامج الولاء
      final updateTime = Timestamp.now();
      await _firestore.collection('loyaltyProgram').doc(user.uid).update({
        'points': FieldValue.increment(-points),
        'totalPointsRedeemed': FieldValue.increment(points),
        'lastUpdated': updateTime,
        'pointsHistory': FieldValue.arrayUnion([historyEntry]),
      });

      // تحديث التخزين المؤقت
      if (_cachedLoyaltyData != null &&
          _cachedLoyaltyData!.userId == user.uid) {
        _cachedLoyaltyData = _cachedLoyaltyData!.copyWith(
          points: _cachedLoyaltyData!.points - points,
          totalPointsRedeemed: _cachedLoyaltyData!.totalPointsRedeemed + points,
          lastUpdated: DateTime.now(),
          pointsHistory: [..._cachedLoyaltyData!.pointsHistory, historyEntry]);
        _lastCacheTime = DateTime.now();
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على تاريخ النقاط للمستخدم
  Future<List<Map<String, dynamic>>> getPointsHistory() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return [];
      }

      final loyaltyData = await getCurrentUserLoyaltyData();
      if (loyaltyData == null) {
        return [];
      }

      // ترتيب التاريخ من الأحدث إلى الأقدم
      final history =
          List<Map<String, dynamic>>.from(loyaltyData.pointsHistory);
      history.sort((a, b) {
        final dateA = (a['date'] as Timestamp).toDate();
        final dateB = (b['date'] as Timestamp).toDate();
        return dateB.compareTo(dateA);
      });

      return history;
    } catch (e) {
      return [];
    }
  }

  /// الحصول على مزايا المستوى الحالي
  List<Map<String, dynamic>> getCurrentLevelBenefits(LoyaltyLevel level) {
    switch (level) {
      case LoyaltyLevel.bronze:
        return [
          {'title': 'خصم 5% على باقات الإعلانات', 'icon': 'discount'},
          {'title': 'إمكانية نشر 3 إعلانات مجانية', 'icon': 'ad'},
          {'title': 'دعم العملاء العادي', 'icon': 'support'},
        ];
      case LoyaltyLevel.silver:
        return [
          {'title': 'خصم 10% على باقات الإعلانات', 'icon': 'discount'},
          {'title': 'إمكانية نشر 5 إعلانات مجانية', 'icon': 'ad'},
          {'title': 'تثبيت إعلان واحد مجاني', 'icon': 'pin'},
          {'title': 'دعم العملاء ذو أولوية', 'icon': 'support'},
        ];
      case LoyaltyLevel.gold:
        return [
          {'title': 'خصم 15% على باقات الإعلانات', 'icon': 'discount'},
          {'title': 'إمكانية نشر 10 إعلانات مجانية', 'icon': 'ad'},
          {'title': 'تثبيت 3 إعلانات مجانية', 'icon': 'pin'},
          {'title': 'إعلان VIP مجاني', 'icon': 'vip'},
          {'title': 'دعم العملاء المميز', 'icon': 'support'},
        ];
      case LoyaltyLevel.platinum:
        return [
          {'title': 'خصم 25% على باقات الإعلانات', 'icon': 'discount'},
          {'title': 'إمكانية نشر إعلانات غير محدودة', 'icon': 'ad'},
          {'title': 'تثبيت 5 إعلانات مجانية', 'icon': 'pin'},
          {'title': '3 إعلانات VIP مجانية', 'icon': 'vip'},
          {'title': 'دعم العملاء الحصري', 'icon': 'support'},
          {'title': 'شارة بلاتينية على الملف الشخصي', 'icon': 'badge'},
        ];
      case LoyaltyLevel.diamond:
        return [
          {'title': 'خصم 35% على باقات الإعلانات', 'icon': 'discount'},
          {'title': 'إمكانية نشر إعلانات غير محدودة', 'icon': 'ad'},
          {'title': 'تثبيت 10 إعلانات مجانية', 'icon': 'pin'},
          {'title': '5 إعلانات VIP مجانية', 'icon': 'vip'},
          {'title': 'دعم العملاء الماسي 24/7', 'icon': 'support'},
          {'title': 'شارة ماسية على الملف الشخصي', 'icon': 'badge'},
          {'title': 'وصول مبكر للميزات الجديدة', 'icon': 'early_access'},
          {'title': 'تقارير تحليلية متقدمة', 'icon': 'analytics'},
        ];
      case LoyaltyLevel.vip:
        return [
          {'title': 'خصم 50% على باقات الإعلانات', 'icon': 'discount'},
          {'title': 'إمكانية نشر إعلانات غير محدودة', 'icon': 'ad'},
          {'title': 'تثبيت إعلانات غير محدود', 'icon': 'pin'},
          {'title': 'إعلانات VIP غير محدودة', 'icon': 'vip'},
          {'title': 'مدير حساب شخصي', 'icon': 'support'},
          {'title': 'شارة VIP ذهبية على الملف الشخصي', 'icon': 'badge'},
          {'title': 'وصول حصري للميزات التجريبية', 'icon': 'early_access'},
          {'title': 'تقارير تحليلية شاملة', 'icon': 'analytics'},
          {'title': 'استشارات عقارية مجانية', 'icon': 'consultation'},
          {'title': 'أولوية في عرض الإعلانات', 'icon': 'priority'},
        ];
    }
  }

  /// الحصول على قائمة الأنشطة التي تمنح نقاط
  List<Map<String, dynamic>> getPointsActivities() {
    return [
      {'title': 'إنشاء حساب جديد', 'points': 50, 'icon': 'person_add'},
      {'title': 'تأكيد البريد الإلكتروني', 'points': 20, 'icon': 'email'},
      {'title': 'إكمال الملف الشخصي', 'points': 30, 'icon': 'person'},
      {'title': 'نشر إعلان جديد', 'points': 10, 'icon': 'post_add'},
      {'title': 'تحديث إعلان', 'points': 5, 'icon': 'update'},
      {'title': 'شراء باقة إعلانات', 'points': 50, 'icon': 'shopping_cart'},
      {'title': 'تقييم التطبيق', 'points': 25, 'icon': 'star'},
      {'title': 'مشاركة التطبيق', 'points': 15, 'icon': 'share'},
      {'title': 'دعوة صديق', 'points': 30, 'icon': 'person_add'},
      {'title': 'تسجيل دخول يومي', 'points': 5, 'icon': 'login'},
    ];
  }

  /// إضافة نقاط عند إنشاء حساب جديد
  Future<bool> addPointsForNewAccount(String userId) async {
    return await addPoints(50, 'إنشاء حساب جديد');
  }

  /// إضافة نقاط عند تأكيد البريد الإلكتروني
  Future<bool> addPointsForEmailVerification() async {
    return await addPoints(20, 'تأكيد البريد الإلكتروني');
  }

  /// إضافة نقاط عند إكمال الملف الشخصي
  Future<bool> addPointsForCompletingProfile() async {
    return await addPoints(30, 'إكمال الملف الشخصي');
  }

  /// إضافة نقاط عند نشر إعلان جديد
  Future<bool> addPointsForNewAd() async {
    return await addPoints(10, 'نشر إعلان جديد');
  }

  /// إضافة نقاط عند تحديث إعلان
  Future<bool> addPointsForUpdatingAd() async {
    return await addPoints(5, 'تحديث إعلان');
  }

  /// إضافة نقاط عند شراء باقة إعلانات
  Future<bool> addPointsForPurchasingPlan() async {
    return await addPoints(50, 'شراء باقة إعلانات');
  }

  /// إضافة نقاط عند تقييم التطبيق
  Future<bool> addPointsForRatingApp() async {
    return await addPoints(25, 'تقييم التطبيق');
  }

  /// إضافة نقاط عند مشاركة التطبيق
  Future<bool> addPointsForSharingApp() async {
    return await addPoints(15, 'مشاركة التطبيق');
  }

  /// إضافة نقاط عند تسجيل الدخول اليومي
  Future<bool> addPointsForDailyLogin() async {
    final user = _auth.currentUser;
    if (user == null) {
      return false;
    }

    try {
      // التحقق من آخر تسجيل دخول من Firestore
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) {
        return false;
      }

      final lastLoginTimestamp = userDoc.data()?['lastLoginDate'] as Timestamp?;
      final now = DateTime.now();

      if (lastLoginTimestamp != null) {
        final lastLoginDate = lastLoginTimestamp.toDate();
        final today = DateTime(now.year, now.month, now.day);
        final lastLogin = DateTime(
            lastLoginDate.year, lastLoginDate.month, lastLoginDate.day);

        // إذا كان آخر تسجيل دخول في نفس اليوم، لا تضف نقاطًا
        if (today.isAtSameMomentAs(lastLogin)) {
          return false;
        }
      }

      // تحديث تاريخ آخر تسجيل دخول في Firestore
      await _firestore.collection('users').doc(user.uid).update({
        'lastLoginDate': Timestamp.now(),
      });

      // إضافة النقاط
      return await addPoints(5, 'تسجيل دخول يومي');
    } catch (e) {
      // تسجيل الخطأ بطريقة آمنة (يمكن استبدالها بنظام تسجيل أخطاء مناسب)
      return false;
    }
  }

  /// إضافة نقاط عند قراءة سياسة الخصوصية
  Future<bool> addPointsForReadingPolicy() async {
    return await addPoints(5, 'قراءة سياسة الخصوصية');
  }

  /// إضافة نقاط عند قراءة شروط الاستخدام
  Future<bool> addPointsForReadingTerms() async {
    return await addPoints(5, 'قراءة شروط الاستخدام');
  }

  /// إضافة نقاط عند زيارة صفحة الدعم
  Future<bool> addPointsForVisitingSupport() async {
    return await addPoints(5, 'زيارة صفحة الدعم');
  }

  /// إضافة نقاط عند تحديث الملف الشخصي
  Future<bool> addPointsForUpdatingProfile() async {
    return await addPoints(10, 'تحديث الملف الشخصي');
  }

  /// إضافة نقاط عند إضافة عقار للمفضلة
  Future<bool> addPointsForAddingToFavorites() async {
    return await addPoints(2, 'إضافة عقار للمفضلة');
  }

  /// إضافة نقاط عند البحث عن عقارات
  Future<bool> addPointsForSearchingProperties() async {
    return await addPoints(1, 'البحث عن عقارات');
  }

  /// إضافة نقاط عند المشاركة في المنتدى
  Future<bool> addPointsForForumParticipation() async {
    return await addPoints(5, 'المشاركة في المنتدى');
  }
}
