import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/post_model.dart';
import '../../../domain/models/forum/topic_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/empty_view.dart';

/// صفحة تفاصيل الموضوع
class TopicDetailsPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/topic';

  /// معرف الموضوع
  final String topicId;

  const TopicDetailsPage({super.key, required this.topicId});

  @override
  State<TopicDetailsPage> createState() => _TopicDetailsPageState();
}

class _TopicDetailsPageState extends State<TopicDetailsPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _replyController = TextEditingController();
  bool _isReplying = false;
  bool _isBookmarked = false;
  bool _isFollowing = false;
  bool _isLiked = false;

  @override
  void initState() {
    super.initState();

    // إضافة مستمع للتمرير لتحميل المزيد من المشاركات
    _scrollController.addListener(_scrollListener);

    // جلب البيانات عند تحميل الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _replyController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // جلب تفاصيل الموضوع
    forumProvider.fetchTopic(widget.topicId);

    // جلب المشاركات
    forumProvider.fetchPosts(widget.topicId, refresh: true);

    // التحقق مما إذا كان المستخدم يتابع الموضوع
    if (authProvider.isLoggedIn) {
      _isFollowing = await forumProvider.isFollowingTopic(
        widget.topicId,
        authProvider.user!.uid);

      // التحقق مما إذا كان المستخدم قد أضاف إشارة مرجعية للموضوع
      _isBookmarked = await forumProvider.isTopicBookmarked(
        widget.topicId,
        authProvider.user!.uid);

      // التحقق مما إذا كان المستخدم قد أعجب بالموضوع
      // هذا يتطلب تنفيذ دالة للتحقق من الإعجاب
      // _isLiked = await forumProvider.isTopicLiked(widget.topicId, authProvider.user!.uid);

      setState(() {});
    }
  }

  /// مستمع التمرير لتحميل المزيد من المشاركات
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      if (forumProvider.postsState != LoadingState.loading &&
          forumProvider.hasMorePosts) {
        forumProvider.fetchPosts(widget.topicId);
      }
    }
  }

  /// إرسال رد
  Future<void> _sendReply() async {
    if (_replyController.text.trim().isEmpty) return;

    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يجب تسجيل الدخول للرد على الموضوع')));
      return;
    }

    setState(() {
      _isReplying = true;
    });

    try {
      final post = PostModel(
        id: '',
        topicId: widget.topicId,
        topicTitle: forumProvider.currentTopic?.title ?? '',
        categoryId: forumProvider.currentTopic?.categoryId ?? '',
        categoryName: forumProvider.currentTopic?.categoryName ?? '',
        userId: authProvider.user!.uid,
        userName: authProvider.user!.displayName ?? 'مستخدم',
        userImage: authProvider.user!.photoURL,
        content: _replyController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());

      await forumProvider.createPost(post);

      _replyController.clear();

      // تمرير للأسفل لرؤية الرد الجديد
      await Future.delayed(Duration(milliseconds: 300));
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeOut);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء إرسال الرد')));
    } finally {
      setState(() {
        _isReplying = false;
      });
    }
  }

  /// متابعة الموضوع
  Future<void> _toggleFollow() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يجب تسجيل الدخول لمتابعة الموضوع')));
      return;
    }

    setState(() {
      _isFollowing = !_isFollowing;
    });

    try {
      if (_isFollowing) {
        await forumProvider.followTopic(widget.topicId, authProvider.user!.uid);
      } else {
        await forumProvider.unfollowTopic(
            widget.topicId, authProvider.user!.uid);
      }
    } catch (e) {
      setState(() {
        _isFollowing = !_isFollowing;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تغيير حالة المتابعة')));
    }
  }

  /// إضافة إشارة مرجعية للموضوع
  Future<void> _toggleBookmark() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يجب تسجيل الدخول لإضافة إشارة مرجعية')));
      return;
    }

    setState(() {
      _isBookmarked = !_isBookmarked;
    });

    try {
      if (_isBookmarked) {
        await forumProvider.bookmarkTopic(
            widget.topicId, authProvider.user!.uid);
      } else {
        await forumProvider.unbookmarkTopic(
            widget.topicId, authProvider.user!.uid);
      }
    } catch (e) {
      setState(() {
        _isBookmarked = !_isBookmarked;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تغيير حالة الإشارة المرجعية')));
    }
  }

  /// الإعجاب بالموضوع
  Future<void> _toggleLike() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يجب تسجيل الدخول للإعجاب بالموضوع')));
      return;
    }

    setState(() {
      _isLiked = !_isLiked;
    });

    try {
      if (_isLiked) {
        await forumProvider.likeTopic(widget.topicId, authProvider.user!.uid);
      } else {
        await forumProvider.unlikeTopic(widget.topicId, authProvider.user!.uid);
      }
    } catch (e) {
      setState(() {
        _isLiked = !_isLiked;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تغيير حالة الإعجاب')));
    }
  }

  /// مشاركة الموضوع
  Future<void> _shareTopic() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final topic = forumProvider.currentTopic;

    if (topic == null) return;

    // مشاركة الموضوع
    await Share.share(
      'شاهد هذا الموضوع: ${topic.title}\n\nتطبيق Krea',
      subject: topic.title);

    // تسجيل المشاركة في Firebase إذا كان المستخدم مسجل الدخول
    if (authProvider.isLoggedIn) {
      await forumProvider.shareTopic(widget.topicId, authProvider.user!.uid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تفاصيل الموضوع'),
        actions: [
          IconButton(
            icon: Icon(_isBookmarked ? Icons.bookmark : Icons.bookmark_border),
            onPressed: _toggleBookmark),
          IconButton(
            icon: Icon(_isFollowing
                ? Icons.notifications_active
                : Icons.notifications_none),
            onPressed: _toggleFollow),
          IconButton(
            icon: Icon(Icons.share),
            onPressed: _shareTopic),
        ]),
      body: Column(
        children: [
          Expanded(
            child: _buildTopicDetails()),
          _buildReplyInput(),
        ]));
  }

  /// بناء تفاصيل الموضوع
  Widget _buildTopicDetails() {
    return Consumer<ForumProvider>(
      builder: (context, forumProvider, child) {
        if (forumProvider.topicState == LoadingState.loading) {
          return Center(child: LoadingIndicator());
        } else if (forumProvider.topicState == LoadingState.error) {
          return ErrorView(
            message: 'حدث خطأ في تحميل الموضوع',
            onRetry: () => forumProvider.fetchTopic(widget.topicId));
        } else if (forumProvider.topicState == LoadingState.empty ||
            forumProvider.currentTopic == null) {
          return EmptyView(
            message: 'الموضوع غير موجود',
            icon: Icons.forum_outlined);
        }

        final topic = forumProvider.currentTopic!;

        return RefreshIndicator(
          onRefresh: () async {
            await forumProvider.fetchTopic(widget.topicId);
            await forumProvider.fetchPosts(widget.topicId, refresh: true);
          },
          child: ListView(
            controller: _scrollController,
            padding: EdgeInsets.all(16),
            children: [
              _buildTopicHeader(topic),
              Divider(height: 32),
              _buildPostsList(forumProvider),
            ]));
      });
  }

  /// بناء رأس الموضوع
  Widget _buildTopicHeader(TopicModel topic) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black.withAlpha(13),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      margin: EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الكاتب والفئة
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade200,
                  width: 1))),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundImage: topic.userImage != null
                      ? NetworkImage(topic.userImage!)
                      : null,
                  radius: 22,
                  backgroundColor: AppColors.primary.withAlpha(25),
                  child: topic.userImage == null ? Icon(Icons.person, color: AppColors.primary) : null),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        topic.userName,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16)),
                      SizedBox(height: 2),
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 12,
                            color: Colors.grey.shade600),
                          SizedBox(width: 4),
                          Text(
                            _formatDate(topic.createdAt),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12)),
                        ]),
                    ])),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(25),
                    borderRadius: BorderRadius.circular(20)),
                  child: Text(
                    topic.categoryName,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500))),
              ])),

          // عنوان الموضوع
          Padding(
            padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Text(
              topic.title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
                height: 1.3))),

          // محتوى الموضوع
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              topic.content,
              style: TextStyle(
                fontSize: 16,
                height: 1.5,
                color: Colors.black87))),

          // صور الموضوع
          if (topic.images != null && topic.images!.isNotEmpty) ...[
            SizedBox(height: 16),
            _buildTopicImages(topic.images!),
          ],

          // الوسوم
          if (topic.tags != null && topic.tags!.isNotEmpty) ...[
            Padding(
              padding: EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ...topic.tags!.map((tag) => Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.blue.shade100,
                        width: 1)),
                    child: Text(
                      tag,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700)))),
                ])),
          ],

          // إحصائيات الموضوع
          Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItemWithLabel(
                  Icons.remove_red_eye_outlined,
                  topic.viewsCount.toString(),
                  'مشاهدة'),
                _buildStatItemWithLabel(
                  Icons.forum_outlined,
                  topic.repliesCount.toString(),
                  'رد'),
                _buildStatItemWithLabel(
                  Icons.favorite_outline,
                  topic.likesCount.toString(),
                  'إعجاب'),
              ])),

          // خط فاصل
          Divider(height: 1, color: Colors.grey.shade200),

          // أزرار التفاعل
          Padding(
            padding: EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(
                  icon: _isLiked ? Icons.favorite : Icons.favorite_border,
                  label: _isLiked ? 'إلغاء الإعجاب' : 'إعجاب',
                  color: _isLiked ? Colors.red : Colors.grey.shade700,
                  onPressed: _toggleLike),
                _buildActionButton(
                  icon: Icons.comment,
                  label: 'رد',
                  color: Colors.grey.shade700,
                  onPressed: () {
                    // التركيز على حقل الرد
                    FocusScope.of(context).requestFocus(FocusNode());
                    _replyController.text = '';
                  }),
                _buildActionButton(
                  icon: _isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                  label: _isBookmarked ? 'إلغاء الحفظ' : 'حفظ',
                  color: _isBookmarked ? AppColors.primary : Colors.grey.shade700,
                  onPressed: _toggleBookmark),
                _buildActionButton(
                  icon: Icons.share,
                  label: 'مشاركة',
                  color: Colors.grey.shade700,
                  onPressed: _shareTopic),
              ])),
        ]));
  }

  /// بناء عنصر إحصائية مع تسمية
  Widget _buildStatItemWithLabel(IconData icon, String count, String label) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: Colors.grey.shade600),
            SizedBox(width: 4),
            Text(
              count,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800)),
          ]),
        SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600)),
      ]);
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    Color? color,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          children: [
            Icon(icon, color: color),
            SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color)),
          ])));
  }

  /// بناء صور الموضوع
  Widget _buildTopicImages(List<String> images) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        height: 200,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: images.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: EdgeInsets.only(right: 12),
              child: InkWell(
                onTap: () {
                  // عرض الصورة بشكل كامل
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => Scaffold(
                        appBar: AppBar(
                          backgroundColor: Colors.black,
                          iconTheme: IconThemeData(color: Colors.white)),
                        backgroundColor: Colors.black,
                        body: Center(
                          child: InteractiveViewer(
                            child: CachedNetworkImage(
                              imageUrl: images[index],
                              placeholder: (context, url) => Center(
                                child: CircularProgressIndicator(
                                  color: Colors.white)),
                              errorWidget: (context, url, error) => Icon(
                                Icons.error,
                                color: Colors.white)))))));
                },
                child: Hero(
                  tag: 'topic_image_${images[index]}',
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: images[index],
                      width: 200,
                      height: 200,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey.shade200,
                        child: Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: AppColors.primary))),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey.shade200,
                        child: Icon(Icons.error, color: Colors.red)))))));
          })));
  }



  /// بناء قائمة المشاركات
  Widget _buildPostsList(ForumProvider forumProvider) {
    if (forumProvider.postsState == LoadingState.loading &&
        forumProvider.posts.isEmpty) {
      return Center(child: LoadingIndicator());
    } else if (forumProvider.postsState == LoadingState.error) {
      return ErrorView(
        message: 'حدث خطأ في تحميل المشاركات',
        onRetry: () => forumProvider.fetchPosts(widget.topicId, refresh: true));
    } else if (forumProvider.postsState == LoadingState.empty) {
      return EmptyView(
        message: 'لا توجد ردود على هذا الموضوع',
        icon: Icons.forum_outlined);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(bottom: 16),
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: AppColors.primary.withAlpha(15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primary.withAlpha(30),
              width: 1)),
          child: Row(
            children: [
              Icon(
                Icons.forum_outlined,
                color: AppColors.primary,
                size: 20),
              SizedBox(width: 8),
              Text(
                'الردود (${forumProvider.posts.length})',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: AppColors.primary)),
            ])),
        AnimationLimiter(
          child: ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: forumProvider.posts.length + (forumProvider.hasMorePosts ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == forumProvider.posts.length) {
                return Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: LoadingIndicator()));
              }

              return AnimationConfiguration.staggeredList(
                position: index,
                duration: const Duration(milliseconds: 375),
                child: SlideAnimation(
                  verticalOffset: 50.0,
                  child: FadeInAnimation(
                    child: _buildPostItem(forumProvider.posts[index]))));
            })),
      ]);
  }

  /// بناء عنصر المشاركة
  Widget _buildPostItem(PostModel post) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 1,
      shadowColor: Colors.black.withAlpha(10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Colors.grey.shade200,
          width: 1)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس المشاركة
            Row(
              children: [
                CircleAvatar(
                  backgroundImage: post.userImage != null
                      ? NetworkImage(post.userImage!)
                      : null,
                  radius: 18,
                  backgroundColor: AppColors.primary.withAlpha(25),
                  child: post.userImage == null
                      ? Icon(Icons.person, size: 18, color: AppColors.primary)
                      : null),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.userName,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14)),
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 12,
                            color: Colors.grey.shade600),
                          SizedBox(width: 4),
                          Text(
                            _formatDate(post.createdAt),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12)),
                        ]),
                    ])),
              ]),

            // محتوى المشاركة
            Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: Text(
                post.content,
                style: TextStyle(
                  fontSize: 15,
                  height: 1.4,
                  color: Colors.black87))),

            // صور المشاركة
            if (post.images != null && post.images!.isNotEmpty) ...[
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: post.images!.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: EdgeInsets.only(right: 8),
                      child: InkWell(
                        onTap: () {
                          // عرض الصورة بشكل كامل
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => Scaffold(
                                appBar: AppBar(
                                  backgroundColor: Colors.black,
                                  iconTheme: IconThemeData(color: Colors.white)),
                                backgroundColor: Colors.black,
                                body: Center(
                                  child: InteractiveViewer(
                                    child: CachedNetworkImage(
                                      imageUrl: post.images![index],
                                      placeholder: (context, url) => Center(
                                        child: CircularProgressIndicator(
                                          color: Colors.white)),
                                      errorWidget: (context, url, error) => Icon(
                                        Icons.error,
                                        color: Colors.white)))))));
                        },
                        child: Hero(
                          tag: 'post_image_${post.images![index]}',
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: CachedNetworkImage(
                              imageUrl: post.images![index],
                              width: 120,
                              height: 120,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: Colors.grey.shade200,
                                child: Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: AppColors.primary))),
                              errorWidget: (context, url, error) => Container(
                                color: Colors.grey.shade200,
                                child: Icon(Icons.error, color: Colors.red)))))));
                  })),
              SizedBox(height: 12),
            ],

            // أزرار التفاعل
            Divider(height: 1, color: Colors.grey.shade200),
            Padding(
              padding: EdgeInsets.only(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  TextButton.icon(
                    icon: Icon(Icons.reply, size: 16, color: AppColors.primary),
                    label: Text(
                      'رد',
                      style: TextStyle(color: AppColors.primary)),
                    onPressed: () {
                      // التركيز على حقل الرد مع اقتباس المشاركة
                      FocusScope.of(context).requestFocus(FocusNode());
                      _replyController.text = 'رداً على @${post.userName}: ';
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20)))),
                  TextButton.icon(
                    icon: Icon(Icons.favorite_border, size: 16, color: Colors.red),
                    label: Text(
                      'إعجاب',
                      style: TextStyle(color: Colors.red)),
                    onPressed: () {
                      // الإعجاب بالمشاركة
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20)))),
                ])),
          ])));
  }

  /// بناء حقل الرد
  Widget _buildReplyInput() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 opacity
            blurRadius: 8,
            offset: Offset(0, -3)),
        ],
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade200,
            width: 1))),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: Colors.grey.shade300,
                  width: 1)),
              child: TextField(
                controller: _replyController,
                decoration: InputDecoration(
                  hintText: 'اكتب رداً...',
                  hintStyle: TextStyle(
                    color: Colors.grey.shade500),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  suffixIcon: IconButton(
                    icon: Icon(
                      Icons.photo_camera,
                      color: Colors.grey.shade600),
                    onPressed: () {
                      // إضافة صورة للرد
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('سيتم دعم إضافة الصور قريباً')));
                    })),
                maxLines: 4,
                minLines: 1,
                textInputAction: TextInputAction.newline,
                style: TextStyle(
                  fontSize: 15)))),
          SizedBox(width: 12),
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary,
                  AppColors.primary.withAlpha(204), // 0.8 opacity
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight),
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withAlpha(76), // 0.3 opacity
                  blurRadius: 8,
                  offset: Offset(0, 3)),
              ]),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _isReplying ? null : _sendReply,
                borderRadius: BorderRadius.circular(30),
                child: Container(
                  padding: EdgeInsets.all(12),
                  child: _isReplying
                      ? SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2))
                      : Icon(
                          Icons.send,
                          color: Colors.white,
                          size: 24))))),
        ]));
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} سنة';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} شهر';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
