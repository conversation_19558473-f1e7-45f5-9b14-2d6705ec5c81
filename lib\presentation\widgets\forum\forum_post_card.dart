import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../domain/models/forum/forum_post_model.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';

/// بطاقة مشاركة المنتدى المحسنة
class ForumPostCard extends StatelessWidget {
  /// مشاركة المنتدى
  final ForumPost post;

  /// دالة عند النقر على زر الإعجاب
  final Function(bool isLiked)? onLike;

  /// دالة عند النقر على زر الرد
  final VoidCallback? onReply;

  /// دالة عند النقر على زر الإبلاغ
  final VoidCallback? onReport;

  /// دالة عند النقر على زر التعديل
  final VoidCallback? onEdit;

  /// دالة عند النقر على زر الحذف
  final VoidCallback? onDelete;

  /// دالة عند النقر على زر تعيين كأفضل إجابة
  final VoidCallback? onMarkAsBestAnswer;

  /// ما إذا كان المستخدم الحالي هو صاحب المشاركة
  final bool isCurrentUser;

  /// ما إذا كان المستخدم الحالي هو صاحب الموضوع
  final bool isTopicOwner;

  /// ما إذا كان المستخدم الحالي قد أعجب بالمشاركة
  final bool isLiked;

  const ForumPostCard({
    super.key,
    required this.post,
    this.onLike,
    this.onReply,
    this.onReport,
    this.onEdit,
    this.onDelete,
    this.onMarkAsBestAnswer,
    this.isCurrentUser = false,
    this.isTopicOwner = false,
    this.isLiked = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: post.isBestAnswer
              ? AppColors.success.withValues(alpha: 0.5)
              : Colors.grey.withValues(alpha: 0.1),
          width: post.isBestAnswer ? 2 : 1)),
      color: Colors.white,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس المشاركة
          _buildPostHeader(context),

          // محتوى المشاركة
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              post.content,
              style: AppTextStyles.body1)),

          // صور المشاركة
          if (post.images != null && post.images!.isNotEmpty)
            _buildPostImages(),

          // تذييل المشاركة
          _buildPostFooter(context),
        ]));
  }

  /// بناء رأس المشاركة
  Widget _buildPostHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: post.isBestAnswer
            ? AppColors.success.withOpacity(0.05)
            : Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12))),
      child: Row(
        children: [
          // صورة المستخدم
          if (post.userImage != null)
            CircleAvatar(
              radius: 20,
              backgroundImage: NetworkImage(post.userImage!))
          else
            CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.primary.withOpacity(0.2),
              child: const Icon(
                Icons.person,
                size: 24,
                color: AppColors.primary)),
          const SizedBox(width: 12),

          // معلومات المستخدم
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      post.userName,
                      style: AppTextStyles.subtitle2.copyWith(
                        fontWeight: FontWeight.bold)),
                    if (isCurrentUser) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4)),
                        child: Text(
                          'أنت',
                          style: AppTextStyles.caption.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold))),
                    ],
                    if (post.isBestAnswer) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.success.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4)),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.check_circle,
                              color: AppColors.success,
                              size: 12),
                            const SizedBox(width: 4),
                            Text(
                              'أفضل إجابة',
                              style: AppTextStyles.caption.copyWith(
                                color: AppColors.success,
                                fontWeight: FontWeight.bold)),
                          ])),
                    ],
                  ]),
                const SizedBox(height: 4),
                Text(
                  timeago.format(post.createdAt, locale: 'ar'),
                  style: AppTextStyles.caption.copyWith(
                    color: Colors.black54)),
              ])),

          // قائمة الخيارات
          PopupMenuButton<String>(
            icon: const Icon(
              Icons.more_vert,
              color: Colors.black54),
            onSelected: (value) {
              switch (value) {
                case 'reply':
                  onReply?.call();
                  break;
                case 'edit':
                  onEdit?.call();
                  break;
                case 'delete':
                  onDelete?.call();
                  break;
                case 'report':
                  onReport?.call();
                  break;
                case 'best_answer':
                  onMarkAsBestAnswer?.call();
                  break;
              }
            },
            itemBuilder: (context) {
              final List<PopupMenuEntry<String>> items = [];

              // خيار الرد
              if (onReply != null) {
                items.add(
                  const PopupMenuItem<String>(
                    value: 'reply',
                    child: Row(
                      children: [
                        Icon(Icons.reply, size: 20),
                        SizedBox(width: 8),
                        Text('رد'),
                      ])));
              }

              // خيار التعديل (للمستخدم الحالي فقط)
              if (isCurrentUser && onEdit != null) {
                items.add(
                  const PopupMenuItem<String>(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ])));
              }

              // خيار الحذف (للمستخدم الحالي فقط)
              if (isCurrentUser && onDelete != null) {
                items.add(
                  const PopupMenuItem<String>(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20),
                        SizedBox(width: 8),
                        Text('حذف'),
                      ])));
              }

              // خيار الإبلاغ (للمستخدمين الآخرين فقط)
              if (!isCurrentUser && onReport != null) {
                items.add(
                  const PopupMenuItem<String>(
                    value: 'report',
                    child: Row(
                      children: [
                        Icon(Icons.flag, size: 20),
                        SizedBox(width: 8),
                        Text('إبلاغ'),
                      ])));
              }

              // خيار تعيين كأفضل إجابة (لصاحب الموضوع فقط)
              if (isTopicOwner &&
                  !post.isBestAnswer &&
                  onMarkAsBestAnswer != null) {
                items.add(
                  const PopupMenuItem<String>(
                    value: 'best_answer',
                    child: Row(
                      children: [
                        Icon(Icons.check_circle, size: 20),
                        SizedBox(width: 8),
                        Text('تعيين كأفضل إجابة'),
                      ])));
              }

              return items;
            }),
        ]));
  }

  /// بناء صور المشاركة
  Widget _buildPostImages() {
    return Container(
      height: 200,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: post.images!.length,
        itemBuilder: (context, index) {
          return Container(
            width: 200,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: NetworkImage(post.images![index]),
                fit: BoxFit.cover)));
        }));
  }

  /// بناء تذييل المشاركة
  Widget _buildPostFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12))),
      child: Row(
        children: [
          // زر الإعجاب
          InkWell(
            onTap: () {
              onLike?.call(!isLiked);
            },
            borderRadius: BorderRadius.circular(4),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Icon(
                    isLiked ? Icons.favorite : Icons.favorite_border,
                    color: isLiked ? AppColors.error : Colors.black54,
                    size: 20),
                  const SizedBox(width: 4),
                  Text(
                    '${post.likesCount}',
                    style: AppTextStyles.caption.copyWith(
                      color: isLiked ? AppColors.error : Colors.black54,
                      fontWeight: isLiked ? FontWeight.bold : FontWeight.normal)),
                ]))),
          const SizedBox(width: 16),

          // زر الرد
          if (onReply != null)
            InkWell(
              onTap: onReply,
              borderRadius: BorderRadius.circular(4),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    const Icon(
                      Icons.reply,
                      color: Colors.black54,
                      size: 20),
                    const SizedBox(width: 4),
                    Text(
                      'رد',
                      style: AppTextStyles.caption.copyWith(
                        color: Colors.black54)),
                  ]))),

          const Spacer(),

          // عدد الردود
          if (post.repliesCount > 0)
            Text(
              '${post.repliesCount} رد',
              style: AppTextStyles.caption.copyWith(
                color: Colors.black54)),
        ]));
  }
}
