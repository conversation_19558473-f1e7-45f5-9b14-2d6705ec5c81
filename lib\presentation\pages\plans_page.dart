import 'package:flutter/material.dart';

import '../../core/constants/plan_constants.dart';

/// PlansPage displays a list of available plans that users can choose from.
/// Each plan is represented by a PlanCard, and tapping on one navigates the user
/// to the PaymentPage with the corresponding plan price.
class PlansPage extends StatelessWidget {
  const PlansPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("اختر الخطة")),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          PlanCard(
            planName: "الخطة النحاسية",
            duration: PlanConstants.nahasDuration,
            price: PlanConstants.nahasPrice,
            onTap: () => _goToPayment(context, PlanConstants.nahasPrice)),
          const SizedBox(height: 16),
          PlanCard(
            planName: "الخطة الفضية",
            duration: PlanConstants.fiddaDuration,
            price: PlanConstants.fiddaPrice,
            onTap: () => _goToPayment(context, PlanConstants.fiddaPrice)),
          const SizedBox(height: 16),
          PlanCard(
            planName: "الخطة الذهبية",
            duration: PlanConstants.thahabDuration,
            price: PlanConstants.thahabPrice,
            onTap: () => _goToPayment(context, PlanConstants.thahabPrice)),
        ]));
  }

  /// Navigates to the payment page while passing the selected plan's price.
  void _goToPayment(BuildContext context, double planPrice) {
    // توجيه إلى صفحة اختيار طريقة الدفع
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('يرجى إنشاء إعلان أولاً لاختيار خطة الدفع'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}

/// A widget that displays a card for a specific plan.
/// It shows the plan name, duration, and price, and includes a button to purchase the plan.
class PlanCard extends StatelessWidget {
  final String planName;
  final Duration duration;
  final double price;
  final VoidCallback onTap;

  const PlanCard({
    super.key,
    required this.planName,
    required this.duration,
    required this.price,
    required this.onTap,
  });

  /// Formats the duration to display in either days or months.
  /// If the duration is 30 days or more, it is formatted in months.
  String _formatDuration(Duration duration) {
    if (duration.inDays >= 30) {
      int months = duration.inDays ~/ 30;
      return "$months شهر${months > 1 ? 'ات' : ''}";
    } else {
      return "${duration.inDays} يوم${duration.inDays > 1 ? 'ا' : ''}";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      child: ListTile(
        title: Text(
          planName,
          style: const TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Text(
          "المدة: ${_formatDuration(duration)} - السعر: ${price.toStringAsFixed(2)} د.ك"),
        trailing: ElevatedButton(
          onPressed: onTap,
          child: const Text("شراء"))));
  }
}
