// تم تعليق خدمة المقارنة مؤقتاً - سيتم تفعيلها لاحقاً
/*
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../entities/estate.dart';

class PropertyComparisonService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  static const int maxComparisonItems = 3;

  /// إضافة عقار للمقارنة
  Future<bool> addToComparison(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final comparisonRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('comparison');

      // التحقق من عدد العقارات في المقارنة
      final snapshot = await comparisonRef.get();
      if (snapshot.docs.length >= maxComparisonItems) {
        throw Exception('لا يمكن إضافة أكثر من $maxComparisonItems عقارات للمقارنة');
      }

      // التحقق من وجود العقار مسبقاً
      final existingDoc = await comparisonRef.doc(propertyId).get();
      if (existingDoc.exists) {
        throw Exception('العقار موجود بالفعل في المقارنة');
      }

      // إضافة العقار للمقارنة
      await comparisonRef.doc(propertyId).set({
        'propertyId': propertyId,
        'addedAt': FieldValue.serverTimestamp(),
        'userId': user.uid,
      });

      // تسجيل النشاط
      await _logActivity(user.uid, propertyId, 'added_to_comparison');

      return true;
    } catch (e) {
      print('Error adding to comparison: $e');
      rethrow;
    }
  }

  /// إزالة عقار من المقارنة
  Future<bool> removeFromComparison(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('comparison')
          .doc(propertyId)
          .delete();

      // تسجيل النشاط
      await _logActivity(user.uid, propertyId, 'removed_from_comparison');

      return true;
    } catch (e) {
      print('Error removing from comparison: $e');
      return false;
    }
  }

  /// الحصول على قائمة العقارات في المقارنة
  Future<List<Estate>> getComparisonProperties() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final comparisonSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('comparison')
          .orderBy('addedAt')
          .get();

      final propertyIds = comparisonSnapshot.docs
          .map((doc) => doc.data()['propertyId'] as String)
          .toList();

      if (propertyIds.isEmpty) return [];

      // جلب تفاصيل العقارات
      final List<Estate> properties = [];
      for (final propertyId in propertyIds) {
        final propertyDoc = await _firestore
            .collection('estates')
            .doc(propertyId)
            .get();

        if (propertyDoc.exists) {
          final data = propertyDoc.data()!;
          properties.add(_mapToEstate(data, propertyDoc.id));
        }
      }

      return properties;
    } catch (e) {
      print('Error getting comparison properties: $e');
      return [];
    }
  }

  /// التحقق من وجود عقار في المقارنة
  Future<bool> isInComparison(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final doc = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('comparison')
          .doc(propertyId)
          .get();

      return doc.exists;
    } catch (e) {
      print('Error checking comparison status: $e');
      return false;
    }
  }

  /// مسح جميع العقارات من المقارنة
  Future<bool> clearComparison() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final comparisonRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('comparison');

      final snapshot = await comparisonRef.get();
      final batch = _firestore.batch();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error clearing comparison: $e');
      return false;
    }
  }

  /// الحصول على عدد العقارات في المقارنة
  Future<int> getComparisonCount() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return 0;

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('comparison')
          .get();

      return snapshot.docs.length;
    } catch (e) {
      print('Error getting comparison count: $e');
      return 0;
    }
  }

  /// تحليل المقارنة وإنشاء تقرير
  Future<Map<String, dynamic>> generateComparisonReport(List<Estate> properties) async {
    if (properties.isEmpty) {
      return {'error': 'لا توجد عقارات للمقارنة'};
    }

    try {
      // حساب الإحصائيات
      final prices = properties.map((p) => p.price).toList();
      final areas = properties.map((p) => p.area).toList();
      final pricePerSqm = properties.map((p) => p.price / (p.area ?? 1)).toList();

      final report = {
        'propertyCount': properties.length,
        'priceAnalysis': {
          'min': prices.reduce((a, b) => a < b ? a : b),
          'max': prices.reduce((a, b) => a > b ? a : b),
          'average': prices.reduce((a, b) => a + b) / prices.length,
        },
        'areaAnalysis': {
          'min': areas.where((a) => a != null).reduce((a, b) => (a! < b!) ? a : b),
          'max': areas.where((a) => a != null).reduce((a, b) => (a! > b!) ? a : b),
          'average': areas.where((a) => a != null).fold(0.0, (total, a) => total + a!) / areas.where((a) => a != null).length,
        },
        'pricePerSqmAnalysis': {
          'min': pricePerSqm.reduce((a, b) => a < b ? a : b),
          'max': pricePerSqm.reduce((a, b) => a > b ? a : b),
          'average': pricePerSqm.reduce((a, b) => a + b) / pricePerSqm.length,
        },
        'recommendations': _generateRecommendations(properties),
        'generatedAt': DateTime.now().toIso8601String(),
      };

      // حفظ التقرير في Firebase
      await _saveComparisonReport(report);

      return report;
    } catch (e) {
      print('Error generating comparison report: $e');
      return {'error': 'حدث خطأ في إنشاء التقرير'};
    }
  }

  /// إنشاء توصيات بناءً على المقارنة
  List<String> _generateRecommendations(List<Estate> properties) {
    final recommendations = <String>[];

    if (properties.length < 2) {
      recommendations.add('أضف المزيد من العقارات للحصول على مقارنة أفضل');
      return recommendations;
    }

    // تحليل الأسعار
    final prices = properties.map((p) => p.price).toList();
    final minPrice = prices.reduce((a, b) => a < b ? a : b);
    final maxPrice = prices.reduce((a, b) => a > b ? a : b);
    final avgPrice = prices.reduce((a, b) => a + b) / prices.length;

    final cheapestProperty = properties.firstWhere((p) => p.price == minPrice);
    final mostExpensiveProperty = properties.firstWhere((p) => p.price == maxPrice);

    recommendations.add('أرخص عقار: ${cheapestProperty.title} بسعر ${minPrice.toStringAsFixed(0)} د.ك');
    recommendations.add('أغلى عقار: ${mostExpensiveProperty.title} بسعر ${maxPrice.toStringAsFixed(0)} د.ك');

    // تحليل المساحة
    final areas = properties.map((p) => p.area).toList();
    final pricePerSqm = properties.map((p) => p.price / (p.area ?? 1)).toList();
    final minPricePerSqm = pricePerSqm.reduce((a, b) => a < b ? a : b);
    final bestValueProperty = properties.firstWhere((p) => (p.price / (p.area ?? 1)) == minPricePerSqm);

    recommendations.add('أفضل قيمة مقابل المساحة: ${bestValueProperty.title}');

    // تحليل المميزات
    final propertiesWithAC = properties.where((p) => p.hasCentralAC).length;
    final propertiesWithElevator = properties.where((p) => p.hasElevator ?? false).length;
    final propertiesWithGarage = properties.where((p) => p.hasGarage).length;

    if (propertiesWithAC > 0) {
      recommendations.add('$propertiesWithAC من ${properties.length} عقارات تحتوي على تكييف مركزي');
    }

    if (propertiesWithElevator > 0) {
      recommendations.add('$propertiesWithElevator من ${properties.length} عقارات تحتوي على مصعد');
    }

    if (propertiesWithGarage > 0) {
      recommendations.add('$propertiesWithGarage من ${properties.length} عقارات تحتوي على مرآب');
    }

    // توصية الاستثمار
    if (avgPrice > 100000) {
      recommendations.add('هذه العقارات في الفئة العليا، مناسبة للاستثمار طويل المدى');
    } else if (avgPrice > 50000) {
      recommendations.add('هذه العقارات في الفئة المتوسطة، مناسبة للسكن أو الاستثمار');
    } else {
      recommendations.add('هذه العقارات اقتصادية، مناسبة للمشترين لأول مرة');
    }

    return recommendations;
  }

  /// حفظ تقرير المقارنة
  Future<void> _saveComparisonReport(Map<String, dynamic> report) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('comparisonReports')
          .add(report);
    } catch (e) {
      print('Error saving comparison report: $e');
    }
  }

  /// الحصول على تقارير المقارنة السابقة
  Future<List<Map<String, dynamic>>> getComparisonReports() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('comparisonReports')
          .orderBy('generatedAt', descending: true)
          .limit(10)
          .get();

      return snapshot.docs.map((doc) => doc.data()).toList();
    } catch (e) {
      print('Error getting comparison reports: $e');
      return [];
    }
  }

  /// مشاركة المقارنة
  Future<String> shareComparison(List<Estate> properties) async {
    try {
      final comparisonData = {
        'properties': properties.map((p) => {
          'id': p.id,
          'title': p.title,
          'price': p.price,
          'area': p.area,
          'location': p.location,
          'photoUrl': p.photoUrls.isNotEmpty ? p.photoUrls.first : null,
        }).toList(),
        'sharedAt': DateTime.now().toIso8601String(),
        'sharedBy': _auth.currentUser?.uid,
      };

      final docRef = await _firestore
          .collection('sharedComparisons')
          .add(comparisonData);

      // إنشاء رابط المشاركة
      final shareUrl = 'https://your-app.com/comparison/${docRef.id}';
      return shareUrl;
    } catch (e) {
      print('Error sharing comparison: $e');
      throw Exception('فشل في مشاركة المقارنة');
    }
  }

  /// الحصول على مقارنة مشتركة
  Future<Map<String, dynamic>?> getSharedComparison(String comparisonId) async {
    try {
      final doc = await _firestore
          .collection('sharedComparisons')
          .doc(comparisonId)
          .get();

      if (doc.exists) {
        return doc.data();
      }
      return null;
    } catch (e) {
      print('Error getting shared comparison: $e');
      return null;
    }
  }

  /// تحويل البيانات من Firestore إلى Estate object
  Estate _mapToEstate(Map<String, dynamic> data, String id) {
    return Estate(
      id: id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      location: data['location'] ?? '',
      photoUrls: List<String>.from(data['photoUrls'] ?? []),
      isFeatured: data['isFeatured'] ?? false,
      planType: data['planType'] ?? 'free',
      startDate: data['startDate'] != null
          ? DateTime.parse(data['startDate'])
          : null,
      endDate: data['endDate'] != null
          ? DateTime.parse(data['endDate'])
          : null,
      createdAt: data['createdAt'] != null
          ? DateTime.parse(data['createdAt'])
          : DateTime.now(),
      mainCategory: data['mainCategory'],
      subCategory: data['subCategory'],
      postedByUserType: data['postedByUserType'],
      hidePhone: data['hidePhone'] ?? false,
      extraPhones: List<String>.from(data['extraPhones'] ?? []),
      shareLocation: data['shareLocation'] ?? false,
      lat: data['lat']?.toDouble(),
      lng: data['lng']?.toDouble(),
      hasCentralAC: data['hasCentralAC'] ?? false,
      hasSecurity: data['hasSecurity'],
      allowPets: data['allowPets'],
      hasElevator: data['hasElevator'],
      hasSwimmingPool: data['hasSwimmingPool'],
      hasMaidRoom: data['hasMaidRoom'] ?? false,
      hasGarage: data['hasGarage'] ?? false,
      hasBalcony: data['hasBalcony'],
      isFullyFurnished: data['isFullyFurnished'],
      rebound: data['rebound'],
      numberOfRooms: data['numberOfRooms'],
      internalLocation: data['internalLocation'],
      salon: data['salon'],
      area: data['area']?.toDouble(),
      floorNumber: data['floorNumber'],
      numberOfBathrooms: data['numberOfBathrooms'],
      buildingAge: data['buildingAge'],
      numberOfFloors: data['numberOfFloors'],
      propertyType: data['propertyType'],
      autoRepublish: data['autoRepublish'] ?? false,
      kuwaitCornersPin: data['kuwaitCornersPin'] ?? false,
      movingAd: data['movingAd'] ?? false,
      vipBadge: data['vipBadge'] ?? false,
      pinnedOnHome: data['pinnedOnHome'] ?? false,
      discountCode: data['discountCode'],
      advertiserImage: data['advertiserImage'],
      advertiserName: data['advertiserName'],
      advertiserEmail: data['advertiserEmail'],
      advertiserRegistrationDate: data['advertiserRegistrationDate'] != null
          ? DateTime.parse(data['advertiserRegistrationDate'])
          : null,
      advertiserAdsCount: data['advertiserAdsCount'],
      ownerId: data['ownerId'],
      originalEstateId: data['originalEstateId'],
      isOriginal: data['isOriginal'] ?? true,
      copiedBy: List<String>.from(data['copiedBy'] ?? []),
      isPaymentVerified: data['isPaymentVerified'] ?? false,
      viewsCount: data['viewsCount'],
      inquiriesCount: data['inquiriesCount'],
      favoritesCount: data['favoritesCount'],
      latitude: data['latitude']?.toDouble(),
      longitude: data['longitude']?.toDouble(),
      rooms: data['rooms'],
      bathrooms: data['bathrooms'],
      floors: data['floors'],
      purpose: data['purpose'],
      hasGarden: data['hasGarden'],
      hasPool: data['hasPool'],
      hasDriverRoom: data['hasDriverRoom'],
      hasPrivateEntrance: data['hasPrivateEntrance'],
      hasEquippedKitchen: data['hasEquippedKitchen'],
      isAvailable: data['isAvailable'] ?? true);
  }



  /// تحويل العقار إلى بيانات مقارنة
  Map<String, dynamic> _propertyToComparisonData(Estate property) {
    return {
      'id': property.id,
      'title': property.title,
      'price': property.price,
      'pricePerSqm': property.area != null && property.area! > 0
          ? property.price / property.area!
          : 0,
      'area': property.area,
      'location': property.location,
      'rooms': property.numberOfRooms,
      'bathrooms': property.numberOfBathrooms,
      'floor': property.floorNumber,
      'buildingAge': property.buildingAge,
      'features': {
        'hasElevator': property.hasElevator ?? false,
        'hasGarage': property.hasGarage ?? false,
        'hasCentralAC': property.hasCentralAC,
        'hasSwimmingPool': property.hasSwimmingPool ?? false,
        'hasMaidRoom': property.hasMaidRoom,
        'hasBalcony': property.hasBalcony ?? false,
        'isFullyFurnished': property.isFullyFurnished ?? false,
        'hasSecurity': property.hasSecurity ?? false,
        'allowPets': property.allowPets ?? false,
      },
      'advertiser': {
        'name': property.advertiserName,
        'email': property.advertiserEmail,
        'adsCount': property.advertiserAdsCount,
      },
      'photos': property.photoUrls,
      'createdAt': property.createdAt.toIso8601String(),
    };
  }

  /// إنشاء ملخص المقارنة
  Map<String, dynamic> _generateSummary(List<Estate> properties) {
    if (properties.isEmpty) return {};

    final prices = properties.map((p) => p.price).toList();
    final areas = properties.map((p) => p.area ?? 0).where((a) => a > 0).toList();
    final pricesPerSqm = properties
        .where((p) => p.area != null && p.area! > 0)
        .map((p) => p.price / p.area!)
        .toList();

    return {
      'totalProperties': properties.length,
      'priceRange': {
        'min': prices.reduce((a, b) => a < b ? a : b),
        'max': prices.reduce((a, b) => a > b ? a : b),
        'average': prices.reduce((a, b) => a + b) / prices.length,
      },
      'areaRange': areas.isNotEmpty ? {
        'min': areas.reduce((a, b) => a < b ? a : b),
        'max': areas.reduce((a, b) => a > b ? a : b),
        'average': areas.reduce((a, b) => a + b) / areas.length,
      } : null,
      'pricePerSqmRange': pricesPerSqm.isNotEmpty ? {
        'min': pricesPerSqm.reduce((a, b) => a < b ? a : b),
        'max': pricesPerSqm.reduce((a, b) => a > b ? a : b),
        'average': pricesPerSqm.reduce((a, b) => a + b) / pricesPerSqm.length,
      } : null,
      'locations': properties.map((p) => p.location).toSet().toList(),
      'categories': properties.map((p) => p.mainCategory).toSet().toList(),
    };
  }



  /// إنشاء تحليل المقارنة
  Map<String, dynamic> _generateAnalysis(List<Estate> properties) {
    return {
      'featuresDistribution': _analyzeFeaturesDistribution(properties),
      'locationAnalysis': _analyzeLocations(properties),
      'priceAnalysis': _analyzePrices(properties),
      'sizeAnalysis': _analyzeSizes(properties),
    };
  }

  /// تحليل توزيع المميزات
  Map<String, int> _analyzeFeaturesDistribution(List<Estate> properties) {
    final features = <String, int>{};

    for (final property in properties) {
      if (property.hasElevator == true) features['مصعد'] = (features['مصعد'] ?? 0) + 1;
      if (property.hasGarage == true) features['جراج'] = (features['جراج'] ?? 0) + 1;
      if (property.hasCentralAC == true) features['تكييف مركزي'] = (features['تكييف مركزي'] ?? 0) + 1;
      if (property.hasSwimmingPool == true) features['مسبح'] = (features['مسبح'] ?? 0) + 1;
      if (property.hasMaidRoom == true) features['غرفة خادمة'] = (features['غرفة خادمة'] ?? 0) + 1;
      if (property.hasBalcony == true) features['شرفة'] = (features['شرفة'] ?? 0) + 1;
      if (property.isFullyFurnished == true) features['مفروش'] = (features['مفروش'] ?? 0) + 1;
      if (property.hasSecurity == true) features['حراسة'] = (features['حراسة'] ?? 0) + 1;
    }

    return features;
  }

  /// تحليل المواقع
  Map<String, int> _analyzeLocations(List<Estate> properties) {
    final locations = <String, int>{};
    for (final property in properties) {
      final location = property.location.split(' - ').first;
      locations[location] = (locations[location] ?? 0) + 1;
    }
    return locations;
  }

  /// تحليل الأسعار
  Map<String, dynamic> _analyzePrices(List<Estate> properties) {
    final prices = properties.map((p) => p.price).toList();
    prices.sort();

    return {
      'min': prices.first,
      'max': prices.last,
      'median': prices[prices.length ~/ 2],
      'average': prices.reduce((a, b) => a + b) / prices.length,
    };
  }

  /// تحليل المساحات
  Map<String, dynamic> _analyzeSizes(List<Estate> properties) {
    final areas = properties
        .map((p) => p.area)
        .where((a) => a != null && a > 0)
        .cast<double>()
        .toList();

    if (areas.isEmpty) return {};

    areas.sort();

    return {
      'min': areas.first,
      'max': areas.last,
      'median': areas[areas.length ~/ 2],
      'average': areas.reduce((a, b) => a + b) / areas.length,
    };
  }



  /// تسجيل نشاط المستخدم
  Future<void> _logActivity(String userId, String propertyId, String action) async {
    try {
      await _firestore.collection('userActivities').add({
        'userId': userId,
        'propertyId': propertyId,
        'action': action,
        'type': 'comparison',
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error logging activity: $e');
    }
  }
}
*/
