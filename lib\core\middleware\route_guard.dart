// lib/core/middleware/route_guard.dart
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants/user_types.dart';
import '../../domain/services/user_interface_customization_service.dart';

/// حارس المسارات للتحقق من صلاحيات المستخدم
class RouteGuard {
  /// التحقق من إمكانية الوصول لصفحة طلبات العقارات
  static Future<bool> canAccessPropertyRequests(BuildContext context) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _showAccessDeniedMessage(context, 'يجب تسجيل الدخول للوصول لهذه الصفحة');
      return false;
    }

    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      final userTypeString = userType.toString().split('.').last;
      
      final canAccess = UserTypeConstants.canViewPropertyRequests(userTypeString);
      
      if (!canAccess) {
        _showAccessDeniedMessage(
          context, 
          'هذه الصفحة متاحة للمستثمرين والمالكين والشركات فقط'
        );
      }
      
      return canAccess;
    } catch (e) {
      _showAccessDeniedMessage(context, 'حدث خطأ في التحقق من الصلاحيات');
      return false;
    }
  }

  /// التحقق من إمكانية الوصول لصفحة طلباتي
  static Future<bool> canAccessMyPropertyRequests(BuildContext context) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _showAccessDeniedMessage(context, 'يجب تسجيل الدخول للوصول لهذه الصفحة');
      return false;
    }

    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      final userTypeString = userType.toString().split('.').last;
      
      final canAccess = UserTypeConstants.canCreatePropertyRequests(userTypeString);
      
      if (!canAccess) {
        _showAccessDeniedMessage(
          context, 
          'هذه الصفحة متاحة للباحثين عن العقارات فقط'
        );
      }
      
      return canAccess;
    } catch (e) {
      _showAccessDeniedMessage(context, 'حدث خطأ في التحقق من الصلاحيات');
      return false;
    }
  }

  /// التحقق من إمكانية إنشاء طلب عقار
  static Future<bool> canCreatePropertyRequest(BuildContext context) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _showAccessDeniedMessage(context, 'يجب تسجيل الدخول لإنشاء طلب عقار');
      return false;
    }

    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      final userTypeString = userType.toString().split('.').last;
      
      final canAccess = UserTypeConstants.canCreatePropertyRequests(userTypeString);
      
      if (!canAccess) {
        _showAccessDeniedMessage(
          context, 
          'إنشاء طلبات العقارات متاح للباحثين عن العقارات فقط'
        );
      }
      
      return canAccess;
    } catch (e) {
      _showAccessDeniedMessage(context, 'حدث خطأ في التحقق من الصلاحيات');
      return false;
    }
  }

  /// التحقق من إمكانية إضافة عقار
  static Future<bool> canPostAd(BuildContext context) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _showAccessDeniedMessage(context, 'يجب تسجيل الدخول لإضافة عقار');
      return false;
    }

    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      final userTypeString = userType.toString().split('.').last;
      
      final canAccess = UserTypeConstants.canPostAds(userTypeString);
      
      if (!canAccess) {
        _showAccessDeniedMessage(
          context, 
          'إضافة العقارات متاحة للمستثمرين والمالكين والشركات فقط'
        );
      }
      
      return canAccess;
    } catch (e) {
      _showAccessDeniedMessage(context, 'حدث خطأ في التحقق من الصلاحيات');
      return false;
    }
  }

  /// التحقق من إمكانية الوصول للمفضلة
  static Future<bool> canAccessFavorites(BuildContext context) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _showAccessDeniedMessage(context, 'يجب تسجيل الدخول للوصول للمفضلة');
      return false;
    }

    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      final userTypeString = userType.toString().split('.').last;
      
      final canAccess = UserTypeConstants.canAccessFavorites(userTypeString);
      
      if (!canAccess) {
        _showAccessDeniedMessage(
          context, 
          'المفضلة غير متاحة للشركات العقارية'
        );
      }
      
      return canAccess;
    } catch (e) {
      _showAccessDeniedMessage(context, 'حدث خطأ في التحقق من الصلاحيات');
      return false;
    }
  }

  /// التحقق العام من تسجيل الدخول
  static bool isLoggedIn(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _showAccessDeniedMessage(context, 'يجب تسجيل الدخول للوصول لهذه الصفحة');
      return false;
    }
    return true;
  }

  /// عرض رسالة رفض الوصول
  static void _showAccessDeniedMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.cairo()),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3)));
  }

  /// إعادة توجيه للصفحة الرئيسية
  static void redirectToHome(BuildContext context) {
    Navigator.of(context).pushReplacementNamed('/');
  }

  /// إعادة توجيه لصفحة تسجيل الدخول
  static void redirectToLogin(BuildContext context) {
    Navigator.of(context).pushReplacementNamed('/login');
  }
}

/// Widget wrapper للحماية
class ProtectedRoute extends StatelessWidget {
  final Widget child;
  final Future<bool> Function(BuildContext) guard;
  final String? redirectRoute;

  const ProtectedRoute({
    super.key,
    required this.child,
    required this.guard,
    this.redirectRoute,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: guard(context),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.green)),
                  const SizedBox(height: 16),
                  Text(
                    'جاري التحقق من الصلاحيات...',
                    style: GoogleFonts.cairo()),
                ])));
        }

        if (snapshot.hasData && snapshot.data == true) {
          return child;
        }

        // إعادة توجيه في حالة عدم وجود صلاحية
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (redirectRoute != null) {
            Navigator.of(context).pushReplacementNamed(redirectRoute!);
          } else {
            RouteGuard.redirectToHome(context);
          }
        });

        return Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.block,
                  size: 64,
                  color: Colors.red[400]),
                const SizedBox(height: 16),
                Text(
                  'ليس لديك صلاحية للوصول لهذه الصفحة',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[600])),
                const SizedBox(height: 8),
                Text(
                  'سيتم إعادة توجيهك للصفحة الرئيسية...',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[600])),
              ])));
      });
  }
}
