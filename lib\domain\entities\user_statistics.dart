import 'package:equatable/equatable.dart';

/// نموذج لإحصائيات المستخدم
class UserStatistics extends Equatable {
  /// معرف المستخدم
  final String userId;
  
  /// عدد الإعلانات النشطة
  final int activeAdsCount;
  
  /// عدد الإعلانات المنتهية
  final int expiredAdsCount;
  
  /// عدد الإعلانات المباعة/المؤجرة
  final int soldRentedAdsCount;
  
  /// عدد المشاهدات الإجمالي
  final int totalViewsCount;
  
  /// عدد الاتصالات الإجمالي
  final int totalContactsCount;
  
  /// عدد المفضلة الإجمالي
  final int totalFavoritesCount;
  
  /// عدد العملاء
  final int clientsCount;
  
  /// عدد المواعيد
  final int appointmentsCount;
  
  /// عدد العروض
  final int offersCount;
  
  /// عدد العقارات المباعة/المؤجرة
  final int soldRentedCount;
  
  /// إجمالي المبيعات/الإيجارات
  final double totalSalesRentals;
  
  /// متوسط سعر البيع/الإيجار
  final double averageSaleRentalPrice;
  
  /// متوسط وقت البيع/الإيجار (بالأيام)
  final double averageSaleRentalTime;
  
  /// متوسط التقييم
  final double averageRating;
  
  /// عدد التقييمات
  final int reviewsCount;
  
  /// عدد الزيارات للملف الشخصي
  final int profileVisitsCount;
  
  /// إحصائيات حسب نوع العقار
  final Map<String, int>? statsByPropertyType;
  
  /// إحصائيات حسب المنطقة
  final Map<String, int>? statsByLocation;
  
  /// إحصائيات حسب الشهر
  final Map<String, dynamic>? statsByMonth;
  
  /// إحصائيات إضافية
  final Map<String, dynamic>? additionalStats;

  const UserStatistics({
    required this.userId,
    required this.activeAdsCount,
    required this.expiredAdsCount,
    required this.soldRentedAdsCount,
    required this.totalViewsCount,
    required this.totalContactsCount,
    required this.totalFavoritesCount,
    required this.clientsCount,
    required this.appointmentsCount,
    required this.offersCount,
    required this.soldRentedCount,
    required this.totalSalesRentals,
    required this.averageSaleRentalPrice,
    required this.averageSaleRentalTime,
    required this.averageRating,
    required this.reviewsCount,
    required this.profileVisitsCount,
    this.statsByPropertyType,
    this.statsByLocation,
    this.statsByMonth,
    this.additionalStats,
  });

  /// إنشاء نسخة معدلة من الإحصائيات
  UserStatistics copyWith({
    String? userId,
    int? activeAdsCount,
    int? expiredAdsCount,
    int? soldRentedAdsCount,
    int? totalViewsCount,
    int? totalContactsCount,
    int? totalFavoritesCount,
    int? clientsCount,
    int? appointmentsCount,
    int? offersCount,
    int? soldRentedCount,
    double? totalSalesRentals,
    double? averageSaleRentalPrice,
    double? averageSaleRentalTime,
    double? averageRating,
    int? reviewsCount,
    int? profileVisitsCount,
    Map<String, int>? statsByPropertyType,
    Map<String, int>? statsByLocation,
    Map<String, dynamic>? statsByMonth,
    Map<String, dynamic>? additionalStats,
  }) {
    return UserStatistics(
      userId: userId ?? this.userId,
      activeAdsCount: activeAdsCount ?? this.activeAdsCount,
      expiredAdsCount: expiredAdsCount ?? this.expiredAdsCount,
      soldRentedAdsCount: soldRentedAdsCount ?? this.soldRentedAdsCount,
      totalViewsCount: totalViewsCount ?? this.totalViewsCount,
      totalContactsCount: totalContactsCount ?? this.totalContactsCount,
      totalFavoritesCount: totalFavoritesCount ?? this.totalFavoritesCount,
      clientsCount: clientsCount ?? this.clientsCount,
      appointmentsCount: appointmentsCount ?? this.appointmentsCount,
      offersCount: offersCount ?? this.offersCount,
      soldRentedCount: soldRentedCount ?? this.soldRentedCount,
      totalSalesRentals: totalSalesRentals ?? this.totalSalesRentals,
      averageSaleRentalPrice: averageSaleRentalPrice ?? this.averageSaleRentalPrice,
      averageSaleRentalTime: averageSaleRentalTime ?? this.averageSaleRentalTime,
      averageRating: averageRating ?? this.averageRating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      profileVisitsCount: profileVisitsCount ?? this.profileVisitsCount,
      statsByPropertyType: statsByPropertyType ?? this.statsByPropertyType,
      statsByLocation: statsByLocation ?? this.statsByLocation,
      statsByMonth: statsByMonth ?? this.statsByMonth,
      additionalStats: additionalStats ?? this.additionalStats);
  }
  
  /// تحويل الإحصائيات إلى Map
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'activeAdsCount': activeAdsCount,
      'expiredAdsCount': expiredAdsCount,
      'soldRentedAdsCount': soldRentedAdsCount,
      'totalViewsCount': totalViewsCount,
      'totalContactsCount': totalContactsCount,
      'totalFavoritesCount': totalFavoritesCount,
      'clientsCount': clientsCount,
      'appointmentsCount': appointmentsCount,
      'offersCount': offersCount,
      'soldRentedCount': soldRentedCount,
      'totalSalesRentals': totalSalesRentals,
      'averageSaleRentalPrice': averageSaleRentalPrice,
      'averageSaleRentalTime': averageSaleRentalTime,
      'averageRating': averageRating,
      'reviewsCount': reviewsCount,
      'profileVisitsCount': profileVisitsCount,
      'statsByPropertyType': statsByPropertyType,
      'statsByLocation': statsByLocation,
      'statsByMonth': statsByMonth,
      'additionalStats': additionalStats,
    };
  }
  
  /// إنشاء إحصائيات من Map
  factory UserStatistics.fromMap(Map<String, dynamic> map) {
    return UserStatistics(
      userId: map['userId'] ?? '',
      activeAdsCount: map['activeAdsCount'] ?? 0,
      expiredAdsCount: map['expiredAdsCount'] ?? 0,
      soldRentedAdsCount: map['soldRentedAdsCount'] ?? 0,
      totalViewsCount: map['totalViewsCount'] ?? 0,
      totalContactsCount: map['totalContactsCount'] ?? 0,
      totalFavoritesCount: map['totalFavoritesCount'] ?? 0,
      clientsCount: map['clientsCount'] ?? 0,
      appointmentsCount: map['appointmentsCount'] ?? 0,
      offersCount: map['offersCount'] ?? 0,
      soldRentedCount: map['soldRentedCount'] ?? 0,
      totalSalesRentals: (map['totalSalesRentals'] ?? 0.0).toDouble(),
      averageSaleRentalPrice: (map['averageSaleRentalPrice'] ?? 0.0).toDouble(),
      averageSaleRentalTime: (map['averageSaleRentalTime'] ?? 0.0).toDouble(),
      averageRating: (map['averageRating'] ?? 0.0).toDouble(),
      reviewsCount: map['reviewsCount'] ?? 0,
      profileVisitsCount: map['profileVisitsCount'] ?? 0,
      statsByPropertyType: map['statsByPropertyType'] != null 
          ? Map<String, int>.from(map['statsByPropertyType']) 
          : null,
      statsByLocation: map['statsByLocation'] != null 
          ? Map<String, int>.from(map['statsByLocation']) 
          : null,
      statsByMonth: map['statsByMonth'],
      additionalStats: map['additionalStats']);
  }
  
  /// الحصول على إجمالي عدد الإعلانات
  int getTotalAdsCount() {
    return activeAdsCount + expiredAdsCount + soldRentedAdsCount;
  }
  
  /// الحصول على نسبة الإعلانات المباعة/المؤجرة
  double getSoldRentedPercentage() {
    final totalAds = getTotalAdsCount();
    if (totalAds == 0) {
      return 0.0;
    }
    
    return (soldRentedAdsCount / totalAds) * 100;
  }
  
  /// الحصول على متوسط المشاهدات لكل إعلان
  double getAverageViewsPerAd() {
    final totalAds = getTotalAdsCount();
    if (totalAds == 0) {
      return 0.0;
    }
    
    return totalViewsCount / totalAds;
  }
  
  /// الحصول على متوسط الاتصالات لكل إعلان
  double getAverageContactsPerAd() {
    final totalAds = getTotalAdsCount();
    if (totalAds == 0) {
      return 0.0;
    }
    
    return totalContactsCount / totalAds;
  }
  
  /// الحصول على نسبة تحويل المشاهدات إلى اتصالات
  double getViewToContactConversionRate() {
    if (totalViewsCount == 0) {
      return 0.0;
    }
    
    return (totalContactsCount / totalViewsCount) * 100;
  }
  
  /// الحصول على نسبة تحويل الاتصالات إلى مبيعات/إيجارات
  double getContactToSaleConversionRate() {
    if (totalContactsCount == 0) {
      return 0.0;
    }
    
    return (soldRentedCount / totalContactsCount) * 100;
  }

  @override
  List<Object?> get props => [
    userId,
    activeAdsCount,
    expiredAdsCount,
    soldRentedAdsCount,
    totalViewsCount,
    totalContactsCount,
    totalFavoritesCount,
    clientsCount,
    appointmentsCount,
    offersCount,
    soldRentedCount,
    totalSalesRentals,
    averageSaleRentalPrice,
    averageSaleRentalTime,
    averageRating,
    reviewsCount,
    profileVisitsCount,
    statsByPropertyType,
    statsByLocation,
    statsByMonth,
    additionalStats,
  ];
}
