import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';

/// مجموعة شاملة من أنماط النصوص باستخدام خط Cairo
/// يوفر هذا الملف طرق سهلة لتطبيق خط Cairo في جميع أنحاء التطبيق
class CairoTextStyles {
  
  // ========== أنماط العناوين ==========
  
  /// عنوان كبير جداً - للصفحات الرئيسية
  static TextStyle get displayLarge => GoogleFonts.cairo(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    height: 1.2);

  /// عنوان كبير - للعناوين المهمة
  static TextStyle get displayMedium => GoogleFonts.cairo(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    height: 1.2);

  /// عنوان متوسط - للعناوين الفرعية
  static TextStyle get displaySmall => GoogleFonts.cairo(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    height: 1.3);

  /// عنوان كبير - للصفحات
  static TextStyle get headlineLarge => GoogleFonts.cairo(
    fontSize: 22,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    height: 1.3);

  /// عنوان متوسط - للأقسام
  static TextStyle get headlineMedium => GoogleFonts.cairo(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    height: 1.3);

  /// عنوان صغير - للعناوين الفرعية
  static TextStyle get headlineSmall => GoogleFonts.cairo(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    height: 1.4);

  // ========== أنماط العناوين الفرعية ==========

  /// عنوان فرعي كبير
  static TextStyle get titleLarge => GoogleFonts.cairo(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    height: 1.4);

  /// عنوان فرعي متوسط
  static TextStyle get titleMedium => GoogleFonts.cairo(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
    height: 1.4);

  /// عنوان فرعي صغير
  static TextStyle get titleSmall => GoogleFonts.cairo(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
    height: 1.4);

  // ========== أنماط النصوص الأساسية ==========

  /// نص أساسي كبير
  static TextStyle get bodyLarge => GoogleFonts.cairo(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
    height: 1.5);

  /// نص أساسي متوسط
  static TextStyle get bodyMedium => GoogleFonts.cairo(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    height: 1.5);

  /// نص أساسي صغير
  static TextStyle get bodySmall => GoogleFonts.cairo(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    height: 1.5);

  // ========== أنماط التسميات ==========

  /// تسمية كبيرة - للأزرار والعناصر التفاعلية
  static TextStyle get labelLarge => GoogleFonts.cairo(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
    height: 1.4);

  /// تسمية متوسطة
  static TextStyle get labelMedium => GoogleFonts.cairo(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: AppColors.textSecondary,
    height: 1.4);

  /// تسمية صغيرة
  static TextStyle get labelSmall => GoogleFonts.cairo(
    fontSize: 11,
    fontWeight: FontWeight.w500,
    color: AppColors.textSecondary,
    height: 1.4);

  // ========== أنماط مخصصة للتطبيق ==========

  /// نمط خاص للأزرار
  static TextStyle get button => GoogleFonts.cairo(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: Colors.white,
    height: 1.2);

  /// نمط خاص لشريط التطبيق
  static TextStyle get appBarTitle => GoogleFonts.cairo(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: Colors.white,
    height: 1.2);

  /// نمط خاص للعناوين في الكروت
  static TextStyle get cardTitle => GoogleFonts.cairo(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    height: 1.3);

  /// نمط خاص للنصوص الفرعية في الكروت
  static TextStyle get cardSubtitle => GoogleFonts.cairo(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    height: 1.4);

  /// نمط خاص للأسعار
  static TextStyle get price => GoogleFonts.cairo(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
    height: 1.2);

  /// نمط خاص للتسميات في حقول الإدخال
  static TextStyle get inputLabel => GoogleFonts.cairo(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    height: 1.4);

  /// نمط خاص للنصوص التوضيحية
  static TextStyle get hint => GoogleFonts.cairo(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textLight,
    height: 1.4);

  /// نمط خاص لرسائل الخطأ
  static TextStyle get error => GoogleFonts.cairo(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.error,
    height: 1.4);

  /// نمط خاص للنصوص الناجحة
  static TextStyle get success => GoogleFonts.cairo(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: Colors.green,
    height: 1.4);

  /// نمط خاص للتحذيرات
  static TextStyle get warning => GoogleFonts.cairo(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: Colors.orange,
    height: 1.4);

  // ========== دوال مساعدة لتخصيص الأنماط ==========

  /// إنشاء نمط نص مخصص مع خط Cairo
  static TextStyle custom({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
    Color? decorationColor,
    double? letterSpacing,
    double? wordSpacing,
  }) {
    return GoogleFonts.cairo(
      fontSize: fontSize ?? 14,
      fontWeight: fontWeight ?? FontWeight.normal,
      color: color ?? AppColors.textPrimary,
      height: height ?? 1.4,
      decoration: decoration,
      decorationColor: decorationColor,
      letterSpacing: letterSpacing,
      wordSpacing: wordSpacing);
  }

  /// تطبيق لون مخصص على نمط موجود
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  /// تطبيق حجم خط مخصص على نمط موجود
  static TextStyle withSize(TextStyle style, double fontSize) {
    return style.copyWith(fontSize: fontSize);
  }

  /// تطبيق وزن خط مخصص على نمط موجود
  static TextStyle withWeight(TextStyle style, FontWeight fontWeight) {
    return style.copyWith(fontWeight: fontWeight);
  }

  /// تطبيق ارتفاع سطر مخصص على نمط موجود
  static TextStyle withHeight(TextStyle style, double height) {
    return style.copyWith(height: height);
  }
}
