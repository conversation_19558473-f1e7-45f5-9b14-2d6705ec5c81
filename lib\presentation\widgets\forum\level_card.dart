import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';

import '../../../domain/models/forum/user_level_model.dart';

/// بطاقة المستوى
class LevelCard extends StatelessWidget {
  /// نموذج المستوى
  final UserLevelModel level;

  /// النقاط الحالية
  final int currentPoints;

  /// ما إذا كان المستوى الحالي
  final bool isCurrentLevel;

  /// ما إذا كان المستوى مقفل
  final bool isLocked;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;

  /// حجم البطاقة
  final LevelCardSize size;

  const LevelCard({
    super.key,
    required this.level,
    required this.currentPoints,
    this.isCurrentLevel = false,
    this.isLocked = false,
    this.onTap,
    this.size = LevelCardSize.medium,
  });

  @override
  Widget build(BuildContext context) {
    switch (size) {
      case LevelCardSize.small:
        return _buildSmallCard(context);
      case LevelCardSize.medium:
        return _buildMediumCard(context);
      case LevelCardSize.large:
        return _buildLargeCard(context);
    }
  }

  /// بناء بطاقة صغيرة
  Widget _buildSmallCard(BuildContext context) {
    final progress = _calculateProgress();
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 80,
        height: 100,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isLocked
                ? Colors.grey.shade300
                : isCurrentLevel
                    ? level.color
                    : Colors.grey.shade300),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2)),
          ]),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة المستوى
            Stack(
              alignment: Alignment.center,
              children: [
                CircularPercentIndicator(
                  radius: 25,
                  lineWidth: 3,
                  percent: isLocked ? 0 : progress,
                  center: _buildLevelIcon(size: 30),
                  progressColor: isLocked
                      ? Colors.grey.shade400
                      : level.color,
                  backgroundColor: Colors.grey.shade200),
                if (isLocked)
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      shape: BoxShape.circle),
                    child: const Icon(
                      Icons.lock,
                      color: Colors.white,
                      size: 20)),
              ]),
            const SizedBox(height: 8),
            
            // اسم المستوى
            Text(
              level.name,
              style: TextStyle(
                fontSize: 10,
                fontWeight: isCurrentLevel ? FontWeight.bold : FontWeight.normal,
                color: isLocked
                    ? Colors.grey.shade500
                    : isCurrentLevel
                        ? level.color
                        : Colors.grey.shade700),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis),
          ])));
  }

  /// بناء بطاقة متوسطة
  Widget _buildMediumCard(BuildContext context) {
    final progress = _calculateProgress();
    final percentage = (progress * 100).round();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isLocked
              ? Colors.grey.shade300
              : isCurrentLevel
                  ? level.color
                  : Colors.transparent,
          width: isCurrentLevel ? 1 : 0)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // أيقونة المستوى
              Stack(
                alignment: Alignment.center,
                children: [
                  CircularPercentIndicator(
                    radius: 30,
                    lineWidth: 5,
                    percent: isLocked ? 0 : progress,
                    center: _buildLevelIcon(size: 35),
                    progressColor: isLocked
                        ? Colors.grey.shade400
                        : level.color,
                    backgroundColor: Colors.grey.shade200),
                  if (isLocked)
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle),
                      child: const Icon(
                        Icons.lock,
                        color: Colors.white,
                        size: 24)),
                ]),
              const SizedBox(width: 16),
              
              // معلومات المستوى
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // اسم المستوى
                        Expanded(
                          child: Text(
                            level.name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: isLocked
                                  ? Colors.grey.shade500
                                  : isCurrentLevel
                                      ? level.color
                                      : Colors.black87,
                              fontSize: 16),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis)),
                        
                        // النقاط المطلوبة
                        Text(
                          '${level.requiredPoints} نقطة',
                          style: TextStyle(
                            color: isLocked
                                ? Colors.grey.shade500
                                : Colors.grey.shade700,
                            fontSize: 12)),
                      ]),
                    const SizedBox(height: 4),
                    
                    // وصف المستوى
                    Text(
                      level.description,
                      style: TextStyle(
                        color: isLocked
                            ? Colors.grey.shade500
                            : Colors.grey.shade700,
                        fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis),
                    const SizedBox(height: 8),
                    
                    // شريط التقدم
                    if (isCurrentLevel)
                      LinearPercentIndicator(
                        lineHeight: 8,
                        percent: progress,
                        progressColor: level.color,
                        backgroundColor: Colors.grey.shade200,
                        barRadius: const Radius.circular(4),
                        padding: EdgeInsets.zero,
                        trailing: Text(
                          '$percentage%',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12))),
                  ])),
            ]))));
  }

  /// بناء بطاقة كبيرة
  Widget _buildLargeCard(BuildContext context) {
    final progress = _calculateProgress();
    final percentage = (progress * 100).round();
    
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isLocked
              ? Colors.grey.shade300
              : isCurrentLevel
                  ? level.color
                  : Colors.transparent,
          width: isCurrentLevel ? 2 : 0)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // أيقونة المستوى
              Stack(
                alignment: Alignment.center,
                children: [
                  CircularPercentIndicator(
                    radius: 50,
                    lineWidth: 8,
                    percent: isLocked ? 0 : progress,
                    center: _buildLevelIcon(size: 60),
                    progressColor: isLocked
                        ? Colors.grey.shade400
                        : level.color,
                    backgroundColor: Colors.grey.shade200,
                    animation: true,
                    animationDuration: 1000),
                  if (isLocked)
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle),
                      child: const Icon(
                        Icons.lock,
                        color: Colors.white,
                        size: 40)),
                ]),
              const SizedBox(height: 16),
              
              // اسم المستوى
              Text(
                level.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isLocked
                      ? Colors.grey.shade500
                      : isCurrentLevel
                          ? level.color
                          : Colors.black87,
                  fontSize: 18),
                textAlign: TextAlign.center),
              const SizedBox(height: 8),
              
              // وصف المستوى
              Text(
                level.description,
                style: TextStyle(
                  color: isLocked
                      ? Colors.grey.shade500
                      : Colors.grey.shade700,
                  fontSize: 14),
                textAlign: TextAlign.center),
              const SizedBox(height: 16),
              
              // النقاط المطلوبة
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8),
                decoration: BoxDecoration(
                  color: isLocked
                      ? Colors.grey.shade200
                      : level.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20)),
                child: Text(
                  '${level.requiredPoints} نقطة',
                  style: TextStyle(
                    color: isLocked
                        ? Colors.grey.shade700
                        : level.color,
                    fontWeight: FontWeight.bold))),
              const SizedBox(height: 16),
              
              // شريط التقدم
              if (isCurrentLevel) ...[
                LinearPercentIndicator(
                  lineHeight: 10,
                  percent: progress,
                  progressColor: level.color,
                  backgroundColor: Colors.grey.shade200,
                  barRadius: const Radius.circular(5),
                  padding: EdgeInsets.zero,
                  animation: true,
                  animationDuration: 1000,
                  center: Text(
                    '$percentage%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold))),
                const SizedBox(height: 16),
              ],
              
              // المكافآت
              if (level.rewards.isNotEmpty) ...[
                Text(
                  'المكافآت',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isLocked
                        ? Colors.grey.shade500
                        : Colors.grey.shade800,
                    fontSize: 16)),
                const SizedBox(height: 8),
                ...level.rewards.map((reward) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: isLocked
                            ? Colors.grey.shade400
                            : level.color,
                        size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          reward,
                          style: TextStyle(
                            color: isLocked
                                ? Colors.grey.shade500
                                : Colors.grey.shade700,
                            fontSize: 14))),
                    ]))),
              ],
            ]))));
  }

  /// بناء أيقونة المستوى
  Widget _buildLevelIcon({required double size}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: isLocked
            ? Colors.grey.shade200
            : level.color.withOpacity(0.1),
        shape: BoxShape.circle),
      child: Icon(
        level.icon,
        size: size / 2,
        color: isLocked
            ? Colors.grey.shade400
            : level.color));
  }

  /// حساب نسبة التقدم
  double _calculateProgress() {
    if (isCurrentLevel) {
      final nextLevel = UserLevelModel.getNextLevel(currentPoints);
      if (nextLevel == null) {
        return 1.0; // أعلى مستوى
      }
      
      final pointsForCurrentLevel = level.requiredPoints;
      final pointsForNextLevel = nextLevel.requiredPoints;
      final pointsRange = pointsForNextLevel - pointsForCurrentLevel;
      final userPointsInRange = currentPoints - pointsForCurrentLevel;
      
      return userPointsInRange / pointsRange;
    } else if (currentPoints >= level.requiredPoints) {
      return 1.0; // مستوى مكتمل
    } else {
      return 0.0; // مستوى غير مكتمل
    }
  }
}

/// حجم بطاقة المستوى
enum LevelCardSize {
  /// صغير
  small,

  /// متوسط
  medium,

  /// كبير
  large,
}
