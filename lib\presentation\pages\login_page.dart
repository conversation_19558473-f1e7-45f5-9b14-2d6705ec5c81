import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/presentation/pages/user_type_selection_page.dart';
import 'package:local_auth/local_auth.dart';
// import '../../core/security/advanced_security_manager.dart';

import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';
import 'email_verification_page.dart';
import 'home_page.dart';
import 'reset_password_page.dart';

/// LoginPage مسؤولة عن توثيق المستخدمين.
/// تعرض الصفحة رسالة ترحيب بسيطة ونموذج تسجيل الدخول بطريقة أنيقة وبسيطة.
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage>
    with SingleTickerProviderStateMixin {
  // المفتاح للتحكم بالتحقق من صحة النموذج.
  final _formKey = GlobalKey<FormState>();

  // متغيرات للتحكم بالرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // متغيرات للتحقق البيومتري
  final LocalAuthentication _localAuth = LocalAuthentication();
  bool _canCheckBiometrics = false;
  bool _isBiometricAvailable = false;

  // مدير الأمان المتقدم
  // final AdvancedSecurityManager _securityManager = AdvancedSecurityManager();

  // متغيرات للتحكم بحقل كلمة المرور
  bool _obscurePassword = true;

  // قيم الحقول.
  String email = '';
  String password = '';
  bool rememberMe = false;
  bool _isLoading = false; // حالة التحميل

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutQuad));

    // بدء الرسوم المتحركة
    _animationController.forward();

    // التحقق من توفر المصادقة البيومترية
    _checkBiometrics();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// التحقق من توفر المصادقة البيومترية
  Future<void> _checkBiometrics() async {
    bool canCheckBiometrics;
    try {
      canCheckBiometrics = await _localAuth.canCheckBiometrics;
      if (canCheckBiometrics) {
        final availableBiometrics = await _localAuth.getAvailableBiometrics();
        _isBiometricAvailable = availableBiometrics.isNotEmpty;
      }
    } on PlatformException {
      canCheckBiometrics = false;
    }

    if (!mounted) return;

    setState(() {
      _canCheckBiometrics = canCheckBiometrics;
    });
  }

  /// المصادقة باستخدام البصمة أو Face ID
  Future<void> _authenticateWithBiometrics() async {
    if (!_canCheckBiometrics || !_isBiometricAvailable) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('المصادقة البيومترية غير متوفرة على هذا الجهاز')));
      }
      return;
    }

    try {
      final authenticated = await _localAuth.authenticate(
        localizedReason: 'قم بالمصادقة للدخول إلى حسابك',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true));

      if (authenticated && mounted) {
        // هنا يمكن استدعاء عملية تسجيل الدخول التلقائي
        // على سبيل المثال، استخدام بيانات محفوظة مسبقًا
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم المصادقة بنجاح')));
      }
    } on PlatformException catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في المصادقة: ${e.message}')));
      }
    }
  }

  /// يبدأ عملية تسجيل الدخول بعد التحقق من صحة النموذج.
  void _login() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      // TODO: فحص الأمان قبل تسجيل الدخول
      // final securityStatus = await _securityManager.checkAppIntegrity();
      // if (!securityStatus.isSecure) {
      //   if (mounted) {
      //     ScaffoldMessenger.of(context).showSnackBar(
      //       const SnackBar(
      //         content: Text('تم اكتشاف مشاكل أمنية. يرجى التواصل مع الدعم الفني.'),
      //         backgroundColor: Colors.red,
      //       ),
      //     );
      //   }
      //   return;
      // }

      // TODO: التحقق من حالة القفل
      // if (_securityManager.isLocked(email)) {
      //   if (mounted) {
      //     ScaffoldMessenger.of(context).showSnackBar(
      //       const SnackBar(
      //         content: Text('تم قفل الحساب مؤقتاً بسبب محاولات دخول متكررة'),
      //         backgroundColor: Colors.orange,
      //       ),
      //     );
      //   }
      //   return;
      // }

      // بدء التحميل.
      setState(() {
        _isLoading = true;
      });

      context.read<AuthBloc>().add(
            LoginRequested(
              email: email,
              password: password,
              rememberMe: rememberMe));
    }
  }

  /// تبني قسم الترحيب البسيط.
  Widget _buildWelcomeSection() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // شعار التطبيق
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(20),
                  blurRadius: 15,
                  offset: const Offset(0, 5)),
              ]),
            child: Padding(
              padding: const EdgeInsets.all(15),
              child: Image.asset(
                'assets/images/logo.png',
                fit: BoxFit.contain))),
          const SizedBox(height: 24),
          // عنوان الترحيب
          Text(
            "مرحباً بعودتك!",
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
              letterSpacing: 0.5),
            textAlign: TextAlign.center),
          const SizedBox(height: 12),
          // رسالة ترحيبية
          Text(
            "سعداء بعودتك مرة أخرى إلى KREA",
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade700,
              letterSpacing: 0.3),
            textAlign: TextAlign.center),
          const SizedBox(height: 8),
          // وصف قصير للتطبيق
          Text(
            "منصتك المتكاملة لإدارة العقارات",
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
              fontStyle: FontStyle.italic),
            textAlign: TextAlign.center),
        ]));
  }

  /// تبني نموذج تسجيل الدخول مع الحقول اللازمة.
  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: SlideTransition(
        position: _slideAnimation,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              // حقل البريد الإلكتروني.
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 4)),
                  ]),
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: "البريد الإلكتروني",
                    hintText: "أدخل بريدك الإلكتروني",
                    prefixIcon:
                        const Icon(Icons.email, color: AppColors.primary),
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16.0),
                      borderSide: BorderSide.none),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16.0),
                      borderSide: BorderSide(color: Colors.grey.shade200)),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16.0),
                      borderSide:
                          const BorderSide(color: AppColors.primary, width: 2))),
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "الرجاء إدخال بريد إلكتروني";
                    }
                    // التحقق من صحة تنسيق البريد الإلكتروني
                    final emailRegex =
                        RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                    if (!emailRegex.hasMatch(value)) {
                      return "الرجاء إدخال بريد إلكتروني صحيح";
                    }
                    return null;
                  },
                  onSaved: (value) => email = value!.trim(),
                  onFieldSubmitted: (_) {
                    FocusScope.of(context).nextFocus();
                  })),
              const SizedBox(height: 20),
              // حقل كلمة المرور.
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 4)),
                  ]),
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: "كلمة المرور",
                    hintText: "أدخل كلمة المرور",
                    prefixIcon:
                        const Icon(Icons.lock, color: AppColors.primary),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility_off
                            : Icons.visibility,
                        color: Colors.grey),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      }),
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16.0),
                      borderSide: BorderSide.none),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16.0),
                      borderSide: BorderSide(color: Colors.grey.shade200)),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16.0),
                      borderSide:
                          const BorderSide(color: AppColors.primary, width: 2))),
                  obscureText: _obscurePassword,
                  textInputAction: TextInputAction.done,
                  validator: (value) => value == null || value.isEmpty
                      ? "الرجاء إدخال كلمة المرور"
                      : null,
                  onSaved: (value) => password = value!.trim(),
                  onFieldSubmitted: (_) => _login())),
              const SizedBox(height: 12),
              // صف يجمع بين خانة "تذكرني" وزر "نسيت كلمة المرور؟".
              Row(
                children: [
                  Theme(
                    data: ThemeData(
                      checkboxTheme: CheckboxThemeData(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4)))),
                    child: Checkbox(
                      value: rememberMe,
                      activeColor: AppColors.primary,
                      onChanged: (bool? value) {
                        setState(() {
                          rememberMe = value ?? false;
                        });
                      })),
                  const Text(
                    "تذكرني",
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (_) => const ResetPasswordPage()));
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8))),
                    child: const Text(
                      "نسيت كلمة المرور؟",
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold))),
                ]),
              const SizedBox(height: 24),
              // زر تسجيل الدخول مع مؤشر الدوران.
              Container(
                width: double.infinity,
                height: 55,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4)),
                  ]),
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _login,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16)),
                    padding: const EdgeInsets.symmetric(vertical: 12)),
                  icon: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2))
                      : const Icon(Icons.login_rounded, size: 22),
                  label: Text(
                    _isLoading ? "جاري الدخول..." : "تسجيل الدخول",
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)))),

              // زر المصادقة البيومترية
              if (_canCheckBiometrics && _isBiometricAvailable)
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: TextButton.icon(
                    onPressed: _authenticateWithBiometrics,
                    icon: const Icon(Icons.fingerprint, size: 22),
                    label: const Text("تسجيل الدخول باستخدام البصمة"),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary))),

              const Padding(
                padding: EdgeInsets.symmetric(vertical: 20),
                child: Row(
                  children: [
                    Expanded(child: Divider(thickness: 1)),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        "أو",
                        style: TextStyle(
                          color: Colors.grey,
                          fontWeight: FontWeight.w500))),
                    Expanded(child: Divider(thickness: 1)),
                  ])),

              // دعوة للتسجيل.
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey.shade200)),
                child: Column(
                  children: [
                    Text(
                      "ليس لديك حساب؟",
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                        fontWeight: FontWeight.w500)),
                    const SizedBox(height: 8),
                    OutlinedButton.icon(
                      onPressed: () {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                              builder: (_) => const UserTypeSelectionPage()));
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.primary,
                        side: const BorderSide(color: AppColors.primary),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12))),
                      icon: const Icon(Icons.app_registration, size: 20),
                      label: const Text(
                        "سجل حساب جديد",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold))),
                  ])),
            ]))));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // خلفية متدرجة للصفحة
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Colors.grey.shade50,
              Colors.grey.shade100,
            ])),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
              child: BlocListener<AuthBloc, AuthState>(
                listener: (context, state) {
                  if (state is AuthLoading) {
                    // تحديث حالة التحميل.
                    setState(() {
                      _isLoading = true;
                    });
                  }
                  if (state is Authenticated) {
                    setState(() {
                      _isLoading = false;
                    });

                    // عرض رسالة نجاح مع معلومات النقاط إذا كانت متوفرة
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                            state.welcomeMessage ?? "تم تسجيل الدخول بنجاح"),
                        backgroundColor: AppColors.success,
                        duration: const Duration(seconds: 3)));

                    // الانتقال إلى الصفحة الرئيسية
                    Future.delayed(const Duration(milliseconds: 500), () {
                      if (mounted) {
                        Navigator.pushAndRemoveUntil(
                          context,
                          MaterialPageRoute(builder: (_) => const HomePage()),
                          (route) => false);
                      }
                    });
                  } else if (state is AuthError) {
                    setState(() {
                      _isLoading = false;
                    });

                    // TODO: تسجيل محاولة فاشلة في مدير الأمان
                    // _securityManager.recordFailedAttempt(email);

                    // التحقق من نوع الخطأ للتوجيه المناسب
                    if (state.message.contains('غير مفعل')) {
                      // توجيه إلى صفحة التحقق من البريد الإلكتروني
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => EmailVerificationPage(email: email)));
                    } else {
                      // عرض رسالة الخطأ العادية
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(state.message),
                          backgroundColor: AppColors.error,
                          duration: const Duration(seconds: 3)));
                    }
                  }
                },
                child: Card(
                  elevation: 0,
                  color: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildWelcomeSection(),
                        const SizedBox(height: 32),
                        _buildLoginForm(),
                      ])))))))));
  }
}
