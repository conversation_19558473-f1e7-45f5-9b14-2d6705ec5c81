import 'package:flutter/foundation.dart';
import '../../domain/entities/project.dart';
import '../../domain/entities/project_task.dart';
import '../../domain/entities/project_milestone.dart';
import '../../domain/entities/project_document.dart';
import '../../domain/entities/team_member.dart';
import '../../domain/repositories/project_repository.dart';

/// مزود حالة المشاريع
class ProjectProvider extends ChangeNotifier {
  final ProjectRepository _projectRepository;

  ProjectProvider({required ProjectRepository projectRepository})
      : _projectRepository = projectRepository;

  // الحالة
  bool _isLoading = false;
  String? _error;
  List<Project> _projects = [];
  Project? _selectedProject;
  List<ProjectTask> _projectTasks = [];
  List<ProjectMilestone> _projectMilestones = [];
  List<ProjectDocument> _projectDocuments = [];
  List<TeamMember> _projectMembers = [];
  Map<String, dynamic> _projectStatistics = {};

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<Project> get projects => _projects;
  Project? get selectedProject => _selectedProject;
  List<ProjectTask> get projectTasks => _projectTasks;
  List<ProjectMilestone> get projectMilestones => _projectMilestones;
  List<ProjectDocument> get projectDocuments => _projectDocuments;
  List<TeamMember> get projectMembers => _projectMembers;
  Map<String, dynamic> get projectStatistics => _projectStatistics;

  /// تحميل مشاريع الشركة
  Future<void> loadCompanyProjects(String companyId) async {
    _setLoading(true);
    try {
      _projects = await _projectRepository.getCompanyProjects(companyId);
      _clearError();
    } catch (e) {
      _setError('فشل في تحميل المشاريع: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل مشاريع الشركة بالتحميل المتدرج
  Future<Map<String, dynamic>> loadCompanyProjectsPaginated({
    required String companyId,
    int limit = 20,
    String? lastProjectId,
    ProjectStatus? status,
    String? query,
  }) async {
    try {
      return await _projectRepository.getCompanyProjectsPaginated(
        companyId: companyId,
        limit: limit,
        lastProjectId: lastProjectId,
        status: status,
        query: query,
      );
    } catch (e) {
      _setError('فشل في تحميل المشاريع: $e');
      return {'projects': [], 'hasMore': false};
    }
  }

  /// البحث عن مشاريع
  Future<void> searchProjects({
    required String companyId,
    String? query,
    ProjectStatus? status,
    ProjectType? type,
    ProjectPriority? priority,
  }) async {
    _setLoading(true);
    try {
      _projects = await _projectRepository.searchProjects(
        companyId: companyId,
        query: query,
        status: status,
        type: type,
        priority: priority,
      );
      _clearError();
    } catch (e) {
      _setError('فشل في البحث عن المشاريع: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء مشروع جديد
  Future<String?> createProject(Project project) async {
    _setLoading(true);
    try {
      final projectId = await _projectRepository.createProject(project);
      await loadCompanyProjects(project.companyId);
      _clearError();
      return projectId;
    } catch (e) {
      _setError('فشل في إنشاء المشروع: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث مشروع
  Future<bool> updateProject(Project project) async {
    _setLoading(true);
    try {
      await _projectRepository.updateProject(project);
      await loadCompanyProjects(project.companyId);
      _clearError();
      return true;
    } catch (e) {
      _setError('فشل في تحديث المشروع: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف مشروع
  Future<bool> deleteProject(String projectId, String companyId) async {
    _setLoading(true);
    try {
      await _projectRepository.deleteProject(projectId);
      await loadCompanyProjects(companyId);
      _clearError();
      return true;
    } catch (e) {
      _setError('فشل في حذف المشروع: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديد مشروع
  Future<void> selectProject(String projectId) async {
    _setLoading(true);
    try {
      _selectedProject = await _projectRepository.getProjectById(projectId);
      if (_selectedProject != null) {
        await _loadProjectDetails(projectId);
      }
      _clearError();
    } catch (e) {
      _setError('فشل في تحميل تفاصيل المشروع: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل تفاصيل المشروع
  Future<void> _loadProjectDetails(String projectId) async {
    try {
      // تحميل المهام والمعالم والوثائق والأعضاء بشكل متوازي
      final futures = await Future.wait([
        _projectRepository.getProjectTasks(projectId),
        _projectRepository.getProjectMilestones(projectId),
        _projectRepository.getProjectDocuments(projectId),
        _projectRepository.getProjectMembers(projectId),
        _projectRepository.getProjectStatistics(projectId),
      ]);

      _projectTasks = futures[0] as List<ProjectTask>;
      _projectMilestones = futures[1] as List<ProjectMilestone>;
      _projectDocuments = futures[2] as List<ProjectDocument>;
      _projectMembers = futures[3] as List<TeamMember>;
      _projectStatistics = futures[4] as Map<String, dynamic>;
    } catch (e) {
      _setError('فشل في تحميل تفاصيل المشروع: $e');
    }
  }

  /// إضافة مهمة جديدة
  Future<String?> addTask(String projectId, ProjectTask task) async {
    try {
      final taskId = await _projectRepository.addProjectTask(projectId, task);
      await _loadProjectDetails(projectId);
      _clearError();
      return taskId;
    } catch (e) {
      _setError('فشل في إضافة المهمة: $e');
      return null;
    }
  }

  /// تحديث حالة المهمة
  Future<bool> updateTaskStatus(String taskId, TaskStatus status) async {
    try {
      await _projectRepository.updateTaskStatus(taskId, status);
      if (_selectedProject != null) {
        await _loadProjectDetails(_selectedProject!.id);
      }
      _clearError();
      return true;
    } catch (e) {
      _setError('فشل في تحديث حالة المهمة: $e');
      return false;
    }
  }

  /// إضافة معلم جديد
  Future<String?> addMilestone(String projectId, ProjectMilestone milestone) async {
    try {
      final milestoneId = await _projectRepository.addProjectMilestone(projectId, milestone);
      await _loadProjectDetails(projectId);
      _clearError();
      return milestoneId;
    } catch (e) {
      _setError('فشل في إضافة المعلم: $e');
      return null;
    }
  }

  /// إضافة عضو للمشروع
  Future<bool> addProjectMember(String projectId, String memberId) async {
    try {
      await _projectRepository.addProjectMember(projectId, memberId);
      await _loadProjectDetails(projectId);
      _clearError();
      return true;
    } catch (e) {
      _setError('فشل في إضافة عضو للمشروع: $e');
      return false;
    }
  }

  /// الحصول على المهام المتأخرة
  Future<List<ProjectTask>> getOverdueTasks(String projectId) async {
    try {
      return await _projectRepository.getOverdueTasks(projectId);
    } catch (e) {
      _setError('فشل في الحصول على المهام المتأخرة: $e');
      return [];
    }
  }

  /// الحصول على المعالم المتأخرة
  Future<List<ProjectMilestone>> getOverdueMilestones(String projectId) async {
    try {
      return await _projectRepository.getOverdueMilestones(projectId);
    } catch (e) {
      _setError('فشل في الحصول على المعالم المتأخرة: $e');
      return [];
    }
  }

  /// الحصول على تقرير تقدم المشروع
  Future<Map<String, dynamic>> getProjectProgressReport(String projectId) async {
    try {
      return await _projectRepository.getProjectProgressReport(projectId);
    } catch (e) {
      _setError('فشل في إنشاء تقرير التقدم: $e');
      return {};
    }
  }

  /// الحصول على إحصائيات مشاريع الشركة
  Future<Map<String, dynamic>> getCompanyProjectsStatistics(String companyId) async {
    try {
      return await _projectRepository.getCompanyProjectsStatistics(companyId);
    } catch (e) {
      _setError('فشل في الحصول على إحصائيات الشركة: $e');
      return {};
    }
  }

  /// الاستماع لتغييرات المشروع
  Stream<Project?> listenToProject(String projectId) {
    return _projectRepository.listenToProject(projectId);
  }

  /// الاستماع لتغييرات مهام المشروع
  Stream<List<ProjectTask>> listenToProjectTasks(String projectId) {
    return _projectRepository.listenToProjectTasks(projectId);
  }

  /// تنظيف البيانات
  void clearData() {
    _projects.clear();
    _selectedProject = null;
    _projectTasks.clear();
    _projectMilestones.clear();
    _projectDocuments.clear();
    _projectMembers.clear();
    _projectStatistics.clear();
    _clearError();
    notifyListeners();
  }

  // وظائف مساعدة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
