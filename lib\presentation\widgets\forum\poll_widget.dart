import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/poll_model.dart';
import '../../../domain/models/forum/poll_option_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';

/// مكون عرض استطلاع الرأي
class PollWidget extends StatefulWidget {
  /// نموذج استطلاع الرأي
  final PollModel poll;

  /// معرف الموضوع
  final String topicId;

  /// ما إذا كان للقراءة فقط
  final bool readOnly;

  /// دالة يتم استدعاؤها عند تغيير التصويت
  final Function(PollModel)? onPollUpdated;

  const PollWidget({
    super.key,
    required this.poll,
    required this.topicId,
    this.readOnly = false,
    this.onPollUpdated,
  });

  @override
  State<PollWidget> createState() => _PollWidgetState();
}

class _PollWidgetState extends State<PollWidget> with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  String? _selectedOptionId;
  List<String> _selectedOptionIds = [];
  String? _errorMessage;

  late AnimationController _animationController;
  late List<Animation<double>> _animations;

  bool get _hasVoted {
    final userId = Provider.of<AuthProvider>(context, listen: false).user?.uid;
    if (userId == null) return false;

    return widget.poll.options.any((option) => option.votedBy.contains(userId));
  }

  @override
  void initState() {
    super.initState();
    _initializeSelectedOptions();

    // تهيئة متحكم الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800));

    // تهيئة الرسوم المتحركة لكل خيار
    _animations = List.generate(
      widget.poll.options.length,
      (index) => Tween<double>(begin: 0, end: 1).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            index * 0.1,
            0.5 + index * 0.1,
            curve: Curves.easeOutCubic))));

    if (_hasVoted) {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تهيئة الخيارات المحددة
  void _initializeSelectedOptions() {
    final userId = Provider.of<AuthProvider>(context, listen: false).user?.uid;
    if (userId == null) return;

    if (widget.poll.allowMultipleChoices) {
      _selectedOptionIds = widget.poll.options
          .where((option) => option.votedBy.contains(userId))
          .map((option) => option.id)
          .toList();
    } else {
      final votedOption = widget.poll.options.firstWhere(
        (option) => option.votedBy.contains(userId),
        orElse: () => PollOptionModel(
          id: '',
          text: '',
          votedBy: [],
          votesCount: 0));
      if (votedOption.id.isNotEmpty) {
        _selectedOptionId = votedOption.id;
      }
    }
  }

  // إجمالي عدد الأصوات
  int get _totalVotes {
    return widget.poll.options.fold<int>(
      0,
      (sum, option) => sum + option.votesCount);
  }

  // ما إذا كان الاستطلاع منتهي الصلاحية
  bool get _isExpired {
    if (widget.poll.endDate == null) return false;
    return widget.poll.endDate!.isBefore(DateTime.now());
  }

  /// تبديل خيار
  void _toggleOption(String optionId) {
    if (_hasVoted || _isLoading || _isExpired || widget.readOnly) return;

    setState(() {
      if (widget.poll.allowMultipleChoices) {
        if (_selectedOptionIds.contains(optionId)) {
          _selectedOptionIds.remove(optionId);
        } else {
          _selectedOptionIds.add(optionId);
        }
      } else {
        if (_selectedOptionId == optionId) {
          _selectedOptionId = null;
        } else {
          _selectedOptionId = optionId;
        }
      }
    });
  }

  /// إرسال التصويت
  Future<void> _submitVote() async {
    final userId = Provider.of<AuthProvider>(context, listen: false).user?.uid;
    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول للتصويت')));
      return;
    }

    final optionIds = widget.poll.allowMultipleChoices
        ? _selectedOptionIds
        : _selectedOptionId != null ? [_selectedOptionId!] : [];

    if (optionIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار خيار واحد على الأقل')));
      return;
    }

    if (_isExpired) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('انتهت صلاحية الاستطلاع')));
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);

      final success = await forumProvider.voteOnPoll(
        widget.topicId,
        widget.poll.id,
        optionIds.cast<String>(),
        userId);

      if (success && mounted) {
        final updatedPoll = await forumProvider.getPollById(
          widget.topicId,
          widget.poll.id);

        if (updatedPoll != null && widget.onPollUpdated != null) {
          widget.onPollUpdated!(updatedPoll);
        }

        // تشغيل الرسوم المتحركة
        _animationController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء التصويت: ${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.poll,
                color: AppColors.primary,
                size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.poll.question,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16))),
            ]),
          const SizedBox(height: 8),
          if (widget.poll.description != null &&
              widget.poll.description!.isNotEmpty) ...[
            Text(
              widget.poll.description!,
              style: TextStyle(
                color: Colors.grey.shade700,
                fontSize: 14)),
            const SizedBox(height: 12),
          ],
          if (_hasVoted || widget.readOnly)
            _buildPollResults()
          else
            _buildPollOptions(),
          const SizedBox(height: 12),
          _buildPollFooter(),
        ]));
  }

  /// بناء خيارات الاستطلاع
  Widget _buildPollOptions() {
    return Column(
      children: [
        ...widget.poll.options.map((option) {
          final isSelected = widget.poll.allowMultipleChoices
              ? _selectedOptionIds.contains(option.id)
              : _selectedOptionId == option.id;

          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              onTap: _isLoading ? null : () => _toggleOption(option.id),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primary.withOpacity(0.1)
                      : Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected
                        ? AppColors.primary
                        : Colors.grey.shade300,
                    width: 1)),
                child: Row(
                  children: [
                    widget.poll.allowMultipleChoices
                        ? Checkbox(
                            value: isSelected,
                            onChanged: _isLoading
                                ? null
                                : (value) => _toggleOption(option.id),
                            activeColor: AppColors.primary)
                        : Radio<String>(
                            value: option.id,
                            groupValue: _selectedOptionId,
                            onChanged: _isLoading
                                ? null
                                : (value) => _toggleOption(option.id),
                            activeColor: AppColors.primary),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        option.text,
                        style: TextStyle(
                          color: isSelected
                              ? AppColors.primary
                              : Colors.black87,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal))),
                  ]))));
        }),
        const SizedBox(height: 16),
        if (_isLoading)
          const Center(
            child: CircularProgressIndicator())
        else
          ElevatedButton(
            onPressed: _submitVote,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 48),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8))),
            child: const Text('تصويت')),
      ]);
  }

  /// بناء نتائج الاستطلاع
  Widget _buildPollResults() {
    final totalVotes = widget.poll.options.fold<int>(
      0,
      (sum, option) => sum + option.votesCount);

    return Column(
      children: [
        ...widget.poll.options.map((option) {
          final percentage = totalVotes > 0
              ? (option.votesCount / totalVotes * 100).toStringAsFixed(1)
              : '0.0';
          final userId = Provider.of<AuthProvider>(context).user?.uid;
          final hasVoted = userId != null && option.votedBy.contains(userId);

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        option.text,
                        style: TextStyle(
                          fontWeight:
                              hasVoted ? FontWeight.bold : FontWeight.normal,
                          color: hasVoted ? AppColors.primary : Colors.black87))),
                    Text(
                      '$percentage%',
                      style: TextStyle(
                        fontWeight:
                            hasVoted ? FontWeight.bold : FontWeight.normal,
                        color: hasVoted ? AppColors.primary : Colors.black87)),
                    const SizedBox(width: 8),
                    Text(
                      '(${option.votesCount})',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12)),
                  ]),
                const SizedBox(height: 4),
                AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return Stack(
                      children: [
                        Container(
                          height: 8,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(4))),
                        Container(
                          height: 8,
                          width: totalVotes > 0
                              ? (option.votesCount / totalVotes) *
                                  MediaQuery.of(context).size.width *
                                  0.7 *
                                  _animationController.value
                              : 0,
                          decoration: BoxDecoration(
                            color: hasVoted ? AppColors.primary : Colors.grey.shade400,
                            borderRadius: BorderRadius.circular(4))),
                      ]);
                  }),
              ]));
        }),
      ]);
  }

  /// بناء تذييل الاستطلاع
  Widget _buildPollFooter() {
    final totalVotes = widget.poll.options.fold<int>(
      0,
      (sum, option) => sum + option.votesCount);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'إجمالي الأصوات: $totalVotes',
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 12)),
        if (widget.poll.endDate != null) ...[
          if (_isExpired)
            Text(
              'انتهى الاستطلاع',
              style: TextStyle(
                color: Colors.red.shade600,
                fontSize: 12,
                fontWeight: FontWeight.bold))
          else
            Row(
              children: [
                const Icon(
                  Icons.access_time,
                  size: 14,
                  color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  'ينتهي في ${_formatDate(widget.poll.endDate!)}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12)),
              ]),
        ],
      ]);
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }
}