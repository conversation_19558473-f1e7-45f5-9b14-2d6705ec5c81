import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/services/connectivity_service.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';
import 'package:kuwait_corners/presentation/bloc/estate_bloc.dart';
import 'package:kuwait_corners/presentation/bloc/estate_event.dart';
import 'package:kuwait_corners/presentation/bloc/estate_state.dart';
import 'package:kuwait_corners/presentation/widgets/error_widget.dart';
import 'package:kuwait_corners/presentation/widgets/estate_card.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:kuwait_corners/presentation/widgets/no_connection_widget.dart';

/// Widget to display a paginated list of estates.
class PaginatedEstatesList extends StatefulWidget {
  final bool isGridView;
  final String? filterCategory;

  const PaginatedEstatesList({
    super.key,
    this.isGridView = false,
    this.filterCategory,
  });

  @override
  State<PaginatedEstatesList> createState() => _PaginatedEstatesListState();
}

class _PaginatedEstatesListState extends State<PaginatedEstatesList> {
  final ScrollController _scrollController = ScrollController();
  final ConnectivityService _connectivityService = ConnectivityService();
  bool _isConnected = true;

  @override
  void initState() {
    super.initState();

    // التحقق من حالة الاتصال
    _checkConnectivity();

    // الاستماع لتغييرات الاتصال
    _connectivityService.connectionStatus.listen((isConnected) {
      setState(() {
        _isConnected = isConnected;
      });

      // إذا عاد الاتصال، نحاول تحميل البيانات مرة أخرى
      if (isConnected) {
        _loadEstates(refresh: true);
      }
    });

    // تحميل البيانات الأولية
    _loadEstates(refresh: true);

    // إضافة مستمع للتمرير لتحميل المزيد من البيانات عند الوصول إلى نهاية القائمة
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _checkConnectivity() async {
    final isConnected = await _connectivityService.isConnected();
    setState(() {
      _isConnected = isConnected;
    });
  }

  void _loadEstates({bool refresh = false}) {
    context.read<EstateBloc>().add(FetchPaginatedEstates(refresh: refresh));
  }

  void _loadMoreEstates(String lastDocumentId) {
    context
        .read<EstateBloc>()
        .add(FetchPaginatedEstates(lastDocumentId: lastDocumentId));
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final state = context.read<EstateBloc>().state;
      if (state is PaginatedEstatesLoaded && state.hasMore) {
        _loadMoreEstates(state.lastDocumentId!);
      }
    }
  }

  List<Estate> _filterEstates(List<Estate> estates) {
    if (widget.filterCategory == null || widget.filterCategory!.isEmpty) {
      return estates;
    }

    return estates
        .where((estate) =>
            estate.mainCategory == widget.filterCategory ||
            estate.subCategory == widget.filterCategory)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EstateBloc, EstateState>(
      builder: (context, state) {
        // إذا لم يكن هناك اتصال بالإنترنت
        if (!_isConnected && state is! PaginatedEstatesLoaded) {
          return NoConnectionWidget(
            onRetry: () {
              _checkConnectivity();
              if (_isConnected) {
                _loadEstates(refresh: true);
              }
            });
        }

        // حالة التحميل الأولي
        if (state is EstateLoading &&
            state.isFirstLoad &&
            !state.isLoadingMore) {
          return const LoadingWidget(isListView: true);
        }

        // حالة الخطأ
        if (state is EstateError) {
          return ErrorDisplayWidget(
            message: state.message,
            onRetry: () => _loadEstates(refresh: true));
        }

        // حالة تحميل البيانات بنجاح
        if (state is PaginatedEstatesLoaded) {
          final filteredEstates = _filterEstates(state.estates);

          if (filteredEstates.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.search_off,
                    size: 60,
                    color: Colors.grey),
                  const SizedBox(height: 16),
                  const Text(
                    'لا توجد عقارات متاحة',
                    style: TextStyle(fontSize: 18)),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => _loadEstates(refresh: true),
                    child: const Text('تحديث')),
                ]));
          }

          return widget.isGridView
              ? _buildGridView(filteredEstates, state)
              : _buildListView(filteredEstates, state);
        }

        return const SizedBox();
      });
  }

  Widget _buildListView(List<Estate> estates, PaginatedEstatesLoaded state) {
    return RefreshIndicator(
      onRefresh: () async {
        _loadEstates(refresh: true);
      },
      child: ListView.builder(
        controller: _scrollController,
        itemCount: estates.length + (state.hasMore ? 1 : 0),
        padding: const EdgeInsets.all(16),
        itemBuilder: (context, index) {
          if (index == estates.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator()));
          }

          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: EstateCard(
              estate: estates[index],
              isFeaturedSection: false));
        }));
  }

  Widget _buildGridView(List<Estate> estates, PaginatedEstatesLoaded state) {
    return RefreshIndicator(
      onRefresh: () async {
        _loadEstates(refresh: true);
      },
      child: GridView.builder(
        controller: _scrollController,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75, // Ajustado para evitar desbordamiento
          crossAxisSpacing: 10,
          mainAxisSpacing: 10),
        itemCount: estates.length + (state.hasMore ? 1 : 0),
        padding: const EdgeInsets.all(16),
        itemBuilder: (context, index) {
          if (index == estates.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator()));
          }

          return EstateCard(
            estate: estates[index],
            isFeaturedSection: false);
        }));
  }
}
