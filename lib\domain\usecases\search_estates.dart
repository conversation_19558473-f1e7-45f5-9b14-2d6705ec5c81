import '../entities/estate_base.dart';
import '../repositories/estate_repository.dart';

/// حالة استخدام للبحث عن العقارات
class SearchEstates {
  final EstateRepository repository;

  SearchEstates(this.repository);

  /// البحث عن العقارات
  /// [query] هو نص البحث
  /// [mainCategory] هو التصنيف الرئيسي (سكني، تجاري، أرض)
  /// [subCategory] هو التصنيف الفرعي (شقة، منزل، مكتب، محل، الخ)
  /// [minPrice] هو الحد الأدنى للسعر
  /// [maxPrice] هو الحد الأقصى للسعر
  /// [location] هو الموقع
  /// [filters] هي مرشحات إضافية
  /// يعيد قائمة بالعقارات التي تطابق معايير البحث
  Future<List<EstateBase>> call({
    String? query,
    String? mainCategory,
    String? subCategory,
    double? minPrice,
    double? maxPrice,
    String? location,
    Map<String, dynamic>? filters,
  }) async {
    return await repository.searchEstates(
      query: query,
      mainCategory: mainCategory,
      subCategory: subCategory,
      minPrice: minPrice,
      maxPrice: maxPrice,
      location: location,
      filters: filters);
  }
}
