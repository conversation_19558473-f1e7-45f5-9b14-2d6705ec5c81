import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/cairo_text_styles.dart';
import '../../../domain/entities/market_analysis.dart';
import '../../../domain/enums/analysis_type.dart';
import '../../../domain/enums/analysis_period.dart';
import '../../../domain/repositories/analytics_repository.dart';
import '../../../infrastructure/services/analytics_service.dart';
import '../../widgets/analytics/analysis_card.dart';
import '../../widgets/analytics/analysis_filter.dart';
import '../../widgets/analytics/chart_container.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/error_display_widget.dart';

/// شاشة تحليل السوق
class MarketAnalysisScreen extends StatefulWidget {
  final AnalysisType? initialType;

  /// إنشاء شاشة تحليل السوق
  const MarketAnalysisScreen({super.key, this.initialType});

  @override
  State<MarketAnalysisScreen> createState() => _MarketAnalysisScreenState();
}

class _MarketAnalysisScreenState extends State<MarketAnalysisScreen> {
  AnalysisType _selectedType = AnalysisType.price;
  AnalysisPeriod _selectedPeriod = AnalysisPeriod.year;
  String? _selectedArea;
  String? _selectedPropertyType;
  bool _isLoading = false;
  String? _errorMessage;
  List<MarketAnalysis> _analyses = [];

  @override
  void initState() {
    super.initState();
    // استخدام النوع الأولي إذا كان متاحًا
    if (widget.initialType != null) {
      _selectedType = widget.initialType!;
    }
    _loadAnalyses();
  }

  /// تحميل تحليلات السوق
  Future<void> _loadAnalyses() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final analyticsRepository = context.read<AnalyticsRepository>();
      final analyses = await analyticsRepository.getMarketAnalyses(
        type: _selectedType,
        period: _selectedPeriod,
        area: _selectedArea,
        propertyType: _selectedPropertyType);

      setState(() {
        _analyses = analyses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل التحليلات: $e';
        _isLoading = false;
      });
    }
  }

  /// تغيير نوع التحليل
  void _changeAnalysisType(AnalysisType type) {
    setState(() {
      _selectedType = type;
    });
    _loadAnalyses();
  }

  /// تغيير فترة التحليل
  void _changeAnalysisPeriod(AnalysisPeriod period) {
    setState(() {
      _selectedPeriod = period;
    });
    _loadAnalyses();
  }

  /// تغيير المنطقة
  void _changeArea(String? area) {
    setState(() {
      _selectedArea = area;
    });
    _loadAnalyses();
  }

  /// تغيير نوع العقار
  void _changePropertyType(String? propertyType) {
    setState(() {
      _selectedPropertyType = propertyType;
    });
    _loadAnalyses();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تحليل السوق', style: CairoTextStyles.headlineSmall),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white),
      body: Stack(
        children: [
          // خلفية الأيقونات الاقتصادية
          _buildEconomicIconsBackground(),
          // المحتوى الرئيسي
          Column(
            children: [
              // فلاتر التحليل
              AnalysisFilter(
                selectedType: _selectedType,
                selectedPeriod: _selectedPeriod,
                selectedArea: _selectedArea,
                selectedPropertyType: _selectedPropertyType,
                onTypeChanged: _changeAnalysisType,
                onPeriodChanged: _changeAnalysisPeriod,
                onAreaChanged: _changeArea,
                onPropertyTypeChanged: _changePropertyType),

              // محتوى التحليل
              Expanded(
                child: _buildContent()),
            ]),
        ],
      ),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator());
    }

    if (_errorMessage != null) {
      return ErrorDisplayWidget(
        message: _errorMessage!,
        onRetry: _loadAnalyses);
    }

    if (_analyses.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 64,
              color: AppColors.primary.withValues(alpha: 0.5)),
            const SizedBox(height: 16),
            const Text(
              'لا توجد تحليلات متاحة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text(
              'جرب تغيير المعايير أو المحاولة لاحقًا',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600])),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadAnalyses,
              child: const Text('إعادة المحاولة')),
          ]));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _analyses.length,
      itemBuilder: (context, index) {
        final analysis = _analyses[index];
        return AnalysisCard(
          analysis: analysis,
          onTap: () => _showAnalysisDetails(analysis));
      });
  }

  /// عرض تفاصيل التحليل
  void _showAnalysisDetails(MarketAnalysis analysis) {
    final analyticsService = RepositoryProvider.of<AnalyticsService>(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان التحليل
                  Text(
                    analysis.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),

                  // وصف التحليل
                  Text(
                    analysis.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600])),
                  const SizedBox(height: 16),

                  // الرسم البياني
                  if (analysis.type == AnalysisType.price)
                    ChartContainer(
                      title: 'تغير الأسعار',
                      height: 250,
                      chart: LineChart(
                        analyticsService.createPriceLineChart(analysis.data))),

                  const SizedBox(height: 16),

                  // الرؤى والتوصيات
                  const Text(
                    'الرؤى',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),

                  // قائمة الرؤى
                  ...analysis.insights.map((insight) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(Icons.lightbulb_outline,
                                size: 18, color: AppColors.warning),
                            const SizedBox(width: 8),
                            Expanded(child: Text(insight)),
                          ]))),

                  const SizedBox(height: 16),

                  const Text(
                    'التوصيات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),

                  // قائمة التوصيات
                  ...analysis.recommendations.map((recommendation) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(Icons.recommend,
                                size: 18, color: AppColors.success),
                            const SizedBox(width: 8),
                            Expanded(child: Text(recommendation)),
                          ]))),

                  const SizedBox(height: 16),

                  // الإحصائيات
                  const Text(
                    'الإحصائيات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),

                  // بطاقات الإحصائيات
                  Wrap(
                    spacing: 16,
                    runSpacing: 16,
                    children: [
                      _buildStatCard(
                        'متوسط السعر',
                        '${analysis.averagePricePerSquareMeter.toStringAsFixed(0)} ريال/م²',
                        Icons.monetization_on_outlined),
                      _buildStatCard(
                        'نسبة التغير',
                        '${analysis.priceChangePercentage.toStringAsFixed(1)}%',
                        Icons.trending_up,
                        color: analysis.priceChangePercentage >= 0
                            ? AppColors.success
                            : AppColors.error),
                      _buildStatCard(
                        'نمو العرض',
                        '${analysis.supplyGrowthRate.toStringAsFixed(1)}%',
                        Icons.inventory_2_outlined),
                      _buildStatCard(
                        'نمو الطلب',
                        '${analysis.demandGrowthRate.toStringAsFixed(1)}%',
                        Icons.people_outline),
                    ]),
                ]));
          });
      });
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon,
      {Color? color}) {
    return Container(
      width: MediaQuery.of(context).size.width / 2 - 24,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 18, color: color ?? AppColors.primary),
              const SizedBox(width: 8),
              Text(
                title,
                style: CairoTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600])),
            ]),
          const SizedBox(height: 8),
          Text(
            value,
            style: CairoTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color ?? Colors.black)),
        ]));
  }

  /// بناء خلفية الأيقونات الاقتصادية
  Widget _buildEconomicIconsBackground() {
    final economicIcons = [
      Icons.trending_up,
      Icons.trending_down,
      Icons.bar_chart,
      Icons.pie_chart,
      Icons.attach_money,
      Icons.account_balance,
      Icons.analytics,
      Icons.assessment,
      Icons.business_center,
      Icons.monetization_on,
      Icons.show_chart,
      Icons.timeline,
      Icons.insights,
      Icons.calculate,
      Icons.balance,
      Icons.savings,
      Icons.currency_exchange,
      Icons.price_change,
      Icons.inventory,
      Icons.real_estate_agent,
    ];

    return Positioned.fill(
      child: CustomPaint(
        painter: EconomicIconsPainter(
          icons: economicIcons,
          color: AppColors.primary.withValues(alpha: 0.05),
        ),
      ),
    );
  }
}

/// رسام الأيقونات الاقتصادية في الخلفية
class EconomicIconsPainter extends CustomPainter {
  final List<IconData> icons;
  final Color color;

  EconomicIconsPainter({
    required this.icons,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // إنشاء نمط منتظم من الأيقونات
    const iconSize = 24.0;
    const spacing = 80.0;

    final rows = (size.height / spacing).ceil();
    final cols = (size.width / spacing).ceil();

    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        // إضافة تنويع في المواضع لجعلها تبدو أكثر طبيعية
        final offsetX = (col * spacing) + (row.isEven ? 0 : spacing / 2);
        final offsetY = (row * spacing) + 40;

        // اختيار أيقونة عشوائية
        final iconIndex = (row * cols + col) % icons.length;
        final icon = icons[iconIndex];

        // رسم الأيقونة
        _drawIcon(
          canvas,
          icon,
          Offset(offsetX, offsetY),
          iconSize,
          paint,
        );
      }
    }
  }

  void _drawIcon(Canvas canvas, IconData icon, Offset position, double size, Paint paint) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(icon.codePoint),
        style: TextStyle(
          fontSize: size,
          fontFamily: icon.fontFamily,
          color: paint.color,
          fontWeight: FontWeight.w300,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        position.dx - textPainter.width / 2,
        position.dy - textPainter.height / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
