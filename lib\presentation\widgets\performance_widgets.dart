import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../infrastructure/services/app_manager.dart';
import '../../infrastructure/services/performance_service.dart';

/// مكون عرض حالة الاتصال
class ConnectionStatusWidget extends StatefulWidget {
  /// ما إذا كان يجب عرض أيقونة فقط
  final bool iconOnly;

  /// لون الأيقونة
  final Color? iconColor;

  /// حجم الأيقونة
  final double iconSize;

  const ConnectionStatusWidget({
    super.key,
    this.iconOnly = false,
    this.iconColor,
    this.iconSize = 24.0,
  });

  @override
  _ConnectionStatusWidgetState createState() => _ConnectionStatusWidgetState();
}

class _ConnectionStatusWidgetState extends State<ConnectionStatusWidget> {
  ConnectivityResult _connectionStatus = ConnectivityResult.none;

  @override
  void initState() {
    super.initState();

    // الحصول على حالة الاتصال الحالية
    _updateConnectionStatus();

    // الاستماع لتغييرات حالة الاتصال
    Connectivity().onConnectivityChanged.listen((result) {
      setState(() {
        _connectionStatus = result;
      });
    });
  }

  /// تحديث حالة الاتصال
  Future<void> _updateConnectionStatus() async {
    final result = await Connectivity().checkConnectivity();
    setState(() {
      _connectionStatus = result;
    });
  }

  @override
  Widget build(BuildContext context) {
    // تحديد الأيقونة والنص بناءً على حالة الاتصال
    IconData iconData;
    String statusText;
    Color statusColor;

    switch (_connectionStatus) {
      case ConnectivityResult.wifi:
        iconData = Icons.wifi;
        statusText = 'متصل بشبكة Wi-Fi';
        statusColor = Colors.green;
        break;
      case ConnectivityResult.mobile:
        iconData = Icons.signal_cellular_4_bar;
        statusText = 'متصل بشبكة الجوال';
        statusColor = Colors.orange;
        break;
      case ConnectivityResult.ethernet:
        iconData = Icons.settings_ethernet;
        statusText = 'متصل بشبكة سلكية';
        statusColor = Colors.green;
        break;
      case ConnectivityResult.bluetooth:
        iconData = Icons.bluetooth;
        statusText = 'متصل عبر البلوتوث';
        statusColor = Colors.blue;
        break;
      case ConnectivityResult.none:
      default:
        iconData = Icons.signal_wifi_off;
        statusText = 'غير متصل بالإنترنت';
        statusColor = Colors.red;
        break;
    }

    // إذا كان مطلوب عرض الأيقونة فقط
    if (widget.iconOnly) {
      return Icon(
        iconData,
        color: widget.iconColor ?? statusColor,
        size: widget.iconSize);
    }

    // عرض الأيقونة والنص
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          iconData,
          color: statusColor,
          size: widget.iconSize),
        const SizedBox(width: 8),
        Text(
          statusText,
          style: TextStyle(
            color: statusColor,
            fontWeight: FontWeight.bold)),
      ]);
  }
}

/// مكون عرض حالة البطارية
class BatteryStatusWidget extends StatefulWidget {
  /// ما إذا كان يجب عرض أيقونة فقط
  final bool iconOnly;

  /// لون الأيقونة
  final Color? iconColor;

  /// حجم الأيقونة
  final double iconSize;

  const BatteryStatusWidget({
    super.key,
    this.iconOnly = false,
    this.iconColor,
    this.iconSize = 24.0,
  });

  @override
  _BatteryStatusWidgetState createState() => _BatteryStatusWidgetState();
}

class _BatteryStatusWidgetState extends State<BatteryStatusWidget> {
  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // الحصول على مستوى البطارية
    final batteryLevel = performanceService.batteryLevel;

    // الحصول على حالة البطارية
    final batteryState = performanceService.batteryState;

    // الحصول على حالة وضع توفير الطاقة
    final isLowPowerMode = performanceService.isLowPowerMode;

    // تحديد الأيقونة والنص بناءً على حالة البطارية
    IconData iconData;
    String statusText;
    Color statusColor;

    if (batteryLevel >= 80) {
      iconData = Icons.battery_full;
      statusColor = Colors.green;
    } else if (batteryLevel >= 50) {
      iconData = Icons.battery_6_bar;
      statusColor = Colors.green;
    } else if (batteryLevel >= 30) {
      iconData = Icons.battery_4_bar;
      statusColor = Colors.orange;
    } else if (batteryLevel >= 15) {
      iconData = Icons.battery_2_bar;
      statusColor = Colors.orange;
    } else {
      iconData = Icons.battery_0_bar;
      statusColor = Colors.red;
    }

    // تحديد النص بناءً على حالة البطارية
    switch (batteryState) {
      case BatteryState.charging:
        statusText = 'جاري الشحن ($batteryLevel%)';
        iconData = Icons.battery_charging_full;
        statusColor = Colors.green;
        break;
      case BatteryState.full:
        statusText = 'مشحونة بالكامل (100%)';
        iconData = Icons.battery_full;
        statusColor = Colors.green;
        break;
      case BatteryState.discharging:
      case BatteryState.unknown:
      default:
        statusText = 'البطارية: $batteryLevel%';
        break;
    }

    // إضافة معلومات وضع توفير الطاقة
    if (isLowPowerMode) {
      statusText += ' (وضع توفير الطاقة)';
    }

    // إذا كان مطلوب عرض الأيقونة فقط
    if (widget.iconOnly) {
      return Icon(
        iconData,
        color: widget.iconColor ?? statusColor,
        size: widget.iconSize);
    }

    // عرض الأيقونة والنص
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          iconData,
          color: statusColor,
          size: widget.iconSize),
        const SizedBox(width: 8),
        Text(
          statusText,
          style: TextStyle(
            color: statusColor,
            fontWeight: FontWeight.bold)),
      ]);
  }
}

/// مكون عرض حالة الأداء
class PerformanceStatusWidget extends StatefulWidget {
  const PerformanceStatusWidget({super.key});

  @override
  _PerformanceStatusWidgetState createState() =>
      _PerformanceStatusWidgetState();
}

class _PerformanceStatusWidgetState extends State<PerformanceStatusWidget> {
  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // الحصول على مقاييس الأداء
    final performanceMetrics = performanceService.getPerformanceMetrics();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'حالة الأداء:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16)),
        const SizedBox(height: 8),
        _buildMetricRow(
          'معدل الإطارات',
          '${performanceMetrics['fps'].toStringAsFixed(1)} FPS',
          Icons.speed),
        _buildMetricRow(
          'استخدام الذاكرة',
          '${performanceMetrics['memoryUsage'].toStringAsFixed(1)} MB',
          Icons.memory),
        _buildMetricRow(
          'استخدام المعالج',
          '${performanceMetrics['cpuUsage'].toStringAsFixed(1)}%',
          Icons.developer_board),
        _buildMetricRow(
          'البطارية',
          '${performanceMetrics['batteryLevel']}%',
          Icons.battery_full),
        _buildMetricRow(
          'نوع الاتصال',
          _getConnectionTypeText(performanceMetrics['connectionType']),
          Icons.wifi),
      ]);
  }

  /// بناء صف لعرض مقياس أداء
  Widget _buildMetricRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.blue),
          const SizedBox(width: 8),
          Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Text(value),
        ]));
  }

  /// الحصول على نص نوع الاتصال
  String _getConnectionTypeText(String connectionType) {
    switch (connectionType) {
      case 'ConnectivityResult.wifi':
        return 'Wi-Fi';
      case 'ConnectivityResult.mobile':
        return 'بيانات الجوال';
      case 'ConnectivityResult.ethernet':
        return 'شبكة سلكية';
      case 'ConnectivityResult.bluetooth':
        return 'بلوتوث';
      case 'ConnectivityResult.none':
      default:
        return 'غير متصل';
    }
  }
}

/// مكون إعدادات الأداء
class PerformanceSettingsWidget extends StatefulWidget {
  /// دالة تغيير الإعدادات
  final void Function()? onSettingsChanged;

  const PerformanceSettingsWidget({
    super.key,
    this.onSettingsChanged,
  });

  @override
  _PerformanceSettingsWidgetState createState() =>
      _PerformanceSettingsWidgetState();
}

class _PerformanceSettingsWidgetState extends State<PerformanceSettingsWidget> {
  bool _isPerformanceMode = false;
  String _imageQuality = 'medium';
  bool _preloadingEnabled = true;
  bool _animationsEnabled = true;

  @override
  void initState() {
    super.initState();

    // تحميل الإعدادات الحالية
    _loadSettings();
  }

  /// تحميل الإعدادات الحالية
  void _loadSettings() {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    setState(() {
      _isPerformanceMode = performanceService.isPerformanceMode;
      _imageQuality = performanceService.getImageQuality();
      _preloadingEnabled = performanceService.isPreloadingEnabled();
      _animationsEnabled = performanceService.areAnimationsEnabled();
    });
  }

  /// تغيير وضع الأداء
  Future<void> _togglePerformanceMode(bool value) async {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تغيير وضع الأداء
    await performanceService.setPerformanceMode(value);

    // تحديث الإعدادات
    _loadSettings();

    // استدعاء دالة تغيير الإعدادات
    if (widget.onSettingsChanged != null) {
      widget.onSettingsChanged!();
    }
  }

  /// تغيير جودة الصور
  void _setImageQuality(String value) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تغيير جودة الصور
    performanceService.setImageQuality(value);

    setState(() {
      _imageQuality = value;
    });

    // استدعاء دالة تغيير الإعدادات
    if (widget.onSettingsChanged != null) {
      widget.onSettingsChanged!();
    }
  }

  /// تغيير حالة التحميل المسبق
  void _togglePreloading(bool value) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تغيير حالة التحميل المسبق
    performanceService.setPreloadingEnabled(value);

    setState(() {
      _preloadingEnabled = value;
    });

    // استدعاء دالة تغيير الإعدادات
    if (widget.onSettingsChanged != null) {
      widget.onSettingsChanged!();
    }
  }

  /// تغيير حالة الرسوم المتحركة
  void _toggleAnimations(bool value) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تغيير حالة الرسوم المتحركة
    performanceService.setAnimationsEnabled(value);

    setState(() {
      _animationsEnabled = value;
    });

    // استدعاء دالة تغيير الإعدادات
    if (widget.onSettingsChanged != null) {
      widget.onSettingsChanged!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إعدادات الأداء:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16)),
        const SizedBox(height: 8),
        SwitchListTile(
          title: const Text('وضع الأداء العالي'),
          subtitle: const Text(
              'تفعيل جميع الميزات بأعلى جودة (يزيد من استهلاك البطارية والبيانات)'),
          value: _isPerformanceMode,
          onChanged: _togglePerformanceMode),
        const Divider(),
        ListTile(
          title: const Text('جودة الصور'),
          subtitle: Text(_getImageQualityText(_imageQuality)),
          trailing: DropdownButton<String>(
            value: _imageQuality,
            onChanged: _isPerformanceMode
                ? null
                : (value) {
                    if (value != null) {
                      _setImageQuality(value);
                    }
                  },
            items: const [
              DropdownMenuItem(
                value: 'low',
                child: Text('منخفضة')),
              DropdownMenuItem(
                value: 'medium',
                child: Text('متوسطة')),
              DropdownMenuItem(
                value: 'high',
                child: Text('عالية')),
            ])),
        SwitchListTile(
          title: const Text('التحميل المسبق'),
          subtitle: const Text(
              'تحميل العناصر قبل ظهورها على الشاشة (يحسن السرعة ولكن يزيد من استهلاك البيانات)'),
          value: _preloadingEnabled,
          onChanged: _isPerformanceMode ? null : _togglePreloading),
        SwitchListTile(
          title: const Text('الرسوم المتحركة'),
          subtitle: const Text(
              'تفعيل الرسوم المتحركة في واجهة المستخدم (يحسن المظهر ولكن يزيد من استهلاك البطارية)'),
          value: _animationsEnabled,
          onChanged: _isPerformanceMode ? null : _toggleAnimations),
        const Divider(),
        FutureBuilder<String>(
          future: context.performanceService.getCacheSize(),
          builder: (context, snapshot) {
            final cacheSize = snapshot.data ?? 'جاري الحساب...';

            return ListTile(
              title: const Text('حجم التخزين المؤقت'),
              subtitle: Text(cacheSize),
              trailing: ElevatedButton(
                onPressed: () async {
                  await context.performanceService.clearCache();
                  setState(() {});

                  if (widget.onSettingsChanged != null) {
                    widget.onSettingsChanged!();
                  }
                },
                child: const Text('تنظيف')));
          }),
        const SizedBox(height: 8),
        Center(
          child: ElevatedButton(
            onPressed: () async {
              await context.performanceService.optimizePerformance();
              _loadSettings();

              if (widget.onSettingsChanged != null) {
                widget.onSettingsChanged!();
              }

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تحسين الأداء')));
            },
            child: const Text('تحسين الأداء تلقائياً'))),
      ]);
  }

  /// الحصول على نص جودة الصور
  String _getImageQualityText(String quality) {
    switch (quality) {
      case 'low':
        return 'منخفضة (يقلل من استهلاك البيانات)';
      case 'medium':
        return 'متوسطة (توازن بين الجودة واستهلاك البيانات)';
      case 'high':
        return 'عالية (أفضل جودة ولكن يزيد من استهلاك البيانات)';
      default:
        return 'متوسطة';
    }
  }
}

/// مكون محسن للرسوم المتحركة
class OptimizedAnimatedWidget extends StatefulWidget {
  /// مكون الطفل
  final Widget child;

  /// مدة الرسم المتحرك
  final Duration? duration;

  /// منحنى الرسم المتحرك
  final Curve? curve;

  /// ما إذا كان يجب تفعيل الرسم المتحرك
  final bool enabled;

  const OptimizedAnimatedWidget({
    super.key,
    required this.child,
    this.duration,
    this.curve,
    this.enabled = true,
  });

  @override
  _OptimizedAnimatedWidgetState createState() =>
      _OptimizedAnimatedWidgetState();
}

class _OptimizedAnimatedWidgetState extends State<OptimizedAnimatedWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تحديد ما إذا كان يجب تفعيل الرسم المتحرك
    final shouldAnimate =
        widget.enabled && performanceService.areAnimationsEnabled();

    // تحديد مدة الرسم المتحرك
    final duration = widget.duration ??
        performanceService.getAppropriateUIAnimationDuration();

    // تحديد منحنى الرسم المتحرك
    final curve =
        widget.curve ?? performanceService.getAppropriateUIAnimationCurve();

    // إنشاء وحدة التحكم في الرسم المتحرك
    _controller = AnimationController(
      vsync: this,
      duration: shouldAnimate ? duration : Duration.zero);

    // إنشاء الرسم المتحرك
    _animation = CurvedAnimation(
      parent: _controller,
      curve: curve);

    // بدء الرسم المتحرك
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: widget.child);
  }
}

/// مكون محسن للانتقال
class OptimizedPageTransition extends PageRouteBuilder {
  /// الصفحة المستهدفة
  final Widget page;

  /// نوع الانتقال
  final PageTransitionType type;

  /// مدة الانتقال
  final Duration? duration;

  /// منحنى الانتقال
  final Curve? curve;

  OptimizedPageTransition({
    required this.page,
    this.type = PageTransitionType.fade,
    this.duration,
    this.curve,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // الحصول على خدمة الأداء
            final performanceService = context.performanceService;

            // تحديد ما إذا كان يجب تفعيل الرسم المتحرك
            final shouldAnimate = performanceService.areAnimationsEnabled();

            // إذا كان الرسم المتحرك معطل، نعيد الصفحة مباشرة
            if (!shouldAnimate) {
              return child;
            }

            // تحديد منحنى الرسم المتحرك
            final animationCurve =
                curve ?? performanceService.getAppropriateUIAnimationCurve();
            final curvedAnimation = CurvedAnimation(
              parent: animation,
              curve: animationCurve);

            // تنفيذ الانتقال بناءً على النوع
            switch (type) {
              case PageTransitionType.fade:
                return FadeTransition(
                  opacity: curvedAnimation,
                  child: child);
              case PageTransitionType.rightToLeft:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(1, 0),
                    end: Offset.zero).animate(curvedAnimation),
                  child: child);
              case PageTransitionType.leftToRight:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(-1, 0),
                    end: Offset.zero).animate(curvedAnimation),
                  child: child);
              case PageTransitionType.topToBottom:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, -1),
                    end: Offset.zero).animate(curvedAnimation),
                  child: child);
              case PageTransitionType.bottomToTop:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 1),
                    end: Offset.zero).animate(curvedAnimation),
                  child: child);
              case PageTransitionType.scale:
                return ScaleTransition(
                  scale: curvedAnimation,
                  child: child);
              case PageTransitionType.rotate:
                return RotationTransition(
                  turns: curvedAnimation,
                  child: child);
              case PageTransitionType.size:
                return SizeTransition(
                  sizeFactor: curvedAnimation,
                  child: child);
              default:
                return FadeTransition(
                  opacity: curvedAnimation,
                  child: child);
            }
          },
          transitionDuration: duration ?? const Duration(milliseconds: 300));
}

/// أنواع الانتقال
enum PageTransitionType {
  fade,
  rightToLeft,
  leftToRight,
  topToBottom,
  bottomToTop,
  scale,
  rotate,
  size,
}
