import 'dart:math';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../domain/entities/market_analysis.dart';
import '../../domain/entities/market_prediction.dart';
import '../../domain/entities/price_estimation.dart';
import '../../domain/entities/recommendation.dart';
import '../../domain/enums/analysis_type.dart';
import '../../domain/enums/analysis_period.dart';
import '../../domain/repositories/analytics_repository.dart';

/// خدمة التحليلات
class AnalyticsService {
  final AnalyticsRepository _analyticsRepository;

  /// إنشاء خدمة التحليلات
  AnalyticsService(this._analyticsRepository);

  /// الحصول على تحليل السوق بواسطة المعرف
  Future<MarketAnalysis?> getMarketAnalysisById(String analysisId) {
    return _analyticsRepository.getMarketAnalysisById(analysisId);
  }

  /// الحصول على تحليلات السوق
  Future<List<MarketAnalysis>> getMarketAnalyses({
    AnalysisType? type,
    AnalysisPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketAnalyses(
      type: type,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تحليلات السوق بالتحميل المتدرج
  Future<Map<String, dynamic>> getMarketAnalysesPaginated({
    AnalysisType? type,
    AnalysisPeriod? period,
    String? area,
    String? propertyType,
    int limit = 20,
    String? lastAnalysisId,
  }) {
    return _analyticsRepository.getMarketAnalysesPaginated(
      type: type,
      period: period,
      area: area,
      propertyType: propertyType,
      limit: limit,
      lastAnalysisId: lastAnalysisId);
  }

  /// الحصول على أحدث تحليلات السوق
  Future<List<MarketAnalysis>> getLatestMarketAnalyses({int limit = 5}) {
    return _analyticsRepository.getLatestMarketAnalyses(limit: limit);
  }

  /// الحصول على تنبؤ السوق بواسطة المعرف
  Future<MarketPrediction?> getMarketPredictionById(String predictionId) {
    return _analyticsRepository.getMarketPredictionById(predictionId);
  }

  /// الحصول على تنبؤات السوق
  Future<List<MarketPrediction>> getMarketPredictions({
    PredictionType? type,
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketPredictions(
      type: type,
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تنبؤات السوق بالتحميل المتدرج
  Future<Map<String, dynamic>> getMarketPredictionsPaginated({
    PredictionType? type,
    PredictionPeriod? period,
    String? area,
    String? propertyType,
    int limit = 20,
    String? lastPredictionId,
  }) {
    return _analyticsRepository.getMarketPredictionsPaginated(
      type: type,
      period: period,
      area: area,
      propertyType: propertyType,
      limit: limit,
      lastPredictionId: lastPredictionId);
  }

  /// الحصول على أحدث تنبؤات السوق
  Future<List<MarketPrediction>> getLatestMarketPredictions({int limit = 5}) {
    return _analyticsRepository.getLatestMarketPredictions(limit: limit);
  }

  /// تقدير سعر عقار
  Future<PriceEstimation> estimatePrice({
    required String userId,
    required PriceEstimationType type,
    String? estateId,
    required String area,
    required String propertyType,
    required double size,
    int? rooms,
    int? bathrooms,
    int? age,
    int? floor,
    bool? isFurnished,
    bool? isRenovated,
    List<String>? features,
  }) {
    return _analyticsRepository.estimatePrice(
      userId: userId,
      type: type,
      estateId: estateId,
      area: area,
      propertyType: propertyType,
      size: size,
      rooms: rooms,
      bathrooms: bathrooms,
      age: age,
      floor: floor,
      isFurnished: isFurnished,
      isRenovated: isRenovated,
      features: features);
  }

  /// الحصول على تقدير السعر بواسطة المعرف
  Future<PriceEstimation?> getPriceEstimationById(String estimationId) {
    return _analyticsRepository.getPriceEstimationById(estimationId);
  }

  /// الحصول على تقديرات الأسعار للمستخدم
  Future<List<PriceEstimation>> getUserPriceEstimations(String userId) {
    return _analyticsRepository.getUserPriceEstimations(userId);
  }

  /// الحصول على تقديرات الأسعار للمستخدم بالتحميل المتدرج
  Future<Map<String, dynamic>> getUserPriceEstimationsPaginated({
    required String userId,
    int limit = 20,
    String? lastEstimationId,
  }) {
    return _analyticsRepository.getUserPriceEstimationsPaginated(
      userId: userId,
      limit: limit,
      lastEstimationId: lastEstimationId);
  }

  /// الحصول على توصيات للمستخدم
  Future<List<Recommendation>> getUserRecommendations({
    required String userId,
    RecommendationType? type,
    int limit = 10,
  }) {
    return _analyticsRepository.getUserRecommendations(
      userId: userId,
      type: type,
      limit: limit);
  }

  /// تحديث حالة التوصية
  Future<void> updateRecommendationStatus({
    required String recommendationId,
    bool? isViewed,
    bool? isClicked,
    bool? isDismissed,
  }) {
    return _analyticsRepository.updateRecommendationStatus(
      recommendationId: recommendationId,
      isViewed: isViewed,
      isClicked: isClicked,
      isDismissed: isDismissed);
  }

  /// الحصول على إحصائيات المناطق
  Future<Map<String, dynamic>> getAreasStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
  }) {
    return _analyticsRepository.getAreasStatistics(period: period);
  }

  /// الحصول على إحصائيات أنواع العقارات
  Future<Map<String, dynamic>> getPropertyTypesStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
  }) {
    return _analyticsRepository.getPropertyTypesStatistics(period: period);
  }

  /// الحصول على إحصائيات الأسعار
  Future<Map<String, dynamic>> getPriceStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getPriceStatistics(
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على إحصائيات العرض والطلب
  Future<Map<String, dynamic>> getSupplyDemandStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getSupplyDemandStatistics(
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على إحصائيات المبيعات
  Future<Map<String, dynamic>> getSalesStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getSalesStatistics(
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على إحصائيات الإيجارات
  Future<Map<String, dynamic>> getRentalsStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getRentalsStatistics(
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على إحصائيات العائد الاستثماري
  Future<Map<String, dynamic>> getInvestmentReturnStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getInvestmentReturnStatistics(
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على مؤشرات السوق
  Future<Map<String, dynamic>> getMarketIndicators({
    AnalysisPeriod period = AnalysisPeriod.year,
  }) {
    return _analyticsRepository.getMarketIndicators(period: period);
  }

  /// الحصول على أفضل المناطق للاستثمار
  Future<List<Map<String, dynamic>>> getTopInvestmentAreas({int limit = 5}) {
    return _analyticsRepository.getTopInvestmentAreas(limit: limit);
  }

  /// الحصول على أفضل أنواع العقارات للاستثمار
  Future<List<Map<String, dynamic>>> getTopInvestmentPropertyTypes(
      {int limit = 5}) {
    return _analyticsRepository.getTopInvestmentPropertyTypes(limit: limit);
  }

  /// الحصول على تقرير السوق
  Future<Map<String, dynamic>> getMarketReport({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) {
    return _analyticsRepository.getMarketReport(
      period: period,
      area: area,
      propertyType: propertyType);
  }

  /// الحصول على تقرير السوق المتقدم مع تحليل الذكاء الاصطناعي
  Future<Map<String, dynamic>> getAdvancedMarketReport({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
    bool includeAIAnalysis = true,
    bool includePredictions = true,
    bool includeInvestmentOpportunities = true,
  }) async {
    // الحصول على تقرير السوق الأساسي
    final basicReport = await _analyticsRepository.getMarketReport(
      period: period,
      area: area,
      propertyType: propertyType);

    // إضافة تحليلات متقدمة
    final advancedReport = Map<String, dynamic>.from(basicReport);

    // إضافة تحليل الذكاء الاصطناعي
    if (includeAIAnalysis) {
      advancedReport['aiAnalysis'] = await _generateAIMarketAnalysis(
        period: period,
        area: area,
        propertyType: propertyType,
        basicData: basicReport);
    }

    // إضافة التنبؤات المستقبلية
    if (includePredictions) {
      advancedReport['futurePredictions'] = await _generateMarketPredictions(
        period: period,
        area: area,
        propertyType: propertyType,
        basicData: basicReport);
    }

    // إضافة فرص الاستثمار
    if (includeInvestmentOpportunities) {
      advancedReport['investmentOpportunities'] =
          await _findInvestmentOpportunities(
        period: period,
        area: area,
        propertyType: propertyType,
        basicData: basicReport);
    }

    return advancedReport;
  }

  /// توليد تحليل السوق باستخدام الذكاء الاصطناعي
  Future<Map<String, dynamic>> _generateAIMarketAnalysis({
    required AnalysisPeriod period,
    String? area,
    String? propertyType,
    required Map<String, dynamic> basicData,
  }) async {
    // في التطبيق الحقيقي، هذا سيتصل بخدمة الذكاء الاصطناعي
    // هنا نقوم بمحاكاة النتائج

    // استخراج البيانات الأساسية
    final priceData = basicData['priceData'] ?? {};
    final supplyDemandData = basicData['supplyDemandData'] ?? {};
    final salesData = basicData['salesData'] ?? {};

    // تحليل اتجاه الأسعار
    final priceTrend = _analyzeMarketTrend(priceData);

    // تحليل العرض والطلب
    final supplyDemandAnalysis = _analyzeSupplyDemand(supplyDemandData);

    // تحليل المبيعات
    final salesAnalysis = _analyzeSalesData(salesData);

    // تحليل العوامل المؤثرة
    final factorsAnalysis = _analyzeMarketFactors(basicData);

    // توليد توصيات استنادًا إلى التحليل
    final recommendations = _generateMarketRecommendations(
      priceTrend: priceTrend,
      supplyDemandAnalysis: supplyDemandAnalysis,
      salesAnalysis: salesAnalysis,
      factorsAnalysis: factorsAnalysis);

    return {
      'priceTrend': priceTrend,
      'supplyDemandAnalysis': supplyDemandAnalysis,
      'salesAnalysis': salesAnalysis,
      'factorsAnalysis': factorsAnalysis,
      'recommendations': recommendations,
      'confidenceScore': 0.85, // درجة الثقة في التحليل
      'analysisDate': DateTime.now().toIso8601String(),
    };
  }

  /// تحليل اتجاه السوق
  Map<String, dynamic> _analyzeMarketTrend(Map<String, dynamic> priceData) {
    // في التطبيق الحقيقي، سيتم استخدام خوارزميات تحليل الاتجاهات
    // هنا نقوم بمحاكاة النتائج

    final prices = priceData['prices'] as List<dynamic>? ?? [];

    // حساب متوسط التغير
    double averageChange = 0;
    if (prices.isNotEmpty) {
      double totalChange = 0;
      for (int i = 1; i < prices.length; i++) {
        final currentPrice = prices[i]['value'] as double;
        final previousPrice = prices[i - 1]['value'] as double;
        totalChange += (currentPrice - previousPrice) / previousPrice;
      }
      averageChange = prices.length > 1 ? totalChange / (prices.length - 1) : 0;
    }

    // تحديد نوع الاتجاه
    String trendType = 'مستقر';
    if (averageChange > 0.05) {
      trendType = 'صعودي قوي';
    } else if (averageChange > 0.02) {
      trendType = 'صعودي معتدل';
    } else if (averageChange > 0) {
      trendType = 'صعودي طفيف';
    } else if (averageChange < -0.05) {
      trendType = 'هبوطي قوي';
    } else if (averageChange < -0.02) {
      trendType = 'هبوطي معتدل';
    } else if (averageChange < 0) {
      trendType = 'هبوطي طفيف';
    }

    // تحليل التقلبات
    double volatility = 0;
    if (prices.isNotEmpty) {
      double sumSquaredDeviations = 0;
      for (int i = 1; i < prices.length; i++) {
        final currentPrice = prices[i]['value'] as double;
        final previousPrice = prices[i - 1]['value'] as double;
        final change = (currentPrice - previousPrice) / previousPrice;
        sumSquaredDeviations += pow(change - averageChange, 2);
      }
      volatility = prices.length > 1
          ? sqrt(sumSquaredDeviations / (prices.length - 1))
          : 0;
    }

    return {
      'type': trendType,
      'averageChange': averageChange,
      'volatility': volatility,
      'strength': _getTrendStrength(averageChange, volatility),
      'sustainability': _getTrendSustainability(priceData),
      'description':
          _generateTrendDescription(trendType, averageChange, volatility),
    };
  }

  /// الحصول على قوة الاتجاه
  String _getTrendStrength(double averageChange, double volatility) {
    final absChange = averageChange.abs();

    if (absChange > 0.05) {
      return 'قوي';
    } else if (absChange > 0.02) {
      return 'معتدل';
    } else {
      return 'ضعيف';
    }
  }

  /// الحصول على استدامة الاتجاه
  String _getTrendSustainability(Map<String, dynamic> priceData) {
    // في التطبيق الحقيقي، سيتم تحليل استدامة الاتجاه بناءً على عوامل متعددة
    // هنا نقوم بمحاكاة النتائج

    final random = Random();
    final value = random.nextDouble();

    if (value > 0.7) {
      return 'مستدام';
    } else if (value > 0.4) {
      return 'متوسط الاستدامة';
    } else {
      return 'غير مستدام';
    }
  }

  /// توليد وصف الاتجاه
  String _generateTrendDescription(
      String trendType, double averageChange, double volatility) {
    final changePercent = (averageChange * 100).toStringAsFixed(1);

    if (trendType.contains('صعودي')) {
      return 'يشهد السوق اتجاهاً $trendType مع متوسط زيادة بنسبة $changePercent% في الأسعار. ${_getVolatilityDescription(volatility)}';
    } else if (trendType.contains('هبوطي')) {
      return 'يشهد السوق اتجاهاً $trendType مع متوسط انخفاض بنسبة ${(averageChange * -100).toStringAsFixed(1)}% في الأسعار. ${_getVolatilityDescription(volatility)}';
    } else {
      return 'يشهد السوق اتجاهاً $trendType مع تغيرات طفيفة في الأسعار. ${_getVolatilityDescription(volatility)}';
    }
  }

  /// الحصول على وصف التقلبات
  String _getVolatilityDescription(double volatility) {
    if (volatility > 0.1) {
      return 'السوق يشهد تقلبات كبيرة، مما يشير إلى عدم استقرار.';
    } else if (volatility > 0.05) {
      return 'السوق يشهد بعض التقلبات، مما يتطلب المراقبة.';
    } else {
      return 'السوق مستقر نسبياً مع تقلبات محدودة.';
    }
  }

  /// تحليل العرض والطلب
  Map<String, dynamic> _analyzeSupplyDemand(
      Map<String, dynamic> supplyDemandData) {
    // في التطبيق الحقيقي، سيتم استخدام خوارزميات تحليل العرض والطلب
    // هنا نقوم بمحاكاة النتائج

    final supplyData = supplyDemandData['supply'] as List<dynamic>? ?? [];
    final demandData = supplyDemandData['demand'] as List<dynamic>? ?? [];

    // حساب متوسط نمو العرض
    double supplyGrowthRate = 0;
    if (supplyData.isNotEmpty) {
      double totalChange = 0;
      for (int i = 1; i < supplyData.length; i++) {
        final currentSupply = supplyData[i]['value'] as double;
        final previousSupply = supplyData[i - 1]['value'] as double;
        totalChange += (currentSupply - previousSupply) / previousSupply;
      }
      supplyGrowthRate =
          supplyData.length > 1 ? totalChange / (supplyData.length - 1) : 0;
    }

    // حساب متوسط نمو الطلب
    double demandGrowthRate = 0;
    if (demandData.isNotEmpty) {
      double totalChange = 0;
      for (int i = 1; i < demandData.length; i++) {
        final currentDemand = demandData[i]['value'] as double;
        final previousDemand = demandData[i - 1]['value'] as double;
        totalChange += (currentDemand - previousDemand) / previousDemand;
      }
      demandGrowthRate =
          demandData.length > 1 ? totalChange / (demandData.length - 1) : 0;
    }

    // تحديد حالة التوازن
    String balanceState = 'متوازن';
    if (supplyGrowthRate > demandGrowthRate + 0.05) {
      balanceState = 'فائض في العرض';
    } else if (demandGrowthRate > supplyGrowthRate + 0.05) {
      balanceState = 'فائض في الطلب';
    }

    // تحديد اتجاه السوق
    String marketDirection = 'مستقر';
    if (demandGrowthRate > 0.05 && supplyGrowthRate < demandGrowthRate) {
      marketDirection = 'صعودي';
    } else if (supplyGrowthRate > 0.05 && demandGrowthRate < supplyGrowthRate) {
      marketDirection = 'هبوطي';
    }

    return {
      'supplyGrowthRate': supplyGrowthRate,
      'demandGrowthRate': demandGrowthRate,
      'balanceState': balanceState,
      'marketDirection': marketDirection,
      'supplyDemandRatio': supplyData.isNotEmpty && demandData.isNotEmpty
          ? (supplyData.last['value'] as double) /
              (demandData.last['value'] as double)
          : 1.0,
      'description': _generateSupplyDemandDescription(
        supplyGrowthRate,
        demandGrowthRate,
        balanceState,
        marketDirection),
    };
  }

  /// توليد وصف العرض والطلب
  String _generateSupplyDemandDescription(
    double supplyGrowthRate,
    double demandGrowthRate,
    String balanceState,
    String marketDirection) {
    final supplyPercent = (supplyGrowthRate * 100).toStringAsFixed(1);
    final demandPercent = (demandGrowthRate * 100).toStringAsFixed(1);

    return 'يشهد السوق حالة $balanceState مع نمو في العرض بنسبة $supplyPercent% ونمو في الطلب بنسبة $demandPercent%. الاتجاه العام للسوق $marketDirection.';
  }

  /// تحليل بيانات المبيعات
  Map<String, dynamic> _analyzeSalesData(Map<String, dynamic> salesData) {
    // في التطبيق الحقيقي، سيتم استخدام خوارزميات تحليل المبيعات
    // هنا نقوم بمحاكاة النتائج

    final salesVolumeData = salesData['volume'] as List<dynamic>? ?? [];
    final salesValueData = salesData['value'] as List<dynamic>? ?? [];

    // حساب متوسط نمو حجم المبيعات
    double volumeGrowthRate = 0;
    if (salesVolumeData.isNotEmpty) {
      double totalChange = 0;
      for (int i = 1; i < salesVolumeData.length; i++) {
        final currentVolume = salesVolumeData[i]['value'] as double;
        final previousVolume = salesVolumeData[i - 1]['value'] as double;
        totalChange += (currentVolume - previousVolume) / previousVolume;
      }
      volumeGrowthRate = salesVolumeData.length > 1
          ? totalChange / (salesVolumeData.length - 1)
          : 0;
    }

    // حساب متوسط نمو قيمة المبيعات
    double valueGrowthRate = 0;
    if (salesValueData.isNotEmpty) {
      double totalChange = 0;
      for (int i = 1; i < salesValueData.length; i++) {
        final currentValue = salesValueData[i]['value'] as double;
        final previousValue = salesValueData[i - 1]['value'] as double;
        totalChange += (currentValue - previousValue) / previousValue;
      }
      valueGrowthRate = salesValueData.length > 1
          ? totalChange / (salesValueData.length - 1)
          : 0;
    }

    // حساب متوسط سعر البيع
    double averageSalePrice = 0;
    if (salesVolumeData.isNotEmpty && salesValueData.isNotEmpty) {
      final lastVolume = salesVolumeData.last['value'] as double;
      final lastValue = salesValueData.last['value'] as double;

      if (lastVolume > 0) {
        averageSalePrice = lastValue / lastVolume;
      }
    }

    // تحديد حالة السوق
    String marketState = 'مستقر';
    if (volumeGrowthRate > 0.05 && valueGrowthRate > 0.05) {
      marketState = 'نشط';
    } else if (volumeGrowthRate < -0.05 && valueGrowthRate < -0.05) {
      marketState = 'راكد';
    }

    return {
      'volumeGrowthRate': volumeGrowthRate,
      'valueGrowthRate': valueGrowthRate,
      'averageSalePrice': averageSalePrice,
      'marketState': marketState,
      'description': _generateSalesDescription(
        volumeGrowthRate,
        valueGrowthRate,
        averageSalePrice,
        marketState),
    };
  }

  /// توليد وصف المبيعات
  String _generateSalesDescription(
    double volumeGrowthRate,
    double valueGrowthRate,
    double averageSalePrice,
    String marketState) {
    final volumePercent = (volumeGrowthRate * 100).toStringAsFixed(1);
    final valuePercent = (valueGrowthRate * 100).toStringAsFixed(1);
    final formattedPrice = averageSalePrice.toStringAsFixed(0);

    return 'السوق في حالة $marketState مع تغير في حجم المبيعات بنسبة $volumePercent% وتغير في قيمة المبيعات بنسبة $valuePercent%. متوسط سعر البيع $formattedPrice ريال.';
  }

  /// تحليل العوامل المؤثرة في السوق
  Map<String, dynamic> _analyzeMarketFactors(Map<String, dynamic> basicData) {
    // في التطبيق الحقيقي، سيتم تحليل العوامل المؤثرة بناءً على بيانات حقيقية
    // هنا نقوم بمحاكاة النتائج

    final random = Random();

    // العوامل الاقتصادية
    final economicFactors = <String, dynamic>{
      'gdpGrowth': 0.02 + random.nextDouble() * 0.03,
      'inflationRate': 0.01 + random.nextDouble() * 0.04,
      'interestRate': 0.02 + random.nextDouble() * 0.03,
      'unemploymentRate': 0.03 + random.nextDouble() * 0.05,
      'impact': random.nextDouble() > 0.5 ? 'إيجابي' : 'سلبي',
    };

    // العوامل الديموغرافية
    final demographicFactors = <String, dynamic>{
      'populationGrowth': 0.01 + random.nextDouble() * 0.02,
      'urbanizationRate': 0.01 + random.nextDouble() * 0.02,
      'householdFormation': 0.01 + random.nextDouble() * 0.03,
      'impact': random.nextDouble() > 0.3 ? 'إيجابي' : 'سلبي',
    };

    // العوامل التنظيمية
    final regulatoryFactors = <String, dynamic>{
      'taxChanges': random.nextBool(),
      'zoningChanges': random.nextBool(),
      'lendingRegulations': random.nextBool(),
      'impact': random.nextDouble() > 0.4 ? 'إيجابي' : 'سلبي',
    };

    // العوامل الأخرى
    final otherFactors = <String, dynamic>{
      'constructionCosts': 0.02 + random.nextDouble() * 0.04,
      'landAvailability': -0.01 - random.nextDouble() * 0.03,
      'foreignInvestment': 0.01 + random.nextDouble() * 0.05,
      'impact': random.nextDouble() > 0.5 ? 'إيجابي' : 'سلبي',
    };

    return {
      'economicFactors': economicFactors,
      'demographicFactors': demographicFactors,
      'regulatoryFactors': regulatoryFactors,
      'otherFactors': otherFactors,
      'description': _generateFactorsDescription(
        economicFactors,
        demographicFactors,
        regulatoryFactors,
        otherFactors),
    };
  }

  /// توليد وصف العوامل المؤثرة
  String _generateFactorsDescription(
    Map<String, dynamic> economicFactors,
    Map<String, dynamic> demographicFactors,
    Map<String, dynamic> regulatoryFactors,
    Map<String, dynamic> otherFactors) {
    final economicImpact = economicFactors['impact'] as String;
    final demographicImpact = demographicFactors['impact'] as String;
    final regulatoryImpact = regulatoryFactors['impact'] as String;
    final otherImpact = otherFactors['impact'] as String;

    final gdpGrowthValue = economicFactors['gdpGrowth'] as double;
    final inflationRateValue = economicFactors['inflationRate'] as double;
    final populationGrowthValue =
        demographicFactors['populationGrowth'] as double;

    final gdpGrowth = (gdpGrowthValue * 100).toStringAsFixed(1);
    final inflationRate = (inflationRateValue * 100).toStringAsFixed(1);
    final populationGrowth = (populationGrowthValue * 100).toStringAsFixed(1);

    return 'تؤثر العوامل الاقتصادية بشكل $economicImpact على السوق، مع نمو الناتج المحلي بنسبة $gdpGrowth% ومعدل تضخم $inflationRate%. '
        'العوامل الديموغرافية لها تأثير $demographicImpact مع نمو سكاني بنسبة $populationGrowth%. '
        'التغييرات التنظيمية تؤثر بشكل $regulatoryImpact على السوق. '
        'كما تؤثر عوامل أخرى مثل تكاليف البناء وتوفر الأراضي بشكل $otherImpact.';
  }

  /// توليد توصيات السوق
  Map<String, dynamic> _generateMarketRecommendations({
    required Map<String, dynamic> priceTrend,
    required Map<String, dynamic> supplyDemandAnalysis,
    required Map<String, dynamic> salesAnalysis,
    required Map<String, dynamic> factorsAnalysis,
  }) {
    // في التطبيق الحقيقي، سيتم توليد التوصيات بناءً على تحليل البيانات
    // هنا نقوم بمحاكاة النتائج

    final recommendations = <String>[];
    final investmentStrategies = <String>[];
    final riskFactors = <String>[];
    final opportunities = <String>[];

    // توصيات بناءً على اتجاه الأسعار
    final trendType = priceTrend['type'] as String;
    if (trendType.contains('صعودي')) {
      recommendations
          .add('الاستفادة من ارتفاع الأسعار في بيع العقارات الحالية');
      investmentStrategies.add('التركيز على العقارات ذات القيمة المضافة');
      riskFactors.add('احتمالية تصحيح الأسعار بعد فترة من الارتفاع');
      opportunities.add('الاستثمار في المناطق الناشئة قبل ارتفاع أسعارها');
    } else if (trendType.contains('هبوطي')) {
      recommendations.add('البحث عن فرص شراء مع انخفاض الأسعار');
      investmentStrategies.add('استراتيجية الشراء والاحتفاظ على المدى الطويل');
      riskFactors.add('استمرار انخفاض الأسعار لفترة أطول من المتوقع');
      opportunities.add('شراء العقارات المتميزة بأسعار مخفضة');
    } else {
      recommendations.add('الاستثمار في العقارات ذات العائد المستقر');
      investmentStrategies.add('التنويع بين مختلف أنواع العقارات');
      riskFactors.add('تغير مفاجئ في اتجاه السوق');
      opportunities.add('الاستثمار في العقارات ذات الموقع المتميز');
    }

    // توصيات بناءً على تحليل العرض والطلب
    final balanceState = supplyDemandAnalysis['balanceState'] as String;
    if (balanceState == 'فائض في الطلب') {
      recommendations
          .add('التركيز على تطوير عقارات جديدة لتلبية الطلب المتزايد');
      investmentStrategies.add('الاستثمار في قطاع التطوير العقاري');
      opportunities.add('تطوير مشاريع سكنية في المناطق ذات الطلب المرتفع');
    } else if (balanceState == 'فائض في العرض') {
      recommendations.add('تجنب الاستثمار في المناطق ذات المعروض الزائد');
      investmentStrategies
          .add('التركيز على العقارات الفريدة ذات الميزة التنافسية');
      riskFactors.add('صعوبة بيع العقارات في ظل المنافسة العالية');
    }

    // توصيات بناءً على تحليل المبيعات
    final marketState = salesAnalysis['marketState'] as String;
    if (marketState == 'نشط') {
      recommendations.add('الاستفادة من نشاط السوق في تسريع عمليات البيع');
      opportunities.add('فرصة لبيع العقارات بأسعار جيدة في ظل النشاط الحالي');
    } else if (marketState == 'راكد') {
      recommendations.add('التركيز على تحسين جودة العقارات لجذب المشترين');
      riskFactors.add('طول فترة عرض العقارات للبيع');
      investmentStrategies.add('الاستثمار في العقارات ذات الدخل المستقر');
    }

    return {
      'recommendations': recommendations,
      'investmentStrategies': investmentStrategies,
      'riskFactors': riskFactors,
      'opportunities': opportunities,
    };
  }

  /// توليد تنبؤات السوق
  Future<Map<String, dynamic>> _generateMarketPredictions({
    required AnalysisPeriod period,
    String? area,
    String? propertyType,
    required Map<String, dynamic> basicData,
  }) async {
    // في التطبيق الحقيقي، سيتم استخدام نماذج تنبؤ متقدمة
    // هنا نقوم بمحاكاة النتائج

    final random = Random();

    // استخراج البيانات الأساسية
    final priceData = basicData['priceData'] ?? {};
    final prices = priceData['prices'] as List<dynamic>? ?? [];

    // الحصول على القيمة الحالية
    double currentValue = 0;
    if (prices.isNotEmpty) {
      currentValue = prices.last['value'] as double;
    } else {
      currentValue = 1000 + random.nextDouble() * 9000;
    }

    // توليد تنبؤات الأسعار
    final pricePredictions = <Map<String, dynamic>>[];
    double lastValue = currentValue;

    // تحديد اتجاه التنبؤ
    final trendDirection = random.nextDouble() > 0.5 ? 1.0 : -1.0;
    final trendStrength = 0.01 + random.nextDouble() * 0.04;

    // توليد قيم التنبؤ
    for (int i = 1; i <= 12; i++) {
      final change =
          trendDirection * trendStrength + (random.nextDouble() - 0.5) * 0.02;
      lastValue = lastValue * (1 + change);

      pricePredictions.add({
        'date': _getMonthName(i),
        'value': lastValue,
        'change': change,
      });
    }

    // تحديد اتجاه التنبؤ
    final trend = trendDirection > 0 ? 'صعودي' : 'هبوطي';

    // حساب نسبة التغير المتوقعة
    final predictionLastValue = pricePredictions.last['value'] as double;
    final expectedChange = (predictionLastValue - currentValue) / currentValue;

    // تحديد العوامل المؤثرة في التنبؤ
    final factors = <Map<String, dynamic>>[
      {
        'name': 'العرض والطلب',
        'impact': 0.3 + random.nextDouble() * 0.2,
        'direction': random.nextDouble() > 0.5 ? 'إيجابي' : 'سلبي',
      },
      {
        'name': 'النمو الاقتصادي',
        'impact': 0.2 + random.nextDouble() * 0.2,
        'direction': random.nextDouble() > 0.6 ? 'إيجابي' : 'سلبي',
      },
      {
        'name': 'أسعار الفائدة',
        'impact': 0.1 + random.nextDouble() * 0.2,
        'direction': random.nextDouble() > 0.4 ? 'إيجابي' : 'سلبي',
      },
      {
        'name': 'التضخم',
        'impact': 0.1 + random.nextDouble() * 0.15,
        'direction': random.nextDouble() > 0.3 ? 'إيجابي' : 'سلبي',
      },
      {
        'name': 'السياسات الحكومية',
        'impact': 0.15 + random.nextDouble() * 0.15,
        'direction': random.nextDouble() > 0.5 ? 'إيجابي' : 'سلبي',
      },
    ];

    return {
      'currentValue': currentValue,
      'predictions': pricePredictions,
      'trend': trend,
      'expectedChange': expectedChange,
      'confidenceLevel': 0.7 + random.nextDouble() * 0.2,
      'factors': factors,
      'description': _generatePredictionDescription(trend, expectedChange),
    };
  }

  /// توليد وصف التنبؤ
  String _generatePredictionDescription(String trend, double expectedChange) {
    final changePercent = (expectedChange * 100).toStringAsFixed(1);

    if (trend == 'صعودي') {
      return 'من المتوقع أن تشهد الأسعار اتجاهاً صعودياً خلال الفترة القادمة بنسبة تغير متوقعة $changePercent%. يرجع ذلك إلى عدة عوامل منها العرض والطلب والنمو الاقتصادي.';
    } else {
      return 'من المتوقع أن تشهد الأسعار اتجاهاً هبوطياً خلال الفترة القادمة بنسبة تغير متوقعة ${(expectedChange * -100).toStringAsFixed(1)}%. يرجع ذلك إلى عدة عوامل منها العرض والطلب والنمو الاقتصادي.';
    }
  }

  /// الحصول على اسم الشهر
  String _getMonthName(int month) {
    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    final currentMonth = DateTime.now().month;
    final targetMonth = (currentMonth + month - 1) % 12;

    return months[targetMonth];
  }

  /// البحث عن فرص الاستثمار
  Future<List<Map<String, dynamic>>> _findInvestmentOpportunities({
    required AnalysisPeriod period,
    String? area,
    String? propertyType,
    required Map<String, dynamic> basicData,
  }) async {
    // في التطبيق الحقيقي، سيتم تحليل البيانات لإيجاد فرص الاستثمار الحقيقية
    // هنا نقوم بمحاكاة النتائج

    final random = Random();
    final opportunities = <Map<String, dynamic>>[];

    // قائمة المناطق والمحافظات الكويتية
    final areas = [
      'العاصمة',
      'حولي',
      'الفروانية',
      'مبارك الكبير',
      'الأحمدي',
      'الجهراء',
      'السالمية',
      'الشويخ',
      'الفحيحيل',
      'المنقف',
      'الجابرية',
      'سلوى',
      'الرميثية',
      'الفنطاس',
      'أبو حليفة',
      'صباح السالم',
      'القرين',
      'العقيلة',
      'الرقة',
      'هدية',
    ];

    // قائمة أنواع العقارات
    final propertyTypes = [
      'شقة',
      'فيلا',
      'أرض',
      'عمارة',
      'مكتب',
      'محل تجاري',
      'مستودع',
    ];

    // توليد فرص استثمارية
    for (int i = 0; i < 5; i++) {
      final selectedArea = areas[random.nextInt(areas.length)];
      final selectedType = propertyTypes[random.nextInt(propertyTypes.length)];
      final expectedReturn = 0.05 + random.nextDouble() * 0.1;
      final riskLevel = random.nextDouble();

      String riskCategory;
      if (riskLevel < 0.3) {
        riskCategory = 'منخفض';
      } else if (riskLevel < 0.7) {
        riskCategory = 'متوسط';
      } else {
        riskCategory = 'مرتفع';
      }

      final investmentPeriod = 1 + random.nextInt(5);

      opportunities.add({
        'area': selectedArea,
        'propertyType': selectedType,
        'expectedReturn': expectedReturn,
        'riskLevel': riskCategory,
        'investmentPeriod': investmentPeriod,
        'description': _generateOpportunityDescription(
          selectedArea,
          selectedType,
          expectedReturn,
          riskCategory,
          investmentPeriod),
        'reasons': _generateOpportunityReasons(selectedArea, selectedType),
      });
    }

    // ترتيب الفرص حسب العائد المتوقع
    opportunities.sort((a, b) {
      final returnA = a['expectedReturn'] as double;
      final returnB = b['expectedReturn'] as double;
      return returnB.compareTo(returnA);
    });

    return opportunities;
  }

  /// توليد وصف فرصة استثمارية
  String _generateOpportunityDescription(
    String area,
    String propertyType,
    double expectedReturn,
    String riskLevel,
    int investmentPeriod) {
    final returnPercent = (expectedReturn * 100).toStringAsFixed(1);

    return 'استثمار في $propertyType بمنطقة $area مع عائد متوقع $returnPercent% ومستوى مخاطرة $riskLevel. فترة الاستثمار المقترحة $investmentPeriod سنوات.';
  }

  /// توليد أسباب فرصة استثمارية
  List<String> _generateOpportunityReasons(String area, String propertyType) {
    final reasons = <String>[];

    reasons.add('نمو متوقع في منطقة $area');
    reasons.add('طلب متزايد على $propertyType');
    reasons.add('مشاريع تطويرية قريبة تزيد من قيمة العقارات');
    reasons.add('تحسن في البنية التحتية للمنطقة');

    return reasons;
  }

  /// الحصول على تقرير المستخدم
  Future<Map<String, dynamic>> getUserReport(String userId) {
    return _analyticsRepository.getUserReport(userId);
  }

  /// إنشاء رسم بياني خطي للأسعار
  LineChartData createPriceLineChart(Map<String, dynamic> data) {
    final spots = <FlSpot>[];
    final dates = <String>[];

    // استخراج البيانات
    final priceData = data['prices'] as List<dynamic>;
    for (int i = 0; i < priceData.length; i++) {
      final price = priceData[i]['value'] as double;
      spots.add(FlSpot(i.toDouble(), price));
      dates.add(priceData[i]['date'] as String);
    }

    return LineChartData(
      gridData: FlGridData(show: true),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 22)),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 28))),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: const Color(0xff37434d), width: 1)),
      minX: 0,
      maxX: spots.length.toDouble() - 1,
      minY: 0,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          color: Colors.blue,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: const FlDotData(show: false),
          belowBarData: BarAreaData(
            show: true,
            color: Colors.blue.withAlpha(76))),
      ]);
  }

  /// إنشاء رسم بياني شريطي للمناطق
  BarChartData createAreasBarChart(Map<String, dynamic> data) {
    final areasData = data['areas'] as List<dynamic>;

    return BarChartData(
      alignment: BarChartAlignment.spaceAround,
      maxY: areasData
              .map<double>((area) => area['value'] as double)
              .reduce((a, b) => a > b ? a : b) *
          1.2,
      barTouchData: BarTouchData(
        enabled: true,
        touchTooltipData: BarTouchTooltipData(
          // tooltipBgColor parameter has been renamed in newer versions
          // tooltipBgColor: Colors.blueGrey,
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            final area = areasData[groupIndex]['name'] as String;
            final value = areasData[groupIndex]['value'] as double;
            return BarTooltipItem(
              '$area\n',
              const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14),
              children: <TextSpan>[
                TextSpan(
                  text: value.toString(),
                  style: const TextStyle(
                    color: Colors.yellow,
                    fontSize: 16,
                    fontWeight: FontWeight.w500)),
              ]);
          })),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 22)),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 28))),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.grey.shade300, width: 1)),
      barGroups: List.generate(
        areasData.length,
        (index) {
          final value = areasData[index]['value'] as double;
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: value,
                color: Colors.blue,
                width: 22,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6))),
            ]);
        }));
  }

  /// إنشاء رسم بياني دائري لأنواع العقارات
  PieChartData createPropertyTypesPieChart(Map<String, dynamic> data) {
    final propertyTypesData = data['propertyTypes'] as List<dynamic>;
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.yellow,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
      Colors.amber,
    ];

    return PieChartData(
      borderData: FlBorderData(show: false),
      sectionsSpace: 0,
      centerSpaceRadius: 40,
      sections: List.generate(
        propertyTypesData.length,
        (index) {
          final data = propertyTypesData[index];
          final value = data['value'] as double;
          final name = data['name'] as String;
          final color = colors[index % colors.length];

          return PieChartSectionData(
            color: color,
            value: value,
            title: '$name\n${value.toStringAsFixed(1)}%',
            radius: 50,
            titleStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white));
        }));
  }

  /// إنشاء رسم بياني خطي للتنبؤات
  LineChartData createPredictionLineChart(MarketPrediction prediction) {
    final spots = <FlSpot>[];
    final dates = <String>[];

    // استخراج البيانات
    final predictionData = prediction.data['predictions'] as List<dynamic>;
    for (int i = 0; i < predictionData.length; i++) {
      final value = predictionData[i]['value'] as double;
      spots.add(FlSpot(i.toDouble(), value));
      dates.add(predictionData[i]['date'] as String);
    }

    // إضافة نقطة القيمة الحالية
    spots.insert(0, FlSpot(0, prediction.currentValue));
    dates.insert(0, 'الآن');

    return LineChartData(
      gridData: FlGridData(show: true),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 22)),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 28))),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.grey.shade300, width: 1)),
      minX: 0,
      maxX: spots.length.toDouble() - 1,
      minY: 0,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          color: prediction.trend == PredictionTrend.up
              ? Colors.green
              : prediction.trend == PredictionTrend.down
                  ? Colors.red
                  : Colors.blue,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true,
            getDotPainter: (spot, percent, barData, index) {
              final dotColor = prediction.trend == PredictionTrend.up
                  ? Colors.green
                  : prediction.trend == PredictionTrend.down
                      ? Colors.red
                      : Colors.blue;
              return FlDotCirclePainter(
                radius: index == 0 ? 6 : 4,
                color: index == 0 ? Colors.white : dotColor,
                strokeWidth: 2,
                strokeColor: dotColor);
            }),
          belowBarData: BarAreaData(
            show: true,
            color: prediction.trend == PredictionTrend.up
                ? Colors.green.withAlpha(76)
                : prediction.trend == PredictionTrend.down
                    ? Colors.red.withAlpha(76)
                    : Colors.blue.withAlpha(76))),
      ],
      extraLinesData: ExtraLinesData(
        horizontalLines: [
          HorizontalLine(
            y: prediction.currentValue,
            color: Colors.grey,
            strokeWidth: 1,
            dashArray: [5, 5]),
        ]));
  }

  /// إنشاء رسم بياني شريطي للعائد الاستثماري
  BarChartData createInvestmentReturnBarChart(Map<String, dynamic> data) {
    final investmentData = data['investments'] as List<dynamic>;

    return BarChartData(
      alignment: BarChartAlignment.spaceAround,
      maxY: investmentData
              .map<double>((investment) => investment['return'] as double)
              .reduce((a, b) => a > b ? a : b) *
          1.2,
      barTouchData: BarTouchData(
        enabled: true,
        touchTooltipData: BarTouchTooltipData(
          // tooltipBgColor parameter has been renamed in newer versions
          // tooltipBgColor: Colors.blueGrey,
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            final name = investmentData[groupIndex]['name'] as String;
            final returnValue = investmentData[groupIndex]['return'] as double;
            return BarTooltipItem(
              '$name\n',
              const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14),
              children: <TextSpan>[
                TextSpan(
                  text: '${returnValue.toStringAsFixed(2)}%',
                  style: const TextStyle(
                    color: Colors.yellow,
                    fontSize: 16,
                    fontWeight: FontWeight.w500)),
              ]);
          })),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 22)),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 28))),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.grey.shade300, width: 1)),
      barGroups: List.generate(
        investmentData.length,
        (index) {
          final returnValue = investmentData[index]['return'] as double;
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: returnValue,
                color: Colors.green,
                width: 22,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6))),
            ]);
        }));
  }
}
