import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/notification_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/empty_view.dart';

/// صفحة الإشعارات
class NotificationsPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/notifications';

  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // إضافة مستمع للتمرير لتحميل المزيد من الإشعارات
    _scrollController.addListener(_scrollListener);

    // جلب البيانات عند تحميل الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يجب تسجيل الدخول لعرض الإشعارات')));
      Navigator.pop(context);
      return;
    }

    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    // جلب الإشعارات
    await forumProvider.fetchNotifications(authProvider.user!.uid,
        refresh: true);
  }

  /// مستمع التمرير لتحميل المزيد من الإشعارات
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      if (forumProvider.notificationsState != LoadingState.loading &&
          forumProvider.hasMoreNotifications &&
          authProvider.isLoggedIn) {
        forumProvider.fetchNotifications(authProvider.user!.uid);
      }
    }
  }

  /// تعليم جميع الإشعارات كمقروءة
  Future<void> _markAllAsRead() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) return;

    try {
      await forumProvider.markAllNotificationsAsRead(authProvider.user!.uid);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم تعليم جميع الإشعارات كمقروءة')));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تعليم الإشعارات كمقروءة')));
    }
  }

  /// تعليم إشعار كمقروء
  Future<void> _markAsRead(String notificationId) async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    try {
      await forumProvider.markNotificationAsRead(notificationId);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تعليم الإشعار كمقروء')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('الإشعارات'),
        actions: [
          IconButton(
            icon: Icon(Icons.done_all),
            onPressed: _markAllAsRead,
            tooltip: 'تعليم الكل كمقروء'),
        ]),
      body: Consumer<ForumProvider>(
        builder: (context, forumProvider, child) {
          if (forumProvider.notificationsState == LoadingState.loading &&
              forumProvider.notifications.isEmpty) {
            return Center(child: LoadingIndicator());
          } else if (forumProvider.notificationsState == LoadingState.error) {
            return ErrorView(
              message: 'حدث خطأ في تحميل الإشعارات',
              onRetry: _loadData);
          } else if (forumProvider.notificationsState == LoadingState.empty ||
              forumProvider.notifications.isEmpty) {
            return EmptyView(
              message: 'لا توجد إشعارات',
              icon: Icons.notifications_none);
          }

          return RefreshIndicator(
            onRefresh: _loadData,
            child: ListView.builder(
              controller: _scrollController,
              padding: EdgeInsets.all(8),
              itemCount: forumProvider.notifications.length +
                  (forumProvider.hasMoreNotifications ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == forumProvider.notifications.length) {
                  return Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: LoadingIndicator()));
                }

                final notification = forumProvider.notifications[index];
                return _buildNotificationItem(notification);
              }));
        }));
  }

  /// بناء عنصر الإشعار
  Widget _buildNotificationItem(NotificationModel notification) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: 20),
        color: Colors.red,
        child: Icon(
          Icons.delete,
          color: Colors.white)),
      onDismissed: (direction) {
        // حذف الإشعار
        final forumProvider =
            Provider.of<ForumProvider>(context, listen: false);
        forumProvider.deleteNotification(notification.id);
      },
      child: Card(
        margin: EdgeInsets.symmetric(vertical: 4),
        color: notification.isRead ? null : Colors.blue[50],
        child: InkWell(
          onTap: () {
            // تعليم الإشعار كمقروء
            if (!notification.isRead) {
              _markAsRead(notification.id);
            }

            // التنقل إلى الصفحة المناسبة حسب نوع الإشعار
            _navigateToNotificationTarget(notification);
          },
          child: Padding(
            padding: EdgeInsets.all(12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildNotificationIcon(notification),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        notification.title,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16)),
                      SizedBox(height: 4),
                      Text(
                        notification.body,
                        style: TextStyle(
                          fontSize: 14)),
                      SizedBox(height: 4),
                      Text(
                        _formatDate(notification.createdAt),
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 12)),
                    ])),
                if (!notification.isRead)
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle)),
              ])))));
  }

  /// بناء أيقونة الإشعار
  Widget _buildNotificationIcon(NotificationModel notification) {
    IconData iconData;
    Color iconColor;

    switch (notification.type) {
      case NotificationType.topicReply:
        iconData = Icons.forum;
        iconColor = Colors.blue;
        break;
      case NotificationType.postReply:
        iconData = Icons.comment;
        iconColor = Colors.green;
        break;
      case NotificationType.mention:
        iconData = Icons.alternate_email;
        iconColor = Colors.purple;
        break;
      case NotificationType.topicLike:
      case NotificationType.postLike:
        iconData = Icons.favorite;
        iconColor = Colors.red;
        break;
      case NotificationType.follow:
        iconData = Icons.notifications_active;
        iconColor = Colors.orange;
        break;
      case NotificationType.topicReaction:
      case NotificationType.postReaction:
        iconData = Icons.emoji_emotions;
        iconColor = Colors.amber;
        break;
      case NotificationType.topicFeature:
        iconData = Icons.star;
        iconColor = Colors.amber;
        break;
      case NotificationType.topicPin:
        iconData = Icons.push_pin;
        iconColor = Colors.blue;
        break;
      case NotificationType.bestAnswer:
        iconData = Icons.verified;
        iconColor = Colors.green;
        break;
      case NotificationType.topicSolved:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case NotificationType.system:
        iconData = Icons.info;
        iconColor = Colors.blue;
        break;
      default:
        iconData = Icons.notifications;
        iconColor = AppColors.primary;
        break;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withOpacity(0.2),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20));
  }

  /// التنقل إلى هدف الإشعار
  void _navigateToNotificationTarget(NotificationModel notification) {
    // التنقل حسب نوع الإشعار
    if (notification.itemId != null) {
      if (notification.itemType == 'topic') {
        Navigator.pushNamed(
          context,
          '/forum/topic',
          arguments: notification.itemId);
      } else if (notification.itemType == 'category') {
        Navigator.pushNamed(
          context,
          '/forum',
          arguments: notification.itemId);
      }
    } else {
      // إذا لم يكن هناك هدف محدد، لا تفعل شيئاً
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} سنة';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} شهر';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
