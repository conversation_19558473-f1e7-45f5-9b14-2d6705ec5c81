import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/post_model.dart';
import '../../../domain/models/forum/forum_post_model.dart';

/// بطاقة مشاركة المنتدى الحديثة
class ModernPostCard extends StatelessWidget {
  /// مشاركة المنتدى
  final dynamic post;

  /// دالة يتم استدعاؤها عند النقر على زر الإعجاب
  final VoidCallback? onLikeTap;

  /// دالة يتم استدعاؤها عند النقر على زر الرد
  final VoidCallback? onReplyTap;

  /// دالة يتم استدعاؤها عند النقر على زر الإبلاغ
  final VoidCallback? onReportTap;

  /// دالة يتم استدعاؤها عند النقر على زر التعديل
  final VoidCallback? onEditTap;

  /// دالة يتم استدعاؤها عند النقر على زر الحذف
  final VoidCallback? onDeleteTap;

  /// دالة يتم استدعاؤها عند النقر على زر تعيين كأفضل إجابة
  final VoidCallback? onMarkAsBestAnswerTap;

  /// ما إذا كان المستخدم الحالي هو صاحب المشاركة
  final bool isCurrentUser;

  /// ما إذا كان المستخدم الحالي هو صاحب الموضوع
  final bool isTopicOwner;

  /// ما إذا كان المستخدم الحالي قد أعجب بالمشاركة
  final bool isLiked;

  /// ما إذا كانت المشاركة رداً على مشاركة أخرى
  final bool isReply;

  /// مستوى التداخل (للردود المتداخلة)
  final int indentLevel;

  const ModernPostCard({
    super.key,
    required this.post,
    this.onLikeTap,
    this.onReplyTap,
    this.onReportTap,
    this.onEditTap,
    this.onDeleteTap,
    this.onMarkAsBestAnswerTap,
    this.isCurrentUser = false,
    this.isTopicOwner = false,
    this.isLiked = false,
    this.isReply = false,
    this.indentLevel = 0,
  });

  @override
  Widget build(BuildContext context) {
    // التعامل مع نوعي المشاركة (PostModel و ForumPost)
    final PostModel postModel = post is PostModel
        ? post
        : (post is ForumPost ? post.model : null);

    final hasImages = postModel.images != null && postModel.images!.isNotEmpty;
    final hasAttachments = postModel.attachments != null && postModel.attachments!.isNotEmpty;

    return Padding(
      padding: EdgeInsets.only(
        left: 8.0 * indentLevel,
        right: 8.0,
        top: 8.0,
        bottom: 8.0),
      child: Card(
        elevation: 2,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: postModel.isBestAnswer
                ? AppColors.success.withValues(alpha: 0.5)
                : Colors.grey.withValues(alpha: 0.1),
            width: postModel.isBestAnswer ? 2 : 1)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس المشاركة
            _buildPostHeader(context, postModel),

            // محتوى المشاركة
            _buildPostContent(postModel),

            // صور المشاركة
            if (hasImages) _buildPostImages(postModel),

            // مرفقات المشاركة
            if (hasAttachments) _buildPostAttachments(postModel),

            // تذييل المشاركة
            _buildPostFooter(context, postModel),
          ])));
  }

  /// بناء رأس المشاركة
  Widget _buildPostHeader(BuildContext context, PostModel post) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
      decoration: BoxDecoration(
        color: post.isBestAnswer
            ? AppColors.success.withValues(alpha: 0.05)
            : isReply
                ? Colors.grey.withValues(alpha: 0.05)
                : null,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16))),
      child: Row(
        children: [
          // صورة المستخدم
          CircleAvatar(
            radius: 18,
            backgroundColor: AppColors.primary.withValues(alpha: 0.1),
            backgroundImage: post.userImage != null
                ? CachedNetworkImageProvider(post.userImage!)
                : null,
            child: post.userImage == null
                ? Text(
                    post.userName.isNotEmpty ? post.userName[0].toUpperCase() : '?',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold))
                : null),
          const SizedBox(width: 12),

          // معلومات المستخدم والوقت
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      post.userName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14)),
                    if (isCurrentUser) ...[
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4)),
                        child: Text(
                          'أنت',
                          style: TextStyle(
                            fontSize: 10,
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold))),
                    ],
                    if (post.isBestAnswer) ...[
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4)),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.check_circle,
                              color: AppColors.success,
                              size: 10),
                            const SizedBox(width: 2),
                            Text(
                              'أفضل إجابة',
                              style: TextStyle(
                                fontSize: 10,
                                color: AppColors.success,
                                fontWeight: FontWeight.bold)),
                          ])),
                    ],
                  ]),
                const SizedBox(height: 2),
                Text(
                  timeago.format(post.createdAt, locale: 'ar'),
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12)),
              ])),

          // قائمة الخيارات
          if (isCurrentUser || isTopicOwner)
            PopupMenuButton<String>(
              icon: Icon(
                Icons.more_vert,
                color: Colors.grey.shade600,
                size: 20),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12)),
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    onEditTap?.call();
                    break;
                  case 'delete':
                    onDeleteTap?.call();
                    break;
                  case 'best_answer':
                    onMarkAsBestAnswerTap?.call();
                    break;
                  case 'report':
                    onReportTap?.call();
                    break;
                }
              },
              itemBuilder: (context) => [
                if (isCurrentUser) ...[
                  const PopupMenuItem<String>(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 18),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ])),
                  const PopupMenuItem<String>(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 18),
                        SizedBox(width: 8),
                        Text('حذف'),
                      ])),
                ],
                if (isTopicOwner && !post.isBestAnswer && !isCurrentUser)
                  const PopupMenuItem<String>(
                    value: 'best_answer',
                    child: Row(
                      children: [
                        Icon(Icons.check_circle, size: 18),
                        SizedBox(width: 8),
                        Text('تعيين كأفضل إجابة'),
                      ])),
                if (!isCurrentUser)
                  const PopupMenuItem<String>(
                    value: 'report',
                    child: Row(
                      children: [
                        Icon(Icons.flag, size: 18),
                        SizedBox(width: 8),
                        Text('إبلاغ'),
                      ])),
              ]),
        ]));
  }

  /// بناء محتوى المشاركة
  Widget _buildPostContent(PostModel post) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Text(
        post.content,
        style: TextStyle(
          fontSize: 15,
          color: Colors.grey.shade800,
          height: 1.5)));
  }

  /// بناء صور المشاركة
  Widget _buildPostImages(PostModel post) {
    if (post.images == null || post.images!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 180,
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: post.images!.length == 1
            ? _buildSingleImage(post.images!.first)
            : _buildMultipleImages(post.images!)));
  }

  /// بناء صورة واحدة
  Widget _buildSingleImage(String imageUrl) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      width: double.infinity,
      placeholder: (context, url) => Container(
        color: Colors.grey.shade200,
        child: const Center(
          child: CircularProgressIndicator())),
      errorWidget: (context, url, error) => Container(
        color: Colors.grey.shade200,
        child: const Icon(Icons.error)));
  }

  /// بناء صور متعددة
  Widget _buildMultipleImages(List<String> images) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: _buildSingleImage(images.first)),
        if (images.length > 1) ...[
          const SizedBox(width: 4),
          Expanded(
            flex: 2,
            child: Column(
              children: [
                Expanded(
                  child: _buildSingleImage(images[1])),
                if (images.length > 2) ...[
                  const SizedBox(height: 4),
                  Expanded(
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        _buildSingleImage(images[2]),
                        if (images.length > 3)
                          Container(
                            color: Colors.black.withValues(alpha: 0.5),
                            child: Center(
                              child: Text(
                                '+${images.length - 3}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18)))),
                      ])),
                ],
              ])),
        ],
      ]);
  }

  /// بناء مرفقات المشاركة
  Widget _buildPostAttachments(PostModel post) {
    if (post.attachments == null || post.attachments!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          const Text(
            'المرفقات',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: post.attachments!.map((attachment) {
              final String fileName = attachment['name'] ?? 'ملف';
              final String fileType = attachment['type'] ?? '';

              IconData icon;
              if (fileType.contains('pdf')) {
                icon = Icons.picture_as_pdf;
              } else if (fileType.contains('word') || fileType.contains('document')) {
                icon = Icons.description;
              } else if (fileType.contains('excel') || fileType.contains('sheet')) {
                icon = Icons.table_chart;
              } else if (fileType.contains('zip') || fileType.contains('rar')) {
                icon = Icons.folder_zip;
              } else {
                icon = Icons.insert_drive_file;
              }

              return InkWell(
                onTap: () {
                  // فتح الملف
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300)),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        icon,
                        size: 16,
                        color: Colors.grey.shade700),
                      const SizedBox(width: 8),
                      Text(
                        fileName,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade800)),
                    ])));
            }).toList()),
        ]));
  }

  /// بناء تذييل المشاركة
  Widget _buildPostFooter(BuildContext context, PostModel post) {
    return Container(
      padding: const EdgeInsets.fromLTRB(8, 4, 8, 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          // زر الإعجاب
          _buildActionButton(
            icon: isLiked ? Icons.favorite : Icons.favorite_border,
            label: '${post.likesCount}',
            color: isLiked ? Colors.red : null,
            onTap: onLikeTap),

          // زر الرد
          _buildActionButton(
            icon: Icons.reply,
            label: 'رد',
            onTap: onReplyTap),

          // زر الإبلاغ
          if (!isCurrentUser)
            _buildActionButton(
              icon: Icons.flag_outlined,
              label: 'إبلاغ',
              onTap: onReportTap),

          // زر التعديل
          if (isCurrentUser)
            _buildActionButton(
              icon: Icons.edit_outlined,
              label: 'تعديل',
              onTap: onEditTap),

          // زر الحذف
          if (isCurrentUser)
            _buildActionButton(
              icon: Icons.delete_outlined,
              label: 'حذف',
              onTap: onDeleteTap),

          // زر تعيين كأفضل إجابة
          if (isTopicOwner && !post.isBestAnswer && !isCurrentUser)
            _buildActionButton(
              icon: Icons.check_circle_outlined,
              label: 'أفضل إجابة',
              onTap: onMarkAsBestAnswerTap),
        ]));
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    String? label,
    Color? color,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: color ?? Colors.grey.shade600),
            if (label != null) ...[
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  color: color ?? Colors.grey.shade700,
                  fontSize: 12)),
            ],
          ])));
  }
}
