import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'cache_manager.dart';
import 'performance_optimization_service.dart';

/// خدمة قاعدة البيانات المحسنة مع تحسينات الأداء
class EnhancedDatabaseService {
  static final EnhancedDatabaseService _instance = EnhancedDatabaseService._internal();
  factory EnhancedDatabaseService() => _instance;
  EnhancedDatabaseService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AppCacheManager _cacheManager = AppCacheManager();
  final PerformanceOptimizationService _performanceService = PerformanceOptimizationService();
  
  bool _isInitialized = false;
  final Map<String, Timer> _queryTimers = {};

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تفعيل التخزين المؤقت المحلي
      _firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      // تهيئة خدمة تحسين الأداء
      await _performanceService.initialize();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة قاعدة البيانات المحسنة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة قاعدة البيانات: $e');
    }
  }

  /// استعلام محسن مع فلترة وترتيب
  Future<List<T>> optimizedQuery<T>({
    required String collection,
    required T Function(Map<String, dynamic>) fromJson,
    List<QueryFilter>? filters,
    List<QuerySort>? sorts,
    int? limit,
    DocumentSnapshot? startAfter,
    bool useCache = true,
    Duration? cacheDuration,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // إنشاء مفتاح التخزين المؤقت
      final cacheKey = _generateCacheKey(collection, filters, sorts, limit);
      
      // محاولة الحصول على البيانات من التخزين المؤقت
      if (useCache) {
        final cachedData = _cacheManager.getCachedData(cacheKey);
        if (cachedData != null) {
          final List<dynamic> cachedList = cachedData as List<dynamic>;
          return cachedList.map((item) => fromJson(item as Map<String, dynamic>)).toList();
        }
      }

      // بناء الاستعلام
      Query query = _firestore.collection(collection);
      
      // تطبيق الفلاتر
      if (filters != null) {
        for (final filter in filters) {
          query = _applyFilter(query, filter);
        }
      }
      
      // تطبيق الترتيب
      if (sorts != null) {
        for (final sort in sorts) {
          query = query.orderBy(sort.field, descending: sort.descending);
        }
      }
      
      // تطبيق الحد الأقصى
      if (limit != null) {
        query = query.limit(limit);
      }
      
      // تطبيق البداية من نقطة معينة
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      // تنفيذ الاستعلام
      final querySnapshot = await query.get();
      
      // تحويل البيانات
      final results = querySnapshot.docs
          .map((doc) => fromJson({...doc.data() as Map<String, dynamic>, 'id': doc.id}))
          .toList();

      // تخزين النتائج في التخزين المؤقت
      if (useCache && results.isNotEmpty) {
        final dataToCache = querySnapshot.docs
            .map((doc) => {...doc.data() as Map<String, dynamic>, 'id': doc.id})
            .toList();
        
        await _cacheManager.cacheData(
          cacheKey, 
          dataToCache, 
          expiry: cacheDuration ?? const Duration(minutes: 30),
        );
      }

      // تسجيل مقاييس الأداء
      stopwatch.stop();
      _performanceService.recordQuery(cacheKey, stopwatch.elapsedMilliseconds);

      return results;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ خطأ في الاستعلام المحسن: $e');
      rethrow;
    }
  }

  /// استعلام مجمع محسن
  Future<List<List<T>>> batchOptimizedQuery<T>({
    required List<BatchQueryConfig<T>> queries,
    bool useCache = true,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final results = <List<T>>[];
      final futures = <Future<List<T>>>[];

      // تنفيذ الاستعلامات بشكل متوازي
      for (final queryConfig in queries) {
        final future = optimizedQuery<T>(
          collection: queryConfig.collection,
          fromJson: queryConfig.fromJson,
          filters: queryConfig.filters,
          sorts: queryConfig.sorts,
          limit: queryConfig.limit,
          useCache: useCache,
        );
        futures.add(future);
      }

      // انتظار جميع النتائج
      final allResults = await Future.wait(futures);
      results.addAll(allResults);

      stopwatch.stop();
      _performanceService.recordQuery('batch_query', stopwatch.elapsedMilliseconds);

      return results;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ خطأ في الاستعلام المجمع: $e');
      rethrow;
    }
  }

  /// إضافة مستند مع تحسينات
  Future<String> addDocumentOptimized(
    String collection,
    Map<String, dynamic> data, {
    bool invalidateCache = true,
  }) async {
    try {
      // إضافة timestamp
      data['createdAt'] = FieldValue.serverTimestamp();
      data['updatedAt'] = FieldValue.serverTimestamp();

      final docRef = await _firestore.collection(collection).add(data);

      // إلغاء التخزين المؤقت للمجموعة
      if (invalidateCache) {
        await _invalidateCollectionCache(collection);
      }

      return docRef.id;
    } catch (e) {
      debugPrint('❌ خطأ في إضافة المستند: $e');
      rethrow;
    }
  }

  /// تحديث مستند مع تحسينات
  Future<void> updateDocumentOptimized(
    String collection,
    String documentId,
    Map<String, dynamic> data, {
    bool invalidateCache = true,
  }) async {
    try {
      // إضافة timestamp للتحديث
      data['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore.collection(collection).doc(documentId).update(data);

      // إلغاء التخزين المؤقت للمجموعة
      if (invalidateCache) {
        await _invalidateCollectionCache(collection);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث المستند: $e');
      rethrow;
    }
  }

  /// حذف مستند مع تحسينات
  Future<void> deleteDocumentOptimized(
    String collection,
    String documentId, {
    bool invalidateCache = true,
  }) async {
    try {
      await _firestore.collection(collection).doc(documentId).delete();

      // إلغاء التخزين المؤقت للمجموعة
      if (invalidateCache) {
        await _invalidateCollectionCache(collection);
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف المستند: $e');
      rethrow;
    }
  }

  /// تطبيق فلتر على الاستعلام
  Query _applyFilter(Query query, QueryFilter filter) {
    switch (filter.operator) {
      case FilterOperator.isEqualTo:
        return query.where(filter.field, isEqualTo: filter.value);
      case FilterOperator.isNotEqualTo:
        return query.where(filter.field, isNotEqualTo: filter.value);
      case FilterOperator.isLessThan:
        return query.where(filter.field, isLessThan: filter.value);
      case FilterOperator.isLessThanOrEqualTo:
        return query.where(filter.field, isLessThanOrEqualTo: filter.value);
      case FilterOperator.isGreaterThan:
        return query.where(filter.field, isGreaterThan: filter.value);
      case FilterOperator.isGreaterThanOrEqualTo:
        return query.where(filter.field, isGreaterThanOrEqualTo: filter.value);
      case FilterOperator.arrayContains:
        return query.where(filter.field, arrayContains: filter.value);
      case FilterOperator.arrayContainsAny:
        return query.where(filter.field, arrayContainsAny: filter.value);
      case FilterOperator.whereIn:
        return query.where(filter.field, whereIn: filter.value);
      case FilterOperator.whereNotIn:
        return query.where(filter.field, whereNotIn: filter.value);
      case FilterOperator.isNull:
        return query.where(filter.field, isNull: true);
      case FilterOperator.isNotNull:
        return query.where(filter.field, isNull: false);
    }
  }

  /// إنشاء مفتاح التخزين المؤقت
  String _generateCacheKey(
    String collection,
    List<QueryFilter>? filters,
    List<QuerySort>? sorts,
    int? limit,
  ) {
    final buffer = StringBuffer(collection);
    
    if (filters != null) {
      for (final filter in filters) {
        buffer.write('_${filter.field}_${filter.operator.name}_${filter.value}');
      }
    }
    
    if (sorts != null) {
      for (final sort in sorts) {
        buffer.write('_sort_${sort.field}_${sort.descending}');
      }
    }
    
    if (limit != null) {
      buffer.write('_limit_$limit');
    }
    
    return buffer.toString();
  }

  /// إلغاء التخزين المؤقت للمجموعة
  Future<void> _invalidateCollectionCache(String collection) async {
    await _cacheManager.clearCacheByPattern(collection);
  }

  /// الحصول على إحصائيات الأداء
  Map<String, dynamic> getPerformanceStats() {
    return _performanceService.getPerformanceStats();
  }

  /// تنظيف الموارد
  void dispose() {
    _queryTimers.forEach((key, timer) => timer.cancel());
    _queryTimers.clear();
    _performanceService.dispose();
  }
}

/// فلتر الاستعلام
class QueryFilter {
  final String field;
  final FilterOperator operator;
  final dynamic value;

  QueryFilter({
    required this.field,
    required this.operator,
    required this.value,
  });
}

/// عامل الفلترة
enum FilterOperator {
  isEqualTo,
  isNotEqualTo,
  isLessThan,
  isLessThanOrEqualTo,
  isGreaterThan,
  isGreaterThanOrEqualTo,
  arrayContains,
  arrayContainsAny,
  whereIn,
  whereNotIn,
  isNull,
  isNotNull,
}

/// ترتيب الاستعلام
class QuerySort {
  final String field;
  final bool descending;

  QuerySort({
    required this.field,
    this.descending = false,
  });
}

/// تكوين الاستعلام المجمع
class BatchQueryConfig<T> {
  final String collection;
  final T Function(Map<String, dynamic>) fromJson;
  final List<QueryFilter>? filters;
  final List<QuerySort>? sorts;
  final int? limit;

  BatchQueryConfig({
    required this.collection,
    required this.fromJson,
    this.filters,
    this.sorts,
    this.limit,
  });
}
