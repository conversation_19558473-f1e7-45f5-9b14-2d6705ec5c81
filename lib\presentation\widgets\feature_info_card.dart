// lib/presentation/widgets/feature_info_card.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/theme/app_colors.dart';

/// بطاقة معلومات الميزة
/// تعرض معلومات تفصيلية عن كل ميزة إضافية
class FeatureInfoCard extends StatelessWidget {
  /// عنوان الميزة
  final String title;
  
  /// وصف الميزة
  final String description;
  
  /// أيقونة الميزة
  final IconData icon;
  
  /// سعر الميزة
  final double price;
  
  /// ما إذا كانت الميزة محددة
  final bool isSelected;
  
  /// دالة يتم استدعاؤها عند تغيير حالة الميزة
  final ValueChanged<bool> onChanged;
  
  /// مدة الميزة (إذا كانت محددة)
  final String? duration;
  
  /// ما إذا كانت الميزة موصى بها
  final bool isRecommended;

  const FeatureInfoCard({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.price,
    required this.isSelected,
    required this.onChanged,
    this.duration,
    this.isRecommended = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isSelected ? AppColors.primary : Colors.transparent,
          width: 2)),
      child: InkWell(
        onTap: () => onChanged(!isSelected),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // أيقونة وعنوان الميزة
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.primary
                          : AppColors.primary.withAlpha(30),
                      shape: BoxShape.circle),
                    child: Icon(
                      icon,
                      color: isSelected ? Colors.white : AppColors.primary,
                      size: 24)),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                title,
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: isSelected
                                      ? AppColors.primary
                                      : Colors.grey.shade800))),
                            Switch(
                              value: isSelected,
                              onChanged: onChanged,
                              activeColor: AppColors.primary),
                          ]),
                        if (isRecommended)
                          Container(
                            margin: const EdgeInsets.only(top: 4),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.amber.shade100,
                              borderRadius: BorderRadius.circular(12)),
                            child: Text(
                              "موصى به",
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.amber.shade800))),
                      ])),
                ]),
              
              const SizedBox(height: 16),
              
              // وصف الميزة
              Text(
                description,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade700)),
              
              const SizedBox(height: 16),
              
              // السعر والمدة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "${price.toStringAsFixed(3)} د.ك",
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary)),
                  if (duration != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.grey.shade300)),
                      child: Text(
                        duration!,
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade800))),
                ]),
              
              if (isSelected) ...[
                const SizedBox(height: 16),
                
                // شارة التفعيل
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(20)),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green.shade700,
                        size: 16),
                      const SizedBox(width: 4),
                      Text(
                        "تم التفعيل",
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700)),
                    ])),
              ],
            ]))));
  }
}
