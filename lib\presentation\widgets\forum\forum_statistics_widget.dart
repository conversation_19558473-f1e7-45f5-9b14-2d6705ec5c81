import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/theme/app_colors.dart';
import '../../providers/forum_provider.dart';

/// ويدجت إحصائيات المنتدى
class ForumStatisticsWidget extends StatefulWidget {
  /// ما إذا كان مصغر
  final bool isCompact;

  /// دالة يتم استدعاؤها عند النقر على الويدجت
  final VoidCallback? onTap;

  const ForumStatisticsWidget({
    super.key,
    this.isCompact = false,
    this.onTap,
  });

  @override
  State<ForumStatisticsWidget> createState() => _ForumStatisticsWidgetState();
}

class _ForumStatisticsWidgetState extends State<ForumStatisticsWidget> {
  bool _isLoading = true;
  Map<String, dynamic> _statistics = {};
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  /// تحميل إحصائيات المنتدى
  Future<void> _loadStatistics() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);

      // طباعة حالة الإحصائيات قبل التحميل
      debugPrint('Before loading - Forum statistics: ${forumProvider.forumStatistics.length} items');

      // استخدام fetchForumStatistics لجلب البيانات
      await forumProvider.fetchForumStatistics();

      // طباعة حالة الإحصائيات بعد التحميل
      debugPrint('After loading - Forum statistics: ${forumProvider.forumStatistics.length} items');
      debugPrint('Forum statistics state: ${forumProvider.forumStatisticsState}');

      // الحصول على البيانات من المزود
      setState(() {
        _statistics = forumProvider.forumStatistics;
        _isLoading = false;

        // التحقق من حالة البيانات
        if (_statistics.isEmpty) {
          _errorMessage = 'لا توجد إحصائيات متاحة';
        }
      });
    } catch (e) {
      debugPrint('Error loading forum statistics: $e');
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
        _statistics = {}; // استخدام بيانات فارغة في حالة الخطأ
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // استخدام Consumer للاستماع للتغييرات في ForumProvider
    return Consumer<ForumProvider>(
      builder: (context, forumProvider, child) {
        // تحديث البيانات من المزود
        if (forumProvider.forumStatistics.isNotEmpty) {
          _statistics = forumProvider.forumStatistics;
          _isLoading = false;
        } else if (forumProvider.forumStatisticsState == LoadingState.loading) {
          _isLoading = true;
        } else if (forumProvider.forumStatisticsState == LoadingState.error ||
                  forumProvider.forumStatisticsState == LoadingState.empty) {
          _isLoading = false;
          _errorMessage = 'لا توجد إحصائيات متاحة';
        }

        // طباعة حالة الإحصائيات للتصحيح
        debugPrint('Forum statistics: ${_statistics.length} items, isLoading: $_isLoading, state: ${forumProvider.forumStatisticsState}');

        return InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2)),
              ]),
            child: _buildContent()));
      });
  }

  /// بناء محتوى الويدجت
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ: $_errorMessage',
              style: const TextStyle(
                color: Colors.red),
              textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadStatistics,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white)),
          ]));
    }

    if (_statistics.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد إحصائيات متاحة',
          style: TextStyle(
            color: Colors.grey)));
    }

    return widget.isCompact ? _buildCompactContent() : _buildFullContent();
  }

  /// بناء المحتوى المصغر
  Widget _buildCompactContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات المنتدى',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              icon: Icons.category,
              value: _statistics['categoriesCount']?.toString() ?? '0',
              label: 'فئة',
              color: Colors.green, // تغيير من الأزرق إلى الأخضر
            ),
            _buildStatItem(
              icon: Icons.topic,
              value: _statistics['topicsCount']?.toString() ?? '0',
              label: 'موضوع',
              color: AppColors.primary),
            _buildStatItem(
              icon: Icons.comment,
              value: _statistics['postsCount']?.toString() ?? '0',
              label: 'مشاركة',
              color: Colors.green),
            _buildStatItem(
              icon: Icons.person,
              value: _statistics['usersCount']?.toString() ?? '0',
              label: 'مستخدم',
              color: Colors.orange),
          ]),
      ]);
  }

  /// بناء المحتوى الكامل
  Widget _buildFullContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات المنتدى',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              icon: Icons.category,
              value: _statistics['categoriesCount']?.toString() ?? '0',
              label: 'فئة',
              color: Colors.blue),
            _buildStatItem(
              icon: Icons.topic,
              value: _statistics['topicsCount']?.toString() ?? '0',
              label: 'موضوع',
              color: AppColors.primary),
            _buildStatItem(
              icon: Icons.comment,
              value: _statistics['postsCount']?.toString() ?? '0',
              label: 'مشاركة',
              color: Colors.green),
            _buildStatItem(
              icon: Icons.person,
              value: _statistics['usersCount']?.toString() ?? '0',
              label: 'مستخدم',
              color: Colors.orange),
          ]),
        const SizedBox(height: 24),
        const Text(
          'نشاط المنتدى',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        SizedBox(
          height: 200,
          child: _buildActivityChart()),
        const SizedBox(height: 24),
        const Text(
          'توزيع المواضيع حسب الفئات',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        SizedBox(
          height: 200,
          child: _buildCategoriesChart()),
      ]);
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: widget.isCompact ? 20 : 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: widget.isCompact ? 16 : 18,
            fontWeight: FontWeight.bold)),
        Text(
          label,
          style: TextStyle(
            fontSize: widget.isCompact ? 12 : 14,
            color: Colors.grey.shade600)),
      ]);
  }

  /// بناء مخطط النشاط
  Widget _buildActivityChart() {
    final activityData = _statistics['activityData'] as Map<String, dynamic>? ?? {};
    final days = activityData.keys.toList();
    final values = days.map((day) => activityData[day] as int? ?? 0).toList();

    if (values.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات نشاط متاحة',
          style: TextStyle(
            color: Colors.grey)));
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: 1,
          verticalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade200,
              strokeWidth: 1);
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey.shade200,
              strokeWidth: 1);
          }),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < days.length) {
                  return SideTitleWidget(
                    space: 4,
                    angle: 0,
                    meta: meta,
                    child: Text(
                      days[value.toInt()],
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12)));
                }
                return const SizedBox();
              })),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 1,
              getTitlesWidget: (value, meta) {
                return SideTitleWidget(
                  space: 4,
                  angle: 0,
                  meta: meta,
                  child: Text(
                    value.toInt().toString(),
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12)));
              },
              reservedSize: 40))),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.shade300)),
        minX: 0,
        maxX: days.length.toDouble() - 1,
        minY: 0,
        maxY: values.isEmpty ? 10 : (values.reduce((a, b) => a > b ? a : b) + 1).toDouble(),
        lineBarsData: [
          LineChartBarData(
            spots: List.generate(
              days.length,
              (index) => FlSpot(index.toDouble(), values[index].toDouble())),
            isCurved: true,
            color: AppColors.primary,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: AppColors.primary,
                  strokeWidth: 2,
                  strokeColor: Colors.white);
              }),
            belowBarData: BarAreaData(
              show: true,
              color: Color.fromRGBO(
                AppColors.primary.r.toInt(),
                AppColors.primary.g.toInt(),
                AppColors.primary.b.toInt(),
                0.2))),
        ]));
  }

  /// بناء مخطط الفئات
  Widget _buildCategoriesChart() {
    final categoriesData = _statistics['categoriesData'] as Map<String, dynamic>? ?? {};
    final categories = categoriesData.keys.toList();
    final values = categories.map((category) => categoriesData[category] as int? ?? 0).toList();

    if (values.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات فئات متاحة',
          style: TextStyle(
            color: Colors.grey)));
    }

    return PieChart(
      PieChartData(
        sectionsSpace: 2,
        centerSpaceRadius: 40,
        sections: List.generate(
          categories.length,
          (index) {
            final color = [
              Colors.green, // تغيير من الأزرق إلى الأخضر
              AppColors.primary,
              Colors.green.shade600,
              Colors.orange,
              Colors.purple,
              Colors.red,
              Colors.teal,
              Colors.amber,
            ][index % 8];

            return PieChartSectionData(
              color: color,
              value: values[index].toDouble(),
              title: '${values[index]}',
              radius: 100,
              titleStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white));
          })));
  }
}
