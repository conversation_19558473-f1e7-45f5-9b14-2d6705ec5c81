import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';

/// شريط التطبيق المخصص
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// عنوان الشريط
  final String title;

  /// ما إذا كان يجب عرض زر الرجوع
  final bool showBackButton;

  /// مكون مخصص للعرض في بداية الشريط
  final Widget? leading;

  /// قائمة الإجراءات في نهاية الشريط
  final List<Widget>? actions;

  /// ارتفاع الشريط
  final double height;

  /// لون خلفية الشريط
  final Color? backgroundColor;

  /// لون النص والأيقونات
  final Color? foregroundColor;

  /// مكون في أسفل الشريط
  final PreferredSizeWidget? bottom;

  const CustomAppBar({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.leading,
    this.actions,
    this.height = kToolbarHeight,
    this.backgroundColor,
    this.foregroundColor,
    this.bottom,
  });

  @override
  Size get preferredSize {
    // إضافة ارتفاع المساحة الآمنة إلى الارتفاع الإجمالي
    const double statusBarHeight = 24.0; // تقدير تقريبي
    return Size.fromHeight(
      height + statusBarHeight + (bottom?.preferredSize.height ?? 0.0)
    );
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);

    return Container(
      // إضافة المساحة الآمنة من الأعلى
      padding: EdgeInsets.only(top: mediaQuery.padding.top),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.primary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: AppBar(
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: foregroundColor ?? Colors.white)),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: leading ??
            (showBackButton
                ? IconButton(
                    icon: Icon(
                      Icons.arrow_back,
                      color: foregroundColor ?? Colors.white),
                    onPressed: () => Navigator.of(context).pop())
                : null),
        actions: actions,
        bottom: bottom));
  }
}
