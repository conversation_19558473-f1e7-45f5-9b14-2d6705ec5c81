import 'package:flutter/material.dart';

/// ألوان التطبيق الموحدة
/// تم اختيار مجموعة ألوان تعبر عن موضوع منصة عرض العقارات
/// مع التركيز على الألوان الأرضية والطبيعية التي تعكس مفهوم العقارات
class AppColors {
  // الألوان الرئيسية

  /// اللون الرئيسي - أخضر زيتوني داكن يعبر عن الاستقرار والثقة في مجال العقارات
  static const Color primary = Color(0xFF2E7D32);

  /// اللون الرئيسي الفاتح - درجة أفتح من اللون الأخضر الزيتوني
  static const Color primaryLight = Color(0xFF4CAF50);

  /// اللون الرئيسي الداكن - درجة أغمق من اللون الأخضر الزيتوني
  static const Color primaryDark = Color(0xFF1B5E20);

  /// اللون الثانوي - لون بني فاتح يعبر عن الأرض والبناء
  static const Color secondary = Color(0xFF8D6E63);

  /// اللون الثانوي الفاتح - درجة أفتح من اللون البني
  static const Color secondaryLight = Color(0xFFBCAAA4);

  /// اللون الثانوي الداكن - درجة أغمق من اللون البني
  static const Color secondaryDark = Color(0xFF5D4037);

  // ألوان الخلفية

  /// لون خلفية التطبيق الرئيسية - أبيض مائل للبيج الفاتح جداً
  static const Color background = Color(0xFFFAFAFA);

  /// لون خلفية البطاقات - أبيض نقي
  static const Color cardBackground = Color(0xFFFFFFFF);

  /// لون خلفية الشاشات الفرعية - درجة من البيج الفاتح
  static const Color secondaryBackground = Color(0xFFF5F5F5);

  /// لون خلفية الوضع الداكن - رمادي داكن مائل للبني
  static const Color darkBackground = Color(0xFF263238);

  /// لون خلفية البطاقات في الوضع الداكن
  static const Color darkCardBackground = Color(0xFF37474F);

  // ألوان النصوص

  /// لون النص الرئيسي - أسود مائل للرمادي الداكن
  static const Color textPrimary = Color(0xFF212121);

  /// لون النص الثانوي - رمادي داكن
  static const Color textSecondary = Color(0xFF757575);

  /// لون النص الخفيف - رمادي متوسط
  static const Color textLight = Color(0xFF9E9E9E);

  /// لون النص على الخلفيات الداكنة - أبيض مائل للرمادي الفاتح
  static const Color textOnDark = Color(0xFFEEEEEE);

  /// لون النص الثانوي على الخلفيات الداكنة
  static const Color textSecondaryOnDark = Color(0xFFBDBDBD);

  // ألوان العناصر

  /// لون الأزرار الرئيسية
  static const Color buttonPrimary = primary;

  /// لون الأزرار الثانوية
  static const Color buttonSecondary = secondary;

  /// لون الأزرار المعطلة
  static const Color buttonDisabled = Color(0xFFBDBDBD);

  /// لون الحدود - أفتح من السابق لتحسين المظهر
  static const Color border = Color(0xFFEEEEEE);

  /// لون الحدود الداكنة - أفتح قليلاً من السابق
  static const Color borderDark = Color(0xFFBDBDBD);

  // ألوان الحالة

  /// لون النجاح - أخضر
  static const Color success = Color(0xFF4CAF50);

  /// لون الخطأ - أحمر
  static const Color error = Color(0xFFD32F2F);

  /// لون التحذير - برتقالي
  static const Color warning = Color(0xFFFFA000);

  /// لون المعلومات - أخضر فاتح
  static const Color info = Color(0xFF66BB6A);

  // ألوان متدرجة

  /// تدرج اللون الرئيسي - من الأخضر الفاتح إلى الأخضر الداكن
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryLight, primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight);

  /// تدرج اللون الثانوي - من البني الفاتح إلى البني الداكن
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondaryLight, secondary, secondaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight);

  /// تدرج خلفية التطبيق - من الأبيض إلى البيج الفاتح
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [Colors.white, Color(0xFFF5F5F5)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter);

  /// تدرج خلفية الوضع الداكن
  static const LinearGradient darkBackgroundGradient = LinearGradient(
    colors: [Color(0xFF263238), Color(0xFF1E272C)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter);

  // ألوان خاصة بالعقارات

  /// لون عقارات للبيع
  static const Color forSale = Color(0xFF2E7D32); // أخضر

  /// لون عقارات للإيجار
  static const Color forRent = Color(0xFF4CAF50); // أخضر

  /// لون عقارات للبدل
  static const Color forExchange = Color(0xFF8D6E63); // بني

  /// لون عقارات دولية
  static const Color international = Color(0xFF7B1FA2); // بنفسجي

  /// لون مكاتب عقارية
  static const Color offices = Color(0xFF0097A7); // أزرق مخضر

  // ألوان الفئات

  /// لون فئة الشقق
  static const Color apartment = Color(0xFF00897B); // أخضر مزرق

  /// لون فئة الفلل
  static const Color villa = Color(0xFF5D4037); // بني داكن

  /// لون فئة الأراضي
  static const Color land = Color(0xFF558B2F); // أخضر زيتوني

  /// لون فئة المكاتب التجارية
  static const Color commercial = Color(0xFF388E3C); // أخضر

  /// لون فئة المستودعات
  static const Color warehouse = Color(0xFF6D4C41); // بني

  // ألوان برتقالية متناسقة مع الألوان الرئيسية للطلبات العقارية

  /// اللون البرتقالي الرئيسي للطلبات العقارية
  static const Color primaryOrange = Color(0xFFFF6B35);

  /// اللون البرتقالي الفاتح
  static const Color lightOrange = Color(0xFFFFB085);

  /// اللون البرتقالي الداكن
  static const Color darkOrange = Color(0xFFE55A2B);

  /// اللون البرتقالي المساعد
  static const Color orangeAccent = Color(0xFFFFA366);

  /// خلفية برتقالية فاتحة جداً
  static const Color orangeBackground = Color(0xFFFFF4F0);

  /// خلفية برتقالية فاتحة للبطاقات
  static const Color orangeCardBackground = Color(0xFFFFF8F5);

  /// تدرج برتقالي للطلبات العقارية
  static const LinearGradient orangeGradient = LinearGradient(
    colors: [Color(0xFFFF8A50), Color(0xFFFF6B35)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight);

  /// تدرج برتقالي فاتح للخلفيات
  static const LinearGradient lightOrangeGradient = LinearGradient(
    colors: [Color(0xFFFFF8F5), Color(0xFFFFF4F0)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter);

  /// تدرج مختلط أخضر-برتقالي للعناصر المميزة
  static const LinearGradient greenOrangeGradient = LinearGradient(
    colors: [primary, primaryOrange],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight);
}
