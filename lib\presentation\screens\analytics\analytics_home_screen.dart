import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/enums/analysis_type.dart';
import '../../../domain/repositories/analytics_repository.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/error_display_widget.dart';
import 'market_analysis_screen.dart';
import 'price_estimation_screen.dart';
import 'market_prediction_screen.dart';

/// شاشة التحليلات الرئيسية
class AnalyticsHomeScreen extends StatefulWidget {
  /// إنشاء شاشة التحليلات الرئيسية
  const AnalyticsHomeScreen({super.key});

  @override
  State<AnalyticsHomeScreen> createState() => _AnalyticsHomeScreenState();
}

class _AnalyticsHomeScreenState extends State<AnalyticsHomeScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  Map<String, dynamic>? _marketIndicators;
  List<Map<String, dynamic>>? _topInvestmentAreas;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final analyticsRepository = context.read<AnalyticsRepository>();

      // تحميل مؤشرات السوق
      final marketIndicators = await analyticsRepository.getMarketIndicators();

      // تحميل أفضل المناطق للاستثمار
      final topInvestmentAreas =
          await analyticsRepository.getTopInvestmentAreas();

      setState(() {
        _marketIndicators = marketIndicators;
        _topInvestmentAreas = topInvestmentAreas;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التحليلات والذكاء الاصطناعي'),
        centerTitle: true),
      body: _buildContent());
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator());
    }

    if (_errorMessage != null) {
      return ErrorDisplayWidget(
        message: _errorMessage!,
        onRetry: _loadData);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقات الميزات الرئيسية
          _buildFeatureCards(),
          const SizedBox(height: 24),

          // مؤشرات السوق
          _buildMarketIndicators(),
          const SizedBox(height: 24),

          // أفضل المناطق للاستثمار
          _buildTopInvestmentAreas(),
          const SizedBox(height: 24),

          // رؤى الذكاء الاصطناعي
          _buildAIInsights(),
        ]));
  }

  /// بناء بطاقات الميزات الرئيسية
  Widget _buildFeatureCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الميزات الرئيسية',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            // تحليل السوق
            _buildFeatureCard(
              'تحليل السوق',
              'تحليلات شاملة لسوق العقارات',
              Icons.analytics_outlined,
              Colors.blue,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MarketAnalysisScreen()))),

            // تقدير الأسعار
            _buildFeatureCard(
              'تقدير الأسعار',
              'تقدير أسعار العقارات بدقة',
              Icons.monetization_on_outlined,
              Colors.green,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PriceEstimationScreen()))),

            // التنبؤات المستقبلية
            _buildFeatureCard(
              'التنبؤات المستقبلية',
              'توقعات مستقبلية لسوق العقارات',
              Icons.trending_up,
              Colors.purple,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MarketPredictionScreen()))),

            // المساعد الذكي
            _buildFeatureCard(
              'المساعد الذكي',
              'مساعد عقاري ذكي بالذكاء الاصطناعي',
              Icons.smart_toy_outlined,
              Colors.orange,
              _showAIAssistantDialog),
          ]),
      ]);
  }

  /// بناء بطاقة ميزة
  Widget _buildFeatureCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withAlpha(30),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(
                  icon,
                  color: color,
                  size: 24)),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600]),
                maxLines: 2,
                overflow: TextOverflow.ellipsis),
            ]))));
  }

  /// بناء مؤشرات السوق
  Widget _buildMarketIndicators() {
    if (_marketIndicators == null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'مؤشرات السوق',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            TextButton(
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MarketAnalysisScreen(
                    initialType: AnalysisType.price))),
              child: const Text('عرض المزيد')),
          ]),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            // مؤشر الأسعار
            _buildIndicatorCard(
              'مؤشر الأسعار',
              _marketIndicators!['priceIndex']?.toString() ?? 'غير متاح',
              _marketIndicators!['priceChangePercentage']?.toString() ??
                  'غير متاح',
              Icons.monetization_on_outlined,
              Colors.blue),

            // نسبة العرض والطلب
            _buildIndicatorCard(
              'العرض/الطلب',
              _marketIndicators!['supplyDemandRatio']?.toString() ?? 'غير متاح',
              null,
              Icons.compare_arrows,
              Colors.green),

            // متوسط أيام العرض
            _buildIndicatorCard(
              'أيام العرض',
              _marketIndicators!['averageDaysOnMarket']?.toString() ??
                  'غير متاح',
              null,
              Icons.calendar_today_outlined,
              Colors.orange),

            // العائد الإيجاري
            _buildIndicatorCard(
              'العائد الإيجاري',
              '${_marketIndicators!['rentalYield']?.toString() ?? 'غير متاح'}%',
              null,
              Icons.trending_up,
              Colors.purple),
          ]),
      ]);
  }

  /// بناء بطاقة مؤشر
  Widget _buildIndicatorCard(
    String title,
    String value,
    String? changePercentage,
    IconData icon,
    Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 16),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600])),
              ]),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color)),
            if (changePercentage != null)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  '$changePercentage%',
                  style: TextStyle(
                    fontSize: 12,
                    color: double.tryParse(changePercentage)?.isNegative == true
                        ? Colors.red
                        : Colors.green))),
          ])));
  }

  /// بناء أفضل المناطق للاستثمار
  Widget _buildTopInvestmentAreas() {
    if (_topInvestmentAreas == null || _topInvestmentAreas!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'أفضل المناطق للاستثمار',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            TextButton(
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MarketAnalysisScreen(
                    initialType: AnalysisType.areas))),
              child: const Text('عرض المزيد')),
          ]),
        const SizedBox(height: 16),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _topInvestmentAreas!.length,
            itemBuilder: (context, index) {
              final area = _topInvestmentAreas![index];
              return _buildAreaCard(
                area['name'] as String,
                area['return'] as double,
                area['growth'] as double,
                area['risk'] as double);
            })),
      ]);
  }

  /// بناء بطاقة منطقة
  Widget _buildAreaCard(
    String name,
    double returnRate,
    double growthRate,
    double risk) {
    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              _buildAreaStat('العائد المتوقع',
                  '${returnRate.toStringAsFixed(1)}%', Colors.green),
              const SizedBox(height: 8),
              _buildAreaStat('معدل النمو', '${growthRate.toStringAsFixed(1)}%',
                  Colors.blue),
              const SizedBox(height: 8),
              _buildAreaStat(
                'مستوى المخاطرة',
                _getRiskLevel(risk),
                _getRiskColor(risk)),
            ]))));
  }

  /// بناء إحصائية منطقة
  Widget _buildAreaStat(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600])),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color)),
      ]);
  }

  /// الحصول على مستوى المخاطرة
  String _getRiskLevel(double risk) {
    if (risk < 0.3) {
      return 'منخفض';
    } else if (risk < 0.7) {
      return 'متوسط';
    } else {
      return 'مرتفع';
    }
  }

  /// الحصول على لون المخاطرة
  Color _getRiskColor(double risk) {
    if (risk < 0.3) {
      return Colors.green;
    } else if (risk < 0.7) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  /// بناء رؤى الذكاء الاصطناعي
  Widget _buildAIInsights() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'رؤى الذكاء الاصطناعي',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.purple.withAlpha(30),
                        borderRadius: BorderRadius.circular(8)),
                      child: const Icon(
                        Icons.psychology_outlined,
                        color: Colors.purple,
                        size: 24)),
                    const SizedBox(width: 12),
                    const Text(
                      'تحليل ذكي للسوق',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                  ]),
                const SizedBox(height: 16),
                const Text(
                  'يشهد سوق العقارات اتجاهاً صعودياً معتدلاً مع زيادة في الطلب على العقارات السكنية في المناطق الرئيسية. من المتوقع استمرار هذا الاتجاه خلال الأشهر القادمة مع استقرار أسعار الفائدة.',
                  style: TextStyle(fontSize: 14)),
                const SizedBox(height: 16),
                const Text(
                  'توصيات:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                _buildAIRecommendation(
                  'الاستثمار في العقارات السكنية في المناطق الناشئة للاستفادة من النمو المتوقع.'),
                _buildAIRecommendation(
                  'التركيز على العقارات ذات العائد الإيجاري المرتفع للاستثمار طويل الأجل.'),
                _buildAIRecommendation(
                  'مراقبة تطورات السوق خلال الربع القادم لاتخاذ قرارات استثمارية مدروسة.'),
                const SizedBox(height: 16),
                Center(
                  child: ElevatedButton(
                    onPressed: _showAIAssistantDialog,
                    child: const Text('استشارة المساعد الذكي'))),
              ]))),
      ]);
  }

  /// بناء توصية الذكاء الاصطناعي
  Widget _buildAIRecommendation(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.check_circle_outline,
            size: 18,
            color: Colors.green),
          const SizedBox(width: 8),
          Expanded(
            child: Text(text)),
        ]));
  }

  /// عرض حوار المساعد الذكي
  void _showAIAssistantDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(
                Icons.smart_toy_outlined,
                color: Colors.purple),
              SizedBox(width: 8),
              Text('المساعد العقاري الذكي'),
            ]),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'مرحباً! أنا المساعد العقاري الذكي. كيف يمكنني مساعدتك اليوم؟',
                style: TextStyle(fontSize: 14)),
              SizedBox(height: 16),
              Text(
                'يمكنني مساعدتك في:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('• تحليل سوق العقارات'),
              Text('• تقدير أسعار العقارات'),
              Text('• اقتراح فرص استثمارية'),
              Text('• تقديم نصائح للشراء أو البيع'),
              Text('• الإجابة على أسئلتك العقارية'),
            ]),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق')),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // هنا يمكن فتح واجهة المحادثة مع المساعد الذكي
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('سيتم إطلاق هذه الميزة قريباً')));
              },
              child: const Text('بدء محادثة')),
          ]);
      });
  }
}
