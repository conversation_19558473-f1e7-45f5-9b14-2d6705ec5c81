import 'estate_base.dart';

/// نموذج للأراضي
class LandEstate extends EstateBase {
  // خصائص الأراضي
  final double? area; // المساحة بالمتر المربع
  final String? landType; // نوع الأرض (سكني، تجاري، زراعي)
  final String? streetWidth; // عرض الشارع
  final bool? hasWaterAndElectricity; // متوفر ماء وكهرباء
  final String? landShape; // شكل الأرض (مستطيل، مربع، غير منتظم)
  final int? numberOfStreets; // عدد الشوارع المحيطة
  final String? facingDirection; // الاتجاه (شمال، جنوب، شرق، غرب)
  final bool? isCornerLand; // أرض زاوية

  // معلومات قانونية
  final String? landNumber; // رقم الأرض
  final String? planNumber; // رقم المخطط
  final String? deedNumber; // رقم الصك
  final DateTime? deedDate; // تاريخ الصك
  final String? landUseType; // نوع استخدام الأرض (سكني، تجاري، صناعي)
  final bool? hasMunicipality; // لديها رخصة بلدية

  // معلومات إضافية
  final double? streetLength; // طول الواجهة على الشارع
  final double? depth; // عمق الأرض
  final double? buildingRatio; // نسبة البناء المسموحة
  final int? floorRatio; // عدد الطوابق المسموحة
  final bool? isFilledAndLeveled; // مردومة ومسواة
  final bool? hasBoundaryWall; // لديها سور
  final String? soilType; // نوع التربة
  final double? elevation; // الارتفاع عن مستوى سطح البحر

  // معلومات الإيجار (إذا كان للإيجار)
  final String? rentalPeriod; // فترة الإيجار (شهري، سنوي)
  final double? insuranceAmount; // مبلغ التأمين
  final double? commissionAmount; // مبلغ العمولة
  final String? paymentMethod; // طريقة الدفع (كاش، أقساط)
  final String? contractType; // نوع العقد (تمليك، إيجار)

  // معلومات التطوير
  final bool? hasDevelopmentPlans; // لديها مخططات تطوير
  final String? developmentPlanUrl; // رابط مخطط التطوير
  final String? developmentDescription; // وصف التطوير المقترح
  final double? estimatedDevelopmentCost; // تكلفة التطوير المقدرة

  const LandEstate({
    required super.id,
    required super.title,
    required super.description,
    required super.price,
    required super.location,
    required super.photoUrls,
    required super.isFeatured,
    required super.status,
    super.governorate,
    super.city,
    super.district,
    super.block,
    super.latitude,
    super.longitude,
    super.shareLocation,
    required super.mainCategory,
    super.subCategory,
    required super.ownerId,
    super.advertiserName,
    super.advertiserPhone,
    super.advertiserImage,
    super.advertiserType,
    super.advertiserJoinDate,
    super.advertiserAdsCount,
    super.hidePhone,
    super.extraPhones,
    required super.createdAt,
    super.updatedAt,
    super.startDate,
    super.endDate,
    super.viewsCount,
    super.favoritesCount,
    super.contactCount,
    super.subscriptionPlan,
    super.autoRepublish,
    super.isPinned,
    super.isPromoted,
    super.isVIP,
    super.isVerified,
    super.originalEstateId,
    super.isOriginal,
    super.copiedBy,
    super.floorPlanUrl,
    super.virtualTourUrl,
    super.videoUrl,
    super.documents,
    this.area,
    this.landType,
    this.streetWidth,
    this.hasWaterAndElectricity,
    this.landShape,
    this.numberOfStreets,
    this.facingDirection,
    this.isCornerLand,
    this.landNumber,
    this.planNumber,
    this.deedNumber,
    this.deedDate,
    this.landUseType,
    this.hasMunicipality,
    this.streetLength,
    this.depth,
    this.buildingRatio,
    this.floorRatio,
    this.isFilledAndLeveled,
    this.hasBoundaryWall,
    this.soilType,
    this.elevation,
    this.rentalPeriod,
    this.insuranceAmount,
    this.commissionAmount,
    this.paymentMethod,
    this.contractType,
    this.hasDevelopmentPlans,
    this.developmentPlanUrl,
    this.developmentDescription,
    this.estimatedDevelopmentCost,
  });

  @override
  String getEstateType() {
    return 'land';
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'location': location,
      'photoUrls': photoUrls,
      'isFeatured': isFeatured,
      'status': status,
      'governorate': governorate,
      'city': city,
      'district': district,
      'block': block,
      'latitude': latitude,
      'longitude': longitude,
      'shareLocation': shareLocation,
      'mainCategory': mainCategory,
      'subCategory': subCategory,
      'ownerId': ownerId,
      'advertiserName': advertiserName,
      'advertiserPhone': advertiserPhone,
      'advertiserImage': advertiserImage,
      'advertiserType': advertiserType,
      'advertiserJoinDate': EstateBase.dateTimeToTimestamp(advertiserJoinDate),
      'advertiserAdsCount': advertiserAdsCount,
      'hidePhone': hidePhone,
      'extraPhones': extraPhones,
      'createdAt': EstateBase.dateTimeToTimestamp(createdAt),
      'updatedAt': EstateBase.dateTimeToTimestamp(updatedAt),
      'startDate': EstateBase.dateTimeToTimestamp(startDate),
      'endDate': EstateBase.dateTimeToTimestamp(endDate),
      'viewsCount': viewsCount,
      'favoritesCount': favoritesCount,
      'contactCount': contactCount,
      'subscriptionPlan': subscriptionPlan,
      'autoRepublish': autoRepublish,
      'isPinned': isPinned,
      'isPromoted': isPromoted,
      'isVIP': isVIP,
      'isVerified': isVerified,
      'originalEstateId': originalEstateId,
      'isOriginal': isOriginal,
      'copiedBy': copiedBy,
      'floorPlanUrl': floorPlanUrl,
      'virtualTourUrl': virtualTourUrl,
      'videoUrl': videoUrl,
      'documents': documents,
      'estateType': 'land',
      'area': area,
      'landType': landType,
      'streetWidth': streetWidth,
      'hasWaterAndElectricity': hasWaterAndElectricity,
      'landShape': landShape,
      'numberOfStreets': numberOfStreets,
      'facingDirection': facingDirection,
      'isCornerLand': isCornerLand,
      'landNumber': landNumber,
      'planNumber': planNumber,
      'deedNumber': deedNumber,
      'deedDate': EstateBase.dateTimeToTimestamp(deedDate),
      'landUseType': landUseType,
      'hasMunicipality': hasMunicipality,
      'streetLength': streetLength,
      'depth': depth,
      'buildingRatio': buildingRatio,
      'floorRatio': floorRatio,
      'isFilledAndLeveled': isFilledAndLeveled,
      'hasBoundaryWall': hasBoundaryWall,
      'soilType': soilType,
      'elevation': elevation,
      'rentalPeriod': rentalPeriod,
      'insuranceAmount': insuranceAmount,
      'commissionAmount': commissionAmount,
      'paymentMethod': paymentMethod,
      'contractType': contractType,
      'hasDevelopmentPlans': hasDevelopmentPlans,
      'developmentPlanUrl': developmentPlanUrl,
      'developmentDescription': developmentDescription,
      'estimatedDevelopmentCost': estimatedDevelopmentCost,
    };
  }

  @override
  LandEstate copyWithBase({
    String? id,
    String? title,
    String? description,
    double? price,
    String? location,
    List<String>? photoUrls,
    bool? isFeatured,
    String? status,
    String? governorate,
    String? city,
    String? district,
    String? block,
    double? latitude,
    double? longitude,
    bool? shareLocation,
    String? mainCategory,
    String? subCategory,
    String? ownerId,
    String? advertiserName,
    String? advertiserPhone,
    String? advertiserImage,
    String? advertiserType,
    DateTime? advertiserJoinDate,
    int? advertiserAdsCount,
    bool? hidePhone,
    List<String>? extraPhones,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? startDate,
    DateTime? endDate,
    int? viewsCount,
    int? favoritesCount,
    int? contactCount,
    String? subscriptionPlan,
    bool? autoRepublish,
    bool? isPinned,
    bool? isPromoted,
    bool? isVIP,
    bool? isVerified,
    bool? isPaymentVerified,
    String? originalEstateId,
    bool? isOriginal,
    List<String>? copiedBy,
    String? floorPlanUrl,
    String? virtualTourUrl,
    String? videoUrl,
    List<Map<String, dynamic>>? documents,
  }) {
    return copyWith(
      id: id,
      title: title,
      description: description,
      price: price,
      location: location,
      photoUrls: photoUrls,
      isFeatured: isFeatured,
      status: status,
      governorate: governorate,
      city: city,
      district: district,
      block: block,
      latitude: latitude,
      longitude: longitude,
      shareLocation: shareLocation,
      mainCategory: mainCategory,
      subCategory: subCategory,
      ownerId: ownerId,
      advertiserName: advertiserName,
      advertiserPhone: advertiserPhone,
      advertiserImage: advertiserImage,
      advertiserType: advertiserType,
      advertiserJoinDate: advertiserJoinDate,
      advertiserAdsCount: advertiserAdsCount,
      hidePhone: hidePhone,
      extraPhones: extraPhones,
      createdAt: createdAt,
      updatedAt: updatedAt,
      startDate: startDate,
      endDate: endDate,
      viewsCount: viewsCount,
      favoritesCount: favoritesCount,
      contactCount: contactCount,
      subscriptionPlan: subscriptionPlan,
      autoRepublish: autoRepublish,
      isPinned: isPinned,
      isPromoted: isPromoted,
      isVIP: isVIP,
      isVerified: isVerified,
      originalEstateId: originalEstateId,
      isOriginal: isOriginal,
      copiedBy: copiedBy,
      floorPlanUrl: floorPlanUrl,
      virtualTourUrl: virtualTourUrl,
      videoUrl: videoUrl,
      documents: documents);
  }

  /// إنشاء نسخة معدلة من الأرض
  LandEstate copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    String? location,
    List<String>? photoUrls,
    bool? isFeatured,
    String? status,
    String? governorate,
    String? city,
    String? district,
    String? block,
    double? latitude,
    double? longitude,
    bool? shareLocation,
    String? mainCategory,
    String? subCategory,
    String? ownerId,
    String? advertiserName,
    String? advertiserPhone,
    String? advertiserImage,
    String? advertiserType,
    DateTime? advertiserJoinDate,
    int? advertiserAdsCount,
    bool? hidePhone,
    List<String>? extraPhones,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? startDate,
    DateTime? endDate,
    int? viewsCount,
    int? favoritesCount,
    int? contactCount,
    String? subscriptionPlan,
    bool? autoRepublish,
    bool? isPinned,
    bool? isPromoted,
    bool? isVIP,
    bool? isVerified,
    String? originalEstateId,
    bool? isOriginal,
    List<String>? copiedBy,
    String? floorPlanUrl,
    String? virtualTourUrl,
    String? videoUrl,
    List<Map<String, dynamic>>? documents,
    double? area,
    String? landType,
    String? streetWidth,
    bool? hasWaterAndElectricity,
    String? landShape,
    int? numberOfStreets,
    String? facingDirection,
    bool? isCornerLand,
    String? landNumber,
    String? planNumber,
    String? deedNumber,
    DateTime? deedDate,
    String? landUseType,
    bool? hasMunicipality,
    double? streetLength,
    double? depth,
    double? buildingRatio,
    int? floorRatio,
    bool? isFilledAndLeveled,
    bool? hasBoundaryWall,
    String? soilType,
    double? elevation,
    String? rentalPeriod,
    double? insuranceAmount,
    double? commissionAmount,
    String? paymentMethod,
    String? contractType,
    bool? hasDevelopmentPlans,
    String? developmentPlanUrl,
    String? developmentDescription,
    double? estimatedDevelopmentCost,
  }) {
    return LandEstate(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      location: location ?? this.location,
      photoUrls: photoUrls ?? this.photoUrls,
      isFeatured: isFeatured ?? this.isFeatured,
      status: status ?? this.status,
      governorate: governorate ?? this.governorate,
      city: city ?? this.city,
      district: district ?? this.district,
      block: block ?? this.block,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      shareLocation: shareLocation ?? this.shareLocation,
      mainCategory: mainCategory ?? this.mainCategory,
      subCategory: subCategory ?? this.subCategory,
      ownerId: ownerId ?? this.ownerId,
      advertiserName: advertiserName ?? this.advertiserName,
      advertiserPhone: advertiserPhone ?? this.advertiserPhone,
      advertiserImage: advertiserImage ?? this.advertiserImage,
      advertiserType: advertiserType ?? this.advertiserType,
      advertiserJoinDate: advertiserJoinDate ?? this.advertiserJoinDate,
      advertiserAdsCount: advertiserAdsCount ?? this.advertiserAdsCount,
      hidePhone: hidePhone ?? this.hidePhone,
      extraPhones: extraPhones ?? this.extraPhones,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      viewsCount: viewsCount ?? this.viewsCount,
      favoritesCount: favoritesCount ?? this.favoritesCount,
      contactCount: contactCount ?? this.contactCount,
      subscriptionPlan: subscriptionPlan ?? this.subscriptionPlan,
      autoRepublish: autoRepublish ?? this.autoRepublish,
      isPinned: isPinned ?? this.isPinned,
      isPromoted: isPromoted ?? this.isPromoted,
      isVIP: isVIP ?? this.isVIP,
      isVerified: isVerified ?? this.isVerified,
      originalEstateId: originalEstateId ?? this.originalEstateId,
      isOriginal: isOriginal ?? this.isOriginal,
      copiedBy: copiedBy ?? this.copiedBy,
      floorPlanUrl: floorPlanUrl ?? this.floorPlanUrl,
      virtualTourUrl: virtualTourUrl ?? this.virtualTourUrl,
      videoUrl: videoUrl ?? this.videoUrl,
      documents: documents ?? this.documents,
      area: area ?? this.area,
      landType: landType ?? this.landType,
      streetWidth: streetWidth ?? this.streetWidth,
      hasWaterAndElectricity:
          hasWaterAndElectricity ?? this.hasWaterAndElectricity,
      landShape: landShape ?? this.landShape,
      numberOfStreets: numberOfStreets ?? this.numberOfStreets,
      facingDirection: facingDirection ?? this.facingDirection,
      isCornerLand: isCornerLand ?? this.isCornerLand,
      landNumber: landNumber ?? this.landNumber,
      planNumber: planNumber ?? this.planNumber,
      deedNumber: deedNumber ?? this.deedNumber,
      deedDate: deedDate ?? this.deedDate,
      landUseType: landUseType ?? this.landUseType,
      hasMunicipality: hasMunicipality ?? this.hasMunicipality,
      streetLength: streetLength ?? this.streetLength,
      depth: depth ?? this.depth,
      buildingRatio: buildingRatio ?? this.buildingRatio,
      floorRatio: floorRatio ?? this.floorRatio,
      isFilledAndLeveled: isFilledAndLeveled ?? this.isFilledAndLeveled,
      hasBoundaryWall: hasBoundaryWall ?? this.hasBoundaryWall,
      soilType: soilType ?? this.soilType,
      elevation: elevation ?? this.elevation,
      rentalPeriod: rentalPeriod ?? this.rentalPeriod,
      insuranceAmount: insuranceAmount ?? this.insuranceAmount,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      contractType: contractType ?? this.contractType,
      hasDevelopmentPlans: hasDevelopmentPlans ?? this.hasDevelopmentPlans,
      developmentPlanUrl: developmentPlanUrl ?? this.developmentPlanUrl,
      developmentDescription:
          developmentDescription ?? this.developmentDescription,
      estimatedDevelopmentCost:
          estimatedDevelopmentCost ?? this.estimatedDevelopmentCost);
  }

  /// التحقق من صحة بيانات الأرض
  @override
  Map<String, String> validate() {
    final errors = super.validate();

    if (area != null && area! <= 0) {
      errors['area'] = 'يجب إدخال مساحة صحيحة';
    } else if (area != null && area! > 1000000) {
      errors['area'] = 'المساحة كبيرة جداً، يرجى التحقق';
    }

    if (landType == null || landType!.isEmpty) {
      errors['landType'] = 'يجب تحديد نوع الأرض';
    }

    if (landUseType == null || landUseType!.isEmpty) {
      errors['landUseType'] = 'يجب تحديد نوع استخدام الأرض';
    }

    return errors;
  }

  /// إنشاء أرض من Map
  factory LandEstate.fromMap(Map<String, dynamic> map) {
    return LandEstate(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      location: map['location'] ?? '',
      photoUrls: List<String>.from(map['photoUrls'] ?? []),
      isFeatured: map['isFeatured'] ?? false,
      status: map['status'] ?? 'متاح',
      governorate: map['governorate'],
      city: map['city'],
      district: map['district'],
      block: map['block'],
      latitude: map['latitude'],
      longitude: map['longitude'],
      shareLocation: map['shareLocation'] ?? false,
      mainCategory: map['mainCategory'] ?? 'land',
      subCategory: map['subCategory'],
      ownerId: map['ownerId'] ?? '',
      advertiserName: map['advertiserName'],
      advertiserPhone: map['advertiserPhone'],
      advertiserImage: map['advertiserImage'],
      advertiserType: map['advertiserType'],
      advertiserJoinDate:
          EstateBase.timestampToDateTime(map['advertiserJoinDate']),
      advertiserAdsCount: map['advertiserAdsCount'],
      hidePhone: map['hidePhone'] ?? false,
      extraPhones: List<String>.from(map['extraPhones'] ?? []),
      createdAt:
          EstateBase.timestampToDateTime(map['createdAt']) ?? DateTime.now(),
      updatedAt: EstateBase.timestampToDateTime(map['updatedAt']),
      startDate: EstateBase.timestampToDateTime(map['startDate']),
      endDate: EstateBase.timestampToDateTime(map['endDate']),
      viewsCount: map['viewsCount'] ?? 0,
      favoritesCount: map['favoritesCount'] ?? 0,
      contactCount: map['contactCount'] ?? 0,
      subscriptionPlan: map['subscriptionPlan'] ?? 'free',
      autoRepublish: map['autoRepublish'] ?? false,
      isPinned: map['isPinned'] ?? false,
      isPromoted: map['isPromoted'] ?? false,
      isVIP: map['isVIP'] ?? false,
      isVerified: map['isVerified'] ?? false,
      originalEstateId: map['originalEstateId'],
      isOriginal: map['isOriginal'] ?? true,
      copiedBy:
          map['copiedBy'] != null ? List<String>.from(map['copiedBy']) : null,
      floorPlanUrl: map['floorPlanUrl'],
      virtualTourUrl: map['virtualTourUrl'],
      videoUrl: map['videoUrl'],
      documents: map['documents'] != null
          ? List<Map<String, dynamic>>.from(map['documents'])
          : null,
      area: map['area'],
      landType: map['landType'],
      streetWidth: map['streetWidth'],
      hasWaterAndElectricity: map['hasWaterAndElectricity'],
      landShape: map['landShape'],
      numberOfStreets: map['numberOfStreets'],
      facingDirection: map['facingDirection'],
      isCornerLand: map['isCornerLand'],
      landNumber: map['landNumber'],
      planNumber: map['planNumber'],
      deedNumber: map['deedNumber'],
      deedDate: EstateBase.timestampToDateTime(map['deedDate']),
      landUseType: map['landUseType'],
      hasMunicipality: map['hasMunicipality'],
      streetLength: map['streetLength'],
      depth: map['depth'],
      buildingRatio: map['buildingRatio'],
      floorRatio: map['floorRatio'],
      isFilledAndLeveled: map['isFilledAndLeveled'],
      hasBoundaryWall: map['hasBoundaryWall'],
      soilType: map['soilType'],
      elevation: map['elevation'],
      rentalPeriod: map['rentalPeriod'],
      insuranceAmount: map['insuranceAmount'],
      commissionAmount: map['commissionAmount'],
      paymentMethod: map['paymentMethod'],
      contractType: map['contractType'],
      hasDevelopmentPlans: map['hasDevelopmentPlans'],
      developmentPlanUrl: map['developmentPlanUrl'],
      developmentDescription: map['developmentDescription'],
      estimatedDevelopmentCost: map['estimatedDevelopmentCost']);
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        price,
        location,
        photoUrls,
        isFeatured,
        status,
        governorate,
        city,
        district,
        block,
        latitude,
        longitude,
        shareLocation,
        mainCategory,
        subCategory,
        ownerId,
        advertiserName,
        advertiserPhone,
        advertiserImage,
        advertiserType,
        advertiserJoinDate,
        advertiserAdsCount,
        hidePhone,
        extraPhones,
        createdAt,
        updatedAt,
        startDate,
        endDate,
        viewsCount,
        favoritesCount,
        contactCount,
        subscriptionPlan,
        autoRepublish,
        isPinned,
        isPromoted,
        isVIP,
        isVerified,
        originalEstateId,
        isOriginal,
        copiedBy,
        floorPlanUrl,
        virtualTourUrl,
        videoUrl,
        documents,
        area,
        landType,
        streetWidth,
        hasWaterAndElectricity,
        landShape,
        numberOfStreets,
        facingDirection,
        isCornerLand,
        landNumber,
        planNumber,
        deedNumber,
        deedDate,
        landUseType,
        hasMunicipality,
        streetLength,
        depth,
        buildingRatio,
        floorRatio,
        isFilledAndLeveled,
        hasBoundaryWall,
        soilType,
        elevation,
        rentalPeriod,
        insuranceAmount,
        commissionAmount,
        paymentMethod,
        contractType,
        hasDevelopmentPlans,
        developmentPlanUrl,
        developmentDescription,
        estimatedDevelopmentCost,
      ];
}
