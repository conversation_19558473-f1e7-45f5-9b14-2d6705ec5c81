import 'package:flutter/material.dart';

/// Widget لتوفير مساحة آمنة احترافية للمحتوى
/// يحافظ على المساحة الآمنة مع إخفاء أشرطة النظام
class SafeAreaWrapper extends StatelessWidget {
  /// المحتوى المراد عرضه
  final Widget child;
  
  /// هل يجب تطبيق المساحة الآمنة من الأعلى
  final bool top;
  
  /// هل يجب تطبيق المساحة الآمنة من الأسفل
  final bool bottom;
  
  /// هل يجب تطبيق المساحة الآمنة من اليسار
  final bool left;
  
  /// هل يجب تطبيق المساحة الآمنة من اليمين
  final bool right;
  
  /// الحد الأدنى للمساحة الآمنة من الأعلى
  final double? minimumTopPadding;
  
  /// الحد الأدنى للمساحة الآمنة من الأسفل
  final double? minimumBottomPadding;

  const SafeAreaWrapper({
    super.key,
    required this.child,
    this.top = true,
    this.bottom = true,
    this.left = true,
    this.right = true,
    this.minimumTopPadding,
    this.minimumBottomPadding,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
      minimum: EdgeInsets.only(
        top: minimumTopPadding ?? 0,
        bottom: minimumBottomPadding ?? 0),
      child: child);
  }
}

/// Widget للمساحة الآمنة مع تخصيص إضافي
class CustomSafeArea extends StatelessWidget {
  /// المحتوى المراد عرضه
  final Widget child;
  
  /// لون الخلفية للمساحة الآمنة
  final Color? backgroundColor;
  
  /// هل يجب تطبيق المساحة الآمنة من الأعلى
  final bool maintainTopViewPadding;
  
  /// هل يجب تطبيق المساحة الآمنة من الأسفل
  final bool maintainBottomViewPadding;

  const CustomSafeArea({
    super.key,
    required this.child,
    this.backgroundColor,
    this.maintainTopViewPadding = true,
    this.maintainBottomViewPadding = true,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    
    return Container(
      color: backgroundColor,
      child: Padding(
        padding: EdgeInsets.only(
          top: maintainTopViewPadding ? mediaQuery.padding.top : 0,
          bottom: maintainBottomViewPadding ? mediaQuery.padding.bottom : 0),
        child: child));
  }
}

/// Widget لتطبيق المساحة الآمنة بشكل انتقائي
class SelectiveSafeArea extends StatelessWidget {
  /// المحتوى المراد عرضه
  final Widget child;
  
  /// المساحة الآمنة المخصصة
  final EdgeInsets? customPadding;
  
  /// هل يجب استخدام المساحة الآمنة الافتراضية
  final bool useSystemPadding;

  const SelectiveSafeArea({
    super.key,
    required this.child,
    this.customPadding,
    this.useSystemPadding = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!useSystemPadding && customPadding == null) {
      return child;
    }

    final mediaQuery = MediaQuery.of(context);
    final effectivePadding = customPadding ?? EdgeInsets.only(
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom);

    return Padding(
      padding: effectivePadding,
      child: child);
  }
}

/// Widget لإنشاء مساحة آمنة مع تدرج في الخلفية
class GradientSafeArea extends StatelessWidget {
  /// المحتوى المراد عرضه
  final Widget child;
  
  /// التدرج للخلفية
  final Gradient? gradient;
  
  /// لون الخلفية البديل
  final Color? backgroundColor;

  const GradientSafeArea({
    super.key,
    required this.child,
    this.gradient,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    
    return Container(
      decoration: BoxDecoration(
        gradient: gradient,
        color: backgroundColor),
      child: Padding(
        padding: EdgeInsets.only(
          top: mediaQuery.padding.top,
          bottom: mediaQuery.padding.bottom),
        child: child));
  }
}
