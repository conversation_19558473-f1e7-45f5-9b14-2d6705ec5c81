// lib/data/repositories_impl/property_request_repository_impl.dart
import 'dart:developer' as developer;
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

import '../../domain/models/property_request/property_offer_model.dart';
import '../../domain/models/property_request/property_request_model.dart';
import '../../domain/repositories/property_request_repository.dart';

/// تنفيذ مستودع طلبات العقارات
class PropertyRequestRepositoryImpl implements PropertyRequestRepository {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final FirebaseAuth _auth;
  final Uuid _uuid = const Uuid();

  /// إنشاء مستودع طلبات العقارات
  PropertyRequestRepositoryImpl({
    FirebaseFirestore? firestore,
    FirebaseStorage? storage,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _storage = storage ?? FirebaseStorage.instance,
        _auth = auth ?? FirebaseAuth.instance;

  @override
  Future<List<PropertyRequestModel>> getAllPropertyRequests({
    int limit = 10,
    DocumentSnapshot? startAfter,
    String? sortBy,
    bool descending = true,
    Map<String, dynamic>? filters,
  }) async {
    try {
      Query query = _firestore.collection('property_requests');

      // تطبيق الفلاتر
      if (filters != null) {
        if (filters['propertyType'] != null) {
          query = query.where('propertyType', isEqualTo: filters['propertyType']);
        }
        
        if (filters['status'] != null) {
          query = query.where('status', isEqualTo: filters['status']);
        }
        
        if (filters['minPrice'] != null && filters['maxPrice'] != null) {
          query = query
              .where('minPrice', isGreaterThanOrEqualTo: filters['minPrice'])
              .where('maxPrice', isLessThanOrEqualTo: filters['maxPrice']);
        } else if (filters['minPrice'] != null) {
          query = query.where('minPrice', isGreaterThanOrEqualTo: filters['minPrice']);
        } else if (filters['maxPrice'] != null) {
          query = query.where('maxPrice', isLessThanOrEqualTo: filters['maxPrice']);
        }
        
        if (filters['preferredLocations'] != null && filters['preferredLocations'].isNotEmpty) {
          query = query.where('preferredLocations', arrayContainsAny: filters['preferredLocations']);
        }
        
        if (filters['minRooms'] != null) {
          query = query.where('minRooms', isGreaterThanOrEqualTo: filters['minRooms']);
        }
        
        if (filters['minBathrooms'] != null) {
          query = query.where('minBathrooms', isGreaterThanOrEqualTo: filters['minBathrooms']);
        }
        
        if (filters['minArea'] != null) {
          query = query.where('minArea', isGreaterThanOrEqualTo: filters['minArea']);
        }
        
        if (filters['hasCentralAC'] != null) {
          query = query.where('hasCentralAC', isEqualTo: filters['hasCentralAC']);
        }
        
        if (filters['hasMaidRoom'] != null) {
          query = query.where('hasMaidRoom', isEqualTo: filters['hasMaidRoom']);
        }
        
        if (filters['hasGarage'] != null) {
          query = query.where('hasGarage', isEqualTo: filters['hasGarage']);
        }
        
        if (filters['hasSwimmingPool'] != null) {
          query = query.where('hasSwimmingPool', isEqualTo: filters['hasSwimmingPool']);
        }
        
        if (filters['hasElevator'] != null) {
          query = query.where('hasElevator', isEqualTo: filters['hasElevator']);
        }
        
        if (filters['isFullyFurnished'] != null) {
          query = query.where('isFullyFurnished', isEqualTo: filters['isFullyFurnished']);
        }
      }

      // ترتيب النتائج
      if (sortBy != null) {
        query = query.orderBy(sortBy, descending: descending);
      } else {
        query = query.orderBy('createdAt', descending: true);
      }

      // تحديد نقطة البداية للصفحات
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      // تحديد عدد النتائج
      query = query.limit(limit);

      // تنفيذ الاستعلام
      final snapshot = await query.get();
      
      return snapshot.docs
          .map((doc) => PropertyRequestModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على طلبات العقارات: $e');
      return [];
    }
  }

  @override
  Future<PropertyRequestModel?> getPropertyRequestById(String requestId) async {
    try {
      final doc = await _firestore.collection('property_requests').doc(requestId).get();
      
      if (!doc.exists) {
        return null;
      }
      
      return PropertyRequestModel.fromFirestore(doc);
    } catch (e) {
      developer.log('خطأ في الحصول على طلب العقار: $e');
      return null;
    }
  }

  @override
  Future<PropertyRequestModel> createPropertyRequest(
    PropertyRequestModel request, {
    List<File>? images,
  }) async {
    try {
      // التحقق من المستخدم الحالي
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لإنشاء طلب عقار');
      }

      // إنشاء معرف جديد للطلب
      final requestId = _uuid.v4();
      
      // رفع الصور إذا وجدت
      List<String> imageUrls = [];
      if (images != null && images.isNotEmpty) {
        imageUrls = await _uploadImages(images, 'property_requests/$requestId');
      }
      
      // إنشاء نموذج الطلب مع المعرف الجديد والصور
      final newRequest = request.copyWith(
        id: requestId,
        userId: user.uid,
        images: imageUrls,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());
      
      // حفظ الطلب في Firestore
      await _firestore
          .collection('property_requests')
          .doc(requestId)
          .set(newRequest.toMap());
      
      return newRequest;
    } catch (e) {
      developer.log('خطأ في إنشاء طلب العقار: $e');
      rethrow;
    }
  }

  @override
  Future<void> updatePropertyRequest(
    PropertyRequestModel request, {
    List<File>? newImages,
    List<String>? imagesToDelete,
  }) async {
    try {
      // التحقق من المستخدم الحالي
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لتحديث طلب العقار');
      }

      // التحقق من أن المستخدم هو صاحب الطلب
      if (user.uid != request.userId) {
        throw Exception('لا يمكنك تحديث طلب عقار لا تملكه');
      }

      // حذف الصور المطلوب حذفها
      if (imagesToDelete != null && imagesToDelete.isNotEmpty) {
        await _deleteImages(imagesToDelete);
      }
      
      // رفع الصور الجديدة إذا وجدت
      List<String> newImageUrls = [];
      if (newImages != null && newImages.isNotEmpty) {
        newImageUrls = await _uploadImages(newImages, 'property_requests/${request.id}');
      }
      
      // دمج الصور الجديدة مع الصور الحالية
      List<String> updatedImages = [...(request.images ?? [])];
      
      // حذف الصور المطلوب حذفها
      if (imagesToDelete != null) {
        updatedImages.removeWhere((url) => imagesToDelete.contains(url));
      }
      
      // إضافة الصور الجديدة
      updatedImages.addAll(newImageUrls);
      
      // تحديث نموذج الطلب
      final updatedRequest = request.copyWith(
        images: updatedImages,
        updatedAt: DateTime.now());
      
      // تحديث الطلب في Firestore
      await _firestore
          .collection('property_requests')
          .doc(request.id)
          .update(updatedRequest.toMap());
    } catch (e) {
      developer.log('خطأ في تحديث طلب العقار: $e');
      rethrow;
    }
  }

  @override
  Future<void> deletePropertyRequest(String requestId) async {
    try {
      // التحقق من المستخدم الحالي
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لحذف طلب العقار');
      }

      // الحصول على الطلب
      final request = await getPropertyRequestById(requestId);
      if (request == null) {
        throw Exception('طلب العقار غير موجود');
      }

      // التحقق من أن المستخدم هو صاحب الطلب
      if (user.uid != request.userId) {
        throw Exception('لا يمكنك حذف طلب عقار لا تملكه');
      }

      // حذف الصور المرتبطة بالطلب
      if (request.images != null && request.images!.isNotEmpty) {
        await _deleteImages(request.images!);
      }
      
      // حذف العروض المرتبطة بالطلب
      final offersSnapshot = await _firestore
          .collection('property_offers')
          .where('requestId', isEqualTo: requestId)
          .get();
      
      final batch = _firestore.batch();
      
      for (final doc in offersSnapshot.docs) {
        batch.delete(doc.reference);
      }
      
      // حذف الطلب
      batch.delete(_firestore.collection('property_requests').doc(requestId));
      
      await batch.commit();
    } catch (e) {
      developer.log('خطأ في حذف طلب العقار: $e');
      rethrow;
    }
  }

  @override
  Future<void> updatePropertyRequestStatus(String requestId, RequestStatus status) async {
    try {
      await _firestore.collection('property_requests').doc(requestId).update({
        'status': status.index,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      developer.log('خطأ في تحديث حالة طلب العقار: $e');
      rethrow;
    }
  }

  @override
  Future<void> incrementPropertyRequestViews(String requestId) async {
    try {
      await _firestore.collection('property_requests').doc(requestId).update({
        'viewsCount': FieldValue.increment(1),
      });
    } catch (e) {
      developer.log('خطأ في زيادة عدد مشاهدات طلب العقار: $e');
    }
  }

  @override
  Future<List<PropertyRequestModel>> getUserPropertyRequests(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore
          .collection('property_requests')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true);
      
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }
      
      query = query.limit(limit);
      
      final snapshot = await query.get();
      
      return snapshot.docs
          .map((doc) => PropertyRequestModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على طلبات العقارات للمستخدم: $e');
      return [];
    }
  }

  @override
  Future<List<PropertyRequestModel>> getMostViewedPropertyRequests({int limit = 10}) async {
    try {
      final snapshot = await _firestore
          .collection('property_requests')
          .where('status', isEqualTo: RequestStatus.open.index)
          .orderBy('viewsCount', descending: true)
          .limit(limit)
          .get();
      
      return snapshot.docs
          .map((doc) => PropertyRequestModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على طلبات العقارات الأكثر مشاهدة: $e');
      return [];
    }
  }

  @override
  Future<List<PropertyRequestModel>> getLatestPropertyRequests({int limit = 10}) async {
    try {
      final snapshot = await _firestore
          .collection('property_requests')
          .where('status', isEqualTo: RequestStatus.open.index)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();
      
      return snapshot.docs
          .map((doc) => PropertyRequestModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على أحدث طلبات العقارات: $e');
      return [];
    }
  }

  @override
  Future<List<PropertyRequestModel>> searchPropertyRequests(
    String query, {
    int limit = 20,
    Map<String, dynamic>? filters,
  }) async {
    try {
      // ملاحظة: هذه طريقة بسيطة للبحث، يمكن استخدام Algolia أو Firebase Functions للبحث المتقدم
      final snapshot = await _firestore
          .collection('property_requests')
          .where('status', isEqualTo: RequestStatus.open.index)
          .orderBy('createdAt', descending: true)
          .get();
      
      final List<PropertyRequestModel> requests = snapshot.docs
          .map((doc) => PropertyRequestModel.fromFirestore(doc))
          .toList();
      
      // البحث في العنوان والوصف
      final List<PropertyRequestModel> results = requests.where((request) {
        final title = request.title.toLowerCase();
        final description = request.description.toLowerCase();
        final searchQuery = query.toLowerCase();
        
        return title.contains(searchQuery) || description.contains(searchQuery);
      }).toList();
      
      // تطبيق الفلاتر
      if (filters != null) {
        return results.where((request) {
          bool match = true;
          
          if (filters['propertyType'] != null) {
            match = match && request.propertyType == filters['propertyType'];
          }
          
          if (filters['minPrice'] != null && request.maxPrice != null) {
            match = match && request.maxPrice! >= filters['minPrice'];
          }
          
          if (filters['maxPrice'] != null && request.minPrice != null) {
            match = match && request.minPrice! <= filters['maxPrice'];
          }
          
          if (filters['preferredLocations'] != null && filters['preferredLocations'].isNotEmpty) {
            match = match && request.preferredLocations.any(
                  (location) => filters['preferredLocations'].contains(location));
          }
          
          if (filters['minRooms'] != null && request.minRooms != null) {
            match = match && request.minRooms! >= filters['minRooms'];
          }
          
          if (filters['minBathrooms'] != null && request.minBathrooms != null) {
            match = match && request.minBathrooms! >= filters['minBathrooms'];
          }
          
          if (filters['minArea'] != null && request.minArea != null) {
            match = match && request.minArea! >= filters['minArea'];
          }
          
          if (filters['hasCentralAC'] != null) {
            match = match && request.hasCentralAC == filters['hasCentralAC'];
          }
          
          if (filters['hasMaidRoom'] != null) {
            match = match && request.hasMaidRoom == filters['hasMaidRoom'];
          }
          
          if (filters['hasGarage'] != null) {
            match = match && request.hasGarage == filters['hasGarage'];
          }
          
          if (filters['hasSwimmingPool'] != null) {
            match = match && request.hasSwimmingPool == filters['hasSwimmingPool'];
          }
          
          if (filters['hasElevator'] != null) {
            match = match && request.hasElevator == filters['hasElevator'];
          }
          
          if (filters['isFullyFurnished'] != null) {
            match = match && request.isFullyFurnished == filters['isFullyFurnished'];
          }
          
          return match;
        }).toList();
      }
      
      // ترتيب النتائج حسب الأحدث
      results.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      // تحديد عدد النتائج
      return results.take(limit).toList();
    } catch (e) {
      developer.log('خطأ في البحث عن طلبات العقارات: $e');
      return [];
    }
  }

  @override
  Future<List<PropertyOfferModel>> getOffersByRequest(
    String requestId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore
          .collection('property_offers')
          .where('requestId', isEqualTo: requestId)
          .orderBy('createdAt', descending: true);
      
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }
      
      query = query.limit(limit);
      
      final snapshot = await query.get();
      
      return snapshot.docs
          .map((doc) => PropertyOfferModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على عروض طلب العقار: $e');
      return [];
    }
  }

  @override
  Future<PropertyOfferModel?> getOfferById(String offerId) async {
    try {
      final doc = await _firestore.collection('property_offers').doc(offerId).get();
      
      if (!doc.exists) {
        return null;
      }
      
      return PropertyOfferModel.fromFirestore(doc);
    } catch (e) {
      developer.log('خطأ في الحصول على العرض: $e');
      return null;
    }
  }

  @override
  Future<PropertyOfferModel> createOffer(
    PropertyOfferModel offer, {
    List<File>? images,
  }) async {
    try {
      // التحقق من المستخدم الحالي
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لإنشاء عرض');
      }

      // إنشاء معرف جديد للعرض
      final offerId = _uuid.v4();
      
      // رفع الصور إذا وجدت
      List<String> imageUrls = [];
      if (images != null && images.isNotEmpty) {
        imageUrls = await _uploadImages(images, 'property_offers/$offerId');
      }
      
      // إنشاء نموذج العرض مع المعرف الجديد والصور
      final newOffer = offer.copyWith(
        id: offerId,
        userId: user.uid,
        photoUrls: imageUrls,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());
      
      // حفظ العرض في Firestore
      await _firestore
          .collection('property_offers')
          .doc(offerId)
          .set(newOffer.toMap());
      
      // تحديث عدد العروض في طلب العقار
      await _firestore.collection('property_requests').doc(offer.requestId).update({
        'offersCount': FieldValue.increment(1),
      });
      
      return newOffer;
    } catch (e) {
      developer.log('خطأ في إنشاء العرض: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateOffer(
    PropertyOfferModel offer, {
    List<File>? newImages,
    List<String>? imagesToDelete,
  }) async {
    try {
      // التحقق من المستخدم الحالي
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لتحديث العرض');
      }

      // التحقق من أن المستخدم هو صاحب العرض
      if (user.uid != offer.userId) {
        throw Exception('لا يمكنك تحديث عرض لا تملكه');
      }

      // حذف الصور المطلوب حذفها
      if (imagesToDelete != null && imagesToDelete.isNotEmpty) {
        await _deleteImages(imagesToDelete);
      }
      
      // رفع الصور الجديدة إذا وجدت
      List<String> newImageUrls = [];
      if (newImages != null && newImages.isNotEmpty) {
        newImageUrls = await _uploadImages(newImages, 'property_offers/${offer.id}');
      }
      
      // دمج الصور الجديدة مع الصور الحالية
      List<String> updatedImages = [...(offer.photoUrls ?? [])];
      
      // حذف الصور المطلوب حذفها
      if (imagesToDelete != null) {
        updatedImages.removeWhere((url) => imagesToDelete.contains(url));
      }
      
      // إضافة الصور الجديدة
      updatedImages.addAll(newImageUrls);
      
      // تحديث نموذج العرض
      final updatedOffer = offer.copyWith(
        photoUrls: updatedImages,
        updatedAt: DateTime.now());
      
      // تحديث العرض في Firestore
      await _firestore
          .collection('property_offers')
          .doc(offer.id)
          .update(updatedOffer.toMap());
    } catch (e) {
      developer.log('خطأ في تحديث العرض: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteOffer(String offerId) async {
    try {
      // التحقق من المستخدم الحالي
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لحذف العرض');
      }

      // الحصول على العرض
      final offer = await getOfferById(offerId);
      if (offer == null) {
        throw Exception('العرض غير موجود');
      }

      // التحقق من أن المستخدم هو صاحب العرض
      if (user.uid != offer.userId) {
        throw Exception('لا يمكنك حذف عرض لا تملكه');
      }

      // حذف الصور المرتبطة بالعرض
      if (offer.photoUrls != null && offer.photoUrls!.isNotEmpty) {
        await _deleteImages(offer.photoUrls!);
      }
      
      // حذف العرض
      await _firestore.collection('property_offers').doc(offerId).delete();
      
      // تحديث عدد العروض في طلب العقار
      await _firestore.collection('property_requests').doc(offer.requestId).update({
        'offersCount': FieldValue.increment(-1),
      });
    } catch (e) {
      developer.log('خطأ في حذف العرض: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateOfferStatus(String offerId, OfferStatus status) async {
    try {
      await _firestore.collection('property_offers').doc(offerId).update({
        'status': status.index,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      developer.log('خطأ في تحديث حالة العرض: $e');
      rethrow;
    }
  }

  @override
  Future<List<PropertyOfferModel>> getUserOffers(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore
          .collection('property_offers')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true);
      
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }
      
      query = query.limit(limit);
      
      final snapshot = await query.get();
      
      return snapshot.docs
          .map((doc) => PropertyOfferModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على عروض المستخدم: $e');
      return [];
    }
  }

  @override
  Stream<List<PropertyRequestModel>> listenToPropertyRequests({
    int limit = 10,
    String? sortBy,
    bool descending = true,
    Map<String, dynamic>? filters,
  }) {
    try {
      Query query = _firestore.collection('property_requests');

      // تطبيق الفلاتر
      if (filters != null) {
        if (filters['propertyType'] != null) {
          query = query.where('propertyType', isEqualTo: filters['propertyType']);
        }
        
        if (filters['status'] != null) {
          query = query.where('status', isEqualTo: filters['status']);
        }
      }

      // ترتيب النتائج
      if (sortBy != null) {
        query = query.orderBy(sortBy, descending: descending);
      } else {
        query = query.orderBy('createdAt', descending: true);
      }

      // تحديد عدد النتائج
      query = query.limit(limit);

      return query.snapshots().map((snapshot) => snapshot.docs
          .map((doc) => PropertyRequestModel.fromFirestore(doc))
          .toList());
    } catch (e) {
      developer.log('خطأ في الاستماع لطلبات العقارات: $e');
      return Stream.value([]);
    }
  }

  @override
  Stream<PropertyRequestModel?> listenToPropertyRequest(String requestId) {
    return _firestore
        .collection('property_requests')
        .doc(requestId)
        .snapshots()
        .map((doc) => doc.exists ? PropertyRequestModel.fromFirestore(doc) : null);
  }

  @override
  Stream<List<PropertyOfferModel>> listenToRequestOffers(
    String requestId, {
    int limit = 20,
  }) {
    return _firestore
        .collection('property_offers')
        .where('requestId', isEqualTo: requestId)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => PropertyOfferModel.fromFirestore(doc))
            .toList());
  }

  @override
  Stream<PropertyOfferModel?> listenToOffer(String offerId) {
    return _firestore
        .collection('property_offers')
        .doc(offerId)
        .snapshots()
        .map((doc) => doc.exists ? PropertyOfferModel.fromFirestore(doc) : null);
  }

  /// رفع الصور إلى Firebase Storage
  Future<List<String>> _uploadImages(List<File> images, String folderPath) async {
    List<String> imageUrls = [];
    
    for (final image in images) {
      final fileName = '${_uuid.v4()}${path.extension(image.path)}';
      final ref = _storage.ref().child('$folderPath/$fileName');
      
      await ref.putFile(image);
      final url = await ref.getDownloadURL();
      
      imageUrls.add(url);
    }
    
    return imageUrls;
  }

  /// حذف الصور من Firebase Storage
  Future<void> _deleteImages(List<String> imageUrls) async {
    for (final url in imageUrls) {
      try {
        final ref = _storage.refFromURL(url);
        await ref.delete();
      } catch (e) {
        developer.log('خطأ في حذف الصورة: $e');
      }
    }
  }
}
