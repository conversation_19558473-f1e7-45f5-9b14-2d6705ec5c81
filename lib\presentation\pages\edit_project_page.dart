import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';

/// صفحة تعديل المشروع
class EditProjectPage extends StatefulWidget {
  final String projectId;

  const EditProjectPage({
    super.key,
    required this.projectId,
  });

  @override
  State<EditProjectPage> createState() => _EditProjectPageState();
}

class _EditProjectPageState extends State<EditProjectPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _budgetController = TextEditingController();
  final _teamSizeController = TextEditingController();

  String _selectedType = 'سكني';
  String _selectedStatus = 'قيد التخطيط';
  String _selectedPriority = 'متوسط';
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isUrgent = false;
  bool _isLoading = true;
  bool _isSaving = false;

  final List<String> _projectTypes = [
    'سكني',
    'تجاري',
    'صناعي',
    'مختلط',
    'إداري',
    'ترفيهي',
  ];

  final List<String> _projectStatuses = [
    'قيد التخطيط',
    'قيد التنفيذ',
    'متوقف',
    'مكتمل',
    'ملغي',
  ];

  final List<String> _priorities = [
    'منخفض',
    'متوسط',
    'عالي',
  ];

  @override
  void initState() {
    super.initState();
    _loadProjectData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _budgetController.dispose();
    _teamSizeController.dispose();
    super.dispose();
  }

  Future<void> _loadProjectData() async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('projects')
          .doc(widget.projectId)
          .get();

      if (doc.exists) {
        final data = doc.data()!;
        setState(() {
          _nameController.text = data['name'] ?? '';
          _descriptionController.text = data['description'] ?? '';
          _locationController.text = data['location'] ?? '';
          _budgetController.text = (data['budget'] ?? 0.0).toString();
          _teamSizeController.text = (data['teamSize'] ?? 0).toString();
          _selectedType = data['type'] ?? 'سكني';
          _selectedStatus = data['status'] ?? 'قيد التخطيط';
          _selectedPriority = data['priority'] ?? 'متوسط';
          _isUrgent = data['isUrgent'] ?? false;

          final startTimestamp = data['startDate'] as Timestamp?;
          final endTimestamp = data['endDate'] as Timestamp?;
          _startDate = startTimestamp?.toDate();
          _endDate = endTimestamp?.toDate();

          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'تعديل المشروع',
          style: CairoTextStyles.appBarTitle,
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: LoadingWidget())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfoSection(),
                    const SizedBox(height: 24),
                    _buildProjectDetailsSection(),
                    const SizedBox(height: 24),
                    _buildBudgetAndTeamSection(),
                    const SizedBox(height: 24),
                    _buildDatesSection(),
                    const SizedBox(height: 24),
                    _buildPrioritySection(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الأساسية',
            style: CairoTextStyles.titleLarge,
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _nameController,
            style: CairoTextStyles.bodyMedium,
            decoration: InputDecoration(
              labelText: 'اسم المشروع',
              labelStyle: CairoTextStyles.bodyMedium,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: const Icon(Icons.business),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال اسم المشروع';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _descriptionController,
            style: CairoTextStyles.bodyMedium,
            maxLines: 3,
            decoration: InputDecoration(
              labelText: 'وصف المشروع',
              labelStyle: CairoTextStyles.bodyMedium,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: const Icon(Icons.description),
            ),
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _locationController,
            style: CairoTextStyles.bodyMedium,
            decoration: InputDecoration(
              labelText: 'موقع المشروع',
              labelStyle: CairoTextStyles.bodyMedium,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: const Icon(Icons.location_on),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectDetailsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل المشروع',
            style: CairoTextStyles.titleLarge,
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<String>(
            value: _selectedType,
            style: CairoTextStyles.bodyMedium,
            decoration: InputDecoration(
              labelText: 'نوع المشروع',
              labelStyle: CairoTextStyles.bodyMedium,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: const Icon(Icons.category),
            ),
            items: _projectTypes.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type, style: CairoTextStyles.bodyMedium),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
              });
            },
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<String>(
            value: _selectedStatus,
            style: CairoTextStyles.bodyMedium,
            decoration: InputDecoration(
              labelText: 'حالة المشروع',
              labelStyle: CairoTextStyles.bodyMedium,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: const Icon(Icons.info),
            ),
            items: _projectStatuses.map((status) {
              return DropdownMenuItem(
                value: status,
                child: Text(status, style: CairoTextStyles.bodyMedium),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedStatus = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetAndTeamSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الميزانية والفريق',
            style: CairoTextStyles.titleLarge,
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _budgetController,
                  style: CairoTextStyles.bodyMedium,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'الميزانية (د.ك)',
                    labelStyle: CairoTextStyles.bodyMedium,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.account_balance_wallet),
                  ),
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      final budget = double.tryParse(value);
                      if (budget == null || budget < 0) {
                        return 'يرجى إدخال ميزانية صحيحة';
                      }
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _teamSizeController,
                  style: CairoTextStyles.bodyMedium,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'حجم الفريق',
                    labelStyle: CairoTextStyles.bodyMedium,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.group),
                  ),
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      final teamSize = int.tryParse(value);
                      if (teamSize == null || teamSize < 0) {
                        return 'يرجى إدخال عدد صحيح';
                      }
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDatesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التواريخ',
            style: CairoTextStyles.titleLarge,
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () => _selectStartDate(),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[400]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'تاريخ البداية',
                                style: CairoTextStyles.bodySmall,
                              ),
                              Text(
                                _startDate != null
                                    ? _formatDate(_startDate!)
                                    : 'اختر التاريخ',
                                style: CairoTextStyles.bodyMedium.copyWith(
                                  color: _startDate != null
                                      ? Colors.black
                                      : Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: InkWell(
                  onTap: () => _selectEndDate(),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[400]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.event),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'تاريخ الانتهاء',
                                style: CairoTextStyles.bodySmall,
                              ),
                              Text(
                                _endDate != null
                                    ? _formatDate(_endDate!)
                                    : 'اختر التاريخ',
                                style: CairoTextStyles.bodyMedium.copyWith(
                                  color: _endDate != null
                                      ? Colors.black
                                      : Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrioritySection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الأولوية والإعدادات',
            style: CairoTextStyles.titleLarge,
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<String>(
            value: _selectedPriority,
            style: CairoTextStyles.bodyMedium,
            decoration: InputDecoration(
              labelText: 'أولوية المشروع',
              labelStyle: CairoTextStyles.bodyMedium,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: const Icon(Icons.priority_high),
            ),
            items: _priorities.map((priority) {
              return DropdownMenuItem(
                value: priority,
                child: Text(priority, style: CairoTextStyles.bodyMedium),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedPriority = value!;
              });
            },
          ),
          const SizedBox(height: 16),

          CheckboxListTile(
            title: Text(
              'مشروع عاجل',
              style: CairoTextStyles.bodyMedium,
            ),
            subtitle: Text(
              'تحديد المشروع كعاجل يعطيه أولوية أعلى',
              style: CairoTextStyles.bodySmall,
            ),
            value: _isUrgent,
            onChanged: (value) {
              setState(() {
                _isUrgent = value ?? false;
              });
            },
            controlAffinity: ListTileControlAffinity.leading,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.grey[600],
              side: BorderSide(color: Colors.grey[400]!),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'إلغاء',
              style: CairoTextStyles.button.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isSaving ? null : _saveProject,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isSaving
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'حفظ التغييرات',
                    style: CairoTextStyles.button,
                  ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (date != null) {
      setState(() {
        _startDate = date;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: _startDate ?? DateTime.now(),
      lastDate: DateTime(2030),
    );
    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _saveProject() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final projectData = {
        'name': _nameController.text.trim(),
        'description': _descriptionController.text.trim(),
        'location': _locationController.text.trim(),
        'type': _selectedType,
        'status': _selectedStatus,
        'priority': _selectedPriority,
        'budget': double.tryParse(_budgetController.text) ?? 0.0,
        'teamSize': int.tryParse(_teamSizeController.text) ?? 0,
        'isUrgent': _isUrgent,
        'updatedAt': FieldValue.serverTimestamp(),
        'startDate': _startDate != null ? Timestamp.fromDate(_startDate!) : null,
        'endDate': _endDate != null ? Timestamp.fromDate(_endDate!) : null,
      };

      await FirebaseFirestore.instance
          .collection('projects')
          .doc(widget.projectId)
          .update(projectData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تحديث المشروع بنجاح',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ في تحديث المشروع: ${e.toString()}',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
