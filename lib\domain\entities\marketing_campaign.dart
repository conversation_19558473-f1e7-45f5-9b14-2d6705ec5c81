import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// حالة الحملة التسويقية
enum CampaignStatus {
  /// مسودة
  draft,
  
  /// نشطة
  active,
  
  /// متوقفة
  paused,
  
  /// مكتملة
  completed,
  
  /// ملغاة
  cancelled,
}

/// نوع الحملة التسويقية
enum CampaignType {
  /// إعلانات مدفوعة
  paid,
  
  /// وسائل التواصل الاجتماعي
  social,
  
  /// البريد الإلكتروني
  email,
  
  /// الرسائل النصية
  sms,
  
  /// إشعارات التطبيق
  push,
  
  /// الموقع الإلكتروني
  website,
  
  /// متعددة القنوات
  multiChannel,
}

/// نموذج للحملة التسويقية
class MarketingCampaign extends Equatable {
  /// معرف الحملة
  final String id;
  
  /// معرف الشركة
  final String companyId;
  
  /// اسم الحملة
  final String name;
  
  /// وصف الحملة
  final String? description;
  
  /// نوع الحملة
  final CampaignType type;
  
  /// حالة الحملة
  final CampaignStatus status;
  
  /// تاريخ البداية
  final DateTime startDate;
  
  /// تاريخ النهاية
  final DateTime? endDate;
  
  /// الميزانية
  final double budget;
  
  /// المبلغ المصروف
  final double spentAmount;
  
  /// الجمهور المستهدف
  final Map<String, dynamic>? targetAudience;
  
  /// المناطق المستهدفة
  final List<String>? targetAreas;
  
  /// أنواع العقارات المستهدفة
  final List<String>? targetPropertyTypes;
  
  /// قائمة معرفات العقارات المستهدفة
  final List<String>? targetEstates;
  
  /// قائمة معرفات أعضاء الفريق المسؤولين عن الحملة
  final List<String>? assignedTeamMembers;
  
  /// عدد المشاهدات
  final int viewsCount;
  
  /// عدد النقرات
  final int clicksCount;
  
  /// عدد الاتصالات
  final int contactsCount;
  
  /// عدد العملاء المكتسبين
  final int leadsCount;
  
  /// عدد المبيعات/الإيجارات
  final int salesCount;
  
  /// إجمالي المبيعات/الإيجارات
  final double totalSales;
  
  /// تاريخ إنشاء الحملة
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث للحملة
  final DateTime? updatedAt;
  
  /// معرف المستخدم الذي أنشأ الحملة
  final String createdBy;
  
  /// معرف المستخدم الذي قام بآخر تحديث للحملة
  final String? updatedBy;
  
  /// روابط الإعلانات
  final List<String>? adLinks;
  
  /// صور الإعلانات
  final List<String>? adImages;
  
  /// نصوص الإعلانات
  final List<String>? adTexts;
  
  /// معلومات إضافية
  final Map<String, dynamic>? additionalInfo;

  const MarketingCampaign({
    required this.id,
    required this.companyId,
    required this.name,
    this.description,
    required this.type,
    required this.status,
    required this.startDate,
    this.endDate,
    required this.budget,
    this.spentAmount = 0.0,
    this.targetAudience,
    this.targetAreas,
    this.targetPropertyTypes,
    this.targetEstates,
    this.assignedTeamMembers,
    this.viewsCount = 0,
    this.clicksCount = 0,
    this.contactsCount = 0,
    this.leadsCount = 0,
    this.salesCount = 0,
    this.totalSales = 0.0,
    required this.createdAt,
    this.updatedAt,
    required this.createdBy,
    this.updatedBy,
    this.adLinks,
    this.adImages,
    this.adTexts,
    this.additionalInfo,
  });

  /// إنشاء نسخة معدلة من الحملة
  MarketingCampaign copyWith({
    String? id,
    String? companyId,
    String? name,
    String? description,
    CampaignType? type,
    CampaignStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    double? budget,
    double? spentAmount,
    Map<String, dynamic>? targetAudience,
    List<String>? targetAreas,
    List<String>? targetPropertyTypes,
    List<String>? targetEstates,
    List<String>? assignedTeamMembers,
    int? viewsCount,
    int? clicksCount,
    int? contactsCount,
    int? leadsCount,
    int? salesCount,
    double? totalSales,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    List<String>? adLinks,
    List<String>? adImages,
    List<String>? adTexts,
    Map<String, dynamic>? additionalInfo,
  }) {
    return MarketingCampaign(
      id: id ?? this.id,
      companyId: companyId ?? this.companyId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      budget: budget ?? this.budget,
      spentAmount: spentAmount ?? this.spentAmount,
      targetAudience: targetAudience ?? this.targetAudience,
      targetAreas: targetAreas ?? this.targetAreas,
      targetPropertyTypes: targetPropertyTypes ?? this.targetPropertyTypes,
      targetEstates: targetEstates ?? this.targetEstates,
      assignedTeamMembers: assignedTeamMembers ?? this.assignedTeamMembers,
      viewsCount: viewsCount ?? this.viewsCount,
      clicksCount: clicksCount ?? this.clicksCount,
      contactsCount: contactsCount ?? this.contactsCount,
      leadsCount: leadsCount ?? this.leadsCount,
      salesCount: salesCount ?? this.salesCount,
      totalSales: totalSales ?? this.totalSales,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      adLinks: adLinks ?? this.adLinks,
      adImages: adImages ?? this.adImages,
      adTexts: adTexts ?? this.adTexts,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }
  
  /// تحويل الحملة إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'companyId': companyId,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': endDate != null ? Timestamp.fromDate(endDate!) : null,
      'budget': budget,
      'spentAmount': spentAmount,
      'targetAudience': targetAudience,
      'targetAreas': targetAreas,
      'targetPropertyTypes': targetPropertyTypes,
      'targetEstates': targetEstates,
      'assignedTeamMembers': assignedTeamMembers,
      'viewsCount': viewsCount,
      'clicksCount': clicksCount,
      'contactsCount': contactsCount,
      'leadsCount': leadsCount,
      'salesCount': salesCount,
      'totalSales': totalSales,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'adLinks': adLinks,
      'adImages': adImages,
      'adTexts': adTexts,
      'additionalInfo': additionalInfo,
    };
  }
  
  /// إنشاء حملة من Map
  factory MarketingCampaign.fromMap(Map<String, dynamic> map) {
    return MarketingCampaign(
      id: map['id'] ?? '',
      companyId: map['companyId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      type: _getCampaignTypeFromString(map['type'] ?? 'paid'),
      status: _getCampaignStatusFromString(map['status'] ?? 'draft'),
      startDate: map['startDate'] is Timestamp 
          ? (map['startDate'] as Timestamp).toDate() 
          : DateTime.now(),
      endDate: map['endDate'] is Timestamp 
          ? (map['endDate'] as Timestamp).toDate() 
          : null,
      budget: (map['budget'] ?? 0.0).toDouble(),
      spentAmount: (map['spentAmount'] ?? 0.0).toDouble(),
      targetAudience: map['targetAudience'],
      targetAreas: map['targetAreas'] != null ? List<String>.from(map['targetAreas']) : null,
      targetPropertyTypes: map['targetPropertyTypes'] != null ? List<String>.from(map['targetPropertyTypes']) : null,
      targetEstates: map['targetEstates'] != null ? List<String>.from(map['targetEstates']) : null,
      assignedTeamMembers: map['assignedTeamMembers'] != null ? List<String>.from(map['assignedTeamMembers']) : null,
      viewsCount: map['viewsCount'] ?? 0,
      clicksCount: map['clicksCount'] ?? 0,
      contactsCount: map['contactsCount'] ?? 0,
      leadsCount: map['leadsCount'] ?? 0,
      salesCount: map['salesCount'] ?? 0,
      totalSales: (map['totalSales'] ?? 0.0).toDouble(),
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
      createdBy: map['createdBy'] ?? '',
      updatedBy: map['updatedBy'],
      adLinks: map['adLinks'] != null ? List<String>.from(map['adLinks']) : null,
      adImages: map['adImages'] != null ? List<String>.from(map['adImages']) : null,
      adTexts: map['adTexts'] != null ? List<String>.from(map['adTexts']) : null,
      additionalInfo: map['additionalInfo']);
  }
  
  /// إنشاء حملة من DocumentSnapshot
  factory MarketingCampaign.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return MarketingCampaign.fromMap(data);
  }
  
  /// الحصول على نوع الحملة من النص
  static CampaignType _getCampaignTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'social':
        return CampaignType.social;
      case 'email':
        return CampaignType.email;
      case 'sms':
        return CampaignType.sms;
      case 'push':
        return CampaignType.push;
      case 'website':
        return CampaignType.website;
      case 'multiChannel':
        return CampaignType.multiChannel;
      default:
        return CampaignType.paid;
    }
  }
  
  /// الحصول على حالة الحملة من النص
  static CampaignStatus _getCampaignStatusFromString(String statusStr) {
    switch (statusStr) {
      case 'active':
        return CampaignStatus.active;
      case 'paused':
        return CampaignStatus.paused;
      case 'completed':
        return CampaignStatus.completed;
      case 'cancelled':
        return CampaignStatus.cancelled;
      default:
        return CampaignStatus.draft;
    }
  }
  
  /// الحصول على اسم نوع الحملة بالعربية
  String getCampaignTypeName() {
    switch (type) {
      case CampaignType.social:
        return 'وسائل التواصل الاجتماعي';
      case CampaignType.email:
        return 'البريد الإلكتروني';
      case CampaignType.sms:
        return 'الرسائل النصية';
      case CampaignType.push:
        return 'إشعارات التطبيق';
      case CampaignType.website:
        return 'الموقع الإلكتروني';
      case CampaignType.multiChannel:
        return 'متعددة القنوات';
      case CampaignType.paid:
        return 'إعلانات مدفوعة';
    }
  }
  
  /// الحصول على اسم حالة الحملة بالعربية
  String getCampaignStatusName() {
    switch (status) {
      case CampaignStatus.active:
        return 'نشطة';
      case CampaignStatus.paused:
        return 'متوقفة';
      case CampaignStatus.completed:
        return 'مكتملة';
      case CampaignStatus.cancelled:
        return 'ملغاة';
      case CampaignStatus.draft:
        return 'مسودة';
    }
  }
  
  /// الحصول على لون حالة الحملة
  String getCampaignStatusColor() {
    switch (status) {
      case CampaignStatus.active:
        return '#4CAF50'; // أخضر
      case CampaignStatus.paused:
        return '#FF9800'; // برتقالي
      case CampaignStatus.completed:
        return '#2196F3'; // أزرق
      case CampaignStatus.cancelled:
        return '#F44336'; // أحمر
      case CampaignStatus.draft:
        return '#9E9E9E'; // رمادي
    }
  }
  
  /// تنشيط الحملة
  MarketingCampaign activate(String updatedBy) {
    return copyWith(
      status: CampaignStatus.active,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy);
  }
  
  /// إيقاف الحملة مؤقتاً
  MarketingCampaign pause(String updatedBy) {
    return copyWith(
      status: CampaignStatus.paused,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy);
  }
  
  /// إكمال الحملة
  MarketingCampaign complete(String updatedBy) {
    return copyWith(
      status: CampaignStatus.completed,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy,
      endDate: DateTime.now());
  }
  
  /// إلغاء الحملة
  MarketingCampaign cancel(String updatedBy) {
    return copyWith(
      status: CampaignStatus.cancelled,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy);
  }
  
  /// إضافة مشاهدات
  MarketingCampaign addViews(int count) {
    return copyWith(
      viewsCount: viewsCount + count);
  }
  
  /// إضافة نقرات
  MarketingCampaign addClicks(int count) {
    return copyWith(
      clicksCount: clicksCount + count);
  }
  
  /// إضافة اتصالات
  MarketingCampaign addContacts(int count) {
    return copyWith(
      contactsCount: contactsCount + count);
  }
  
  /// إضافة عملاء مكتسبين
  MarketingCampaign addLeads(int count) {
    return copyWith(
      leadsCount: leadsCount + count);
  }
  
  /// إضافة مبيعات
  MarketingCampaign addSales(int count, double amount) {
    return copyWith(
      salesCount: salesCount + count,
      totalSales: totalSales + amount);
  }
  
  /// إضافة مبلغ مصروف
  MarketingCampaign addSpentAmount(double amount) {
    return copyWith(
      spentAmount: spentAmount + amount);
  }
  
  /// إضافة عضو فريق مسؤول عن الحملة
  MarketingCampaign addTeamMember(String teamMemberId) {
    final newAssignedTeamMembers = List<String>.from(assignedTeamMembers ?? []);
    
    if (!newAssignedTeamMembers.contains(teamMemberId)) {
      newAssignedTeamMembers.add(teamMemberId);
    }
    
    return copyWith(
      assignedTeamMembers: newAssignedTeamMembers);
  }
  
  /// إزالة عضو فريق مسؤول عن الحملة
  MarketingCampaign removeTeamMember(String teamMemberId) {
    if (assignedTeamMembers == null || !assignedTeamMembers!.contains(teamMemberId)) {
      return this;
    }
    
    final newAssignedTeamMembers = List<String>.from(assignedTeamMembers!);
    newAssignedTeamMembers.remove(teamMemberId);
    
    return copyWith(
      assignedTeamMembers: newAssignedTeamMembers);
  }
  
  /// الحصول على معدل التحويل (النقرات / المشاهدات)
  double getClickThroughRate() {
    if (viewsCount == 0) {
      return 0.0;
    }
    
    return (clicksCount / viewsCount) * 100;
  }
  
  /// الحصول على معدل التحويل (الاتصالات / النقرات)
  double getContactRate() {
    if (clicksCount == 0) {
      return 0.0;
    }
    
    return (contactsCount / clicksCount) * 100;
  }
  
  /// الحصول على معدل التحويل (العملاء المكتسبين / الاتصالات)
  double getLeadConversionRate() {
    if (contactsCount == 0) {
      return 0.0;
    }
    
    return (leadsCount / contactsCount) * 100;
  }
  
  /// الحصول على معدل التحويل (المبيعات / العملاء المكتسبين)
  double getSalesConversionRate() {
    if (leadsCount == 0) {
      return 0.0;
    }
    
    return (salesCount / leadsCount) * 100;
  }
  
  /// الحصول على العائد على الاستثمار
  double getReturnOnInvestment() {
    if (spentAmount == 0) {
      return 0.0;
    }
    
    return ((totalSales - spentAmount) / spentAmount) * 100;
  }
  
  /// الحصول على تكلفة الاكتساب
  double getCostPerAcquisition() {
    if (salesCount == 0) {
      return 0.0;
    }
    
    return spentAmount / salesCount;
  }
  
  /// الحصول على نسبة الميزانية المستخدمة
  double getBudgetUsagePercentage() {
    if (budget == 0) {
      return 0.0;
    }
    
    return (spentAmount / budget) * 100;
  }
  
  /// التحقق مما إذا كانت الحملة نشطة
  bool isActive() {
    return status == CampaignStatus.active;
  }
  
  /// التحقق مما إذا كانت الحملة منتهية
  bool isEnded() {
    if (endDate == null) {
      return false;
    }
    
    return DateTime.now().isAfter(endDate!);
  }

  @override
  List<Object?> get props => [
    id,
    companyId,
    name,
    description,
    type,
    status,
    startDate,
    endDate,
    budget,
    spentAmount,
    targetAudience,
    targetAreas,
    targetPropertyTypes,
    targetEstates,
    assignedTeamMembers,
    viewsCount,
    clicksCount,
    contactsCount,
    leadsCount,
    salesCount,
    totalSales,
    createdAt,
    updatedAt,
    createdBy,
    updatedBy,
    adLinks,
    adImages,
    adTexts,
    additionalInfo,
  ];
}
