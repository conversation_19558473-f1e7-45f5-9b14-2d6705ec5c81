import 'package:flutter/material.dart';
import 'package:kuwait_corners/presentation/pages/login_page.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// OnboardingPage تعرض سلسلة من الصفحات لتعريف المستخدم بميزات التطبيق.
/// بعد إتمام العملية أو تخطيها، يتم الانتقال إلى صفحة LoginPage.
class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage>
    with SingleTickerProviderStateMixin {
  // Controller للتحكم بانتقالات الـ PageView.
  final PageController _pageController = PageController();

  // متغيرات للتحكم بالرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _logoScaleAnimation;

  // مؤشر الصفحة الحالية.
  int currentPage = 0;

  // متغير للتحكم بحالة تحميل الصفحة
  bool _isLoading = false;

  // بيانات الـ onboarding لكل صفحة (صورة، عنوان، ونص).
  final List<Map<String, dynamic>> onboardingData = [
    {
      "image": "assets/images/onboarding1.png",
      "title": "مرحبا بك في KREA",
      "body": "اكتشف أفضل العقارات بسهولة ويسر.",
      "color": const Color(0xFF4A6572),
      "textColor": Colors.white,
    },
    {
      "image": "assets/images/onboarding2.png",
      "title": "اختر الخطة المناسبة",
      "body": "تصفح خطط الدفع المتنوعة التي تناسب احتياجاتك.",
      "color": const Color(0xFF0277BD),
      "textColor": Colors.white,
    },
    {
      "image": "assets/images/onboarding3.png",
      "title": "إتمام عملية الدفع",
      "body": "ادفع بأمان وسرعة باستخدام التحويل البنكي.",
      "color": const Color(0xFF00695C),
      "textColor": Colors.white,
    },
  ];

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutQuad));

    _logoScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.elasticOut));

    // بدء الرسوم المتحركة
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  // حفظ حالة إكمال الـ onboarding
  Future<void> _saveOnboardingCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboarding_completed', true);
  }

  /// ينتقل إلى الصفحة التالية. إذا كانت الصفحة الأخيرة، ينهي عملية الـ onboarding.
  void _onNext() {
    if (currentPage < onboardingData.length - 1) {
      _pageController.animateToPage(
        currentPage + 1,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut);
    } else {
      _onFinish();
    }
  }

  /// يتخطى عملية الـ onboarding وينهيها فوراً.
  void _onSkip() {
    _onFinish();
  }

  /// ينتقل إلى LoginPage ويزيل شاشة الـ onboarding من مسار التنقل.
  Future<void> _onFinish() async {
    setState(() {
      _isLoading = true;
    });

    // حفظ حالة إكمال الـ onboarding
    await _saveOnboardingCompleted();

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => const LoginPage()));
    }
  }

  /// تبني محتوى كل صفحة من صفحات الـ onboarding بتصميم عصري.
  Widget _buildPageContent(Map<String, dynamic> data) {
    // تحديد حجم الصورة بناءً على ارتفاع الشاشة بحيث تأخذ مساحة أكبر.
    final double imageSize = MediaQuery.of(context).size.height * 0.35;
    final Color pageColor = data["color"] as Color;
    final Color textColor = data["textColor"] as Color;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // عرض الصورة مع حواف دائرية وظل بسيط.
              Container(
                width: imageSize,
                height: imageSize,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.0),
                  boxShadow: [
                    BoxShadow(
                      color: pageColor.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 8)),
                  ]),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24.0),
                  child: Image.asset(
                    data["image"]!,
                    fit: BoxFit.cover))),
              const SizedBox(height: 40),
              // عرض العنوان.
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: pageColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16)),
                child: Text(
                  data["title"]!,
                  style: TextStyle(
                    fontSize: 24.0,
                    fontWeight: FontWeight.bold,
                    color: pageColor,
                    letterSpacing: 0.5),
                  textAlign: TextAlign.center)),
              const SizedBox(height: 16),
              // عرض النص الوصفي.
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  data["body"]!,
                  style: TextStyle(
                    fontSize: 16.0,
                    color: Colors.black87,
                    height: 1.5,
                    letterSpacing: 0.5),
                  textAlign: TextAlign.center)),
            ]))));
  }

  /// تبني مؤشر الصفحات المتحرك.
  Widget _buildDots() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(onboardingData.length, (index) {
        bool isActive = currentPage == index;
        Color pageColor = onboardingData[index]["color"] as Color;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 6.0),
          width: isActive ? 24.0 : 10.0,
          height: 10.0,
          decoration: BoxDecoration(
            color: isActive ? pageColor : Colors.grey.shade300,
            borderRadius: BorderRadius.circular(5),
            boxShadow: isActive
                ? [
                    BoxShadow(
                      color: pageColor.withOpacity(0.3),
                      blurRadius: 5,
                      offset: const Offset(0, 2)),
                  ]
                : null));
      }));
  }

  /// تبني أزرار التنقل (التالي/إنهاء وتخطي).
  Widget _buildNavigationButtons() {
    Color pageColor = onboardingData[currentPage]["color"] as Color;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // زر "تخطي" أو "السابق"
          if (currentPage > 0)
            TextButton.icon(
              onPressed: () {
                _pageController.previousPage(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut);

                // إعادة تشغيل الرسوم المتحركة
                _animationController.reset();
                _animationController.forward();
              },
              icon: const Icon(Icons.arrow_back_ios, size: 16),
              label: const Text("السابق"),
              style: TextButton.styleFrom(
                foregroundColor: pageColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 10.0)))
          else
            TextButton(
              onPressed: _onSkip,
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey.shade600,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 10.0)),
              child: const Text("تخطي")),

          // زر "التالي/إنهاء"
          Container(
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: pageColor.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4)),
              ]),
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _onNext,
              style: ElevatedButton.styleFrom(
                backgroundColor: pageColor,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25)),
                padding: const EdgeInsets.symmetric(
                  horizontal: 24.0,
                  vertical: 12.0)),
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2))
                  : Icon(
                      currentPage == onboardingData.length - 1
                          ? Icons.check
                          : Icons.arrow_forward,
                      size: 18,
                      color: Colors.white),
              label: Text(
                currentPage == onboardingData.length - 1
                    ? "ابدأ الآن"
                    : "التالي",
                style: const TextStyle(
                  fontSize: 16.0,
                  fontWeight: FontWeight.bold,
                  color: Colors.white)))),
        ]));
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على لون الصفحة الحالية
    Color pageColor = onboardingData[currentPage]["color"] as Color;

    return Scaffold(
      // خلفية الصفحة متدرجة
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Colors.grey.shade50,
              Colors.grey.shade100,
            ])),
        child: SafeArea(
          child: Column(
            children: [
              // شعار التطبيق مع تأثيرات حركية
              Padding(
                padding: const EdgeInsets.only(top: 32.0),
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _logoScaleAnimation.value,
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white,
                            border: Border.all(
                              color: pageColor.withValues(alpha: 0.2),
                              width: 3),
                            boxShadow: [
                              BoxShadow(
                                color: pageColor.withValues(alpha: 0.15),
                                blurRadius: 15,
                                offset: const Offset(0, 5)),
                            ]),
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Image.asset(
                              'assets/images/logo.png',
                              fit: BoxFit.contain)))));
                  })),

              // مؤشر الصفحات
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 24.0),
                child: _buildDots()),

              // محتوى الصفحات
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: onboardingData.length,
                  onPageChanged: (int page) {
                    setState(() {
                      currentPage = page;
                    });

                    // إعادة تشغيل الرسوم المتحركة
                    _animationController.reset();
                    _animationController.forward();
                  },
                  itemBuilder: (_, index) {
                    return _buildPageContent(onboardingData[index]);
                  })),

              // أزرار التنقل
              _buildNavigationButtons(),
            ]))));
  }
}
