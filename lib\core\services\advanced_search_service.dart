// lib/core/services/advanced_search_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';

import '../../domain/models/property_request/property_request_model.dart';
import '../../domain/models/search/search_filters_model.dart';
import '../../domain/models/search/search_suggestion_model.dart';

/// خدمة البحث المتقدم
class AdvancedSearchService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // كلمات مفتاحية شائعة للاقتراحات
  static const List<String> _commonKeywords = [
    'شقة', 'فيلا', 'منزل', 'مكتب', 'محل', 'مخزن', 'أرض',
    'السالمية', 'حولي', 'الفروانية', 'الأحمدي', 'الجهراء', 'مبارك الكبير',
    'مفروش', 'غير مفروش', 'تكييف مركزي', 'مسبح', 'مصعد', 'مرآب',
    'للبيع', 'للإيجار', 'جديد', 'مستعمل'
  ];

  /// البحث المتقدم في طلبات العقارات
  Future<List<PropertyRequestModel>> advancedSearch({
    String? query,
    SearchFiltersModel? filters,
    Position? userLocation,
    double? radiusKm,
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query baseQuery = _firestore.collection('property_requests')
          .where('status', isEqualTo: RequestStatus.open.index);

      // البحث النصي
      if (query != null && query.isNotEmpty) {
        final keywords = _extractKeywords(query);
        if (keywords.isNotEmpty) {
          baseQuery = baseQuery.where('searchKeywords', arrayContainsAny: keywords);
        }
      }

      // تطبيق الفلاتر
      if (filters != null) {
        baseQuery = _applyFilters(baseQuery, filters);
      }

      // ترتيب النتائج
      baseQuery = baseQuery.orderBy('createdAt', descending: true);

      // تحديد الحد الأقصى
      if (startAfter != null) {
        baseQuery = baseQuery.startAfterDocument(startAfter);
      }
      baseQuery = baseQuery.limit(limit);

      final snapshot = await baseQuery.get();
      List<PropertyRequestModel> results = snapshot.docs
          .map((doc) => PropertyRequestModel.fromFirestore(doc))
          .toList();

      // البحث الجغرافي إذا تم توفير الموقع
      if (userLocation != null && radiusKm != null) {
        results = await _filterByLocation(results, userLocation, radiusKm);
      }

      // ترتيب النتائج حسب الصلة
      if (query != null && query.isNotEmpty) {
        results = _rankByRelevance(results, query);
      }

      return results;
    } catch (e) {
      throw Exception('خطأ في البحث المتقدم: $e');
    }
  }

  /// البحث الصوتي
  Future<List<PropertyRequestModel>> voiceSearch(String voiceQuery) async {
    // تحويل النص الصوتي إلى كلمات مفتاحية
    final processedQuery = _processVoiceQuery(voiceQuery);
    
    return await advancedSearch(query: processedQuery);
  }

  /// الحصول على اقتراحات البحث
  Future<List<SearchSuggestionModel>> getSearchSuggestions(String query) async {
    final suggestions = <SearchSuggestionModel>[];
    
    if (query.isEmpty) {
      // اقتراحات افتراضية
      for (final keyword in _commonKeywords.take(5)) {
        suggestions.add(SearchSuggestionModel(
          text: keyword,
          type: SearchSuggestionType.keyword,
          frequency: 0,
        ));
      }
      return suggestions;
    }

    // البحث في الكلمات المفتاحية الشائعة
    final matchingKeywords = _commonKeywords
        .where((keyword) => keyword.contains(query))
        .take(3)
        .toList();

    for (final keyword in matchingKeywords) {
      suggestions.add(SearchSuggestionModel(
        text: keyword,
        type: SearchSuggestionType.keyword,
        frequency: 0,
      ));
    }

    // البحث في عناوين الطلبات السابقة
    try {
      final recentRequests = await _firestore
          .collection('property_requests')
          .where('searchKeywords', arrayContains: query.toLowerCase())
          .orderBy('createdAt', descending: true)
          .limit(3)
          .get();

      for (final doc in recentRequests.docs) {
        final request = PropertyRequestModel.fromFirestore(doc);
        suggestions.add(SearchSuggestionModel(
          text: request.title,
          type: SearchSuggestionType.recent,
          frequency: request.viewsCount,
        ));
      }
    } catch (e) {
      // تجاهل الأخطاء في الاقتراحات
    }

    return suggestions;
  }

  /// حفظ استعلام البحث للإحصائيات
  Future<void> saveSearchQuery(String query, int resultsCount) async {
    try {
      await _firestore.collection('search_analytics').add({
        'query': query,
        'resultsCount': resultsCount,
        'timestamp': FieldValue.serverTimestamp(),
        'keywords': _extractKeywords(query),
      });
    } catch (e) {
      // تجاهل أخطاء الإحصائيات
    }
  }

  /// استخراج الكلمات المفتاحية من النص
  List<String> _extractKeywords(String text) {
    final words = text.toLowerCase()
        .replaceAll(RegExp(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]'), ' ')
        .split(' ')
        .where((word) => word.length > 2)
        .toList();
    
    return words;
  }

  /// تطبيق الفلاتر على الاستعلام
  Query _applyFilters(Query query, SearchFiltersModel filters) {
    if (filters.propertyType != null) {
      query = query.where('propertyType', isEqualTo: filters.propertyType);
    }

    if (filters.minPrice != null) {
      query = query.where('minPrice', isGreaterThanOrEqualTo: filters.minPrice);
    }

    if (filters.maxPrice != null) {
      query = query.where('maxPrice', isLessThanOrEqualTo: filters.maxPrice);
    }

    if (filters.preferredLocations != null && filters.preferredLocations!.isNotEmpty) {
      query = query.where('preferredLocations', arrayContainsAny: filters.preferredLocations);
    }

    if (filters.minRooms != null) {
      query = query.where('minRooms', isGreaterThanOrEqualTo: filters.minRooms);
    }

    if (filters.minBathrooms != null) {
      query = query.where('minBathrooms', isGreaterThanOrEqualTo: filters.minBathrooms);
    }

    if (filters.minArea != null) {
      query = query.where('minArea', isGreaterThanOrEqualTo: filters.minArea);
    }

    // فلاتر المميزات
    if (filters.hasCentralAC == true) {
      query = query.where('hasCentralAC', isEqualTo: true);
    }

    if (filters.hasMaidRoom == true) {
      query = query.where('hasMaidRoom', isEqualTo: true);
    }

    if (filters.hasGarage == true) {
      query = query.where('hasGarage', isEqualTo: true);
    }

    if (filters.hasSwimmingPool == true) {
      query = query.where('hasSwimmingPool', isEqualTo: true);
    }

    if (filters.hasElevator == true) {
      query = query.where('hasElevator', isEqualTo: true);
    }

    if (filters.isFullyFurnished == true) {
      query = query.where('isFullyFurnished', isEqualTo: true);
    }

    return query;
  }

  /// فلترة النتائج حسب الموقع الجغرافي
  Future<List<PropertyRequestModel>> _filterByLocation(
    List<PropertyRequestModel> requests,
    Position userLocation,
    double radiusKm,
  ) async {
    // هذه دالة مبسطة - في التطبيق الحقيقي يجب استخدام GeoHash أو خدمة جغرافية متقدمة
    return requests; // مؤقتاً نعيد جميع النتائج
  }

  /// ترتيب النتائج حسب الصلة
  List<PropertyRequestModel> _rankByRelevance(
    List<PropertyRequestModel> requests,
    String query,
  ) {
    final keywords = _extractKeywords(query);
    
    requests.sort((a, b) {
      final scoreA = _calculateRelevanceScore(a, keywords);
      final scoreB = _calculateRelevanceScore(b, keywords);
      return scoreB.compareTo(scoreA);
    });

    return requests;
  }

  /// حساب نقاط الصلة
  double _calculateRelevanceScore(PropertyRequestModel request, List<String> keywords) {
    double score = 0.0;
    
    final titleWords = _extractKeywords(request.title);
    final descriptionWords = _extractKeywords(request.description);
    
    for (final keyword in keywords) {
      // نقاط إضافية للعنوان
      if (titleWords.contains(keyword)) {
        score += 3.0;
      }
      
      // نقاط للوصف
      if (descriptionWords.contains(keyword)) {
        score += 1.0;
      }
      
      // نقاط لنوع العقار
      if (request.propertyType.toLowerCase().contains(keyword)) {
        score += 2.0;
      }
      
      // نقاط للمواقع
      for (final location in request.preferredLocations) {
        if (location.toLowerCase().contains(keyword)) {
          score += 1.5;
        }
      }
    }
    
    // نقاط إضافية للطلبات الحديثة والشائعة
    final daysSinceCreated = DateTime.now().difference(request.createdAt).inDays;
    score += (30 - daysSinceCreated.clamp(0, 30)) * 0.1; // نقاط للحداثة
    score += request.viewsCount * 0.01; // نقاط للشعبية
    
    return score;
  }

  /// معالجة النص الصوتي
  String _processVoiceQuery(String voiceQuery) {
    // تحويل الأرقام المنطوقة إلى أرقام
    String processed = voiceQuery
        .replaceAll('واحد', '1')
        .replaceAll('اثنان', '2')
        .replaceAll('ثلاثة', '3')
        .replaceAll('أربعة', '4')
        .replaceAll('خمسة', '5')
        .replaceAll('ستة', '6')
        .replaceAll('سبعة', '7')
        .replaceAll('ثمانية', '8')
        .replaceAll('تسعة', '9')
        .replaceAll('عشرة', '10');

    // تحويل المرادفات
    processed = processed
        .replaceAll('بيت', 'منزل')
        .replaceAll('دار', 'منزل')
        .replaceAll('عمارة', 'شقة')
        .replaceAll('استوديو', 'شقة');

    return processed;
  }
}
