import 'package:equatable/equatable.dart';

/// كيان التقييم العقاري
class Valuation extends Equatable {
  final String id;
  final String requestId;
  final String providerId;
  final String providerName;
  final String userId;
  final String? estateId;
  final String valuationType;
  final double valuationAmount;
  final double minValue;
  final double maxValue;
  final double confidence;
  final String propertyType;
  final String area;
  final double size;
  final String address;
  final double? latitude;
  final double? longitude;
  final int? rooms;
  final int? bathrooms;
  final int? age;
  final bool? isFurnished;
  final bool? isRenovated;
  final List<String>? features;
  final List<ValuationFactor> factors;
  final String certificateId;
  final String certificateUrl;
  final String verificationCode;
  final DateTime valuationDate;
  final DateTime expiryDate;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان التقييم العقاري
  const Valuation({
    required this.id,
    required this.requestId,
    required this.providerId,
    required this.providerName,
    required this.userId,
    this.estateId,
    required this.valuationType,
    required this.valuationAmount,
    required this.minValue,
    required this.maxValue,
    required this.confidence,
    required this.propertyType,
    required this.area,
    required this.size,
    required this.address,
    this.latitude,
    this.longitude,
    this.rooms,
    this.bathrooms,
    this.age,
    this.isFurnished,
    this.isRenovated,
    this.features,
    required this.factors,
    required this.certificateId,
    required this.certificateUrl,
    required this.verificationCode,
    required this.valuationDate,
    required this.expiryDate,
    this.additionalInfo,
  });

  /// إنشاء كيان التقييم العقاري من JSON
  factory Valuation.fromJson(Map<String, dynamic> json) {
    return Valuation(
      id: json['id'] as String,
      requestId: json['requestId'] as String,
      providerId: json['providerId'] as String,
      providerName: json['providerName'] as String,
      userId: json['userId'] as String,
      estateId: json['estateId'] as String?,
      valuationType: json['valuationType'] as String,
      valuationAmount: json['valuationAmount'] as double,
      minValue: json['minValue'] as double,
      maxValue: json['maxValue'] as double,
      confidence: json['confidence'] as double,
      propertyType: json['propertyType'] as String,
      area: json['area'] as String,
      size: json['size'] as double,
      address: json['address'] as String,
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      rooms: json['rooms'] as int?,
      bathrooms: json['bathrooms'] as int?,
      age: json['age'] as int?,
      isFurnished: json['isFurnished'] as bool?,
      isRenovated: json['isRenovated'] as bool?,
      features: json['features'] != null
          ? List<String>.from(json['features'] as List)
          : null,
      factors: (json['factors'] as List)
          .map((factor) => ValuationFactor.fromJson(factor))
          .toList(),
      certificateId: json['certificateId'] as String,
      certificateUrl: json['certificateUrl'] as String,
      verificationCode: json['verificationCode'] as String,
      valuationDate: DateTime.parse(json['valuationDate'] as String),
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان التقييم العقاري إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'requestId': requestId,
      'providerId': providerId,
      'providerName': providerName,
      'userId': userId,
      'estateId': estateId,
      'valuationType': valuationType,
      'valuationAmount': valuationAmount,
      'minValue': minValue,
      'maxValue': maxValue,
      'confidence': confidence,
      'propertyType': propertyType,
      'area': area,
      'size': size,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'rooms': rooms,
      'bathrooms': bathrooms,
      'age': age,
      'isFurnished': isFurnished,
      'isRenovated': isRenovated,
      'features': features,
      'factors': factors.map((factor) => factor.toJson()).toList(),
      'certificateId': certificateId,
      'certificateUrl': certificateUrl,
      'verificationCode': verificationCode,
      'valuationDate': valuationDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان التقييم العقاري مع تعديل بعض الخصائص
  Valuation copyWith({
    String? id,
    String? requestId,
    String? providerId,
    String? providerName,
    String? userId,
    String? estateId,
    String? valuationType,
    double? valuationAmount,
    double? minValue,
    double? maxValue,
    double? confidence,
    String? propertyType,
    String? area,
    double? size,
    String? address,
    double? latitude,
    double? longitude,
    int? rooms,
    int? bathrooms,
    int? age,
    bool? isFurnished,
    bool? isRenovated,
    List<String>? features,
    List<ValuationFactor>? factors,
    String? certificateId,
    String? certificateUrl,
    String? verificationCode,
    DateTime? valuationDate,
    DateTime? expiryDate,
    Map<String, dynamic>? additionalInfo,
  }) {
    return Valuation(
      id: id ?? this.id,
      requestId: requestId ?? this.requestId,
      providerId: providerId ?? this.providerId,
      providerName: providerName ?? this.providerName,
      userId: userId ?? this.userId,
      estateId: estateId ?? this.estateId,
      valuationType: valuationType ?? this.valuationType,
      valuationAmount: valuationAmount ?? this.valuationAmount,
      minValue: minValue ?? this.minValue,
      maxValue: maxValue ?? this.maxValue,
      confidence: confidence ?? this.confidence,
      propertyType: propertyType ?? this.propertyType,
      area: area ?? this.area,
      size: size ?? this.size,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      rooms: rooms ?? this.rooms,
      bathrooms: bathrooms ?? this.bathrooms,
      age: age ?? this.age,
      isFurnished: isFurnished ?? this.isFurnished,
      isRenovated: isRenovated ?? this.isRenovated,
      features: features ?? this.features,
      factors: factors ?? this.factors,
      certificateId: certificateId ?? this.certificateId,
      certificateUrl: certificateUrl ?? this.certificateUrl,
      verificationCode: verificationCode ?? this.verificationCode,
      valuationDate: valuationDate ?? this.valuationDate,
      expiryDate: expiryDate ?? this.expiryDate,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// التحقق مما إذا كان التقييم ساري المفعول
  bool isValid() {
    return DateTime.now().isBefore(expiryDate);
  }

  /// الحصول على المدة المتبقية لصلاحية التقييم بالأيام
  int getRemainingDays() {
    final now = DateTime.now();
    return expiryDate.difference(now).inDays;
  }

  /// حساب سعر المتر المربع
  double getPricePerSquareMeter() {
    return valuationAmount / size;
  }

  @override
  List<Object?> get props => [
        id,
        requestId,
        providerId,
        providerName,
        userId,
        estateId,
        valuationType,
        valuationAmount,
        minValue,
        maxValue,
        confidence,
        propertyType,
        area,
        size,
        address,
        latitude,
        longitude,
        rooms,
        bathrooms,
        age,
        isFurnished,
        isRenovated,
        features,
        factors,
        certificateId,
        certificateUrl,
        verificationCode,
        valuationDate,
        expiryDate,
        additionalInfo,
      ];
}

/// كيان عامل التقييم العقاري
class ValuationFactor extends Equatable {
  final String name;
  final dynamic value;
  final double impact;
  final String description;

  /// إنشاء كيان عامل التقييم العقاري
  const ValuationFactor({
    required this.name,
    required this.value,
    required this.impact,
    required this.description,
  });

  /// إنشاء كيان عامل التقييم العقاري من JSON
  factory ValuationFactor.fromJson(Map<String, dynamic> json) {
    return ValuationFactor(
      name: json['name'] as String,
      value: json['value'],
      impact: json['impact'] as double,
      description: json['description'] as String);
  }

  /// تحويل كيان عامل التقييم العقاري إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'value': value,
      'impact': impact,
      'description': description,
    };
  }

  /// نسخ كيان عامل التقييم العقاري مع تعديل بعض الخصائص
  ValuationFactor copyWith({
    String? name,
    dynamic value,
    double? impact,
    String? description,
  }) {
    return ValuationFactor(
      name: name ?? this.name,
      value: value ?? this.value,
      impact: impact ?? this.impact,
      description: description ?? this.description);
  }

  @override
  List<Object?> get props => [
        name,
        value,
        impact,
        description,
      ];
}
