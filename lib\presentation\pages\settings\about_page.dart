import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/core/services/settings_service.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutPage extends StatefulWidget {
  const AboutPage({super.key});

  @override
  State<AboutPage> createState() => _AboutPageState();
}

class _AboutPageState extends State<AboutPage> {
  final SettingsService _settingsService = SettingsService();
  Map<String, String> _appInfo = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAppInfo();
  }

  Future<void> _loadAppInfo() async {
    await _settingsService.initialize();
    final info = await _settingsService.getAppInfo();
    setState(() {
      _appInfo = info;
      _isLoading = false;
    });
  }

  Widget _buildInfoCard({
    required String title,
    required String value,
    required IconData icon,
    Color? iconColor,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (iconColor ?? AppColors.primary).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: iconColor ?? AppColors.primary),
        ),
        title: Text(title, style: CairoTextStyles.titleMedium),
        subtitle: Text(
          value,
          style: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey.shade600),
        ),
      ),
    );
  }

  Widget _buildLinkCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (iconColor ?? AppColors.primary).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: iconColor ?? AppColors.primary),
        ),
        title: Text(title, style: CairoTextStyles.titleMedium),
        subtitle: Text(
          subtitle,
          style: CairoTextStyles.bodySmall.copyWith(color: Colors.grey.shade600),
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا يمكن فتح الرابط')),
        );
      }
    }
  }

  Future<void> _checkForUpdates() async {
    // محاكاة التحقق من التحديثات
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري التحقق من التحديثات...'),
          ],
        ),
      ),
    );

    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      Navigator.pop(context);
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('التحديثات', style: CairoTextStyles.titleLarge),
          content: const Text('أنت تستخدم أحدث إصدار من التطبيق'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('حول التطبيق', style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              children: [
                // شعار التطبيق
                Container(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    children: [
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          gradient: AppColors.primaryGradient,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(
                          Icons.home_work,
                          size: 50,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Krea',
                        style: CairoTextStyles.headlineLarge.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'تطبيق العقارات الأول في الكويت',
                        style: CairoTextStyles.bodyLarge.copyWith(
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // معلومات التطبيق
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'معلومات التطبيق',
                    style: CairoTextStyles.headlineSmall.copyWith(color: AppColors.primary),
                  ),
                ),
                _buildInfoCard(
                  title: 'الإصدار',
                  value: _appInfo['version'] ?? '1.0.0',
                  icon: Icons.info,
                  iconColor: AppColors.info,
                ),
                _buildInfoCard(
                  title: 'رقم البناء',
                  value: _appInfo['buildNumber'] ?? '1',
                  icon: Icons.build,
                  iconColor: AppColors.secondary,
                ),
                _buildInfoCard(
                  title: 'اسم الحزمة',
                  value: _appInfo['packageName'] ?? 'com.krea.app',
                  icon: Icons.inventory_2,
                  iconColor: AppColors.warning,
                ),

                const SizedBox(height: 16),

                // الروابط والدعم
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'الدعم والروابط',
                    style: CairoTextStyles.headlineSmall.copyWith(color: AppColors.primary),
                  ),
                ),
                _buildLinkCard(
                  title: 'التحقق من التحديثات',
                  subtitle: 'البحث عن إصدارات جديدة',
                  icon: Icons.system_update,
                  iconColor: AppColors.success,
                  onTap: _checkForUpdates,
                ),
                _buildLinkCard(
                  title: 'موقعنا الإلكتروني',
                  subtitle: 'زيارة الموقع الرسمي',
                  icon: Icons.language,
                  iconColor: AppColors.info,
                  onTap: () => _launchUrl('https://krea.com'),
                ),
                _buildLinkCard(
                  title: 'تواصل معنا',
                  subtitle: '<EMAIL>',
                  icon: Icons.email,
                  iconColor: AppColors.secondary,
                  onTap: () => _launchUrl('mailto:<EMAIL>'),
                ),
                _buildLinkCard(
                  title: 'الدعم الفني',
                  subtitle: '+965 9929 8821',
                  icon: Icons.phone,
                  iconColor: AppColors.success,
                  onTap: () => _launchUrl('tel:+96599298821'),
                ),

                const SizedBox(height: 16),

                // وسائل التواصل الاجتماعي
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'تابعنا على',
                    style: CairoTextStyles.headlineSmall.copyWith(color: AppColors.primary),
                  ),
                ),
                _buildLinkCard(
                  title: 'فيسبوك',
                  subtitle: 'تابعنا على فيسبوك',
                  icon: Icons.facebook,
                  iconColor: const Color(0xFF1877F2),
                  onTap: () => _launchUrl('https://facebook.com/krea'),
                ),
                _buildLinkCard(
                  title: 'تويتر',
                  subtitle: 'تابعنا على تويتر',
                  icon: Icons.alternate_email,
                  iconColor: const Color(0xFF1DA1F2),
                  onTap: () => _launchUrl('https://twitter.com/krea'),
                ),
                _buildLinkCard(
                  title: 'إنستغرام',
                  subtitle: 'تابعنا على إنستغرام',
                  icon: Icons.camera_alt,
                  iconColor: const Color(0xFFE4405F),
                  onTap: () => _launchUrl('https://instagram.com/krea'),
                ),

                const SizedBox(height: 16),

                // حقوق الطبع والنشر
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Text(
                        '© 2024 Krea. جميع الحقوق محفوظة.',
                        style: CairoTextStyles.bodySmall.copyWith(
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'صُنع بـ ❤️ في الكويت',
                        style: CairoTextStyles.bodySmall.copyWith(
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),
              ],
            ),
    );
  }
}
