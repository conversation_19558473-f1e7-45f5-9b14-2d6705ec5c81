import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// حالة الموعد
enum AppointmentStatus {
  /// موعد مجدول
  scheduled,
  
  /// موعد مؤكد
  confirmed,
  
  /// موعد ملغي
  cancelled,
  
  /// موعد مكتمل
  completed,
  
  /// موعد مؤجل
  postponed,
}

/// نموذج للمواعيد
class Appointment extends Equatable {
  /// معرف الموعد
  final String id;
  
  /// معرف المستخدم المالك للموعد (الوسيط أو الشركة)
  final String ownerId;

  /// معرف الوسيط (alias لـ ownerId للتوافق مع الكود القديم)
  String get agentId => ownerId;
  
  /// معرف العميل
  final String clientId;
  
  /// اسم العميل
  final String clientName;
  
  /// رقم هاتف العميل
  final String clientPhone;
  
  /// معرف العقار
  final String? estateId;
  
  /// عنوان العقار
  final String? estateTitle;
  
  /// عنوان الموعد
  final String title;
  
  /// وصف الموعد
  final String? description;
  
  /// تاريخ ووقت الموعد
  final DateTime dateTime;
  
  /// مدة الموعد بالدقائق
  final int duration;
  
  /// حالة الموعد
  final AppointmentStatus status;
  
  /// موقع الموعد
  final String? location;
  
  /// خط العرض
  final double? latitude;
  
  /// خط الطول
  final double? longitude;
  
  /// ملاحظات الموعد
  final String? notes;
  
  /// تاريخ إنشاء الموعد
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث للموعد
  final DateTime? updatedAt;
  
  /// معرف المستخدم الذي أنشأ الموعد
  final String createdBy;
  
  /// معرف المستخدم الذي قام بآخر تحديث للموعد
  final String? updatedBy;
  
  /// ما إذا كان الموعد تم إرسال تذكير له
  final bool reminderSent;
  
  /// وقت إرسال التذكير
  final DateTime? reminderSentAt;
  
  /// ما إذا كان الموعد تم تأكيده من قبل العميل
  final bool confirmedByClient;
  
  /// وقت تأكيد الموعد من قبل العميل
  final DateTime? confirmedByClientAt;
  
  /// سبب إلغاء الموعد
  final String? cancellationReason;
  
  /// معلومات إضافية
  final Map<String, dynamic>? additionalInfo;

  const Appointment({
    required this.id,
    required this.ownerId,
    required this.clientId,
    required this.clientName,
    required this.clientPhone,
    this.estateId,
    this.estateTitle,
    required this.title,
    this.description,
    required this.dateTime,
    required this.duration,
    required this.status,
    this.location,
    this.latitude,
    this.longitude,
    this.notes,
    required this.createdAt,
    this.updatedAt,
    required this.createdBy,
    this.updatedBy,
    this.reminderSent = false,
    this.reminderSentAt,
    this.confirmedByClient = false,
    this.confirmedByClientAt,
    this.cancellationReason,
    this.additionalInfo,
  });

  /// إنشاء نسخة معدلة من الموعد
  Appointment copyWith({
    String? id,
    String? ownerId,
    String? clientId,
    String? clientName,
    String? clientPhone,
    String? estateId,
    String? estateTitle,
    String? title,
    String? description,
    DateTime? dateTime,
    int? duration,
    AppointmentStatus? status,
    String? location,
    double? latitude,
    double? longitude,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    bool? reminderSent,
    DateTime? reminderSentAt,
    bool? confirmedByClient,
    DateTime? confirmedByClientAt,
    String? cancellationReason,
    Map<String, dynamic>? additionalInfo,
  }) {
    return Appointment(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      clientPhone: clientPhone ?? this.clientPhone,
      estateId: estateId ?? this.estateId,
      estateTitle: estateTitle ?? this.estateTitle,
      title: title ?? this.title,
      description: description ?? this.description,
      dateTime: dateTime ?? this.dateTime,
      duration: duration ?? this.duration,
      status: status ?? this.status,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      reminderSent: reminderSent ?? this.reminderSent,
      reminderSentAt: reminderSentAt ?? this.reminderSentAt,
      confirmedByClient: confirmedByClient ?? this.confirmedByClient,
      confirmedByClientAt: confirmedByClientAt ?? this.confirmedByClientAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }
  
  /// تحويل الموعد إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'ownerId': ownerId,
      'clientId': clientId,
      'clientName': clientName,
      'clientPhone': clientPhone,
      'estateId': estateId,
      'estateTitle': estateTitle,
      'title': title,
      'description': description,
      'dateTime': Timestamp.fromDate(dateTime),
      'duration': duration,
      'status': status.toString().split('.').last,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'reminderSent': reminderSent,
      'reminderSentAt': reminderSentAt != null ? Timestamp.fromDate(reminderSentAt!) : null,
      'confirmedByClient': confirmedByClient,
      'confirmedByClientAt': confirmedByClientAt != null ? Timestamp.fromDate(confirmedByClientAt!) : null,
      'cancellationReason': cancellationReason,
      'additionalInfo': additionalInfo,
    };
  }
  
  /// إنشاء موعد من Map
  factory Appointment.fromMap(Map<String, dynamic> map) {
    return Appointment(
      id: map['id'] ?? '',
      ownerId: map['ownerId'] ?? '',
      clientId: map['clientId'] ?? '',
      clientName: map['clientName'] ?? '',
      clientPhone: map['clientPhone'] ?? '',
      estateId: map['estateId'],
      estateTitle: map['estateTitle'],
      title: map['title'] ?? '',
      description: map['description'],
      dateTime: map['dateTime'] is Timestamp 
          ? (map['dateTime'] as Timestamp).toDate() 
          : DateTime.now(),
      duration: map['duration'] ?? 60,
      status: _getAppointmentStatusFromString(map['status'] ?? 'scheduled'),
      location: map['location'],
      latitude: map['latitude'],
      longitude: map['longitude'],
      notes: map['notes'],
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
      createdBy: map['createdBy'] ?? '',
      updatedBy: map['updatedBy'],
      reminderSent: map['reminderSent'] ?? false,
      reminderSentAt: map['reminderSentAt'] is Timestamp 
          ? (map['reminderSentAt'] as Timestamp).toDate() 
          : null,
      confirmedByClient: map['confirmedByClient'] ?? false,
      confirmedByClientAt: map['confirmedByClientAt'] is Timestamp 
          ? (map['confirmedByClientAt'] as Timestamp).toDate() 
          : null,
      cancellationReason: map['cancellationReason'],
      additionalInfo: map['additionalInfo']);
  }
  
  /// إنشاء موعد من DocumentSnapshot
  factory Appointment.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return Appointment.fromMap(data);
  }
  
  /// الحصول على حالة الموعد من النص
  static AppointmentStatus _getAppointmentStatusFromString(String statusStr) {
    switch (statusStr) {
      case 'confirmed':
        return AppointmentStatus.confirmed;
      case 'cancelled':
        return AppointmentStatus.cancelled;
      case 'completed':
        return AppointmentStatus.completed;
      case 'postponed':
        return AppointmentStatus.postponed;
      default:
        return AppointmentStatus.scheduled;
    }
  }
  
  /// الحصول على اسم حالة الموعد بالعربية
  String getAppointmentStatusName() {
    switch (status) {
      case AppointmentStatus.confirmed:
        return 'مؤكد';
      case AppointmentStatus.cancelled:
        return 'ملغي';
      case AppointmentStatus.completed:
        return 'مكتمل';
      case AppointmentStatus.postponed:
        return 'مؤجل';
      case AppointmentStatus.scheduled:
        return 'مجدول';
    }
  }
  
  /// الحصول على لون حالة الموعد
  String getAppointmentStatusColor() {
    switch (status) {
      case AppointmentStatus.confirmed:
        return '#4CAF50'; // أخضر
      case AppointmentStatus.cancelled:
        return '#F44336'; // أحمر
      case AppointmentStatus.completed:
        return '#2196F3'; // أزرق
      case AppointmentStatus.postponed:
        return '#FF9800'; // برتقالي
      case AppointmentStatus.scheduled:
        return '#9E9E9E'; // رمادي
    }
  }
  
  /// تأكيد الموعد
  Appointment confirm(String updatedBy) {
    return copyWith(
      status: AppointmentStatus.confirmed,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy);
  }
  
  /// إلغاء الموعد
  Appointment cancel(String updatedBy, String reason) {
    return copyWith(
      status: AppointmentStatus.cancelled,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy,
      cancellationReason: reason);
  }
  
  /// إكمال الموعد
  Appointment complete(String updatedBy) {
    return copyWith(
      status: AppointmentStatus.completed,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy);
  }
  
  /// تأجيل الموعد
  Appointment postpone(String updatedBy, DateTime newDateTime) {
    return copyWith(
      status: AppointmentStatus.postponed,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy,
      dateTime: newDateTime);
  }
  
  /// إعادة جدولة الموعد
  Appointment reschedule(String updatedBy, DateTime newDateTime) {
    return copyWith(
      status: AppointmentStatus.scheduled,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy,
      dateTime: newDateTime);
  }
  
  /// تأكيد الموعد من قبل العميل
  Appointment confirmByClient() {
    return copyWith(
      confirmedByClient: true,
      confirmedByClientAt: DateTime.now(),
      updatedAt: DateTime.now());
  }
  
  /// إرسال تذكير للموعد
  Appointment sendReminder() {
    return copyWith(
      reminderSent: true,
      reminderSentAt: DateTime.now(),
      updatedAt: DateTime.now());
  }
  
  /// التحقق مما إذا كان الموعد قادم
  bool isUpcoming() {
    return dateTime.isAfter(DateTime.now()) && 
        (status == AppointmentStatus.scheduled || status == AppointmentStatus.confirmed);
  }
  
  /// التحقق مما إذا كان الموعد اليوم
  bool isToday() {
    final now = DateTime.now();
    return dateTime.year == now.year && 
        dateTime.month == now.month && 
        dateTime.day == now.day;
  }
  
  /// الحصول على وقت انتهاء الموعد
  DateTime getEndTime() {
    return dateTime.add(Duration(minutes: duration));
  }

  @override
  List<Object?> get props => [
    id,
    ownerId,
    clientId,
    clientName,
    clientPhone,
    estateId,
    estateTitle,
    title,
    description,
    dateTime,
    duration,
    status,
    location,
    latitude,
    longitude,
    notes,
    createdAt,
    updatedAt,
    createdBy,
    updatedBy,
    reminderSent,
    reminderSentAt,
    confirmedByClient,
    confirmedByClientAt,
    cancellationReason,
    additionalInfo,
  ];
}
