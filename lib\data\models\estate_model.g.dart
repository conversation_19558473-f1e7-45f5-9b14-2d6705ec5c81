// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'estate_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EstateModel _$EstateModelFromJson(Map<String, dynamic> json) => EstateModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      location: json['location'] as String,
      photoUrls:
          (json['photoUrls'] as List<dynamic>).map((e) => e as String).toList(),
      isFeatured: json['isFeatured'] as bool,
      planType: json['planType'] as String,
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      mainCategory: json['mainCategory'] as String?,
      subCategory: json['subCategory'] as String?,
      postedByUserType: json['postedByUserType'] as String?,
      hidePhone: json['hidePhone'] as bool? ?? false,
      extraPhones: (json['extraPhones'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      shareLocation: json['shareLocation'] as bool? ?? false,
      lat: (json['lat'] as num?)?.toDouble(),
      lng: (json['lng'] as num?)?.toDouble(),
      hasCentralAC: json['hasCentralAC'] as bool? ?? false,
      hasSecurity: json['hasSecurity'] as bool?,
      allowPets: json['allowPets'] as bool?,
      hasElevator: json['hasElevator'] as bool?,
      hasSwimmingPool: json['hasSwimmingPool'] as bool?,
      hasMaidRoom: json['hasMaidRoom'] as bool? ?? false,
      hasGarage: json['hasGarage'] as bool? ?? false,
      hasBalcony: json['hasBalcony'] as bool?,
      isFullyFurnished: json['isFullyFurnished'] as bool?,
      rebound: json['rebound'] as String?,
      numberOfRooms: (json['numberOfRooms'] as num?)?.toInt(),
      internalLocation: json['internalLocation'] as String?,
      salon: json['salon'] as String?,
      area: (json['area'] as num?)?.toDouble(),
      floorNumber: (json['floorNumber'] as num?)?.toInt(),
      numberOfBathrooms: (json['numberOfBathrooms'] as num?)?.toInt(),
      buildingAge: (json['buildingAge'] as num?)?.toInt(),
      numberOfFloors: (json['numberOfFloors'] as num?)?.toInt(),
      propertyType: json['propertyType'] as String?,
      usageType: json['usageType'] as String?,
      autoRepublish: json['autoRepublish'] as bool? ?? false,
      kuwaitCornersPin: json['kuwaitCornersPin'] as bool? ?? false,
      movingAd: json['movingAd'] as bool? ?? false,
      vipBadge: json['vipBadge'] as bool? ?? false,
      pinnedOnHome: json['pinnedOnHome'] as bool? ?? false,
      discountCode: json['discountCode'] as String?,
      advertiserImage: json['advertiserImage'] as String?,
      advertiserName: json['advertiserName'] as String?,
      advertiserEmail: json['advertiserEmail'] as String?,
      advertiserRegistrationDate: json['advertiserRegistrationDate'] == null
          ? null
          : DateTime.parse(json['advertiserRegistrationDate'] as String),
      advertiserAdsCount: (json['advertiserAdsCount'] as num?)?.toInt(),
      ownerId: json['ownerId'] as String?,
      originalEstateId: json['originalEstateId'] as String?,
      isOriginal: json['isOriginal'] as bool? ?? true,
      copiedBy: (json['copiedBy'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isPaymentVerified: json['isPaymentVerified'] as bool? ?? false,
    );

Map<String, dynamic> _$EstateModelToJson(EstateModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'price': instance.price,
      'location': instance.location,
      'photoUrls': instance.photoUrls,
      'isFeatured': instance.isFeatured,
      'planType': instance.planType,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'mainCategory': instance.mainCategory,
      'subCategory': instance.subCategory,
      'postedByUserType': instance.postedByUserType,
      'hidePhone': instance.hidePhone,
      'extraPhones': instance.extraPhones,
      'shareLocation': instance.shareLocation,
      'lat': instance.lat,
      'lng': instance.lng,
      'hasCentralAC': instance.hasCentralAC,
      'hasSecurity': instance.hasSecurity,
      'allowPets': instance.allowPets,
      'hasElevator': instance.hasElevator,
      'hasSwimmingPool': instance.hasSwimmingPool,
      'hasMaidRoom': instance.hasMaidRoom,
      'hasGarage': instance.hasGarage,
      'hasBalcony': instance.hasBalcony,
      'isFullyFurnished': instance.isFullyFurnished,
      'rebound': instance.rebound,
      'numberOfRooms': instance.numberOfRooms,
      'internalLocation': instance.internalLocation,
      'salon': instance.salon,
      'area': instance.area,
      'floorNumber': instance.floorNumber,
      'numberOfBathrooms': instance.numberOfBathrooms,
      'buildingAge': instance.buildingAge,
      'numberOfFloors': instance.numberOfFloors,
      'propertyType': instance.propertyType,
      'usageType': instance.usageType,
      'advertiserImage': instance.advertiserImage,
      'advertiserName': instance.advertiserName,
      'advertiserEmail': instance.advertiserEmail,
      'advertiserRegistrationDate':
          instance.advertiserRegistrationDate?.toIso8601String(),
      'advertiserAdsCount': instance.advertiserAdsCount,
      'autoRepublish': instance.autoRepublish,
      'kuwaitCornersPin': instance.kuwaitCornersPin,
      'movingAd': instance.movingAd,
      'vipBadge': instance.vipBadge,
      'pinnedOnHome': instance.pinnedOnHome,
      'discountCode': instance.discountCode,
      'ownerId': instance.ownerId,
      'originalEstateId': instance.originalEstateId,
      'isOriginal': instance.isOriginal,
      'copiedBy': instance.copiedBy,
      'isPaymentVerified': instance.isPaymentVerified,
    };
