import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/topic_model.dart';
import '../../../domain/models/forum/filter_options_model.dart';
import '../../../core/enums/view_modes.dart';
import 'modern_topic_card.dart';

/// قائمة عرض مواضيع المنتدى الحديثة
class ModernTopicsList extends StatelessWidget {
  /// قائمة المواضيع
  final List<TopicModel> topics;

  /// دالة يتم استدعاؤها عند النقر على موضوع
  final Function(TopicModel) onTopicTap;

  /// دالة يتم استدعاؤها عند النقر على زر الإعجاب
  final Function(TopicModel)? onLikeTap;

  /// دالة يتم استدعاؤها عند النقر على زر الحفظ
  final Function(TopicModel)? onBookmarkTap;

  /// دالة يتم استدعاؤها عند النقر على زر المشاركة
  final Function(TopicModel)? onShareTap;

  /// معرف المستخدم الحالي
  final String? currentUserId;

  /// خيارات الفلترة الحالية
  final ForumFilterOptionsModel? filterOptions;

  /// نمط العرض (قائمة أو شبكة)
  final TopicViewMode viewMode;

  /// ما إذا كان يتم تحميل المزيد من المواضيع
  final bool isLoadingMore;

  /// دالة يتم استدعاؤها عند التمرير للأسفل لتحميل المزيد
  final VoidCallback? onLoadMore;

  /// متحكم التمرير
  final ScrollController? scrollController;

  /// ما إذا كانت البطاقات مصغرة
  final bool isCompact;

  /// ما إذا كان يتم عرض الفئات
  final bool showCategories;

  const ModernTopicsList({
    super.key,
    required this.topics,
    required this.onTopicTap,
    this.onLikeTap,
    this.onBookmarkTap,
    this.onShareTap,
    this.currentUserId,
    this.filterOptions,
    this.viewMode = TopicViewMode.list,
    this.isLoadingMore = false,
    this.onLoadMore,
    this.scrollController,
    this.isCompact = false,
    this.showCategories = true,
  });

  @override
  Widget build(BuildContext context) {
    if (topics.isEmpty) {
      return _buildEmptyView();
    }

    return AnimationLimiter(
      child: viewMode == TopicViewMode.grid
          ? _buildGridView()
          : viewMode == TopicViewMode.compact
              ? _buildCompactListView()
              : _buildListView());
  }

  /// بناء عرض فارغ
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.forum_outlined,
            size: 64,
            color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد مواضيع',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600)),
          const SizedBox(height: 8),
          Text(
            'كن أول من يضيف موضوعاً جديداً',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600)),
        ]));
  }

  /// بناء عرض القائمة
  Widget _buildListView() {
    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(8),
      itemCount: topics.length + (isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == topics.length) {
          return _buildLoadingIndicator();
        }

        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 375),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: _buildTopicCard(topics[index]))));
      });
  }

  /// بناء عرض القائمة المصغرة
  Widget _buildCompactListView() {
    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(8),
      itemCount: topics.length + (isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == topics.length) {
          return _buildLoadingIndicator();
        }

        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 375),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: _buildTopicCard(topics[index], isCompact: true))));
      });
  }

  /// بناء عرض الشبكة
  Widget _buildGridView() {
    return GridView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8),
      itemCount: topics.length + (isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == topics.length) {
          return _buildLoadingIndicator();
        }

        return AnimationConfiguration.staggeredGrid(
          position: index,
          duration: const Duration(milliseconds: 375),
          columnCount: 2,
          child: ScaleAnimation(
            child: FadeInAnimation(
              child: _buildTopicCard(topics[index], isCompact: true))));
      });
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingIndicator() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: CircularProgressIndicator(
          color: AppColors.primary)));
  }

  /// بناء بطاقة الموضوع
  Widget _buildTopicCard(TopicModel topic, {bool isCompact = false}) {
    final isLiked = topic.likedBy?.contains(currentUserId) ?? false;
    final isBookmarked = topic.bookmarkedBy?.contains(currentUserId) ?? false;

    return ModernTopicCard(
      topic: topic,
      onTap: () => onTopicTap(topic),
      onLikeTap: onLikeTap != null ? () => onLikeTap!(topic) : null,
      onBookmarkTap: onBookmarkTap != null ? () => onBookmarkTap!(topic) : null,
      onShareTap: onShareTap != null ? () => onShareTap!(topic) : null,
      isLiked: isLiked,
      isBookmarked: isBookmarked,
      isCompact: isCompact || this.isCompact);
  }
}
