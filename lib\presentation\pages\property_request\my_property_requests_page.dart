// lib/presentation/pages/property_request/my_property_requests_page.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../domain/services/user_interface_customization_service.dart';
import '../../../core/constants/user_types.dart';
import 'create_property_request_page.dart';
import 'property_request_details_page.dart';

/// صفحة طلبات العقارات الخاصة بالمستخدم الحالي
class MyPropertyRequestsPage extends StatefulWidget {
  const MyPropertyRequestsPage({super.key});

  @override
  State<MyPropertyRequestsPage> createState() => _MyPropertyRequestsPageState();
}

class _MyPropertyRequestsPageState extends State<MyPropertyRequestsPage> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = true;
  List<Map<String, dynamic>> _myRequests = [];
  String? _currentUserId;
  bool _isUserSeeker = false;

  @override
  void initState() {
    super.initState();
    _checkUserTypeAndAccess();
    _scrollController.addListener(_onScroll);
  }

  /// التحقق من نوع المستخدم والصلاحية للوصول لهذه الصفحة
  Future<void> _checkUserTypeAndAccess() async {
    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      final userTypeString = userType.toString().split('.').last;

      setState(() {
        _isUserSeeker = UserTypeConstants.canCreatePropertyRequests(userTypeString);
      });

      if (_isUserSeeker) {
        _getCurrentUser();
      } else {
        // إذا لم يكن المستخدم باحث عن عقار، إعادة توجيه للصفحة الرئيسية
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'هذه الصفحة متاحة للباحثين عن العقارات فقط',
                style: GoogleFonts.cairo()),
              backgroundColor: Colors.red));
        }
      }
    } catch (e) {
      // في حالة الخطأ، إعادة توجيه للصفحة الرئيسية
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/');
      }
    }
  }

  /// الحصول على المستخدم الحالي
  void _getCurrentUser() {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      _currentUserId = user.uid;
      _loadMyRequests();
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل طلبات المستخدم الحالي
  Future<void> _loadMyRequests() async {
    if (_currentUserId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final querySnapshot = await FirebaseFirestore.instance
          .collection('property_requests')
          .where('userId', isEqualTo: _currentUserId)
          .orderBy('createdAt', descending: true)
          .get();

      final requests = querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();

      setState(() {
        _myRequests = requests;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading my requests: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// التعامل مع التمرير
  void _onScroll() {
    // يمكن إضافة منطق التحميل التدريجي هنا إذا لزم الأمر
  }

  /// حذف طلب عقار
  Future<void> _deleteRequest(String requestId) async {
    try {
      await FirebaseFirestore.instance
          .collection('property_requests')
          .doc(requestId)
          .delete();

      // إزالة الطلب من القائمة المحلية
      setState(() {
        _myRequests.removeWhere((request) => request['id'] == requestId);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم حذف الطلب بنجاح',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.green));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في حذف الطلب',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red));
      }
    }
  }

  /// عرض حوار تأكيد الحذف
  void _showDeleteConfirmation(String requestId, String title) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
        content: Text(
          'هل أنت متأكد من حذف طلب "$title"؟',
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo())),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteRequest(requestId);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(
              'حذف',
              style: GoogleFonts.cairo())),
        ]));
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'طلباتي',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white)),
        backgroundColor: Colors.green,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white)),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreatePropertyRequestPage()));

          // إعادة تحميل الطلبات إذا تم إنشاء طلب جديد
          if (result == true) {
            _loadMyRequests();
          }
        },
        backgroundColor: Colors.green,
        child: const Icon(Icons.add)));
  }

  Widget _buildBody() {
    if (_currentUserId == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.login,
              size: 64,
              color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'يجب تسجيل الدخول لعرض طلباتك',
              style: GoogleFonts.cairo(
                fontSize: 18,
                color: Colors.grey[600])),
          ]));
    }

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.green)));
    }

    if (_myRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_outlined,
              size: 64,
              color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد طلبات عقارات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600])),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإنشاء طلب عقار جديد',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[500])),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CreatePropertyRequestPage()));

                if (result == true) {
                  _loadMyRequests();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white),
              child: Text(
                'إنشاء طلب جديد',
                style: GoogleFonts.cairo())),
          ]));
    }

    return RefreshIndicator(
      onRefresh: _loadMyRequests,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _myRequests.length,
        itemBuilder: (context, index) {
          final request = _myRequests[index];
          return _buildRequestCard(request);
        }));
  }

  Widget _buildRequestCard(Map<String, dynamic> request) {
    final title = request['title'] ?? 'بدون عنوان';
    final description = request['description'] ?? '';
    final propertyType = request['propertyType'] ?? '';
    final budget = request['budget']?.toString() ?? '0';
    final createdAt = request['createdAt'] as Timestamp?;
    final isActive = request['isActive'] ?? true;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PropertyRequestDetailsPage(
                requestId: request['id'])));
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold))),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'delete') {
                        _showDeleteConfirmation(request['id'], title);
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            const Icon(Icons.delete, color: Colors.red),
                            const SizedBox(width: 8),
                            Text(
                              'حذف',
                              style: GoogleFonts.cairo(color: Colors.red)),
                          ])),
                    ]),
                ]),
              if (description.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  description,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[600]),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis),
              ],
              const SizedBox(height: 12),
              Row(
                children: [
                  if (propertyType.isNotEmpty) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8)),
                      child: Text(
                        propertyType,
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.green[700]))),
                    const SizedBox(width: 8),
                  ],
                  if (budget != '0') ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8)),
                      child: Text(
                        '$budget د.ك',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.blue[700]))),
                  ],
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4),
                    decoration: BoxDecoration(
                      color: isActive
                          ? Colors.green.withOpacity(0.1)
                          : Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8)),
                    child: Text(
                      isActive ? 'نشط' : 'غير نشط',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: isActive ? Colors.green[700] : Colors.grey[600]))),
                ]),
              if (createdAt != null) ...[
                const SizedBox(height: 8),
                Text(
                  'تاريخ الإنشاء: ${_formatDate(createdAt.toDate())}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey[500])),
              ],
            ]))));
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
