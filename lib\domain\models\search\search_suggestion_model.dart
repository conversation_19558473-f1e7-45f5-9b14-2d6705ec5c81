// lib/domain/models/search/search_suggestion_model.dart
import 'package:equatable/equatable.dart';

/// أنواع اقتراحات البحث
enum SearchSuggestionType {
  /// كلمة مفتاحية
  keyword,
  
  /// بحث حديث
  recent,
  
  /// بحث شائع
  popular,
  
  /// اقتراح تلقائي
  autocomplete,
  
  /// موقع جغرافي
  location,
  
  /// نوع عقار
  propertyType,
}

/// نموذج اقتراح البحث
class SearchSuggestionModel extends Equatable {
  /// النص المقترح
  final String text;

  /// نوع الاقتراح
  final SearchSuggestionType type;

  /// تكرار البحث (للترتيب)
  final int frequency;

  /// معرف إضافي (مثل معرف الموقع)
  final String? id;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  /// أيقونة الاقتراح
  final String? icon;

  /// وصف إضافي
  final String? description;

  const SearchSuggestionModel({
    required this.text,
    required this.type,
    this.frequency = 0,
    this.id,
    this.metadata,
    this.icon,
    this.description,
  });

  /// إنشاء اقتراح كلمة مفتاحية
  factory SearchSuggestionModel.keyword(String keyword, {int frequency = 0}) {
    return SearchSuggestionModel(
      text: keyword,
      type: SearchSuggestionType.keyword,
      frequency: frequency,
      icon: 'search',
    );
  }

  /// إنشاء اقتراح بحث حديث
  factory SearchSuggestionModel.recent(String query, {int frequency = 0}) {
    return SearchSuggestionModel(
      text: query,
      type: SearchSuggestionType.recent,
      frequency: frequency,
      icon: 'history',
      description: 'بحث سابق',
    );
  }

  /// إنشاء اقتراح بحث شائع
  factory SearchSuggestionModel.popular(String query, {required int frequency}) {
    return SearchSuggestionModel(
      text: query,
      type: SearchSuggestionType.popular,
      frequency: frequency,
      icon: 'trending_up',
      description: 'بحث شائع',
    );
  }

  /// إنشاء اقتراح موقع
  factory SearchSuggestionModel.location(
    String locationName, {
    String? locationId,
    double? latitude,
    double? longitude,
  }) {
    return SearchSuggestionModel(
      text: locationName,
      type: SearchSuggestionType.location,
      id: locationId,
      icon: 'location_on',
      description: 'موقع',
      metadata: {
        if (latitude != null) 'latitude': latitude,
        if (longitude != null) 'longitude': longitude,
      },
    );
  }

  /// إنشاء اقتراح نوع عقار
  factory SearchSuggestionModel.propertyType(String propertyType) {
    return SearchSuggestionModel(
      text: propertyType,
      type: SearchSuggestionType.propertyType,
      icon: _getPropertyTypeIcon(propertyType),
      description: 'نوع عقار',
    );
  }

  /// الحصول على أيقونة نوع العقار
  static String _getPropertyTypeIcon(String propertyType) {
    switch (propertyType) {
      case 'شقة':
        return 'apartment';
      case 'فيلا':
      case 'منزل':
        return 'home';
      case 'أرض':
        return 'landscape';
      case 'مكتب':
        return 'business';
      case 'محل تجاري':
        return 'storefront';
      case 'مخزن':
        return 'warehouse';
      default:
        return 'home_work';
    }
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'text': text,
      'type': type.index,
      'frequency': frequency,
      'id': id,
      'metadata': metadata,
      'icon': icon,
      'description': description,
    };
  }

  /// إنشاء من Map
  factory SearchSuggestionModel.fromMap(Map<String, dynamic> map) {
    return SearchSuggestionModel(
      text: map['text'] ?? '',
      type: SearchSuggestionType.values[map['type'] ?? 0],
      frequency: map['frequency'] ?? 0,
      id: map['id'],
      metadata: map['metadata'] != null 
          ? Map<String, dynamic>.from(map['metadata'])
          : null,
      icon: map['icon'],
      description: map['description'],
    );
  }

  /// إنشاء نسخة محدثة
  SearchSuggestionModel copyWith({
    String? text,
    SearchSuggestionType? type,
    int? frequency,
    String? id,
    Map<String, dynamic>? metadata,
    String? icon,
    String? description,
  }) {
    return SearchSuggestionModel(
      text: text ?? this.text,
      type: type ?? this.type,
      frequency: frequency ?? this.frequency,
      id: id ?? this.id,
      metadata: metadata ?? this.metadata,
      icon: icon ?? this.icon,
      description: description ?? this.description,
    );
  }

  /// الحصول على لون الاقتراح
  String get colorCode {
    switch (type) {
      case SearchSuggestionType.keyword:
        return '#2196F3'; // أزرق
      case SearchSuggestionType.recent:
        return '#9E9E9E'; // رمادي
      case SearchSuggestionType.popular:
        return '#FF5722'; // برتقالي
      case SearchSuggestionType.autocomplete:
        return '#4CAF50'; // أخضر
      case SearchSuggestionType.location:
        return '#F44336'; // أحمر
      case SearchSuggestionType.propertyType:
        return '#9C27B0'; // بنفسجي
    }
  }

  /// الحصول على أولوية الاقتراح للترتيب
  int get priority {
    switch (type) {
      case SearchSuggestionType.recent:
        return 5;
      case SearchSuggestionType.popular:
        return 4;
      case SearchSuggestionType.location:
        return 3;
      case SearchSuggestionType.propertyType:
        return 2;
      case SearchSuggestionType.keyword:
        return 1;
      case SearchSuggestionType.autocomplete:
        return 0;
    }
  }

  @override
  List<Object?> get props => [
        text,
        type,
        frequency,
        id,
        metadata,
        icon,
        description,
      ];

  @override
  String toString() {
    return 'SearchSuggestionModel(text: $text, type: $type, frequency: $frequency)';
  }
}
