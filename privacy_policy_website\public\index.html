<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق Krea - منصة العقارات الرائدة في الكويت</title>
    <meta name="description" content="تطبيق Krea - منصة العقارات الرائدة في دولة الكويت لبيع وشراء وتأجير العقارات بطريقة آمنة وسهلة">
    <meta name="keywords" content="عقارات الكويت، بيع عقارات، شراء عقارات، تأجير عقارات، منازل للبيع، شقق للإيجار">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.8rem;
            font-weight: 800;
        }

        .logo i {
            margin-left: 10px;
            font-size: 2rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: opacity 0.3s;
        }

        .nav-links a:hover {
            opacity: 0.8;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.95;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary {
            background: white;
            color: #4CAF50;
            border: 2px solid white;
        }

        .btn-primary:hover {
            background: #4CAF50;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary:hover {
            background: white;
            color: #4CAF50;
            border: 2px solid white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* Fix for link buttons in cards */
        .link-card .btn {
            margin-top: 10px;
            display: inline-flex;
        }

        .link-card .btn-primary {
            background: #4CAF50;
            color: white;
            border: 2px solid #4CAF50;
        }

        .link-card .btn-primary:hover {
            background: #45a049;
            border: 2px solid #45a049;
        }

        .link-card .btn-secondary {
            background: transparent;
            color: #e74c3c;
            border: 2px solid #e74c3c;
        }

        .link-card .btn-secondary:hover {
            background: #e74c3c;
            color: white;
            border: 2px solid #e74c3c;
        }

        /* Features Section */
        .features {
            padding: 80px 0;
            background: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }

        .section-title p {
            font-size: 1.2rem;
            color: #666;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Stats Section */
        .stats {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 60px 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            text-align: center;
        }

        .stat-item h3 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .stat-item p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Links Section */
        .links-section {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .link-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .link-card:hover {
            transform: translateY(-5px);
        }

        .link-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .link-card h3 {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        .link-card p {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        /* Footer */
        .footer {
            background: #333;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: white;
            text-decoration: none;
            transition: opacity 0.3s;
        }

        .footer-links a:hover {
            opacity: 0.8;
        }

        .contact-info {
            margin-bottom: 2rem;
        }

        .contact-info p {
            margin-bottom: 0.5rem;
        }

        .email, .phone-number {
            direction: ltr;
            display: inline-block;
            unicode-bidi: bidi-override;
            text-align: left;
        }

        /* Mobile Menu */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .nav-links.mobile-open {
            display: flex;
            flex-direction: column;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            padding: 1rem 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero {
                padding: 140px 0 80px;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .features-grid,
            .links-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">
                <i class="fas fa-home"></i>
                Krea
            </div>
            <nav>
                <ul class="nav-links" id="navLinks">
                    <li><a href="#features">المميزات</a></li>
                    <li><a href="#about">عن التطبيق</a></li>
                    <li><a href="#links">الروابط المهمة</a></li>
                    <li><a href="#contact">اتصل بنا</a></li>
                </ul>
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>تطبيق Krea</h1>
            <p>منصة العقارات الرائدة في دولة الكويت لبيع وشراء وتأجير العقارات بطريقة آمنة وسهلة</p>
            <div class="cta-buttons">
                <a href="#" class="btn btn-primary">
                    <i class="fab fa-google-play"></i>
                    تحميل من Google Play
                </a>
                <a href="#features" class="btn btn-secondary">
                    <i class="fas fa-info-circle"></i>
                    اعرف المزيد
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-title">
                <h2>مميزات تطبيق Krea</h2>
                <p>اكتشف لماذا يختار الآلاف تطبيق Krea لاحتياجاتهم العقارية</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3>عقارات متنوعة</h3>
                    <p>منازل، شقق، مكاتب، أراضي، ومخازن في جميع أنحاء الكويت مع خيارات البيع والإيجار</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>بحث متقدم</h3>
                    <p>فلترة دقيقة حسب المنطقة، السعر، النوع، والمساحة للعثور على العقار المثالي</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>أنواع مستخدمين متعددة</h3>
                    <p>باحثون، مستثمرون، ملاك عقارات، وشركات عقارية - كل نوع له واجهة مخصصة</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>أمان وموثوقية</h3>
                    <p>نظام دفع آمن عبر خدمة ومض الكويتية مع تشفير البيانات وحماية الخصوصية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3>منتدى عقاري</h3>
                    <p>مناقشات ونصائح عقارية من خبراء ومستخدمين آخرين في مجتمع Krea</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>إدارة المشاريع</h3>
                    <p>أدوات متقدمة لإدارة المشاريع العقارية والفرق والمستندات للشركات</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>10,000+</h3>
                    <p>عقار متاح</p>
                </div>
                <div class="stat-item">
                    <h3>5,000+</h3>
                    <p>مستخدم نشط</p>
                </div>
                <div class="stat-item">
                    <h3>1,000+</h3>
                    <p>صفقة ناجحة</p>
                </div>
                <div class="stat-item">
                    <h3>24/7</h3>
                    <p>دعم فني</p>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="features">
        <div class="container">
            <div class="section-title">
                <h2>عن تطبيق Krea</h2>
                <p>منصة العقارات الأكثر ثقة في دولة الكويت</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3>جودة عالية</h3>
                    <p>جميع العقارات محققة ومراجعة من قبل فريقنا المتخصص لضمان الجودة والمصداقية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>سرعة في الخدمة</h3>
                    <p>استجابة سريعة للاستفسارات ومعالجة فورية للطلبات والمعاملات</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3>ثقة ومصداقية</h3>
                    <p>نظام تقييم شفاف ومراجعات حقيقية من المستخدمين لضمان الثقة المتبادلة</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Links Section -->
    <section id="links" class="links-section">
        <div class="container">
            <div class="section-title">
                <h2>الروابط المهمة</h2>
                <p>معلومات مهمة حول الخصوصية وإدارة حسابك</p>
            </div>
            <div class="links-grid">
                <div class="link-card">
                    <div class="link-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>سياسة الخصوصية</h3>
                    <p>اطلع على كيفية جمع واستخدام وحماية بياناتك الشخصية في تطبيق Krea</p>
                    <a href="privacy-policy.html" class="btn btn-primary">
                        <i class="fas fa-eye"></i>
                        عرض سياسة الخصوصية
                    </a>
                </div>
                <div class="link-card">
                    <div class="link-icon">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <h3>حذف الحساب</h3>
                    <p>إذا كنت تريد حذف حسابك وجميع بياناتك نهائياً من تطبيق Krea</p>
                    <a href="delete-account.html" class="btn btn-secondary">
                        <i class="fas fa-trash-alt"></i>
                        طلب حذف الحساب
                    </a>
                </div>
                <div class="link-card">
                    <div class="link-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>الدعم الفني</h3>
                    <p>تحتاج مساعدة؟ فريق الدعم الفني متاح 24/7 لمساعدتك في أي استفسار</p>
                    <a href="mailto:<EMAIL>" class="btn btn-primary">
                        <i class="fas fa-envelope"></i>
                        تواصل مع الدعم
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="privacy-policy.html">سياسة الخصوصية</a>
                <a href="delete-account.html">حذف الحساب</a>
                <a href="mailto:<EMAIL>">الدعم الفني</a>
                <a href="mailto:<EMAIL>">اتصل بنا</a>
            </div>

            <div class="contact-info">
                <p><strong>البريد الإلكتروني:</strong> <span class="email"><EMAIL></span></p>
                <p><strong>الهاتف:</strong> <span class="phone-number">+965 9929 8821</span></p>
                <p><strong>الموقع:</strong> دولة الكويت</p>
            </div>

            <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #555;">
                <p>© 2025 تطبيق Krea. جميع الحقوق محفوظة.</p>
                <p>منصة العقارات الرائدة في دولة الكويت</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const navLinks = document.getElementById('navLinks');

        mobileMenuBtn.addEventListener('click', () => {
            navLinks.classList.toggle('mobile-open');
            const icon = mobileMenuBtn.querySelector('i');
            if (navLinks.classList.contains('mobile-open')) {
                icon.className = 'fas fa-times';
            } else {
                icon.className = 'fas fa-bars';
            }
        });

        // Close mobile menu when clicking on a link
        const navLinkItems = navLinks.querySelectorAll('a');
        navLinkItems.forEach(link => {
            link.addEventListener('click', () => {
                navLinks.classList.remove('mobile-open');
                mobileMenuBtn.querySelector('i').className = 'fas fa-bars';
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(76, 175, 80, 0.95)';
                header.style.backdropFilter = 'blur(10px)';
            } else {
                header.style.background = 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)';
                header.style.backdropFilter = 'none';
            }
        });
    </script>
</body>
</html>
