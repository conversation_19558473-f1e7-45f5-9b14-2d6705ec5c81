import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/services/referral_tracking_service.dart';
import 'package:kuwait_corners/core/constants/user_types.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Importing BLoC and state files as well as the Home and Onboarding pages.
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import 'home_page.dart';
import 'login_page.dart';
import 'onboarding_page.dart';
import 'user_type_selection_page.dart';

/// The SplashPage is the application's splash screen that displays the app logo.
/// After 3 seconds, it checks the authentication state:
/// - If the user is authenticated, navigates to HomePage.
/// - Otherwise, navigates to OnboardingPage.
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  // متغيرات لتخزين معلومات الإحالة من الرابط الديناميكي
  String? _referralCode;
  String? _referralId;

  // خدمة تتبع الإحالات
  final ReferralTrackingService _trackingService = ReferralTrackingService();

  @override
  void initState() {
    super.initState();

    // إعداد AnimationController البسيط للشعار
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // انيميشن الظهور التدريجي
    _fadeAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    // انيميشن التكبير مع تأثير مرن
    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    // بدء الانيميشن
    _controller.forward();

    // معالجة الروابط الديناميكية
    _initDynamicLinks();

    // Start a timer for 3 seconds before checking authentication status.
    Timer(const Duration(seconds: 3), () async {
      try {
        // التحقق من حالة المصادقة الحالية
        final currentUser = FirebaseAuth.instance.currentUser;

        // التحقق من حالة "تذكرني"
        final prefs = await SharedPreferences.getInstance();
        final bool rememberMe = prefs.getBool('rememberMe') ?? false;

        // التحقق من حالة إكمال الـ onboarding
        final bool onboardingCompleted =
            prefs.getBool('onboarding_completed') ?? false;

        if (currentUser != null && currentUser.emailVerified && rememberMe) {
          // إذا كان المستخدم مسجل الدخول وتم التحقق من بريده الإلكتروني وتم تفعيل "تذكرني"
          // نقوم بتحديث حالة المصادقة في AuthBloc والانتقال إلى الصفحة الرئيسية
          _fetchUserTypeAndNavigate(currentUser);
        } else if (currentUser != null &&
            currentUser.emailVerified &&
            !rememberMe) {
          // إذا كان المستخدم مسجل الدخول ولكن لم يتم تفعيل "تذكرني"
          // نقوم بتسجيل الخروج والانتقال إلى صفحة تسجيل الدخول
          await FirebaseAuth.instance.signOut();
          if (mounted) {
            if (onboardingCompleted) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (_) => const LoginPage()));
            } else {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (_) => const OnboardingPage()));
            }
          }
        } else {
          // إذا لم يكن المستخدم مسجل الدخول
          if (mounted) {
            if (onboardingCompleted) {
              // إذا تم إكمال الـ onboarding، ننتقل إلى صفحة تسجيل الدخول
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (_) => const LoginPage()));
            } else {
              // إذا لم يتم إكمال الـ onboarding، ننتقل إلى صفحة الترحيب
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (_) => const OnboardingPage()));
            }
          }
        }
      } catch (e) {
        // في حالة حدوث أي خطأ، ننتقل إلى صفحة الترحيب
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (_) => const OnboardingPage()));
        }
      }
    });
  }

  // تم حذف دالة الانيميشنز المعقدة لتبسيط التصميم

  /// معالجة الروابط العميقة
  Future<void> _initDynamicLinks() async {
    try {
      // 1. محاولة معالجة الروابط الديناميكية (Firebase Dynamic Links)
      // ملاحظة: Firebase Dynamic Links سيتم إيقافه في 25 أغسطس 2025
      try {
        final PendingDynamicLinkData? initialLink =
            await FirebaseDynamicLinks.instance.getInitialLink();

        if (initialLink != null) {
          final Uri deepLink = initialLink.link;
          _handleDeepLink(deepLink);
        }

        // الاستماع للروابط الديناميكية عندما يكون التطبيق مفتوحًا
        FirebaseDynamicLinks.instance.onLink.listen(
          (dynamicLinkData) {
            final Uri deepLink = dynamicLinkData.link;
            _handleDeepLink(deepLink);
          },
          onError: (error) {
            // تجاهل الخطأ
          });
      } catch (e) {
        // تجاهل أخطاء Firebase Dynamic Links
      }

      // 2. معالجة الروابط المباشرة (Custom URL Scheme)
      // هذا يعمل فقط في تطبيقات الويب، ولكن نضيفه للاكتمال
      try {
        final initialUri = Uri.base;
        if (initialUri.scheme == 'kuwaitcorners') {
          _handleDeepLink(initialUri);
        }
      } catch (e) {
        // تجاهل الخطأ
      }

      // 3. التحقق من وجود معلومات إحالة معلقة باستخدام خدمة التتبع
      try {
        final pendingReferral = await _trackingService.checkPendingReferral();
        final pendingReferralCode = pendingReferral['referralCode'];
        final pendingReferralId = pendingReferral['referralId'];

        if (pendingReferralCode != null && pendingReferralCode.isNotEmpty) {
          _referralCode = pendingReferralCode;
          _referralId = pendingReferralId;

          // تسجيل تثبيت التطبيق
          await _trackingService.logInstallation();

          // إذا كان المستخدم غير مسجل الدخول، توجيهه إلى صفحة اختيار نوع المستخدم
          if (FirebaseAuth.instance.currentUser == null && mounted) {
            // تأخير التوجيه لضمان اكتمال تحميل الصفحة
            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted) {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                      builder: (_) => const UserTypeSelectionPage()));
              }
            });
          } else if (FirebaseAuth.instance.currentUser != null) {
            // إذا كان المستخدم مسجل الدخول، إكمال عملية الإحالة
            await _trackingService.completeReferral();
          }
        }
      } catch (e) {
        // تجاهل الخطأ
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// معالجة الرابط العميق
  void _handleDeepLink(Uri deepLink) {
    // التحقق من وجود معلمة رمز الإحالة
    if (deepLink.queryParameters.containsKey('code')) {
      _referralCode = deepLink.queryParameters['code'];

      // التحقق من وجود معلمة معرف الإحالة
      if (deepLink.queryParameters.containsKey('id')) {
        _referralId = deepLink.queryParameters['id'];
      }

      // حفظ معلومات الإحالة في التخزين المحلي لاستخدامها لاحقًا
      _saveReferralInfo(_referralCode!, _referralId);

      // تسجيل تثبيت التطبيق
      _trackingService.logInstallation();

      // إذا كان المستخدم غير مسجل الدخول، توجيهه إلى صفحة اختيار نوع المستخدم
      if (FirebaseAuth.instance.currentUser == null && mounted) {
        // تأخير التوجيه لضمان اكتمال تحميل الصفحة
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (_) => const UserTypeSelectionPage()));
          }
        });
      } else if (FirebaseAuth.instance.currentUser != null) {
        // إذا كان المستخدم مسجل الدخول، إكمال عملية الإحالة
        _trackingService.completeReferral();
      }
    }
  }

  /// حفظ معلومات الإحالة في التخزين المحلي
  Future<void> _saveReferralInfo(String code, String? id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('pending_referral_code', code);
      if (id != null) {
        await prefs.setString('pending_referral_id', id);
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// جلب نوع المستخدم من Firestore والانتقال إلى الصفحة المناسبة
  Future<void> _fetchUserTypeAndNavigate(User currentUser) async {
    try {
      debugPrint('=== Splash Page - Fetching User Type ===');
      debugPrint('User ID: ${currentUser.uid}');

      // جلب نوع المستخدم من Firestore
      String userType = UserTypeConstants.seeker; // افتراضي

      try {
        final doc = await FirebaseFirestore.instance
            .collection('users')
            .doc(currentUser.uid)
            .get();

        if (doc.exists) {
          final userData = doc.data()!;
          debugPrint('User data from Firestore: $userData');

          // البحث عن نوع المستخدم في الحقول المختلفة وتطبيعه
          final rawUserType = userData['userType'] as String? ??
                             userData['type'] as String? ??
                             UserTypeConstants.seeker;
          userType = UserTypeConstants.normalizeUserType(rawUserType);

          debugPrint('User type found: $userType');
        } else {
          debugPrint('User document does not exist, using default type: seeker');
        }
      } catch (firestoreError) {
        debugPrint('Error fetching user type from Firestore: $firestoreError');
        // استخدام النوع الافتراضي في حالة الخطأ
      }

      if (mounted) {
        // تحديث حالة المصادقة في AuthBloc مع نوع المستخدم الصحيح
        context.read<AuthBloc>().add(LoginSuccessEvent(currentUser, userType));

        // الانتقال إلى الصفحة الرئيسية
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (_) => const HomePage()));
      }
    } catch (e) {
      debugPrint('Error in _fetchUserTypeAndNavigate: $e');
      // في حالة حدوث خطأ، ننتقل إلى صفحة الترحيب
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (_) => const OnboardingPage()));
      }
    }
  }

  @override
  void dispose() {
    // Dispose the AnimationController to free up resources.
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Image.asset(
                  'assets/images/logo.png',
                  width: 200,
                  height: 200,
                  fit: BoxFit.contain,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
