import 'package:flutter/material.dart';
import 'app_colors.dart';

/// أنماط النصوص في التطبيق
class AppTextStyles {
  /// نمط العنوان الرئيسي
  static const TextStyle headline1 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: Colors.black);

  /// نمط العنوان الثانوي
  static const TextStyle headline2 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: Colors.black);

  /// نمط العنوان الثالث
  static const TextStyle headline3 = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: Colors.black);

  /// نمط العنوان الرابع
  static const TextStyle headline4 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: Colors.black);

  /// نمط العنوان الخامس
  static const TextStyle headline5 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.bold,
    color: Colors.black);

  /// نمط العنوان السادس
  static const TextStyle headline6 = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    color: Colors.black);

  /// نمط النص الأساسي
  static const TextStyle bodyText1 = TextStyle(
    fontSize: 16,
    color: Colors.black87);

  /// نمط النص الثانوي
  static const TextStyle bodyText2 = TextStyle(
    fontSize: 14,
    color: Colors.black87);

  /// نمط النص الصغير
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    color: Colors.black54);

  /// نمط النص الأصغر
  static const TextStyle overline = TextStyle(
    fontSize: 10,
    color: Colors.black54,
    letterSpacing: 0.5);

  /// نمط زر الإجراء
  static const TextStyle button = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.bold,
    color: Colors.white);

  /// نمط عنوان البطاقة
  static TextStyle cardTitle = const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: Colors.black);

  /// نمط وصف البطاقة
  static TextStyle cardSubtitle = TextStyle(
    fontSize: 14,
    color: Colors.grey.shade600);

  /// نمط نص البطاقة
  static TextStyle cardBody = TextStyle(
    fontSize: 14,
    color: Colors.grey.shade800);

  /// نمط عنوان القسم
  static TextStyle sectionTitle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: AppColors.primary);

  /// نمط وصف القسم
  static TextStyle sectionSubtitle = TextStyle(
    fontSize: 14,
    color: Colors.grey.shade600);

  /// نمط نص الخطأ
  static const TextStyle error = TextStyle(
    fontSize: 14,
    color: Colors.red);

  /// نمط نص النجاح
  static const TextStyle success = TextStyle(
    fontSize: 14,
    color: Colors.green);

  /// نمط نص التحذير
  static const TextStyle warning = TextStyle(
    fontSize: 14,
    color: Colors.orange);

  /// نمط نص المعلومات
  static const TextStyle info = TextStyle(
    fontSize: 14,
    color: Colors.blue);
}
