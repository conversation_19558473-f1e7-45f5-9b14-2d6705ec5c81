import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../domain/entities/virtual_tour.dart';
import '../../domain/repositories/virtual_tour_repository.dart';

/// تطبيق مستودع الجولات الافتراضية
class VirtualTourRepositoryImpl implements VirtualTourRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  @override
  Future<List<VirtualTour>> getEstateVirtualTours(String estateId) async {
    try {
      final snapshot = await _firestore
          .collection('virtualTours')
          .where('estateId', isEqualTo: estateId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => VirtualTour.fromSnapshot(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الجولات الافتراضية: $e');
    }
  }

  @override
  Future<VirtualTour?> getVirtualTourById(String tourId) async {
    try {
      final doc = await _firestore
          .collection('virtualTours')
          .doc(tourId)
          .get();

      if (doc.exists) {
        return VirtualTour.fromSnapshot(doc);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب الجولة الافتراضية: $e');
    }
  }

  @override
  Future<String> addVirtualTour(VirtualTour tour, {File? thumbnailFile}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لإضافة جولة افتراضية');
      }

      // رفع الصورة المصغرة إذا تم توفيرها
      String? thumbnailUrl;
      if (thumbnailFile != null) {
        thumbnailUrl = await uploadThumbnailImage(
          thumbnailFile, 
          tour.estateId, 
          DateTime.now().millisecondsSinceEpoch.toString()
        );
      }

      // إنشاء الجولة مع الصورة المصغرة
      final tourWithThumbnail = tour.copyWith(
        createdBy: user.uid,
        thumbnailUrl: thumbnailUrl,
        createdAt: DateTime.now(),
      );

      // حفظ الجولة في Firestore
      final docRef = await _firestore
          .collection('virtualTours')
          .add(tourWithThumbnail.toMap());

      return docRef.id;
    } catch (e) {
      throw Exception('فشل في إضافة الجولة الافتراضية: $e');
    }
  }

  @override
  Future<void> updateVirtualTour(VirtualTour tour, {File? newThumbnailFile}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لتحديث الجولة الافتراضية');
      }

      // التحقق من ملكية الجولة
      final existingTour = await getVirtualTourById(tour.id);
      if (existingTour == null) {
        throw Exception('الجولة الافتراضية غير موجودة');
      }
      if (existingTour.createdBy != user.uid) {
        throw Exception('ليس لديك صلاحية لتحديث هذه الجولة');
      }

      // رفع صورة مصغرة جديدة إذا تم توفيرها
      String? thumbnailUrl = tour.thumbnailUrl;
      if (newThumbnailFile != null) {
        // حذف الصورة القديمة
        if (existingTour.thumbnailUrl != null) {
          await _deleteImageFromStorage(existingTour.thumbnailUrl!);
        }
        
        // رفع الصورة الجديدة
        thumbnailUrl = await uploadThumbnailImage(
          newThumbnailFile, 
          tour.estateId, 
          tour.id
        );
      }

      // تحديث الجولة
      final updatedTour = tour.copyWith(
        thumbnailUrl: thumbnailUrl,
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('virtualTours')
          .doc(tour.id)
          .update(updatedTour.toMap());
    } catch (e) {
      throw Exception('فشل في تحديث الجولة الافتراضية: $e');
    }
  }

  @override
  Future<void> deleteVirtualTour(String tourId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لحذف الجولة الافتراضية');
      }

      // التحقق من ملكية الجولة
      final tour = await getVirtualTourById(tourId);
      if (tour == null) {
        throw Exception('الجولة الافتراضية غير موجودة');
      }
      if (tour.createdBy != user.uid) {
        throw Exception('ليس لديك صلاحية لحذف هذه الجولة');
      }

      // حذف الصورة المصغرة من التخزين
      if (tour.thumbnailUrl != null) {
        await _deleteImageFromStorage(tour.thumbnailUrl!);
      }

      // حذف الجولة من Firestore
      await _firestore
          .collection('virtualTours')
          .doc(tourId)
          .delete();
    } catch (e) {
      throw Exception('فشل في حذف الجولة الافتراضية: $e');
    }
  }

  @override
  Future<String> uploadThumbnailImage(File file, String estateId, String tourId) async {
    try {
      final fileName = 'virtual_tour_${estateId}_${tourId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final ref = _storage.ref().child('virtual_tours/thumbnails/$fileName');
      
      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;
      
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('فشل في رفع الصورة المصغرة: $e');
    }
  }

  @override
  Future<void> incrementVirtualTourViews(String tourId) async {
    try {
      await _firestore
          .collection('virtualTours')
          .doc(tourId)
          .update({
        'viewsCount': FieldValue.increment(1),
      });
    } catch (e) {
      throw Exception('فشل في تحديث عدد المشاهدات: $e');
    }
  }

  @override
  Future<List<VirtualTour>> getMostViewedVirtualTours({int limit = 10}) async {
    try {
      final snapshot = await _firestore
          .collection('virtualTours')
          .where('isActive', isEqualTo: true)
          .orderBy('viewsCount', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => VirtualTour.fromSnapshot(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الجولات الأكثر مشاهدة: $e');
    }
  }

  @override
  Future<List<VirtualTour>> getUserVirtualTours(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('virtualTours')
          .where('createdBy', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => VirtualTour.fromSnapshot(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب جولات المستخدم: $e');
    }
  }

  @override
  Future<void> toggleVirtualTourStatus(String tourId, bool isPaymentVerified) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لتغيير حالة الجولة');
      }

      // التحقق من ملكية الجولة
      final tour = await getVirtualTourById(tourId);
      if (tour == null) {
        throw Exception('الجولة الافتراضية غير موجودة');
      }
      if (tour.createdBy != user.uid) {
        throw Exception('ليس لديك صلاحية لتغيير حالة هذه الجولة');
      }

      await _firestore
          .collection('virtualTours')
          .doc(tourId)
          .update({
        'isPaymentVerified': isPaymentVerified,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في تغيير حالة الجولة: $e');
    }
  }

  /// حذف صورة من التخزين
  Future<void> _deleteImageFromStorage(String imageUrl) async {
    try {
      final ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      // تجاهل الأخطاء في حذف الصور (قد تكون محذوفة مسبقاً)
      print('تحذير: فشل في حذف الصورة من التخزين: $e');
    }
  }

  /// البحث في الجولات الافتراضية
  Future<List<VirtualTour>> searchVirtualTours({
    String? query,
    String? tourType,
    String? estateId,
    int limit = 20,
  }) async {
    try {
      Query queryRef = _firestore.collection('virtualTours');
      
      // فلترة حسب العقار
      if (estateId != null && estateId.isNotEmpty) {
        queryRef = queryRef.where('estateId', isEqualTo: estateId);
      }
      
      // فلترة حسب نوع الجولة
      if (tourType != null && tourType.isNotEmpty) {
        queryRef = queryRef.where('tourType', isEqualTo: tourType);
      }
      
      // فلترة الجولات المدفوعة فقط
      queryRef = queryRef.where('isPaymentVerified', isEqualTo: true);
      
      // ترتيب وتحديد العدد
      queryRef = queryRef
          .orderBy('createdAt', descending: true)
          .limit(limit);

      final snapshot = await queryRef.get();
      List<VirtualTour> results = snapshot.docs
          .map((doc) => VirtualTour.fromSnapshot(doc))
          .toList();

      // فلترة النص في الذاكرة
      if (query != null && query.isNotEmpty) {
        final searchQuery = query.toLowerCase();
        results = results.where((tour) {
          return tour.title.toLowerCase().contains(searchQuery) ||
                 (tour.description?.toLowerCase().contains(searchQuery) ?? false);
        }).toList();
      }

      return results;
    } catch (e) {
      throw Exception('فشل في البحث في الجولات الافتراضية: $e');
    }
  }

  /// الحصول على إحصائيات الجولات الافتراضية
  Future<Map<String, dynamic>> getVirtualTourStatistics(String estateId) async {
    try {
      final snapshot = await _firestore
          .collection('virtualTours')
          .where('estateId', isEqualTo: estateId)
          .where('isActive', isEqualTo: true)
          .get();

      int totalTours = snapshot.docs.length;
      int totalViews = 0;
      Map<String, int> tourTypeCount = {};

      for (final doc in snapshot.docs) {
        final tour = VirtualTour.fromSnapshot(doc);
        totalViews += tour.viewsCount;
        
        tourTypeCount[tour.tourType] = (tourTypeCount[tour.tourType] ?? 0) + 1;
      }

      return {
        'totalTours': totalTours,
        'totalViews': totalViews,
        'averageViewsPerTour': totalTours > 0 ? totalViews / totalTours : 0,
        'tourTypeDistribution': tourTypeCount,
      };
    } catch (e) {
      throw Exception('فشل في جلب إحصائيات الجولات الافتراضية: $e');
    }
  }
}
