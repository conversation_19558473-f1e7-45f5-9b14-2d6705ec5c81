import 'package:json_annotation/json_annotation.dart';

import '../../domain/entities/estate.dart';

part 'estate_model.g.dart';

@JsonSerializable()
class EstateModel {
  final String id;
  final String title;
  final String description;
  final double price;
  final String location;
  final List<String> photoUrls;
  final bool isFeatured;
  final String planType;
  final DateTime? startDate;
  final DateTime? endDate;
  final DateTime createdAt;
  final String? mainCategory;
  final String? subCategory;
  final String? postedByUserType;
  final bool hidePhone;
  final List<String> extraPhones;
  final bool shareLocation;
  final double? lat;
  final double? lng;
  // التجهيزات الإضافية
  final bool hasCentralAC;
  final bool? hasSecurity;
  final bool? allowPets;
  final bool? hasElevator;
  final bool? hasSwimmingPool;
  final bool hasMaidRoom;
  final bool hasGarage;
  final bool? hasBalcony;
  final bool? isFullyFurnished;
  // تفاصيل العقار الداخلية
  final String? rebound;
  final int? numberOfRooms;
  final String? internalLocation;
  final String? salon;
  final double? area;
  final int? floorNumber;
  final int? numberOfBathrooms;
  final int? buildingAge;
  final int? numberOfFloors;
  final String? propertyType;
  final String? usageType; // نوع الاستغلال (للبيع/للإيجار/للبدل/استثمار)
  // الحقول الجديدة المطلوبة (بيانات المُعلِن)
  final String? advertiserImage;
  final String? advertiserName;
  final String? advertiserEmail;
  final DateTime? advertiserRegistrationDate;
  final int? advertiserAdsCount;
  // الحقول الجديدة الأخرى
  final bool autoRepublish;
  final bool kuwaitCornersPin;
  final bool movingAd;
  final bool vipBadge;
  final bool pinnedOnHome;
  final String? discountCode;

  // حقول ملكية العقار والعلاقات
  final String? ownerId; // معرف مالك العقار الأصلي
  final String? originalEstateId; // معرف العقار الأصلي (في حالة النسخ)
  final bool isOriginal; // هل هذا عقار أصلي أم منسوخ
  final List<String>
      copiedBy; // قائمة بمعرفات المستخدمين الذين نسخوا هذا العقار
  final bool isPaymentVerified; // هل تم التحقق من الدفع

  const EstateModel({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.location,
    required this.photoUrls,
    required this.isFeatured,
    required this.planType,
    required this.startDate,
    required this.endDate,
    required this.createdAt,
    this.mainCategory,
    this.subCategory,
    this.postedByUserType,
    this.hidePhone = false,
    this.extraPhones = const [],
    this.shareLocation = false,
    this.lat,
    this.lng,
    this.hasCentralAC = false,
    this.hasSecurity,
    this.allowPets,
    this.hasElevator,
    this.hasSwimmingPool,
    this.hasMaidRoom = false,
    this.hasGarage = false,
    this.hasBalcony,
    this.isFullyFurnished,
    this.rebound,
    this.numberOfRooms,
    this.internalLocation,
    this.salon,
    this.area,
    this.floorNumber,
    this.numberOfBathrooms,
    this.buildingAge,
    this.numberOfFloors,
    this.propertyType,
    this.usageType,
    this.autoRepublish = false,
    this.kuwaitCornersPin = false,
    this.movingAd = false,
    this.vipBadge = false,
    this.pinnedOnHome = false,
    this.discountCode,
    this.advertiserImage,
    this.advertiserName,
    this.advertiserEmail,
    this.advertiserRegistrationDate,
    this.advertiserAdsCount,
    this.ownerId,
    this.originalEstateId,
    this.isOriginal = true,
    this.copiedBy = const [],
    this.isPaymentVerified = false,
  });

  factory EstateModel.fromJson(Map<String, dynamic> json) =>
      _$EstateModelFromJson(json);
  Map<String, dynamic> toJson() => _$EstateModelToJson(this);

  Estate toEntity() {
    return Estate(
      id: id,
      title: title,
      description: description,
      price: price,
      location: location,
      photoUrls: photoUrls,
      isFeatured: isFeatured,
      planType: planType,
      startDate: startDate,
      endDate: endDate,
      createdAt: createdAt,
      mainCategory: mainCategory,
      subCategory: subCategory,
      postedByUserType: postedByUserType,
      hidePhone: hidePhone,
      extraPhones: extraPhones,
      shareLocation: shareLocation,
      lat: lat,
      lng: lng,
      hasCentralAC: hasCentralAC,
      hasSecurity: hasSecurity,
      allowPets: allowPets,
      hasElevator: hasElevator,
      hasSwimmingPool: hasSwimmingPool,
      hasMaidRoom: hasMaidRoom,
      hasGarage: hasGarage,
      hasBalcony: hasBalcony,
      isFullyFurnished: isFullyFurnished,
      rebound: rebound,
      numberOfRooms: numberOfRooms,
      internalLocation: internalLocation,
      salon: salon,
      area: area,
      floorNumber: floorNumber,
      numberOfBathrooms: numberOfBathrooms,
      buildingAge: buildingAge,
      numberOfFloors: numberOfFloors,
      propertyType: propertyType,
      usageType: usageType,
      autoRepublish: autoRepublish,
      kuwaitCornersPin: kuwaitCornersPin,
      movingAd: movingAd,
      vipBadge: vipBadge,
      pinnedOnHome: pinnedOnHome,
      discountCode: discountCode,
      advertiserImage: advertiserImage,
      advertiserName: advertiserName,
      advertiserEmail: advertiserEmail,
      advertiserRegistrationDate: advertiserRegistrationDate,
      advertiserAdsCount: advertiserAdsCount,
      ownerId: ownerId,
      originalEstateId: originalEstateId,
      isOriginal: isOriginal,
      copiedBy: copiedBy,
      isPaymentVerified: isPaymentVerified);
  }

  factory EstateModel.fromEntity(Estate estate) {
    return EstateModel(
      id: estate.id,
      title: estate.title,
      description: estate.description,
      price: estate.price,
      location: estate.location,
      photoUrls: estate.photoUrls,
      isFeatured: estate.isFeatured,
      planType: estate.planType,
      startDate: estate.startDate,
      endDate: estate.endDate,
      createdAt: estate.createdAt,
      mainCategory: estate.mainCategory,
      subCategory: estate.subCategory,
      postedByUserType: estate.postedByUserType,
      hidePhone: estate.hidePhone,
      extraPhones: estate.extraPhones,
      shareLocation: estate.shareLocation,
      lat: estate.lat,
      lng: estate.lng,
      hasCentralAC: estate.hasCentralAC,
      hasSecurity: estate.hasSecurity,
      allowPets: estate.allowPets,
      hasElevator: estate.hasElevator,
      hasSwimmingPool: estate.hasSwimmingPool,
      hasMaidRoom: estate.hasMaidRoom,
      hasGarage: estate.hasGarage,
      hasBalcony: estate.hasBalcony,
      isFullyFurnished: estate.isFullyFurnished,
      rebound: estate.rebound,
      numberOfRooms: estate.numberOfRooms,
      internalLocation: estate.internalLocation,
      salon: estate.salon,
      area: estate.area,
      floorNumber: estate.floorNumber,
      numberOfBathrooms: estate.numberOfBathrooms,
      buildingAge: estate.buildingAge,
      numberOfFloors: estate.numberOfFloors,
      propertyType: estate.propertyType,
      usageType: estate.usageType,
      autoRepublish: estate.autoRepublish,
      kuwaitCornersPin: estate.kuwaitCornersPin,
      movingAd: estate.movingAd,
      vipBadge: estate.vipBadge,
      pinnedOnHome: estate.pinnedOnHome,
      discountCode: estate.discountCode,
      advertiserImage: estate.advertiserImage,
      advertiserName: estate.advertiserName,
      advertiserEmail: estate.advertiserEmail,
      advertiserRegistrationDate: estate.advertiserRegistrationDate,
      advertiserAdsCount: estate.advertiserAdsCount,
      ownerId: estate.ownerId,
      originalEstateId: estate.originalEstateId,
      isOriginal: estate.isOriginal,
      copiedBy: estate.copiedBy,
      isPaymentVerified: estate.isPaymentVerified);
  }
}
