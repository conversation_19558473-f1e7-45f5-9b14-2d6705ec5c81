// lib/presentation/pages/property_request/property_request_notifications_page.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:kuwait_corners/core/services/realtime_notification_service.dart';
import 'package:kuwait_corners/presentation/widgets/notifications/property_request_notification_item.dart';

/// صفحة إشعارات طلبات العقارات
class PropertyRequestNotificationsPage extends StatefulWidget {
  const PropertyRequestNotificationsPage({super.key});

  @override
  State<PropertyRequestNotificationsPage> createState() =>
      _PropertyRequestNotificationsPageState();
}

class _PropertyRequestNotificationsPageState
    extends State<PropertyRequestNotificationsPage> {
  // خدمة الإشعارات في الوقت الحقيقي
  final RealtimeNotificationService _notificationService =
      RealtimeNotificationService();

  // قائمة الإشعارات
  List<RealtimeNotificationModel> _notifications = [];

  // حالة التحميل
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  /// تحميل الإشعارات
  Future<void> _loadNotifications() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // تهيئة خدمة الإشعارات
      await _notificationService.initialize();

      // الاستماع للإشعارات الجديدة
      _notificationService.notificationsStream?.listen((notification) {
        // فلترة الإشعارات المتعلقة بطلبات العقارات فقط
        if (_isPropertyRequestNotification(notification)) {
          setState(() {
            _notifications.insert(0, notification);
          });
        }
      });

      // تحميل الإشعارات الحالية
      final allNotifications =
          await _notificationService.getNotifications(limit: 50);

      // فلترة الإشعارات المتعلقة بطلبات العقارات فقط
      final propertyRequestNotifications = allNotifications
          .where(_isPropertyRequestNotification)
          .toList();

      setState(() {
        _notifications = propertyRequestNotifications;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل الإشعارات: $e';
      });
    }
  }

  /// التحقق مما إذا كان الإشعار متعلق بطلبات العقارات
  bool _isPropertyRequestNotification(RealtimeNotificationModel notification) {
    return notification.type == RealtimeNotificationType.newPropertyOffer ||
        notification.type == RealtimeNotificationType.propertyOfferAccepted ||
        notification.type == RealtimeNotificationType.propertyOfferRejected ||
        notification.type == RealtimeNotificationType.propertyRequestStatusUpdate;
  }

  /// تعليم جميع الإشعارات كمقروءة
  Future<void> _markAllAsRead() async {
    try {
      for (final notification in _notifications) {
        if (!notification.isRead) {
          await _notificationService.markAsRead(notification.id);
        }
      }

      setState(() {
        _notifications = _notifications.map((notification) {
          return RealtimeNotificationModel(
            id: notification.id,
            recipientId: notification.recipientId,
            senderId: notification.senderId,
            senderName: notification.senderName,
            senderPhotoUrl: notification.senderPhotoUrl,
            type: notification.type,
            title: notification.title,
            body: notification.body,
            data: notification.data,
            timestamp: notification.timestamp,
            isRead: true);
        }).toList();
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تعليم جميع الإشعارات كمقروءة')));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: $e')));
    }
  }

  /// تعليم إشعار كمقروء
  Future<void> _markAsRead(String notificationId) async {
    try {
      await _notificationService.markAsRead(notificationId);

      setState(() {
        final index = _notifications
            .indexWhere((notification) => notification.id == notificationId);
        if (index != -1) {
          final notification = _notifications[index];
          _notifications[index] = RealtimeNotificationModel(
            id: notification.id,
            recipientId: notification.recipientId,
            senderId: notification.senderId,
            senderName: notification.senderName,
            senderPhotoUrl: notification.senderPhotoUrl,
            type: notification.type,
            title: notification.title,
            body: notification.body,
            data: notification.data,
            timestamp: notification.timestamp,
            isRead: true);
        }
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إشعارات طلبات العقارات',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNotifications,
            tooltip: 'تحديث'),
          IconButton(
            icon: const Icon(Icons.done_all),
            onPressed: _markAllAsRead,
            tooltip: 'تعليم الكل كمقروء'),
        ]),
      body: _buildBody());
  }

  /// بناء محتوى الصفحة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadNotifications,
              child: const Text('إعادة المحاولة')),
          ]));
    }

    if (_notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_off,
              size: 64,
              color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد إشعارات لطلبات العقارات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                color: Colors.grey.shade600)),
          ]));
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: _notifications.length,
        itemBuilder: (context, index) {
          final notification = _notifications[index];
          return PropertyRequestNotificationItem(
            notification: notification,
            onMarkAsRead: _markAsRead);
        }));
  }
}
