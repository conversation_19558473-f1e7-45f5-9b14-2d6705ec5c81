import 'package:cloud_firestore/cloud_firestore.dart';

/// أنواع الاشتراكات
enum SubscriptionType {
  /// اشتراك مجاني
  free,
  
  /// اشتراك شهري
  monthly,
  
  /// اشتراك سنوي
  yearly,
}

/// نموذج بيانات الاشتراك
class SubscriptionModel {
  /// معرف الاشتراك
  final String id;
  
  /// معرف المستخدم
  final String userId;
  
  /// نوع الاشتراك
  final SubscriptionType type;
  
  /// تاريخ بدء الاشتراك
  final DateTime startDate;
  
  /// تاريخ انتهاء الاشتراك
  final DateTime endDate;
  
  /// عدد الإعلانات المسموح بها
  final int allowedAds;
  
  /// عدد الإعلانات المتبقية
  int remainingAds;
  
  /// عدد الصور المسموح بها لكل إعلان
  final int allowedImagesPerAd;
  
  /// مدة عرض الإعلان (بالأيام)
  final int adDurationDays;
  
  /// ما إذا كان الاشتراك نشطًا
  bool isActive;
  
  /// ما إذا كان التجديد التلقائي مفعلًا
  bool autoRenew;
  
  /// معرف عملية الدفع
  final String? paymentId;
  
  /// سعر الاشتراك
  final double price;
  
  /// عملة الاشتراك
  final String currency;
  
  /// تاريخ آخر تجديد
  DateTime? lastRenewalDate;
  
  /// تاريخ التجديد القادم
  DateTime? nextRenewalDate;
  
  /// بيانات إضافية
  final Map<String, dynamic> metadata;

  SubscriptionModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.allowedAds,
    required this.remainingAds,
    required this.allowedImagesPerAd,
    required this.adDurationDays,
    required this.isActive,
    required this.autoRenew,
    this.paymentId,
    required this.price,
    required this.currency,
    this.lastRenewalDate,
    this.nextRenewalDate,
    required this.metadata,
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory SubscriptionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return SubscriptionModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      type: _getSubscriptionTypeFromString(data['type'] ?? 'free'),
      startDate: (data['startDate'] as Timestamp).toDate(),
      endDate: (data['endDate'] as Timestamp).toDate(),
      allowedAds: data['allowedAds'] ?? 0,
      remainingAds: data['remainingAds'] ?? 0,
      allowedImagesPerAd: data['allowedImagesPerAd'] ?? 0,
      adDurationDays: data['adDurationDays'] ?? 0,
      isActive: data['isActive'] ?? false,
      autoRenew: data['autoRenew'] ?? false,
      paymentId: data['paymentId'],
      price: (data['price'] ?? 0).toDouble(),
      currency: data['currency'] ?? 'KWD',
      lastRenewalDate: data['lastRenewalDate'] != null
          ? (data['lastRenewalDate'] as Timestamp).toDate()
          : null,
      nextRenewalDate: data['nextRenewalDate'] != null
          ? (data['nextRenewalDate'] as Timestamp).toDate()
          : null,
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}));
  }

  /// تحويل النموذج إلى Map لتخزينه في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'type': type.toString().split('.').last,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'allowedAds': allowedAds,
      'remainingAds': remainingAds,
      'allowedImagesPerAd': allowedImagesPerAd,
      'adDurationDays': adDurationDays,
      'isActive': isActive,
      'autoRenew': autoRenew,
      'paymentId': paymentId,
      'price': price,
      'currency': currency,
      'lastRenewalDate': lastRenewalDate != null
          ? Timestamp.fromDate(lastRenewalDate!)
          : null,
      'nextRenewalDate': nextRenewalDate != null
          ? Timestamp.fromDate(nextRenewalDate!)
          : null,
      'metadata': metadata,
    };
  }
  
  /// الحصول على نوع الاشتراك من النص
  static SubscriptionType _getSubscriptionTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'monthly':
        return SubscriptionType.monthly;
      case 'yearly':
        return SubscriptionType.yearly;
      default:
        return SubscriptionType.free;
    }
  }
  
  /// الحصول على اسم نوع الاشتراك بالعربية
  String getSubscriptionTypeName() {
    switch (type) {
      case SubscriptionType.monthly:
        return 'اشتراك شهري';
      case SubscriptionType.yearly:
        return 'اشتراك سنوي';
      default:
        return 'اشتراك مجاني';
    }
  }
  
  /// الحصول على عدد الأيام المتبقية في الاشتراك
  int getRemainingDays() {
    return endDate.difference(DateTime.now()).inDays;
  }
  
  /// التحقق مما إذا كان الاشتراك على وشك الانتهاء
  bool isExpiringSoon() {
    final remainingDays = getRemainingDays();
    return remainingDays <= 7 && remainingDays > 0;
  }
  
  /// التحقق مما إذا كان الاشتراك منتهي
  bool isExpired() {
    return DateTime.now().isAfter(endDate);
  }
  
  /// التحقق مما إذا كان عدد الإعلانات المتبقية منخفضًا
  bool hasLowRemainingAds() {
    return remainingAds <= 2 && remainingAds > 0;
  }
  
  /// الحصول على نسخة محدثة من النموذج
  SubscriptionModel copyWith({
    String? id,
    String? userId,
    SubscriptionType? type,
    DateTime? startDate,
    DateTime? endDate,
    int? allowedAds,
    int? remainingAds,
    int? allowedImagesPerAd,
    int? adDurationDays,
    bool? isActive,
    bool? autoRenew,
    String? paymentId,
    double? price,
    String? currency,
    DateTime? lastRenewalDate,
    DateTime? nextRenewalDate,
    Map<String, dynamic>? metadata,
  }) {
    return SubscriptionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      allowedAds: allowedAds ?? this.allowedAds,
      remainingAds: remainingAds ?? this.remainingAds,
      allowedImagesPerAd: allowedImagesPerAd ?? this.allowedImagesPerAd,
      adDurationDays: adDurationDays ?? this.adDurationDays,
      isActive: isActive ?? this.isActive,
      autoRenew: autoRenew ?? this.autoRenew,
      paymentId: paymentId ?? this.paymentId,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      lastRenewalDate: lastRenewalDate ?? this.lastRenewalDate,
      nextRenewalDate: nextRenewalDate ?? this.nextRenewalDate,
      metadata: metadata ?? this.metadata);
  }
}

/// نموذج بيانات الترويج
class PromotionModel {
  /// معرف الترويج
  final String id;
  
  /// معرف الإعلان
  final String adId;
  
  /// معرف المستخدم
  final String userId;
  
  /// نوع الترويج
  final PromotionType type;
  
  /// تاريخ بدء الترويج
  final DateTime startDate;
  
  /// تاريخ انتهاء الترويج
  final DateTime endDate;
  
  /// سعر الترويج
  final double price;
  
  /// عملة الترويج
  final String currency;
  
  /// معرف عملية الدفع
  final String? paymentId;
  
  /// ما إذا كان الترويج نشطًا
  bool isActive;
  
  /// بيانات إضافية
  final Map<String, dynamic> metadata;

  PromotionModel({
    required this.id,
    required this.adId,
    required this.userId,
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.price,
    required this.currency,
    this.paymentId,
    required this.isActive,
    required this.metadata,
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory PromotionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return PromotionModel(
      id: doc.id,
      adId: data['adId'] ?? '',
      userId: data['userId'] ?? '',
      type: _getPromotionTypeFromString(data['type'] ?? 'featured'),
      startDate: (data['startDate'] as Timestamp).toDate(),
      endDate: (data['endDate'] as Timestamp).toDate(),
      price: (data['price'] ?? 0).toDouble(),
      currency: data['currency'] ?? 'KWD',
      paymentId: data['paymentId'],
      isActive: data['isActive'] ?? false,
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}));
  }

  /// تحويل النموذج إلى Map لتخزينه في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'adId': adId,
      'userId': userId,
      'type': type.toString().split('.').last,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'price': price,
      'currency': currency,
      'paymentId': paymentId,
      'isActive': isActive,
      'metadata': metadata,
    };
  }
  
  /// الحصول على نوع الترويج من النص
  static PromotionType _getPromotionTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'pinned':
        return PromotionType.pinned;
      case 'highlighted':
        return PromotionType.highlighted;
      case 'video':
        return PromotionType.video;
      case 'premium':
        return PromotionType.premium;
      default:
        return PromotionType.featured;
    }
  }
  
  /// الحصول على اسم نوع الترويج بالعربية
  String getPromotionTypeName() {
    switch (type) {
      case PromotionType.pinned:
        return 'إعلان مثبت';
      case PromotionType.highlighted:
        return 'إعلان مميز';
      case PromotionType.video:
        return 'إعلان فيديو';
      case PromotionType.premium:
        return 'إعلان متميز';
      default:
        return 'إعلان بارز';
    }
  }
  
  /// الحصول على عدد الأيام المتبقية في الترويج
  int getRemainingDays() {
    return endDate.difference(DateTime.now()).inDays;
  }
  
  /// التحقق مما إذا كان الترويج على وشك الانتهاء
  bool isExpiringSoon() {
    final remainingDays = getRemainingDays();
    return remainingDays <= 2 && remainingDays > 0;
  }
  
  /// التحقق مما إذا كان الترويج منتهي
  bool isExpired() {
    return DateTime.now().isAfter(endDate);
  }
}

/// أنواع الترويج
enum PromotionType {
  /// إعلان بارز
  featured,
  
  /// إعلان مثبت
  pinned,
  
  /// إعلان مميز
  highlighted,
  
  /// إعلان فيديو
  video,
  
  /// إعلان متميز
  premium,
}
