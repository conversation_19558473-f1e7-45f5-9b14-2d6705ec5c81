import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// خدمة الإعلانات المدفوعة
class PaidAdsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// أنواع الإعلانات المدفوعة
  static const Map<String, Map<String, dynamic>> adTypes = {
    'featured': {
      'name': 'إعلان مميز',
      'description': 'يظهر في أعلى النتائج مع إطار ذهبي',
      'price': 15.0,
      'duration': 30, // أيام
      'color': 'gold',
      'icon': 'star',
    },
    'pinned': {
      'name': 'إعلان مثبت',
      'description': 'يظهر في الصفحة الرئيسية بشكل دائم',
      'price': 20.0,
      'duration': 30, // أيام
      'color': 'blue',
      'icon': 'push_pin',
    },
    'both': {
      'name': 'مميز + مثبت',
      'description': 'يجمع بين مميزات الإعلان المميز والمثبت',
      'price': 30.0,
      'duration': 30, // أيام
      'color': 'purple',
      'icon': 'workspace_premium',
    },
  };

  /// الحصول على أنواع الإعلانات المتاحة
  List<Map<String, dynamic>> getAvailableAdTypes() {
    return adTypes.entries.map((entry) {
      return {
        'id': entry.key,
        ...entry.value,
      };
    }).toList();
  }

  /// حساب سعر الإعلان
  double calculateAdPrice(String adType, {int? customDuration}) {
    final basePrice = adTypes[adType]?['price'] as double? ?? 0.0;
    final baseDuration = adTypes[adType]?['duration'] as int? ?? 30;
    
    if (customDuration != null && customDuration != baseDuration) {
      // حساب السعر بناءً على المدة المخصصة
      return (basePrice / baseDuration) * customDuration;
    }
    
    return basePrice;
  }

  /// إنشاء طلب دفع للإعلان
  Future<Map<String, dynamic>> createPaymentRequest({
    required String estateId,
    required String adType,
    required int duration,
    String? userId,
  }) async {
    try {
      userId ??= _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final price = calculateAdPrice(adType, customDuration: duration);
      final paymentId = _firestore.collection('payments').doc().id;
      
      final paymentData = {
        'id': paymentId,
        'userId': userId,
        'estateId': estateId,
        'adType': adType,
        'duration': duration,
        'price': price,
        'currency': 'KWD',
        'status': 'pending',
        'createdAt': Timestamp.fromDate(DateTime.now()),
        'expiresAt': Timestamp.fromDate(DateTime.now().add(Duration(hours: 24))),
        'paymentMethod': 'bank_transfer',
        'bankDetails': _getBankDetails(),
      };

      await _firestore.collection('payments').doc(paymentId).set(paymentData);

      return {
        'paymentId': paymentId,
        'price': price,
        'currency': 'KWD',
        'bankDetails': _getBankDetails(),
        'expiresAt': DateTime.now().add(Duration(hours: 24)),
      };
    } catch (e) {
      throw Exception('خطأ في إنشاء طلب الدفع: $e');
    }
  }

  /// تأكيد الدفع وتفعيل الإعلان
  Future<bool> confirmPayment(String paymentId) async {
    try {
      final paymentDoc = await _firestore.collection('payments').doc(paymentId).get();
      
      if (!paymentDoc.exists) {
        throw Exception('طلب الدفع غير موجود');
      }

      final paymentData = paymentDoc.data()!;
      
      if (paymentData['status'] != 'pending') {
        throw Exception('طلب الدفع غير صالح');
      }

      // تحديث حالة الدفع
      await _firestore.collection('payments').doc(paymentId).update({
        'status': 'confirmed',
        'confirmedAt': Timestamp.fromDate(DateTime.now()),
      });

      // تفعيل الإعلان
      await _activateAd(
        paymentData['estateId'],
        paymentData['adType'],
        paymentData['duration']);

      return true;
    } catch (e) {
      throw Exception('خطأ في تأكيد الدفع: $e');
    }
  }

  /// تفعيل الإعلان المدفوع
  Future<void> _activateAd(String estateId, String adType, int duration) async {
    try {
      final startDate = DateTime.now();
      final endDate = startDate.add(Duration(days: duration));

      final updateData = {
        'isPaidAd': true,
        'adType': adType,
        'adExpiryDate': Timestamp.fromDate(endDate),
        'isFeatured': adType == 'featured' || adType == 'both',
        'pinnedOnHome': adType == 'pinned' || adType == 'both',
        'planType': 'premium',
      };

      await _firestore.collection('estates').doc(estateId).update(updateData);

      // إضافة سجل في تاريخ الإعلانات المدفوعة
      await _firestore.collection('paid_ads_history').add({
        'estateId': estateId,
        'adType': adType,
        'duration': duration,
        'startDate': Timestamp.fromDate(startDate),
        'endDate': Timestamp.fromDate(endDate),
        'activatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('خطأ في تفعيل الإعلان: $e');
    }
  }

  /// الحصول على تفاصيل البنك للتحويل
  Map<String, dynamic> _getBankDetails() {
    // أرقام بنوك كويتية عشوائية كما طلب المستخدم
    final banks = [
      {
        'bankName': 'بنك الكويت الوطني',
        'accountNumber': '******************************',
        'accountName': 'شركة كريا للعقارات',
        'swiftCode': 'NBOKKWKW',
      },
      {
        'bankName': 'بنك الخليج',
        'accountNumber': '******************************',
        'accountName': 'شركة كريا للعقارات',
        'swiftCode': 'GULKKWKW',
      },
      {
        'bankName': 'البنك الأهلي الكويتي',
        'accountNumber': '******************************',
        'accountName': 'شركة كريا للعقارات',
        'swiftCode': 'ABKKKWKW',
      },
    ];

    // اختيار بنك عشوائي
    final randomBank = banks[DateTime.now().millisecond % banks.length];
    
    return {
      ...randomBank,
      'contactPhone': '+965 9929 8821',
      'instructions': [
        'قم بتحويل المبلغ إلى الحساب المذكور أعلاه',
        'احتفظ بإيصال التحويل',
        'أرسل صورة الإيصال عبر واتساب إلى الرقم المذكور',
        'سيتم تفعيل إعلانك خلال 24 ساعة من التأكد من التحويل',
      ],
    };
  }

  /// الحصول على الإعلانات المدفوعة النشطة
  Future<List<Map<String, dynamic>>> getActivePaidAds({String? userId}) async {
    try {
      Query query = _firestore
          .collection('estates')
          .where('isPaidAd', isEqualTo: true)
          .where('adExpiryDate', isGreaterThan: Timestamp.fromDate(DateTime.now()));

      if (userId != null) {
        query = query.where('ownerId', isEqualTo: userId);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'estateId': doc.id,
          'title': data['title'],
          'adType': data['adType'],
          'expiryDate': (data['adExpiryDate'] as Timestamp).toDate(),
          'daysRemaining': (data['adExpiryDate'] as Timestamp)
              .toDate()
              .difference(DateTime.now())
              .inDays,
        };
      }).toList();
    } catch (e) {
      throw Exception('خطأ في جلب الإعلانات المدفوعة: $e');
    }
  }

  /// تجديد الإعلان المدفوع
  Future<Map<String, dynamic>> renewAd({
    required String estateId,
    required String adType,
    required int duration,
  }) async {
    try {
      // التحقق من وجود العقار
      final estateDoc = await _firestore.collection('estates').doc(estateId).get();
      if (!estateDoc.exists) {
        throw Exception('العقار غير موجود');
      }

      // إنشاء طلب دفع جديد للتجديد
      return await createPaymentRequest(
        estateId: estateId,
        adType: adType,
        duration: duration);
    } catch (e) {
      throw Exception('خطأ في تجديد الإعلان: $e');
    }
  }

  /// الحصول على إحصائيات الإعلانات المدفوعة
  Future<Map<String, dynamic>> getPaidAdsStatistics({String? userId}) async {
    try {
      Query query = _firestore.collection('paid_ads_history');
      
      if (userId != null) {
        // نحتاج للحصول على العقارات الخاصة بالمستخدم أولاً
        final userEstates = await _firestore
            .collection('estates')
            .where('ownerId', isEqualTo: userId)
            .get();
        
        final estateIds = userEstates.docs.map((doc) => doc.id).toList();
        
        if (estateIds.isEmpty) {
          return {
            'totalAds': 0,
            'totalRevenue': 0.0,
            'activeAds': 0,
            'expiredAds': 0,
          };
        }
        
        query = query.where('estateId', whereIn: estateIds);
      }

      final querySnapshot = await query.get();
      final totalAds = querySnapshot.docs.length;
      
      // حساب الإيرادات الإجمالية
      double totalRevenue = 0.0;
      int activeAds = 0;
      int expiredAds = 0;
      
      for (final doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final adType = data['adType'] as String;
        final duration = data['duration'] as int;
        final endDate = (data['endDate'] as Timestamp).toDate();
        
        totalRevenue += calculateAdPrice(adType, customDuration: duration);
        
        if (endDate.isAfter(DateTime.now())) {
          activeAds++;
        } else {
          expiredAds++;
        }
      }

      return {
        'totalAds': totalAds,
        'totalRevenue': totalRevenue,
        'activeAds': activeAds,
        'expiredAds': expiredAds,
      };
    } catch (e) {
      return {
        'totalAds': 0,
        'totalRevenue': 0.0,
        'activeAds': 0,
        'expiredAds': 0,
      };
    }
  }

  /// إلغاء الإعلان المدفوع
  Future<void> cancelPaidAd(String estateId) async {
    try {
      await _firestore.collection('estates').doc(estateId).update({
        'isPaidAd': false,
        'adType': null,
        'adExpiryDate': null,
        'isFeatured': false,
        'pinnedOnHome': false,
        'planType': 'free',
      });
    } catch (e) {
      throw Exception('خطأ في إلغاء الإعلان المدفوع: $e');
    }
  }
}
