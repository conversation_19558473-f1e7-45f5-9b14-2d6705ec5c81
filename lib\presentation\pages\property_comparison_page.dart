import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';

import '../../domain/entities/estate.dart';

class PropertyComparisonPage extends StatefulWidget {
  final List<Estate> properties;

  const PropertyComparisonPage({
    super.key,
    required this.properties,
  });

  @override
  State<PropertyComparisonPage> createState() => _PropertyComparisonPageState();
}

class _PropertyComparisonPageState extends State<PropertyComparisonPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'مقارنة العقارات',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareComparison),
        ]),
      body: widget.properties.isEmpty
          ? _buildEmptyState()
          : SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                children: [
                  // رأس المقارنة مع صور العقارات
                  _buildComparisonHeader(),

                  // جدول المقارنة
                  _buildComparisonTable(),

                  // أزرار الإجراءات
                  _buildActionButtons(),
                ])));
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.compare_arrows,
            size: 80,
            color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد عقارات للمقارنة',
            style: GoogleFonts.cairo(
              fontSize: 18,
              color: Colors.grey.shade600)),
          const SizedBox(height: 8),
          Text(
            'أضف عقارات من الصفحة الرئيسية للمقارنة',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade500)),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'العودة للرئيسية',
              style: GoogleFonts.cairo())),
        ]));
  }

  /// بناء رأس المقارنة
  Widget _buildComparisonHeader() {
    return SizedBox(
      height: 200,
      child: Row(
        children: [
          // عمود التسميات
          Container(
            width: 120,
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  'العقارات',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor)),
              ])),

          // أعمدة العقارات
          Expanded(
            child: Row(
              children: widget.properties.map((property) {
                return Expanded(
                  child: _buildPropertyHeader(property));
              }).toList())),
        ]));
  }

  /// بناء رأس العقار
  Widget _buildPropertyHeader(Estate property) {
    return Container(
      margin: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة العقار
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: AspectRatio(
              aspectRatio: 16 / 9,
              child: property.photoUrls.isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: property.photoUrls.first,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey.shade200,
                        child: const Center(
                          child: CircularProgressIndicator())),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey.shade200,
                        child: const Icon(Icons.error)))
                  : Container(
                      color: Colors.grey.shade200,
                      child: const Icon(
                        Icons.home,
                        size: 40,
                        color: Colors.grey)))),

          // معلومات أساسية
          Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  property.title,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.bold),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis),
                const SizedBox(height: 4),
                Text(
                  '${NumberFormat('#,###').format(property.price)} د.ك',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor)),
                Text(
                  property.location,
                  style: GoogleFonts.cairo(
                    fontSize: 10,
                    color: Colors.grey.shade600),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
              ])),
        ]));
  }

  /// بناء جدول المقارنة
  Widget _buildComparisonTable() {
    final comparisonItems = [
      {'label': 'السعر', 'getValue': (Estate e) => '${NumberFormat('#,###').format(e.price)} د.ك'},
      {'label': 'المساحة', 'getValue': (Estate e) => '${e.area ?? 0} م²'},
      {'label': 'عدد الغرف', 'getValue': (Estate e) => '${e.numberOfRooms ?? 0}'},
      {'label': 'عدد الحمامات', 'getValue': (Estate e) => '${e.numberOfBathrooms ?? 0}'},
      {'label': 'عمر البناء', 'getValue': (Estate e) => '${e.buildingAge ?? 'غير محدد'}'},
      {'label': 'الطابق', 'getValue': (Estate e) => '${e.floorNumber ?? 'غير محدد'}'},
      {'label': 'نوع العقار', 'getValue': (Estate e) => e.propertyType ?? 'غير محدد'},
      {'label': 'الفئة', 'getValue': (Estate e) => e.mainCategory ?? 'غير محدد'},
      {'label': 'تكييف مركزي', 'getValue': (Estate e) => e.hasCentralAC ? 'نعم' : 'لا'},
      {'label': 'غرفة خادمة', 'getValue': (Estate e) => e.hasMaidRoom ? 'نعم' : 'لا'},
      {'label': 'مرآب', 'getValue': (Estate e) => e.hasGarage ? 'نعم' : 'لا'},
      {'label': 'مصعد', 'getValue': (Estate e) => (e.hasElevator ?? false) ? 'نعم' : 'لا'},
      {'label': 'مفروش', 'getValue': (Estate e) => (e.isFullyFurnished ?? false) ? 'نعم' : 'لا'},
      {'label': 'المعلن', 'getValue': (Estate e) => e.advertiserName ?? 'غير محدد'},
    ];

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        children: comparisonItems.map((item) {
          return _buildComparisonRow(
            item['label'] as String,
            widget.properties.map((p) => (item['getValue'] as String Function(Estate))(p)).toList());
        }).toList()));
  }

  /// بناء صف المقارنة
  Widget _buildComparisonRow(String label, List<String> values) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1))),
      child: Row(
        children: [
          // عمود التسمية
          Container(
            width: 120,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                right: BorderSide(
                  color: Colors.grey.shade200,
                  width: 1))),
            child: Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700))),

          // أعمدة القيم
          Expanded(
            child: Row(
              children: values.map((value) {
                return Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Colors.grey.shade200,
                          width: 0.5))),
                    child: Text(
                      value,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade800),
                      textAlign: TextAlign.center)));
              }).toList())),
        ]));
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _clearComparison,
                  icon: const Icon(Icons.clear_all),
                  label: Text(
                    'مسح المقارنة',
                    style: GoogleFonts.cairo()),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12)))),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _addMoreProperties,
                  icon: const Icon(Icons.add),
                  label: Text(
                    'إضافة عقارات',
                    style: GoogleFonts.cairo()),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12)))),
            ]),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _generateReport,
              icon: const Icon(Icons.assessment),
              label: Text(
                'إنشاء تقرير مقارنة',
                style: GoogleFonts.cairo()),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12)))),
        ]));
  }

  /// مشاركة المقارنة
  void _shareComparison() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم تفعيل خاصية المشاركة قريباً',
          style: GoogleFonts.cairo())));
  }

  /// مسح المقارنة
  void _clearComparison() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'مسح المقارنة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
        content: Text(
          'هل أنت متأكد من مسح جميع العقارات من المقارنة؟',
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo())),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('مسح', style: GoogleFonts.cairo())),
        ]));
  }

  /// إضافة عقارات أكثر
  void _addMoreProperties() {
    Navigator.pop(context);
  }

  /// إنشاء تقرير المقارنة
  void _generateReport() {
    // TODO: Implement report generation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'جاري إنشاء تقرير المقارنة...',
          style: GoogleFonts.cairo()),
        backgroundColor: Colors.green));
  }
}
