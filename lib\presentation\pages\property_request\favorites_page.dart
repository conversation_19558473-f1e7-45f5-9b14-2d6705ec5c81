// lib/presentation/pages/property_request/favorites_page.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/services/property_request_favorites_service.dart';
import '../../../core/theme/app_colors.dart';
import '../../../domain/models/favorites/favorite_property_request_model.dart';
import '../../widgets/property_request/favorite_property_request_card.dart';
import '../../widgets/common/enhanced_progress_indicator.dart';

/// صفحة المفضلة
class FavoritesPage extends StatefulWidget {
  const FavoritesPage({super.key});

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage> {
  final PropertyRequestFavoritesService _favoritesService = PropertyRequestFavoritesService();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  
  List<FavoritePropertyRequestModel> _favorites = [];
  List<FavoritePropertyRequestModel> _filteredFavorites = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  String _searchQuery = '';
  String _sortBy = 'addedAt';
  bool _descending = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadFavorites();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل المفضلة
  Future<void> _loadFavorites({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _isLoading = true;
        _favorites.clear();
        _filteredFavorites.clear();
        _hasMoreData = true;
      });
    }

    try {
      final favorites = await _favoritesService.getFavoriteRequests(
        limit: 20,
        sortBy: _sortBy,
        descending: _descending,
      );

      setState(() {
        if (refresh) {
          _favorites = favorites;
        } else {
          _favorites.addAll(favorites);
        }
        _hasMoreData = favorites.length == 20;
        _isLoading = false;
        _isLoadingMore = false;
      });

      _applySearch();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isLoadingMore = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المفضلة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تحميل المزيد من المفضلة
  Future<void> _loadMoreFavorites() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() => _isLoadingMore = true);

    try {
      final favorites = await _favoritesService.getFavoriteRequests(
        limit: 20,
        sortBy: _sortBy,
        descending: _descending,
      );

      setState(() {
        _favorites.addAll(favorites);
        _hasMoreData = favorites.length == 20;
        _isLoadingMore = false;
      });

      _applySearch();
    } catch (e) {
      setState(() => _isLoadingMore = false);
    }
  }

  /// التمرير للأسفل
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreFavorites();
    }
  }

  /// تطبيق البحث
  void _applySearch() {
    if (_searchQuery.isEmpty) {
      setState(() {
        _filteredFavorites = List.from(_favorites);
      });
    } else {
      setState(() {
        _filteredFavorites = _favorites.where((favorite) {
          final searchText = _searchQuery.toLowerCase();
          return favorite.requestTitle.toLowerCase().contains(searchText) ||
                 favorite.requestDescription.toLowerCase().contains(searchText) ||
                 favorite.propertyType.toLowerCase().contains(searchText) ||
                 favorite.preferredLocations.any((location) => 
                     location.toLowerCase().contains(searchText));
        }).toList();
      });
    }
  }

  /// البحث في المفضلة
  void _searchFavorites(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applySearch();
  }

  /// تغيير الترتيب
  void _changeSorting(String sortBy, bool descending) {
    setState(() {
      _sortBy = sortBy;
      _descending = descending;
    });
    _loadFavorites(refresh: true);
  }

  /// مسح جميع المفضلة
  Future<void> _clearAllFavorites() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'مسح جميع المفضلة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في مسح جميع المفضلة؟ لا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('مسح الكل', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _favoritesService.clearAllFavorites();
        _loadFavorites(refresh: true);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم مسح جميع المفضلة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في مسح المفضلة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.orangeBackground,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.greenOrangeGradient,
          ),
        ),
        title: Text(
          'المفضلة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (_favorites.isNotEmpty)
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, color: Colors.white),
              onSelected: (value) {
                switch (value) {
                  case 'sort_date_desc':
                    _changeSorting('addedAt', true);
                    break;
                  case 'sort_date_asc':
                    _changeSorting('addedAt', false);
                    break;
                  case 'sort_title':
                    _changeSorting('requestTitle', false);
                    break;
                  case 'sort_priority':
                    _changeSorting('priority', true);
                    break;
                  case 'clear_all':
                    _clearAllFavorites();
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'sort_date_desc',
                  child: Row(
                    children: [
                      const Icon(Icons.access_time),
                      const SizedBox(width: 8),
                      Text('الأحدث أولاً', style: GoogleFonts.cairo()),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'sort_date_asc',
                  child: Row(
                    children: [
                      const Icon(Icons.history),
                      const SizedBox(width: 8),
                      Text('الأقدم أولاً', style: GoogleFonts.cairo()),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'sort_title',
                  child: Row(
                    children: [
                      const Icon(Icons.sort_by_alpha),
                      const SizedBox(width: 8),
                      Text('ترتيب أبجدي', style: GoogleFonts.cairo()),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'sort_priority',
                  child: Row(
                    children: [
                      const Icon(Icons.priority_high),
                      const SizedBox(width: 8),
                      Text('حسب الأولوية', style: GoogleFonts.cairo()),
                    ],
                  ),
                ),
                const PopupMenuDivider(),
                PopupMenuItem(
                  value: 'clear_all',
                  child: Row(
                    children: [
                      const Icon(Icons.clear_all, color: Colors.red),
                      const SizedBox(width: 8),
                      Text(
                        'مسح الكل',
                        style: GoogleFonts.cairo(color: Colors.red),
                      ),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          if (_favorites.isNotEmpty)
            Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                onChanged: _searchFavorites,
                decoration: InputDecoration(
                  hintText: 'البحث في المفضلة...',
                  hintStyle: GoogleFonts.cairo(color: Colors.grey),
                  prefixIcon: const Icon(Icons.search, color: AppColors.primary),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _searchFavorites('');
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                style: GoogleFonts.cairo(),
              ),
            ),

          // قائمة المفضلة
          Expanded(
            child: _buildFavoritesList(),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المفضلة
  Widget _buildFavoritesList() {
    if (_isLoading) {
      return const Center(
        child: EnhancedProgressIndicator(
          message: 'جاري تحميل المفضلة...',
        ),
      );
    }

    if (_favorites.isEmpty) {
      return _buildEmptyState();
    }

    if (_filteredFavorites.isEmpty && _searchQuery.isNotEmpty) {
      return _buildNoSearchResults();
    }

    return RefreshIndicator(
      onRefresh: () => _loadFavorites(refresh: true),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _filteredFavorites.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _filteredFavorites.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final favorite = _filteredFavorites[index];
          return FavoritePropertyRequestCard(
            favorite: favorite,
            onRemove: () => _loadFavorites(refresh: true),
          );
        },
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryOrange.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppColors.orangeGradient,
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.favorite_border,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد مفضلة بعد',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'ابدأ بإضافة طلبات العقارات التي تهمك إلى المفضلة',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة عدم وجود نتائج بحث
  Widget _buildNoSearchResults() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.search_off,
              size: 48,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم نجد أي مفضلة تطابق بحثك عن "$_searchQuery"',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
