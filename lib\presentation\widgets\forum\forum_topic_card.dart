import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/models/forum/forum_topic_model.dart';
import '../../../domain/models/forum/topic_model.dart';

/// بطاقة موضوع المنتدى المحسنة
class ForumTopicCard extends StatelessWidget {
  /// موضوع المنتدى
  final ForumTopic topic;

  /// دالة عند النقر على البطاقة
  final VoidCallback onTap;

  /// ما إذا كانت البطاقة مميزة
  final bool isCompact;

  const ForumTopicCard({
    super.key,
    required this.topic,
    required this.onTap,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد لون الموضوع بناءً على حالته
    final Color topicColor = _getTopicColor(topic.status);

    // تحديد أيقونة الموضوع بناءً على نوعه
    final IconData topicIcon = _getTopicIcon(topic.type);

    // تحديد لون الخلفية
    final Color backgroundColor = Colors.white;

    return Card(
      elevation: 2,
      shadowColor: Colors.black12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: topicColor.withOpacity(0.3),
          width: 1)),
      color: backgroundColor,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: isCompact
              ? _buildCompactView(topicColor, topicIcon)
              : _buildFullView(topicColor, topicIcon, context))));
  }

  /// بناء العرض المختصر للموضوع
  Widget _buildCompactView(Color topicColor, IconData topicIcon) {
    return Row(
      children: [
        // أيقونة الموضوع
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: topicColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8)),
          child: Icon(
            topicIcon,
            color: topicColor,
            size: 20)),
        const SizedBox(width: 12),
        // عنوان الموضوع وعدد الردود
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                topic.title,
                style: AppTextStyles.headline5.copyWith(
                  fontWeight: FontWeight.bold),
                maxLines: 1,
                overflow: TextOverflow.ellipsis),
              const SizedBox(height: 4),
              Text(
                '${topic.repliesCount} رد • ${topic.viewsCount} مشاهدة',
                style: AppTextStyles.caption.copyWith(
                  color: Colors.black54)),
            ])),
        // وقت آخر تحديث
        Text(
          timeago.format(topic.updatedAt, locale: 'ar'),
          style: AppTextStyles.caption.copyWith(
            color: Colors.black45,
            fontSize: 10)),
      ]);
  }

  /// بناء العرض الكامل للموضوع
  Widget _buildFullView(
      Color topicColor, IconData topicIcon, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // رأس الموضوع
        Row(
          children: [
            // أيقونة الموضوع
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: topicColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8)),
              child: Icon(
                topicIcon,
                color: topicColor,
                size: 24)),
            const SizedBox(width: 12),
            // عنوان الموضوع
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    topic.title,
                    style: AppTextStyles.headline4.copyWith(
                      fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),
                  const SizedBox(height: 4),
                  // الفئة
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: topicColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4)),
                    child: Text(
                      topic.categoryName,
                      style: AppTextStyles.caption.copyWith(
                        color: topicColor,
                        fontWeight: FontWeight.bold))),
                ])),
            // مؤشرات الحالة
            if (topic.status == TopicStatus.pinned ||
                topic.status == TopicStatus.pinnedAndFeatured)
              Tooltip(
                message: 'مثبت',
                child: Icon(
                  Icons.push_pin,
                  color: AppColors.warning,
                  size: 16)),
            if (topic.status == TopicStatus.featured ||
                topic.status == TopicStatus.pinnedAndFeatured) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: 'مميز',
                child: Icon(
                  Icons.star,
                  color: AppColors.warning,
                  size: 16)),
            ],
            if (topic.status == TopicStatus.closed ||
                topic.status == TopicStatus.closedAndPinned) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: 'مغلق',
                child: Icon(
                  Icons.lock,
                  color: AppColors.error,
                  size: 16)),
            ],
            if (topic.isSolved) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: 'محلول',
                child: Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 16)),
            ],
          ]),

        // محتوى الموضوع
        if (topic.content.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            topic.content,
            style: AppTextStyles.bodyText2.copyWith(
              color: Colors.black87),
            maxLines: 2,
            overflow: TextOverflow.ellipsis),
        ],

        // الوسوم
        if (topic.tags != null && topic.tags!.isNotEmpty) ...[
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: topic.tags!.map((tag) {
              return Chip(
                label: Text(
                  tag,
                  style: AppTextStyles.caption.copyWith(
                    color: Colors.black87)),
                backgroundColor: Colors.grey.shade200,
                padding: EdgeInsets.zero,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact);
            }).toList()),
        ],

        const SizedBox(height: 12),
        const Divider(height: 1),
        const SizedBox(height: 12),

        // معلومات الموضوع
        Row(
          children: [
            // صورة المستخدم
            if (topic.userImage != null)
              CircleAvatar(
                radius: 16,
                backgroundImage: NetworkImage(topic.userImage!))
            else
              CircleAvatar(
                radius: 16,
                backgroundColor: topicColor.withOpacity(0.2),
                child: Icon(
                  Icons.person,
                  size: 20,
                  color: topicColor)),
            const SizedBox(width: 8),
            // اسم المستخدم
            Text(
              topic.userName,
              style: AppTextStyles.bodyText1.copyWith(
                fontWeight: FontWeight.bold)),
            const Spacer(),
            // إحصائيات الموضوع
            _buildStatItem(Icons.remove_red_eye, '${topic.viewsCount}'),
            const SizedBox(width: 12),
            _buildStatItem(Icons.forum, '${topic.repliesCount}'),
            const SizedBox(width: 12),
            _buildStatItem(Icons.favorite, '${topic.likesCount}'),
          ]),

        const SizedBox(height: 12),

        // أزرار التفاعل
        Row(
          children: [
            // زر المفضلة
            _buildActionButton(
              icon: Icons.favorite_border,
              label: 'مفضلة',
              onTap: () => _toggleFavorite(),
              color: AppColors.primary),
            const SizedBox(width: 16),
            // زر الحفظ
            _buildActionButton(
              icon: Icons.bookmark_border,
              label: 'حفظ',
              onTap: () => _toggleBookmark(),
              color: AppColors.primaryDark),
            const SizedBox(width: 16),
            // زر المشاركة
            _buildActionButton(
              icon: Icons.share,
              label: 'مشاركة',
              onTap: () => _shareTopic(),
              color: AppColors.primaryLight),
          ]),

        // آخر رد
        if (topic.lastReplyUserName != null) ...[
          const SizedBox(height: 12),
          Row(
            children: [
              const Icon(
                Icons.reply,
                size: 14,
                color: Colors.black45),
              const SizedBox(width: 4),
              Text(
                'آخر رد: ',
                style: AppTextStyles.caption.copyWith(
                  color: Colors.black54)),
              Text(
                topic.lastReplyUserName!,
                style: AppTextStyles.caption.copyWith(
                  color: Colors.black87,
                  fontWeight: FontWeight.bold)),
              const Spacer(),
              Text(
                timeago.format(topic.lastReplyDate ?? topic.updatedAt,
                    locale: 'ar'),
                style: AppTextStyles.caption.copyWith(
                  color: Colors.black45)),
            ]),
        ],
      ]);
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(IconData icon, String count) {
    return Row(
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.black45),
        const SizedBox(width: 4),
        Text(
          count,
          style: AppTextStyles.caption.copyWith(
            color: Colors.black54)),
      ]);
  }

  /// الحصول على لون الموضوع بناءً على حالته
  Color _getTopicColor(TopicStatus status) {
    switch (status) {
      case TopicStatus.pinned:
      case TopicStatus.pinnedAndFeatured:
        return AppColors.warning;
      case TopicStatus.featured:
        return AppColors.info;
      case TopicStatus.closed:
      case TopicStatus.closedAndPinned:
        return AppColors.error;
      case TopicStatus.deleted:
        return Colors.grey;
      case TopicStatus.open:
      default:
        return AppColors.primary;
    }
  }

  /// الحصول على أيقونة الموضوع بناءً على نوعه
  IconData _getTopicIcon(TopicType type) {
    switch (type) {
      case TopicType.announcement:
        return Icons.campaign;
      case TopicType.poll:
        return Icons.poll;
      case TopicType.question:
        return Icons.help;
      case TopicType.article:
        return Icons.article;
      case TopicType.propertyRequest:
        return Icons.real_estate_agent;
      case TopicType.normal:
      default:
        return Icons.forum;
    }
  }

  /// بناء زر التفاعل
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: AppTextStyles.caption.copyWith(
                color: color,
                fontWeight: FontWeight.w500)),
          ])));
  }

  /// تبديل حالة المفضلة
  void _toggleFavorite() {
    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(onTap as BuildContext).showSnackBar(
      SnackBar(
        content: Text(
          'تم إضافة الموضوع "${topic.title}" إلى المفضلة',
          style: const TextStyle(color: Colors.white)),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'تراجع',
          textColor: Colors.white,
          onPressed: () {
            // منطق التراجع
          })));
  }

  /// تبديل حالة الحفظ
  void _toggleBookmark() {
    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(onTap as BuildContext).showSnackBar(
      SnackBar(
        content: Text(
          'تم حفظ الموضوع "${topic.title}"',
          style: const TextStyle(color: Colors.white)),
        backgroundColor: AppColors.primaryDark,
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'عرض المحفوظات',
          textColor: Colors.white,
          onPressed: () {
            // الانتقال لصفحة المحفوظات
          })));
  }

  /// مشاركة الموضوع
  void _shareTopic() async {
    try {
      final shareText = '''
🔗 موضوع من منتدى كريا

📝 ${topic.title}

${topic.content.isNotEmpty ? topic.content.substring(0, topic.content.length > 100 ? 100 : topic.content.length) + (topic.content.length > 100 ? '...' : '') : ''}

👤 بواسطة: ${topic.userName}
📊 ${topic.repliesCount} رد • ${topic.viewsCount} مشاهدة

📱 حمل تطبيق كريا للعقارات
      ''';

      await Share.share(
        shareText,
        subject: 'موضوع من منتدى كريا: ${topic.title}');
    } catch (e) {
      // في حالة فشل المشاركة، نسخ النص إلى الحافظة
      await Clipboard.setData(ClipboardData(
        text: 'موضوع من منتدى كريا: ${topic.title}'));

      if (onTap is BuildContext) {
        ScaffoldMessenger.of(onTap as BuildContext).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ رابط الموضوع إلى الحافظة'),
            backgroundColor: AppColors.info,
            duration: Duration(seconds: 2)));
      }
    }
  }
}
