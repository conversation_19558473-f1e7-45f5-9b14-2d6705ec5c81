import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

import '../../domain/entities/forum_notification.dart';
import '../../domain/entities/forum_topic.dart';
import '../../domain/entities/forum_post.dart';
import '../../domain/entities/forum_category.dart';

/// خدمة إشعارات المنتدى
class ForumNotificationService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  /// إنشاء خدمة إشعارات المنتدى
  ForumNotificationService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  /// الحصول على إشعارات المستخدم
  Future<List<ForumNotification>> getUserNotifications({
    int limit = 20,
    String? lastNotificationId,
    bool includeRead = false,
  }) async {
    final user = _auth.currentUser;
    if (user == null) {
      return [];
    }

    try {
      Query query = _firestore
          .collection('forum_notifications')
          .where('userId', isEqualTo: user.uid)
          .where('status', whereIn: includeRead
              ? [NotificationStatus.unread.index, NotificationStatus.read.index]
              : [NotificationStatus.unread.index])
          .orderBy('createdAt', descending: true);

      if (lastNotificationId != null) {
        final lastDoc = await _firestore
            .collection('forum_notifications')
            .doc(lastNotificationId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => ForumNotification.fromSnapshot(doc))
          .toList();
    } catch (e, stackTrace) {
      debugPrint('Error getting user notifications: $e');
      debugPrint(stackTrace.toString());
      return [];
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadNotificationsCount() async {
    final user = _auth.currentUser;
    if (user == null) {
      return 0;
    }

    try {
      final snapshot = await _firestore
          .collection('forum_notifications')
          .where('userId', isEqualTo: user.uid)
          .where('status', isEqualTo: NotificationStatus.unread.index)
          .count()
          .get();

      return snapshot.count;
    } catch (e) {
      debugPrint('Error getting unread notifications count: $e');
      return 0;
    }
  }

  /// تحديث حالة الإشعار
  Future<bool> updateNotificationStatus(
    String notificationId,
    NotificationStatus status) async {
    final user = _auth.currentUser;
    if (user == null) {
      return false;
    }

    try {
      final notificationRef =
          _firestore.collection('forum_notifications').doc(notificationId);
      final notificationDoc = await notificationRef.get();

      if (!notificationDoc.exists) {
        return false;
      }

      final notification = ForumNotification.fromSnapshot(notificationDoc);
      if (notification.userId != user.uid) {
        return false;
      }

      Map<String, dynamic> updateData = {
        'status': status.index,
      };

      if (status == NotificationStatus.read) {
        updateData['readAt'] = FieldValue.serverTimestamp();
      }

      await notificationRef.update(updateData);
      return true;
    } catch (e) {
      debugPrint('Error updating notification status: $e');
      return false;
    }
  }

  /// تحديث حالة جميع الإشعارات غير المقروءة إلى مقروءة
  Future<bool> markAllNotificationsAsRead() async {
    final user = _auth.currentUser;
    if (user == null) {
      return false;
    }

    try {
      final batch = _firestore.batch();
      final snapshot = await _firestore
          .collection('forum_notifications')
          .where('userId', isEqualTo: user.uid)
          .where('status', isEqualTo: NotificationStatus.unread.index)
          .get();

      if (snapshot.docs.isEmpty) {
        return true;
      }

      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {
          'status': NotificationStatus.read.index,
          'readAt': FieldValue.serverTimestamp(),
        });
      }

      await batch.commit();
      return true;
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
      return false;
    }
  }

  /// حذف إشعار
  Future<bool> deleteNotification(String notificationId) async {
    final user = _auth.currentUser;
    if (user == null) {
      return false;
    }

    try {
      final notificationRef =
          _firestore.collection('forum_notifications').doc(notificationId);
      final notificationDoc = await notificationRef.get();

      if (!notificationDoc.exists) {
        return false;
      }

      final notification = ForumNotification.fromSnapshot(notificationDoc);
      if (notification.userId != user.uid) {
        return false;
      }

      await notificationRef.update({
        'status': NotificationStatus.deleted.index,
      });
      return true;
    } catch (e) {
      debugPrint('Error deleting notification: $e');
      return false;
    }
  }

  /// حذف جميع الإشعارات
  Future<bool> deleteAllNotifications() async {
    final user = _auth.currentUser;
    if (user == null) {
      return false;
    }

    try {
      final batch = _firestore.batch();
      final snapshot = await _firestore
          .collection('forum_notifications')
          .where('userId', isEqualTo: user.uid)
          .where('status', whereIn: [
            NotificationStatus.unread.index,
            NotificationStatus.read.index
          ])
          .get();

      if (snapshot.docs.isEmpty) {
        return true;
      }

      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {
          'status': NotificationStatus.deleted.index,
        });
      }

      await batch.commit();
      return true;
    } catch (e) {
      debugPrint('Error deleting all notifications: $e');
      return false;
    }
  }

  /// إنشاء إشعار جديد
  Future<String?> createNotification({
    required String userId,
    required NotificationType type,
    required String title,
    required String body,
    String? senderId,
    String? senderName,
    String? senderImage,
    String? topicId,
    String? topicTitle,
    String? postId,
    String? categoryId,
    String? categoryName,
    Map<String, dynamic>? data,
  }) async {
    try {
      final notification = ForumNotification(
        id: '', // سيتم تعيينه من Firestore
        userId: userId,
        type: type,
        status: NotificationStatus.unread,
        title: title,
        body: body,
        senderId: senderId,
        senderName: senderName,
        senderImage: senderImage,
        topicId: topicId,
        topicTitle: topicTitle,
        postId: postId,
        categoryId: categoryId,
        categoryName: categoryName,
        data: data,
        createdAt: DateTime.now());

      final docRef = await _firestore
          .collection('forum_notifications')
          .add(notification.toMap());
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating notification: $e');
      return null;
    }
  }
}
