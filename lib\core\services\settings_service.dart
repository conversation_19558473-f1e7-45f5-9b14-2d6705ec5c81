import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import '../models/app_settings.dart';
import 'notification_service.dart';

/// خدمة إدارة إعدادات التطبيق
class SettingsService {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  static const String _settingsKey = 'app_settings';
  static const String _themeKey = 'theme_mode';
  static const String _notificationsKey = 'notifications_enabled';
  static const String _locationKey = 'location_enabled';
  static const String _searchHistoryKey = 'save_search_history';
  static const String _autoSyncKey = 'auto_sync_enabled';
  static const String _offlineModeKey = 'offline_mode_enabled';
  static const String _dataUsageKey = 'data_usage_mode';
  static const String _imageQualityKey = 'image_quality';
  static const String _cacheKey = 'cache_enabled';
  static const String _analyticsKey = 'analytics_enabled';
  static const String _crashReportingKey = 'crash_reporting_enabled';
  static const String _performanceModeKey = 'performance_mode';
  static const String _autoBackupKey = 'auto_backup_enabled';
  static const String _biometricKey = 'biometric_enabled';
  static const String _autoLockKey = 'auto_lock_enabled';
  static const String _lockTimeoutKey = 'lock_timeout';

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  late SharedPreferences _prefs;
  late NotificationService _notificationService;

  AppSettings? _currentSettings;
  bool _isInitialized = false;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    _prefs = await SharedPreferences.getInstance();
    _notificationService = NotificationService();
    await _loadSettings();
    _isInitialized = true;
  }

  /// تحميل الإعدادات
  Future<void> _loadSettings() async {
    try {
      // تحميل الإعدادات من التخزين المحلي
      final settingsJson = _prefs.getString(_settingsKey);

      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        _currentSettings = AppSettings.fromJson(settingsMap);
      } else {
        // إنشاء إعدادات افتراضية
        _currentSettings = AppSettings.defaultSettings();
        await _saveSettings();
      }

      // مزامنة مع Firebase إذا كان المستخدم مسجل الدخول
      await _syncWithFirebase();
    } catch (e) {
      // في حالة الخطأ، استخدم الإعدادات الافتراضية
      _currentSettings = AppSettings.defaultSettings();
      await _saveSettings();
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    if (_currentSettings == null) return;

    try {
      // حفظ في التخزين المحلي
      final settingsJson = jsonEncode(_currentSettings!.toJson());
      await _prefs.setString(_settingsKey, settingsJson);

      // مزامنة مع Firebase
      await _syncWithFirebase();
    } catch (e) {
      debugPrint('Error saving settings: $e');
    }
  }

  /// مزامنة الإعدادات مع Firebase
  Future<void> _syncWithFirebase() async {
    try {
      final user = _auth.currentUser;
      if (user != null && _currentSettings != null) {
        await _firestore
            .collection('users')
            .doc(user.uid)
            .collection('settings')
            .doc('app_settings')
            .set(_currentSettings!.toJson(), SetOptions(merge: true));
      }
    } catch (e) {
      debugPrint('Error syncing settings with Firebase: $e');
    }
  }

  /// الحصول على الإعدادات الحالية
  AppSettings get currentSettings {
    return _currentSettings ?? AppSettings.defaultSettings();
  }

  /// تحديث إعدادات المظهر
  Future<void> updateThemeSettings({
    ThemeMode? themeMode,
    bool? useSystemTheme,
    String? primaryColor,
    String? accentColor,
  }) async {
    if (_currentSettings == null) return;

    _currentSettings = _currentSettings!.copyWith(
      themeSettings: _currentSettings!.themeSettings.copyWith(
        themeMode: themeMode,
        useSystemTheme: useSystemTheme,
        primaryColor: primaryColor,
        accentColor: accentColor,
      ),
    );

    await _saveSettings();
  }

  /// تحديث إعدادات الإشعارات
  Future<void> updateNotificationSettings({
    bool? pushNotifications,
    bool? emailNotifications,
    bool? newEstateNotifications,
    bool? estateUpdateNotifications,
    bool? messageNotifications,
    bool? accountNotifications,
    bool? specialOfferNotifications,
    bool? reminderNotifications,
    bool? systemNotifications,
    String? notificationSound,
    bool? vibration,
    String? quietHoursStart,
    String? quietHoursEnd,
  }) async {
    if (_currentSettings == null) return;

    _currentSettings = _currentSettings!.copyWith(
      notificationSettings: _currentSettings!.notificationSettings.copyWith(
        pushNotifications: pushNotifications,
        emailNotifications: emailNotifications,
        newEstateNotifications: newEstateNotifications,
        estateUpdateNotifications: estateUpdateNotifications,
        messageNotifications: messageNotifications,
        accountNotifications: accountNotifications,
        specialOfferNotifications: specialOfferNotifications,
        reminderNotifications: reminderNotifications,
        systemNotifications: systemNotifications,
        notificationSound: notificationSound,
        vibration: vibration,
        quietHoursStart: quietHoursStart,
        quietHoursEnd: quietHoursEnd,
      ),
    );

    await _saveSettings();

    // تحديث إعدادات الإشعارات في خدمة الإشعارات
    await _notificationService.updateNotificationSettings({
      'enablePushNotifications': pushNotifications ?? _currentSettings!.notificationSettings.pushNotifications,
      'enableEmailNotifications': emailNotifications ?? _currentSettings!.notificationSettings.emailNotifications,
      'enableNewEstateNotifications': newEstateNotifications ?? _currentSettings!.notificationSettings.newEstateNotifications,
      'enableEstateUpdateNotifications': estateUpdateNotifications ?? _currentSettings!.notificationSettings.estateUpdateNotifications,
      'enableMessageNotifications': messageNotifications ?? _currentSettings!.notificationSettings.messageNotifications,
      'enableAccountNotifications': accountNotifications ?? _currentSettings!.notificationSettings.accountNotifications,
      'enableSpecialOfferNotifications': specialOfferNotifications ?? _currentSettings!.notificationSettings.specialOfferNotifications,
      'enableReminderNotifications': reminderNotifications ?? _currentSettings!.notificationSettings.reminderNotifications,
      'enableSystemNotifications': systemNotifications ?? _currentSettings!.notificationSettings.systemNotifications,
    });
  }

  /// تحديث إعدادات الخصوصية والأمان
  Future<void> updatePrivacySettings({
    bool? locationServices,
    bool? saveSearchHistory,
    bool? shareUsageData,
    bool? personalizedAds,
    bool? biometricAuth,
    bool? autoLock,
    int? lockTimeout,
    bool? twoFactorAuth,
    bool? allowDataCollection,
  }) async {
    if (_currentSettings == null) return;

    _currentSettings = _currentSettings!.copyWith(
      privacySettings: _currentSettings!.privacySettings.copyWith(
        locationServices: locationServices,
        saveSearchHistory: saveSearchHistory,
        shareUsageData: shareUsageData,
        personalizedAds: personalizedAds,
        biometricAuth: biometricAuth,
        autoLock: autoLock,
        lockTimeout: lockTimeout,
        twoFactorAuth: twoFactorAuth,
        allowDataCollection: allowDataCollection,
      ),
    );

    await _saveSettings();
  }

  /// تحديث إعدادات الأداء
  Future<void> updatePerformanceSettings({
    bool? autoSync,
    bool? offlineMode,
    String? dataUsageMode,
    String? imageQuality,
    bool? cacheEnabled,
    bool? analyticsEnabled,
    bool? crashReporting,
    bool? performanceMode,
    bool? autoBackup,
  }) async {
    if (_currentSettings == null) return;

    _currentSettings = _currentSettings!.copyWith(
      performanceSettings: _currentSettings!.performanceSettings.copyWith(
        autoSync: autoSync,
        offlineMode: offlineMode,
        dataUsageMode: dataUsageMode,
        imageQuality: imageQuality,
        cacheEnabled: cacheEnabled,
        analyticsEnabled: analyticsEnabled,
        crashReporting: crashReporting,
        performanceMode: performanceMode,
        autoBackup: autoBackup,
      ),
    );

    await _saveSettings();
  }

  /// تحديث إعدادات التطبيق العامة
  Future<void> updateGeneralSettings({
    String? language,
    String? region,
    String? currency,
    String? dateFormat,
    String? timeFormat,
    bool? autoUpdate,
    bool? betaFeatures,
  }) async {
    if (_currentSettings == null) return;

    _currentSettings = _currentSettings!.copyWith(
      generalSettings: _currentSettings!.generalSettings.copyWith(
        language: language,
        region: region,
        currency: currency,
        dateFormat: dateFormat,
        timeFormat: timeFormat,
        autoUpdate: autoUpdate,
        betaFeatures: betaFeatures,
      ),
    );

    await _saveSettings();
  }

  /// إعادة تعيين الإعدادات إلى القيم الافتراضية
  Future<void> resetToDefaults() async {
    _currentSettings = AppSettings.defaultSettings();
    await _saveSettings();
  }

  /// تصدير الإعدادات
  Map<String, dynamic> exportSettings() {
    return _currentSettings?.toJson() ?? {};
  }

  /// استيراد الإعدادات
  Future<void> importSettings(Map<String, dynamic> settingsData) async {
    try {
      _currentSettings = AppSettings.fromJson(settingsData);
      await _saveSettings();
    } catch (e) {
      debugPrint('Error importing settings: $e');
      throw Exception('فشل في استيراد الإعدادات');
    }
  }

  /// الحصول على معلومات التطبيق
  Future<Map<String, String>> getAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return {
        'appName': packageInfo.appName,
        'packageName': packageInfo.packageName,
        'version': packageInfo.version,
        'buildNumber': packageInfo.buildNumber,
        'buildSignature': packageInfo.buildSignature,
      };
    } catch (e) {
      return {
        'appName': 'Krea',
        'packageName': 'com.krea.app',
        'version': '1.0.0',
        'buildNumber': '1',
        'buildSignature': '',
      };
    }
  }

  /// تنظيف البيانات والتخزين المؤقت
  Future<void> clearAppData() async {
    try {
      // مسح التخزين المؤقت
      await _prefs.clear();

      // إعادة تعيين الإعدادات
      _currentSettings = AppSettings.defaultSettings();
      await _saveSettings();

      // مسح بيانات Firebase المحلية (إذا أردت)
      // await FirebaseAuth.instance.signOut();
    } catch (e) {
      debugPrint('Error clearing app data: $e');
      throw Exception('فشل في مسح بيانات التطبيق');
    }
  }

  /// التحقق من وجود تحديثات
  Future<bool> checkForUpdates() async {
    // هنا يمكن إضافة منطق التحقق من التحديثات
    // مثل استدعاء API أو التحقق من متجر التطبيقات
    return false;
  }

  /// الحصول على حجم التخزين المؤقت
  Future<String> getCacheSize() async {
    try {
      // حساب حجم التخزين المؤقت الفعلي
      final directory = await getTemporaryDirectory();
      int totalSize = 0;

      if (await directory.exists()) {
        await for (FileSystemEntity entity in directory.list(recursive: true)) {
          if (entity is File) {
            try {
              final fileSize = await entity.length();
              totalSize += fileSize;
            } catch (e) {
              // تجاهل الملفات التي لا يمكن قراءتها
            }
          }
        }
      }

      // تحويل إلى ميجابايت
      double sizeInMB = totalSize / (1024 * 1024);
      return "${sizeInMB.toStringAsFixed(1)} MB";
    } catch (e) {
      debugPrint('Error calculating cache size: $e');
      return "غير متاح";
    }
  }

  /// مسح التخزين المؤقت
  Future<void> clearCache() async {
    try {
      // مسح التخزين المؤقت الفعلي
      final tempDirectory = await getTemporaryDirectory();

      if (await tempDirectory.exists()) {
        await for (FileSystemEntity entity in tempDirectory.list()) {
          try {
            if (entity is File) {
              await entity.delete();
            } else if (entity is Directory) {
              await entity.delete(recursive: true);
            }
          } catch (e) {
            // تجاهل الملفات التي لا يمكن حذفها
            debugPrint('Could not delete: ${entity.path}');
          }
        }
      }
    } catch (e) {
      debugPrint('Error clearing cache: $e');
      throw Exception('فشل في مسح التخزين المؤقت');
    }
  }
}
