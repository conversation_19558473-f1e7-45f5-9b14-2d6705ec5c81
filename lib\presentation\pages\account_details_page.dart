// lib/presentation/pages/account_details_page.dart
import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

import '../../domain/entities/user_profile.dart';
import '../../domain/repositories/user_repository.dart';

class AccountDetailsPage extends StatefulWidget {
  const AccountDetailsPage({super.key});

  @override
  _AccountDetailsPageState createState() => _AccountDetailsPageState();
}

class _AccountDetailsPageState extends State<AccountDetailsPage> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  // Controllers for fields.
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  String _email = "";
  String _profileImageUrl = "";
  String _userType = "user"; // "user", "owner", "company"
  File? _newProfileImage;

  final ImagePicker _picker = ImagePicker();

  UserProfile? _profile;

  // تحميل بيانات المستخدم باستخدام UserRepository.
  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      _email = user.email ?? "";
      final userRepository = RepositoryProvider.of<UserRepository>(context);
      _profile = await userRepository.getUserProfile(user.uid);
      _nameController.text = _profile?.fullNameOrCompanyName ?? "";
      _phoneController.text = _profile?.phone ?? "";
      _addressController.text = _profile?.address ?? "";
      _profileImageUrl = _profile?.profileImageUrl ?? "";
      _userType = _profile?.userType ?? "user";
    }
    setState(() {
      _isLoading = false;
    });
  }

  // اختيار صورة الملف الشخصي.
  Future<void> _pickProfileImage() async {
    final XFile? picked = await _picker.pickImage(source: ImageSource.gallery);
    if (picked != null) {
      setState(() {
        _newProfileImage = File(picked.path);
      });
    }
  }

  // حفظ التغييرات عبر UserRepository.
  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() {
      _isLoading = true;
    });
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      final userRepository = RepositoryProvider.of<UserRepository>(context);
      String profileUrl = _profileImageUrl;
      if (_newProfileImage != null) {
        profileUrl = await userRepository.uploadProfileImage(
            user.uid, _newProfileImage!);
      }
      final updatedProfile = UserProfile(
        userId: user.uid,
        email: _email,
        fullNameOrCompanyName: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        address: _addressController.text.trim(),
        profileImageUrl: profileUrl,
        userType: _userType);
      await userRepository.updateUserProfile(updatedProfile);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("تم حفظ التغييرات بنجاح")));
    }
    setState(() {
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تحديد تسمية الحقل بناءً على نوع المستخدم.
    final String nameLabel =
        _userType == "company" ? "اسم الشركة" : "الاسم الكامل";

    return Scaffold(
      appBar: AppBar(
        title: const Text("بيانات الحساب")),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // صورة الملف الشخصي.
                    GestureDetector(
                      onTap: _pickProfileImage,
                      child: CircleAvatar(
                        radius: 50,
                        backgroundImage: _newProfileImage != null
                            ? FileImage(_newProfileImage!)
                            : (_profileImageUrl.isNotEmpty
                                ? NetworkImage(_profileImageUrl)
                                : null) as ImageProvider<Object>?,
                        child: (_newProfileImage == null &&
                                _profileImageUrl.isEmpty)
                            ? const Icon(Icons.camera_alt,
                                size: 50, color: Colors.white70)
                            : null)),
                    const SizedBox(height: 16),
                    // البريد الإلكتروني (غير قابل للتعديل)
                    TextFormField(
                      initialValue: _email,
                      decoration: const InputDecoration(
                        labelText: "البريد الإلكتروني",
                        border: OutlineInputBorder()),
                      readOnly: true),
                    const SizedBox(height: 16),
                    // الاسم (اسم الشركة أو الاسم الكامل حسب نوع المستخدم)
                    TextFormField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        labelText: nameLabel,
                        border: const OutlineInputBorder()),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "الرجاء إدخال $nameLabel";
                        }
                        return null;
                      }),
                    const SizedBox(height: 16),
                    // رقم الهاتف
                    TextFormField(
                      controller: _phoneController,
                      decoration: const InputDecoration(
                        labelText: "رقم الهاتف",
                        border: OutlineInputBorder()),
                      keyboardType: TextInputType.phone),
                    const SizedBox(height: 16),
                    // العنوان
                    TextFormField(
                      controller: _addressController,
                      decoration: const InputDecoration(
                        labelText: "العنوان",
                        border: OutlineInputBorder())),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: _saveChanges,
                      child: const Text("حفظ التغييرات")),
                  ]))));
  }
}
