import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:math';

/// نموذج لحساب التمويل العقاري
class MortgageCalculation extends Equatable {
  /// معرف الحساب
  final String id;
  
  /// معرف المستخدم
  final String userId;
  
  /// اسم الحساب
  final String? name;
  
  /// سعر العقار
  final double propertyPrice;
  
  /// الدفعة المقدمة
  final double downPayment;
  
  /// مبلغ التمويل
  final double loanAmount;
  
  /// مدة التمويل بالسنوات
  final int loanTerm;
  
  /// معدل الفائدة السنوي
  final double interestRate;
  
  /// معرف العقار (اختياري)
  final String? estateId;
  
  /// تاريخ إنشاء الحساب
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث للحساب
  final DateTime? updatedAt;
  
  /// ما إذا كان الحساب محفوظ
  final bool isSaved;
  
  /// ملاحظات الحساب
  final String? notes;
  
  /// بيانات إضافية
  final Map<String, dynamic>? additionalData;

  const MortgageCalculation({
    required this.id,
    required this.userId,
    this.name,
    required this.propertyPrice,
    required this.downPayment,
    required this.loanAmount,
    required this.loanTerm,
    required this.interestRate,
    this.estateId,
    required this.createdAt,
    this.updatedAt,
    this.isSaved = false,
    this.notes,
    this.additionalData,
  });

  /// إنشاء نسخة معدلة من الحساب
  MortgageCalculation copyWith({
    String? id,
    String? userId,
    String? name,
    double? propertyPrice,
    double? downPayment,
    double? loanAmount,
    int? loanTerm,
    double? interestRate,
    String? estateId,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSaved,
    String? notes,
    Map<String, dynamic>? additionalData,
  }) {
    return MortgageCalculation(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      propertyPrice: propertyPrice ?? this.propertyPrice,
      downPayment: downPayment ?? this.downPayment,
      loanAmount: loanAmount ?? this.loanAmount,
      loanTerm: loanTerm ?? this.loanTerm,
      interestRate: interestRate ?? this.interestRate,
      estateId: estateId ?? this.estateId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSaved: isSaved ?? this.isSaved,
      notes: notes ?? this.notes,
      additionalData: additionalData ?? this.additionalData);
  }
  
  /// تحويل الحساب إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'propertyPrice': propertyPrice,
      'downPayment': downPayment,
      'loanAmount': loanAmount,
      'loanTerm': loanTerm,
      'interestRate': interestRate,
      'estateId': estateId,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'isSaved': isSaved,
      'notes': notes,
      'additionalData': additionalData,
    };
  }
  
  /// إنشاء حساب من Map
  factory MortgageCalculation.fromMap(Map<String, dynamic> map) {
    return MortgageCalculation(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      name: map['name'],
      propertyPrice: (map['propertyPrice'] ?? 0.0).toDouble(),
      downPayment: (map['downPayment'] ?? 0.0).toDouble(),
      loanAmount: (map['loanAmount'] ?? 0.0).toDouble(),
      loanTerm: map['loanTerm'] ?? 0,
      interestRate: (map['interestRate'] ?? 0.0).toDouble(),
      estateId: map['estateId'],
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
      isSaved: map['isSaved'] ?? false,
      notes: map['notes'],
      additionalData: map['additionalData']);
  }
  
  /// إنشاء حساب من DocumentSnapshot
  factory MortgageCalculation.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return MortgageCalculation.fromMap(data);
  }
  
  /// حساب القسط الشهري
  double calculateMonthlyPayment() {
    if (loanAmount <= 0 || loanTerm <= 0 || interestRate <= 0) {
      return 0.0;
    }
    
    final monthlyInterestRate = interestRate / 100 / 12;
    final numberOfPayments = loanTerm * 12;
    
    return loanAmount * 
        (monthlyInterestRate * pow(1 + monthlyInterestRate, numberOfPayments)) / 
        (pow(1 + monthlyInterestRate, numberOfPayments) - 1);
  }
  
  /// حساب إجمالي المدفوعات
  double calculateTotalPayment() {
    return calculateMonthlyPayment() * loanTerm * 12;
  }
  
  /// حساب إجمالي الفائدة
  double calculateTotalInterest() {
    return calculateTotalPayment() - loanAmount;
  }
  
  /// حساب نسبة التمويل إلى القيمة
  double calculateLoanToValueRatio() {
    if (propertyPrice <= 0) {
      return 0.0;
    }
    
    return (loanAmount / propertyPrice) * 100;
  }
  
  /// حساب نسبة الدفعة المقدمة
  double calculateDownPaymentPercentage() {
    if (propertyPrice <= 0) {
      return 0.0;
    }
    
    return (downPayment / propertyPrice) * 100;
  }
  
  /// حساب جدول السداد
  List<Map<String, dynamic>> calculateAmortizationSchedule() {
    if (loanAmount <= 0 || loanTerm <= 0 || interestRate <= 0) {
      return [];
    }
    
    final monthlyInterestRate = interestRate / 100 / 12;
    final numberOfPayments = loanTerm * 12;
    final monthlyPayment = calculateMonthlyPayment();
    
    double remainingBalance = loanAmount;
    final schedule = <Map<String, dynamic>>[];
    
    for (int i = 1; i <= numberOfPayments; i++) {
      final interestPayment = remainingBalance * monthlyInterestRate;
      final principalPayment = monthlyPayment - interestPayment;
      remainingBalance -= principalPayment;
      
      schedule.add({
        'paymentNumber': i,
        'paymentDate': DateTime(createdAt.year, createdAt.month + i, createdAt.day),
        'monthlyPayment': monthlyPayment,
        'principalPayment': principalPayment,
        'interestPayment': interestPayment,
        'remainingBalance': remainingBalance > 0 ? remainingBalance : 0,
      });
    }
    
    return schedule;
  }
  
  /// حفظ الحساب
  MortgageCalculation save(String name) {
    return copyWith(
      name: name,
      isSaved: true,
      updatedAt: DateTime.now());
  }
  
  /// إلغاء حفظ الحساب
  MortgageCalculation unsave() {
    return copyWith(
      isSaved: false,
      updatedAt: DateTime.now());
  }
  
  /// إضافة ملاحظة
  MortgageCalculation addNote(String note) {
    return copyWith(
      notes: note,
      updatedAt: DateTime.now());
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    name,
    propertyPrice,
    downPayment,
    loanAmount,
    loanTerm,
    interestRate,
    estateId,
    createdAt,
    updatedAt,
    isSaved,
    notes,
    additionalData,
  ];
}
