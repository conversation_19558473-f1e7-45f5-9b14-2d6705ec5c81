// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBPpwrKf9Mg4Al9ZtfhUtmozOJXgSUAVCk',
    appId: '1:951329683889:android:b7de9803cfd70cefaa2590',
    messagingSenderId: '951329683889',
    projectId: 'real-estate-998a9',
    databaseURL: 'https://real-estate-998a9-default-rtdb.firebaseio.com',
    storageBucket: 'real-estate-998a9.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC_NZWeTGb0l4DU94Na4BBnz9uxINoAdxM',
    appId: '1:951329683889:ios:283721b99a184d2caa2590',
    messagingSenderId: '951329683889',
    projectId: 'real-estate-998a9',
    databaseURL: 'https://real-estate-998a9-default-rtdb.firebaseio.com',
    storageBucket: 'real-estate-998a9.firebasestorage.app',
    iosBundleId: 'com.example.kuwaitCorners',
  );
}
