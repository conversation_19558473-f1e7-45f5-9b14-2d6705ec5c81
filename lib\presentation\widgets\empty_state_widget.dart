import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';

/// Widget لعرض حالة فارغة مع رسالة وأيقونة وزر اختياري.
class EmptyStateWidget extends StatelessWidget {
  /// عنوان الحالة الفارغة
  final String title;
  
  /// وصف الحالة الفارغة
  final String? description;
  
  /// أيقونة الحالة الفارغة
  final IconData icon;
  
  /// نص الزر الاختياري
  final String? buttonText;
  
  /// دالة يتم استدعاؤها عند النقر على الزر
  final VoidCallback? onButtonPressed;
  
  /// لون الأيقونة
  final Color? iconColor;
  
  /// حجم الأيقونة
  final double iconSize;

  const EmptyStateWidget({
    super.key,
    required this.title,
    this.description,
    required this.icon,
    this.buttonText,
    this.onButtonPressed,
    this.iconColor,
    this.iconSize = 80,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الحالة الفارغة
            Container(
              width: iconSize * 1.5,
              height: iconSize * 1.5,
              decoration: BoxDecoration(
                color: (iconColor ?? AppColors.primary).withOpacity(0.1),
                shape: BoxShape.circle),
              child: Icon(
                icon,
                size: iconSize,
                color: iconColor ?? AppColors.primary)),
            const SizedBox(height: 24),
            // عنوان الحالة الفارغة
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87),
              textAlign: TextAlign.center),
            if (description != null) ...[
              const SizedBox(height: 12),
              // وصف الحالة الفارغة
              Text(
                description!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                  height: 1.4),
                textAlign: TextAlign.center),
            ],
            if (buttonText != null && onButtonPressed != null) ...[
              const SizedBox(height: 32),
              // زر الإجراء
              Container(
                width: 200,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4)),
                  ]),
                child: ElevatedButton(
                  onPressed: onButtonPressed,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25))),
                  child: Text(
                    buttonText!,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)))),
            ],
          ])));
  }
}
