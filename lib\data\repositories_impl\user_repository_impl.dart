// lib/data/repositories_impl/user_repository_impl.dart
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../../domain/entities/user_profile.dart';
import '../../domain/repositories/user_repository.dart';

class UserRepositoryImpl implements UserRepository {
  final FirebaseFirestore firestore;
  final FirebaseStorage storage;

  UserRepositoryImpl({
    required this.firestore,
    required this.storage,
  });

  @override
  Future<UserProfile> getUserProfile(String userId) async {
    final doc = await firestore.collection('users').doc(userId).get();
    final data = doc.data()!;
    return UserProfile(
      userId: userId,
      email: data['email'] ?? '',
      fullNameOrCompanyName: data['fullNameOrCompanyName'] ?? '',
      phone: data['phone'] ?? '',
      address: data['address'] ?? '',
      profileImageUrl: data['profileImageUrl'] ?? '',
      userType: data['userType'] ?? 'user');
  }

  @override
  Future<void> updateUserProfile(UserProfile profile) async {
    await firestore.collection('users').doc(profile.userId).update({
      'fullNameOrCompanyName': profile.fullNameOrCompanyName,
      'phone': profile.phone,
      'address': profile.address,
      'profileImageUrl': profile.profileImageUrl,
    });
  }

  @override
  Future<String> uploadProfileImage(String userId, File file) async {
    final ref = storage.ref().child('profile_images').child('$userId.jpg');
    await ref.putFile(file);
    return await ref.getDownloadURL();
  }
}
