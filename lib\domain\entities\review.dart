import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج للتقييمات
class Review extends Equatable {
  /// معرف التقييم
  final String id;
  
  /// معرف العنصر المقيم (عقار، وسيط، مالك، شركة)
  final String itemId;
  
  /// نوع العنصر المقيم
  final String itemType;
  
  /// معرف المستخدم الذي قام بالتقييم
  final String userId;
  
  /// اسم المستخدم الذي قام بالتقييم
  final String userName;
  
  /// صورة المستخدم الذي قام بالتقييم
  final String? userImage;
  
  /// عنوان التقييم
  final String? title;
  
  /// نص التقييم
  final String content;
  
  /// التقييم (من 1 إلى 5)
  final double rating;
  
  /// تاريخ إنشاء التقييم
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث للتقييم
  final DateTime? updatedAt;
  
  /// ما إذا كان التقييم تم التحقق منه
  final bool isVerified;
  
  /// ما إذا كان التقييم تم الموافقة عليه
  final bool isApproved;
  
  /// ما إذا كان التقييم تم الإبلاغ عنه
  final bool isReported;
  
  /// عدد الإعجابات
  final int likesCount;
  
  /// عدد التعليقات
  final int commentsCount;
  
  /// قائمة معرفات المستخدمين الذين أعجبوا بالتقييم
  final List<String>? likedBy;
  
  /// صور مرفقة بالتقييم
  final List<String>? images;
  
  /// فيديوهات مرفقة بالتقييم
  final List<String>? videos;
  
  /// تقييمات فرعية (مثل تقييم الموقع، النظافة، الخدمة، الخ)
  final Map<String, double>? subRatings;
  
  /// رد على التقييم
  final Map<String, dynamic>? reply;

  const Review({
    required this.id,
    required this.itemId,
    required this.itemType,
    required this.userId,
    required this.userName,
    this.userImage,
    this.title,
    required this.content,
    required this.rating,
    required this.createdAt,
    this.updatedAt,
    this.isVerified = false,
    this.isApproved = true,
    this.isReported = false,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.likedBy,
    this.images,
    this.videos,
    this.subRatings,
    this.reply,
  });

  /// إنشاء نسخة معدلة من التقييم
  Review copyWith({
    String? id,
    String? itemId,
    String? itemType,
    String? userId,
    String? userName,
    String? userImage,
    String? title,
    String? content,
    double? rating,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    bool? isApproved,
    bool? isReported,
    int? likesCount,
    int? commentsCount,
    List<String>? likedBy,
    List<String>? images,
    List<String>? videos,
    Map<String, double>? subRatings,
    Map<String, dynamic>? reply,
  }) {
    return Review(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      itemType: itemType ?? this.itemType,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      title: title ?? this.title,
      content: content ?? this.content,
      rating: rating ?? this.rating,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      isApproved: isApproved ?? this.isApproved,
      isReported: isReported ?? this.isReported,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      likedBy: likedBy ?? this.likedBy,
      images: images ?? this.images,
      videos: videos ?? this.videos,
      subRatings: subRatings ?? this.subRatings,
      reply: reply ?? this.reply);
  }
  
  /// تحويل التقييم إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'itemId': itemId,
      'itemType': itemType,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'title': title,
      'content': content,
      'rating': rating,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'isVerified': isVerified,
      'isApproved': isApproved,
      'isReported': isReported,
      'likesCount': likesCount,
      'commentsCount': commentsCount,
      'likedBy': likedBy,
      'images': images,
      'videos': videos,
      'subRatings': subRatings,
      'reply': reply,
    };
  }
  
  /// إنشاء تقييم من Map
  factory Review.fromMap(Map<String, dynamic> map) {
    return Review(
      id: map['id'] ?? '',
      itemId: map['itemId'] ?? '',
      itemType: map['itemType'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'],
      title: map['title'],
      content: map['content'] ?? '',
      rating: (map['rating'] ?? 0.0).toDouble(),
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
      isVerified: map['isVerified'] ?? false,
      isApproved: map['isApproved'] ?? true,
      isReported: map['isReported'] ?? false,
      likesCount: map['likesCount'] ?? 0,
      commentsCount: map['commentsCount'] ?? 0,
      likedBy: map['likedBy'] != null ? List<String>.from(map['likedBy']) : null,
      images: map['images'] != null ? List<String>.from(map['images']) : null,
      videos: map['videos'] != null ? List<String>.from(map['videos']) : null,
      subRatings: map['subRatings'] != null 
          ? Map<String, double>.from(map['subRatings'].map(
              (key, value) => MapEntry(key, (value as num).toDouble())))
          : null,
      reply: map['reply']);
  }
  
  /// إنشاء تقييم من DocumentSnapshot
  factory Review.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return Review.fromMap(data);
  }
  
  /// إضافة إعجاب
  Review addLike(String userId) {
    final newLikedBy = List<String>.from(likedBy ?? []);
    
    if (!newLikedBy.contains(userId)) {
      newLikedBy.add(userId);
    }
    
    return copyWith(
      likedBy: newLikedBy,
      likesCount: newLikedBy.length);
  }
  
  /// إزالة إعجاب
  Review removeLike(String userId) {
    if (likedBy == null || !likedBy!.contains(userId)) {
      return this;
    }
    
    final newLikedBy = List<String>.from(likedBy!);
    newLikedBy.remove(userId);
    
    return copyWith(
      likedBy: newLikedBy,
      likesCount: newLikedBy.length);
  }
  
  /// إضافة رد
  Review addReply(String userId, String userName, String content) {
    return copyWith(
      reply: {
        'userId': userId,
        'userName': userName,
        'content': content,
        'createdAt': Timestamp.fromDate(DateTime.now()),
      },
      updatedAt: DateTime.now());
  }
  
  /// إزالة رد
  Review removeReply() {
    return copyWith(
      reply: null,
      updatedAt: DateTime.now());
  }
  
  /// الإبلاغ عن التقييم
  Review report() {
    return copyWith(
      isReported: true,
      updatedAt: DateTime.now());
  }
  
  /// التحقق من التقييم
  Review verify() {
    return copyWith(
      isVerified: true,
      updatedAt: DateTime.now());
  }
  
  /// الموافقة على التقييم
  Review approve() {
    return copyWith(
      isApproved: true,
      updatedAt: DateTime.now());
  }
  
  /// رفض التقييم
  Review reject() {
    return copyWith(
      isApproved: false,
      updatedAt: DateTime.now());
  }

  @override
  List<Object?> get props => [
    id,
    itemId,
    itemType,
    userId,
    userName,
    userImage,
    title,
    content,
    rating,
    createdAt,
    updatedAt,
    isVerified,
    isApproved,
    isReported,
    likesCount,
    commentsCount,
    likedBy,
    images,
    videos,
    subRatings,
    reply,
  ];
}
