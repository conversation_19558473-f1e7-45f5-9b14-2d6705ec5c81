// lib/presentation/pages/enhanced_media_upload_page.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core/services/enhanced_ad_draft_service.dart';
import '../../core/services/image_processing_service.dart';
import '../../core/theme/app_colors.dart';
import '../../domain/entities/estate.dart';
import '../bloc/improved_ad_bloc.dart';
import 'improved_ad_details_page.dart';

/// صفحة رفع الصور والوسائط المحسنة بتصميم عصري وتجربة مستخدم متطورة
class EnhancedMediaUploadPage extends StatefulWidget {
  final Estate estate;

  const EnhancedMediaUploadPage({super.key, required this.estate});

  @override
  State<EnhancedMediaUploadPage> createState() =>
      _EnhancedMediaUploadPageState();
}

class _EnhancedMediaUploadPageState extends State<EnhancedMediaUploadPage>
    with TickerProviderStateMixin {
  // خدمات ومتغيرات
  final ImagePicker _picker = ImagePicker();
  final ImageProcessingService _imageService = ImageProcessingService();
  final EnhancedAdDraftService _draftService = EnhancedAdDraftService();

  // متغيرات الحالة
  List<File> _pickedFiles = [];
  bool _isProcessing = false;
  String? _errorMessage;
  final bool _showTips = true;
  final bool _isFullScreenPreview = false;
  final int _currentPreviewIndex = 0;
  final bool _isDragging = false;
  File? _draggedFile;

  // متغيرات الرسوم المتحركة
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  late AnimationController _rotateController;
  late Animation<double> _rotateAnimation;

  // ثوابت
  final int _maxImages = 10;
  final double _maxSizeMB = 5.0;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500));
    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut);

    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400));
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeOutBack);

    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600));
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic));

    _rotateController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300));
    _rotateAnimation = Tween<double>(
      begin: 0.0,
      end: 0.05).animate(CurvedAnimation(
      parent: _rotateController,
      curve: Curves.elasticIn));

    // تشغيل الرسوم المتحركة
    _fadeController.forward();
    _scaleController.forward();
    _slideController.forward();

    // استرجاع المسودة المحفوظة
    _loadDraft();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    _slideController.dispose();
    _rotateController.dispose();
    super.dispose();
  }

  /// استرجاع المسودة المحفوظة
  Future<void> _loadDraft() async {
    try {
      final draft = await _draftService.getLastDraft();
      if (draft != null && draft.containsKey('imagePaths')) {
        final List<dynamic> paths = draft['imagePaths'];
        if (paths.isNotEmpty) {
          setState(() {
            _pickedFiles = paths.map((path) => File(path.toString())).toList();
          });
        }
      }
    } catch (e) {
      // تجاهل الأخطاء عند استرجاع المسودة
    }
  }

  /// اختيار صور من المعرض
  Future<void> _pickImages() async {
    try {
      setState(() {
        _isProcessing = true;
        _errorMessage = null;
      });

      final files = await _picker.pickMultiImage();
      if (files.isNotEmpty) {
        final availableSlots = _maxImages - _pickedFiles.length;
        if (availableSlots > 0) {
          // تحويل XFile إلى File
          final newFiles =
              files.take(availableSlots).map((f) => File(f.path)).toList();

          // معالجة الصور
          await _processImages(newFiles);

          // تشغيل رسوم متحركة للصور الجديدة
          _scaleController.reset();
          _scaleController.forward();
        } else {
          setState(() {
            _errorMessage =
                "لا يمكن إضافة المزيد من الصور. الحد الأقصى هو $_maxImages صور.";
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = "حدث خطأ أثناء اختيار الصور. يرجى المحاولة مرة أخرى.";
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    try {
      setState(() {
        _isProcessing = true;
        _errorMessage = null;
      });

      final file = await _picker.pickImage(source: ImageSource.camera);
      if (file != null) {
        if (_pickedFiles.length < _maxImages) {
          // تحويل XFile إلى File
          final newFile = File(file.path);

          // معالجة الصورة
          await _processImages([newFile]);

          // تشغيل رسوم متحركة للصورة الجديدة
          _scaleController.reset();
          _scaleController.forward();
        } else {
          setState(() {
            _errorMessage =
                "لا يمكن إضافة المزيد من الصور. الحد الأقصى هو $_maxImages صور.";
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = "حدث خطأ أثناء التقاط الصورة. يرجى المحاولة مرة أخرى.";
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// معالجة الصور المختارة
  Future<void> _processImages(List<File> newFiles) async {
    for (final file in newFiles) {
      // التحقق من حجم الصورة
      final isValidSize =
          await _imageService.isImageSizeValid(file, _maxSizeMB);
      if (!isValidSize) {
        setState(() {
          _errorMessage =
              "حجم الصورة كبير جداً. يجب أن يكون أقل من $_maxSizeMB ميجابايت.";
        });
        continue;
      }

      // التحقق من أبعاد الصورة
      final isValidDimensions =
          await _imageService.isImageDimensionsValid(file);
      if (!isValidDimensions) {
        setState(() {
          _errorMessage =
              "أبعاد الصورة صغيرة جداً. يجب أن تكون على الأقل 300×300 بكسل.";
        });
        continue;
      }

      // إضافة الصورة إلى القائمة
      setState(() {
        _pickedFiles.add(file);
      });
    }

    // حفظ المسودة
    _saveDraft();
  }

  /// تحرير صورة
  Future<void> _editImage(int index) async {
    try {
      final file = _pickedFiles[index];

      final croppedFile = await ImageCropper().cropImage(
        sourcePath: file.path,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'تحرير الصورة',
            toolbarColor: AppColors.primary,
            toolbarWidgetColor: Colors.white,
            lockAspectRatio: false,
            activeControlsWidgetColor: AppColors.primary),
          IOSUiSettings(
            title: 'تحرير الصورة',
            doneButtonTitle: 'تم',
            cancelButtonTitle: 'إلغاء'),
        ]);

      if (croppedFile != null) {
        setState(() {
          _pickedFiles[index] = File(croppedFile.path);
        });

        // حفظ المسودة
        _saveDraft();

        // تشغيل رسوم متحركة للصورة المحررة
        _rotateController.reset();
        _rotateController.forward();
      }
    } catch (e) {
      setState(() {
        _errorMessage = "حدث خطأ أثناء تحرير الصورة. يرجى المحاولة مرة أخرى.";
      });
    }
  }

  /// حذف صورة
  void _deleteImage(int index) {
    setState(() {
      _pickedFiles.removeAt(index);
    });

    // حفظ المسودة
    _saveDraft();
  }

  /// إعادة ترتيب الصور
  void _reorderImages(int oldIndex, int newIndex) {
    setState(() {
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final item = _pickedFiles.removeAt(oldIndex);
      _pickedFiles.insert(newIndex, item);
    });

    // حفظ المسودة
    _saveDraft();
  }

  /// حفظ المسودة
  Future<void> _saveDraft() async {
    final paths = _pickedFiles.map((f) => f.path).toList();
    await _draftService.saveDraft({
      'imagePaths': paths,
      'step': 2,
    });
  }

  /// فتح فيديو المساعدة
  Future<void> _launchVideo() async {
    const url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  /// الانتقال إلى الخطوة التالية
  void _goToNextStep() {
    // حفظ الصور في BLoC
    final paths = _pickedFiles.map((f) => f.path).toList();
    context.read<ImprovedAdBloc>().add(AddImages(paths));

    // الانتقال إلى صفحة التفاصيل
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            ImprovedAdDetailsPage(estate: widget.estate),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero).animate(animation),
              child: child));
        }));
  }
}
