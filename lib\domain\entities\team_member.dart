import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// دور عضو الفريق
enum TeamMemberRole {
  /// مدير
  manager,
  
  /// مشرف
  supervisor,
  
  /// وكيل مبيعات
  salesAgent,
  
  /// مسوق
  marketer,
  
  /// مسؤول خدمة العملاء
  customerService,
  
  /// مسؤول إداري
  admin,
}

/// حالة عضو الفريق
enum TeamMemberStatus {
  /// نشط
  active,
  
  /// غير نشط
  inactive,
  
  /// في إجازة
  onLeave,
  
  /// معلق
  suspended,
}

/// نموذج لعضو الفريق
class TeamMember extends Equatable {
  /// معرف عضو الفريق
  final String id;
  
  /// معرف الشركة
  final String companyId;
  
  /// معرف المستخدم
  final String userId;
  
  /// الاسم الكامل
  final String fullName;
  
  /// البريد الإلكتروني
  final String email;
  
  /// رقم الهاتف
  final String phoneNumber;
  
  /// صورة العضو
  final String? image;
  
  /// دور العضو
  final TeamMemberRole role;
  
  /// حالة العضو
  final TeamMemberStatus status;
  
  /// تاريخ الانضمام
  final DateTime joinDate;
  
  /// تاريخ آخر تسجيل دخول
  final DateTime? lastLoginDate;
  
  /// المشرف المباشر
  final String? supervisorId;
  
  /// اسم المشرف المباشر
  final String? supervisorName;
  
  /// المناطق المسؤول عنها
  final List<String>? assignedAreas;
  
  /// أنواع العقارات المسؤول عنها
  final List<String>? assignedPropertyTypes;
  
  /// قائمة معرفات العملاء المسؤول عنهم
  final List<String>? assignedClients;
  
  /// قائمة معرفات العقارات المسؤول عنها
  final List<String>? assignedEstates;
  
  /// هدف المبيعات الشهري
  final double? monthlySalesTarget;
  
  /// هدف المبيعات السنوي
  final double? annualSalesTarget;
  
  /// نسبة العمولة
  final double? commissionRate;
  
  /// الراتب الأساسي
  final double? baseSalary;
  
  /// إجمالي المبيعات
  final double totalSales;
  
  /// عدد العقارات المباعة/المؤجرة
  final int soldRentedCount;
  
  /// عدد العملاء
  final int clientsCount;
  
  /// عدد المواعيد
  final int appointmentsCount;
  
  /// عدد العروض
  final int offersCount;
  
  /// متوسط التقييم
  final double averageRating;
  
  /// عدد التقييمات
  final int reviewsCount;
  
  /// معلومات إضافية
  final Map<String, dynamic>? additionalInfo;

  const TeamMember({
    required this.id,
    required this.companyId,
    required this.userId,
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    this.image,
    required this.role,
    required this.status,
    required this.joinDate,
    this.lastLoginDate,
    this.supervisorId,
    this.supervisorName,
    this.assignedAreas,
    this.assignedPropertyTypes,
    this.assignedClients,
    this.assignedEstates,
    this.monthlySalesTarget,
    this.annualSalesTarget,
    this.commissionRate,
    this.baseSalary,
    this.totalSales = 0.0,
    this.soldRentedCount = 0,
    this.clientsCount = 0,
    this.appointmentsCount = 0,
    this.offersCount = 0,
    this.averageRating = 0.0,
    this.reviewsCount = 0,
    this.additionalInfo,
  });

  /// إنشاء نسخة معدلة من عضو الفريق
  TeamMember copyWith({
    String? id,
    String? companyId,
    String? userId,
    String? fullName,
    String? email,
    String? phoneNumber,
    String? image,
    TeamMemberRole? role,
    TeamMemberStatus? status,
    DateTime? joinDate,
    DateTime? lastLoginDate,
    String? supervisorId,
    String? supervisorName,
    List<String>? assignedAreas,
    List<String>? assignedPropertyTypes,
    List<String>? assignedClients,
    List<String>? assignedEstates,
    double? monthlySalesTarget,
    double? annualSalesTarget,
    double? commissionRate,
    double? baseSalary,
    double? totalSales,
    int? soldRentedCount,
    int? clientsCount,
    int? appointmentsCount,
    int? offersCount,
    double? averageRating,
    int? reviewsCount,
    Map<String, dynamic>? additionalInfo,
  }) {
    return TeamMember(
      id: id ?? this.id,
      companyId: companyId ?? this.companyId,
      userId: userId ?? this.userId,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      image: image ?? this.image,
      role: role ?? this.role,
      status: status ?? this.status,
      joinDate: joinDate ?? this.joinDate,
      lastLoginDate: lastLoginDate ?? this.lastLoginDate,
      supervisorId: supervisorId ?? this.supervisorId,
      supervisorName: supervisorName ?? this.supervisorName,
      assignedAreas: assignedAreas ?? this.assignedAreas,
      assignedPropertyTypes: assignedPropertyTypes ?? this.assignedPropertyTypes,
      assignedClients: assignedClients ?? this.assignedClients,
      assignedEstates: assignedEstates ?? this.assignedEstates,
      monthlySalesTarget: monthlySalesTarget ?? this.monthlySalesTarget,
      annualSalesTarget: annualSalesTarget ?? this.annualSalesTarget,
      commissionRate: commissionRate ?? this.commissionRate,
      baseSalary: baseSalary ?? this.baseSalary,
      totalSales: totalSales ?? this.totalSales,
      soldRentedCount: soldRentedCount ?? this.soldRentedCount,
      clientsCount: clientsCount ?? this.clientsCount,
      appointmentsCount: appointmentsCount ?? this.appointmentsCount,
      offersCount: offersCount ?? this.offersCount,
      averageRating: averageRating ?? this.averageRating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }
  
  /// تحويل عضو الفريق إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'companyId': companyId,
      'userId': userId,
      'fullName': fullName,
      'email': email,
      'phoneNumber': phoneNumber,
      'image': image,
      'role': role.toString().split('.').last,
      'status': status.toString().split('.').last,
      'joinDate': Timestamp.fromDate(joinDate),
      'lastLoginDate': lastLoginDate != null ? Timestamp.fromDate(lastLoginDate!) : null,
      'supervisorId': supervisorId,
      'supervisorName': supervisorName,
      'assignedAreas': assignedAreas,
      'assignedPropertyTypes': assignedPropertyTypes,
      'assignedClients': assignedClients,
      'assignedEstates': assignedEstates,
      'monthlySalesTarget': monthlySalesTarget,
      'annualSalesTarget': annualSalesTarget,
      'commissionRate': commissionRate,
      'baseSalary': baseSalary,
      'totalSales': totalSales,
      'soldRentedCount': soldRentedCount,
      'clientsCount': clientsCount,
      'appointmentsCount': appointmentsCount,
      'offersCount': offersCount,
      'averageRating': averageRating,
      'reviewsCount': reviewsCount,
      'additionalInfo': additionalInfo,
    };
  }
  
  /// إنشاء عضو فريق من Map
  factory TeamMember.fromMap(Map<String, dynamic> map) {
    return TeamMember(
      id: map['id'] ?? '',
      companyId: map['companyId'] ?? '',
      userId: map['userId'] ?? '',
      fullName: map['fullName'] ?? '',
      email: map['email'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      image: map['image'],
      role: _getTeamMemberRoleFromString(map['role'] ?? 'salesAgent'),
      status: _getTeamMemberStatusFromString(map['status'] ?? 'active'),
      joinDate: map['joinDate'] is Timestamp 
          ? (map['joinDate'] as Timestamp).toDate() 
          : DateTime.now(),
      lastLoginDate: map['lastLoginDate'] is Timestamp 
          ? (map['lastLoginDate'] as Timestamp).toDate() 
          : null,
      supervisorId: map['supervisorId'],
      supervisorName: map['supervisorName'],
      assignedAreas: map['assignedAreas'] != null ? List<String>.from(map['assignedAreas']) : null,
      assignedPropertyTypes: map['assignedPropertyTypes'] != null ? List<String>.from(map['assignedPropertyTypes']) : null,
      assignedClients: map['assignedClients'] != null ? List<String>.from(map['assignedClients']) : null,
      assignedEstates: map['assignedEstates'] != null ? List<String>.from(map['assignedEstates']) : null,
      monthlySalesTarget: map['monthlySalesTarget'] != null ? (map['monthlySalesTarget'] as num).toDouble() : null,
      annualSalesTarget: map['annualSalesTarget'] != null ? (map['annualSalesTarget'] as num).toDouble() : null,
      commissionRate: map['commissionRate'] != null ? (map['commissionRate'] as num).toDouble() : null,
      baseSalary: map['baseSalary'] != null ? (map['baseSalary'] as num).toDouble() : null,
      totalSales: (map['totalSales'] ?? 0.0).toDouble(),
      soldRentedCount: map['soldRentedCount'] ?? 0,
      clientsCount: map['clientsCount'] ?? 0,
      appointmentsCount: map['appointmentsCount'] ?? 0,
      offersCount: map['offersCount'] ?? 0,
      averageRating: (map['averageRating'] ?? 0.0).toDouble(),
      reviewsCount: map['reviewsCount'] ?? 0,
      additionalInfo: map['additionalInfo']);
  }
  
  /// إنشاء عضو فريق من DocumentSnapshot
  factory TeamMember.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return TeamMember.fromMap(data);
  }
  
  /// الحصول على دور عضو الفريق من النص
  static TeamMemberRole _getTeamMemberRoleFromString(String roleStr) {
    switch (roleStr) {
      case 'manager':
        return TeamMemberRole.manager;
      case 'supervisor':
        return TeamMemberRole.supervisor;
      case 'marketer':
        return TeamMemberRole.marketer;
      case 'customerService':
        return TeamMemberRole.customerService;
      case 'admin':
        return TeamMemberRole.admin;
      default:
        return TeamMemberRole.salesAgent;
    }
  }
  
  /// الحصول على حالة عضو الفريق من النص
  static TeamMemberStatus _getTeamMemberStatusFromString(String statusStr) {
    switch (statusStr) {
      case 'inactive':
        return TeamMemberStatus.inactive;
      case 'onLeave':
        return TeamMemberStatus.onLeave;
      case 'suspended':
        return TeamMemberStatus.suspended;
      default:
        return TeamMemberStatus.active;
    }
  }
  
  /// الحصول على اسم دور عضو الفريق بالعربية
  String getTeamMemberRoleName() {
    switch (role) {
      case TeamMemberRole.manager:
        return 'مدير';
      case TeamMemberRole.supervisor:
        return 'مشرف';
      case TeamMemberRole.marketer:
        return 'مسوق';
      case TeamMemberRole.customerService:
        return 'خدمة عملاء';
      case TeamMemberRole.admin:
        return 'مسؤول إداري';
      case TeamMemberRole.salesAgent:
        return 'وكيل مبيعات';
    }
  }
  
  /// الحصول على اسم حالة عضو الفريق بالعربية
  String getTeamMemberStatusName() {
    switch (status) {
      case TeamMemberStatus.inactive:
        return 'غير نشط';
      case TeamMemberStatus.onLeave:
        return 'في إجازة';
      case TeamMemberStatus.suspended:
        return 'معلق';
      case TeamMemberStatus.active:
        return 'نشط';
    }
  }
  
  /// الحصول على لون حالة عضو الفريق
  String getTeamMemberStatusColor() {
    switch (status) {
      case TeamMemberStatus.inactive:
        return '#9E9E9E'; // رمادي
      case TeamMemberStatus.onLeave:
        return '#FF9800'; // برتقالي
      case TeamMemberStatus.suspended:
        return '#F44336'; // أحمر
      case TeamMemberStatus.active:
        return '#4CAF50'; // أخضر
    }
  }
  
  /// تحديث حالة عضو الفريق
  TeamMember updateStatus(TeamMemberStatus newStatus) {
    return copyWith(
      status: newStatus);
  }
  
  /// تحديث دور عضو الفريق
  TeamMember updateRole(TeamMemberRole newRole) {
    return copyWith(
      role: newRole);
  }
  
  /// تحديث المشرف المباشر
  TeamMember updateSupervisor(String supervisorId, String supervisorName) {
    return copyWith(
      supervisorId: supervisorId,
      supervisorName: supervisorName);
  }
  
  /// إضافة منطقة مسؤول عنها
  TeamMember addAssignedArea(String area) {
    final newAssignedAreas = List<String>.from(assignedAreas ?? []);
    
    if (!newAssignedAreas.contains(area)) {
      newAssignedAreas.add(area);
    }
    
    return copyWith(
      assignedAreas: newAssignedAreas);
  }
  
  /// إزالة منطقة مسؤول عنها
  TeamMember removeAssignedArea(String area) {
    if (assignedAreas == null || !assignedAreas!.contains(area)) {
      return this;
    }
    
    final newAssignedAreas = List<String>.from(assignedAreas!);
    newAssignedAreas.remove(area);
    
    return copyWith(
      assignedAreas: newAssignedAreas);
  }
  
  /// إضافة نوع عقار مسؤول عنه
  TeamMember addAssignedPropertyType(String propertyType) {
    final newAssignedPropertyTypes = List<String>.from(assignedPropertyTypes ?? []);
    
    if (!newAssignedPropertyTypes.contains(propertyType)) {
      newAssignedPropertyTypes.add(propertyType);
    }
    
    return copyWith(
      assignedPropertyTypes: newAssignedPropertyTypes);
  }
  
  /// إضافة عميل مسؤول عنه
  TeamMember addAssignedClient(String clientId) {
    final newAssignedClients = List<String>.from(assignedClients ?? []);
    
    if (!newAssignedClients.contains(clientId)) {
      newAssignedClients.add(clientId);
    }
    
    return copyWith(
      assignedClients: newAssignedClients,
      clientsCount: (assignedClients?.length ?? 0) + 1);
  }
  
  /// إضافة عقار مسؤول عنه
  TeamMember addAssignedEstate(String estateId) {
    final newAssignedEstates = List<String>.from(assignedEstates ?? []);
    
    if (!newAssignedEstates.contains(estateId)) {
      newAssignedEstates.add(estateId);
    }
    
    return copyWith(
      assignedEstates: newAssignedEstates);
  }
  
  /// تحديث هدف المبيعات الشهري
  TeamMember updateMonthlySalesTarget(double target) {
    return copyWith(
      monthlySalesTarget: target);
  }
  
  /// تحديث نسبة العمولة
  TeamMember updateCommissionRate(double rate) {
    return copyWith(
      commissionRate: rate);
  }
  
  /// إضافة مبيعات
  TeamMember addSale(double amount) {
    return copyWith(
      totalSales: totalSales + amount,
      soldRentedCount: soldRentedCount + 1);
  }
  
  /// الحصول على نسبة تحقيق هدف المبيعات الشهري
  double getMonthlySalesTargetPercentage() {
    if (monthlySalesTarget == null || monthlySalesTarget! <= 0) {
      return 0.0;
    }
    
    return (totalSales / monthlySalesTarget!) * 100;
  }
  
  /// الحصول على نسبة تحقيق هدف المبيعات السنوي
  double getAnnualSalesTargetPercentage() {
    if (annualSalesTarget == null || annualSalesTarget! <= 0) {
      return 0.0;
    }
    
    return (totalSales / annualSalesTarget!) * 100;
  }
  
  /// الحصول على إجمالي العمولة
  double getTotalCommission() {
    if (commissionRate == null || commissionRate! <= 0) {
      return 0.0;
    }
    
    return totalSales * (commissionRate! / 100);
  }
  
  /// التحقق مما إذا كان عضو الفريق مدير
  bool isManager() {
    return role == TeamMemberRole.manager;
  }
  
  /// التحقق مما إذا كان عضو الفريق مشرف
  bool isSupervisor() {
    return role == TeamMemberRole.supervisor;
  }
  
  /// التحقق مما إذا كان عضو الفريق نشط
  bool isActive() {
    return status == TeamMemberStatus.active;
  }

  @override
  List<Object?> get props => [
    id,
    companyId,
    userId,
    fullName,
    email,
    phoneNumber,
    image,
    role,
    status,
    joinDate,
    lastLoginDate,
    supervisorId,
    supervisorName,
    assignedAreas,
    assignedPropertyTypes,
    assignedClients,
    assignedEstates,
    monthlySalesTarget,
    annualSalesTarget,
    commissionRate,
    baseSalary,
    totalSales,
    soldRentedCount,
    clientsCount,
    appointmentsCount,
    offersCount,
    averageRating,
    reviewsCount,
    additionalInfo,
  ];
}
