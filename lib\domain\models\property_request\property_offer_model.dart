// lib/domain/models/property_request/property_offer_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

import '../forum/post_model.dart';

/// حالة العرض
enum OfferStatus {
  /// قيد الانتظار
  pending,

  /// مقبول
  accepted,

  /// مرفوض
  rejected
}

/// نموذج عرض عقار
class PropertyOfferModel extends Equatable {
  /// معرف العرض
  final String id;

  /// معرف طلب العقار
  final String requestId;

  /// عنوان طلب العقار
  final String requestTitle;

  /// معرف المستخدم
  final String userId;

  /// اسم المستخدم
  final String userName;

  /// صورة المستخدم
  final String? userImage;

  /// نوع المستخدم
  final String userType;

  /// معرف العقار (إذا كان موجوداً في النظام)
  final String? estateId;

  /// عنوان العقار
  final String title;

  /// وصف العقار
  final String description;

  /// سعر العقار
  final double price;

  /// عنوان العقار
  final String address;

  /// عدد الغرف
  final int rooms;

  /// عدد الحمامات
  final int bathrooms;

  /// المساحة
  final double? area;

  /// روابط الصور
  final List<String>? photoUrls;

  /// هل هو عرض مباشر
  final bool isDirectOffer;

  /// حالة العرض
  final OfferStatus status;

  /// هل يحتوي على تكييف مركزي
  final bool hasCentralAC;

  /// هل يحتوي على غرفة خادمة
  final bool hasMaidRoom;

  /// هل يحتوي على مرآب
  final bool hasGarage;

  /// هل يحتوي على مسبح
  final bool hasSwimmingPool;

  /// هل يحتوي على مصعد
  final bool hasElevator;

  /// هل هو مفروش بالكامل
  final bool isFullyFurnished;

  /// معلومات إضافية
  final String? additionalInfo;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// تاريخ التحديث
  final DateTime updatedAt;

  /// المرفقات
  final List<Map<String, dynamic>>? attachments;

  const PropertyOfferModel({
    required this.id,
    required this.requestId,
    required this.requestTitle,
    required this.userId,
    required this.userName,
    this.userImage,
    required this.userType,
    this.estateId,
    required this.title,
    required this.description,
    required this.price,
    required this.address,
    required this.rooms,
    required this.bathrooms,
    this.area,
    this.photoUrls,
    this.isDirectOffer = true,
    this.status = OfferStatus.pending,
    this.hasCentralAC = false,
    this.hasMaidRoom = false,
    this.hasGarage = false,
    this.hasSwimmingPool = false,
    this.hasElevator = false,
    this.isFullyFurnished = false,
    this.additionalInfo,
    required this.createdAt,
    required this.updatedAt,
    this.attachments,
  });

  /// إنشاء نسخة معدلة من العرض
  PropertyOfferModel copyWith({
    String? id,
    String? requestId,
    String? requestTitle,
    String? userId,
    String? userName,
    String? userImage,
    String? userType,
    String? estateId,
    String? title,
    String? description,
    double? price,
    String? address,
    int? rooms,
    int? bathrooms,
    double? area,
    List<String>? photoUrls,
    bool? isDirectOffer,
    OfferStatus? status,
    bool? hasCentralAC,
    bool? hasMaidRoom,
    bool? hasGarage,
    bool? hasSwimmingPool,
    bool? hasElevator,
    bool? isFullyFurnished,
    String? additionalInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<Map<String, dynamic>>? attachments,
  }) {
    return PropertyOfferModel(
      id: id ?? this.id,
      requestId: requestId ?? this.requestId,
      requestTitle: requestTitle ?? this.requestTitle,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      userType: userType ?? this.userType,
      estateId: estateId ?? this.estateId,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      address: address ?? this.address,
      rooms: rooms ?? this.rooms,
      bathrooms: bathrooms ?? this.bathrooms,
      area: area ?? this.area,
      photoUrls: photoUrls ?? this.photoUrls,
      isDirectOffer: isDirectOffer ?? this.isDirectOffer,
      status: status ?? this.status,
      hasCentralAC: hasCentralAC ?? this.hasCentralAC,
      hasMaidRoom: hasMaidRoom ?? this.hasMaidRoom,
      hasGarage: hasGarage ?? this.hasGarage,
      hasSwimmingPool: hasSwimmingPool ?? this.hasSwimmingPool,
      hasElevator: hasElevator ?? this.hasElevator,
      isFullyFurnished: isFullyFurnished ?? this.isFullyFurnished,
      additionalInfo: additionalInfo ?? this.additionalInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      attachments: attachments ?? this.attachments);
  }

  /// تحويل العرض إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'requestId': requestId,
      'requestTitle': requestTitle,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'userType': userType,
      'estateId': estateId,
      'title': title,
      'description': description,
      'price': price,
      'address': address,
      'rooms': rooms,
      'bathrooms': bathrooms,
      'area': area,
      'photoUrls': photoUrls,
      'isDirectOffer': isDirectOffer,
      'status': status.index,
      'hasCentralAC': hasCentralAC,
      'hasMaidRoom': hasMaidRoom,
      'hasGarage': hasGarage,
      'hasSwimmingPool': hasSwimmingPool,
      'hasElevator': hasElevator,
      'isFullyFurnished': isFullyFurnished,
      'additionalInfo': additionalInfo,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'attachments': attachments,
    };
  }

  /// إنشاء عرض من خريطة
  factory PropertyOfferModel.fromMap(Map<String, dynamic> map) {
    return PropertyOfferModel(
      id: map['id'] ?? '',
      requestId: map['requestId'] ?? '',
      requestTitle: map['requestTitle'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'],
      userType: map['userType'] ?? '',
      estateId: map['estateId'],
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      price: map['price']?.toDouble() ?? 0.0,
      address: map['address'] ?? map['location'] ?? '',
      rooms: map['rooms'] ?? 0,
      bathrooms: map['bathrooms'] ?? 0,
      area: map['area']?.toDouble(),
      photoUrls: map['photoUrls'] != null ? List<String>.from(map['photoUrls']) : null,
      isDirectOffer: map['isDirectOffer'] ?? true,
      status: OfferStatus.values[map['status'] ?? 0],
      hasCentralAC: map['hasCentralAC'] ?? false,
      hasMaidRoom: map['hasMaidRoom'] ?? false,
      hasGarage: map['hasGarage'] ?? false,
      hasSwimmingPool: map['hasSwimmingPool'] ?? false,
      hasElevator: map['hasElevator'] ?? false,
      isFullyFurnished: map['isFullyFurnished'] ?? false,
      additionalInfo: map['additionalInfo'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      attachments: map['attachments'] != null
          ? List<Map<String, dynamic>>.from(
              map['attachments']?.map((x) => Map<String, dynamic>.from(x)))
          : null);
  }

  /// إنشاء عرض من وثيقة Firestore
  factory PropertyOfferModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      throw Exception('Document data was null');
    }

    return PropertyOfferModel.fromMap({
      'id': doc.id,
      ...data,
    });
  }

  /// تحويل العرض إلى مشاركة منتدى
  PostModel toPostModel() {
    return PostModel(
      id: id,
      topicId: requestId,
      topicTitle: requestTitle,
      categoryId: 'property_requests',
      categoryName: 'طلبات العقارات',
      content: description,
      userId: userId,
      userName: userName,
      userImage: userImage,
      createdAt: createdAt,
      updatedAt: updatedAt,
      images: photoUrls,
      attachments: [
        {
          'isPropertyOffer': true,
          'estateId': estateId,
          'title': title,
          'price': price,
          'address': address,
          'rooms': rooms,
          'bathrooms': bathrooms,
          'area': area,
          'isDirectOffer': isDirectOffer,
          'offerStatus': status.index,
          'hasCentralAC': hasCentralAC,
          'hasMaidRoom': hasMaidRoom,
          'hasGarage': hasGarage,
          'hasSwimmingPool': hasSwimmingPool,
          'hasElevator': hasElevator,
          'isFullyFurnished': isFullyFurnished,
          'additionalInfo': additionalInfo,
          'userType': userType,
        }
      ]);
  }

  /// Create property offer from post model
  factory PropertyOfferModel.fromPostModel(PostModel post) {
    // Extract data from post's extraData field if available
    final extraData = post.attachments?.isNotEmpty == true
        ? post.attachments!.first
        : <String, dynamic>{};

    return PropertyOfferModel(
      id: post.id,
      requestId: post.topicId,
      requestTitle: post.topicTitle,
      userId: post.userId,
      userName: post.userName,
      userImage: post.userImage,
      userType: extraData['userType'] ?? '',
      estateId: extraData['estateId'],
      title: extraData['title'] ?? '',
      description: post.content,
      price: extraData['price']?.toDouble() ?? 0.0,
      address: extraData['address'] ?? extraData['location'] ?? '',
      rooms: extraData['rooms'] ?? 0,
      bathrooms: extraData['bathrooms'] ?? 0,
      area: extraData['area']?.toDouble(),
      photoUrls: post.images,
      isDirectOffer: extraData['isDirectOffer'] ?? true,
      status: extraData['offerStatus'] != null
          ? OfferStatus.values[extraData['offerStatus']]
          : OfferStatus.pending,
      hasCentralAC: extraData['hasCentralAC'] ?? false,
      hasMaidRoom: extraData['hasMaidRoom'] ?? false,
      hasGarage: extraData['hasGarage'] ?? false,
      hasSwimmingPool: extraData['hasSwimmingPool'] ?? false,
      hasElevator: extraData['hasElevator'] ?? false,
      isFullyFurnished: extraData['isFullyFurnished'] ?? false,
      additionalInfo: extraData['additionalInfo'],
      createdAt: post.createdAt,
      updatedAt: post.updatedAt,
      attachments: post.attachments);
  }

  @override
  List<Object?> get props => [
        id,
        requestId,
        requestTitle,
        userId,
        userName,
        userImage,
        userType,
        estateId,
        title,
        description,
        price,
        address,
        rooms,
        bathrooms,
        area,
        photoUrls,
        isDirectOffer,
        status,
        hasCentralAC,
        hasMaidRoom,
        hasGarage,
        hasSwimmingPool,
        hasElevator,
        isFullyFurnished,
        additionalInfo,
        createdAt,
        updatedAt,
        attachments,
      ];
}
