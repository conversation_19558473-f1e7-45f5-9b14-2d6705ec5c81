import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'enhanced_state_management_service.dart';

/// خدمة محسنة لمعالجة الأخطاء وإدارة الحالات
class EnhancedErrorHandler {
  static final EnhancedErrorHandler _instance = EnhancedErrorHandler._internal();

  factory EnhancedErrorHandler() => _instance;

  EnhancedErrorHandler._internal();

  final EnhancedStateManagementService _stateService = EnhancedStateManagementService();

  // إعدادات إعادة المحاولة
  static const int _maxRetryAttempts = 3;
  static const Duration _retryDelay = Duration(seconds: 2);

  // تتبع محاولات إعادة المحاولة
  final Map<String, int> _retryAttempts = {};

  /// معالجة الأخطاء العامة
  Future<T?> handleError<T>({
    required String operationId,
    required Future<T> Function() operation,
    String? customMessage,
    bool enableRetry = true,
    int? maxRetries,
  }) async {
    final maxAttempts = maxRetries ?? _maxRetryAttempts;
    final currentAttempts = _retryAttempts[operationId] ?? 0;

    try {
      // تنفيذ العملية
      final result = await operation();

      // إعادة تعيين عداد المحاولات عند النجاح
      _retryAttempts.remove(operationId);

      return result;
    } catch (error) {
      // تحليل نوع الخطأ
      final errorInfo = _analyzeError(error);

      // إنشاء رسالة خطأ مناسبة
      final errorMessage = customMessage ?? _getErrorMessage(errorInfo);

      // إضافة الخطأ إلى خدمة إدارة الحالة
      _stateService.addError(ErrorState(
        id: operationId,
        message: errorMessage,
        type: errorInfo.type,
        isRetryable: errorInfo.isRetryable && enableRetry,
        metadata: {
          'originalError': error.toString(),
          'attempts': currentAttempts + 1,
          'maxAttempts': maxAttempts,
          'timestamp': DateTime.now().toIso8601String(),
        }));

      // محاولة إعادة التنفيذ إذا كان مسموحاً
      if (enableRetry && errorInfo.isRetryable && currentAttempts < maxAttempts) {
        _retryAttempts[operationId] = currentAttempts + 1;

        // انتظار قبل إعادة المحاولة
        await Future.delayed(_retryDelay * (currentAttempts + 1));

        // إعادة المحاولة
        return await handleError<T>(
          operationId: operationId,
          operation: operation,
          customMessage: customMessage,
          enableRetry: enableRetry,
          maxRetries: maxRetries);
      }

      // إذا فشلت جميع المحاولات، إرجاع null
      return null;
    }
  }

  /// تحليل نوع الخطأ
  ErrorInfo _analyzeError(dynamic error) {
    if (error is FirebaseException) {
      return _analyzeFirebaseError(error);
    } else if (error is TimeoutException) {
      return ErrorInfo(
        type: ErrorType.network,
        isRetryable: true,
        severity: ErrorSeverity.medium,
        category: ErrorCategory.network);
    } else if (error is FormatException) {
      return ErrorInfo(
        type: ErrorType.validation,
        isRetryable: false,
        severity: ErrorSeverity.low,
        category: ErrorCategory.data);
    } else {
      return ErrorInfo(
        type: ErrorType.unknown,
        isRetryable: true,
        severity: ErrorSeverity.high,
        category: ErrorCategory.system);
    }
  }

  /// تحليل أخطاء Firebase
  ErrorInfo _analyzeFirebaseError(FirebaseException error) {
    switch (error.code) {
      case 'permission-denied':
        return ErrorInfo(
          type: ErrorType.authorization,
          isRetryable: false,
          severity: ErrorSeverity.high,
          category: ErrorCategory.security);

      case 'unauthenticated':
        return ErrorInfo(
          type: ErrorType.authentication,
          isRetryable: false,
          severity: ErrorSeverity.high,
          category: ErrorCategory.security);

      case 'unavailable':
      case 'deadline-exceeded':
        return ErrorInfo(
          type: ErrorType.network,
          isRetryable: true,
          severity: ErrorSeverity.medium,
          category: ErrorCategory.network);

      case 'resource-exhausted':
        return ErrorInfo(
          type: ErrorType.server,
          isRetryable: true,
          severity: ErrorSeverity.high,
          category: ErrorCategory.server);

      case 'invalid-argument':
      case 'failed-precondition':
        return ErrorInfo(
          type: ErrorType.validation,
          isRetryable: false,
          severity: ErrorSeverity.medium,
          category: ErrorCategory.data);

      default:
        return ErrorInfo(
          type: ErrorType.server,
          isRetryable: true,
          severity: ErrorSeverity.medium,
          category: ErrorCategory.server);
    }
  }

  /// الحصول على رسالة خطأ مناسبة
  String _getErrorMessage(ErrorInfo errorInfo) {
    switch (errorInfo.type) {
      case ErrorType.network:
        return 'مشكلة في الاتصال بالإنترنت. يرجى التحقق من اتصالك وإعادة المحاولة.';

      case ErrorType.connectivity:
        return 'لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصالك.';

      case ErrorType.authentication:
        return 'انتهت صلاحية جلسة المستخدم. يرجى تسجيل الدخول مرة أخرى.';

      case ErrorType.authorization:
        return 'ليس لديك صلاحية للوصول إلى هذه البيانات.';

      case ErrorType.validation:
        return 'البيانات المدخلة غير صحيحة. يرجى التحقق من البيانات وإعادة المحاولة.';

      case ErrorType.server:
        return 'مشكلة في الخادم. يرجى إعادة المحاولة لاحقاً.';

      case ErrorType.unknown:
        return 'حدث خطأ غير متوقع. يرجى إعادة المحاولة.';

      case ErrorType.cleared:
        return '';
    }
  }

  /// معالجة أخطاء تحميل البيانات
  Future<T?> handleDataLoadingError<T>({
    required String operationId,
    required Future<T> Function() loadOperation,
    T? fallbackData,
    bool showUserMessage = true,
  }) async {
    return await handleError<T>(
      operationId: operationId,
      operation: loadOperation,
      customMessage: showUserMessage ? 'فشل في تحميل البيانات' : null,
      enableRetry: true) ?? fallbackData;
  }

  /// معالجة أخطاء حفظ البيانات
  Future<bool> handleDataSavingError({
    required String operationId,
    required Future<void> Function() saveOperation,
    bool showUserMessage = true,
  }) async {
    try {
      await handleError<void>(
        operationId: operationId,
        operation: saveOperation,
        customMessage: showUserMessage ? 'فشل في حفظ البيانات' : null,
        enableRetry: true);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// معالجة أخطاء الشبكة
  Future<T?> handleNetworkError<T>({
    required String operationId,
    required Future<T> Function() networkOperation,
    T? offlineData,
  }) async {
    return await handleError<T>(
      operationId: operationId,
      operation: networkOperation,
      customMessage: 'مشكلة في الاتصال بالشبكة',
      enableRetry: true) ?? offlineData;
  }

  /// إعادة تعيين محاولات إعادة المحاولة
  void resetRetryAttempts(String operationId) {
    _retryAttempts.remove(operationId);
  }

  /// مسح جميع محاولات إعادة المحاولة
  void clearAllRetryAttempts() {
    _retryAttempts.clear();
  }

  /// الحصول على إحصائيات الأخطاء
  Map<String, dynamic> getErrorStats() {
    final activeErrors = _stateService.getAllActiveErrors();
    final retryOperations = _retryAttempts.length;

    final errorsByType = <ErrorType, int>{};
    for (final error in activeErrors) {
      errorsByType[error.type] = (errorsByType[error.type] ?? 0) + 1;
    }

    return {
      'totalActiveErrors': activeErrors.length,
      'retryOperations': retryOperations,
      'errorsByType': errorsByType.map((key, value) => MapEntry(key.toString(), value)),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// معلومات الخطأ
class ErrorInfo {
  final ErrorType type;
  final bool isRetryable;
  final ErrorSeverity severity;
  final ErrorCategory category;

  ErrorInfo({
    required this.type,
    required this.isRetryable,
    required this.severity,
    required this.category,
  });
}

/// مستوى خطورة الخطأ
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// فئة الخطأ
enum ErrorCategory {
  network,
  security,
  data,
  server,
  system,
  user,
}

/// امتدادات مساعدة
extension ErrorSeverityExtension on ErrorSeverity {
  String get displayName {
    switch (this) {
      case ErrorSeverity.low:
        return 'منخفض';
      case ErrorSeverity.medium:
        return 'متوسط';
      case ErrorSeverity.high:
        return 'عالي';
      case ErrorSeverity.critical:
        return 'حرج';
    }
  }

  String get icon {
    switch (this) {
      case ErrorSeverity.low:
        return 'ℹ️';
      case ErrorSeverity.medium:
        return '⚠️';
      case ErrorSeverity.high:
        return '❌';
      case ErrorSeverity.critical:
        return '🚨';
    }
  }
}

extension ErrorCategoryExtension on ErrorCategory {
  String get displayName {
    switch (this) {
      case ErrorCategory.network:
        return 'شبكة';
      case ErrorCategory.security:
        return 'أمان';
      case ErrorCategory.data:
        return 'بيانات';
      case ErrorCategory.server:
        return 'خادم';
      case ErrorCategory.system:
        return 'نظام';
      case ErrorCategory.user:
        return 'مستخدم';
    }
  }
}
