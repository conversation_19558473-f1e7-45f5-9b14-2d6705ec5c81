import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';
import 'package:kuwait_corners/presentation/bloc/estate_bloc.dart';
import 'package:kuwait_corners/presentation/bloc/estate_event.dart';
import 'package:kuwait_corners/presentation/bloc/estate_state.dart';
import 'package:kuwait_corners/presentation/widgets/estate_card.dart';

/// صفحة تعرض العقارات حسب نوع المستخدم (مالك، مستثمر، شركة)
class UserPropertiesPage extends StatefulWidget {
  final String userType; // 'owner', 'investor', 'company'

  const UserPropertiesPage({super.key, required this.userType});

  @override
  State<UserPropertiesPage> createState() => _UserPropertiesPageState();
}

class _UserPropertiesPageState extends State<UserPropertiesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final currentUserId = FirebaseAuth.instance.currentUser?.uid;
  String _viewMode = 'grid'; // 'grid' or 'list'
  String _sortBy = 'newest'; // 'newest', 'price_high', 'price_low'
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    // إنشاء تاب كنترولر مع عدد التابات حسب نوع المستخدم
    _tabController = TabController(
        length: widget.userType == 'investor' ? 2 : 1, vsync: this);

    // تحميل العقارات
    _refreshEstates();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تحديث العقارات عند العودة للصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _refreshEstates();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // تحديث ترتيب العقارات
  List<Estate> _sortEstates(List<Estate> estates) {
    switch (_sortBy) {
      case 'newest':
        return estates..sort((a, b) => b.createdAt.compareTo(a.createdAt));
      case 'price_high':
        return estates..sort((a, b) => b.price.compareTo(a.price));
      case 'price_low':
        return estates..sort((a, b) => a.price.compareTo(b.price));
      default:
        return estates;
    }
  }

  // تحديث العقارات
  Future<void> _refreshEstates() async {
    setState(() {
      _isRefreshing = true;
    });

    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) {
      context.read<EstateBloc>().add(FetchEstates());
    }

    setState(() {
      _isRefreshing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _getPageTitle(),
          style: CairoTextStyles.appBarTitle),
        actions: [
          // زر التوجه للرئيسية
          IconButton(
            icon: Icon(
              Icons.home,
              color: Colors.white),
            onPressed: () {
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/home',
                (route) => false);
            },
            tooltip: 'الرئيسية'),
          // زر تبديل طريقة العرض
          IconButton(
            icon: Icon(
              _viewMode == 'grid' ? Icons.view_list : Icons.grid_view,
              color: Colors.white),
            onPressed: () {
              setState(() {
                _viewMode = _viewMode == 'grid' ? 'list' : 'grid';
              });
            },
            tooltip: _viewMode == 'grid' ? 'عرض قائمة' : 'عرض شبكة'),
          // زر الترتيب
          PopupMenuButton<String>(
            icon: Icon(Icons.sort, color: Colors.white),
            onSelected: (value) {
              setState(() {
                _sortBy = value;
              });
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'newest',
                child: Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      color:
                          _sortBy == 'newest' ? AppColors.primary : Colors.grey,
                      size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'الأحدث',
                      style: TextStyle(
                        color: _sortBy == 'newest' ? AppColors.primary : null,
                        fontWeight:
                            _sortBy == 'newest' ? FontWeight.bold : null)),
                  ])),
              PopupMenuItem(
                value: 'price_high',
                child: Row(
                  children: [
                    Icon(
                      Icons.arrow_downward,
                      color: _sortBy == 'price_high'
                          ? AppColors.primary
                          : Colors.grey,
                      size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'السعر: من الأعلى',
                      style: TextStyle(
                        color:
                            _sortBy == 'price_high' ? AppColors.primary : null,
                        fontWeight:
                            _sortBy == 'price_high' ? FontWeight.bold : null)),
                  ])),
              PopupMenuItem(
                value: 'price_low',
                child: Row(
                  children: [
                    Icon(
                      Icons.arrow_upward,
                      color: _sortBy == 'price_low'
                          ? AppColors.primary
                          : Colors.grey,
                      size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'السعر: من الأقل',
                      style: TextStyle(
                        color:
                            _sortBy == 'price_low' ? AppColors.primary : null,
                        fontWeight:
                            _sortBy == 'price_low' ? FontWeight.bold : null)),
                  ])),
            ]),
        ],
        bottom: widget.userType == 'investor'
            ? TabBar(
                controller: _tabController,
                tabs: [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.home_work, size: 16),
                        const SizedBox(width: 8),
                        const Text('عقاراتي الأصلية'),
                      ])),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.content_copy, size: 16),
                        const SizedBox(width: 8),
                        const Text('عقارات منسوخة'),
                      ])),
                ],
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white70,
                indicatorColor: Colors.white,
                indicatorWeight: 3)
            : null,
        elevation: 0,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white),
      body: RefreshIndicator(
        onRefresh: _refreshEstates,
        color: AppColors.primary,
        child: BlocBuilder<EstateBloc, EstateState>(
          builder: (context, state) {
            if (state is EstateLoading && !_isRefreshing) {
              return const Center(
                child: CircularProgressIndicator());
            } else if (state is EstateError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 64, color: AppColors.error),
                    const SizedBox(height: 16),
                    Text(
                      'حدث خطأ أثناء تحميل العقارات',
                      style: CairoTextStyles.headlineSmall.copyWith(
                          color: Colors.grey.shade700)),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      style: CairoTextStyles.bodyMedium.copyWith(
                          color: Colors.grey),
                      textAlign: TextAlign.center),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _refreshEstates,
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة المحاولة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12)))),
                  ]));
            } else if (state is EstateLoaded) {
              if (widget.userType == 'investor') {
                return TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOriginalEstatesList(state.estates),
                    _buildCopiedEstatesList(state.estates),
                  ]);
              } else {
                return _buildOriginalEstatesList(state.estates);
              }
            }
            return const Center(
              child: Text('لا توجد عقارات'));
          })),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // انتقل إلى صفحة إنشاء الإعلان المحسنة
          Navigator.pushNamed(context, '/improved-ad-creation');
        },
        icon: const Icon(Icons.add),
        label: const Text('إضافة عقار'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 4));
  }

  String _getPageTitle() {
    switch (widget.userType) {
      case 'owner':
        return 'عقاراتي';
      case 'investor':
        return 'عقارات المستثمر';
      case 'company':
        return 'عقارات الشركة';
      default:
        return 'العقارات';
    }
  }

  /// دالة مساعدة لتحرير العقار
  void _editEstate(Estate estate) {
    // عرض مربع حوار تأكيد التحديث
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث العقار'),
        content: const Text(
          'هل تريد تحديث بيانات هذا العقار؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء')),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white),
            onPressed: () {
              Navigator.pop(context);

              // انتقل إلى صفحة إنشاء الإعلان المحسنة مع تمرير بيانات العقار
              Navigator.pushNamed(
                context,
                '/improved-ad-creation',
                arguments: {
                  'isEditing': true,
                  'estate': estate,
                }).then((_) {
                if (mounted) {
                  // تحديث القائمة بعد العودة
                  context.read<EstateBloc>().add(FetchEstates());

                  // عرض رسالة نجاح
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تحديث العقار بنجاح'),
                      backgroundColor: Colors.green));
                }
              });
            },
            child: const Text('تحديث')),
        ]));
  }

  Widget _buildOriginalEstatesList(List<Estate> allEstates) {
    // تصفية العقارات حسب المستخدم الحالي والعقارات الأصلية
    final userEstates = allEstates
        .where((estate) => estate.ownerId == currentUserId && estate.isOriginal)
        .toList();

    // ترتيب العقارات
    final sortedEstates = _sortEstates(userEstates);

    if (sortedEstates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                shape: BoxShape.circle),
              child: Icon(
                Icons.home_work,
                size: 64,
                color: AppColors.primary.withOpacity(0.7))),
            const SizedBox(height: 24),
            Text(
              'لا توجد عقارات أصلية',
              style: CairoTextStyles.headlineSmall.copyWith(
                color: Colors.grey.shade800)),
            const SizedBox(height: 8),
            Text(
              'أضف عقارك الأول وابدأ في عرضه للمهتمين',
              style: CairoTextStyles.bodyMedium.copyWith(
                color: Colors.grey.shade600),
              textAlign: TextAlign.center),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                // انتقل إلى صفحة إنشاء الإعلان المحسنة
                Navigator.pushNamed(context, '/improved-ad-creation');
              },
              icon: const Icon(Icons.add),
              label: const Text('إضافة عقار جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)))),
          ]));
    }

    // عرض العقارات حسب نمط العرض المحدد
    if (_viewMode == 'grid') {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 12.0),
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.75,
            crossAxisSpacing: 8,
            mainAxisSpacing: 12),
          itemCount: sortedEstates.length,
          itemBuilder: (context, index) {
            final estate = sortedEstates[index];
            return EstateCard(
              estate: estate,
              isManageSection: true,
              onEdit: () {
                // انتقل إلى صفحة تعديل العقار
                // Navigator.push(context, MaterialPageRoute(builder: (_) => EditEstatePage(estate: estate)));
              });
          }));
    } else {
      return ListView.builder(
        padding: const EdgeInsets.all(12.0),
        itemCount: sortedEstates.length,
        itemBuilder: (context, index) {
          final estate = sortedEstates[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: EstateCard(
              estate: estate,
              isManageSection: true,
              onEdit: () {
                // انتقل إلى صفحة تعديل العقار
                // Navigator.push(context, MaterialPageRoute(builder: (_) => EditEstatePage(estate: estate)));
              }));
        });
    }
  }

  Widget _buildCopiedEstatesList(List<Estate> allEstates) {
    // تصفية العقارات المنسوخة بواسطة المستخدم الحالي
    final copiedEstates = allEstates
        .where(
            (estate) => estate.ownerId == currentUserId && !estate.isOriginal)
        .toList();

    // ترتيب العقارات
    final sortedEstates = _sortEstates(copiedEstates);

    if (sortedEstates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.secondary.withOpacity(0.1),
                shape: BoxShape.circle),
              child: Icon(
                Icons.content_copy,
                size: 64,
                color: AppColors.secondary.withOpacity(0.7))),
            const SizedBox(height: 24),
            Text(
              'لم تقم بنسخ أي عقارات بعد',
              style: CairoTextStyles.headlineSmall.copyWith(
                color: Colors.grey.shade800)),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                'يمكنك نسخ عقارات من المالكين الآخرين لعرضها باسمك مع الإشارة للمالك الأصلي',
                style: CairoTextStyles.bodyMedium.copyWith(
                  color: Colors.grey.shade600),
                textAlign: TextAlign.center)),
            const SizedBox(height: 24),
            OutlinedButton.icon(
              onPressed: () {
                // انتقل إلى صفحة استكشاف العقارات
                // Navigator.push(context, MaterialPageRoute(builder: (_) => ExplorePage()));
              },
              icon: const Icon(Icons.search),
              label: const Text('استكشاف العقارات'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.secondary,
                side: BorderSide(color: AppColors.secondary),
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)))),
          ]));
    }

    // عرض العقارات حسب نمط العرض المحدد
    if (_viewMode == 'grid') {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 12.0),
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.75,
            crossAxisSpacing: 8,
            mainAxisSpacing: 12),
          itemCount: sortedEstates.length,
          itemBuilder: (context, index) {
            final estate = sortedEstates[index];
            return EstateCard(
              estate: estate,
              isManageSection: true,
              onEdit: () {
                // انتقل إلى صفحة تعديل العقار
                // Navigator.push(context, MaterialPageRoute(builder: (_) => EditEstatePage(estate: estate)));
              });
          }));
    } else {
      return ListView.builder(
        padding: const EdgeInsets.all(12.0),
        itemCount: sortedEstates.length,
        itemBuilder: (context, index) {
          final estate = sortedEstates[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: EstateCard(
              estate: estate,
              isManageSection: true,
              onEdit: () {
                // انتقل إلى صفحة تعديل العقار
                // Navigator.push(context, MaterialPageRoute(builder: (_) => EditEstatePage(estate: estate)));
              }));
        });
    }
  }
}
