import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// خدمة الدفع عبر ومض الكويتية
class WamdPaymentService {
  static const String _wamdPhoneNumber = '+965 9929 8821';
  static const String _wamdDisplayNumber = '99298821';
  
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  WamdPaymentService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  /// رقم هاتف ومض للتحويلات
  static String get wamdPhoneNumber => _wamdPhoneNumber;
  static String get wamdDisplayNumber => _wamdDisplayNumber;

  /// إنشاء طلب دفع جديد
  Future<Map<String, dynamic>> createPaymentRequest({
    required String estateId,
    required double amount,
    required String currency,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final paymentId = _firestore.collection('wamd_payments').doc().id;
      final now = DateTime.now();
      
      final paymentData = {
        'id': paymentId,
        'userId': user.uid,
        'estateId': estateId,
        'amount': amount,
        'currency': currency,
        'description': description,
        'status': 'pending',
        'paymentMethod': 'wamd',
        'wamdPhoneNumber': _wamdPhoneNumber,
        'createdAt': Timestamp.fromDate(now),
        'updatedAt': Timestamp.fromDate(now),
        'expiresAt': Timestamp.fromDate(now.add(const Duration(hours: 24))),
        'metadata': metadata ?? {},
        'verificationRequired': true,
        'manualReview': true,
      };

      await _firestore.collection('wamd_payments').doc(paymentId).set(paymentData);

      return {
        'paymentId': paymentId,
        'amount': amount,
        'currency': currency,
        'wamdPhoneNumber': _wamdPhoneNumber,
        'wamdDisplayNumber': _wamdDisplayNumber,
        'status': 'pending',
        'expiresAt': now.add(const Duration(hours: 24)),
        'instructions': _getPaymentInstructions(amount, currency),
      };
    } catch (e) {
      throw Exception('فشل في إنشاء طلب الدفع: $e');
    }
  }

  /// تأكيد الدفع (للمراجعة اليدوية)
  Future<bool> confirmPayment({
    required String paymentId,
    String? transactionReference,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final paymentDoc = await _firestore
          .collection('wamd_payments')
          .doc(paymentId)
          .get();

      if (!paymentDoc.exists) {
        throw Exception('طلب الدفع غير موجود');
      }

      final paymentData = paymentDoc.data()!;
      
      if (paymentData['userId'] != user.uid) {
        throw Exception('غير مصرح لك بتأكيد هذا الدفع');
      }

      if (paymentData['status'] != 'pending') {
        throw Exception('طلب الدفع غير صالح للتأكيد');
      }

      // تحديث حالة الدفع إلى "في انتظار المراجعة"
      await _firestore.collection('wamd_payments').doc(paymentId).update({
        'status': 'pending_review',
        'confirmedAt': FieldValue.serverTimestamp(),
        'transactionReference': transactionReference,
        'additionalData': additionalData ?? {},
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // إنشاء إشعار للإدارة
      await _createAdminNotification(paymentId, paymentData);

      return true;
    } catch (e) {
      throw Exception('فشل في تأكيد الدفع: $e');
    }
  }

  /// التحقق من حالة الدفع
  Future<Map<String, dynamic>?> getPaymentStatus(String paymentId) async {
    try {
      final paymentDoc = await _firestore
          .collection('wamd_payments')
          .doc(paymentId)
          .get();

      if (!paymentDoc.exists) {
        return null;
      }

      final data = paymentDoc.data()!;
      return {
        'id': data['id'],
        'status': data['status'],
        'amount': data['amount'],
        'currency': data['currency'],
        'createdAt': data['createdAt'],
        'confirmedAt': data['confirmedAt'],
        'verifiedAt': data['verifiedAt'],
        'expiresAt': data['expiresAt'],
        'isExpired': _isPaymentExpired(data['expiresAt']),
      };
    } catch (e) {
      throw Exception('فشل في جلب حالة الدفع: $e');
    }
  }

  /// الحصول على تعليمات الدفع
  List<String> _getPaymentInstructions(double amount, String currency) {
    return [
      'افتح تطبيق ومض أو تطبيق البنك الخاص بك',
      'اختر خدمة التحويل السريع (ومض)',
      'أدخل رقم الهاتف: $_wamdPhoneNumber',
      'أدخل المبلغ: ${amount.toStringAsFixed(1)} $currency',
      'أكمل عملية التحويل',
      'اضغط على "تأكيد الدفع" أدناه بعد إتمام التحويل',
      'سيتم تفعيل طلبك خلال 24-48 ساعة من التحقق من الدفع',
    ];
  }

  /// إنشاء إشعار للإدارة
  Future<void> _createAdminNotification(String paymentId, Map<String, dynamic> paymentData) async {
    try {
      final notificationId = _firestore.collection('admin_notifications').doc().id;
      
      await _firestore.collection('admin_notifications').doc(notificationId).set({
        'id': notificationId,
        'type': 'payment_confirmation',
        'title': 'طلب تأكيد دفع ومض جديد',
        'message': 'طلب تأكيد دفع بمبلغ ${paymentData['amount']} ${paymentData['currency']}',
        'paymentId': paymentId,
        'userId': paymentData['userId'],
        'estateId': paymentData['estateId'],
        'amount': paymentData['amount'],
        'currency': paymentData['currency'],
        'status': 'unread',
        'priority': 'high',
        'createdAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // تسجيل الخطأ ولكن عدم إيقاف العملية
      print('فشل في إنشاء إشعار الإدارة: $e');
    }
  }

  /// التحقق من انتهاء صلاحية الدفع
  bool _isPaymentExpired(Timestamp? expiresAt) {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt.toDate());
  }

  /// إلغاء طلب الدفع
  Future<bool> cancelPayment(String paymentId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final paymentDoc = await _firestore
          .collection('wamd_payments')
          .doc(paymentId)
          .get();

      if (!paymentDoc.exists) {
        throw Exception('طلب الدفع غير موجود');
      }

      final paymentData = paymentDoc.data()!;
      
      if (paymentData['userId'] != user.uid) {
        throw Exception('غير مصرح لك بإلغاء هذا الدفع');
      }

      if (paymentData['status'] == 'verified' || paymentData['status'] == 'completed') {
        throw Exception('لا يمكن إلغاء دفع مكتمل');
      }

      await _firestore.collection('wamd_payments').doc(paymentId).update({
        'status': 'cancelled',
        'cancelledAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      throw Exception('فشل في إلغاء الدفع: $e');
    }
  }

  /// الحصول على مدفوعات المستخدم
  Future<List<Map<String, dynamic>>> getUserPayments({
    String? status,
    int limit = 20,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      Query query = _firestore
          .collection('wamd_payments')
          .where('userId', isEqualTo: user.uid)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }

      final snapshot = await query.get();
      
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['isExpired'] = _isPaymentExpired(data['expiresAt']);
        return data;
      }).toList();
    } catch (e) {
      throw Exception('فشل في جلب المدفوعات: $e');
    }
  }

  /// تنظيف المدفوعات المنتهية الصلاحية
  Future<void> cleanupExpiredPayments() async {
    try {
      final now = Timestamp.fromDate(DateTime.now());
      
      final expiredPayments = await _firestore
          .collection('wamd_payments')
          .where('expiresAt', isLessThan: now)
          .where('status', isEqualTo: 'pending')
          .get();

      final batch = _firestore.batch();
      
      for (final doc in expiredPayments.docs) {
        batch.update(doc.reference, {
          'status': 'expired',
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      await batch.commit();
    } catch (e) {
      print('فشل في تنظيف المدفوعات المنتهية الصلاحية: $e');
    }
  }
}
