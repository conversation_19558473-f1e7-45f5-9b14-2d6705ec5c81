import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../domain/entities/user.dart' as domain_user;

/// حالة التحقق من المستخدم
enum VerificationStatus {
  /// غير محقق
  unverified,

  /// قيد المراجعة
  pending,

  /// تم التحقق
  verified,

  /// مرفوض
  rejected
}

// تم نقل تعريف UserType إلى lib/domain/entities/user.dart لتجنب التضارب

/// نموذج بيانات طلب التحقق
class VerificationRequest {
  /// معرف الطلب
  final String id;

  /// معرف المستخدم
  final String userId;

  /// نوع المستخدم
  final domain_user.UserType userType;

  /// حالة التحقق
  final VerificationStatus status;

  /// تاريخ تقديم الطلب
  final DateTime submissionDate;

  /// تاريخ المراجعة (إذا تمت)
  final DateTime? reviewDate;

  /// ملاحظات المراجعة
  final String? reviewNotes;

  /// روابط المستندات المرفقة
  final List<String> documentUrls;

  /// معلومات إضافية
  final Map<String, dynamic> additionalInfo;

  VerificationRequest({
    required this.id,
    required this.userId,
    required this.userType,
    required this.status,
    required this.submissionDate,
    this.reviewDate,
    this.reviewNotes,
    required this.documentUrls,
    required this.additionalInfo,
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory VerificationRequest.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return VerificationRequest(
      id: doc.id,
      userId: data['userId'] ?? '',
      userType: domain_user.UserType.values.firstWhere(
        (e) => e.toString() == data['userType'],
        orElse: () => domain_user.UserType.seeker),
      status: VerificationStatus.values.firstWhere(
        (e) => e.toString() == data['status'],
        orElse: () => VerificationStatus.unverified),
      submissionDate: (data['submissionDate'] as Timestamp).toDate(),
      reviewDate: data['reviewDate'] != null
          ? (data['reviewDate'] as Timestamp).toDate()
          : null,
      reviewNotes: data['reviewNotes'],
      documentUrls: List<String>.from(data['documentUrls'] ?? []),
      additionalInfo: Map<String, dynamic>.from(data['additionalInfo'] ?? {}));
  }

  /// تحويل النموذج إلى Map لتخزينه في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userType': userType.toString(),
      'status': status.toString(),
      'submissionDate': Timestamp.fromDate(submissionDate),
      'reviewDate': reviewDate != null ? Timestamp.fromDate(reviewDate!) : null,
      'reviewNotes': reviewNotes,
      'documentUrls': documentUrls,
      'additionalInfo': additionalInfo,
    };
  }
}

/// خدمة التحقق من المستخدمين
class UserVerificationService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// الحصول على حالة التحقق للمستخدم الحالي
  Future<VerificationStatus> getCurrentUserVerificationStatus() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return VerificationStatus.unverified;
      }

      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) {
        return VerificationStatus.unverified;
      }

      final userData = userDoc.data()!;
      final statusStr = userData['verificationStatus'] as String?;

      if (statusStr == null) {
        return VerificationStatus.unverified;
      }

      return VerificationStatus.values.firstWhere(
        (e) => e.toString() == statusStr,
        orElse: () => VerificationStatus.unverified);
    } catch (e) {
      return VerificationStatus.unverified;
    }
  }

  /// الحصول على نوع المستخدم الحالي
  Future<domain_user.UserType> getCurrentUserType() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return domain_user.UserType.seeker;
      }

      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) {
        return domain_user.UserType.seeker;
      }

      final userData = userDoc.data()!;
      final typeStr = userData['userType'] as String?;

      if (typeStr == null) {
        return domain_user.UserType.seeker;
      }

      return domain_user.UserType.values.firstWhere(
        (e) => e.toString() == typeStr,
        orElse: () => domain_user.UserType.seeker);
    } catch (e) {
      return domain_user.UserType.seeker;
    }
  }

  /// تقديم طلب تحقق جديد
  Future<String?> submitVerificationRequest({
    required domain_user.UserType userType,
    required List<File> documents,
    required Map<String, dynamic> additionalInfo,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لتقديم طلب التحقق');
      }

      // التحقق من عدم وجود طلب سابق قيد المراجعة
      final existingRequests = await _firestore
          .collection('verificationRequests')
          .where('userId', isEqualTo: user.uid)
          .where('status', isEqualTo: VerificationStatus.pending.toString())
          .get();

      if (existingRequests.docs.isNotEmpty) {
        throw Exception('لديك طلب تحقق قيد المراجعة بالفعل');
      }

      // رفع المستندات
      final documentUrls = await _uploadDocuments(documents, user.uid);

      // إنشاء طلب التحقق
      final request = VerificationRequest(
        id: '', // سيتم تعيينه من Firestore
        userId: user.uid,
        userType: userType,
        status: VerificationStatus.pending,
        submissionDate: DateTime.now(),
        documentUrls: documentUrls,
        additionalInfo: additionalInfo);

      // حفظ الطلب في Firestore
      final docRef = await _firestore
          .collection('verificationRequests')
          .add(request.toFirestore());

      // تحديث حالة التحقق للمستخدم
      await _firestore.collection('users').doc(user.uid).update({
        'verificationStatus': VerificationStatus.pending.toString(),
        'userType': userType.toString(),
      });

      return docRef.id;
    } catch (e) {
      return null;
    }
  }

  /// رفع المستندات إلى Firebase Storage
  Future<List<String>> _uploadDocuments(List<File> documents, String userId) async {
    final urls = <String>[];

    for (int i = 0; i < documents.length; i++) {
      final file = documents[i];
      final fileName = 'verification_${userId}_${DateTime.now().millisecondsSinceEpoch}_$i.jpg';
      final ref = _storage.ref().child('verification_documents/$userId/$fileName');

      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;

      final url = await snapshot.ref.getDownloadURL();
      urls.add(url);
    }

    return urls;
  }

  /// الحصول على طلب التحقق الحالي للمستخدم
  Future<VerificationRequest?> getCurrentUserVerificationRequest() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return null;
      }

      final requests = await _firestore
          .collection('verificationRequests')
          .where('userId', isEqualTo: user.uid)
          .orderBy('submissionDate', descending: true)
          .limit(1)
          .get();

      if (requests.docs.isEmpty) {
        return null;
      }

      return VerificationRequest.fromFirestore(requests.docs.first);
    } catch (e) {
      return null;
    }
  }

  /// إلغاء طلب التحقق الحالي
  Future<bool> cancelVerificationRequest() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      final requests = await _firestore
          .collection('verificationRequests')
          .where('userId', isEqualTo: user.uid)
          .where('status', isEqualTo: VerificationStatus.pending.toString())
          .get();

      if (requests.docs.isEmpty) {
        return false;
      }

      // حذف الطلب
      await _firestore
          .collection('verificationRequests')
          .doc(requests.docs.first.id)
          .delete();

      // تحديث حالة التحقق للمستخدم
      await _firestore.collection('users').doc(user.uid).update({
        'verificationStatus': VerificationStatus.unverified.toString(),
      });

      return true;
    } catch (e) {
      return false;
    }
  }
}
