import 'package:flutter/material.dart';

import 'reactions_button.dart';

/// ملخص التفاعلات
class ReactionsSummary extends StatelessWidget {
  /// التفاعلات
  final Map<String, List<String>>? reactions;

  /// حجم الأيقونات
  final double iconSize;

  /// ما إذا كان الملخص مصغراً
  final bool isCompact;

  /// دالة يتم استدعاؤها عند النقر على الملخص
  final VoidCallback? onTap;

  const ReactionsSummary({
    super.key,
    required this.reactions,
    this.iconSize = 16,
    this.isCompact = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (reactions == null || reactions!.isEmpty) {
      return const SizedBox.shrink();
    }

    final totalCount = _getTotalReactionsCount();
    if (totalCount == 0) {
      return const SizedBox.shrink();
    }

    final topReactions = _getTopReactions(3);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey.shade300)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونات التفاعلات
            _buildReactionIcons(topReactions),
            
            // عدد التفاعلات
            if (!isCompact) ...[
              const SizedBox(width: 4),
              Text(
                totalCount.toString(),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.bold)),
            ],
          ])));
  }

  /// بناء أيقونات التفاعلات
  Widget _buildReactionIcons(List<MapEntry<String, List<String>>> topReactions) {
    return Stack(
      children: topReactions.asMap().entries.map((entry) {
        final index = entry.key;
        final reaction = entry.value;
        final reactionType = _getReactionTypeFromString(reaction.key);
        if (reactionType == null) {
          return const SizedBox.shrink();
        }

        final reactionInfo = _getReactionInfo(reactionType);
        
        return Positioned(
          left: index * (iconSize * 0.6),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2)),
            child: Text(
              reactionInfo.emoji,
              style: TextStyle(fontSize: iconSize))));
      }).toList());
  }

  /// الحصول على إجمالي عدد التفاعلات
  int _getTotalReactionsCount() {
    if (reactions == null) {
      return 0;
    }

    int count = 0;
    for (final entry in reactions!.entries) {
      count += entry.value.length;
    }
    return count;
  }

  /// الحصول على أكثر التفاعلات
  List<MapEntry<String, List<String>>> _getTopReactions(int count) {
    if (reactions == null) {
      return [];
    }

    final sortedReactions = reactions!.entries.toList()
      ..sort((a, b) => b.value.length.compareTo(a.value.length));

    return sortedReactions.take(count).toList();
  }

  /// الحصول على نوع التفاعل من السلسلة النصية
  ReactionType? _getReactionTypeFromString(String reactionString) {
    for (final reaction in ReactionType.values) {
      if (reaction.name == reactionString) {
        return reaction;
      }
    }
    return null;
  }

  /// الحصول على معلومات التفاعل من النوع
  ReactionInfo _getReactionInfo(ReactionType type) {
    return availableReactions.firstWhere(
      (reaction) => reaction.type == type,
      orElse: () => availableReactions.first);
  }
}
