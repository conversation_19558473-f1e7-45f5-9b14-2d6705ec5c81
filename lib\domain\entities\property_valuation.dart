import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج لتقييم العقار
class PropertyValuation extends Equatable {
  /// معرف التقييم
  final String id;
  
  /// معرف العقار (اختياري)
  final String? estateId;
  
  /// معرف المستخدم الذي طلب التقييم
  final String userId;
  
  /// نوع العقار
  final String propertyType;
  
  /// المنطقة
  final String location;
  
  /// المحافظة
  final String? governorate;
  
  /// المدينة
  final String? city;
  
  /// المنطقة
  final String? district;
  
  /// المساحة بالمتر المربع
  final double area;
  
  /// عدد الغرف
  final int? numberOfRooms;
  
  /// عدد الحمامات
  final int? numberOfBathrooms;
  
  /// عمر البناء
  final int? buildingAge;
  
  /// نوع التشطيب
  final String? finishingType;
  
  /// الطابق
  final int? floorNumber;
  
  /// الاتجاه
  final String? facingDirection;
  
  /// الإطلالة
  final String? viewType;
  
  /// ميزات إضافية
  final List<String>? features;
  
  /// تاريخ التقييم
  final DateTime valuationDate;
  
  /// القيمة المقدرة للبيع
  final double estimatedSaleValue;
  
  /// القيمة المقدرة للإيجار
  final double estimatedRentalValue;
  
  /// القيمة المقدرة للمتر المربع
  final double estimatedPricePerSquareMeter;
  
  /// نسبة الدقة
  final double accuracyPercentage;
  
  /// نطاق القيمة (الحد الأدنى)
  final double? valueRangeMin;
  
  /// نطاق القيمة (الحد الأقصى)
  final double? valueRangeMax;
  
  /// العوامل المؤثرة في التقييم
  final Map<String, double>? valuationFactors;
  
  /// العقارات المشابهة
  final List<Map<String, dynamic>>? similarProperties;
  
  /// ملاحظات التقييم
  final String? notes;
  
  /// معرف المستخدم الذي قام بالتقييم (إذا كان التقييم من قبل وسيط أو مقيم)
  final String? valuedBy;
  
  /// اسم المستخدم الذي قام بالتقييم
  final String? valuedByName;
  
  /// ما إذا كان التقييم معتمد
  final bool isVerified;
  
  /// تاريخ انتهاء صلاحية التقييم
  final DateTime? expiryDate;
  
  /// بيانات إضافية
  final Map<String, dynamic>? additionalData;

  const PropertyValuation({
    required this.id,
    this.estateId,
    required this.userId,
    required this.propertyType,
    required this.location,
    this.governorate,
    this.city,
    this.district,
    required this.area,
    this.numberOfRooms,
    this.numberOfBathrooms,
    this.buildingAge,
    this.finishingType,
    this.floorNumber,
    this.facingDirection,
    this.viewType,
    this.features,
    required this.valuationDate,
    required this.estimatedSaleValue,
    required this.estimatedRentalValue,
    required this.estimatedPricePerSquareMeter,
    required this.accuracyPercentage,
    this.valueRangeMin,
    this.valueRangeMax,
    this.valuationFactors,
    this.similarProperties,
    this.notes,
    this.valuedBy,
    this.valuedByName,
    this.isVerified = false,
    this.expiryDate,
    this.additionalData,
  });

  /// إنشاء نسخة معدلة من التقييم
  PropertyValuation copyWith({
    String? id,
    String? estateId,
    String? userId,
    String? propertyType,
    String? location,
    String? governorate,
    String? city,
    String? district,
    double? area,
    int? numberOfRooms,
    int? numberOfBathrooms,
    int? buildingAge,
    String? finishingType,
    int? floorNumber,
    String? facingDirection,
    String? viewType,
    List<String>? features,
    DateTime? valuationDate,
    double? estimatedSaleValue,
    double? estimatedRentalValue,
    double? estimatedPricePerSquareMeter,
    double? accuracyPercentage,
    double? valueRangeMin,
    double? valueRangeMax,
    Map<String, double>? valuationFactors,
    List<Map<String, dynamic>>? similarProperties,
    String? notes,
    String? valuedBy,
    String? valuedByName,
    bool? isVerified,
    DateTime? expiryDate,
    Map<String, dynamic>? additionalData,
  }) {
    return PropertyValuation(
      id: id ?? this.id,
      estateId: estateId ?? this.estateId,
      userId: userId ?? this.userId,
      propertyType: propertyType ?? this.propertyType,
      location: location ?? this.location,
      governorate: governorate ?? this.governorate,
      city: city ?? this.city,
      district: district ?? this.district,
      area: area ?? this.area,
      numberOfRooms: numberOfRooms ?? this.numberOfRooms,
      numberOfBathrooms: numberOfBathrooms ?? this.numberOfBathrooms,
      buildingAge: buildingAge ?? this.buildingAge,
      finishingType: finishingType ?? this.finishingType,
      floorNumber: floorNumber ?? this.floorNumber,
      facingDirection: facingDirection ?? this.facingDirection,
      viewType: viewType ?? this.viewType,
      features: features ?? this.features,
      valuationDate: valuationDate ?? this.valuationDate,
      estimatedSaleValue: estimatedSaleValue ?? this.estimatedSaleValue,
      estimatedRentalValue: estimatedRentalValue ?? this.estimatedRentalValue,
      estimatedPricePerSquareMeter: estimatedPricePerSquareMeter ?? this.estimatedPricePerSquareMeter,
      accuracyPercentage: accuracyPercentage ?? this.accuracyPercentage,
      valueRangeMin: valueRangeMin ?? this.valueRangeMin,
      valueRangeMax: valueRangeMax ?? this.valueRangeMax,
      valuationFactors: valuationFactors ?? this.valuationFactors,
      similarProperties: similarProperties ?? this.similarProperties,
      notes: notes ?? this.notes,
      valuedBy: valuedBy ?? this.valuedBy,
      valuedByName: valuedByName ?? this.valuedByName,
      isVerified: isVerified ?? this.isVerified,
      expiryDate: expiryDate ?? this.expiryDate,
      additionalData: additionalData ?? this.additionalData);
  }
  
  /// تحويل التقييم إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'estateId': estateId,
      'userId': userId,
      'propertyType': propertyType,
      'location': location,
      'governorate': governorate,
      'city': city,
      'district': district,
      'area': area,
      'numberOfRooms': numberOfRooms,
      'numberOfBathrooms': numberOfBathrooms,
      'buildingAge': buildingAge,
      'finishingType': finishingType,
      'floorNumber': floorNumber,
      'facingDirection': facingDirection,
      'viewType': viewType,
      'features': features,
      'valuationDate': Timestamp.fromDate(valuationDate),
      'estimatedSaleValue': estimatedSaleValue,
      'estimatedRentalValue': estimatedRentalValue,
      'estimatedPricePerSquareMeter': estimatedPricePerSquareMeter,
      'accuracyPercentage': accuracyPercentage,
      'valueRangeMin': valueRangeMin,
      'valueRangeMax': valueRangeMax,
      'valuationFactors': valuationFactors,
      'similarProperties': similarProperties,
      'notes': notes,
      'valuedBy': valuedBy,
      'valuedByName': valuedByName,
      'isVerified': isVerified,
      'expiryDate': expiryDate != null ? Timestamp.fromDate(expiryDate!) : null,
      'additionalData': additionalData,
    };
  }
  
  /// إنشاء تقييم من Map
  factory PropertyValuation.fromMap(Map<String, dynamic> map) {
    return PropertyValuation(
      id: map['id'] ?? '',
      estateId: map['estateId'],
      userId: map['userId'] ?? '',
      propertyType: map['propertyType'] ?? '',
      location: map['location'] ?? '',
      governorate: map['governorate'],
      city: map['city'],
      district: map['district'],
      area: (map['area'] ?? 0.0).toDouble(),
      numberOfRooms: map['numberOfRooms'],
      numberOfBathrooms: map['numberOfBathrooms'],
      buildingAge: map['buildingAge'],
      finishingType: map['finishingType'],
      floorNumber: map['floorNumber'],
      facingDirection: map['facingDirection'],
      viewType: map['viewType'],
      features: map['features'] != null ? List<String>.from(map['features']) : null,
      valuationDate: map['valuationDate'] is Timestamp 
          ? (map['valuationDate'] as Timestamp).toDate() 
          : DateTime.now(),
      estimatedSaleValue: (map['estimatedSaleValue'] ?? 0.0).toDouble(),
      estimatedRentalValue: (map['estimatedRentalValue'] ?? 0.0).toDouble(),
      estimatedPricePerSquareMeter: (map['estimatedPricePerSquareMeter'] ?? 0.0).toDouble(),
      accuracyPercentage: (map['accuracyPercentage'] ?? 0.0).toDouble(),
      valueRangeMin: map['valueRangeMin'] != null ? (map['valueRangeMin'] as num).toDouble() : null,
      valueRangeMax: map['valueRangeMax'] != null ? (map['valueRangeMax'] as num).toDouble() : null,
      valuationFactors: map['valuationFactors'] != null 
          ? Map<String, double>.from(map['valuationFactors'].map(
              (key, value) => MapEntry(key, (value as num).toDouble())))
          : null,
      similarProperties: map['similarProperties'] != null 
          ? List<Map<String, dynamic>>.from(map['similarProperties']) 
          : null,
      notes: map['notes'],
      valuedBy: map['valuedBy'],
      valuedByName: map['valuedByName'],
      isVerified: map['isVerified'] ?? false,
      expiryDate: map['expiryDate'] is Timestamp 
          ? (map['expiryDate'] as Timestamp).toDate() 
          : null,
      additionalData: map['additionalData']);
  }
  
  /// إنشاء تقييم من DocumentSnapshot
  factory PropertyValuation.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return PropertyValuation.fromMap(data);
  }
  
  /// الحصول على القيمة الإجمالية للعقار
  double getTotalValue() {
    return estimatedSaleValue;
  }
  
  /// الحصول على العائد السنوي
  double getAnnualYield() {
    if (estimatedSaleValue <= 0) {
      return 0.0;
    }
    
    return (estimatedRentalValue * 12 / estimatedSaleValue) * 100;
  }
  
  /// الحصول على فترة استرداد رأس المال (بالسنوات)
  double getPaybackPeriod() {
    if (estimatedRentalValue <= 0) {
      return 0.0;
    }
    
    return estimatedSaleValue / (estimatedRentalValue * 12);
  }
  
  /// التحقق مما إذا كان التقييم ساري المفعول
  bool isValid() {
    if (expiryDate == null) {
      // إذا لم يكن هناك تاريخ انتهاء، نفترض أن التقييم صالح لمدة 3 أشهر
      final validUntil = valuationDate.add(const Duration(days: 90));
      return DateTime.now().isBefore(validUntil);
    }
    
    return DateTime.now().isBefore(expiryDate!);
  }
  
  /// الحصول على نسبة العامل المؤثر في التقييم
  double getFactorPercentage(String factor) {
    if (valuationFactors == null || !valuationFactors!.containsKey(factor)) {
      return 0.0;
    }
    
    return valuationFactors![factor]!;
  }
  
  /// التحقق من التقييم
  PropertyValuation verify(String verifierId, String verifierName) {
    return copyWith(
      isVerified: true,
      valuedBy: verifierId,
      valuedByName: verifierName,
      expiryDate: DateTime.now().add(const Duration(days: 180)));
  }

  @override
  List<Object?> get props => [
    id,
    estateId,
    userId,
    propertyType,
    location,
    governorate,
    city,
    district,
    area,
    numberOfRooms,
    numberOfBathrooms,
    buildingAge,
    finishingType,
    floorNumber,
    facingDirection,
    viewType,
    features,
    valuationDate,
    estimatedSaleValue,
    estimatedRentalValue,
    estimatedPricePerSquareMeter,
    accuracyPercentage,
    valueRangeMin,
    valueRangeMax,
    valuationFactors,
    similarProperties,
    notes,
    valuedBy,
    valuedByName,
    isVerified,
    expiryDate,
    additionalData,
  ];
}
