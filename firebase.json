{"database": {"rules": "database.rules.json"}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/referral", "destination": "/referral.html"}, {"source": "/referral/**", "destination": "/referral.html"}]}, "functions": {"source": "functions", "runtime": "nodejs18"}, "flutter": {"platforms": {"android": {"default": {"projectId": "real-estate-998a9", "appId": "1:951329683889:android:b7de9803cfd70cefaa2590", "fileOutput": "android/app/google-services.json"}}, "dart": {"lib/firebase_options.dart": {"projectId": "real-estate-998a9", "configurations": {"android": "1:951329683889:android:b7de9803cfd70cefaa2590", "ios": "1:951329683889:ios:283721b99a184d2caa2590"}}}}}}