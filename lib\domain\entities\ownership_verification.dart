import 'package:equatable/equatable.dart';

/// حالة التحقق من الملكية
enum OwnershipVerificationStatus {
  pending,
  verified,
  rejected,
  incomplete,
  awaitingDocuments,
}

/// كيان التحقق من ملكية العقار
class OwnershipVerification extends Equatable {
  final String id;
  final String userId;
  final String estateId;
  final OwnershipVerificationStatus status;
  final String ownerIdNumber;
  final String ownerName;
  final String propertyNumber;
  final String propertyType;
  final String area;
  final String address;
  final Map<String, String>? documents;
  final List<OwnershipVerificationNote>? notes;
  final String? certificateId;
  final String? certificateUrl;
  final String? verificationCode;
  final DateTime requestDate;
  final DateTime? verificationDate;
  final DateTime? rejectionDate;
  final String? rejectionReason;
  final DateTime? expiryDate;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان التحقق من ملكية العقار
  const OwnershipVerification({
    required this.id,
    required this.userId,
    required this.estateId,
    required this.status,
    required this.ownerIdNumber,
    required this.ownerName,
    required this.propertyNumber,
    required this.propertyType,
    required this.area,
    required this.address,
    this.documents,
    this.notes,
    this.certificateId,
    this.certificateUrl,
    this.verificationCode,
    required this.requestDate,
    this.verificationDate,
    this.rejectionDate,
    this.rejectionReason,
    this.expiryDate,
    this.additionalInfo,
  });

  /// إنشاء كيان التحقق من ملكية العقار من JSON
  factory OwnershipVerification.fromJson(Map<String, dynamic> json) {
    return OwnershipVerification(
      id: json['id'] as String,
      userId: json['userId'] as String,
      estateId: json['estateId'] as String,
      status: _parseStatus(json['status'] as String),
      ownerIdNumber: json['ownerIdNumber'] as String,
      ownerName: json['ownerName'] as String,
      propertyNumber: json['propertyNumber'] as String,
      propertyType: json['propertyType'] as String,
      area: json['area'] as String,
      address: json['address'] as String,
      documents: json['documents'] != null
          ? Map<String, String>.from(json['documents'] as Map)
          : null,
      notes: json['notes'] != null
          ? (json['notes'] as List)
              .map((note) => OwnershipVerificationNote.fromJson(note))
              .toList()
          : null,
      certificateId: json['certificateId'] as String?,
      certificateUrl: json['certificateUrl'] as String?,
      verificationCode: json['verificationCode'] as String?,
      requestDate: DateTime.parse(json['requestDate'] as String),
      verificationDate: json['verificationDate'] != null
          ? DateTime.parse(json['verificationDate'] as String)
          : null,
      rejectionDate: json['rejectionDate'] != null
          ? DateTime.parse(json['rejectionDate'] as String)
          : null,
      rejectionReason: json['rejectionReason'] as String?,
      expiryDate: json['expiryDate'] != null
          ? DateTime.parse(json['expiryDate'] as String)
          : null,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان التحقق من ملكية العقار إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'estateId': estateId,
      'status': _statusToString(status),
      'ownerIdNumber': ownerIdNumber,
      'ownerName': ownerName,
      'propertyNumber': propertyNumber,
      'propertyType': propertyType,
      'area': area,
      'address': address,
      'documents': documents,
      'notes': notes?.map((note) => note.toJson()).toList(),
      'certificateId': certificateId,
      'certificateUrl': certificateUrl,
      'verificationCode': verificationCode,
      'requestDate': requestDate.toIso8601String(),
      'verificationDate': verificationDate?.toIso8601String(),
      'rejectionDate': rejectionDate?.toIso8601String(),
      'rejectionReason': rejectionReason,
      'expiryDate': expiryDate?.toIso8601String(),
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان التحقق من ملكية العقار مع تعديل بعض الخصائص
  OwnershipVerification copyWith({
    String? id,
    String? userId,
    String? estateId,
    OwnershipVerificationStatus? status,
    String? ownerIdNumber,
    String? ownerName,
    String? propertyNumber,
    String? propertyType,
    String? area,
    String? address,
    Map<String, String>? documents,
    List<OwnershipVerificationNote>? notes,
    String? certificateId,
    String? certificateUrl,
    String? verificationCode,
    DateTime? requestDate,
    DateTime? verificationDate,
    DateTime? rejectionDate,
    String? rejectionReason,
    DateTime? expiryDate,
    Map<String, dynamic>? additionalInfo,
  }) {
    return OwnershipVerification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      estateId: estateId ?? this.estateId,
      status: status ?? this.status,
      ownerIdNumber: ownerIdNumber ?? this.ownerIdNumber,
      ownerName: ownerName ?? this.ownerName,
      propertyNumber: propertyNumber ?? this.propertyNumber,
      propertyType: propertyType ?? this.propertyType,
      area: area ?? this.area,
      address: address ?? this.address,
      documents: documents ?? this.documents,
      notes: notes ?? this.notes,
      certificateId: certificateId ?? this.certificateId,
      certificateUrl: certificateUrl ?? this.certificateUrl,
      verificationCode: verificationCode ?? this.verificationCode,
      requestDate: requestDate ?? this.requestDate,
      verificationDate: verificationDate ?? this.verificationDate,
      rejectionDate: rejectionDate ?? this.rejectionDate,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      expiryDate: expiryDate ?? this.expiryDate,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// تحويل حالة التحقق من نص إلى قيمة تعداد
  static OwnershipVerificationStatus _parseStatus(String status) {
    switch (status) {
      case 'pending':
        return OwnershipVerificationStatus.pending;
      case 'verified':
        return OwnershipVerificationStatus.verified;
      case 'rejected':
        return OwnershipVerificationStatus.rejected;
      case 'incomplete':
        return OwnershipVerificationStatus.incomplete;
      case 'awaiting_documents':
        return OwnershipVerificationStatus.awaitingDocuments;
      default:
        return OwnershipVerificationStatus.pending;
    }
  }

  /// تحويل حالة التحقق من قيمة تعداد إلى نص
  static String _statusToString(OwnershipVerificationStatus status) {
    switch (status) {
      case OwnershipVerificationStatus.pending:
        return 'pending';
      case OwnershipVerificationStatus.verified:
        return 'verified';
      case OwnershipVerificationStatus.rejected:
        return 'rejected';
      case OwnershipVerificationStatus.incomplete:
        return 'incomplete';
      case OwnershipVerificationStatus.awaitingDocuments:
        return 'awaiting_documents';
    }
  }

  /// التحقق مما إذا كان التحقق مكتمل
  bool isVerified() {
    return status == OwnershipVerificationStatus.verified;
  }

  /// التحقق مما إذا كان التحقق مرفوض
  bool isRejected() {
    return status == OwnershipVerificationStatus.rejected;
  }

  /// التحقق مما إذا كان التحقق في انتظار المستندات
  bool isAwaitingDocuments() {
    return status == OwnershipVerificationStatus.awaitingDocuments;
  }

  /// التحقق مما إذا كان التحقق ساري المفعول
  bool isValid() {
    if (status != OwnershipVerificationStatus.verified) {
      return false;
    }

    if (expiryDate == null) {
      // إذا لم يكن هناك تاريخ انتهاء، نفترض أن التحقق صالح لمدة سنة
      final validUntil = verificationDate!.add(const Duration(days: 365));
      return DateTime.now().isBefore(validUntil);
    }

    return DateTime.now().isBefore(expiryDate!);
  }

  /// الحصول على المدة المتبقية لصلاحية التحقق بالأيام
  int? getRemainingDays() {
    if (!isVerified() || verificationDate == null) {
      return null;
    }

    final expiryDate = this.expiryDate ??
        verificationDate!.add(const Duration(days: 365));
    final now = DateTime.now();
    return expiryDate.difference(now).inDays;
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        estateId,
        status,
        ownerIdNumber,
        ownerName,
        propertyNumber,
        propertyType,
        area,
        address,
        documents,
        notes,
        certificateId,
        certificateUrl,
        verificationCode,
        requestDate,
        verificationDate,
        rejectionDate,
        rejectionReason,
        expiryDate,
        additionalInfo,
      ];
}

/// كيان ملاحظة التحقق من ملكية العقار
class OwnershipVerificationNote extends Equatable {
  final String id;
  final String content;
  final String authorId;
  final String authorName;
  final DateTime createdAt;
  final bool isInternal;

  /// إنشاء كيان ملاحظة التحقق من ملكية العقار
  const OwnershipVerificationNote({
    required this.id,
    required this.content,
    required this.authorId,
    required this.authorName,
    required this.createdAt,
    this.isInternal = false,
  });

  /// إنشاء كيان ملاحظة التحقق من ملكية العقار من JSON
  factory OwnershipVerificationNote.fromJson(Map<String, dynamic> json) {
    return OwnershipVerificationNote(
      id: json['id'] as String,
      content: json['content'] as String,
      authorId: json['authorId'] as String,
      authorName: json['authorName'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      isInternal: json['isInternal'] as bool? ?? false);
  }

  /// تحويل كيان ملاحظة التحقق من ملكية العقار إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'authorId': authorId,
      'authorName': authorName,
      'createdAt': createdAt.toIso8601String(),
      'isInternal': isInternal,
    };
  }

  /// نسخ كيان ملاحظة التحقق من ملكية العقار مع تعديل بعض الخصائص
  OwnershipVerificationNote copyWith({
    String? id,
    String? content,
    String? authorId,
    String? authorName,
    DateTime? createdAt,
    bool? isInternal,
  }) {
    return OwnershipVerificationNote(
      id: id ?? this.id,
      content: content ?? this.content,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      createdAt: createdAt ?? this.createdAt,
      isInternal: isInternal ?? this.isInternal);
  }

  @override
  List<Object?> get props => [
        id,
        content,
        authorId,
        authorName,
        createdAt,
        isInternal,
      ];
}
