import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart' as quill;
import 'package:flutter_quill_extensions/flutter_quill_extensions.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:video_player/video_player.dart';

import '../../../core/theme/app_colors.dart';

/// محرر النصوص المتقدم
class RichTextEditor extends StatefulWidget {
  /// النص الأولي
  final String initialText;

  /// دالة يتم استدعاؤها عند تغيير النص
  final Function(String html, String plainText) onTextChanged;

  /// دالة يتم استدعاؤها عند إضافة صورة
  final Function(File)? onImageAdded;

  /// دالة يتم استدعاؤها عند إضافة ملف
  final Function(File)? onFileAdded;

  /// دالة يتم استدعاؤها عند إضافة فيديو
  final Function(File)? onVideoAdded;

  /// ارتفاع المحرر
  final double? height;

  /// ما إذا كان المحرر للقراءة فقط
  final bool readOnly;

  /// ما إذا كان المحرر مصغر
  final bool isCompact;

  /// نص التلميح
  final String? hintText;

  const RichTextEditor({
    super.key,
    this.initialText = '',
    required this.onTextChanged,
    this.onImageAdded,
    this.onFileAdded,
    this.onVideoAdded,
    this.height,
    this.readOnly = false,
    this.isCompact = false,
    this.hintText,
  });

  @override
  State<RichTextEditor> createState() => _RichTextEditorState();
}

class _RichTextEditorState extends State<RichTextEditor> {
  late quill.QuillController _controller;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  void _initializeController() {
    if (widget.initialText.isNotEmpty) {
      try {
        // محاولة تحميل النص كـ HTML
        final document = quill.Document.fromJson(
          quill.Document.fromHtml(widget.initialText).toDelta().toJson());
        _controller = quill.QuillController(
          document: document,
          selection: const TextSelection.collapsed(offset: 0));
      } catch (e) {
        // إذا فشل، استخدم النص العادي
        _controller = quill.QuillController.basic();
        _controller.document.insert(0, widget.initialText);
      }
    } else {
      _controller = quill.QuillController.basic();
    }

    _controller.document.changes.listen((event) {
      final html = quill.Document.fromDelta(_controller.document.toDelta())
          .toHtml();
      final plainText = _controller.document.toPlainText();
      widget.onTextChanged(html, plainText);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300)),
      child: Column(
        children: [
          if (!widget.readOnly && !widget.isCompact) _buildToolbar(),
          if (!widget.readOnly && widget.isCompact) _buildCompactToolbar(),
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(8),
              child: quill.QuillEditor(
                controller: _controller,
                scrollController: ScrollController(),
                scrollable: true,
                focusNode: _focusNode,
                autoFocus: false,
                readOnly: widget.readOnly,
                expands: false,
                padding: const EdgeInsets.all(8),
                placeholder: widget.hintText ?? 'اكتب هنا...',
                embedBuilders: FlutterQuillEmbeds.builders(),
                customStyles: quill.DefaultStyles(
                  h1: quill.DefaultTextBlockStyle(
                    TextStyle(
                      fontSize: 24,
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      height: 1.5),
                    const quill.VerticalSpacing(16, 0),
                    const quill.VerticalSpacing(0, 0),
                    null),
                  h2: quill.DefaultTextBlockStyle(
                    TextStyle(
                      fontSize: 20,
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      height: 1.5),
                    const quill.VerticalSpacing(12, 0),
                    const quill.VerticalSpacing(0, 0),
                    null),
                  h3: quill.DefaultTextBlockStyle(
                    TextStyle(
                      fontSize: 18,
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      height: 1.5),
                    const quill.VerticalSpacing(8, 0),
                    const quill.VerticalSpacing(0, 0),
                    null),
                  paragraph: quill.DefaultTextBlockStyle(
                    TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                      height: 1.5),
                    const quill.VerticalSpacing(4, 0),
                    const quill.VerticalSpacing(0, 0),
                    null),
                  bold: const TextStyle(
                    fontWeight: FontWeight.bold),
                  italic: const TextStyle(
                    fontStyle: FontStyle.italic),
                  underline: const TextStyle(
                    decoration: TextDecoration.underline),
                  strikeThrough: const TextStyle(
                    decoration: TextDecoration.lineThrough),
                  link: TextStyle(
                    color: AppColors.primary,
                    decoration: TextDecoration.underline))))),
        ]));
  }

  /// بناء شريط الأدوات
  Widget _buildToolbar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12)),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300))),
      child: quill.QuillToolbar.basic(
        controller: _controller,
        showFontFamily: false,
        showFontSize: false,
        showSearchButton: false,
        showCodeBlock: false,
        showInlineCode: false,
        showListCheck: false,
        showIndent: false,
        showLink: true,
        showUndo: true,
        showRedo: true,
        showQuote: true,
        showClearFormat: true,
        showBoldButton: true,
        showItalicButton: true,
        showUnderLineButton: true,
        showStrikeThrough: true,
        showColorButton: true,
        showBackgroundColorButton: true,
        showAlignmentButtons: true,
        showLeftAlignment: true,
        showCenterAlignment: true,
        showRightAlignment: true,
        showJustifyAlignment: true,
        showHeaderStyle: true,
        showListNumbers: true,
        showListBullets: true,
        multiRowsDisplay: false,
        showDividers: true,
        showImageButton: true,
        showVideoButton: false,
        showCameraButton: true,
        iconTheme: quill.QuillIconTheme(
          iconSelectedFillColor: AppColors.primary,
          iconSelectedColor: Colors.white,
          iconUnselectedFillColor: Colors.transparent,
          iconUnselectedColor: Colors.grey.shade700,
          disabledIconColor: Colors.grey.shade400,
          disabledIconFillColor: Colors.transparent),
        dialogTheme: quill.QuillDialogTheme(
          dialogBackgroundColor: Colors.white,
          labelTextStyle: const TextStyle(
            color: Colors.black,
            fontSize: 14),
          inputTextStyle: const TextStyle(
            color: Colors.black,
            fontSize: 14)),
        embedButtons: FlutterQuillEmbeds.buttons(
          onImagePickCallback: _onImagePickCallback,
          webImagePickImpl: _webImagePickImpl)));
  }

  /// دالة اختيار الصورة
  Future<String> _onImagePickCallback(File file) async {
    if (widget.onImageAdded != null) {
      widget.onImageAdded!(file);
    }
    return file.path;
  }

  /// دالة اختيار الصورة من الويب
  Future<String?> _webImagePickImpl(
      Future<String> Function(File file) onImagePickCallback) async {
    final picker = ImagePicker();
    final XFile? pickedFile =
        await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      final file = File(pickedFile.path);
      return onImagePickCallback(file);
    }
    return null;
  }

  /// بناء شريط الأدوات المصغر
  Widget _buildCompactToolbar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12)),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1))),
      child: quill.QuillToolbar.basic(
        controller: _controller,
        showFontFamily: false,
        showFontSize: false,
        showBackgroundColorButton: false,
        showColorButton: false,
        showCodeBlock: false,
        showInlineCode: false,
        showListCheck: false,
        showIndent: false,
        showLink: true,
        showSearchButton: false,
        showSubscript: false,
        showSuperscript: false,
        multiRowsDisplay: false,
        showDirection: false,
        showAlignmentButtons: false,
        showHeaderStyle: false,
        showListNumbers: false,
        showListBullets: false,
        showQuote: false,
        showStrikeThrough: false,
        showUnderLineButton: true,
        showItalicButton: true,
        showBoldButton: true,
        showClearFormat: false,
        toolbarIconSize: 18,
        toolbarSectionSpacing: 4,
        toolbarIconAlignment: WrapAlignment.center));
  }
}

/// أزرار التضمين لمحرر النصوص
class FlutterQuillEmbeds {
  /// الحصول على أزرار التضمين
  static List<quill.EmbedButtonBuilder> buttons({
    Future<String> Function(File file)? onImagePickCallback,
    Future<String?> Function(
            Future<String> Function(File file) onImagePickCallback)?
        webImagePickImpl,
  }) {
    return [
      (controller, icon, color) => quill.ImageButton(
            icon: icon,
            controller: controller,
            onImagePickCallback: onImagePickCallback ?? ((file) async => file.path),
            webImagePickImpl: webImagePickImpl),
    ];
  }

  /// الحصول على بناة التضمين
  static List<quill.EmbedBuilder> builders() {
    return [
      quill.ImageEmbedBuilder(),
      quill.VideoEmbedBuilder(),
    ];
  }
}
