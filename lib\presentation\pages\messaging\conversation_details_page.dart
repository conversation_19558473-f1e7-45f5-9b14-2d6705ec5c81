import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kuwait_corners/core/services/messaging_service.dart';
import 'package:kuwait_corners/domain/entities/conversation.dart';
import 'package:kuwait_corners/domain/entities/message.dart';
import 'package:kuwait_corners/data/repositories_impl/messaging_repository_impl.dart';
import 'package:kuwait_corners/presentation/widgets/loading_indicator.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// صفحة تفاصيل المحادثة
class ConversationDetailsPage extends StatefulWidget {
  final String conversationId;

  const ConversationDetailsPage({
    super.key,
    required this.conversationId,
  });

  @override
  State<ConversationDetailsPage> createState() =>
      _ConversationDetailsPageState();
}

class _ConversationDetailsPageState extends State<ConversationDetailsPage> {
  late MessagingService _messagingService;

  Conversation? _conversation;
  List<Message> _messages = [];
  bool _isLoading = true;
  String? _errorMessage;

  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  Timer? _typingTimer;
  bool _isTyping = false;

  StreamSubscription? _messagesSubscription;
  StreamSubscription? _typingSubscription;
  StreamSubscription? _conversationSubscription;

  @override
  void initState() {
    super.initState();
    // Create a default messaging service with a real repository
    final messagingRepository = MessagingRepositoryImpl();
    _messagingService = MessagingService(
      messagingRepository: messagingRepository);
    _loadConversation();

    // الاستماع للتغييرات في المحادثة
    _listenToConversation();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _typingTimer?.cancel();
    _messagesSubscription?.cancel();
    _typingSubscription?.cancel();
    _conversationSubscription?.cancel();

    // إيقاف حالة الكتابة عند مغادرة الصفحة
    if (_isTyping) {
      _messagingService.setTyping(widget.conversationId, false);
    }

    super.dispose();
  }

  /// تحميل المحادثة
  Future<void> _loadConversation() async {
    final user = _auth.currentUser;
    if (user == null) {
      setState(() {
        _errorMessage = 'يجب تسجيل الدخول لعرض المحادثة';
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل المحادثة
      final conversation =
          await _messagingService.getConversationById(widget.conversationId);

      if (conversation == null) {
        setState(() {
          _errorMessage = 'المحادثة غير موجودة';
          _isLoading = false;
        });
        return;
      }

      // تعيين جميع الرسائل كمقروءة
      await _messagingService.markConversationAsRead(widget.conversationId);

      setState(() {
        _conversation = conversation;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل المحادثة';
        _isLoading = false;
      });
    }
  }

  /// تحميل الرسائل
  Future<void> _loadMessages() async {
    try {
      final messages = await _messagingService.getConversationMessages(
        widget.conversationId);

      if (mounted) {
        setState(() {
          _messages = messages;
        });

        // تمرير إلى آخر رسالة
        if (_messages.isNotEmpty) {
          Future.delayed(const Duration(milliseconds: 100), () {
            if (_scrollController.hasClients) {
              _scrollController.animateTo(
                _scrollController.position.maxScrollExtent,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut);
            }
          });
        }
      }
    } catch (e) {
      // Handle error
      if (mounted) {
        setState(() {
          // Just keep the messages empty
        });
      }
    }
  }

  /// الاستماع للتغييرات في المحادثة
  void _listenToConversation() {
    // Comment out the messages subscription for now
    // _messagesSubscription = _messagingService
    //     .listenToConversationMessages(widget.conversationId)
    //     .listen((messages) {
    //   setState(() {
    //     _messages = messages;
    //   });
    //
    //   // تمرير إلى آخر رسالة
    //   if (_messages.isNotEmpty) {
    //     Future.delayed(const Duration(milliseconds: 100), () {
    //       if (_scrollController.hasClients) {
    //         _scrollController.animateTo(
    //           _scrollController.position.maxScrollExtent,
    //           duration: const Duration(milliseconds: 300),
    //           curve: Curves.easeOut,
    //         );
    //       }
    //     });
    //   }
    // });

    // Load messages manually instead
    _loadMessages();

    // الاستماع للتغييرات في حالة الكتابة
    _typingSubscription = _messagingService
        .listenToTypingParticipants(widget.conversationId)
        .listen((typingUsers) {
      if (mounted) {
        setState(() {
          if (_conversation != null) {
            _conversation = _conversation!.copyWith(
              typingParticipants: typingUsers);
          }
        });
      }
    });

    // Comment out the conversation subscription for now
    // _conversationSubscription = _messagingService
    //     .listenToConversation(widget.conversationId)
    //     .listen((conversation) {
    //   if (mounted && conversation != null) {
    //     setState(() {
    //       _conversation = conversation;
    //     });
    //   }
    // });
  }

  /// إرسال رسالة نصية
  Future<void> _sendTextMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    _messageController.clear();

    // إيقاف حالة الكتابة
    if (_isTyping) {
      _isTyping = false;
      _messagingService.setTyping(widget.conversationId, false);
    }

    try {
      await _messagingService.sendTextMessage(
        widget.conversationId,
        text);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('حدث خطأ أثناء إرسال الرسالة')));
    }
  }

  /// إرسال رسالة صورة
  Future<void> _sendImageMessage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      try {
        await _messagingService.sendImageMessage(
          widget.conversationId,
          File(pickedFile.path));
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء إرسال الصورة')));
        }
      }
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.camera);

    if (pickedFile != null) {
      try {
        await _messagingService.sendImageMessage(
          widget.conversationId,
          File(pickedFile.path));
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء إرسال الصورة')));
        }
      }
    }
  }

  /// تحديث حالة الكتابة
  void _updateTypingStatus(String text) {
    if (text.isNotEmpty && !_isTyping) {
      // بدء الكتابة
      _isTyping = true;
      _messagingService.setTyping(widget.conversationId, true);
    }

    // إعادة ضبط المؤقت
    _typingTimer?.cancel();
    _typingTimer = Timer(const Duration(seconds: 3), () {
      if (_isTyping) {
        _isTyping = false;
        _messagingService.setTyping(widget.conversationId, false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final user = _auth.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: _conversation != null
            ? _buildConversationTitle()
            : const Text('محادثة'),
        actions: [
          if (_conversation != null &&
              _conversation!.type == ConversationType.estate)
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                // التنقل إلى صفحة العقار
                // Navigator.push(
                //   context,
                //   MaterialPageRoute(
                //     builder: (context) => EstateDetailsPage(
                //       estateId: _conversation!.estateId!,
                //     ),
                //   ),
                // );
              }),
        ]),
      body: _isLoading && _conversation == null
          ? const LoadingIndicator()
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(_errorMessage!),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadConversation,
                        child: const Text('إعادة المحاولة')),
                    ]))
              : Column(
                  children: [
                    if (_conversation != null &&
                        _conversation!.type == ConversationType.estate)
                      _buildEstateInfo(),
                    Expanded(
                      child: _messages.isEmpty
                          ? _buildEmptyState()
                          : ListView.builder(
                              controller: _scrollController,
                              padding: const EdgeInsets.all(16),
                              itemCount: _messages.length,
                              itemBuilder: (context, index) {
                                final message = _messages[index];
                                final isMe = user != null &&
                                    message.senderId == user.uid;

                                return _buildMessageBubble(message, isMe);
                              })),
                    if (_conversation != null && _isTypingOtherUser())
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        child: Row(
                          children: [
                            Text(
                              '${_getOtherUserName()} يكتب الآن...',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                                fontStyle: FontStyle.italic)),
                          ])),
                    _buildMessageInput(),
                  ]));
  }

  /// بناء عنوان المحادثة
  Widget _buildConversationTitle() {
    final user = _auth.currentUser;
    if (user == null || _conversation == null) return const Text('محادثة');

    // تحديد معلومات المستخدم الآخر
    final otherUserIndex =
        _conversation!.participantIds.indexWhere((id) => id != user.uid);
    if (otherUserIndex == -1) return const Text('محادثة');

    final otherUserName = _conversation!.participantNames[otherUserIndex];
    final otherUserImage = _conversation!.participantImages[otherUserIndex];

    return Row(
      children: [
        CircleAvatar(
          radius: 16,
          backgroundImage:
              otherUserImage != null ? NetworkImage(otherUserImage) : null,
          child: otherUserImage == null
              ? const Icon(Icons.person, size: 16)
              : null),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            _conversation!.type == ConversationType.estate
                ? _conversation!.title
                : otherUserName,
            maxLines: 1,
            overflow: TextOverflow.ellipsis)),
      ]);
  }

  /// بناء معلومات العقار
  Widget _buildEstateInfo() {
    if (_conversation == null ||
        _conversation!.type != ConversationType.estate ||
        _conversation!.estateTitle == null) {
      return const SizedBox();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1)),
        ]),
      child: Row(
        children: [
          if (_conversation!.estateImage != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                _conversation!.estateImage!,
                width: 60,
                height: 60,
                fit: BoxFit.cover))
          else
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8)),
              child: const Icon(
                Icons.home,
                color: Colors.grey)),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _conversation!.estateTitle!,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
                const SizedBox(height: 4),
                Text(
                  'استفسار عن عقار',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600)),
              ])),
          IconButton(
            icon: const Icon(Icons.arrow_forward),
            onPressed: () {
              // التنقل إلى صفحة العقار
              // Navigator.push(
              //   context,
              //   MaterialPageRoute(
              //     builder: (context) => EstateDetailsPage(
              //       estateId: _conversation!.estateId!,
              //     ),
              //   ),
              // );
            }),
        ]));
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد رسائل',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600)),
          const SizedBox(height: 8),
          Text(
            'ابدأ المحادثة بإرسال رسالة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500),
            textAlign: TextAlign.center),
        ]));
  }

  /// بناء فقاعة رسالة
  Widget _buildMessageBubble(Message message, bool isMe) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              radius: 16,
              backgroundImage: message.senderImage != null
                  ? NetworkImage(message.senderImage!)
                  : null,
              child: message.senderImage == null
                  ? const Icon(Icons.person, size: 16)
                  : null),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isMe ? Colors.blue.shade100 : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(16).copyWith(
                  bottomRight: isMe ? const Radius.circular(0) : null,
                  bottomLeft: !isMe ? const Radius.circular(0) : null)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (message.type == MessageType.text)
                    Text(
                      message.content,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade800))
                  else if (message.type == MessageType.image)
                    GestureDetector(
                      onTap: () {
                        // عرض الصورة بحجم كامل
                        showDialog(
                          context: context,
                          builder: (context) {
                            return Dialog(
                              child: Image.network(message.content));
                          });
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          message.content,
                          width: 200,
                          height: 200,
                          fit: BoxFit.cover)))
                  else if (message.type == MessageType.estate)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.grey.shade300)),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.home,
                            color: Colors.blue),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              message.metadata?['estateTitle'] ?? 'عقار',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis)),
                        ])),
                  const SizedBox(height: 4),
                  Text(
                    _formatDate(message.timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey.shade600)),
                ]))),
          if (isMe) ...[
            const SizedBox(width: 8),
            Icon(
              message.status == MessageStatus.sent
                  ? Icons.check
                  : message.status == MessageStatus.delivered
                      ? Icons.done_all
                      : message.status == MessageStatus.read
                          ? Icons.done_all
                          : Icons.access_time,
              size: 16,
              color: message.status == MessageStatus.read
                  ? Colors.blue
                  : Colors.grey.shade600),
          ],
        ]));
  }

  /// بناء حقل إدخال الرسالة
  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, -1)),
        ]),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.photo_library),
            onPressed: _sendImageMessage),
          IconButton(
            icon: const Icon(Icons.camera_alt),
            onPressed: _takePhoto),
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                hintText: 'اكتب رسالة...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(24))),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8)),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onChanged: _updateTypingStatus,
              onSubmitted: (_) => _sendTextMessage())),
          const SizedBox(width: 8),
          CircleAvatar(
            backgroundColor: Colors.blue,
            child: IconButton(
              icon: const Icon(
                Icons.send,
                color: Colors.white),
              onPressed: _sendTextMessage)),
        ]));
  }

  /// التحقق مما إذا كان المستخدم الآخر يكتب
  bool _isTypingOtherUser() {
    final user = _auth.currentUser;
    if (user == null ||
        _conversation == null ||
        _conversation!.typingParticipants == null) {
      return false;
    }

    return _conversation!.typingParticipants!.any((id) => id != user.uid);
  }

  /// الحصول على اسم المستخدم الآخر
  String _getOtherUserName() {
    final user = _auth.currentUser;
    if (user == null || _conversation == null) return '';

    final otherUserIndex =
        _conversation!.participantIds.indexWhere((id) => id != user.uid);
    if (otherUserIndex == -1) return '';

    return _conversation!.participantNames[otherUserIndex];
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
