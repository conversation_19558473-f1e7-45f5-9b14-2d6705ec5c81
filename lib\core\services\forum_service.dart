import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';

import '../../domain/models/forum/category_model.dart';
import '../../domain/models/forum/topic_model.dart';
import '../../domain/models/forum/post_model.dart';
import '../../domain/models/forum/user_statistics_model.dart';
import '../../domain/models/forum/notification_model.dart';
import '../../domain/models/forum/reaction_model.dart';
import '../../domain/models/forum/poll_model.dart';
import '../../domain/models/forum/badge_model.dart';
import '../../domain/models/forum/achievement_model.dart';
import '../../domain/models/forum/report_model.dart';
import '../../domain/repositories/lobby_repository.dart';
import 'rewards_service.dart';
import 'poll_service.dart';
import 'moderation_service.dart';


/// خدمة اللوبي
class ForumService {
  final LobbyRepository _repository;
  late final RewardsService _rewardsService;
  late final PollService _pollService;
  late final ModerationService _moderationService;


  ForumService({required LobbyRepository repository})
      : _repository = repository {
    _rewardsService = RewardsService();
    _pollService = PollService();
    _moderationService = ModerationService();

  }

  // الخدمات المساعدة
  RewardsService get rewardsService => _rewardsService;
  PollService get pollService => _pollService;
  ModerationService get moderationService => _moderationService;


  // فئات اللوبي

  /// الحصول على جميع فئات اللوبي
  Future<List<CategoryModel>> getCategories() async {
    return await _repository.getCategories();
  }

  /// الحصول على فئة محددة بواسطة المعرف
  Future<CategoryModel?> getCategoryById(String categoryId) async {
    return await _repository.getCategoryById(categoryId);
  }

  /// إنشاء فئة جديدة
  Future<CategoryModel> createCategory(CategoryModel category) async {
    return await _repository.createCategory(category);
  }

  /// تحديث فئة موجودة
  Future<void> updateCategory(CategoryModel category) async {
    await _repository.updateCategory(category);
  }

  /// حذف فئة
  Future<void> deleteCategory(String categoryId) async {
    await _repository.deleteCategory(categoryId);
  }

  // مواضيع اللوبي

  /// الحصول على مواضيع فئة محددة
  Future<List<TopicModel>> getTopicsByCategory(
    String categoryId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
    String? sortBy,
    bool descending = true,
  }) async {
    return await _repository.getTopicsByCategory(
      categoryId,
      limit: limit,
      startAfter: startAfter,
      sortBy: sortBy,
      descending: descending);
  }

  /// الحصول على المواضيع المميزة
  Future<List<TopicModel>> getFeaturedTopics({int limit = 5}) async {
    return await _repository.getFeaturedTopics(limit: limit);
  }

  /// الحصول على المواضيع المثبتة
  Future<List<TopicModel>> getPinnedTopics({int limit = 5}) async {
    return await _repository.getPinnedTopics(limit: limit);
  }

  /// الحصول على أحدث المواضيع
  Future<List<TopicModel>> getLatestTopics({int limit = 10}) async {
    return await _repository.getLatestTopics(limit: limit);
  }

  /// الحصول على المواضيع الأكثر مشاهدة
  Future<List<TopicModel>> getMostViewedTopics({int limit = 10}) async {
    return await _repository.getMostViewedTopics(limit: limit);
  }

  /// الحصول على المواضيع الأكثر إعجاباً
  Future<List<TopicModel>> getMostLikedTopics({int limit = 10}) async {
    return await _repository.getMostLikedTopics(limit: limit);
  }

  /// الحصول على المواضيع الأكثر نشاطاً
  Future<List<TopicModel>> getMostActiveTopics({int limit = 10}) async {
    return await _repository.getMostActiveTopics(limit: limit);
  }

  /// الحصول على مواضيع مستخدم محدد
  Future<List<TopicModel>> getUserTopics(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  }) async {
    return await _repository.getUserTopics(
      userId,
      limit: limit,
      startAfter: startAfter);
  }

  /// الحصول على موضوع محدد بواسطة المعرف
  Future<TopicModel?> getTopicById(String topicId) async {
    return await _repository.getTopicById(topicId);
  }

  /// إنشاء موضوع جديد
  Future<TopicModel> createTopic(TopicModel topic, {List<File>? images}) async {
    final createdTopic = await _repository.createTopic(topic, images: images);

    // منح نقاط لمنشئ الموضوع
    await _rewardsService.awardPointsForActivity(topic.userId, 'create_topic');

    return createdTopic;
  }

  /// تحديث موضوع موجود
  Future<void> updateTopic(TopicModel topic,
      {List<File>? newImages, List<String>? imagesToDelete}) async {
    await _repository.updateTopic(topic,
        newImages: newImages, imagesToDelete: imagesToDelete);
  }

  /// حذف موضوع
  Future<void> deleteTopic(String topicId) async {
    await _repository.deleteTopic(topicId);
  }

  /// زيادة عدد مشاهدات الموضوع
  Future<void> incrementTopicViews(String topicId) async {
    await _repository.incrementTopicViews(topicId);
  }

  /// الإعجاب بموضوع
  Future<void> likeTopic(String topicId, String userId) async {
    await _repository.likeTopic(topicId, userId);

    // منح نقاط للمعجب
    await _rewardsService.awardPointsForActivity(userId, 'give_like');

    // منح نقاط لصاحب الموضوع
    final topic = await getTopicById(topicId);
    if (topic != null && topic.userId != userId) {
      await _rewardsService.awardPointsForActivity(topic.userId, 'receive_like');
    }
  }

  /// إلغاء الإعجاب بموضوع
  Future<void> unlikeTopic(String topicId, String userId) async {
    await _repository.unlikeTopic(topicId, userId);
  }

  /// إضافة تفاعل إلى موضوع
  Future<void> addTopicReaction(
    String topicId,
    String userId,
    String reactionType) async {
    await _repository.addTopicReaction(topicId, userId, reactionType);
  }

  /// إزالة تفاعل من موضوع
  Future<void> removeTopicReaction(
    String topicId,
    String userId,
    String reactionType) async {
    await _repository.removeTopicReaction(topicId, userId, reactionType);
  }

  /// تثبيت موضوع
  Future<void> pinTopic(String topicId) async {
    await _repository.pinTopic(topicId);
  }

  /// إلغاء تثبيت موضوع
  Future<void> unpinTopic(String topicId) async {
    await _repository.unpinTopic(topicId);
  }

  /// تمييز موضوع
  Future<void> featureTopic(String topicId) async {
    await _repository.featureTopic(topicId);
  }

  /// إلغاء تمييز موضوع
  Future<void> unfeatureTopic(String topicId) async {
    await _repository.unfeatureTopic(topicId);
  }

  /// إغلاق موضوع
  Future<void> closeTopic(String topicId) async {
    await _repository.closeTopic(topicId);
  }

  /// إعادة فتح موضوع
  Future<void> reopenTopic(String topicId) async {
    await _repository.reopenTopic(topicId);
  }

  /// تعليم موضوع كمحلول
  Future<void> markTopicAsSolved(String topicId, String postId) async {
    await _repository.markTopicAsSolved(topicId, postId);
  }

  /// إلغاء تعليم موضوع كمحلول
  Future<void> unmarkTopicAsSolved(String topicId) async {
    await _repository.unmarkTopicAsSolved(topicId);
  }

  // مشاركات اللوبي

  /// الحصول على مشاركات موضوع محدد
  Future<List<PostModel>> getPostsByTopic(
    String topicId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
    bool includeReplies = true,
  }) async {
    return await _repository.getPostsByTopic(
      topicId,
      limit: limit,
      startAfter: startAfter,
      includeReplies: includeReplies);
  }

  /// الحصول على ردود مشاركة محددة
  Future<List<PostModel>> getRepliesByPost(
    String postId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  }) async {
    return await _repository.getRepliesByPost(
      postId,
      limit: limit,
      startAfter: startAfter);
  }

  /// الحصول على مشاركات مستخدم محدد
  Future<List<PostModel>> getUserPosts(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    return await _repository.getUserPosts(
      userId,
      limit: limit,
      startAfter: startAfter);
  }

  /// الحصول على مشاركة محددة بواسطة المعرف
  Future<PostModel?> getPostById(String postId) async {
    return await _repository.getPostById(postId);
  }

  /// إنشاء مشاركة جديدة
  Future<PostModel> createPost(PostModel post, {List<File>? images}) async {
    final createdPost = await _repository.createPost(post, images: images);

    // منح نقاط لمنشئ المشاركة
    await _rewardsService.awardPointsForActivity(post.userId, 'create_post');

    return createdPost;
  }

  /// تحديث مشاركة موجودة
  Future<void> updatePost(PostModel post,
      {List<File>? newImages, List<String>? imagesToDelete}) async {
    await _repository.updatePost(post,
        newImages: newImages, imagesToDelete: imagesToDelete);
  }

  /// حذف مشاركة
  Future<void> deletePost(String postId) async {
    await _repository.deletePost(postId);
  }

  /// الإعجاب بمشاركة
  Future<void> likePost(String postId, String userId) async {
    await _repository.likePost(postId, userId);
  }

  /// إلغاء الإعجاب بمشاركة
  Future<void> unlikePost(String postId, String userId) async {
    await _repository.unlikePost(postId, userId);
  }

  /// إضافة تفاعل إلى مشاركة
  Future<void> addPostReaction(
    String postId,
    String userId,
    String reactionType) async {
    await _repository.addPostReaction(postId, userId, reactionType);
  }

  /// إزالة تفاعل من مشاركة
  Future<void> removePostReaction(
    String postId,
    String userId,
    String reactionType) async {
    await _repository.removePostReaction(postId, userId, reactionType);
  }

  /// تثبيت مشاركة
  Future<void> pinPost(String postId) async {
    await _repository.pinPost(postId);
  }

  /// إلغاء تثبيت مشاركة
  Future<void> unpinPost(String postId) async {
    await _repository.unpinPost(postId);
  }

  /// تعليم مشاركة كأفضل إجابة
  Future<void> markPostAsBestAnswer(String postId, String topicId) async {
    await _repository.markPostAsBestAnswer(postId, topicId);
  }

  /// إلغاء تعليم مشاركة كأفضل إجابة
  Future<void> unmarkPostAsBestAnswer(String postId, String topicId) async {
    await _repository.unmarkPostAsBestAnswer(postId, topicId);
  }

  /// الإبلاغ عن مشاركة
  Future<void> reportPost(String postId, String userId, String reason) async {
    await _repository.reportPost(postId, userId, reason);
  }

  // البحث

  /// البحث في المواضيع
  Future<List<TopicModel>> searchTopics(
    String query, {
    int limit = 20,
    String? categoryId,
  }) async {
    return await _repository.searchTopics(
      query,
      limit: limit,
      categoryId: categoryId);
  }

  /// البحث في المشاركات
  Future<List<PostModel>> searchPosts(
    String query, {
    int limit = 20,
    String? topicId,
  }) async {
    return await _repository.searchPosts(
      query,
      limit: limit,
      topicId: topicId);
  }

  // إحصائيات المستخدم

  /// الحصول على إحصائيات مستخدم محدد
  Future<UserStatisticsModel?> getUserStatistics(String userId) async {
    return await _repository.getUserStatistics(userId);
  }

  /// تحديث إحصائيات مستخدم
  Future<void> updateUserStatistics(String userId) async {
    await _repository.updateUserStatistics(userId);
  }

  /// إعادة حساب إحصائيات مستخدم
  Future<UserStatisticsModel?> recalculateUserStatistics(String userId) async {
    return await _repository.recalculateUserStatistics(userId);
  }

  // الإشعارات

  /// الحصول على إشعارات مستخدم محدد
  Future<List<NotificationModel>> getUserNotifications(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
    bool unreadOnly = false,
  }) async {
    return await _repository.getUserNotifications(
      userId,
      limit: limit,
      startAfter: startAfter,
      unreadOnly: unreadOnly);
  }

  /// تعليم إشعار كمقروء
  Future<void> markNotificationAsRead(String notificationId) async {
    await _repository.markNotificationAsRead(notificationId);
  }

  /// تعليم جميع إشعارات المستخدم كمقروءة
  Future<void> markAllNotificationsAsRead(String userId) async {
    await _repository.markAllNotificationsAsRead(userId);
  }

  /// إنشاء إشعار جديد
  Future<NotificationModel> createNotification(
      NotificationModel notification) async {
    return await _repository.createNotification(notification);
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    await _repository.deleteNotification(notificationId);
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadNotificationsCount(String userId) async {
    return await _repository.getUnreadNotificationsCount(userId);
  }

  // التفاعلات

  /// الحصول على إحصائيات التفاعلات
  Future<ReactionStats?> getReactionStats(
    String itemId,
    ReactionItemType itemType) async {
    return await _repository.getReactionStats(itemId, itemType);
  }

  // متابعة المواضيع

  /// متابعة موضوع
  Future<void> followTopic(String topicId, String userId) async {
    await _repository.followTopic(topicId, userId);
  }

  /// إلغاء متابعة موضوع
  Future<void> unfollowTopic(String topicId, String userId) async {
    await _repository.unfollowTopic(topicId, userId);
  }

  /// الحصول على المواضيع المتابعة
  Future<List<TopicModel>> getFollowedTopics(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    return await _repository.getFollowedTopics(
      userId,
      limit: limit,
      startAfter: startAfter);
  }

  /// التحقق مما إذا كان المستخدم يتابع موضوعاً
  Future<bool> isFollowingTopic(String topicId, String userId) async {
    return await _repository.isFollowingTopic(topicId, userId);
  }

  // الإشارات المرجعية

  /// إضافة إشارة مرجعية إلى موضوع
  Future<void> bookmarkTopic(String topicId, String userId) async {
    await _repository.bookmarkTopic(topicId, userId);
  }

  /// إزالة إشارة مرجعية من موضوع
  Future<void> unbookmarkTopic(String topicId, String userId) async {
    await _repository.unbookmarkTopic(topicId, userId);
  }

  /// الحصول على المواضيع المحفوظة
  Future<List<TopicModel>> getBookmarkedTopics(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    return await _repository.getBookmarkedTopics(
      userId,
      limit: limit,
      startAfter: startAfter);
  }

  /// التحقق مما إذا كان الموضوع محفوظاً
  Future<bool> isTopicBookmarked(String topicId, String userId) async {
    return await _repository.isTopicBookmarked(topicId, userId);
  }

  // مشاركة الموضوع

  /// مشاركة موضوع
  Future<void> shareTopic(String topicId, String userId) async {
    await _repository.shareTopic(topicId, userId);
  }

  // إحصائيات اللوبي

  /// الحصول على إحصائيات اللوبي
  Future<Map<String, dynamic>> getForumStatistics() async {
    return await _repository.getForumStatistics();
  }

  // استطلاعات الرأي

  /// إنشاء استطلاع رأي
  Future<String?> createPoll(String topicId, Map<String, dynamic> pollData) async {
    return await _repository.createPoll(topicId, pollData);
  }

  /// تحديث استطلاع رأي
  Future<void> updatePoll(String topicId, String pollId, Map<String, dynamic> pollData) async {
    await _repository.updatePoll(topicId, pollId, pollData);
  }

  /// حذف استطلاع رأي
  Future<void> deletePoll(String topicId, String pollId) async {
    await _repository.deletePoll(topicId, pollId);
  }

  /// الحصول على استطلاع رأي بواسطة المعرف
  Future<Map<String, dynamic>?> getPollById(String topicId, String pollId) async {
    return await _repository.getPollById(topicId, pollId);
  }

  /// التصويت على استطلاع رأي
  Future<void> voteOnPoll(String topicId, String pollId, List<String> optionIds, String userId) async {
    await _repository.voteOnPoll(topicId, pollId, optionIds, userId);
  }

  /// إلغاء التصويت على استطلاع رأي
  Future<void> unvoteOnPoll(String topicId, String pollId, String userId) async {
    await _repository.unvoteOnPoll(topicId, pollId, userId);
  }

  // الاستماع للتغييرات

  /// الاستماع للتغييرات في فئات المنتدى
  Stream<List<CategoryModel>> listenToCategories() {
    return _repository.listenToCategories();
  }

  /// الاستماع للتغييرات في مواضيع فئة
  Stream<List<TopicModel>> listenToCategoryTopics(
    String categoryId, {
    int limit = 20,
    String? sortBy,
    bool descending = true,
  }) {
    return _repository.listenToCategoryTopics(
      categoryId,
      limit: limit,
      sortBy: sortBy,
      descending: descending);
  }

  /// الاستماع للتغييرات في موضوع
  Stream<TopicModel?> listenToTopic(String topicId) {
    return _repository.listenToTopic(topicId);
  }

  /// الاستماع للتغييرات في مشاركات موضوع
  Stream<List<PostModel>> listenToTopicPosts(
    String topicId, {
    int limit = 20,
    bool includeReplies = true,
  }) {
    return _repository.listenToTopicPosts(
      topicId,
      limit: limit,
      includeReplies: includeReplies);
  }

  /// الاستماع للتغييرات في مشاركة
  Stream<PostModel?> listenToPost(String postId) {
    return _repository.listenToPost(postId);
  }

  /// الاستماع للتغييرات في إشعارات مستخدم
  Stream<List<NotificationModel>> listenToUserNotifications(
    String userId, {
    int limit = 20,
    bool unreadOnly = false,
  }) {
    return _repository.listenToUserNotifications(
      userId,
      limit: limit,
      unreadOnly: unreadOnly);
  }

  /// الاستماع للتغييرات في عدد الإشعارات غير المقروءة
  Stream<int> listenToUnreadNotificationsCount(String userId) {
    return _repository.listenToUnreadNotificationsCount(userId);
  }

  /// الاستماع للتغييرات في إحصائيات مستخدم
  Stream<UserStatisticsModel?> listenToUserStatistics(String userId) {
    return _repository.listenToUserStatistics(userId);
  }

  // ===== دوال المكافآت والإنجازات =====

  /// منح نقاط للمستخدم
  Future<void> awardPoints(String userId, int points, String reason) async {
    await _rewardsService.awardPoints(userId, points, reason);
  }

  /// منح نقاط للأنشطة
  Future<void> awardPointsForActivity(String userId, String activity) async {
    await _rewardsService.awardPointsForActivity(userId, activity);
  }

  /// الحصول على إنجازات المستخدم
  Future<List<AchievementModel>> getUserAchievements(String userId) async {
    return await _rewardsService.getUserAchievements(userId);
  }

  /// الحصول على شارات المستخدم
  Future<List<BadgeModel>> getUserBadges(String userId) async {
    return await _rewardsService.getUserBadges(userId);
  }

  /// الحصول على سجل النقاط
  Future<List<Map<String, dynamic>>> getPointsHistory(String userId, {int limit = 20}) async {
    return await _rewardsService.getPointsHistory(userId, limit: limit);
  }

  // ===== دوال الاستطلاعات المحسنة =====

  /// إنشاء استطلاع رأي محسن
  Future<PollModel?> createEnhancedPoll({
    required String topicId,
    required String question,
    String? description,
    required List<String> options,
    required bool allowMultipleChoices,
    required String createdBy,
    DateTime? endDate,
  }) async {
    return await _pollService.createPoll(
      topicId: topicId,
      question: question,
      description: description,
      options: options,
      allowMultipleChoices: allowMultipleChoices,
      createdBy: createdBy,
      endDate: endDate,
    );
  }

  /// التصويت في استطلاع
  Future<bool> voteInPoll({
    required String pollId,
    required List<String> optionIds,
    required String userId,
  }) async {
    return await _pollService.voteInPoll(
      pollId: pollId,
      optionIds: optionIds,
      userId: userId,
    );
  }

  /// إلغاء التصويت
  Future<bool> removeVoteFromPoll({
    required String pollId,
    required String userId,
  }) async {
    return await _pollService.removeVote(
      pollId: pollId,
      userId: userId,
    );
  }

  /// الحصول على نتائج الاستطلاع
  Future<Map<String, dynamic>> getPollResults(String pollId) async {
    return await _pollService.getPollResults(pollId);
  }

  /// فحص إذا كان المستخدم صوت
  Future<bool> hasUserVotedInPoll(String pollId, String userId) async {
    return await _pollService.hasUserVoted(pollId, userId);
  }

  /// الحصول على الاستطلاعات النشطة
  Future<List<PollModel>> getActivePolls({int limit = 10}) async {
    return await _pollService.getActivePolls(limit: limit);
  }

  // ===== دوال الإشراف المتقدمة =====

  /// الحصول على جميع التقارير
  Future<List<ReportModel>> getAllReports({
    ReportStatus? status,
    ReportContentType? contentType,
    int limit = 50,
  }) async {
    return await _moderationService.getAllReports(
      status: status,
      contentType: contentType,
      limit: limit,
    );
  }

  /// معالجة تقرير
  Future<bool> handleReport({
    required String reportId,
    required String moderatorId,
    required ReportStatus newStatus,
    String? adminNotes,
    bool deleteContent = false,
    bool banUser = false,
    int banDurationDays = 0,
  }) async {
    return await _moderationService.handleReport(
      reportId: reportId,
      moderatorId: moderatorId,
      newStatus: newStatus,
      adminNotes: adminNotes,
      deleteContent: deleteContent,
      banUser: banUser,
      banDurationDays: banDurationDays,
    );
  }

  /// فحص إذا كان المستخدم محظور
  Future<bool> isUserBanned(String userId) async {
    return await _moderationService.isUserBanned(userId);
  }

  /// إلغاء حظر مستخدم
  Future<bool> unbanUser(String userId, String moderatorId) async {
    return await _moderationService.unbanUser(userId, moderatorId);
  }

  /// الحصول على إحصائيات الإشراف
  Future<Map<String, dynamic>> getModerationStats() async {
    return await _moderationService.getModerationStats();
  }


}
