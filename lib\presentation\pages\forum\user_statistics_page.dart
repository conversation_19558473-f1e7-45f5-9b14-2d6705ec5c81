import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/user_statistics_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/empty_view.dart';

/// صفحة إحصائيات المستخدم
class UserStatisticsPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/user-statistics';

  /// معرف المستخدم (اختياري، إذا كان فارغاً سيتم استخدام المستخدم الحالي)
  final String? userId;

  const UserStatisticsPage({super.key, this.userId});

  @override
  State<UserStatisticsPage> createState() => _UserStatisticsPageState();
}

class _UserStatisticsPageState extends State<UserStatisticsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // جلب البيانات عند تحميل الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // تحديد معرف المستخدم
    final userId = widget.userId ?? authProvider.user?.uid;

    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يجب تسجيل الدخول لعرض الإحصائيات')));
      Navigator.pop(context);
      return;
    }

    // جلب إحصائيات المستخدم
    await forumProvider.fetchUserStatistics(userId);
  }

  /// إعادة حساب الإحصائيات
  Future<void> _recalculateStatistics() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // تحديد معرف المستخدم
      final userId = widget.userId ?? authProvider.user?.uid;

      if (userId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('يجب تسجيل الدخول لإعادة حساب الإحصائيات')));
        return;
      }

      // إعادة حساب الإحصائيات
      final success = await forumProvider.recalculateUserStatistics(userId);

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم إعادة حساب الإحصائيات بنجاح')));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء إعادة حساب الإحصائيات')));
      }
    } finally {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إحصائيات المستخدم',
          style: TextStyle(
            fontWeight: FontWeight.bold)),
        elevation: 0,
        actions: [
          IconButton(
            icon: _isRefreshing
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.primary))
                : Icon(Icons.refresh),
            onPressed: _isRefreshing ? null : _recalculateStatistics,
            tooltip: 'إعادة حساب الإحصائيات'),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.primary,
          indicatorWeight: 3,
          labelStyle: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 15),
          unselectedLabelStyle: TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 15),
          tabs: [
            Tab(text: 'الملخص'),
            Tab(text: 'النشاط'),
            Tab(text: 'الإنجازات'),
          ])),
      body: Consumer<ForumProvider>(
        builder: (context, forumProvider, child) {
          if (forumProvider.userStatisticsState == LoadingState.loading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  LoadingIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'جاري تحميل الإحصائيات...',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16)),
                ]));
          } else if (forumProvider.userStatisticsState == LoadingState.error) {
            return ErrorView(
              message: 'حدث خطأ في تحميل الإحصائيات',
              onRetry: _loadData);
          } else if (forumProvider.userStatisticsState == LoadingState.empty ||
              forumProvider.userStatistics == null) {
            return EmptyView(
              message: 'لا توجد إحصائيات متاحة',
              icon: Icons.bar_chart);
          }

          final statistics = forumProvider.userStatistics!;

          return AnimationLimiter(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSummaryTab(statistics),
                _buildActivityTab(statistics),
                _buildAchievementsTab(statistics),
              ]));
        }));
  }

  /// بناء علامة تبويب الملخص
  Widget _buildSummaryTab(UserStatisticsModel statistics) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildUserHeader(statistics),
          SizedBox(height: 24),
          _buildStatisticsCard(
            title: 'ملخص النشاط',
            items: [
              StatItem(
                icon: Icons.forum,
                label: 'المواضيع',
                value: '${statistics.topicsCount}'),
              StatItem(
                icon: Icons.comment,
                label: 'المشاركات',
                value: '${statistics.postsCount}'),
              StatItem(
                icon: Icons.remove_red_eye,
                label: 'المشاهدات',
                value: '${statistics.totalTopicViews}'),
              StatItem(
                icon: Icons.favorite,
                label: 'الإعجابات',
                value:
                    '${statistics.totalTopicLikes + (statistics.totalPostLikes ?? 0)}'),
            ]),
          SizedBox(height: 16),
          _buildStatisticsCard(
            title: 'المواضيع المميزة',
            items: [
              StatItem(
                icon: Icons.star,
                label: 'المميزة',
                value: '${statistics.featuredTopicsCount ?? 0}'),
              StatItem(
                icon: Icons.push_pin,
                label: 'المثبتة',
                value: '${statistics.pinnedTopicsCount ?? 0}'),
              StatItem(
                icon: Icons.check_circle,
                label: 'المحلولة',
                value: '${statistics.solvedTopicsCount ?? 0}'),
              StatItem(
                icon: Icons.verified,
                label: 'أفضل إجابة',
                value: '${statistics.bestAnswersCount ?? 0}'),
            ]),
          SizedBox(height: 16),
          _buildLevelCard(statistics),
        ]));
  }

  /// بناء علامة تبويب النشاط - تصميم محسن
  Widget _buildActivityTab(UserStatisticsModel statistics) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة معدلات النشاط
          Card(
            elevation: 2,
            shadowColor: Colors.black.withAlpha(13),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16)),
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.trending_up,
                        color: AppColors.primary,
                        size: 24),
                      SizedBox(width: 10),
                      Text(
                        'معدلات النشاط',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87)),
                    ]),
                  SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: _buildActivityRateCard(
                          'يومي',
                          statistics.dailyActivityRate.toStringAsFixed(1),
                          Icons.today,
                          Colors.blue)),
                      SizedBox(width: 12),
                      Expanded(
                        child: _buildActivityRateCard(
                          'أسبوعي',
                          statistics.weeklyActivityRate.toStringAsFixed(1),
                          Icons.view_week,
                          Colors.green)),
                      SizedBox(width: 12),
                      Expanded(
                        child: _buildActivityRateCard(
                          'شهري',
                          statistics.monthlyActivityRate.toStringAsFixed(1),
                          Icons.calendar_month,
                          Colors.purple)),
                    ]),
                ]))),

          SizedBox(height: 16),

          // بطاقة متوسطات النشاط
          _buildStatisticsCard(
            title: 'متوسطات النشاط',
            items: [
              StatItem(
                icon: Icons.comment,
                label: 'المشاركات لكل موضوع',
                value: statistics.averagePostsPerTopic != null
                    ? statistics.averagePostsPerTopic.toStringAsFixed(1)
                    : '0'),
              StatItem(
                icon: Icons.remove_red_eye,
                label: 'المشاهدات لكل موضوع',
                value: statistics.averageViewsPerTopic != null
                    ? statistics.averageViewsPerTopic.toStringAsFixed(1)
                    : '0'),
              StatItem(
                icon: Icons.favorite,
                label: 'الإعجابات لكل موضوع',
                value: statistics.averageLikesPerTopic != null
                    ? statistics.averageLikesPerTopic.toStringAsFixed(1)
                    : '0'),
            ]),

          SizedBox(height: 16),

          // مخطط النشاط الزمني
          _buildActivityTimeline(statistics),
        ]));
  }

  /// بناء بطاقة معدل النشاط
  Widget _buildActivityRateCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withAlpha(50),
          width: 1)),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24),
          SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87)),
          SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600)),
        ]));
  }

  /// بناء علامة تبويب الإنجازات
  Widget _buildAchievementsTab(UserStatisticsModel statistics) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBadgesGrid(statistics),
          SizedBox(height: 16),
          _buildPointsCard(statistics),
        ]));
  }

  /// بناء رأس المستخدم
  Widget _buildUserHeader(UserStatisticsModel statistics) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black.withAlpha(13),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppColors.primary.withAlpha(25),
              Colors.white,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight)),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 10,
                    offset: Offset(0, 5)),
                ]),
              child: CircleAvatar(
                radius: 45,
                backgroundColor: AppColors.primary.withAlpha(25),
                backgroundImage: statistics.userImage != null
                    ? NetworkImage(statistics.userImage!)
                    : null,
                child: statistics.userImage == null
                    ? Icon(
                        Icons.person,
                        size: 40,
                        color: AppColors.primary)
                    : null)),
            SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statistics.userName,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87)),
                  SizedBox(height: 6),
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: Colors.grey.shade600),
                      SizedBox(width: 6),
                      Text(
                        'عضو منذ ${_formatDate(statistics.joinDate)}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14)),
                    ]),
                  SizedBox(height: 12),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getLevelColor(statistics.level).withAlpha(30),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: _getLevelColor(statistics.level).withAlpha(100),
                        width: 1)),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.military_tech,
                          color: _getLevelColor(statistics.level),
                          size: 18),
                        SizedBox(width: 6),
                        Text(
                          statistics.level,
                          style: TextStyle(
                            color: _getLevelColor(statistics.level),
                            fontWeight: FontWeight.bold,
                            fontSize: 14)),
                      ])),
                ])),
          ])));
  }

  /// بناء بطاقة إحصائيات
  Widget _buildStatisticsCard({
    required String title,
    required List<StatItem> items,
  }) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black.withAlpha(13),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getTitleIcon(title),
                  color: AppColors.primary,
                  size: 24),
                SizedBox(width: 10),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87)),
              ]),
            SizedBox(height: 20),
            AnimationLimiter(
              child: GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                childAspectRatio: 2.2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                children: List.generate(
                  items.length,
                  (index) => AnimationConfiguration.staggeredGrid(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    columnCount: 2,
                    child: ScaleAnimation(
                      child: FadeInAnimation(
                        child: _buildStatItem(items[index]))))))),
          ])));
  }

  /// الحصول على أيقونة العنوان
  IconData _getTitleIcon(String title) {
    if (title.contains('ملخص')) {
      return Icons.summarize;
    } else if (title.contains('متوسطات')) {
      return Icons.trending_up;
    } else if (title.contains('المميزة')) {
      return Icons.star;
    } else {
      return Icons.bar_chart;
    }
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(StatItem item) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: Offset(0, 2)),
        ],
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1)),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getIconColor(item.icon).withAlpha(20),
              shape: BoxShape.circle),
            child: Icon(
              item.icon,
              color: _getIconColor(item.icon),
              size: 18)),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  item.label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
                SizedBox(height: 2),
                Text(
                  item.value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87)),
              ])),
        ]));
  }

  /// الحصول على لون الأيقونة
  Color _getIconColor(IconData icon) {
    if (icon == Icons.forum) {
      return AppColors.primary;
    } else if (icon == Icons.comment) {
      return AppColors.primary; // تغيير من الأزرق إلى الأخضر
    } else if (icon == Icons.remove_red_eye) {
      return AppColors.primaryLight; // تغيير إلى درجة أفتح من الأخضر
    } else if (icon == Icons.favorite) {
      return Colors.red;
    } else if (icon == Icons.star) {
      return Colors.amber;
    } else if (icon == Icons.push_pin) {
      return Colors.orange;
    } else if (icon == Icons.check_circle) {
      return AppColors.success; // استخدام لون النجاح الأخضر
    } else if (icon == Icons.verified) {
      return AppColors.primaryDark; // تغيير إلى درجة أغمق من الأخضر
    } else {
      return AppColors.primary;
    }
  }

  /// بناء بطاقة المستوى
  Widget _buildLevelCard(UserStatisticsModel statistics) {
    // حساب النسبة المئوية للمستوى التالي
    double progressPercentage = 0.0;
    String nextLevel = '';
    int currentLevelPoints = 0;
    int nextLevelPoints = 0;

    switch (statistics.level) {
      case 'مبتدئ':
        currentLevelPoints = 0;
        nextLevelPoints = 100;
        nextLevel = 'نشط';
        break;
      case 'نشط':
        currentLevelPoints = 100;
        nextLevelPoints = 500;
        nextLevel = 'متميز';
        break;
      case 'متميز':
        currentLevelPoints = 500;
        nextLevelPoints = 1000;
        nextLevel = 'محترف';
        break;
      case 'محترف':
        currentLevelPoints = 1000;
        nextLevelPoints = 2000;
        nextLevel = 'خبير';
        break;
      case 'خبير':
        currentLevelPoints = 2000;
        nextLevelPoints = 5000;
        nextLevel = 'أسطورة';
        break;
      case 'أسطورة':
        currentLevelPoints = 5000;
        nextLevelPoints = 5000;
        nextLevel = 'أسطورة';
        progressPercentage = 1.0;
        break;
    }

    if (statistics.level != 'أسطورة') {
      progressPercentage = (statistics.points - currentLevelPoints) /
          (nextLevelPoints - currentLevelPoints);
    }

    return Card(
      elevation: 2,
      shadowColor: Colors.black.withAlpha(13),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              _getLevelColor(statistics.level).withAlpha(15),
              Colors.white,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.leaderboard,
                  color: _getLevelColor(statistics.level),
                  size: 24),
                SizedBox(width: 10),
                Text(
                  'المستوى والنقاط',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87)),
              ]),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: _getLevelColor(statistics.level).withAlpha(30),
                        shape: BoxShape.circle),
                      child: Icon(
                        _getLevelIcon(statistics.level),
                        color: _getLevelColor(statistics.level),
                        size: 24)),
                    SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'المستوى الحالي',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600)),
                        Text(
                          statistics.level,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: _getLevelColor(statistics.level))),
                      ]),
                  ]),
                if (statistics.level != 'أسطورة')
                  Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'المستوى التالي',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600)),
                          Text(
                            nextLevel,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: _getLevelColor(nextLevel))),
                        ]),
                      SizedBox(width: 12),
                      Container(
                        padding: EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: _getLevelColor(nextLevel).withAlpha(30),
                          shape: BoxShape.circle),
                        child: Icon(
                          _getLevelIcon(nextLevel),
                          color: _getLevelColor(nextLevel),
                          size: 24)),
                    ]),
              ]),
            SizedBox(height: 20),
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Stack(
                children: [
                  Container(
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(10))),
                  FractionallySizedBox(
                    widthFactor: progressPercentage,
                    child: Container(
                      height: 16,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            _getLevelColor(statistics.level),
                            _getLevelColor(statistics.level).withAlpha(200),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight),
                        borderRadius: BorderRadius.circular(10)))),
                ])),
            SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 16),
                    SizedBox(width: 4),
                    Text(
                      '${statistics.points} نقطة',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14)),
                  ]),
                if (statistics.level != 'أسطورة')
                  Text(
                    'متبقي ${nextLevelPoints - statistics.points} نقطة للمستوى التالي',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12)),
              ]),
          ])));
  }

  /// الحصول على أيقونة المستوى
  IconData _getLevelIcon(String level) {
    switch (level) {
      case 'مبتدئ':
        return Icons.emoji_events_outlined;
      case 'نشط':
        return Icons.emoji_events;
      case 'متميز':
        return Icons.workspace_premium_outlined;
      case 'محترف':
        return Icons.workspace_premium;
      case 'خبير':
        return Icons.military_tech_outlined;
      case 'أسطورة':
        return Icons.military_tech;
      default:
        return Icons.emoji_events_outlined;
    }
  }

  /// بناء مخطط زمني للنشاط - تصميم محسن
  Widget _buildActivityTimeline(UserStatisticsModel statistics) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black.withAlpha(13),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: AppColors.primary,
                  size: 24),
                SizedBox(width: 10),
                Text(
                  'النشاط الأخير',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87)),
              ]),
            SizedBox(height: 20),

            // آخر نشاط
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withAlpha(20),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.blue.withAlpha(50),
                  width: 1)),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(10),
                          blurRadius: 4,
                          offset: Offset(0, 2)),
                      ]),
                    child: Icon(
                      Icons.access_time,
                      color: Colors.blue,
                      size: 24)),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'آخر نشاط',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600)),
                        SizedBox(height: 4),
                        Text(
                          'منذ ${_formatDate(statistics.lastActivityDate)}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87)),
                      ])),
                ])),

            SizedBox(height: 16),

            // تاريخ الانضمام
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withAlpha(20),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.green.withAlpha(50),
                  width: 1)),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(10),
                          blurRadius: 4,
                          offset: Offset(0, 2)),
                      ]),
                    child: Icon(
                      Icons.calendar_today,
                      color: Colors.green,
                      size: 24)),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'تاريخ الانضمام',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600)),
                        SizedBox(height: 4),
                        Text(
                          _formatFullDate(statistics.joinDate),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87)),
                      ])),
                ])),
          ])));
  }

  /// تنسيق التاريخ الكامل
  String _formatFullDate(DateTime date) {
    final months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  /// بناء شبكة الشارات
  Widget _buildBadgesGrid(UserStatisticsModel statistics) {
    if (statistics.badges.isEmpty) {
      return Card(
        elevation: 2,
        shadowColor: Colors.black.withAlpha(13),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.emoji_events,
                    color: AppColors.primary,
                    size: 24),
                  SizedBox(width: 10),
                  Text(
                    'الشارات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87)),
                ]),
              SizedBox(height: 30),
              Center(
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        shape: BoxShape.circle),
                      child: Icon(
                        Icons.emoji_events_outlined,
                        size: 60,
                        color: Colors.grey.shade400)),
                    SizedBox(height: 16),
                    Text(
                      'لم يتم الحصول على أي شارات بعد',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 16)),
                    SizedBox(height: 8),
                    Text(
                      'استمر في المشاركة للحصول على شارات',
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 14)),
                  ])),
            ])));
    }

    return Card(
      elevation: 2,
      shadowColor: Colors.black.withAlpha(13),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.emoji_events,
                  color: AppColors.primary,
                  size: 24),
                SizedBox(width: 10),
                Text(
                  'الشارات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87)),
              ]),
            SizedBox(height: 20),
            AnimationLimiter(
              child: GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 0.9,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12),
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: statistics.badges.length,
                itemBuilder: (context, index) {
                  return AnimationConfiguration.staggeredGrid(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    columnCount: 3,
                    child: ScaleAnimation(
                      child: FadeInAnimation(
                        child: _buildBadgeItem(statistics.badges[index]))));
                })),
          ])));
  }

  /// بناء عنصر شارة
  Widget _buildBadgeItem(String badge) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: Offset(0, 2)),
        ],
        border: Border.all(
          color: _getBadgeColor(badge).withAlpha(50),
          width: 1.5)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _getBadgeColor(badge).withAlpha(20),
              shape: BoxShape.circle),
            child: Icon(
              _getBadgeIcon(badge),
              color: _getBadgeColor(badge),
              size: 32)),
          SizedBox(height: 10),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              badge,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.black87),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis)),
        ]));
  }

  /// بناء بطاقة النقاط
  Widget _buildPointsCard(UserStatisticsModel statistics) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black.withAlpha(13),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.stars,
                  color: AppColors.primary,
                  size: 24),
                SizedBox(width: 10),
                Text(
                  'النقاط',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87)),
              ]),
            SizedBox(height: 20),
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.amber.shade300,
                    Colors.amber.shade100,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.amber.withAlpha(50),
                    blurRadius: 8,
                    offset: Offset(0, 2)),
                ]),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(150),
                      shape: BoxShape.circle),
                    child: Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 32)),
                  SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إجمالي النقاط',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87)),
                      Text(
                        '${statistics.points}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                          color: Colors.black87)),
                    ]),
                ])),
            SizedBox(height: 16),
            AnimationLimiter(
              child: Column(
                children: List.generate(
                  3,
                  (index) => AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: _buildPointsListItem(
                          index: index,
                          statistics: statistics))))))),
          ])));
  }

  /// بناء عنصر قائمة النقاط
  Widget _buildPointsListItem({
    required int index,
    required UserStatisticsModel statistics,
  }) {
    IconData icon;
    Color color;
    String title;
    String subtitle;
    String points;

    switch (index) {
      case 0:
        icon = Icons.forum;
        color = AppColors.primary;
        title = 'نقاط المواضيع';
        subtitle = '10 نقاط لكل موضوع';
        points = '${statistics.topicsCount * 10}';
        break;
      case 1:
        icon = Icons.comment;
        color = Colors.blue;
        title = 'نقاط المشاركات';
        subtitle = '5 نقاط لكل مشاركة';
        points = '${statistics.postsCount * 5}';
        break;
      case 2:
        icon = Icons.favorite;
        color = Colors.red;
        title = 'نقاط الإعجابات';
        subtitle = '2 نقطة لكل إعجاب بموضوع، 1 نقطة لكل إعجاب بمشاركة';
        points = '${statistics.totalTopicLikes * 2 + statistics.totalPostLikes}';
        break;
      default:
        icon = Icons.star;
        color = Colors.amber;
        title = 'نقاط أخرى';
        subtitle = '';
        points = '0';
    }

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 8,
            offset: Offset(0, 2)),
        ],
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1)),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withAlpha(20),
              shape: BoxShape.circle),
            child: Icon(
              icon,
              color: color,
              size: 24)),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: Colors.black87)),
                SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600)),
              ])),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: color.withAlpha(20),
              borderRadius: BorderRadius.circular(20)),
            child: Text(
              points,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: 14))),
        ]));
  }

  /// الحصول على لون المستوى
  Color _getLevelColor(String level) {
    switch (level) {
      case 'مبتدئ':
        return Colors.green;
      case 'نشط':
        return Colors.green.shade600; // تغيير من الأزرق إلى الأخضر
      case 'متميز':
        return Colors.purple;
      case 'محترف':
        return Colors.orange;
      case 'خبير':
        return Colors.red;
      case 'أسطورة':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة الشارة
  IconData _getBadgeIcon(String badge) {
    if (badge.contains('كاتب')) {
      return Icons.edit;
    } else if (badge.contains('مشارك')) {
      return Icons.comment;
    } else if (badge.contains('محبوب') || badge.contains('مشهور')) {
      return Icons.favorite;
    } else if (badge.contains('مساعد') || badge.contains('خبير')) {
      return Icons.lightbulb;
    } else if (badge.contains('مميز')) {
      return Icons.star;
    } else if (badge.contains('حلال المشاكل')) {
      return Icons.check_circle;
    } else {
      return Icons.emoji_events;
    }
  }

  /// الحصول على لون الشارة
  Color _getBadgeColor(String badge) {
    if (badge.contains('متميز')) {
      return Colors.purple;
    } else if (badge.contains('محترف')) {
      return Colors.orange;
    } else if (badge.contains('محبوب')) {
      return Colors.red;
    } else if (badge.contains('مشهور')) {
      return Colors.pink;
    } else if (badge.contains('خبير')) {
      return Colors.red;
    } else if (badge.contains('مميز')) {
      return Colors.amber;
    } else if (badge.contains('حلال المشاكل')) {
      return Colors.green;
    } else {
      return AppColors.primary;
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} سنة';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} شهر';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

/// عنصر إحصائية
class StatItem {
  final IconData icon;
  final String label;
  final String value;

  StatItem({
    required this.icon,
    required this.label,
    required this.value,
  });
}
