import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

import '../../domain/models/forum/user_statistics_model.dart';

/// أنواع نشاطات المنتدى
enum ForumActivityType {
  /// إنشاء موضوع
  createTopic,

  /// إنشاء مشاركة
  createPost,

  /// الإعجاب بموضوع
  likeTopic,

  /// الإعجاب بمشاركة
  likePost,

  /// إضافة تفاعل إلى موضوع
  addTopicReaction,

  /// إضافة تفاعل إلى مشاركة
  addPostReaction,

  /// مشاهدة موضوع
  viewTopic,

  /// متابعة موضوع
  followTopic,

  /// إضافة إشارة مرجعية
  bookmarkTopic,

  /// مشاركة موضوع
  shareTopic,

  /// تسجيل الدخول اليومي
  dailyLogin,

  /// إنشاء استطلاع رأي
  createPoll,

  /// التصويت على استطلاع رأي
  voteOnPoll,
}

/// خدمة تتبع نشاطات المنتدى
class ForumActivityTracker {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  ForumActivityTracker({
    required FirebaseFirestore firestore,
    required FirebaseAuth auth,
  })  : _firestore = firestore,
        _auth = auth;

  /// تسجيل نشاط في المنتدى
  Future<void> trackActivity(
    ForumActivityType activityType, {
    String? itemId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final userId = user.uid;
      final now = DateTime.now();

      // إنشاء وثيقة النشاط
      await _firestore.collection('forum_user_activities').add({
        'userId': userId,
        'activityType': activityType.index,
        'itemId': itemId,
        'additionalData': additionalData,
        'timestamp': Timestamp.fromDate(now),
      });

      // تحديث آخر نشاط للمستخدم
      await _firestore.collection('forum_user_statistics').doc(userId).update({
        'lastActivityDate': Timestamp.fromDate(now),
      });

      // تحديث معدلات النشاط
      await _updateActivityRates(userId, now);

      // تحديث النقاط حسب نوع النشاط
      await _updatePointsForActivity(userId, activityType);
    } catch (e) {
      debugPrint('خطأ في تسجيل نشاط المنتدى: $e');
    }
  }

  /// تحديث معدلات النشاط
  Future<void> _updateActivityRates(String userId, DateTime now) async {
    try {
      // حساب عدد الأنشطة في اليوم الحالي
      final startOfDay = DateTime(now.year, now.month, now.day);
      final dailyActivitiesSnapshot = await _firestore
          .collection('forum_user_activities')
          .where('userId', isEqualTo: userId)
          .where('timestamp',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .count()
          .get();

      // حساب عدد الأنشطة في الأسبوع الحالي
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      final weeklyActivitiesSnapshot = await _firestore
          .collection('forum_user_activities')
          .where('userId', isEqualTo: userId)
          .where('timestamp',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfWeek))
          .count()
          .get();

      // حساب عدد الأنشطة في الشهر الحالي
      final startOfMonth = DateTime(now.year, now.month, 1);
      final monthlyActivitiesSnapshot = await _firestore
          .collection('forum_user_activities')
          .where('userId', isEqualTo: userId)
          .where('timestamp',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .count()
          .get();

      // تحديث معدلات النشاط
      await _firestore.collection('forum_user_statistics').doc(userId).update({
        'dailyActivityRate': (dailyActivitiesSnapshot.count ?? 0) / 1,
        'weeklyActivityRate': (weeklyActivitiesSnapshot.count ?? 0) / 7,
        'monthlyActivityRate': (monthlyActivitiesSnapshot.count ?? 0) /
            DateTime(now.year, now.month + 1, 0).day,
      });
    } catch (e) {
      debugPrint('خطأ في تحديث معدلات النشاط: $e');
    }
  }

  /// تحديث النقاط حسب نوع النشاط
  Future<void> _updatePointsForActivity(
      String userId, ForumActivityType activityType) async {
    try {
      int pointsToAdd = 0;

      // تحديد عدد النقاط حسب نوع النشاط
      switch (activityType) {
        case ForumActivityType.createTopic:
          pointsToAdd = 10;
          break;
        case ForumActivityType.createPost:
          pointsToAdd = 5;
          break;
        case ForumActivityType.likeTopic:
        case ForumActivityType.likePost:
          pointsToAdd = 1;
          break;
        case ForumActivityType.addTopicReaction:
        case ForumActivityType.addPostReaction:
          pointsToAdd = 1;
          break;
        case ForumActivityType.viewTopic:
          pointsToAdd = 0;
          break;
        case ForumActivityType.followTopic:
          pointsToAdd = 2;
          break;
        case ForumActivityType.bookmarkTopic:
          pointsToAdd = 2;
          break;
        case ForumActivityType.shareTopic:
          pointsToAdd = 3;
          break;
        case ForumActivityType.dailyLogin:
          pointsToAdd = 5;
          break;
        case ForumActivityType.createPoll:
          pointsToAdd = 8;
          break;
        case ForumActivityType.voteOnPoll:
          pointsToAdd = 2;
          break;
      }

      if (pointsToAdd > 0) {
        // تحديث النقاط
        await _firestore.collection('forum_user_statistics').doc(userId).update({
          'points': FieldValue.increment(pointsToAdd),
        });

        // تحديث المستوى والشارات
        await _updateLevelAndBadges(userId);
      }
    } catch (e) {
      debugPrint('خطأ في تحديث النقاط: $e');
    }
  }

  /// تحديث المستوى والشارات
  Future<void> _updateLevelAndBadges(String userId) async {
    try {
      final statsDoc = await _firestore
          .collection('forum_user_statistics')
          .doc(userId)
          .get();

      if (!statsDoc.exists) return;

      final stats = UserStatisticsModel.fromFirestore(statsDoc);
      final points = stats.points;

      // تحديد المستوى الجديد
      String newLevel;
      if (points < 100) {
        newLevel = 'مبتدئ';
      } else if (points < 500) {
        newLevel = 'نشط';
      } else if (points < 1000) {
        newLevel = 'متميز';
      } else if (points < 2000) {
        newLevel = 'محترف';
      } else if (points < 5000) {
        newLevel = 'خبير';
      } else {
        newLevel = 'أسطورة';
      }

      // تحديث المستوى إذا تغير
      if (newLevel != stats.level) {
        await _firestore
            .collection('forum_user_statistics')
            .doc(userId)
            .update({
          'level': newLevel,
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحديث المستوى والشارات: $e');
    }
  }
}
