import 'package:cloud_firestore/cloud_firestore.dart';

/// نوع التنبؤ
enum PredictionType {
  /// تنبؤ بأسعار البيع
  salePrices,
  
  /// تنبؤ بأسعار الإيجار
  rentalPrices,
  
  /// تنبؤ بحجم المعروض
  supply,
  
  /// تنبؤ بحجم الطلب
  demand,
  
  /// تنبؤ بعدد الصفقات
  transactions,
  
  /// تنبؤ بالعائد الاستثماري
  investmentReturn,
  
  /// تنبؤ بمعدل النمو
  growthRate,
  
  /// تنبؤ بمعدل الإشغال
  occupancyRate,
}

/// فترة التنبؤ
enum PredictionPeriod {
  /// شهر
  month,
  
  /// ثلاثة أشهر
  threeMonths,
  
  /// ستة أشهر
  sixMonths,
  
  /// سنة
  year,
  
  /// سنتين
  twoYears,
  
  /// خمس سنوات
  fiveYears,
}

/// مستوى ثقة التنبؤ
enum ConfidenceLevel {
  /// منخفض
  low,
  
  /// متوسط
  medium,
  
  /// عالي
  high,
  
  /// عالي جداً
  veryHigh,
}

/// اتجاه التنبؤ
enum PredictionTrend {
  /// صعود
  up,
  
  /// هبوط
  down,
  
  /// ثبات
  stable,
  
  /// متذبذب
  volatile,
}

/// كيان تنبؤ السوق
class MarketPrediction {
  /// معرف التنبؤ
  final String id;
  
  /// عنوان التنبؤ
  final String title;
  
  /// وصف التنبؤ
  final String description;
  
  /// نوع التنبؤ
  final PredictionType type;
  
  /// فترة التنبؤ
  final PredictionPeriod period;
  
  /// تاريخ بداية التنبؤ
  final DateTime startDate;
  
  /// تاريخ نهاية التنبؤ
  final DateTime endDate;
  
  /// المنطقة الجغرافية للتنبؤ
  final String? area;
  
  /// نوع العقار للتنبؤ
  final String? propertyType;
  
  /// القيمة الحالية
  final double currentValue;
  
  /// القيمة المتوقعة
  final double predictedValue;
  
  /// الحد الأدنى للقيمة المتوقعة
  final double minValue;
  
  /// الحد الأقصى للقيمة المتوقعة
  final double maxValue;
  
  /// نسبة التغيير المتوقعة
  final double changePercentage;
  
  /// مستوى الثقة
  final ConfidenceLevel confidenceLevel;
  
  /// اتجاه التنبؤ
  final PredictionTrend trend;
  
  /// العوامل المؤثرة في التنبؤ
  final Map<String, double> factors;
  
  /// بيانات التنبؤ
  final Map<String, dynamic> data;
  
  /// الاستنتاجات
  final List<String> insights;
  
  /// التوصيات
  final List<String> recommendations;
  
  /// تاريخ إنشاء التنبؤ
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث للتنبؤ
  final DateTime updatedAt;
  
  /// معرف منشئ التنبؤ
  final String createdBy;
  
  /// بيانات إضافية
  final Map<String, dynamic>? metadata;
  
  /// إنشاء كيان تنبؤ السوق
  MarketPrediction({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.period,
    required this.startDate,
    required this.endDate,
    this.area,
    this.propertyType,
    required this.currentValue,
    required this.predictedValue,
    required this.minValue,
    required this.maxValue,
    required this.changePercentage,
    required this.confidenceLevel,
    required this.trend,
    required this.factors,
    required this.data,
    required this.insights,
    required this.recommendations,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.metadata,
  });
  
  /// إنشاء كيان تنبؤ السوق من JSON
  factory MarketPrediction.fromJson(Map<String, dynamic> json) {
    return MarketPrediction(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: PredictionType.values.firstWhere(
        (e) => e.toString() == 'PredictionType.${json['type']}',
        orElse: () => PredictionType.salePrices),
      period: PredictionPeriod.values.firstWhere(
        (e) => e.toString() == 'PredictionPeriod.${json['period']}',
        orElse: () => PredictionPeriod.year),
      startDate: (json['startDate'] as Timestamp).toDate(),
      endDate: (json['endDate'] as Timestamp).toDate(),
      area: json['area'] as String?,
      propertyType: json['propertyType'] as String?,
      currentValue: (json['currentValue'] as num).toDouble(),
      predictedValue: (json['predictedValue'] as num).toDouble(),
      minValue: (json['minValue'] as num).toDouble(),
      maxValue: (json['maxValue'] as num).toDouble(),
      changePercentage: (json['changePercentage'] as num).toDouble(),
      confidenceLevel: ConfidenceLevel.values.firstWhere(
        (e) => e.toString() == 'ConfidenceLevel.${json['confidenceLevel']}',
        orElse: () => ConfidenceLevel.medium),
      trend: PredictionTrend.values.firstWhere(
        (e) => e.toString() == 'PredictionTrend.${json['trend']}',
        orElse: () => PredictionTrend.stable),
      factors: Map<String, double>.from(json['factors'] ?? {}),
      data: json['data'] as Map<String, dynamic>,
      insights: List<String>.from(json['insights'] ?? []),
      recommendations: List<String>.from(json['recommendations'] ?? []),
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: (json['updatedAt'] as Timestamp).toDate(),
      createdBy: json['createdBy'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?);
  }
  
  /// تحويل كيان تنبؤ السوق إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'period': period.toString().split('.').last,
      'startDate': startDate,
      'endDate': endDate,
      'area': area,
      'propertyType': propertyType,
      'currentValue': currentValue,
      'predictedValue': predictedValue,
      'minValue': minValue,
      'maxValue': maxValue,
      'changePercentage': changePercentage,
      'confidenceLevel': confidenceLevel.toString().split('.').last,
      'trend': trend.toString().split('.').last,
      'factors': factors,
      'data': data,
      'insights': insights,
      'recommendations': recommendations,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'createdBy': createdBy,
      'metadata': metadata,
    };
  }
  
  /// نسخ كيان تنبؤ السوق مع تعديل بعض الخصائص
  MarketPrediction copyWith({
    String? id,
    String? title,
    String? description,
    PredictionType? type,
    PredictionPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    String? area,
    String? propertyType,
    double? currentValue,
    double? predictedValue,
    double? minValue,
    double? maxValue,
    double? changePercentage,
    ConfidenceLevel? confidenceLevel,
    PredictionTrend? trend,
    Map<String, double>? factors,
    Map<String, dynamic>? data,
    List<String>? insights,
    List<String>? recommendations,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    Map<String, dynamic>? metadata,
  }) {
    return MarketPrediction(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      area: area ?? this.area,
      propertyType: propertyType ?? this.propertyType,
      currentValue: currentValue ?? this.currentValue,
      predictedValue: predictedValue ?? this.predictedValue,
      minValue: minValue ?? this.minValue,
      maxValue: maxValue ?? this.maxValue,
      changePercentage: changePercentage ?? this.changePercentage,
      confidenceLevel: confidenceLevel ?? this.confidenceLevel,
      trend: trend ?? this.trend,
      factors: factors ?? this.factors,
      data: data ?? this.data,
      insights: insights ?? this.insights,
      recommendations: recommendations ?? this.recommendations,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      metadata: metadata ?? this.metadata);
  }
}
