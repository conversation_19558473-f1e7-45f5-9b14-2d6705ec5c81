import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import 'animated_forum_background.dart';

/// صفحة تجريبية لعرض الخلفية المتحركة
class ForumBackgroundDemo extends StatelessWidget {
  const ForumBackgroundDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('خلفية المنتدى المتحركة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white),
      body: Container(
        width: double.infinity,
        height: 300,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(24),
            bottomRight: Radius.circular(24)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5)),
          ]),
        clipBehavior: Clip.hardEdge,
        child: Stack(
          children: [
            // الخلفية المتحركة
            AnimatedForumBackground(
              primaryColor: AppColors.primary,
              secondaryColor: AppColors.primary.withOpacity(0.8)),
            
            // المحتوى التجريبي
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 40),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12)),
                        child: const Icon(
                          Icons.forum,
                          color: Colors.white,
                          size: 24)),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'Lobby كريا',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 22,
                            fontWeight: FontWeight.bold))),
                      IconButton(
                        onPressed: () {},
                        icon: const Icon(Icons.search, color: Colors.white)),
                      IconButton(
                        onPressed: () {},
                        icon: const Icon(Icons.notifications_outlined, color: Colors.white)),
                    ]),
                  const SizedBox(height: 16),
                  Text(
                    'مكان للنقاش وتبادل الآراء حول العقارات في Lobby كريا',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14)),
                ])),
          ])));
  }
}
