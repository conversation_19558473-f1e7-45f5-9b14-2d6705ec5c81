import '../entities/appointment.dart';

/// واجهة مستودع المواعيد
abstract class AppointmentRepository {
  /// إنشاء موعد جديد
  Future<String> createAppointment(Appointment appointment);
  
  /// تحديث موعد موجود
  Future<void> updateAppointment(Appointment appointment);
  
  /// حذف موعد
  Future<void> deleteAppointment(String appointmentId);
  
  /// الحصول على موعد بواسطة المعرف
  Future<Appointment?> getAppointmentById(String appointmentId);
  
  /// الحصول على مواعيد المستخدم
  Future<List<Appointment>> getUserAppointments(String userId);
  
  /// الحصول على مواعيد المستخدم بالتحميل المتدرج
  /// [userId] معرف المستخدم
  /// [limit] عدد المواعيد في كل صفحة
  /// [lastAppointmentId] معرف آخر موعد تم تحميله (للصفحات التالية)
  /// [status] حالة المواعيد المطلوبة
  /// [startDate] تاريخ البداية
  /// [endDate] تاريخ النهاية
  /// يعيد Map تحتوي على:
  /// - 'appointments': قائمة المواعيد
  /// - 'lastAppointmentId': معرف آخر موعد (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من المواعيد
  Future<Map<String, dynamic>> getUserAppointmentsPaginated({
    required String userId,
    int limit = 20,
    String? lastAppointmentId,
    AppointmentStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// الحصول على مواعيد العميل
  Future<List<Appointment>> getClientAppointments(String clientId);
  
  /// الحصول على مواعيد العقار
  Future<List<Appointment>> getEstateAppointments(String estateId);
  
  /// الحصول على مواعيد اليوم
  Future<List<Appointment>> getTodayAppointments(String userId);
  
  /// الحصول على المواعيد القادمة
  Future<List<Appointment>> getUpcomingAppointments(String userId);
  
  /// تأكيد موعد
  Future<void> confirmAppointment(String appointmentId, String updatedBy);
  
  /// إلغاء موعد
  Future<void> cancelAppointment(String appointmentId, String updatedBy, String reason);
  
  /// إكمال موعد
  Future<void> completeAppointment(String appointmentId, String updatedBy);
  
  /// تأجيل موعد
  Future<void> postponeAppointment(String appointmentId, String updatedBy, DateTime newDateTime);
  
  /// إعادة جدولة موعد
  Future<void> rescheduleAppointment(String appointmentId, String updatedBy, DateTime newDateTime);
  
  /// تأكيد موعد من قبل العميل
  Future<void> confirmAppointmentByClient(String appointmentId);
  
  /// إرسال تذكير للموعد
  Future<void> sendAppointmentReminder(String appointmentId);
  
  /// التحقق من تعارض المواعيد
  Future<bool> checkAppointmentConflict(String userId, DateTime dateTime, int duration);
  
  /// الحصول على المواعيد المتاحة
  Future<List<DateTime>> getAvailableAppointmentSlots(
    String userId, 
    DateTime date, 
    int duration);
  
  /// الحصول على إحصائيات المواعيد
  Future<Map<String, dynamic>> getAppointmentsStatistics(String userId);
  
  /// تصدير المواعيد إلى ملف
  Future<String> exportAppointmentsToFile(String userId);
  
  /// استيراد المواعيد من تقويم
  Future<int> importAppointmentsFromCalendar(String userId);
  
  /// مزامنة المواعيد مع التقويم
  Future<void> syncAppointmentsWithCalendar(String userId);
}
