import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/presentation/widgets/project_background_widget.dart';

/// صفحة إضافة عضو فريق جديد للمشروع
class AddTeamMemberPage extends StatefulWidget {
  final String projectId;

  const AddTeamMemberPage({super.key, required this.projectId});

  @override
  State<AddTeamMemberPage> createState() => _AddTeamMemberPageState();
}

class _AddTeamMemberPageState extends State<AddTeamMemberPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _salaryController = TextEditingController();
  final _commissionController = TextEditingController();
  final _targetController = TextEditingController();

  String _selectedRole = 'member';
  String _selectedStatus = 'active';
  bool _isLoading = false;
  DateTime? _joinDate;

  final Map<String, String> _roles = {
    'manager': 'مدير',
    'supervisor': 'مشرف',
    'salesAgent': 'وكيل مبيعات',
    'marketer': 'مسوق',
    'customerService': 'خدمة العملاء',
    'admin': 'مسؤول إداري',
    'engineer': 'مهندس',
    'architect': 'معماري',
    'contractor': 'مقاول',
    'worker': 'عامل',
    'consultant': 'استشاري',
    'member': 'عضو فريق',
  };

  final Map<String, String> _statuses = {
    'active': 'نشط',
    'inactive': 'غير نشط',
    'onLeave': 'في إجازة',
    'suspended': 'معلق',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'إضافة عضو فريق',
          style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white)),
      body: ProjectBackgroundWidget(
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBasicInfoSection(),
                const SizedBox(height: 24),
                _buildRoleAndStatusSection(),
                const SizedBox(height: 24),
                _buildSpecialtySection(),
                const SizedBox(height: 24),
                _buildJoinDateSection(),
                const SizedBox(height: 32),
                _buildActionButtons(),
              ])))));
  }

  Widget _buildBasicInfoSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'المعلومات الأساسية',
                  style: CairoTextStyles.headlineSmall),
              ]),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              style: CairoTextStyles.bodyLarge,
              decoration: InputDecoration(
                labelText: 'الاسم الكامل *',
                labelStyle: CairoTextStyles.inputLabel,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12)),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.primary, width: 2)),
                prefixIcon: Icon(Icons.person_outline, color: AppColors.primary)),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الاسم الكامل';
                }
                return null;
              }),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              style: CairoTextStyles.bodyLarge,
              keyboardType: TextInputType.emailAddress,
              decoration: InputDecoration(
                labelText: 'البريد الإلكتروني *',
                labelStyle: CairoTextStyles.inputLabel,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12)),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.primary, width: 2)),
                prefixIcon: Icon(Icons.email, color: AppColors.primary)),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال البريد الإلكتروني';
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                  return 'يرجى إدخال بريد إلكتروني صحيح';
                }
                return null;
              }),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              style: CairoTextStyles.bodyLarge,
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                labelText: 'رقم الهاتف *',
                labelStyle: CairoTextStyles.inputLabel,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12)),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.primary, width: 2)),
                prefixIcon: Icon(Icons.phone, color: AppColors.primary)),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                return null;
              }),
          ])));
  }

  Widget _buildRoleAndStatusSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.work, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'المنصب والحالة',
                  style: CairoTextStyles.headlineSmall),
              ]),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedRole,
                    style: CairoTextStyles.bodyLarge,
                    decoration: InputDecoration(
                      labelText: 'المنصب',
                      labelStyle: CairoTextStyles.inputLabel,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: AppColors.primary, width: 2)),
                      prefixIcon: Icon(Icons.badge, color: AppColors.primary)),
                    items: _roles.entries.map((entry) {
                      return DropdownMenuItem(
                        value: entry.key,
                        child: Text(entry.value, style: CairoTextStyles.bodyMedium));
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedRole = value!;
                      });
                    })),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedStatus,
                    style: CairoTextStyles.bodyLarge,
                    decoration: InputDecoration(
                      labelText: 'الحالة',
                      labelStyle: CairoTextStyles.inputLabel,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: AppColors.primary, width: 2)),
                      prefixIcon: Icon(Icons.flag, color: AppColors.primary)),
                    items: _statuses.entries.map((entry) {
                      return DropdownMenuItem(
                        value: entry.key,
                        child: Text(entry.value, style: CairoTextStyles.bodyMedium));
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedStatus = value!;
                      });
                    })),
              ]),
          ])));
  }

  Widget _buildSpecialtySection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.engineering, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'التخصص والخبرة',
                  style: CairoTextStyles.headlineSmall),
              ]),
            const SizedBox(height: 16),
            TextFormField(
              controller: _salaryController,
              style: CairoTextStyles.bodyLarge,
              decoration: InputDecoration(
                labelText: 'التخصص',
                labelStyle: CairoTextStyles.inputLabel,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12)),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.primary, width: 2)),
                prefixIcon: Icon(Icons.work, color: AppColors.primary))),
            const SizedBox(height: 16),
            TextFormField(
              controller: _commissionController,
              style: CairoTextStyles.bodyLarge,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'سنوات الخبرة',
                labelStyle: CairoTextStyles.inputLabel,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12)),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.primary, width: 2)),
                prefixIcon: Icon(Icons.timeline, color: AppColors.primary))),
            const SizedBox(height: 16),
            TextFormField(
              controller: _targetController,
              style: CairoTextStyles.bodyLarge,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'ملاحظات إضافية',
                labelStyle: CairoTextStyles.inputLabel,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12)),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.primary, width: 2)),
                prefixIcon: Icon(Icons.note_add, color: AppColors.primary))),
          ])));
  }

  Widget _buildJoinDateSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.date_range, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'تاريخ الانضمام',
                  style: CairoTextStyles.headlineSmall),
              ]),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectJoinDate(),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(12)),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today, color: AppColors.primary),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _joinDate != null
                            ? _formatDate(_joinDate!)
                            : 'اختر تاريخ الانضمام',
                        style: CairoTextStyles.custom(
                          fontSize: 16,
                          color: _joinDate != null
                              ? Colors.black87
                              : Colors.grey[500]))),
                  ]))),
          ])));
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveTeamMember,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12)),
              elevation: 2),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2))
                : Text(
                    'إضافة عضو الفريق',
                    style: CairoTextStyles.button))),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12))),
            child: Text(
              'إلغاء',
              style: CairoTextStyles.custom(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.primary)))),
      ]);
  }

  // Helper Methods
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _selectJoinDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _joinDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'));
    if (picked != null && picked != _joinDate) {
      setState(() {
        _joinDate = picked;
      });
    }
  }

  void _saveTeamMember() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final teamMemberData = {
        'companyId': currentUser.uid,
        'fullName': _nameController.text.trim(),
        'email': _emailController.text.trim(),
        'phoneNumber': _phoneController.text.trim(),
        'role': _selectedRole,
        'status': _selectedStatus,
        'joinDate': _joinDate != null ? Timestamp.fromDate(_joinDate!) : Timestamp.fromDate(DateTime.now()),
        'specialty': _salaryController.text.trim(),
        'experienceYears': _commissionController.text.isNotEmpty ? int.tryParse(_commissionController.text) : null,
        'notes': _targetController.text.trim(),
        'createdAt': FieldValue.serverTimestamp(),
        'createdBy': currentUser.uid,
        'totalSales': 0.0,
        'soldRentedCount': 0,
        'clientsCount': 0,
        'appointmentsCount': 0,
        'offersCount': 0,
        'averageRating': 0.0,
        'reviewsCount': 0,
        'isActive': _selectedStatus == 'active',
      };

      await FirebaseFirestore.instance
          .collection('team_members')
          .add(teamMemberData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إضافة عضو الفريق بنجاح',
              style: CairoTextStyles.custom(color: Colors.white)),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8))));
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ في إضافة عضو الفريق: ${e.toString()}',
              style: CairoTextStyles.custom(color: Colors.white)),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8))));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _salaryController.dispose();
    _commissionController.dispose();
    _targetController.dispose();
    super.dispose();
  }
}