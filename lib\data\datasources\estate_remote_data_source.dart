/// An abstract class that defines the contract for estate-related remote operations.
library;
// file: lib/data/datasources/estate_remote_data_source.dart

import 'dart:io';

import '../models/estate_model.dart';

abstract class EstateRemoteDataSource {
  /// جلب جميع العقارات دفعة واحدة (للتوافق مع الكود القديم)
  Future<List<EstateModel>> getAllEstates();

  /// جلب العقارات بالتحميل المتدرج
  /// [limit] عدد العقارات في كل صفحة
  /// [lastDocumentId] معرف آخر مستند تم تحميله (للصفحات التالية)
  /// [page] رقم الصفحة الحالية
  /// [pageSize] عدد العناصر في كل صفحة
  /// [sortBy] حقل الترتيب
  /// [sortAscending] ترتيب تصاعدي أم تنازلي
  /// [filters] فلاتر البحث
  /// [searchQuery] نص البحث
  Future<Map<String, dynamic>> getPaginatedEstates({
    int limit = 10,
    String? lastDocumentId,
    int page = 1,
    int pageSize = 10,
    String sortBy = "createdAt",
    bool sortAscending = false,
    Map<String, dynamic>? filters,
    String? searchQuery,
  });

  Future<void> createEstate(EstateModel model);
  Future<void> updateEstate(EstateModel model);
  Future<void> deleteEstate(String id);

  // واجهة جديدة لرفع الصور وإرجاع روابط التنزيل
  Future<List<String>> uploadImages(List<File> images);

  // واجهة للحصول على عقار بواسطة المعرف
  Future<EstateModel?> getEstateById(String id);
}

/// An implementation of [EstateRemoteDataSource] that uses Firebase Firestore.
// file: lib/data/datasources/estate_remote_data_source_impl.dart
