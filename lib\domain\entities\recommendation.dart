import 'package:cloud_firestore/cloud_firestore.dart';

/// نوع التوصية
enum RecommendationType {
  /// توصية عقار
  estate,

  /// توصية منطقة
  area,

  /// توصية وكيل عقاري
  agent,

  /// توصية مطور عقاري
  developer,

  /// توصية استثمار
  investment,

  /// توصية مقالة
  article,

  /// توصية منتدى
  forum,
}

/// مصدر التوصية
enum RecommendationSource {
  /// تفضيلات المستخدم
  userPreferences,

  /// سجل التصفح
  browsingHistory,

  /// سجل البحث
  searchHistory,

  /// المفضلة
  favorites,

  /// المقارنات
  comparisons,

  /// تفاعلات المستخدم
  userInteractions,

  /// تشابه المستخدمين
  similarUsers,

  /// اتجاهات السوق
  marketTrends,

  /// توصيات المحررين
  editorsPicks,
}

/// كيان التوصية
class Recommendation {
  /// معرف التوصية
  final String id;

  /// معرف المستخدم
  final String userId;

  /// نوع التوصية
  final RecommendationType type;

  /// مصدر التوصية
  final RecommendationSource source;

  /// معرف العنصر الموصى به
  final String itemId;

  /// عنوان العنصر الموصى به
  final String itemTitle;

  /// صورة العنصر الموصى به
  final String? itemImage;

  /// وصف العنصر الموصى به
  final String? itemDescription;

  /// سبب التوصية
  final String reason;

  /// درجة الملاءمة (من 0 إلى 1)
  final double relevanceScore;

  /// تاريخ إنشاء التوصية
  final DateTime createdAt;

  /// تاريخ آخر تحديث للتوصية
  final DateTime updatedAt;

  /// ما إذا كانت التوصية تم عرضها
  final bool isViewed;

  /// ما إذا كانت التوصية تم النقر عليها
  final bool isClicked;

  /// ما إذا كانت التوصية تم تجاهلها
  final bool isDismissed;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  /// إنشاء كيان التوصية
  Recommendation({
    required this.id,
    required this.userId,
    required this.type,
    required this.source,
    required this.itemId,
    required this.itemTitle,
    this.itemImage,
    this.itemDescription,
    required this.reason,
    required this.relevanceScore,
    required this.createdAt,
    required this.updatedAt,
    this.isViewed = false,
    this.isClicked = false,
    this.isDismissed = false,
    this.metadata,
    String? title, // For compatibility with other parts of the code
    String? description, // For compatibility with other parts of the code
    String? imageUrl, // For compatibility with other parts of the code
    double? score, // For compatibility with other parts of the code
  });

  /// إنشاء كيان التوصية من JSON
  factory Recommendation.fromJson(Map<String, dynamic> json) {
    return Recommendation(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: RecommendationType.values.firstWhere(
        (e) => e.toString() == 'RecommendationType.${json['type']}',
        orElse: () => RecommendationType.estate),
      source: RecommendationSource.values.firstWhere(
        (e) => e.toString() == 'RecommendationSource.${json['source']}',
        orElse: () => RecommendationSource.userPreferences),
      itemId: json['itemId'] as String,
      itemTitle: json['itemTitle'] as String,
      itemImage: json['itemImage'] as String?,
      itemDescription: json['itemDescription'] as String?,
      reason: json['reason'] as String,
      relevanceScore: (json['relevanceScore'] as num).toDouble(),
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: (json['updatedAt'] as Timestamp).toDate(),
      isViewed: json['isViewed'] as bool? ?? false,
      isClicked: json['isClicked'] as bool? ?? false,
      isDismissed: json['isDismissed'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?);
  }

  /// تحويل كيان التوصية إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.toString().split('.').last,
      'source': source.toString().split('.').last,
      'itemId': itemId,
      'itemTitle': itemTitle,
      'itemImage': itemImage,
      'itemDescription': itemDescription,
      'reason': reason,
      'relevanceScore': relevanceScore,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isViewed': isViewed,
      'isClicked': isClicked,
      'isDismissed': isDismissed,
      'metadata': metadata,
    };
  }

  /// Getter for score (compatibility with other parts of the code)
  double get score => relevanceScore;

  /// نسخ كيان التوصية مع تعديل بعض الخصائص
  Recommendation copyWith({
    String? id,
    String? userId,
    RecommendationType? type,
    RecommendationSource? source,
    String? itemId,
    String? itemTitle,
    String? itemImage,
    String? itemDescription,
    String? reason,
    double? relevanceScore,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isViewed,
    bool? isClicked,
    bool? isDismissed,
    Map<String, dynamic>? metadata,
  }) {
    return Recommendation(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      source: source ?? this.source,
      itemId: itemId ?? this.itemId,
      itemTitle: itemTitle ?? this.itemTitle,
      itemImage: itemImage ?? this.itemImage,
      itemDescription: itemDescription ?? this.itemDescription,
      reason: reason ?? this.reason,
      relevanceScore: relevanceScore ?? this.relevanceScore,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isViewed: isViewed ?? this.isViewed,
      isClicked: isClicked ?? this.isClicked,
      isDismissed: isDismissed ?? this.isDismissed,
      metadata: metadata ?? this.metadata);
  }
}
