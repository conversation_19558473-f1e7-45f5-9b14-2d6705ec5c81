import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// حالة العرض
enum OfferStatus {
  /// عرض جديد
  new_,
  
  /// عرض قيد المراجعة
  underReview,
  
  /// عرض مقبول
  accepted,
  
  /// عرض مرفوض
  rejected,
  
  /// عرض منتهي
  expired,
  
  /// عرض مسحوب
  withdrawn,
  
  /// عرض مكتمل
  completed,
}

/// نوع العرض
enum OfferType {
  /// عرض شراء
  purchase,
  
  /// عرض استئجار
  rent,
  
  /// عرض مبادلة
  exchange,
}

/// نموذج للعروض
class Offer extends Equatable {
  /// معرف العرض
  final String id;
  
  /// معرف العقار
  final String estateId;
  
  /// عنوان العقار
  final String estateTitle;
  
  /// معرف مقدم العرض
  final String offererId;
  
  /// اسم مقدم العرض
  final String offererName;
  
  /// رقم هاتف مقدم العرض
  final String offererPhone;
  
  /// صورة مقدم العرض
  final String? offererImage;
  
  /// معرف مالك العقار
  final String ownerId;
  
  /// اسم مالك العقار
  final String? ownerName;
  
  /// نوع العرض
  final OfferType type;
  
  /// قيمة العرض
  final double offerAmount;
  
  /// قيمة العربون
  final double? depositAmount;
  
  /// تاريخ تقديم العرض
  final DateTime offerDate;
  
  /// تاريخ انتهاء العرض
  final DateTime? expiryDate;
  
  /// حالة العرض
  final OfferStatus status;
  
  /// تاريخ آخر تحديث للعرض
  final DateTime? updatedAt;
  
  /// معرف المستخدم الذي قام بآخر تحديث للعرض
  final String? updatedBy;
  
  /// تفاصيل العرض
  final String? details;
  
  /// شروط العرض
  final String? conditions;
  
  /// ملاحظات العرض
  final String? notes;
  
  /// سبب رفض العرض
  final String? rejectionReason;
  
  /// معرف الوسيط (إذا كان العرض من خلال وسيط)
  final String? agentId;
  
  /// اسم الوسيط
  final String? agentName;
  
  /// عمولة الوسيط
  final double? agentCommission;
  
  /// معرف العقد (إذا تم قبول العرض وإنشاء عقد)
  final String? contractId;
  
  /// تاريخ التسليم المتوقع
  final DateTime? expectedDeliveryDate;
  
  /// طريقة الدفع
  final String? paymentMethod;
  
  /// تفاصيل طريقة الدفع
  final Map<String, dynamic>? paymentDetails;
  
  /// معلومات إضافية
  final Map<String, dynamic>? additionalInfo;

  const Offer({
    required this.id,
    required this.estateId,
    required this.estateTitle,
    required this.offererId,
    required this.offererName,
    required this.offererPhone,
    this.offererImage,
    required this.ownerId,
    this.ownerName,
    required this.type,
    required this.offerAmount,
    this.depositAmount,
    required this.offerDate,
    this.expiryDate,
    required this.status,
    this.updatedAt,
    this.updatedBy,
    this.details,
    this.conditions,
    this.notes,
    this.rejectionReason,
    this.agentId,
    this.agentName,
    this.agentCommission,
    this.contractId,
    this.expectedDeliveryDate,
    this.paymentMethod,
    this.paymentDetails,
    this.additionalInfo,
  });

  /// إنشاء نسخة معدلة من العرض
  Offer copyWith({
    String? id,
    String? estateId,
    String? estateTitle,
    String? offererId,
    String? offererName,
    String? offererPhone,
    String? offererImage,
    String? ownerId,
    String? ownerName,
    OfferType? type,
    double? offerAmount,
    double? depositAmount,
    DateTime? offerDate,
    DateTime? expiryDate,
    OfferStatus? status,
    DateTime? updatedAt,
    String? updatedBy,
    String? details,
    String? conditions,
    String? notes,
    String? rejectionReason,
    String? agentId,
    String? agentName,
    double? agentCommission,
    String? contractId,
    DateTime? expectedDeliveryDate,
    String? paymentMethod,
    Map<String, dynamic>? paymentDetails,
    Map<String, dynamic>? additionalInfo,
  }) {
    return Offer(
      id: id ?? this.id,
      estateId: estateId ?? this.estateId,
      estateTitle: estateTitle ?? this.estateTitle,
      offererId: offererId ?? this.offererId,
      offererName: offererName ?? this.offererName,
      offererPhone: offererPhone ?? this.offererPhone,
      offererImage: offererImage ?? this.offererImage,
      ownerId: ownerId ?? this.ownerId,
      ownerName: ownerName ?? this.ownerName,
      type: type ?? this.type,
      offerAmount: offerAmount ?? this.offerAmount,
      depositAmount: depositAmount ?? this.depositAmount,
      offerDate: offerDate ?? this.offerDate,
      expiryDate: expiryDate ?? this.expiryDate,
      status: status ?? this.status,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      details: details ?? this.details,
      conditions: conditions ?? this.conditions,
      notes: notes ?? this.notes,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      agentCommission: agentCommission ?? this.agentCommission,
      contractId: contractId ?? this.contractId,
      expectedDeliveryDate: expectedDeliveryDate ?? this.expectedDeliveryDate,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentDetails: paymentDetails ?? this.paymentDetails,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }
  
  /// تحويل العرض إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'estateId': estateId,
      'estateTitle': estateTitle,
      'offererId': offererId,
      'offererName': offererName,
      'offererPhone': offererPhone,
      'offererImage': offererImage,
      'ownerId': ownerId,
      'ownerName': ownerName,
      'type': type.toString().split('.').last.replaceAll('_', ''),
      'offerAmount': offerAmount,
      'depositAmount': depositAmount,
      'offerDate': Timestamp.fromDate(offerDate),
      'expiryDate': expiryDate != null ? Timestamp.fromDate(expiryDate!) : null,
      'status': status.toString().split('.').last.replaceAll('_', ''),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'updatedBy': updatedBy,
      'details': details,
      'conditions': conditions,
      'notes': notes,
      'rejectionReason': rejectionReason,
      'agentId': agentId,
      'agentName': agentName,
      'agentCommission': agentCommission,
      'contractId': contractId,
      'expectedDeliveryDate': expectedDeliveryDate != null ? Timestamp.fromDate(expectedDeliveryDate!) : null,
      'paymentMethod': paymentMethod,
      'paymentDetails': paymentDetails,
      'additionalInfo': additionalInfo,
    };
  }
  
  /// إنشاء عرض من Map
  factory Offer.fromMap(Map<String, dynamic> map) {
    return Offer(
      id: map['id'] ?? '',
      estateId: map['estateId'] ?? '',
      estateTitle: map['estateTitle'] ?? '',
      offererId: map['offererId'] ?? '',
      offererName: map['offererName'] ?? '',
      offererPhone: map['offererPhone'] ?? '',
      offererImage: map['offererImage'],
      ownerId: map['ownerId'] ?? '',
      ownerName: map['ownerName'],
      type: _getOfferTypeFromString(map['type'] ?? 'purchase'),
      offerAmount: (map['offerAmount'] ?? 0.0).toDouble(),
      depositAmount: map['depositAmount'] != null ? (map['depositAmount'] as num).toDouble() : null,
      offerDate: map['offerDate'] is Timestamp 
          ? (map['offerDate'] as Timestamp).toDate() 
          : DateTime.now(),
      expiryDate: map['expiryDate'] is Timestamp 
          ? (map['expiryDate'] as Timestamp).toDate() 
          : null,
      status: _getOfferStatusFromString(map['status'] ?? 'new'),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
      updatedBy: map['updatedBy'],
      details: map['details'],
      conditions: map['conditions'],
      notes: map['notes'],
      rejectionReason: map['rejectionReason'],
      agentId: map['agentId'],
      agentName: map['agentName'],
      agentCommission: map['agentCommission'] != null ? (map['agentCommission'] as num).toDouble() : null,
      contractId: map['contractId'],
      expectedDeliveryDate: map['expectedDeliveryDate'] is Timestamp 
          ? (map['expectedDeliveryDate'] as Timestamp).toDate() 
          : null,
      paymentMethod: map['paymentMethod'],
      paymentDetails: map['paymentDetails'],
      additionalInfo: map['additionalInfo']);
  }
  
  /// إنشاء عرض من DocumentSnapshot
  factory Offer.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return Offer.fromMap(data);
  }
  
  /// الحصول على نوع العرض من النص
  static OfferType _getOfferTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'rent':
        return OfferType.rent;
      case 'exchange':
        return OfferType.exchange;
      default:
        return OfferType.purchase;
    }
  }
  
  /// الحصول على حالة العرض من النص
  static OfferStatus _getOfferStatusFromString(String statusStr) {
    switch (statusStr) {
      case 'underReview':
        return OfferStatus.underReview;
      case 'accepted':
        return OfferStatus.accepted;
      case 'rejected':
        return OfferStatus.rejected;
      case 'expired':
        return OfferStatus.expired;
      case 'withdrawn':
        return OfferStatus.withdrawn;
      case 'completed':
        return OfferStatus.completed;
      default:
        return OfferStatus.new_;
    }
  }
  
  /// الحصول على اسم نوع العرض بالعربية
  String getOfferTypeName() {
    switch (type) {
      case OfferType.rent:
        return 'استئجار';
      case OfferType.exchange:
        return 'مبادلة';
      case OfferType.purchase:
        return 'شراء';
    }
  }
  
  /// الحصول على اسم حالة العرض بالعربية
  String getOfferStatusName() {
    switch (status) {
      case OfferStatus.underReview:
        return 'قيد المراجعة';
      case OfferStatus.accepted:
        return 'مقبول';
      case OfferStatus.rejected:
        return 'مرفوض';
      case OfferStatus.expired:
        return 'منتهي';
      case OfferStatus.withdrawn:
        return 'مسحوب';
      case OfferStatus.completed:
        return 'مكتمل';
      case OfferStatus.new_:
        return 'جديد';
    }
  }
  
  /// الحصول على لون حالة العرض
  String getOfferStatusColor() {
    switch (status) {
      case OfferStatus.underReview:
        return '#FF9800'; // برتقالي
      case OfferStatus.accepted:
        return '#4CAF50'; // أخضر
      case OfferStatus.rejected:
        return '#F44336'; // أحمر
      case OfferStatus.expired:
        return '#9E9E9E'; // رمادي
      case OfferStatus.withdrawn:
        return '#607D8B'; // رمادي داكن
      case OfferStatus.completed:
        return '#2196F3'; // أزرق
      case OfferStatus.new_:
        return '#9C27B0'; // بنفسجي
    }
  }
  
  /// مراجعة العرض
  Offer review(String updatedBy) {
    return copyWith(
      status: OfferStatus.underReview,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy);
  }
  
  /// قبول العرض
  Offer accept(String updatedBy) {
    return copyWith(
      status: OfferStatus.accepted,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy);
  }
  
  /// رفض العرض
  Offer reject(String updatedBy, String reason) {
    return copyWith(
      status: OfferStatus.rejected,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy,
      rejectionReason: reason);
  }
  
  /// سحب العرض
  Offer withdraw(String updatedBy) {
    return copyWith(
      status: OfferStatus.withdrawn,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy);
  }
  
  /// إكمال العرض
  Offer complete(String updatedBy, String contractId) {
    return copyWith(
      status: OfferStatus.completed,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy,
      contractId: contractId);
  }
  
  /// التحقق مما إذا كان العرض منتهي
  bool isExpired() {
    if (expiryDate == null) {
      return false;
    }
    
    return DateTime.now().isAfter(expiryDate!);
  }
  
  /// التحقق مما إذا كان العرض نشط
  bool isActive() {
    return status == OfferStatus.new_ || 
        status == OfferStatus.underReview || 
        status == OfferStatus.accepted;
  }
  
  /// الحصول على نسبة العربون من قيمة العرض
  double getDepositPercentage() {
    if (depositAmount == null || offerAmount <= 0) {
      return 0.0;
    }
    
    return (depositAmount! / offerAmount) * 100;
  }

  @override
  List<Object?> get props => [
    id,
    estateId,
    estateTitle,
    offererId,
    offererName,
    offererPhone,
    offererImage,
    ownerId,
    ownerName,
    type,
    offerAmount,
    depositAmount,
    offerDate,
    expiryDate,
    status,
    updatedAt,
    updatedBy,
    details,
    conditions,
    notes,
    rejectionReason,
    agentId,
    agentName,
    agentCommission,
    contractId,
    expectedDeliveryDate,
    paymentMethod,
    paymentDetails,
    additionalInfo,
  ];
}
