import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../api/api_client.dart';
import '../../../core/errors/exceptions.dart';
import '../../../domain/entities/transportation_info.dart';
import '../../../domain/entities/utility_info.dart';
import '../../../domain/entities/poi.dart';

/// خدمة التكامل مع خدمات النقل والمرافق
class LocationService {
  final ApiClient _apiClient;
  final FlutterSecureStorage _secureStorage;
  final String _baseUrl;

  /// إنشاء خدمة التكامل مع خدمات النقل والمرافق
  LocationService({
    required ApiClient apiClient,
    required FlutterSecureStorage secureStorage,
    String? baseUrl,
  })  : _apiClient = apiClient,
        _secureStorage = secureStorage,
        _baseUrl = baseUrl ?? 'https://api.realestate.com/location';

  /// الحصول على معلومات النقل للموقع
  Future<TransportationInfo> getTransportationInfo({
    required double latitude,
    required double longitude,
    double radius = 1000.0,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/transportation',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return TransportationInfo.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات النقل: $e');
    }
  }

  /// الحصول على معلومات المرافق للموقع
  Future<UtilityInfo> getUtilityInfo({
    required double latitude,
    required double longitude,
    double radius = 1000.0,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/utilities',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return UtilityInfo.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات المرافق: $e');
    }
  }

  /// الحصول على نقاط الاهتمام القريبة
  Future<List<POI>> getNearbyPOIs({
    required double latitude,
    required double longitude,
    double radius = 1000.0,
    List<String>? categories,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/pois',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
          'categories': categories?.join(','),
          'limit': limit.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 6));

      if (response is List) {
        return response.map((poi) => POI.fromJson(poi)).toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على نقاط الاهتمام القريبة: $e');
    }
  }

  /// الحصول على معلومات المنطقة
  Future<Map<String, dynamic>> getAreaInfo(String areaCode) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/areas/$areaCode',
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return response;
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات المنطقة: $e');
    }
  }

  /// الحصول على معلومات الطرق
  Future<Map<String, dynamic>> getRoadInfo({
    required double latitude,
    required double longitude,
    double radius = 1000.0,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/roads',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return response;
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات الطرق: $e');
    }
  }

  /// الحصول على معلومات الأحياء
  Future<Map<String, dynamic>> getNeighborhoodInfo({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/neighborhoods',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return response;
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات الأحياء: $e');
    }
  }

  /// الحصول على معلومات المدارس القريبة
  Future<List<POI>> getNearbySchools({
    required double latitude,
    required double longitude,
    double radius = 2000.0,
    String? schoolType,
    int limit = 10,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/schools',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
          'schoolType': schoolType,
          'limit': limit.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      if (response is List) {
        return response.map((school) => POI.fromJson(school)).toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات المدارس القريبة: $e');
    }
  }

  /// الحصول على معلومات المستشفيات القريبة
  Future<List<POI>> getNearbyHospitals({
    required double latitude,
    required double longitude,
    double radius = 3000.0,
    String? hospitalType,
    int limit = 10,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/hospitals',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
          'hospitalType': hospitalType,
          'limit': limit.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      if (response is List) {
        return response.map((hospital) => POI.fromJson(hospital)).toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException('Location Service',
          'فشل في الحصول على معلومات المستشفيات القريبة: $e');
    }
  }

  /// الحصول على معلومات المساجد القريبة
  Future<List<POI>> getNearbyMosques({
    required double latitude,
    required double longitude,
    double radius = 1000.0,
    int limit = 5,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/mosques',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
          'limit': limit.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      if (response is List) {
        return response.map((mosque) => POI.fromJson(mosque)).toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات المساجد القريبة: $e');
    }
  }

  /// الحصول على معلومات المراكز التجارية القريبة
  Future<List<POI>> getNearbyMalls({
    required double latitude,
    required double longitude,
    double radius = 3000.0,
    int limit = 5,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/malls',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
          'limit': limit.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      if (response is List) {
        return response.map((mall) => POI.fromJson(mall)).toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException('Location Service',
          'فشل في الحصول على معلومات المراكز التجارية القريبة: $e');
    }
  }

  /// الحصول على معلومات الحدائق القريبة
  Future<List<POI>> getNearbyParks({
    required double latitude,
    required double longitude,
    double radius = 2000.0,
    int limit = 5,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/parks',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
          'limit': limit.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      if (response is List) {
        return response.map((park) => POI.fromJson(park)).toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات الحدائق القريبة: $e');
    }
  }

  /// الحصول على معلومات الأمن في المنطقة
  Future<Map<String, dynamic>> getAreaSafetyInfo(String areaCode) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/safety/$areaCode',
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return response;
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات الأمن في المنطقة: $e');
    }
  }

  /// الحصول على معلومات جودة الهواء
  Future<Map<String, dynamic>> getAirQualityInfo({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/air-quality',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 3));

      return response;
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات جودة الهواء: $e');
    }
  }

  /// الحصول على معلومات الطقس
  Future<Map<String, dynamic>> getWeatherInfo({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/weather',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 1));

      return response;
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات الطقس: $e');
    }
  }

  /// الحصول على معلومات الضوضاء
  Future<Map<String, dynamic>> getNoiseInfo({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/noise',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return response;
    } catch (e) {
      throw IntegrationException(
          'Location Service', 'فشل في الحصول على معلومات الضوضاء: $e');
    }
  }
}

/// امتدادات لتسهيل استخدام خدمة التكامل مع خدمات النقل والمرافق
extension LocationServiceExtensions on LocationService {
  /// الحصول على وصف نوع وسيلة النقل
  String getTransportationTypeDescription(String type) {
    switch (type) {
      case 'bus':
        return 'حافلة';
      case 'metro':
        return 'مترو';
      case 'train':
        return 'قطار';
      case 'taxi':
        return 'سيارة أجرة';
      case 'bike':
        return 'دراجة';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على وصف نوع المرفق
  String getUtilityTypeDescription(String type) {
    switch (type) {
      case 'water':
        return 'مياه';
      case 'electricity':
        return 'كهرباء';
      case 'gas':
        return 'غاز';
      case 'internet':
        return 'إنترنت';
      case 'sewage':
        return 'صرف صحي';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على وصف فئة نقطة الاهتمام
  String getPOICategoryDescription(String category) {
    switch (category) {
      case 'restaurant':
        return 'مطعم';
      case 'cafe':
        return 'مقهى';
      case 'school':
        return 'مدرسة';
      case 'hospital':
        return 'مستشفى';
      case 'pharmacy':
        return 'صيدلية';
      case 'mosque':
        return 'مسجد';
      case 'mall':
        return 'مركز تجاري';
      case 'park':
        return 'حديقة';
      case 'gym':
        return 'نادي رياضي';
      case 'bank':
        return 'بنك';
      case 'atm':
        return 'صراف آلي';
      case 'gas_station':
        return 'محطة وقود';
      case 'supermarket':
        return 'سوبر ماركت';
      case 'bakery':
        return 'مخبز';
      case 'police':
        return 'شرطة';
      default:
        return 'غير معروف';
    }
  }
}
