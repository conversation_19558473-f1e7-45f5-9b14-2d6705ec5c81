import 'package:flutter/material.dart';

/// نموذج شارة المستخدم
class BadgeModel {
  /// معرف الشارة
  final String id;

  /// اسم الشارة
  final String name;

  /// وصف الشارة
  final String description;

  /// رابط صورة الشارة
  final String imageUrl;

  /// لون الشارة
  final Color color;

  /// رمز الشارة
  final IconData icon;

  /// نوع الشارة (إنجاز، مستوى، خاص)
  final BadgeType type;

  /// شروط الحصول على الشارة
  final String requirement;

  /// النقاط المكافأة عند الحصول على الشارة
  final int bonusPoints;

  /// تاريخ الحصول على الشارة
  final DateTime? earnedAt;

  /// هل الشارة نادرة
  final bool isRare;

  BadgeModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.color,
    required this.icon,
    required this.type,
    required this.requirement,
    this.bonusPoints = 0,
    this.earnedAt,
    this.isRare = false,
  });

  /// إنشاء نسخة من الشارة مع تحديث بعض الخصائص
  BadgeModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    Color? color,
    IconData? icon,
    BadgeType? type,
    String? requirement,
    int? bonusPoints,
    DateTime? earnedAt,
    bool? isRare,
  }) {
    return BadgeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      type: type ?? this.type,
      requirement: requirement ?? this.requirement,
      bonusPoints: bonusPoints ?? this.bonusPoints,
      earnedAt: earnedAt ?? this.earnedAt,
      isRare: isRare ?? this.isRare);
  }

  /// تحويل الشارة إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'colorValue': color.value,
      'iconCodePoint': icon.codePoint,
      'iconFontFamily': icon.fontFamily,
      'iconFontPackage': icon.fontPackage,
      'type': type.index,
      'requirement': requirement,
      'bonusPoints': bonusPoints,
      'earnedAt': earnedAt?.millisecondsSinceEpoch,
      'isRare': isRare,
    };
  }

  /// إنشاء شارة من خريطة
  factory BadgeModel.fromMap(Map<String, dynamic> map) {
    return BadgeModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      imageUrl: map['imageUrl'] ?? '',
      color: Color(map['colorValue'] ?? 0xFF000000),
      icon: IconData(
        map['iconCodePoint'] ?? Icons.star.codePoint,
        fontFamily: map['iconFontFamily'],
        fontPackage: map['iconFontPackage']),
      type: BadgeType.values[map['type'] ?? 0],
      requirement: map['requirement'] ?? '',
      bonusPoints: map['bonusPoints'] ?? 0,
      earnedAt: map['earnedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['earnedAt'])
          : null,
      isRare: map['isRare'] ?? false);
  }

  /// الحصول على الشارة بواسطة المعرف
  static BadgeModel? getBadgeById(String badgeId) {
    final badges = getAvailableBadges();
    try {
      return badges.firstWhere((badge) => badge.id == badgeId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على لون الشارة كـ Color
  Color getColorObject() {
    return color;
  }

  /// الحصول على قائمة الشارات المتاحة
  static List<BadgeModel> getAvailableBadges() {
    return [
      // شارات المستويات
      BadgeModel(
        id: 'level_beginner',
        name: 'مبتدئ',
        description: 'وصلت إلى مستوى مبتدئ',
        imageUrl: 'assets/images/badges/beginner.png',
        color: Colors.green.shade300,
        icon: Icons.emoji_events_outlined,
        type: BadgeType.level,
        requirement: 'الوصول إلى 0 نقطة',
        bonusPoints: 0),
      BadgeModel(
        id: 'level_active',
        name: 'نشط',
        description: 'وصلت إلى مستوى نشط',
        imageUrl: 'assets/images/badges/active.png',
        color: Colors.green.shade400, // تغيير من الأزرق إلى الأخضر
        icon: Icons.star_outline,
        type: BadgeType.level,
        requirement: 'الوصول إلى 100 نقطة',
        bonusPoints: 20),

      // شارات الإنجازات
      BadgeModel(
        id: 'achievement_first_topic',
        name: 'الكاتب المبتدئ',
        description: 'قمت بإنشاء أول موضوع',
        imageUrl: 'assets/images/badges/first_topic.png',
        color: Colors.amber.shade300,
        icon: Icons.create,
        type: BadgeType.achievement,
        requirement: 'إنشاء موضوع واحد',
        bonusPoints: 10),
      BadgeModel(
        id: 'achievement_first_reply',
        name: 'المشارك الأول',
        description: 'قمت بإضافة أول رد',
        imageUrl: 'assets/images/badges/first_reply.png',
        color: Colors.teal.shade300,
        icon: Icons.reply,
        type: BadgeType.achievement,
        requirement: 'إضافة رد واحد',
        bonusPoints: 5),

      // شارات خاصة
      BadgeModel(
        id: 'special_early_adopter',
        name: 'المستخدم المبكر',
        description: 'من أوائل مستخدمي المنتدى',
        imageUrl: 'assets/images/badges/early_adopter.png',
        color: Colors.purple.shade300,
        icon: Icons.rocket_launch,
        type: BadgeType.special,
        requirement: 'الانضمام خلال الشهر الأول من إطلاق المنتدى',
        bonusPoints: 50,
        isRare: true),
    ];
  }
}

/// أنواع الشارات
enum BadgeType {
  /// شارة مستوى
  level,

  /// شارة إنجاز
  achievement,

  /// شارة خاصة
  special,
}
