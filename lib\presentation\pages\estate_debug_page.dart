import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../core/theme/app_colors.dart';

class EstateDebugPage extends StatefulWidget {
  const EstateDebugPage({super.key});

  @override
  State<EstateDebugPage> createState() => _EstateDebugPageState();
}

class _EstateDebugPageState extends State<EstateDebugPage> {
  Map<String, dynamic> debugInfo = {};
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    setState(() {
      isLoading = true;
    });

    try {
      final info = <String, dynamic>{};

      // إحصائيات العقارات العامة
      final allEstatesSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .get();
      info['totalEstates'] = allEstatesSnapshot.docs.length;

      // العقارات المدفوعة (التي تظهر في التطبيق)
      final paidEstatesSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .where('isPaymentVerified', isEqualTo: true)
          .get();
      info['activeEstates'] = paidEstatesSnapshot.docs.length;
      info['paidEstates'] = paidEstatesSnapshot.docs.length;

      // العقارات النشطة والمدفوعة
      final activePaidEstatesSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .where('isActive', isEqualTo: true)
          .where('isPaymentVerified', isEqualTo: true)
          .get();
      info['activePaidEstates'] = activePaidEstatesSnapshot.docs.length;

      // أحدث 5 عقارات
      final latestEstatesSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .orderBy('createdAt', descending: true)
          .limit(5)
          .get();
      
      info['latestEstates'] = latestEstatesSnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'title': data['title'] ?? 'بدون عنوان',
          'isActive': data['isActive'] ?? false,
          'isPaymentVerified': data['isPaymentVerified'] ?? false,
          'createdAt': data['createdAt']?.toString() ?? 'غير محدد',
          'mainCategory': data['mainCategory'] ?? 'غير محدد',
        };
      }).toList();

      // فحص الفهارس المطلوبة
      info['indexesNeeded'] = [
        'isActive',
        'isPaymentVerified', 
        'createdAt',
        'mainCategory'
      ];

      setState(() {
        debugInfo = info;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        debugInfo = {'error': e.toString()};
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تشخيص العقارات',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDebugInfo,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (debugInfo.containsKey('error'))
                    _buildErrorCard(debugInfo['error'])
                  else ...[
                    _buildStatsCard(),
                    const SizedBox(height: 16),
                    _buildLatestEstatesCard(),
                    const SizedBox(height: 16),
                    _buildIndexesCard(),
                  ],
                ],
              ),
            ),
    );
  }

  Widget _buildErrorCard(String error) {
    return Card(
      color: Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'خطأ',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: GoogleFonts.cairo(color: Colors.red.shade700),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات العقارات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatRow('إجمالي العقارات', debugInfo['totalEstates']),
            _buildStatRow('العقارات النشطة', debugInfo['activeEstates']),
            _buildStatRow('العقارات المدفوعة', debugInfo['paidEstates']),
            _buildStatRow('العقارات النشطة والمدفوعة', debugInfo['activePaidEstates']),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, dynamic value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.cairo(fontSize: 14),
          ),
          Text(
            value?.toString() ?? '0',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLatestEstatesCard() {
    final latestEstates = debugInfo['latestEstates'] as List<dynamic>? ?? [];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أحدث العقارات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            if (latestEstates.isEmpty)
              Text(
                'لا توجد عقارات',
                style: GoogleFonts.cairo(color: Colors.grey),
              )
            else
              ...latestEstates.map((estate) => _buildEstateRow(estate)),
          ],
        ),
      ),
    );
  }

  Widget _buildEstateRow(Map<String, dynamic> estate) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            estate['title'] ?? 'بدون عنوان',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              _buildStatusChip('نشط', estate['isActive'] == true),
              const SizedBox(width: 8),
              _buildStatusChip('مدفوع', estate['isPaymentVerified'] == true),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'التصنيف: ${estate['mainCategory'] ?? 'غير محدد'}',
            style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String label, bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isActive ? Colors.green.shade100 : Colors.red.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: GoogleFonts.cairo(
          fontSize: 10,
          color: isActive ? Colors.green.shade700 : Colors.red.shade700,
        ),
      ),
    );
  }

  Widget _buildIndexesCard() {
    final indexes = debugInfo['indexesNeeded'] as List<dynamic>? ?? [];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الفهارس المطلوبة في Firebase',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'تأكد من وجود الفهارس التالية في Firestore:',
              style: GoogleFonts.cairo(fontSize: 14),
            ),
            const SizedBox(height: 8),
            ...indexes.map((index) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Text(
                '• $index',
                style: TextStyle(
                  fontSize: 12,
                  fontFamily: 'monospace',
                  color: Colors.blue.shade700,
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }
}
