// lib/core/services/auto_save_service.dart
import 'dart:async';

import 'enhanced_ad_draft_service.dart';

/// خدمة الحفظ التلقائي للمسودات
/// تقوم بحفظ المسودة بشكل دوري أثناء إنشاء الإعلان
class AutoSaveService {
  /// خدمة مسودات الإعلانات المحسنة
  final EnhancedAdDraftService _draftService;
  
  /// مؤقت الحفظ التلقائي
  Timer? _autoSaveTimer;
  
  /// الفترة الزمنية بين عمليات الحفظ التلقائي
  final Duration _autoSaveDuration;
  
  /// معرف المسودة الحالية
  String? _currentDraftId;
  
  /// بيانات المسودة الحالية
  Map<String, dynamic> _currentDraftData = {};
  
  /// ما إذا كان الحفظ التلقائي مفعل
  bool _isAutoSaveEnabled = true;

  AutoSaveService({
    required EnhancedAdDraftService draftService,
    Duration autoSaveDuration = const Duration(seconds: 30),
  }) : _draftService = draftService,
       _autoSaveDuration = autoSaveDuration;

  /// بدء الحفظ التلقائي
  void startAutoSave(String draftId, Map<String, dynamic> initialData) {
    _currentDraftId = draftId;
    _currentDraftData = Map<String, dynamic>.from(initialData);
    
    // إلغاء المؤقت السابق إذا كان موجوداً
    _autoSaveTimer?.cancel();
    
    // بدء مؤقت جديد
    if (_isAutoSaveEnabled) {
      _autoSaveTimer = Timer.periodic(_autoSaveDuration, (_) => _saveCurrentDraft());
    }
  }
  
  /// تحديث بيانات المسودة الحالية
  void updateDraftData(Map<String, dynamic> newData) {
    _currentDraftData.addAll(newData);
    
    // حفظ فوري بعد التحديث
    _saveCurrentDraft();
  }
  
  /// حفظ المسودة الحالية
  Future<void> _saveCurrentDraft() async {
    if (_currentDraftId != null && _currentDraftData.isNotEmpty) {
      await _draftService.saveDraft(_currentDraftId!, _currentDraftData);
    }
  }
  
  /// إيقاف الحفظ التلقائي
  void stopAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
    
    // حفظ نهائي عند الإيقاف
    _saveCurrentDraft();
  }
  
  /// تفعيل أو تعطيل الحفظ التلقائي
  void setAutoSaveEnabled(bool enabled) {
    _isAutoSaveEnabled = enabled;
    
    if (!enabled) {
      _autoSaveTimer?.cancel();
      _autoSaveTimer = null;
    } else if (_currentDraftId != null) {
      // إعادة تشغيل المؤقت إذا كان هناك مسودة حالية
      _autoSaveTimer = Timer.periodic(_autoSaveDuration, (_) => _saveCurrentDraft());
    }
  }
  
  /// التنظيف عند التخلص من الخدمة
  void dispose() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
  }
}
