// lib/presentation/pages/improved_ad_preview_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../../core/services/enhanced_ad_draft_service.dart';
import '../../core/services/enhanced_subscription_service.dart';
import '../../domain/entities/estate.dart';
import '../bloc/improved_ad_bloc.dart';
import '../widgets/ad_creation_navigation_buttons.dart';
import '../widgets/enhanced_ad_preview_card.dart';
import '../widgets/improved_ad_creation_progress.dart';
import 'enhanced_payment_page.dart';
import 'in_flow_upgrade_page.dart';

/// صفحة معاينة الإعلان المحسنة
class ImprovedAdPreviewPage extends StatefulWidget {
  final Estate estate;

  const ImprovedAdPreviewPage({super.key, required this.estate});

  @override
  State<ImprovedAdPreviewPage> createState() => _ImprovedAdPreviewPageState();
}

class _ImprovedAdPreviewPageState extends State<ImprovedAdPreviewPage>
    with SingleTickerProviderStateMixin {
  // خدمة المسودات المحسنة
  final _draftService = EnhancedAdDraftService();

  // خدمة الاشتراكات
  final _subscriptionService = EnhancedSubscriptionService();

  // متغيرات التحقق من الصلاحيات
  bool _isCheckingPermissions = false;
  bool _canCreateAd = true;
  int _remainingAds = 0;

  // متغيرات الرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  // متغيرات الحالة
  bool _isLoading = true;
  bool _showQualityTips = false;
  int _adQualityScore = 0;

  // بيانات الإعلان
  String _title = "";
  String _description = "";
  double _price = 0.0;
  String _location = "";
  List<String> _imagePaths = [];
  String _mainCategory = "";
  String _subCategory = "";
  double? _area;
  int? _numberOfRooms;
  int? _numberOfBathrooms;
  int? _buildingAge;
  bool _hasGarage = false;
  bool _hasCentralAC = false;
  bool _hasMaidRoom = false;
  bool _isFullyFurnished = false;

  // بيانات الخطة
  String _planId = "free";
  double _planPrice = 0.0;
  int _planDays = 30;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn));

    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutQuad));

    _animationController.forward();

    // تحميل بيانات الإعلان
    _loadAdData();

    // التحقق من صلاحيات المستخدم
    _checkPermissions();
  }

  /// التحقق من صلاحيات المستخدم
  Future<void> _checkPermissions() async {
    setState(() {
      _isCheckingPermissions = true;
    });

    try {
      // التحقق مما إذا كان المستخدم يمكنه إنشاء إعلان
      final canCreate = await _subscriptionService.canCreateAd(null);

      // الحصول على عدد الإعلانات المتبقية
      final remaining = await _subscriptionService.getRemainingAds(null);

      setState(() {
        _canCreateAd = canCreate;
        _remainingAds = remaining;
        _isCheckingPermissions = false;
      });
    } catch (e) {
      setState(() {
        _isCheckingPermissions = false;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل بيانات الإعلان
  Future<void> _loadAdData() async {
    setState(() {
      _isLoading = true;
    });

    // الحصول على بيانات الإعلان من BLoC
    final adState = context.read<ImprovedAdBloc>().state;

    // تحميل البيانات الأساسية
    _title = adState.title;
    _description = adState.description;
    _price = adState.price;
    _location = "${adState.governorate} - ${adState.city} - ق.${adState.piece}";
    _imagePaths = adState.imagePaths;
    _mainCategory = adState.mainCategory;
    _subCategory = adState.subCategory;

    // تحميل التفاصيل الداخلية
    _area = adState.internalArea;
    _numberOfRooms = adState.internalNumberOfRooms;
    _numberOfBathrooms = adState.internalNumberOfBathrooms;
    _buildingAge = adState.internalBuildingAge;

    // تحميل المميزات
    _hasGarage = adState.hasGarage;
    _hasCentralAC = adState.hasCentralAC;
    _hasMaidRoom = adState.hasMaidRoom;
    _isFullyFurnished = adState.isFullyFurnished;

    // تحميل بيانات الخطة
    _planId = adState.planId;
    _planPrice = adState.planPrice;
    _planDays = adState.planDays;

    // حساب جودة الإعلان
    _calculateAdQuality();

    // حفظ المسودة
    await _draftService.saveDraft({
      'step': 5,
    });

    setState(() {
      _isLoading = false;
    });
  }

  /// حساب جودة الإعلان
  void _calculateAdQuality() {
    int score = 0;
    final List<String> tips = [];

    // التحقق من العنوان
    if (_title.length > 20) {
      score += 20;
    } else {
      tips.add("قم بإضافة عنوان أطول وأكثر تفصيلاً");
    }

    // التحقق من الوصف
    if (_description.length > 100) {
      score += 20;
    } else {
      tips.add("قم بإضافة وصف أكثر تفصيلاً");
    }

    // التحقق من الصور
    if (_imagePaths.length >= 5) {
      score += 20;
    } else {
      tips.add("قم بإضافة المزيد من الصور (5 صور على الأقل)");
    }

    // التحقق من التفاصيل
    int detailsCount = 0;
    if (_area != null) detailsCount++;
    if (_numberOfRooms != null) detailsCount++;
    if (_numberOfBathrooms != null) detailsCount++;
    if (_buildingAge != null) detailsCount++;

    if (detailsCount >= 3) {
      score += 20;
    } else {
      tips.add("قم بإضافة المزيد من تفاصيل العقار");
    }

    // التحقق من المميزات
    int featuresCount = 0;
    if (_hasGarage) featuresCount++;
    if (_hasCentralAC) featuresCount++;
    if (_hasMaidRoom) featuresCount++;
    if (_isFullyFurnished) featuresCount++;

    if (featuresCount >= 2) {
      score += 20;
    } else {
      tips.add("قم بإضافة المزيد من مميزات العقار");
    }

    // تحديث النتيجة
    _adQualityScore = score;

    // حفظ النصائح
    if (tips.isNotEmpty) {
      setState(() {
        _showQualityTips = true;
      });
    }
  }

  /// الحصول على قائمة الميزات المختارة
  List<String> _getSelectedFeatures() {
    // إعداد قائمة الميزات المختارة
    List<String> selectedFeatures = [];

    // إضافة الميزات المختارة بناءً على حالة الإعلان
    final adState = context.read<ImprovedAdBloc>().state;

    if (adState.autoRepublish) {
      selectedFeatures.add("إعادة نشر تلقائي");
    }

    if (adState.kuwaitCornersPin) {
      selectedFeatures.add("تثبيت الإعلان");
    }

    if (adState.movingAd) {
      selectedFeatures.add("إعلان متحرك");
    }

    if (adState.vipBadge) {
      selectedFeatures.add("شارة VIP");
    }

    if (adState.pinnedOnHome) {
      selectedFeatures.add("تثبيت في الصفحة الرئيسية");
    }

    return selectedFeatures;
  }

  /// نشر الإعلان
  Future<void> _publishAd() async {
    // التحقق من صلاحيات المستخدم قبل النشر
    if (!_canCreateAd) {
      _showUpgradeDialog();
      return;
    }

    // إعداد قائمة الميزات المختارة
    List<String> selectedFeatures = _getSelectedFeatures();

    // الحصول على حالة الإعلان
    final adState = context.read<ImprovedAdBloc>().state;

    // حساب المبلغ الإجمالي
    double totalAmount = _planPrice;

    // إضافة تكلفة الميزات الإضافية
    if (adState.autoRepublish) totalAmount += 3.0;
    if (adState.kuwaitCornersPin) totalAmount += 5.0;
    if (adState.movingAd) totalAmount += 3.0;
    if (adState.vipBadge) totalAmount += 7.0;
    if (adState.pinnedOnHome) totalAmount += 10.0;

    // إنشاء نسخة محدثة من كائن Estate مع البيانات المحدثة
    final updatedEstate = widget.estate.copyWith(
      title: _title,
      description: _description,
      price: _price,
      location: _location,
      mainCategory: _mainCategory,
      subCategory: _subCategory,
      area: _area,
      numberOfRooms: _numberOfRooms,
      numberOfBathrooms: _numberOfBathrooms,
      buildingAge: _buildingAge,
      hasGarage: _hasGarage,
      hasCentralAC: _hasCentralAC,
      hasMaidRoom: _hasMaidRoom,
      isFullyFurnished: _isFullyFurnished,
      planType: _planId,
      photoUrls: _imagePaths, // إضافة مسارات الصور
      autoRepublish: adState.autoRepublish,
      kuwaitCornersPin: adState.kuwaitCornersPin,
      movingAd: adState.movingAd,
      vipBadge: adState.vipBadge,
      pinnedOnHome: adState.pinnedOnHome);

    // تقليل عدد الإعلانات المتبقية
    await _subscriptionService.decrementRemainingAds(null);

    // التحقق من أن الـ Widget لا يزال مثبتًا
    if (!mounted) return;

    // الانتقال إلى صفحة الدفع المحسنة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => EnhancedPaymentPage(
          estate: updatedEstate,
          selectedFeatures: selectedFeatures,
          totalAmount: totalAmount)));
  }

  /// عرض حوار الترقية
  void _showUpgradeDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          "لا يمكن نشر إعلان جديد",
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        content: Text(
          "لقد وصلت إلى الحد الأقصى لعدد الإعلانات المسموح بها في باقتك الحالية. يرجى ترقية باقتك للاستمرار.",
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(
              "إلغاء",
              style: GoogleFonts.cairo())),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToUpgrade();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white),
            child: Text(
              "ترقية الباقة",
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold))),
        ]));
  }

  /// الانتقال إلى صفحة الترقية
  void _navigateToUpgrade() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InFlowUpgradePage(
          onUpgradeComplete: () {
            // إعادة التحقق من الصلاحيات بعد الترقية
            _checkPermissions();
            Navigator.pop(context);
          })));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      // مؤشر التقدم وأزرار التنقل أسفل الصفحة
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أزرار التنقل
          AdCreationNavigationButtons(
            onNext: _publishAd,
            onBack: () => Navigator.pop(context),
            nextText: "نشر الإعلان",
            backText: "العودة",
            isNextDisabled: false),

          // مؤشر التقدم
          ImprovedAdCreationProgress(
            currentStep: 5,
            onStepTap: (step) {
              if (step < 5) {
                Navigator.pop(context);
              }
            }),
        ]),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // ترويسة مع زر رجوع وعنوان
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.arrow_back,
                              color: Colors.black87),
                          onPressed: () => Navigator.of(context).pop()),
                        const SizedBox(width: 8),
                        Text(
                          "معاينة الإعلان",
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87)),
                      ]),
                    const SizedBox(height: 16),

                    // مؤشر جودة الإعلان
                    AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, _slideAnimation.value),
                          child: Opacity(
                            opacity: _fadeAnimation.value,
                            child: Card(
                              elevation: 4,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16)),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.analytics,
                                          color: Theme.of(context).primaryColor),
                                        const SizedBox(width: 8),
                                        Text(
                                          "جودة الإعلان",
                                          style: GoogleFonts.cairo(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color:
                                                Theme.of(context).primaryColor)),
                                        const Spacer(),
                                        Text(
                                          "$_adQualityScore%",
                                          style: GoogleFonts.cairo(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: _getQualityColor())),
                                      ]),
                                    const SizedBox(height: 8),

                                    // شريط التقدم
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(4),
                                      child: LinearProgressIndicator(
                                        value: _adQualityScore / 100,
                                        backgroundColor: Colors.grey.shade200,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                _getQualityColor()),
                                        minHeight: 8)),
                                    const SizedBox(height: 8),

                                    // نص الجودة
                                    Text(
                                      _getQualityText(),
                                      style: GoogleFonts.cairo(
                                        fontSize: 14,
                                        color: Colors.grey.shade700)),

                                    // زر عرض النصائح
                                    if (_adQualityScore < 100)
                                      TextButton.icon(
                                        onPressed: () {
                                          setState(() {
                                            _showQualityTips =
                                                !_showQualityTips;
                                          });
                                        },
                                        icon: Icon(
                                          _showQualityTips
                                              ? Icons.keyboard_arrow_up
                                              : Icons.keyboard_arrow_down,
                                          size: 16),
                                        label: Text(
                                          _showQualityTips
                                              ? "إخفاء النصائح"
                                              : "عرض نصائح للتحسين",
                                          style:
                                              GoogleFonts.cairo(fontSize: 12))),

                                    // نصائح التحسين
                                    if (_showQualityTips)
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: Colors.amber.shade50,
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "نصائح لتحسين جودة الإعلان:",
                                              style: GoogleFonts.cairo(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.amber.shade800)),
                                            const SizedBox(height: 8),
                                            if (_title.length <= 20)
                                              _buildTip(
                                                  "قم بإضافة عنوان أطول وأكثر تفصيلاً"),
                                            if (_description.length <= 100)
                                              _buildTip(
                                                  "قم بإضافة وصف أكثر تفصيلاً"),
                                            if (_imagePaths.length < 5)
                                              _buildTip(
                                                  "قم بإضافة المزيد من الصور (5 صور على الأقل)"),
                                            if (_area == null ||
                                                _numberOfRooms == null ||
                                                _numberOfBathrooms == null)
                                              _buildTip(
                                                  "قم بإضافة المزيد من تفاصيل العقار"),
                                            if (!_hasGarage &&
                                                !_hasCentralAC &&
                                                !_hasMaidRoom &&
                                                !_isFullyFurnished)
                                              _buildTip(
                                                  "قم بإضافة المزيد من مميزات العقار"),
                                          ])),
                                  ])))));
                      }),
                    const SizedBox(height: 24),

                    // معاينة الإعلان
                    Text(
                      "معاينة الإعلان",
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor)),
                    const SizedBox(height: 16),

                    // بطاقة معاينة الإعلان
                    EnhancedAdPreviewCard(
                      title: _title,
                      description: _description,
                      price: _price,
                      location: _location,
                      imagePaths: _imagePaths,
                      mainCategory: _mainCategory,
                      subCategory: _subCategory,
                      area: _area,
                      numberOfRooms: _numberOfRooms,
                      numberOfBathrooms: _numberOfBathrooms,
                      buildingAge: _buildingAge,
                      hasGarage: _hasGarage,
                      hasCentralAC: _hasCentralAC,
                      hasMaidRoom: _hasMaidRoom,
                      isFullyFurnished: _isFullyFurnished,
                      subscriptionPlan: _planId,
                      extraFeatures: _getSelectedFeatures()),
                    const SizedBox(height: 24),

                    // ملخص الخطة
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16)),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.workspace_premium,
                                  color: Theme.of(context).primaryColor),
                                const SizedBox(width: 8),
                                Text(
                                  "ملخص الخطة",
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor)),
                              ]),
                            const SizedBox(height: 16),

                            // تفاصيل الخطة
                            _buildPlanDetail("الخطة", _getPlanName()),
                            _buildPlanDetail("المدة", "$_planDays يوم"),
                            _buildPlanDetail(
                                "السعر",
                                _planPrice > 0
                                    ? "${_planPrice.toStringAsFixed(1)} د.ك"
                                    : "مجاني"),
                            _buildPlanDetail("تاريخ البدء",
                                _getFormattedDate(DateTime.now())),
                            _buildPlanDetail(
                                "تاريخ الانتهاء",
                                _getFormattedDate(DateTime.now()
                                    .add(Duration(days: _planDays)))),
                          ]))),

                    const SizedBox(height: 80), // مساحة للزر العائم
                  ]))));
  }

  /// الحصول على لون جودة الإعلان
  Color _getQualityColor() {
    if (_adQualityScore >= 80) {
      return Colors.green;
    } else if (_adQualityScore >= 50) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  /// الحصول على نص جودة الإعلان
  String _getQualityText() {
    if (_adQualityScore >= 80) {
      return "ممتاز! إعلانك ذو جودة عالية ويحتوي على معلومات كافية.";
    } else if (_adQualityScore >= 50) {
      return "جيد. يمكن تحسين إعلانك بإضافة المزيد من التفاصيل.";
    } else {
      return "يحتاج إلى تحسين. قم بإضافة المزيد من المعلومات والصور.";
    }
  }

  /// بناء نصيحة
  Widget _buildTip(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.lightbulb,
            color: Colors.amber.shade800,
            size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              tip,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.amber.shade900))),
        ]));
  }

  /// بناء تفصيل الخطة
  Widget _buildPlanDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Text(
            "$label: ",
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold)),
          Text(
            value,
            style: GoogleFonts.cairo()),
        ]));
  }

  /// الحصول على اسم الخطة
  String _getPlanName() {
    switch (_planId) {
      case "free":
        return "الخطة المجانية";
      case "basic":
        return "الخطة الأساسية";
      case "premium":
        return "الخطة المميزة";
      case "vip":
        return "الخطة الذهبية";
      default:
        return "غير معروف";
    }
  }

  /// الحصول على التاريخ المنسق
  String _getFormattedDate(DateTime date) {
    final formatter = DateFormat('yyyy/MM/dd', 'ar');
    return formatter.format(date);
  }
}
