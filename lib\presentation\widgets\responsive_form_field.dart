import 'package:flutter/material.dart';

/// مكون للحقول المتجاوبة
/// يتكيف مع حجم الشاشة ويعرض الحقول بشكل مناسب
class ResponsiveFormField extends StatelessWidget {
  /// عنوان الحقل
  final String label;
  
  /// مكون الحقل
  final Widget field;
  
  /// ما إذا كان الحقل إلزاميًا
  final bool isRequired;
  
  /// نص المساعدة
  final String? helperText;
  
  /// أيقونة الحقل
  final IconData? icon;

  const ResponsiveFormField({
    super.key,
    required this.label,
    required this.field,
    this.isRequired = false,
    this.helperText,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    
    if (isSmallScreen) {
      // عرض الحقل بشكل عمودي على الشاشات الصغيرة
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(icon, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
              ],
              Text(
                label + (isRequired ? ' *' : ''),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Colors.grey[800])),
            ]),
          const SizedBox(height: 8),
          field,
          if (helperText != null) ...[
            const SizedBox(height: 4),
            Text(
              helperText!,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic)),
          ],
          const SizedBox(height: 16),
        ]);
    } else {
      // عرض الحقل بشكل أفقي على الشاشات الكبيرة
      return Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 150,
              child: Row(
                children: [
                  if (icon != null) ...[
                    Icon(icon, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: Text(
                      label + (isRequired ? ' *' : ''),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.grey[800]))),
                ])),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  field,
                  if (helperText != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      helperText!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic)),
                  ],
                ])),
          ]));
    }
  }
}
