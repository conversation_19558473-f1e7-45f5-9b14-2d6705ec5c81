import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../api/api_client.dart';
import '../../../core/errors/exceptions.dart';
import '../../../domain/entities/ownership_verification.dart';
import '../../../domain/entities/property_license.dart';
import '../../../domain/entities/government_document.dart';

/// خدمة التكامل مع الخدمات الحكومية
class GovernmentService {
  final ApiClient _apiClient;
  final FlutterSecureStorage _secureStorage;
  final String _baseUrl;

  /// إنشاء خدمة التكامل مع الخدمات الحكومية
  GovernmentService({
    required ApiClient apiClient,
    required FlutterSecureStorage secureStorage,
    String? baseUrl,
  })  : _apiClient = apiClient,
        _secureStorage = secureStorage,
        _baseUrl = baseUrl ?? 'https://api.realestate.com/government';

  /// التحقق من ملكية العقار
  Future<OwnershipVerification> verifyOwnership({
    required String userId,
    required String estateId,
    required String ownerIdNumber,
    required String ownerName,
    required String propertyNumber,
    required String propertyType,
    required String area,
    required String address,
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.post(
        '$_baseUrl/ownership/verify',
        headers: {
          'Authorization': 'Bearer $authToken',
        },
        body: {
          'userId': userId,
          'estateId': estateId,
          'ownerIdNumber': ownerIdNumber,
          'ownerName': ownerName,
          'propertyNumber': propertyNumber,
          'propertyType': propertyType,
          'area': area,
          'address': address,
          'additionalInfo': additionalInfo,
        });

      return OwnershipVerification.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في التحقق من ملكية العقار: $e');
    }
  }

  /// الحصول على حالة التحقق من الملكية
  Future<OwnershipVerification> getOwnershipVerificationStatus(
      String verificationId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/ownership/verifications/$verificationId',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      return OwnershipVerification.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في الحصول على حالة التحقق من الملكية: $e');
    }
  }

  /// الحصول على عمليات التحقق من الملكية للمستخدم
  Future<List<OwnershipVerification>> getUserOwnershipVerifications(
      String userId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/ownership/verifications/user/$userId',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      if (response is List) {
        return response
            .map((verification) => OwnershipVerification.fromJson(verification))
            .toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException('Government Service',
          'فشل في الحصول على عمليات التحقق من الملكية للمستخدم: $e');
    }
  }

  /// التحقق من صحة شهادة الملكية
  Future<bool> verifyOwnershipCertificate({
    required String certificateId,
    required String verificationCode,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/ownership/certificates/verify',
        queryParameters: {
          'certificateId': certificateId,
          'verificationCode': verificationCode,
        });

      return response['isValid'] as bool;
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في التحقق من صحة شهادة الملكية: $e');
    }
  }

  /// تنزيل شهادة الملكية
  Future<String> downloadOwnershipCertificate(String verificationId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/ownership/verifications/$verificationId/certificate',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      return response['certificateUrl'] as String;
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في تنزيل شهادة الملكية: $e');
    }
  }

  /// التحقق من رخصة العقار
  Future<PropertyLicense> verifyPropertyLicense({
    required String userId,
    required String estateId,
    required String licenseNumber,
    required String propertyType,
    required String area,
    required String address,
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.post(
        '$_baseUrl/licenses/verify',
        headers: {
          'Authorization': 'Bearer $authToken',
        },
        body: {
          'userId': userId,
          'estateId': estateId,
          'licenseNumber': licenseNumber,
          'propertyType': propertyType,
          'area': area,
          'address': address,
          'additionalInfo': additionalInfo,
        });

      return PropertyLicense.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في التحقق من رخصة العقار: $e');
    }
  }

  /// الحصول على حالة التحقق من الرخصة
  Future<PropertyLicense> getPropertyLicenseStatus(String licenseId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/licenses/$licenseId',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      return PropertyLicense.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في الحصول على حالة التحقق من الرخصة: $e');
    }
  }

  /// الحصول على رخص العقارات للمستخدم
  Future<List<PropertyLicense>> getUserPropertyLicenses(String userId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/licenses/user/$userId',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      if (response is List) {
        return response
            .map((license) => PropertyLicense.fromJson(license))
            .toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في الحصول على رخص العقارات للمستخدم: $e');
    }
  }

  /// تحميل المستندات الحكومية
  Future<Map<String, String>> uploadGovernmentDocuments({
    required String userId,
    required String documentType,
    required Map<String, dynamic> documents,
  }) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.upload(
        '$_baseUrl/documents/upload',
        headers: {
          'Authorization': 'Bearer $authToken',
        },
        files: documents,
        body: {
          'userId': userId,
          'documentType': documentType,
        });

      return Map<String, String>.from(response);
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في تحميل المستندات الحكومية: $e');
    }
  }

  /// الحصول على المستندات الحكومية للمستخدم
  Future<List<GovernmentDocument>> getUserDocuments(String userId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/documents/user/$userId',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      if (response is List) {
        return response
            .map((document) => GovernmentDocument.fromJson(document))
            .toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException('Government Service',
          'فشل في الحصول على المستندات الحكومية للمستخدم: $e');
    }
  }

  /// التحقق من صحة المستند الحكومي
  Future<bool> verifyGovernmentDocument({
    required String documentId,
    required String verificationCode,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/documents/verify',
        queryParameters: {
          'documentId': documentId,
          'verificationCode': verificationCode,
        });

      return response['isValid'] as bool;
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في التحقق من صحة المستند الحكومي: $e');
    }
  }

  /// الحصول على معلومات المنطقة
  Future<Map<String, dynamic>> getAreaInformation(String areaCode) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/areas/$areaCode',
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return response;
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في الحصول على معلومات المنطقة: $e');
    }
  }

  /// الحصول على قائمة المناطق
  Future<List<Map<String, dynamic>>> getAreas() async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/areas',
        cache: true,
        cacheDuration: const Duration(hours: 24));

      if (response is List) {
        return response.map((area) => area as Map<String, dynamic>).toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException(
          'Government Service', 'فشل في الحصول على قائمة المناطق: $e');
    }
  }

  /// الحصول على معلومات الضرائب العقارية
  Future<Map<String, dynamic>> getPropertyTaxInformation({
    required String propertyNumber,
    required String area,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/taxes/property',
        queryParameters: {
          'propertyNumber': propertyNumber,
          'area': area,
        });

      return response;
    } catch (e) {
      throw IntegrationException('Government Service',
          'فشل في الحصول على معلومات الضرائب العقارية: $e');
    }
  }

  /// الحصول على معلومات التخطيط العمراني
  Future<Map<String, dynamic>> getUrbanPlanningInformation({
    required String areaCode,
    double? latitude,
    double? longitude,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/urban-planning',
        queryParameters: {
          'areaCode': areaCode,
          'latitude': latitude?.toString(),
          'longitude': longitude?.toString(),
        },
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return response;
    } catch (e) {
      throw IntegrationException('Government Service',
          'فشل في الحصول على معلومات التخطيط العمراني: $e');
    }
  }
}

/// امتدادات لتسهيل استخدام خدمة التكامل مع الخدمات الحكومية
extension GovernmentServiceExtensions on GovernmentService {
  /// الحصول على وصف حالة التحقق من الملكية
  String getOwnershipVerificationStatusDescription(String status) {
    switch (status) {
      case 'pending':
        return 'قيد المراجعة';
      case 'verified':
        return 'تم التحقق';
      case 'rejected':
        return 'مرفوض';
      case 'incomplete':
        return 'غير مكتمل';
      case 'awaiting_documents':
        return 'في انتظار المستندات';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على وصف حالة الرخصة
  String getLicenseStatusDescription(String status) {
    switch (status) {
      case 'active':
        return 'سارية';
      case 'expired':
        return 'منتهية';
      case 'suspended':
        return 'معلقة';
      case 'revoked':
        return 'ملغاة';
      case 'pending':
        return 'قيد المراجعة';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على وصف نوع المستند الحكومي
  String getDocumentTypeDescription(String type) {
    switch (type) {
      case 'ownership_deed':
        return 'صك ملكية';
      case 'property_license':
        return 'رخصة عقار';
      case 'building_permit':
        return 'رخصة بناء';
      case 'id_card':
        return 'بطاقة هوية';
      case 'commercial_register':
        return 'سجل تجاري';
      case 'tax_certificate':
        return 'شهادة ضريبية';
      default:
        return 'غير معروف';
    }
  }
}
