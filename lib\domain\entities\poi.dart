import 'package:equatable/equatable.dart';

/// كيان نقطة الاهتمام
class POI extends Equatable {
  final String id;
  final String name;
  final String category;
  final String? subcategory;
  final double latitude;
  final double longitude;
  final double distance;
  final String address;
  final String? phoneNumber;
  final String? website;
  final double? rating;
  final int? reviewsCount;
  final Map<String, dynamic>? operatingHours;
  final List<String>? photos;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان نقطة الاهتمام
  const POI({
    required this.id,
    required this.name,
    required this.category,
    this.subcategory,
    required this.latitude,
    required this.longitude,
    required this.distance,
    required this.address,
    this.phoneNumber,
    this.website,
    this.rating,
    this.reviewsCount,
    this.operatingHours,
    this.photos,
    this.additionalInfo,
  });

  /// إنشاء كيان نقطة الاهتمام من JSON
  factory POI.fromJson(Map<String, dynamic> json) {
    return POI(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      subcategory: json['subcategory'] as String?,
      latitude: json['latitude'] as double,
      longitude: json['longitude'] as double,
      distance: json['distance'] as double,
      address: json['address'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      website: json['website'] as String?,
      rating: json['rating'] as double?,
      reviewsCount: json['reviewsCount'] as int?,
      operatingHours: json['operatingHours'] as Map<String, dynamic>?,
      photos: json['photos'] != null
          ? List<String>.from(json['photos'] as List)
          : null,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان نقطة الاهتمام إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'subcategory': subcategory,
      'latitude': latitude,
      'longitude': longitude,
      'distance': distance,
      'address': address,
      'phoneNumber': phoneNumber,
      'website': website,
      'rating': rating,
      'reviewsCount': reviewsCount,
      'operatingHours': operatingHours,
      'photos': photos,
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان نقطة الاهتمام مع تعديل بعض الخصائص
  POI copyWith({
    String? id,
    String? name,
    String? category,
    String? subcategory,
    double? latitude,
    double? longitude,
    double? distance,
    String? address,
    String? phoneNumber,
    String? website,
    double? rating,
    int? reviewsCount,
    Map<String, dynamic>? operatingHours,
    List<String>? photos,
    Map<String, dynamic>? additionalInfo,
  }) {
    return POI(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      distance: distance ?? this.distance,
      address: address ?? this.address,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      website: website ?? this.website,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      operatingHours: operatingHours ?? this.operatingHours,
      photos: photos ?? this.photos,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// الحصول على وصف الفئة
  String getCategoryDescription() {
    switch (category) {
      case 'restaurant':
        return 'مطعم';
      case 'cafe':
        return 'مقهى';
      case 'school':
        return 'مدرسة';
      case 'hospital':
        return 'مستشفى';
      case 'pharmacy':
        return 'صيدلية';
      case 'mosque':
        return 'مسجد';
      case 'mall':
        return 'مركز تجاري';
      case 'park':
        return 'حديقة';
      case 'gym':
        return 'نادي رياضي';
      case 'bank':
        return 'بنك';
      case 'atm':
        return 'صراف آلي';
      case 'gas_station':
        return 'محطة وقود';
      case 'supermarket':
        return 'سوبر ماركت';
      case 'bakery':
        return 'مخبز';
      case 'police':
        return 'شرطة';
      default:
        return 'نقطة اهتمام';
    }
  }

  /// التحقق مما إذا كانت نقطة الاهتمام تعمل حاليًا
  bool isCurrentlyOperating() {
    if (operatingHours == null) {
      return true;
    }

    final now = DateTime.now();
    final dayOfWeek = now.weekday.toString();
    final currentHours = operatingHours![dayOfWeek] as Map<String, dynamic>?;

    if (currentHours == null) {
      return false;
    }

    final openTime = currentHours['open'] as String;
    final closeTime = currentHours['close'] as String;

    final openHour = int.parse(openTime.split(':')[0]);
    final openMinute = int.parse(openTime.split(':')[1]);
    final closeHour = int.parse(closeTime.split(':')[0]);
    final closeMinute = int.parse(closeTime.split(':')[1]);

    final openDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      openHour,
      openMinute);
    final closeDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      closeHour,
      closeMinute);

    return now.isAfter(openDateTime) && now.isBefore(closeDateTime);
  }

  /// الحصول على المسافة بصيغة مقروءة
  String getFormattedDistance() {
    if (distance < 1000) {
      return '${distance.toStringAsFixed(0)} متر';
    } else {
      return '${(distance / 1000).toStringAsFixed(1)} كم';
    }
  }

  /// الحصول على الصورة الرئيسية
  String? getMainPhoto() {
    return photos?.isNotEmpty == true ? photos!.first : null;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        category,
        subcategory,
        latitude,
        longitude,
        distance,
        address,
        phoneNumber,
        website,
        rating,
        reviewsCount,
        operatingHours,
        photos,
        additionalInfo,
      ];
}
