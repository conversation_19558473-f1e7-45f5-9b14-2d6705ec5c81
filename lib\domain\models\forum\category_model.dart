import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';

/// نموذج آخر مشاركة في الفئة
class LastPost extends Equatable {
  /// معرف الموضوع
  final String topicId;

  /// عنوان الموضوع
  final String topicTitle;

  /// معرف المستخدم
  final String userId;

  /// اسم المستخدم
  final String userName;

  /// صورة المستخدم
  final String? userImage;

  /// تاريخ المشاركة
  final DateTime timestamp;

  const LastPost({
    required this.topicId,
    required this.topicTitle,
    required this.userId,
    required this.userName,
    this.userImage,
    required this.timestamp,
  });

  /// إنشاء نسخة معدلة من آخر مشاركة
  LastPost copyWith({
    String? topicId,
    String? topicTitle,
    String? userId,
    String? userName,
    String? userImage,
    DateTime? timestamp,
  }) {
    return LastPost(
      topicId: topicId ?? this.topicId,
      topicTitle: topicTitle ?? this.topicTitle,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      timestamp: timestamp ?? this.timestamp);
  }

  /// تحويل آخر مشاركة إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'topicId': topicId,
      'topicTitle': topicTitle,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'timestamp': Timestamp.fromDate(timestamp),
    };
  }

  /// إنشاء آخر مشاركة من خريطة
  factory LastPost.fromMap(Map<String, dynamic> map) {
    return LastPost(
      topicId: map['topicId'] ?? '',
      topicTitle: map['topicTitle'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'],
      timestamp: (map['timestamp'] is Timestamp)
          ? (map['timestamp'] as Timestamp).toDate()
          : DateTime.now());
  }

  @override
  List<Object?> get props => [
        topicId,
        topicTitle,
        userId,
        userName,
        userImage,
        timestamp,
      ];
}

/// نوع الفئة
enum CategoryType {
  /// فئة عامة
  general,

  /// فئة عقارات
  realEstate,

  /// فئة عقارات (alias)
  property,

  /// فئة سيارات
  cars,

  /// فئة وظائف
  jobs,

  /// فئة خدمات
  services,

  /// فئة أسئلة وأجوبة
  qa,

  /// فئة نقاشات
  discussions,

  /// فئة أخبار
  news,

  /// فئة مخصصة
  custom,
}

/// نموذج فئة المنتدى
class CategoryModel extends Equatable {
  /// معرف الفئة
  final String id;

  /// اسم الفئة
  final String name;

  /// وصف الفئة
  final String description;

  /// رمز الفئة
  final String icon;

  /// لون الفئة
  final String color;

  /// ترتيب الفئة
  final int order;

  /// عدد المواضيع في الفئة
  final int topicsCount;

  /// عدد المشاركات في الفئة
  final int postsCount;

  /// آخر موضوع في الفئة
  final LastPost? lastPost;

  /// ما إذا كانت الفئة مميزة
  final bool isFeatured;

  /// ما إذا كانت الفئة مخفية
  final bool isHidden;

  /// الفئة الأب (إذا كانت فئة فرعية)
  final String? parentId;

  /// الفئات الفرعية
  final List<String>? subCategories;

  /// نوع الفئة
  final CategoryType type;

  /// الكلمات المفتاحية للفئة
  final List<String>? keywords;

  /// قواعد الفئة
  final String? rules;

  /// ما إذا كانت الفئة تتطلب موافقة للنشر
  final bool requiresApproval;

  /// تاريخ إنشاء الفئة
  final DateTime createdAt;

  /// تاريخ آخر تحديث للفئة
  final DateTime updatedAt;

  const CategoryModel({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.order,
    this.topicsCount = 0,
    this.postsCount = 0,
    this.lastPost,
    this.isFeatured = false,
    this.isHidden = false,
    this.parentId,
    this.subCategories,
    this.type = CategoryType.general,
    this.keywords,
    this.rules,
    this.requiresApproval = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء نسخة معدلة من الفئة
  CategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    String? icon,
    String? color,
    int? order,
    int? topicsCount,
    int? postsCount,
    LastPost? lastPost,
    bool? isFeatured,
    bool? isHidden,
    String? parentId,
    List<String>? subCategories,
    CategoryType? type,
    List<String>? keywords,
    String? rules,
    bool? requiresApproval,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      order: order ?? this.order,
      topicsCount: topicsCount ?? this.topicsCount,
      postsCount: postsCount ?? this.postsCount,
      lastPost: lastPost ?? this.lastPost,
      isFeatured: isFeatured ?? this.isFeatured,
      isHidden: isHidden ?? this.isHidden,
      parentId: parentId ?? this.parentId,
      subCategories: subCategories ?? this.subCategories,
      type: type ?? this.type,
      keywords: keywords ?? this.keywords,
      rules: rules ?? this.rules,
      requiresApproval: requiresApproval ?? this.requiresApproval,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt);
  }

  /// تحويل الفئة إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'color': color,
      'order': order,
      'topicsCount': topicsCount,
      'postsCount': postsCount,
      'lastPost': lastPost?.toMap(),
      'isFeatured': isFeatured,
      'isHidden': isHidden,
      'parentId': parentId,
      'subCategories': subCategories,
      'type': type.index,
      'keywords': keywords,
      'rules': rules,
      'requiresApproval': requiresApproval,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// إنشاء فئة من خريطة
  factory CategoryModel.fromMap(Map<String, dynamic> map) {
    return CategoryModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      icon: map['icon'] ?? 'category',
      color: map['color'] ?? '#4CAF50', // تغيير من الأزرق إلى الأخضر
      order: map['order'] ?? 0,
      topicsCount: map['topicsCount'] ?? 0,
      postsCount: map['postsCount'] ?? 0,
      lastPost:
          map['lastPost'] != null ? LastPost.fromMap(map['lastPost']) : null,
      isFeatured: map['isFeatured'] ?? false,
      isHidden: map['isHidden'] ?? false,
      parentId: map['parentId'],
      subCategories: map['subCategories'] != null
          ? List<String>.from(map['subCategories'])
          : null,
      type: map['type'] != null
          ? CategoryType.values[map['type']]
          : CategoryType.general,
      keywords:
          map['keywords'] != null ? List<String>.from(map['keywords']) : null,
      rules: map['rules'],
      requiresApproval: map['requiresApproval'] ?? false,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now());
  }

  /// إنشاء فئة من وثيقة فايربيز
  factory CategoryModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return CategoryModel(
        id: doc.id,
        name: '',
        description: '',
        icon: 'category',
        color: '#4CAF50', // تغيير من الأزرق إلى الأخضر
        order: 0,
        type: CategoryType.general,
        requiresApproval: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());
    }
    return CategoryModel.fromMap({...data, 'id': doc.id});
  }

  /// الحصول على لون الفئة ككائن Color
  Color getColorObject() {
    try {
      return Color(int.parse(color.replaceAll('#', '0xFF')));
    } catch (e) {
      return AppColors.primary; // تغيير من الأزرق إلى الأخضر
    }
  }

  /// زيادة عدد المواضيع
  CategoryModel incrementTopicsCount() {
    return copyWith(
      topicsCount: topicsCount + 1,
      updatedAt: DateTime.now());
  }

  /// تقليل عدد المواضيع
  CategoryModel decrementTopicsCount() {
    return copyWith(
      topicsCount: topicsCount > 0 ? topicsCount - 1 : 0,
      updatedAt: DateTime.now());
  }

  /// زيادة عدد المشاركات
  CategoryModel incrementPostsCount() {
    return copyWith(
      postsCount: postsCount + 1,
      updatedAt: DateTime.now());
  }

  /// تقليل عدد المشاركات
  CategoryModel decrementPostsCount() {
    return copyWith(
      postsCount: postsCount > 0 ? postsCount - 1 : 0,
      updatedAt: DateTime.now());
  }

  /// تحديث آخر موضوع
  CategoryModel updateLastPost({
    required String topicId,
    required String topicTitle,
    required String userId,
    required String userName,
    String? userImage,
  }) {
    return copyWith(
      lastPost: LastPost(
        topicId: topicId,
        topicTitle: topicTitle,
        userId: userId,
        userName: userName,
        userImage: userImage,
        timestamp: DateTime.now()),
      updatedAt: DateTime.now());
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        icon,
        color,
        order,
        topicsCount,
        postsCount,
        lastPost,
        isFeatured,
        isHidden,
        parentId,
        subCategories,
        createdAt,
        updatedAt,
      ];
}
