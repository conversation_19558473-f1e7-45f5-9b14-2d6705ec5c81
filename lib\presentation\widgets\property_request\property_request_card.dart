// lib/presentation/widgets/property_request/property_request_card.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/property_request/property_request_model.dart';

/// بطاقة طلب عقار
class PropertyRequestCard extends StatefulWidget {
  /// طلب العقار
  final PropertyRequestModel request;

  /// دالة عند النقر على البطاقة
  final VoidCallback onTap;

  /// دالة عند النقر على زر المراسلة
  final VoidCallback? onMessageTap;

  /// دالة عند النقر على زر التعليق
  final VoidCallback? onCommentTap;

  /// دالة عند النقر على زر العرض
  final VoidCallback? onOfferTap;

  /// هل البطاقة مختصرة
  final bool isCompact;

  const PropertyRequestCard({
    super.key,
    required this.request,
    required this.onTap,
    this.onMessageTap,
    this.onCommentTap,
    this.onOfferTap,
    this.isCompact = false,
  });

  @override
  State<PropertyRequestCard> createState() => _PropertyRequestCardState();
}

class _PropertyRequestCardState extends State<PropertyRequestCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, AppColors.orangeCardBackground],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withValues(alpha: 0.15),
            blurRadius: 15,
            offset: const Offset(0, 8)),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2)),
        ]),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(20),
          child: widget.isCompact ? _buildCompactView() : _buildFullView())));
  }

  /// بناء العرض المختصر
  Widget _buildCompactView() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // صورة الطلب
          if (widget.request.images != null && widget.request.images!.isNotEmpty)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CachedNetworkImage(
                imageUrl: widget.request.images!.first,
                width: 60,
                height: 60,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  width: 60,
                  height: 60,
                  color: Colors.grey.shade200,
                  child: const Icon(
                    Icons.image,
                    color: Colors.grey)),
                errorWidget: (context, url, error) => Container(
                  width: 60,
                  height: 60,
                  color: Colors.grey.shade200,
                  child: const Icon(
                    Icons.error,
                    color: Colors.red))))
          else
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8)),
              child: Icon(
                _getPropertyTypeIcon(widget.request.propertyType),
                color: Colors.grey)),
          const SizedBox(width: 12),

          // تفاصيل الطلب
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.request.title,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                    fontSize: 14),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
                const SizedBox(height: 4),
                Text(
                  _getPriceRange(widget.request.minPrice, widget.request.maxPrice),
                  style: GoogleFonts.cairo(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                    fontSize: 12)),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 12,
                      color: Colors.grey.shade600),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        widget.request.preferredLocations.join(' - '),
                        style: GoogleFonts.cairo(
                          color: Colors.grey.shade600,
                          fontSize: 12),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis)),
                  ]),
              ])),

          // حالة الطلب
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(widget.request.status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8)),
            child: Text(
              _getStatusText(widget.request.status),
              style: GoogleFonts.cairo(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: _getStatusColor(widget.request.status)))),
        ]));
  }

  /// بناء العرض المدمج والأنيق
  Widget _buildFullView() {
    return Column(
      children: [
        // الجزء الرئيسي للبطاقة
        SizedBox(
          height: 110, // ارتفاع مدمج وأنيق
          child: Row(
            children: [
          // الصورة
          SizedBox(
            width: 90,
            height: 110,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(20),
                bottomRight: Radius.circular(20)),
              child: Stack(
                children: [
                  if (widget.request.images != null && widget.request.images!.isNotEmpty)
                    CachedNetworkImage(
                      imageUrl: widget.request.images!.first,
                      width: 90,
                      height: 110,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        decoration: BoxDecoration(
                          gradient: AppColors.lightOrangeGradient),
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(25)),
                            child: const Icon(
                              Icons.image,
                              color: AppColors.primaryOrange,
                              size: 20)))),
                      errorWidget: (context, url, error) => Container(
                        decoration: BoxDecoration(
                          gradient: AppColors.lightOrangeGradient),
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(25)),
                            child: const Icon(
                              Icons.error,
                              color: Colors.red,
                              size: 20)))))
                  else
                    Container(
                      decoration: BoxDecoration(
                        gradient: AppColors.lightOrangeGradient),
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primaryOrange.withValues(alpha: 0.2),
                                blurRadius: 6,
                                offset: const Offset(0, 2)),
                            ]),
                          child: Icon(
                            _getPropertyTypeIcon(widget.request.propertyType),
                            color: AppColors.primaryOrange,
                            size: 24)))),

                  // حالة الطلب في الزاوية العلوية
                  Positioned(
                    top: 6,
                    left: 6,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 3),
                      decoration: BoxDecoration(
                        color: _getStatusColor(widget.request.status),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: _getStatusColor(widget.request.status).withValues(alpha: 0.3),
                            blurRadius: 3,
                            offset: const Offset(0, 1)),
                        ]),
                      child: Text(
                        _getStatusText(widget.request.status),
                        style: GoogleFonts.cairo(
                          fontSize: 9,
                          fontWeight: FontWeight.bold,
                          color: Colors.white)))),
                ]))),

          // المحتوى المبسط
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // الجزء العلوي - العنوان فقط
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان الطلب
                      Text(
                        widget.request.title,
                        style: GoogleFonts.cairo(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: AppColors.textPrimary),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis),
                      const SizedBox(height: 4),

                      // نوع العقار
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.primaryOrange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6)),
                        child: Text(
                          widget.request.propertyType,
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryOrange))),
                    ]),

                  // الجزء السفلي - السعر والمعلومات الأساسية
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // السعر والمنطقة
                      Row(
                        children: [
                          // السعر
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4),
                            decoration: BoxDecoration(
                              gradient: AppColors.orangeGradient,
                              borderRadius: BorderRadius.circular(8)),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.attach_money,
                                  size: 12,
                                  color: Colors.white),
                                const SizedBox(width: 2),
                                Text(
                                  _getPriceRange(widget.request.minPrice, widget.request.maxPrice),
                                  style: GoogleFonts.cairo(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white)),
                              ])),
                          const Spacer(),
                          // المنطقة
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.location_on,
                                size: 10,
                                color: AppColors.textSecondary),
                              const SizedBox(width: 2),
                              Text(
                                widget.request.preferredLocations.isNotEmpty
                                    ? widget.request.preferredLocations.first
                                    : 'غير محدد',
                                style: GoogleFonts.cairo(
                                  fontSize: 9,
                                  color: AppColors.textSecondary),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis),
                            ]),
                        ]),
                      const SizedBox(height: 6),

                      // معلومات المستخدم والوقت
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 8,
                            backgroundColor: AppColors.primaryOrange.withValues(alpha: 0.2),
                            backgroundImage: widget.request.userImage != null
                                ? NetworkImage(widget.request.userImage!)
                                : null,
                            child: widget.request.userImage == null
                                ? Icon(
                                    Icons.person,
                                    size: 8,
                                    color: AppColors.primaryOrange)
                                : null),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              widget.request.userName,
                              style: GoogleFonts.cairo(
                                fontSize: 9,
                                fontWeight: FontWeight.w600,
                                color: AppColors.textPrimary),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis)),
                          Text(
                            timeago.format(widget.request.createdAt, locale: 'ar'),
                            style: GoogleFonts.cairo(
                              fontSize: 8,
                              color: AppColors.textSecondary)),
                        ]),
                    ]),
                ]))),
            ])),

        // أزرار الإجراءات
        _buildActionButtons(),
      ]);
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20))),
      child: Row(
        children: [
          // زر المراسلة
          Expanded(
            child: _buildActionButton(
              icon: Icons.message_rounded,
              label: 'مراسلة',
              color: Colors.blue,
              onTap: widget.onMessageTap)),
          const SizedBox(width: 8),

          // زر التعليق
          Expanded(
            child: _buildActionButton(
              icon: Icons.comment_rounded,
              label: 'تعليق',
              color: Colors.green,
              onTap: widget.onCommentTap)),
          const SizedBox(width: 8),

          // زر العرض
          Expanded(
            child: _buildActionButton(
              icon: Icons.local_offer_rounded,
              label: 'عرض',
              color: AppColors.primaryOrange,
              onTap: widget.onOfferTap)),
        ]));
  }

  /// بناء زر إجراء واحد
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: color.withValues(alpha: 0.3),
              width: 1)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 16,
                color: color),
              const SizedBox(width: 4),
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: color)),
            ]))));
  }

  /// الحصول على نص حالة الطلب
  String _getStatusText(RequestStatus status) {
    switch (status) {
      case RequestStatus.open:
        return 'مفتوح';
      case RequestStatus.closed:
        return 'مغلق';
      case RequestStatus.resolved:
        return 'تم الحل';
    }
  }

  /// الحصول على لون حالة الطلب
  Color _getStatusColor(RequestStatus status) {
    switch (status) {
      case RequestStatus.open:
        return Colors.green;
      case RequestStatus.closed:
        return Colors.red;
      case RequestStatus.resolved:
        return Colors.blue;
    }
  }

  /// الحصول على أيقونة نوع العقار
  IconData _getPropertyTypeIcon(String propertyType) {
    switch (propertyType) {
      case 'شقة':
        return Icons.apartment;
      case 'فيلا':
        return Icons.home;
      case 'أرض':
        return Icons.landscape;
      case 'مكتب':
        return Icons.business;
      case 'محل تجاري':
        return Icons.storefront;
      case 'مخزن':
        return Icons.warehouse;
      default:
        return Icons.home_work;
    }
  }

  /// الحصول على نطاق السعر
  String _getPriceRange(double? minPrice, double? maxPrice) {
    if (minPrice != null && maxPrice != null) {
      return '${minPrice.toStringAsFixed(0)} - ${maxPrice.toStringAsFixed(0)} د.ك';
    } else if (minPrice != null) {
      return 'من ${minPrice.toStringAsFixed(0)} د.ك';
    } else if (maxPrice != null) {
      return 'حتى ${maxPrice.toStringAsFixed(0)} د.ك';
    }
    return 'السعر غير محدد';
  }


}
