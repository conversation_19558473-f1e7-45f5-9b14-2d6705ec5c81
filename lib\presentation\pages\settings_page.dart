// lib/presentation/pages/settings_page.dart
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/services/loyalty_program_service.dart';
import 'package:kuwait_corners/core/services/social_sharing_service.dart';
import 'package:kuwait_corners/core/services/settings_service.dart';
import 'package:kuwait_corners/core/models/app_settings.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/core/services/enhanced_logout_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:kuwait_corners/presentation/pages/support_page.dart';
import 'package:kuwait_corners/presentation/pages/settings/notification_settings_page.dart';
import 'package:kuwait_corners/presentation/pages/settings/privacy_settings_page.dart';
import 'package:kuwait_corners/presentation/pages/settings/performance_settings_page.dart';
import 'package:kuwait_corners/presentation/pages/settings/about_page.dart';
import 'package:kuwait_corners/presentation/pages/legal/terms_and_conditions_page.dart';
import 'package:kuwait_corners/presentation/pages/legal/privacy_policy_page.dart';
import 'package:kuwait_corners/presentation/providers/theme_provider.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final SettingsService _settingsService = SettingsService();
  late AppSettings _settings;
  bool _isLoading = true;
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadAppInfo();
  }

  // تحميل الإعدادات
  Future<void> _loadSettings() async {
    await _settingsService.initialize();
    setState(() {
      _settings = _settingsService.currentSettings;
      _isLoading = false;
    });
  }

  // تحميل معلومات التطبيق
  Future<void> _loadAppInfo() async {
    try {
      final appInfo = await _settingsService.getAppInfo();
      setState(() {
        _appVersion = '${appInfo['version']} (${appInfo['buildNumber']})';
      });
    } catch (e) {
      setState(() {
        _appVersion = 'غير متوفر';
      });
    }
  }

  // تبديل الوضع الليلي
  Future<void> _toggleDarkMode(bool value) async {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final themeMode = value ? ThemeMode.dark : ThemeMode.light;

    // تحديث ThemeProvider
    await themeProvider.setThemeMode(themeMode);

    // تحديث SettingsService أيضاً للحفاظ على التزامن
    await _settingsService.updateThemeSettings(themeMode: themeMode);
    setState(() {
      _settings = _settings.copyWith(
        themeSettings: _settings.themeSettings.copyWith(themeMode: themeMode),
      );
    });
  }

  // تبديل تفعيل الإشعارات
  Future<void> _toggleNotifications(bool value) async {
    await _settingsService.updateNotificationSettings(pushNotifications: value);
    setState(() {
      _settings = _settings.copyWith(
        notificationSettings: _settings.notificationSettings.copyWith(pushNotifications: value),
      );
    });
  }

  // تبديل تفعيل الموقع
  Future<void> _toggleLocation(bool value) async {
    await _settingsService.updatePrivacySettings(locationServices: value);
    setState(() {
      _settings = _settings.copyWith(
        privacySettings: _settings.privacySettings.copyWith(locationServices: value),
      );
    });
  }

  // تبديل حفظ سجل البحث
  Future<void> _toggleSaveSearchHistory(bool value) async {
    await _settingsService.updatePrivacySettings(saveSearchHistory: value);
    setState(() {
      _settings = _settings.copyWith(
        privacySettings: _settings.privacySettings.copyWith(saveSearchHistory: value),
      );
    });
  }

  // تغيير اللغة (مُعطل حسب المتطلبات - العربية فقط)
  void _changeLanguage(String? language) {
    // اللغة ثابتة على العربية حسب المتطلبات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('التطبيق يدعم اللغة العربية فقط حالياً'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  // إعادة تعيين الإعدادات
  Future<void> _resetSettings() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(
          "إعادة تعيين الإعدادات",
          style: CairoTextStyles.titleMedium.copyWith(color: AppColors.warning),
        ),
        content: Text(
          "هل أنت متأكد من رغبتك في إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
          style: CairoTextStyles.bodyMedium,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx, false),
            child: Text(
              "إلغاء",
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey.shade700),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(ctx, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.warning,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            child: Text("إعادة تعيين", style: CairoTextStyles.bodyMedium),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await _settingsService.resetToDefaults();
        await _loadSettings();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إعادة تعيين الإعدادات بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في إعادة تعيين الإعدادات: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // مسح بيانات التطبيق
  Future<void> _clearAppData() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(
          "مسح بيانات التطبيق",
          style: CairoTextStyles.titleMedium.copyWith(color: AppColors.error),
        ),
        content: Text(
          "هل أنت متأكد من رغبتك في مسح جميع البيانات المحلية؟ هذا الإجراء لا يمكن التراجع عنه.",
          style: CairoTextStyles.bodyMedium,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx, false),
            child: Text(
              "إلغاء",
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey.shade700),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(ctx, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            child: Text("مسح البيانات", style: CairoTextStyles.bodyMedium),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await _settingsService.clearAppData();
        await _loadSettings();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم مسح بيانات التطبيق بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في مسح بيانات التطبيق: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// تسجيل الخروج المحسن باستخدام الخدمة المتقدمة
  Future<void> _performEnhancedLogout() async {
    final logoutService = EnhancedLogoutService();
    await logoutService.performComprehensiveLogout(
      context: context,
      showConfirmationDialog: true,
      clearCache: true,
      logActivity: true,
    );
  }

  /// تسجيل الخروج الاحترافي مع حوار التأكيد (للاحتفاظ بالتوافق)
  Future<void> _performProfessionalLogout() async {
    // عرض حوار التأكيد
    final bool? shouldLogout = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(
                Icons.logout,
                color: AppColors.primary,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'تسجيل الخروج',
                style: CairoTextStyles.headlineSmall.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          content: Text(
            'هل أنت متأكد من رغبتك في تسجيل الخروج؟\n\nسيتم إلغاء تفعيل خاصية "تذكرني" وستحتاج لإدخال بيانات الدخول مرة أخرى.',
            style: CairoTextStyles.bodyMedium.copyWith(
              height: 1.5,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: Text(
                'إلغاء',
                style: CairoTextStyles.button.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'تسجيل الخروج',
                style: CairoTextStyles.button,
              ),
            ),
          ],
        );
      },
    );

    // إذا أكد المستخدم تسجيل الخروج
    if (shouldLogout == true && mounted) {
      try {
        // عرض مؤشر التحميل
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext loadingContext) {
            return Center(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'جاري تسجيل الخروج...',
                      style: CairoTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );

        // تسجيل الخروج من Firebase
        await FirebaseAuth.instance.signOut();

        // مسح بيانات "تذكرني" من التخزين المحلي
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('rememberMe', false);
        await prefs.remove('savedUserType');

        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        // عرض رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 12),
                  Text(
                    'تم تسجيل الخروج بنجاح',
                    style: CairoTextStyles.bodyMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }

        // التوجه لصفحة تسجيل الدخول
        if (mounted) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            '/login',
            (route) => false,
          );
        }
      } catch (e) {
        // إغلاق مؤشر التحميل في حالة الخطأ
        if (mounted) {
          Navigator.of(context).pop();
        }

        // عرض رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'حدث خطأ أثناء تسجيل الخروج: $e',
                      style: CairoTextStyles.bodyMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      }
    }
  }

  // مشاركة التطبيق
  Future<void> _shareApp() async {
    final socialSharingService = SocialSharingService();
    await socialSharingService.shareApp();

    // عرض رسالة نجاح
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تمت مشاركة التطبيق وإضافة 15 نقطة إلى رصيدك!'),
          backgroundColor: Colors.green));
    }
  }

  // فتح صفحة التقييم
  Future<void> _rateApp() async {
    final Uri url = Uri.parse(
        'https://play.google.com/store/apps/details?id=com.example.kuwait_corners');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);

      // إضافة نقاط برنامج الولاء للمستخدم
      final loyaltyService = LoyaltyProgramService();
      await loyaltyService.addPointsForRatingApp();

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('شكراً لتقييمك! تم إضافة 25 نقطة إلى رصيدك'),
            backgroundColor: Colors.green));
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا يمكن فتح متجر التطبيقات')));
      }
    }
  }

  // فتح صفحة الدعم داخل التطبيق
  void _openSupport() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SupportPage()));

    // إضافة نقاط برنامج الولاء للمستخدم عند زيارة صفحة الدعم
    final loyaltyService = LoyaltyProgramService();
    loyaltyService.addPointsForVisitingSupport();

    // عرض رسالة نجاح
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('شكراً لزيارة صفحة الدعم! تم إضافة 5 نقاط إلى رصيدك'),
          backgroundColor: Colors.green));
    }
  }

  // فتح صفحة سياسة الخصوصية داخل التطبيق
  void _openPrivacyPolicy() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PrivacyPolicyPage()));

    // إضافة نقاط برنامج الولاء للمستخدم عند قراءة سياسة الخصوصية
    final loyaltyService = LoyaltyProgramService();
    loyaltyService.addPointsForReadingPolicy();

    // عرض رسالة نجاح
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('شكراً لقراءة سياسة الخصوصية! تم إضافة 5 نقاط إلى رصيدك'),
          backgroundColor: Colors.green));
    }
  }

  // فتح صفحة شروط الاستخدام داخل التطبيق
  void _openTermsOfService() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const TermsAndConditionsPage()));

    // إضافة نقاط برنامج الولاء للمستخدم عند قراءة شروط الاستخدام
    final loyaltyService = LoyaltyProgramService();
    loyaltyService.addPointsForReadingTerms();

    // عرض رسالة نجاح
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('شكراً لقراءة شروط الاستخدام! تم إضافة 5 نقاط إلى رصيدك'),
          backgroundColor: Colors.green));
    }
  }

  // حذف الحساب مع تأكيد
  Future<void> _deleteAccount() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(
          "تأكيد حذف الحساب",
          style: CairoTextStyles.titleMedium.copyWith(
            color: AppColors.error)),
        content: Text(
          "هل أنت متأكد من رغبتك في حذف الحساب؟ هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بياناتك.",
          style: CairoTextStyles.bodyMedium),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx, false),
            child: Text(
              "إلغاء",
              style: CairoTextStyles.bodyMedium.copyWith(
                color: Colors.grey.shade700))),
          ElevatedButton(
            onPressed: () => Navigator.pop(ctx, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8))),
            child: Text(
              "حذف الحساب",
              style: CairoTextStyles.bodyMedium)),
        ]));
    if (confirm == true) {
      setState(() {
        _isLoading = true;
      });
      try {
        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          await user.delete();
          // من الممكن أيضاً حذف بيانات المستخدم من Firestore.
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  "تم حذف الحساب بنجاح",
                  style: CairoTextStyles.bodyMedium),
                backgroundColor: AppColors.success));
            // الانتقال إلى صفحة الدخول أو صفحة Onboarding.
            Navigator.of(context).popUntil((route) => route.isFirst);
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                "حدث خطأ أثناء حذف الحساب: $e",
                style: CairoTextStyles.bodyMedium),
              backgroundColor: AppColors.error));
        }
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // بناء عنصر قسم
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: CairoTextStyles.headlineSmall.copyWith(
          color: AppColors.primary)));
  }

  // بناء عنصر قائمة بسيط
  Widget _buildListItem({
    required IconData icon,
    required String title,
    String? subtitle,
    Color? iconColor,
    VoidCallback? onTap,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (iconColor ?? AppColors.primary).withAlpha(26), // alpha 26 ≈ 10%
          borderRadius: BorderRadius.circular(8)),
        child: Icon(
          icon,
          color: iconColor ?? AppColors.primary)),
      title: Text(
        title,
        style: CairoTextStyles.titleMedium),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: CairoTextStyles.bodySmall.copyWith(
                color: Colors.grey.shade600))
          : null,
      trailing: trailing,
      onTap: onTap);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "الإعدادات",
          style: CairoTextStyles.appBarTitle),
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.black87),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              children: [
                // معلومات المستخدم
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withAlpha(77), // alpha 77 ≈ 30%
                        blurRadius: 10,
                        offset: const Offset(0, 4)),
                    ]),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: Colors.white,
                        child: Icon(
                          Icons.person,
                          size: 36,
                          color: AppColors.primary)),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              FirebaseAuth.instance.currentUser?.displayName ??
                                  "مستخدم",
                              style: CairoTextStyles.headlineSmall.copyWith(
                                color: Colors.white)),
                            Text(
                              FirebaseAuth.instance.currentUser?.email ??
                                  "بريد إلكتروني غير متوفر",
                              style: CairoTextStyles.bodyMedium.copyWith(
                                color: Colors.white.withAlpha(204), // alpha 204 ≈ 80%
                              )),
                          ])),
                      IconButton(
                        icon: const Icon(
                          Icons.edit,
                          color: Colors.white),
                        onPressed: () {
                          // انتقل إلى صفحة تعديل الملف الشخصي
                        }),
                    ])),

                // قسم المظهر
                _buildSectionTitle("المظهر واللغة"),
                SwitchListTile(
                  title: Text(
                    "الوضع الليلي",
                    style: CairoTextStyles.titleMedium),
                  subtitle: Text(
                    "تفعيل المظهر الداكن للتطبيق",
                    style: CairoTextStyles.bodySmall.copyWith(
                      color: Colors.grey.shade600)),
                  value: _settings.themeSettings.themeMode == ThemeMode.dark,
                  onChanged: _toggleDarkMode,
                  secondary: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(26), // alpha 26 ≈ 10%
                      borderRadius: BorderRadius.circular(8)),
                    child: Icon(
                      Icons.dark_mode,
                      color: AppColors.primary))),
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(26), // alpha 26 ≈ 10%
                      borderRadius: BorderRadius.circular(8)),
                    child: Icon(
                      Icons.language,
                      color: AppColors.primary)),
                  title: Text(
                    "اللغة",
                    style: CairoTextStyles.titleMedium),
                  subtitle: Text(
                    "العربية (ثابت)",
                    style: CairoTextStyles.bodySmall.copyWith(
                      color: Colors.grey.shade600)),
                  trailing: const Icon(Icons.lock, size: 16, color: Colors.grey),
                  onTap: () => _changeLanguage(null)),

                const Divider(),

                // قسم الإعدادات الرئيسية
                _buildSectionTitle("الإعدادات الرئيسية"),
                _buildListItem(
                  icon: Icons.notifications,
                  title: "إعدادات الإشعارات",
                  subtitle: "إدارة جميع أنواع الإشعارات",
                  iconColor: AppColors.info,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const NotificationSettingsPage()),
                  ),
                ),
                _buildListItem(
                  icon: Icons.privacy_tip,
                  title: "الخصوصية والأمان",
                  subtitle: "إعدادات الخصوصية والحماية",
                  iconColor: AppColors.warning,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const PrivacySettingsPage()),
                  ),
                ),
                _buildListItem(
                  icon: Icons.speed,
                  title: "الأداء والتخزين",
                  subtitle: "إعدادات الأداء والتخزين المؤقت",
                  iconColor: AppColors.success,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const PerformanceSettingsPage()),
                  ),
                ),

                const Divider(),

                // إعدادات سريعة
                _buildSectionTitle("إعدادات سريعة"),
                SwitchListTile(
                  title: Text(
                    "تفعيل الإشعارات",
                    style: CairoTextStyles.titleMedium),
                  subtitle: Text(
                    "استلام إشعارات حول العقارات الجديدة والعروض",
                    style: CairoTextStyles.bodySmall.copyWith(
                      color: Colors.grey.shade600)),
                  value: _settings.notificationSettings.pushNotifications,
                  onChanged: _toggleNotifications,
                  secondary: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.info.withAlpha(26), // alpha 26 ≈ 10%
                      borderRadius: BorderRadius.circular(8)),
                    child: Icon(
                      Icons.notifications,
                      color: AppColors.info))),
                SwitchListTile(
                  title: Text(
                    "تفعيل خدمات الموقع",
                    style: CairoTextStyles.titleMedium),
                  subtitle: Text(
                    "السماح للتطبيق باستخدام موقعك لعرض العقارات القريبة",
                    style: CairoTextStyles.bodySmall.copyWith(
                      color: Colors.grey.shade600)),
                  value: _settings.privacySettings.locationServices,
                  onChanged: _toggleLocation,
                  secondary: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.info.withAlpha(26), // alpha 26 ≈ 10%
                      borderRadius: BorderRadius.circular(8)),
                    child: Icon(
                      Icons.location_on,
                      color: AppColors.info))),
                SwitchListTile(
                  title: Text(
                    "حفظ سجل البحث",
                    style: CairoTextStyles.titleMedium),
                  subtitle: Text(
                    "حفظ عمليات البحث السابقة لتسهيل الوصول إليها",
                    style: CairoTextStyles.bodySmall.copyWith(
                      color: Colors.grey.shade600)),
                  value: _settings.privacySettings.saveSearchHistory,
                  onChanged: _toggleSaveSearchHistory,
                  secondary: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.info.withAlpha(26), // alpha 26 ≈ 10%
                      borderRadius: BorderRadius.circular(8)),
                    child: Icon(
                      Icons.history,
                      color: AppColors.info))),

                const Divider(),

                // قسم الدعم والمساعدة
                _buildSectionTitle("الدعم والمساعدة"),
                _buildListItem(
                  icon: Icons.share,
                  title: "مشاركة التطبيق",
                  subtitle: "شارك التطبيق مع أصدقائك وعائلتك",
                  iconColor: AppColors.secondary,
                  onTap: _shareApp),
                _buildListItem(
                  icon: Icons.star,
                  title: "تقييم التطبيق",
                  subtitle: "قم بتقييم التطبيق على متجر التطبيقات",
                  iconColor: AppColors.warning,
                  onTap: _rateApp),
                _buildListItem(
                  icon: Icons.support_agent,
                  title: "الدعم الفني",
                  subtitle: "تواصل مع فريق الدعم للمساعدة",
                  iconColor: AppColors.info,
                  onTap: _openSupport),
                _buildListItem(
                  icon: Icons.privacy_tip,
                  title: "سياسة الخصوصية",
                  iconColor: AppColors.secondary,
                  onTap: _openPrivacyPolicy),
                _buildListItem(
                  icon: Icons.description,
                  title: "شروط الاستخدام",
                  iconColor: AppColors.secondary,
                  onTap: _openTermsOfService),
                _buildListItem(
                  icon: Icons.info,
                  title: "عن التطبيق",
                  subtitle: "الإصدار $_appVersion",
                  iconColor: AppColors.primary,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const AboutPage()),
                  ),
                ),

                const Divider(),

                // قسم الحساب والبيانات
                _buildSectionTitle("الحساب والبيانات"),
                _buildListItem(
                  icon: Icons.refresh,
                  title: "إعادة تعيين الإعدادات",
                  subtitle: "استعادة الإعدادات الافتراضية",
                  iconColor: AppColors.warning,
                  onTap: _resetSettings,
                ),
                _buildListItem(
                  icon: Icons.clear_all,
                  title: "مسح بيانات التطبيق",
                  subtitle: "مسح جميع البيانات المحلية",
                  iconColor: AppColors.error,
                  onTap: _clearAppData,
                ),
                _buildListItem(
                  icon: Icons.logout,
                  title: "تسجيل الخروج",
                  iconColor: Colors.orange,
                  onTap: () {
                    _performEnhancedLogout();
                  }),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: ElevatedButton.icon(
                    onPressed: _deleteAccount,
                    icon: const Icon(Icons.delete_forever),
                    label: Text(
                      "حذف الحساب",
                      style: CairoTextStyles.bodyMedium),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12))))),

                // معلومات الشركة
                Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      "© 2025 Krea. جميع الحقوق محفوظة",
                      style: CairoTextStyles.bodySmall.copyWith(
                        color: Colors.grey.shade600),
                      textAlign: TextAlign.center))),
              ]));
  }
}
