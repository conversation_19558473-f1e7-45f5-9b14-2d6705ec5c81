import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/entities/price_estimation.dart';
import '../../../domain/repositories/analytics_repository.dart';

/// شاشة تقدير الأسعار
class PriceEstimationScreen extends StatefulWidget {
  /// إنشاء شاشة تقدير الأسعار
  const PriceEstimationScreen({super.key});

  @override
  State<PriceEstimationScreen> createState() => _PriceEstimationScreenState();
}

class _PriceEstimationScreenState extends State<PriceEstimationScreen> {
  final _formKey = GlobalKey<FormState>();

  // بيانات النموذج
  PriceEstimationType _type = PriceEstimationType.sale;
  String _area = '';
  String _propertyType = '';
  double _size = 0;
  int? _rooms;
  int? _bathrooms;
  int? _age;
  int? _floor;
  bool? _isFurnished;
  bool? _isRenovated;
  final List<String> _features = [];

  bool _isLoading = false;
  String? _errorMessage;
  PriceEstimation? _estimation;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقدير الأسعار'),
        centerTitle: true),
      body: _estimation == null ? _buildForm() : _buildResult());
  }

  /// بناء نموذج التقدير
  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // نوع التقدير
            const Text(
              'نوع التقدير',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<PriceEstimationType>(
                    title: const Text('بيع'),
                    value: PriceEstimationType.sale,
                    groupValue: _type,
                    onChanged: (value) {
                      setState(() {
                        _type = value!;
                      });
                    })),
                Expanded(
                  child: RadioListTile<PriceEstimationType>(
                    title: const Text('إيجار'),
                    value: PriceEstimationType.rent,
                    groupValue: _type,
                    onChanged: (value) {
                      setState(() {
                        _type = value!;
                      });
                    })),
              ]),
            const SizedBox(height: 16),

            // المنطقة
            const Text(
              'المنطقة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8)),
              hint: const Text('اختر المنطقة'),
              items: [
                'الرياض',
                'جدة',
                'الدمام',
                'مكة',
                'المدينة',
                'الخبر',
                'الظهران',
                'أبها',
                'الطائف',
                'تبوك',
              ]
                  .map((area) => DropdownMenuItem<String>(
                        value: area,
                        child: Text(area)))
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _area = value!;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار المنطقة';
                }
                return null;
              }),
            const SizedBox(height: 16),

            // نوع العقار
            const Text(
              'نوع العقار',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8)),
              hint: const Text('اختر نوع العقار'),
              items: [
                'شقة',
                'منزل',
                'بيت',
                'أرض',
                'عمارة',
                'مكتب',
                'محل تجاري',
                'مخزن',
              ]
                  .map((type) => DropdownMenuItem<String>(
                        value: type,
                        child: Text(type)))
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _propertyType = value!;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار نوع العقار';
                }
                return null;
              }),
            const SizedBox(height: 16),

            // المساحة
            const Text(
              'المساحة (متر مربع)',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            TextFormField(
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                hintText: 'أدخل المساحة',
                suffixText: 'م²'),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                setState(() {
                  _size = double.tryParse(value) ?? 0;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال المساحة';
                }
                if (double.tryParse(value) == null ||
                    double.parse(value) <= 0) {
                  return 'يرجى إدخال مساحة صحيحة';
                }
                return null;
              }),
            const SizedBox(height: 16),

            // عدد الغرف
            if (_propertyType != 'أرض')
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'عدد الغرف',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  TextFormField(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      hintText: 'أدخل عدد الغرف'),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _rooms = int.tryParse(value);
                      });
                    }),
                  const SizedBox(height: 16),
                ]),

            // عدد الحمامات
            if (_propertyType != 'أرض')
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'عدد الحمامات',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  TextFormField(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      hintText: 'أدخل عدد الحمامات'),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _bathrooms = int.tryParse(value);
                      });
                    }),
                  const SizedBox(height: 16),
                ]),

            // عمر العقار
            if (_propertyType != 'أرض')
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'عمر العقار (سنوات)',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  TextFormField(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      hintText: 'أدخل عمر العقار'),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _age = int.tryParse(value);
                      });
                    }),
                  const SizedBox(height: 16),
                ]),

            // مفروش
            if (_propertyType != 'أرض')
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'هل العقار مفروش؟',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<bool>(
                          title: const Text('نعم'),
                          value: true,
                          groupValue: _isFurnished,
                          onChanged: (value) {
                            setState(() {
                              _isFurnished = value;
                            });
                          })),
                      Expanded(
                        child: RadioListTile<bool>(
                          title: const Text('لا'),
                          value: false,
                          groupValue: _isFurnished,
                          onChanged: (value) {
                            setState(() {
                              _isFurnished = value;
                            });
                          })),
                    ]),
                  const SizedBox(height: 16),
                ]),

            // زر التقدير
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _estimatePrice,
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white))
                    : const Text('تقدير السعر'))),

            // رسالة الخطأ
            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(
                    color: Colors.red))),
          ])));
  }

  /// بناء نتيجة التقدير
  Widget _buildResult() {
    if (_estimation == null) {
      return const Center(
        child: Text('لا توجد نتائج'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // نتيجة التقدير
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'نتيجة التقدير',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Column(
                        children: [
                          Text(
                            _estimation!.type == PriceEstimationType.sale
                                ? 'السعر المقدر للبيع'
                                : 'السعر المقدر للإيجار',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600])),
                          const SizedBox(height: 8),
                          Text(
                            '${_formatPrice(_estimation!.estimatedValue)} ريال',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.green)),
                          const SizedBox(height: 4),
                          Text(
                            _estimation!.type == PriceEstimationType.sale
                                ? '${_formatPrice(_estimation!.estimatedValue / _estimation!.size)} ريال/م²'
                                : '${_formatPrice(_estimation!.estimatedValue / 12)} ريال/شهر',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600])),
                        ]),
                    ]),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildPriceColumn(
                        'الحد الأدنى',
                        _estimation!.minValue,
                        Colors.orange),
                      _buildPriceColumn(
                        'الحد الأقصى',
                        _estimation!.maxValue,
                        Colors.blue),
                    ]),
                  const SizedBox(height: 16),
                  LinearProgressIndicator(
                    value: _getConfidenceValue(_estimation!.accuracy),
                    backgroundColor: Colors.grey[200],
                    color: Colors.green),
                  const SizedBox(height: 8),
                  Text(
                    'مستوى الثقة: ${_getConfidenceText(_estimation!.accuracy)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600])),
                ]))),
          const SizedBox(height: 24),

          // العوامل المؤثرة
          const Text(
            'العوامل المؤثرة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          ..._buildFactorsList(),
          const SizedBox(height: 24),

          // زر العودة
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  _estimation = null;
                });
              },
              child: const Text('تقدير جديد'))),
        ]));
  }

  /// بناء عمود السعر
  Widget _buildPriceColumn(String label, double price, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600])),
        const SizedBox(height: 8),
        Text(
          '${_formatPrice(price)} ريال',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color)),
      ]);
  }

  /// بناء عنصر العامل
  Widget _buildFactorItem(String name, double impact, String description) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.blue.withAlpha(30),
                shape: BoxShape.circle),
              child: Center(
                child: Text(
                  '${(impact * 100).toStringAsFixed(0)}%',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue)))),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600])),
                ])),
          ])));
  }

  /// تنسيق السعر
  String _formatPrice(double price) {
    return price.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},');
  }

  /// تقدير السعر
  Future<void> _estimatePrice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final analyticsRepository = context.read<AnalyticsRepository>();
      final userId = 'user_123'; // يجب استبداله بمعرف المستخدم الحقيقي

      final estimation = await analyticsRepository.estimatePrice(
        userId: userId,
        type: _type,
        area: _area,
        propertyType: _propertyType,
        size: _size,
        rooms: _rooms,
        bathrooms: _bathrooms,
        age: _age,
        floor: _floor,
        isFurnished: _isFurnished,
        isRenovated: _isRenovated,
        features: _features);

      setState(() {
        _estimation = estimation;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تقدير السعر: $e';
        _isLoading = false;
      });
    }
  }

  /// الحصول على قيمة مستوى الثقة
  double _getConfidenceValue(EstimationAccuracy accuracy) {
    switch (accuracy) {
      case EstimationAccuracy.low:
        return 0.3;
      case EstimationAccuracy.medium:
        return 0.6;
      case EstimationAccuracy.high:
        return 0.8;
      case EstimationAccuracy.veryHigh:
        return 0.95;
    }
  }

  /// الحصول على نص مستوى الثقة
  String _getConfidenceText(EstimationAccuracy accuracy) {
    switch (accuracy) {
      case EstimationAccuracy.low:
        return 'منخفض (30%)';
      case EstimationAccuracy.medium:
        return 'متوسط (60%)';
      case EstimationAccuracy.high:
        return 'عالي (80%)';
      case EstimationAccuracy.veryHigh:
        return 'عالي جداً (95%)';
    }
  }

  /// بناء قائمة العوامل المؤثرة
  List<Widget> _buildFactorsList() {
    List<Widget> factorWidgets = [];

    _estimation!.factors.forEach((key, value) {
      factorWidgets.add(_buildFactorItem(
        key,
        value,
        'عامل مؤثر في تقدير السعر'));
    });

    return factorWidgets;
  }
}
