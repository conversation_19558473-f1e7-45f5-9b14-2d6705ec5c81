import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:kuwait_corners/core/services/loyalty_program_service.dart';
import '../repositories/estate_repository.dart';
import '../entities/estate.dart';
import '../entities/estate_converter.dart';

/// حالة استخدام لإنشاء إعلان جديد
class CreateEstateNew {
  final EstateRepository repository;
  final FirebaseFirestore firestore;
  final LoyaltyProgramService loyaltyService;

  CreateEstateNew(this.repository, this.firestore,
      {LoyaltyProgramService? loyaltyService})
      : loyaltyService = loyaltyService ?? LoyaltyProgramService();

  /// يقوم بإنشاء إعلان جديد مع دمج بيانات المُعلِن من مجموعة "users"
  /// [estate] هو العقار المراد إنشاؤه
  /// [userId] هو معرف المستخدم الحالي
  /// يعيد معرف العقار الجديد
  Future<String> call(Estate estate, String userId) async {
    print('🔄 CreateEstateNew: بدء عملية إنشاء العقار...');
    print('👤 معرف المستخدم: $userId');

    // جلب بيانات المستخدم من مجموعة "users"
    print('📋 جلب بيانات المستخدم من Firestore...');
    final userDoc = await firestore.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      print('❌ بيانات المستخدم غير موجودة');
      throw Exception("بيانات المستخدم غير موجودة");
    }

    final userData = userDoc.data()!;
    print('✅ تم جلب بيانات المستخدم بنجاح');

    // استخراج بيانات المُعلِن
    final advertiserImage = userData['profileImageUrl'] as String?;
    final advertiserName = userData['fullNameOrCompanyName'] as String?;
    // تجربة كلا الحقلين للهاتف
    final advertiserPhone = userData['phone'] as String? ?? userData['phoneNumber'] as String?;
    final advertiserType = userData['userType'] as String?;
    final advertiserRegistrationTimestamp = userData['createdAt'];

    print('📝 بيانات المُعلِن:');
    print('  الاسم: $advertiserName');
    print('  النوع: $advertiserType');
    print('  الهاتف: $advertiserPhone');

    DateTime? advertiserRegistrationDate;
    if (advertiserRegistrationTimestamp != null) {
      advertiserRegistrationDate =
          (advertiserRegistrationTimestamp as Timestamp).toDate();
    }

    // حساب عدد الإعلانات المنشورة بواسطة هذا المستخدم
    print('📊 حساب عدد الإعلانات الحالية...');
    final adsSnapshot = await firestore
        .collection('estates')
        .where('ownerId', isEqualTo: userId)
        .get();

    final advertiserAdsCount = adsSnapshot.docs.length;
    print('📈 عدد الإعلانات الحالية: $advertiserAdsCount');

    // تحديث العقار ببيانات المُعلِن
    print('🔄 تحديث العقار ببيانات المُعلِن...');
    final Estate updatedEstate = estate.copyWithBase(
      advertiserImage: advertiserImage,
      advertiserName: advertiserName,
      advertiserPhone: advertiserPhone,
      advertiserType: advertiserType,
      advertiserJoinDate: advertiserRegistrationDate,
      advertiserAdsCount: advertiserAdsCount,
      ownerId: userId);

    // التحقق من صحة البيانات
    print('✅ التحقق من صحة البيانات...');
    final validationErrors = updatedEstate.validate();
    if (validationErrors.isNotEmpty) {
      print('❌ بيانات العقار غير صحيحة: ${validationErrors.values.join(', ')}');
      throw Exception(
          "بيانات العقار غير صحيحة: ${validationErrors.values.join(', ')}");
    }

    // إنشاء الإعلان باستخدام الريبو
    print('💾 حفظ العقار في قاعدة البيانات...');

    // تحويل Estate إلى EstateBase
    print('🔄 تحويل Estate إلى EstateBase...');
    final estateBase = EstateConverter.fromLegacyEstate(updatedEstate);
    print('✅ تم تحويل العقار بنجاح');

    final estateId = await repository.createEstate(estateBase);
    print('✅ تم حفظ العقار بنجاح. معرف العقار: $estateId');

    // إضافة نقاط برنامج الولاء للمستخدم
    print('🎁 إضافة نقاط برنامج الولاء...');
    await loyaltyService.addPointsForNewAd();
    print('✅ تم إضافة نقاط الولاء بنجاح');

    return estateId;
  }
}
