import '../repositories/estate_repository.dart';

/// Use case class for retrieving estates with pagination.
/// This class delegates the fetching operation to the EstateRepository.
class GetPaginatedEstates {
  /// The repository used to perform estate-related operations.
  final EstateRepository repository;

  /// Constructs a [GetPaginatedEstates] instance with the provided [repository].
  GetPaginatedEstates(this.repository);

  /// Calls the repository's [getPaginatedEstates] method to fetch a paginated list of estates.
  ///
  /// [limit] specifies the number of estates to fetch per page.
  /// [lastDocumentId] is the ID of the last document in the previous page (null for the first page).
  /// [page] is the current page number.
  /// [pageSize] is the number of items per page.
  /// [sortBy] is the field to sort by.
  /// [sortAscending] determines the sort order.
  /// [filters] is a map of filters to apply.
  /// [searchQuery] is an optional search query string.
  ///
  /// Returns a Map containing:
  /// - 'estates': List of Estate entities
  /// - 'lastDocumentId': ID of the last document (for fetching the next page)
  /// - 'hasMore': Boolean indicating if there are more estates to fetch
  Future<Map<String, dynamic>> call({
    int limit = 10,
    String? lastDocumentId,
    int page = 1,
    int pageSize = 10,
    String sortBy = "createdAt",
    bool sortAscending = false,
    Map<String, dynamic> filters = const {},
    String? searchQuery,
  }) async {
    return await repository.getPaginatedEstates(
      limit: limit,
      lastDocumentId: lastDocumentId,
      page: page,
      pageSize: pageSize,
      sortBy: sortBy,
      sortAscending: sortAscending,
      filters: filters,
      searchQuery: searchQuery);
  }
}
