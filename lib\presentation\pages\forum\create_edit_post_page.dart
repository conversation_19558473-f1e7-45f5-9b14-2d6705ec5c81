import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';

import '../../../domain/models/forum/post_model.dart';
import '../../../presentation/providers/auth_provider.dart' as app_auth;
import '../../../presentation/providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/forum/rich_text_editor.dart';

/// صفحة إنشاء وتحرير المشاركة
class CreateEditPostPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/create-post';

  /// معرف الموضوع
  final String topicId;

  /// معرف المشاركة (للتحرير)
  final String? postId;

  /// معرف المشاركة الأب (للرد)
  final String? parentId;

  const CreateEditPostPage({
    super.key,
    required this.topicId,
    this.postId,
    this.parentId,
  });

  @override
  State<CreateEditPostPage> createState() => _CreateEditPostPageState();
}

class _CreateEditPostPageState extends State<CreateEditPostPage> {
  String _content = '';
  final List<File> _selectedImages = [];
  List<String> _existingImages = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    if (widget.postId != null) {
      setState(() {
        _isLoading = true;
      });

      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      final post = await forumProvider.getPost(widget.postId!);

      if (post != null) {
        setState(() {
          _content = post.content;
          _existingImages = post.images ?? [];
        });
      }

      setState(() {
        _isLoading = false;
      });
    }
  }

  /// حفظ المشاركة
  Future<void> _savePost() async {
    if (_content.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال محتوى المشاركة')));
      return;
    }

    final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
    if (!authProvider.isLoggedIn || authProvider.user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);

      // تحميل الصور الجديدة
      final List<String> uploadedImageUrls = [];
      for (final image in _selectedImages) {
        final imageUrl = await forumProvider.uploadImage(image);
        if (imageUrl != null) {
          uploadedImageUrls.add(imageUrl);
        }
      }

      // دمج الصور الموجودة والجديدة
      final allImages = [..._existingImages, ...uploadedImageUrls];

      if (widget.postId != null) {
        // تحديث المشاركة
        await forumProvider.updatePost(
          widget.postId!,
          _content.trim(),
          allImages);
      } else {
        // إنشاء مشاركة جديدة
        await forumProvider.addPost(
          PostModel(
            id: '',
            topicId: widget.topicId,
            topicTitle: '',
            categoryId: '',
            categoryName: '',
            parentId: widget.parentId,
            content: _content.trim(),
            userId: authProvider.user!.uid,
            userName: authProvider.user!.displayName ?? 'مستخدم',
            userImage: authProvider.user!.photoURL,
            likesCount: 0,
            repliesCount: 0,
            isPinned: false,
            isBestAnswer: false,
            isReported: false,
            isDeleted: false,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            images: allImages));
      }

      if (mounted) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: $e')));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// اختيار صورة
  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _selectedImages.add(File(pickedFile.path));
      });
    }
  }

  /// إزالة صورة
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// إزالة صورة موجودة
  void _removeExistingImage(int index) {
    setState(() {
      _existingImages.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.postId != null ? 'تحرير المشاركة' : 'مشاركة جديدة'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _savePost,
            child: const Text('حفظ')),
        ]),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // محتوى المشاركة
                  const Text(
                    'محتوى المشاركة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 300,
                    child: RichTextEditor(
                      initialText: _content,
                      onTextChanged: (html, plainText) {
                        setState(() {
                          _content = html;
                        });
                      },
                      onImageAdded: (file) {
                        setState(() {
                          _selectedImages.add(file);
                        });
                      })),
                  const SizedBox(height: 16),

                  // الصور
                  _buildImagesSection(),
                ])));
  }

  /// بناء قسم الصور
  Widget _buildImagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'الصور',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            TextButton.icon(
              onPressed: _pickImage,
              icon: const Icon(Icons.add_photo_alternate),
              label: const Text('إضافة صورة')),
          ]),
        const SizedBox(height: 8),
        if (_existingImages.isNotEmpty) ...[
          const Text('الصور الحالية:'),
          const SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _existingImages.length,
              itemBuilder: (context, index) {
                return Stack(
                  children: [
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          image: NetworkImage(_existingImages[index]),
                          fit: BoxFit.cover))),
                    Positioned(
                      top: 4,
                      right: 12,
                      child: InkWell(
                        onTap: () => _removeExistingImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16)))),
                  ]);
              })),
          const SizedBox(height: 16),
        ],
        if (_selectedImages.isNotEmpty) ...[
          const Text('الصور الجديدة:'),
          const SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Stack(
                  children: [
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          image: FileImage(_selectedImages[index]),
                          fit: BoxFit.cover))),
                    Positioned(
                      top: 4,
                      right: 12,
                      child: InkWell(
                        onTap: () => _removeImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16)))),
                  ]);
              })),
        ],
      ]);
  }
}
