# مرجع API - تطبيق Krea

## 📋 نظرة عامة

هذا المرجع يوثق جميع الخدمات والواجهات البرمجية المستخدمة في تطبيق Krea.

---

## 🔐 خدمات المصادقة

### Enhanced Auth Service

#### `signInWithEmailAndPassword(email, password)`
تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور.

**المعاملات:**
- `email` (String): البريد الإلكتروني
- `password` (String): كلمة المرور

**القيمة المرجعة:**
- `Future<User?>`: بيانات المستخدم أو null

**مثال:**
```dart
final user = await authService.signInWithEmailAndPassword(
  '<EMAIL>',
  'password123'
);
```

#### `signInWithGoogle()`
تسجيل الدخول باستخدام حساب Google.

**القيمة المرجعة:**
- `Future<User?>`: بيانات المستخدم أو null

#### `signUp(email, password, userType)`
إنشاء حساب جديد.

**المعاملات:**
- `email` (String): البريد الإلكتروني
- `password` (String): كلمة المرور
- `userType` (UserType): نوع المستخدم

#### `signOut()`
تسجيل الخروج من التطبيق.

#### `resetPassword(email)`
إرسال رابط إعادة تعيين كلمة المرور.

---

## 🏠 خدمات العقارات

### Estate Repository

#### `getAllEstates()`
الحصول على جميع العقارات.

**القيمة المرجعة:**
- `Future<List<Estate>>`: قائمة العقارات

#### `getEstateById(id)`
الحصول على عقار محدد بالمعرف.

**المعاملات:**
- `id` (String): معرف العقار

**القيمة المرجعة:**
- `Future<Estate>`: بيانات العقار

#### `createEstate(estate)`
إنشاء عقار جديد.

**المعاملات:**
- `estate` (Estate): بيانات العقار

**القيمة المرجعة:**
- `Future<String>`: معرف العقار الجديد

#### `updateEstate(estate)`
تحديث بيانات عقار موجود.

**المعاملات:**
- `estate` (Estate): البيانات المحدثة

#### `deleteEstate(id)`
حذف عقار.

**المعاملات:**
- `id` (String): معرف العقار

#### `searchEstates(filters)`
البحث في العقارات باستخدام فلاتر.

**المعاملات:**
- `filters` (SearchFilters): معايير البحث

**القيمة المرجعة:**
- `Future<List<Estate>>`: نتائج البحث

---

## 💬 خدمات المنتدى

### Forum Service

#### `getCategories()`
الحصول على فئات المنتدى.

**القيمة المرجعة:**
- `Future<List<ForumCategory>>`: قائمة الفئات

#### `getTopics(categoryId, page, limit)`
الحصول على موضوعات فئة معينة.

**المعاملات:**
- `categoryId` (String): معرف الفئة
- `page` (int): رقم الصفحة
- `limit` (int): عدد العناصر في الصفحة

**القيمة المرجعة:**
- `Future<List<ForumTopic>>`: قائمة الموضوعات

#### `createTopic(topic)`
إنشاء موضوع جديد.

**المعاملات:**
- `topic` (ForumTopic): بيانات الموضوع

**القيمة المرجعة:**
- `Future<String>`: معرف الموضوع

#### `getReplies(topicId)`
الحصول على ردود موضوع معين.

**المعاملات:**
- `topicId` (String): معرف الموضوع

**القيمة المرجعة:**
- `Future<List<ForumReply>>`: قائمة الردود

#### `addReply(reply)`
إضافة رد على موضوع.

**المعاملات:**
- `reply` (ForumReply): بيانات الرد

---

## 📱 خدمات الإشعارات

### Notification Service

#### `initialize()`
تهيئة خدمة الإشعارات.

#### `sendNotification(userId, title, body, data)`
إرسال إشعار لمستخدم محدد.

**المعاملات:**
- `userId` (String): معرف المستخدم
- `title` (String): عنوان الإشعار
- `body` (String): محتوى الإشعار
- `data` (Map<String, dynamic>): بيانات إضافية

#### `getNotifications(userId)`
الحصول على إشعارات المستخدم.

**المعاملات:**
- `userId` (String): معرف المستخدم

**القيمة المرجعة:**
- `Future<List<NotificationModel>>`: قائمة الإشعارات

#### `markAsRead(notificationId)`
تمييز إشعار كمقروء.

**المعاملات:**
- `notificationId` (String): معرف الإشعار

---

## 🎯 خدمات طلبات العقارات

### Property Request Service

#### `createPropertyRequest(request)`
إنشاء طلب عقار جديد.

**المعاملات:**
- `request` (PropertyRequest): بيانات الطلب

**القيمة المرجعة:**
- `Future<String>`: معرف الطلب

#### `getPropertyRequests(userType)`
الحصول على طلبات العقارات حسب نوع المستخدم.

**المعاملات:**
- `userType` (UserType): نوع المستخدم

**القيمة المرجعة:**
- `Future<List<PropertyRequest>>`: قائمة الطلبات

#### `respondToRequest(requestId, response)`
الرد على طلب عقار.

**المعاملات:**
- `requestId` (String): معرف الطلب
- `response` (PropertyRequestResponse): الرد

#### `updateRequestStatus(requestId, status)`
تحديث حالة طلب العقار.

**المعاملات:**
- `requestId` (String): معرف الطلب
- `status` (RequestStatus): الحالة الجديدة

---

## 🏆 خدمات الولاء والمكافآت

### Loyalty Program Service

#### `getUserPoints(userId)`
الحصول على نقاط المستخدم.

**المعاملات:**
- `userId` (String): معرف المستخدم

**القيمة المرجعة:**
- `Future<int>`: عدد النقاط

#### `addPoints(userId, points, reason)`
إضافة نقاط للمستخدم.

**المعاملات:**
- `userId` (String): معرف المستخدم
- `points` (int): عدد النقاط
- `reason` (String): سبب الإضافة

#### `redeemReward(userId, rewardId)`
استبدال مكافأة بالنقاط.

**المعاملات:**
- `userId` (String): معرف المستخدم
- `rewardId` (String): معرف المكافأة

#### `getAvailableRewards()`
الحصول على المكافآت المتاحة.

**القيمة المرجعة:**
- `Future<List<Reward>>`: قائمة المكافآت

---

## 📊 خدمات التحليلات

### Analytics Service

#### `trackEvent(eventName, parameters)`
تتبع حدث معين.

**المعاملات:**
- `eventName` (String): اسم الحدث
- `parameters` (Map<String, dynamic>): معاملات الحدث

#### `trackPageView(pageName)`
تتبع زيارة صفحة.

**المعاملات:**
- `pageName` (String): اسم الصفحة

#### `setUserProperty(name, value)`
تعيين خاصية للمستخدم.

**المعاملات:**
- `name` (String): اسم الخاصية
- `value` (String): قيمة الخاصية

#### `getAnalyticsData(startDate, endDate)`
الحصول على بيانات التحليلات.

**المعاملات:**
- `startDate` (DateTime): تاريخ البداية
- `endDate` (DateTime): تاريخ النهاية

**القيمة المرجعة:**
- `Future<AnalyticsData>`: بيانات التحليلات

---

## 💳 خدمات الدفع

### Payment Service (Wamda)

#### `initiatePayment(amount, description)`
بدء عملية دفع.

**المعاملات:**
- `amount` (double): المبلغ بالدينار الكويتي
- `description` (String): وصف العملية

**القيمة المرجعة:**
- `Future<PaymentResult>`: نتيجة عملية الدفع

#### `verifyPayment(transactionId)`
التحقق من عملية دفع.

**المعاملات:**
- `transactionId` (String): معرف المعاملة

**القيمة المرجعة:**
- `Future<bool>`: حالة التحقق

#### `getPaymentHistory(userId)`
الحصول على تاريخ المدفوعات.

**المعاملات:**
- `userId` (String): معرف المستخدم

**القيمة المرجعة:**
- `Future<List<PaymentTransaction>>`: قائمة المعاملات

---

## 🔒 خدمات الأمان

### Security Service

#### `encryptData(data)`
تشفير البيانات الحساسة.

**المعاملات:**
- `data` (String): البيانات المراد تشفيرها

**القيمة المرجعة:**
- `String`: البيانات المشفرة

#### `decryptData(encryptedData)`
فك تشفير البيانات.

**المعاملات:**
- `encryptedData` (String): البيانات المشفرة

**القيمة المرجعة:**
- `String`: البيانات الأصلية

#### `validatePassword(password)`
التحقق من قوة كلمة المرور.

**المعاملات:**
- `password` (String): كلمة المرور

**القيمة المرجعة:**
- `bool`: صحة كلمة المرور

#### `enableTwoFactorAuth(userId)`
تفعيل المصادقة الثنائية.

**المعاملات:**
- `userId` (String): معرف المستخدم

---

## 📁 خدمات إدارة الملفات

### File Management Service

#### `uploadImage(file, path)`
رفع صورة إلى التخزين السحابي.

**المعاملات:**
- `file` (File): ملف الصورة
- `path` (String): مسار التخزين

**القيمة المرجعة:**
- `Future<String>`: رابط الصورة

#### `uploadDocument(file, path)`
رفع مستند إلى التخزين السحابي.

**المعاملات:**
- `file` (File): ملف المستند
- `path` (String): مسار التخزين

**القيمة المرجعة:**
- `Future<String>`: رابط المستند

#### `deleteFile(url)`
حذف ملف من التخزين السحابي.

**المعاملات:**
- `url` (String): رابط الملف

#### `compressImage(file, quality)`
ضغط صورة لتقليل الحجم.

**المعاملات:**
- `file` (File): ملف الصورة
- `quality` (int): جودة الضغط (0-100)

**القيمة المرجعة:**
- `Future<File>`: الصورة المضغوطة

---

## 🌐 خدمات الشبكة

### Network Service

#### `checkConnectivity()`
فحص حالة الاتصال بالإنترنت.

**القيمة المرجعة:**
- `Future<bool>`: حالة الاتصال

#### `makeRequest(method, url, data)`
إجراء طلب HTTP.

**المعاملات:**
- `method` (String): نوع الطلب (GET, POST, etc.)
- `url` (String): رابط الطلب
- `data` (Map?): بيانات الطلب

**القيمة المرجعة:**
- `Future<Response>`: استجابة الخادم

---

## 📱 خدمات الجهاز

### Device Service

#### `getDeviceInfo()`
الحصول على معلومات الجهاز.

**القيمة المرجعة:**
- `Future<DeviceInfo>`: معلومات الجهاز

#### `getBatteryLevel()`
الحصول على مستوى البطارية.

**القيمة المرجعة:**
- `Future<int>`: مستوى البطارية (0-100)

#### `requestPermissions(permissions)`
طلب صلاحيات من المستخدم.

**المعاملات:**
- `permissions` (List<Permission>): قائمة الصلاحيات

**القيمة المرجعة:**
- `Future<Map<Permission, PermissionStatus>>`: حالة الصلاحيات

---

## ⚠️ معالجة الأخطاء

### أنواع الأخطاء

#### `ServerException`
خطأ في الخادم أو الشبكة.

#### `AuthException`
خطأ في المصادقة أو التحقق.

#### `ValidationException`
خطأ في التحقق من صحة البيانات.

#### `PermissionException`
خطأ في الصلاحيات.

### مثال على معالجة الأخطاء
```dart
try {
  final estates = await estateRepository.getAllEstates();
  // معالجة البيانات
} on ServerException catch (e) {
  // معالجة خطأ الخادم
  showErrorMessage('خطأ في الاتصال بالخادم');
} on AuthException catch (e) {
  // معالجة خطأ المصادقة
  navigateToLogin();
} catch (e) {
  // معالجة الأخطاء العامة
  showErrorMessage('حدث خطأ غير متوقع');
}
```

---

## 📞 الدعم التقني

### للاستفسارات حول API
- **البريد الإلكتروني**: <EMAIL>
- **التوثيق التفاعلي**: docs.krea.app/api
- **GitHub Issues**: للإبلاغ عن مشاكل API

### فريق التطوير
تم تطوير جميع واجهات API بواسطة فريق **Codnet Moroccan** باستخدام أفضل الممارسات والمعايير الدولية.

### معايير API
- **RESTful Design**: اتباع مبادئ REST
- **JSON Format**: تبادل البيانات بصيغة JSON
- **Error Handling**: معالجة شاملة للأخطاء
- **Rate Limiting**: حماية من الاستخدام المفرط
- **Authentication**: أمان متقدم للوصول

---

*تم إنشاء هذا المرجع بواسطة فريق Codnet Moroccan - للمزيد من التفاصيل، راجع الكود المصدري لكل خدمة*
