// lib/presentation/widgets/ad_creation_navigation_buttons.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/theme/app_colors.dart';

/// ويدجت أزرار التنقل بين خطوات إنشاء الإعلان
/// يوفر زر للانتقال للخطوة التالية وزر للعودة للخطوة السابقة
class AdCreationNavigationButtons extends StatelessWidget {
  /// دالة يتم استدعاؤها عند النقر على زر التالي
  final VoidCallback onNext;

  /// دالة يتم استدعاؤها عند النقر على زر العودة
  final VoidCallback onBack;

  /// نص زر التالي
  final String nextText;

  /// نص زر العودة
  final String backText;

  /// ما إذا كان زر التالي معطل
  final bool isNextDisabled;

  /// ما إذا كان زر العودة معطل
  final bool isBackDisabled;

  /// ما إذا كان زر التالي قيد التحميل
  final bool isLoading;

  const AdCreationNavigationButtons({
    super.key,
    required this.onNext,
    required this.onBack,
    this.nextText = "التالي",
    this.backText = "العودة",
    this.isNextDisabled = false,
    this.isBackDisabled = false,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // زر العودة
          Expanded(
            flex: 1,
            child: ElevatedButton.icon(
              onPressed: isBackDisabled ? null : onBack,
              icon: const Icon(Icons.arrow_back, size: 18),
              label: Text(
                backText,
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.grey.shade800,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
                elevation: 0,
                disabledBackgroundColor: Colors.grey.shade100,
                disabledForegroundColor: Colors.grey.shade400))),
          
          const SizedBox(width: 16),
          
          // زر التالي
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: isNextDisabled || isLoading ? null : onNext,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
                elevation: 0,
                disabledBackgroundColor: Colors.grey.shade300),
              child: isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2))
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          nextText,
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                            fontSize: 16)),
                        const SizedBox(width: 8),
                        const Icon(Icons.arrow_forward, size: 18),
                      ]))),
        ]));
  }
}
