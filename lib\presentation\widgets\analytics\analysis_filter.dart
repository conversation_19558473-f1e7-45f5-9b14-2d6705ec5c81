import 'package:flutter/material.dart';

import '../../../data/kuwait_locations.dart';
import '../../../domain/enums/analysis_type.dart';
import '../../../domain/enums/analysis_period.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/cairo_text_styles.dart';

/// فلتر التحليل
class AnalysisFilter extends StatelessWidget {
  final AnalysisType selectedType;
  final AnalysisPeriod selectedPeriod;
  final String? selectedArea;
  final String? selectedPropertyType;
  final Function(AnalysisType) onTypeChanged;
  final Function(AnalysisPeriod) onPeriodChanged;
  final Function(String?) onAreaChanged;
  final Function(String?) onPropertyTypeChanged;

  /// إنشاء فلتر التحليل
  const AnalysisFilter({
    super.key,
    required this.selectedType,
    required this.selectedPeriod,
    this.selectedArea,
    this.selectedPropertyType,
    required this.onTypeChanged,
    required this.onPeriodChanged,
    required this.onAreaChanged,
    required this.onPropertyTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // نوع التحليل
          Text(
            'نوع التحليل',
            style: CairoTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildTypeChip(
                  AnalysisType.price,
                  'الأسعار',
                  Icons.monetization_on_outlined),
                _buildTypeChip(
                  AnalysisType.supplyDemand,
                  'العرض والطلب',
                  Icons.compare_arrows),
                _buildTypeChip(
                  AnalysisType.areas,
                  'المناطق',
                  Icons.location_city_outlined),
                _buildTypeChip(
                  AnalysisType.investmentReturn,
                  'العائد الاستثماري',
                  Icons.trending_up),
              ])),
          const SizedBox(height: 16),

          // فترة التحليل
          Text(
            'الفترة',
            style: CairoTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildPeriodChip(
                  AnalysisPeriod.month,
                  'شهر'),
                _buildPeriodChip(
                  AnalysisPeriod.quarter,
                  'ربع سنة'),
                _buildPeriodChip(
                  AnalysisPeriod.halfYear,
                  'نصف سنة'),
                _buildPeriodChip(
                  AnalysisPeriod.year,
                  'سنة'),
              ])),
          const SizedBox(height: 16),

          // المنطقة ونوع العقار
          Row(
            children: [
              // المنطقة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'المنطقة',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    _buildAreaDropdown(),
                  ])),
              const SizedBox(width: 16),

              // نوع العقار
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'نوع العقار',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    _buildPropertyTypeDropdown(),
                  ])),
            ]),
        ]));
  }

  /// بناء شريحة نوع التحليل
  Widget _buildTypeChip(AnalysisType type, String label, IconData icon) {
    final isSelected = selectedType == type;

    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: ChoiceChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey[700]),
            const SizedBox(width: 4),
            Text(label),
          ]),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            onTypeChanged(type);
          }
        },
        backgroundColor: Colors.grey[200],
        selectedColor: AppColors.primary,
        labelStyle: TextStyle(
          color: isSelected ? Colors.white : Colors.grey[800],
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal)));
  }

  /// بناء شريحة فترة التحليل
  Widget _buildPeriodChip(AnalysisPeriod period, String label) {
    final isSelected = selectedPeriod == period;

    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: ChoiceChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            onPeriodChanged(period);
          }
        },
        backgroundColor: Colors.grey[200],
        selectedColor: AppColors.primary,
        labelStyle: TextStyle(
          color: isSelected ? Colors.white : Colors.grey[800],
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal)));
  }

  /// بناء قائمة منسدلة للمناطق
  Widget _buildAreaDropdown() {
    // استخدام جميع المناطق من الملف الذكي
    final areas = KuwaitLocations.getAllAreas();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8)),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String?>(
          value: selectedArea,
          hint: const Text('جميع المناطق'),
          isExpanded: true,
          icon: const Icon(Icons.keyboard_arrow_down),
          items: [
            const DropdownMenuItem<String?>(
              value: null,
              child: Text('جميع المناطق')),
            ...areas.map((area) => DropdownMenuItem<String?>(
                  value: area,
                  child: Text(area))),
          ],
          onChanged: onAreaChanged)));
  }

  /// بناء قائمة منسدلة لأنواع العقارات
  Widget _buildPropertyTypeDropdown() {
    // قائمة أنواع العقارات
    final propertyTypes = [
      'شقة',
      'منزل',
      'أرض',
      'عمارة',
      'مكتب',
      'محل تجاري',
      'مخزن',
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8)),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String?>(
          value: selectedPropertyType,
          hint: const Text('جميع العقارات'),
          isExpanded: true,
          icon: const Icon(Icons.keyboard_arrow_down),
          items: [
            const DropdownMenuItem<String?>(
              value: null,
              child: Text('جميع العقارات')),
            ...propertyTypes.map((type) => DropdownMenuItem<String?>(
                  value: type,
                  child: Text(type))),
          ],
          onChanged: onPropertyTypeChanged)));
  }
}
