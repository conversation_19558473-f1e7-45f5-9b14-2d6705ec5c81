import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../domain/entities/market_analysis.dart';
import '../../domain/entities/market_prediction.dart';
import '../../domain/entities/recommendation.dart';
import '../../domain/enums/analysis_type.dart';
import '../../infrastructure/services/analytics_service.dart';
import '../../infrastructure/services/market_prediction_service.dart';
import '../../infrastructure/services/recommendation_service.dart';
import '../widgets/app_bar_widget.dart';
import '../widgets/loading_widget.dart';
import 'market_analysis_screen.dart';
import 'market_prediction_screen.dart';
import 'price_estimation_screen.dart';
import 'recommendations_screen.dart';

/// شاشة الصفحة الرئيسية للتحليلات
class AnalyticsHomeScreen extends StatefulWidget {
  /// إنشاء شاشة الصفحة الرئيسية للتحليلات
  const AnalyticsHomeScreen({super.key});

  @override
  _AnalyticsHomeScreenState createState() => _AnalyticsHomeScreenState();
}

class _AnalyticsHomeScreenState extends State<AnalyticsHomeScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  List<MarketAnalysis> _latestAnalyses = [];
  List<MarketPrediction> _latestPredictions = [];
  List<Recommendation> _recommendations = [];
  Map<String, dynamic>? _marketIndicators;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final analyticsService =
          Provider.of<AnalyticsService>(context, listen: false);
      final predictionService =
          Provider.of<MarketPredictionService>(context, listen: false);
      final recommendationService =
          Provider.of<RecommendationService>(context, listen: false);

      final userId =
          'user123'; // في التطبيق الحقيقي، يجب استخدام معرف المستخدم الحالي

      // تحميل أحدث التحليلات
      final latestAnalyses =
          await analyticsService.getLatestMarketAnalyses(limit: 3);

      // تحميل أحدث التنبؤات
      final latestPredictions =
          await predictionService.getLatestMarketPredictions(limit: 3);

      // تحميل التوصيات
      final recommendations =
          await recommendationService.getUserRecommendations(
        userId: userId,
        limit: 5);

      // تحميل مؤشرات السوق
      final marketIndicators = await analyticsService.getMarketIndicators();

      setState(() {
        _latestAnalyses = latestAnalyses;
        _latestPredictions = latestPredictions;
        _recommendations = recommendations;
        _marketIndicators = marketIndicators;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: 'تحليلات وذكاء اصطناعي'),
      body: _isLoading
          ? const LoadingWidget()
          : _errorMessage != null
              ? ErrorWidgetCustom(
                  message: _errorMessage!,
                  onRetry: _loadData)
              : RefreshIndicator(
                  onRefresh: _loadData,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildFeatureCards(),
                          const SizedBox(height: 24),
                          _buildAIInsights(),
                          const SizedBox(height: 24),
                          _buildMarketIndicators(),
                          const SizedBox(height: 24),
                          _buildLatestAnalyses(),
                          const SizedBox(height: 24),
                          _buildLatestPredictions(),
                          const SizedBox(height: 24),
                          _buildRecommendations(),
                        ])))));
  }

  /// بناء بطاقات الميزات
  Widget _buildFeatureCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الميزات الذكية',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          children: [
            _buildFeatureCard(
              'تحليلات السوق',
              'عرض اتجاهات السوق وتغيرات الأسعار',
              Icons.analytics,
              Colors.blue,
              () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const MarketAnalysisScreen()));
              }),
            _buildFeatureCard(
              'تنبؤات السوق',
              'توقعات لاتجاهات السوق المستقبلية',
              Icons.trending_up,
              Colors.green,
              () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const MarketPredictionScreen()));
              }),
            _buildFeatureCard(
              'تقدير الأسعار',
              'تقدير قيمة العقارات باستخدام الذكاء الاصطناعي',
              Icons.price_change,
              Colors.orange,
              () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const PriceEstimationScreen()));
              }),
            _buildFeatureCard(
              'توصيات ذكية',
              'اقتراح عقارات بناءً على تفضيلاتك',
              Icons.lightbulb,
              Colors.purple,
              () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const RecommendationsScreen()));
              }),
            _buildFeatureCard(
              'تحليل الاستثمار',
              'تحليل فرص الاستثمار وتقييم العائد المتوقع',
              Icons.attach_money,
              Colors.amber,
              () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const MarketAnalysisScreen(
                    initialType: AnalysisType.investmentReturn)));
              }),
            _buildFeatureCard(
              'مساعد العقارات الذكي',
              'استشارات عقارية باستخدام الذكاء الاصطناعي',
              Icons.smart_toy,
              Colors.deepPurple,
              () {
                _showAIAssistantDialog();
              }),
          ]),
      ]);
  }

  /// عرض مربع حوار مساعد الذكاء الاصطناعي
  void _showAIAssistantDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.smart_toy,
              color: Colors.deepPurple,
              size: 24),
            SizedBox(width: 8),
            Text('مساعد العقارات الذكي'),
          ]),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'كيف يمكنني مساعدتك اليوم؟',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            _buildAssistantOption(
              'تحليل سوق العقارات',
              'الحصول على تحليل شامل لسوق العقارات الحالي',
              Icons.analytics),
            SizedBox(height: 8),
            _buildAssistantOption(
              'نصائح استثمارية',
              'الحصول على نصائح لاستثمار عقاري ناجح',
              Icons.lightbulb),
            SizedBox(height: 8),
            _buildAssistantOption(
              'تقييم عقار',
              'تقدير قيمة عقار بناءً على بياناته',
              Icons.home),
            SizedBox(height: 8),
            _buildAssistantOption(
              'مقارنة مناطق',
              'مقارنة بين مناطق مختلفة للاستثمار',
              Icons.compare_arrows),
          ]),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق')),
        ]));
  }

  /// بناء خيار المساعد
  Widget _buildAssistantOption(
      String title, String description, IconData icon) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('سيتم تفعيل هذه الميزة قريباً'),
            backgroundColor: Colors.deepPurple));
      },
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300)),
        child: Row(
          children: [
            Icon(icon, color: Colors.deepPurple),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14)),
                  SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),
                ])),
          ])));
  }

  /// بناء قسم رؤى الذكاء الاصطناعي
  Widget _buildAIInsights() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  Icons.psychology,
                  color: Colors.deepPurple,
                  size: 24),
                SizedBox(width: 8),
                Text(
                  'رؤى الذكاء الاصطناعي',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple)),
              ]),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.deepPurple.withAlpha(25),
                borderRadius: BorderRadius.circular(16)),
              child: Row(
                children: [
                  Icon(
                    Icons.auto_awesome,
                    size: 14,
                    color: Colors.deepPurple),
                  SizedBox(width: 4),
                  Text(
                    'محدث اليوم',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.deepPurple,
                      fontWeight: FontWeight.bold)),
                ])),
          ]),
        SizedBox(height: 16),
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.deepPurple.withAlpha(25),
                Colors.blue.withAlpha(15),
              ]),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.deepPurple.withAlpha(50))),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'تحليل سوق العقارات - الربع الثالث 2023',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple)),
              SizedBox(height: 12),
              Text(
                'يشهد سوق العقارات استقراراً نسبياً مع ميل طفيف للارتفاع في أسعار العقارات السكنية بنسبة 3.2% مقارنة بالربع السابق. تظهر البيانات زيادة في الطلب على الشقق الصغيرة والمتوسطة في المناطق الحيوية، مع استمرار جاذبية الاستثمار العقاري كملاذ آمن.',
                style: TextStyle(
                  fontSize: 14,
                  height: 1.5)),
              SizedBox(height: 16),
              Row(
                children: [
                  _buildAIInsightItem(
                    'أفضل المناطق للاستثمار',
                    'السالمية، حولي، الفروانية',
                    Icons.location_on,
                    Colors.blue),
                  SizedBox(width: 16),
                  _buildAIInsightItem(
                    'أفضل أنواع العقارات',
                    'شقق سكنية، عمارات استثمارية',
                    Icons.home,
                    Colors.green),
                ]),
              SizedBox(height: 12),
              Row(
                children: [
                  _buildAIInsightItem(
                    'متوسط العائد المتوقع',
                    '7.5% - 8.2% سنوياً',
                    Icons.trending_up,
                    Colors.orange),
                  SizedBox(width: 16),
                  _buildAIInsightItem(
                    'توقعات الأسعار',
                    'ارتفاع بنسبة 4.1% خلال 6 أشهر',
                    Icons.show_chart,
                    Colors.purple),
                ]),
              SizedBox(height: 16),
              InkWell(
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('سيتم تفعيل هذه الميزة قريباً'),
                      backgroundColor: Colors.deepPurple));
                },
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    color: Colors.deepPurple.withAlpha(30),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.deepPurple.withAlpha(50))),
                  child: Center(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.analytics,
                          size: 16,
                          color: Colors.deepPurple),
                        SizedBox(width: 8),
                        Text(
                          'عرض التقرير الكامل',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.deepPurple)),
                      ])))),
            ])),
      ]);
  }

  /// بناء عنصر رؤية الذكاء الاصطناعي
  Widget _buildAIInsightItem(
    String title,
    String value,
    IconData icon,
    Color color) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withAlpha(50))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  size: 14,
                  color: color),
                SizedBox(width: 4),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[700]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis)),
              ]),
            SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.bold,
                color: color),
              maxLines: 1,
              overflow: TextOverflow.ellipsis),
          ])));
  }

  /// بناء بطاقة ميزة
  Widget _buildFeatureCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color),
              const SizedBox(height: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
                textAlign: TextAlign.center),
              const SizedBox(height: 8),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600]),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis),
            ]))));
  }

  /// بناء مؤشرات السوق
  Widget _buildMarketIndicators() {
    if (_marketIndicators == null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'مؤشرات السوق',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold)),
            TextButton(
              onPressed: () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const MarketAnalysisScreen()));
              },
              child: const Text('عرض المزيد')),
          ]),
        const SizedBox(height: 16),
        Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildIndicatorItem(
                        'مؤشر أسعار البيع',
                        _marketIndicators!['salePriceIndex'] as double,
                        _marketIndicators!['salePriceChange'] as double)),
                    Expanded(
                      child: _buildIndicatorItem(
                        'مؤشر أسعار الإيجار',
                        _marketIndicators!['rentalPriceIndex'] as double,
                        _marketIndicators!['rentalPriceChange'] as double)),
                  ]),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildIndicatorItem(
                        'مؤشر العرض',
                        _marketIndicators!['supplyIndex'] as double,
                        _marketIndicators!['supplyChange'] as double)),
                    Expanded(
                      child: _buildIndicatorItem(
                        'مؤشر الطلب',
                        _marketIndicators!['demandIndex'] as double,
                        _marketIndicators!['demandChange'] as double)),
                  ]),
              ]))),
      ]);
  }

  /// بناء عنصر مؤشر
  Widget _buildIndicatorItem(String title, double value, double change) {
    final isPositive = change >= 0;
    final changeIcon = isPositive ? Icons.arrow_upward : Icons.arrow_downward;
    final changeColor = isPositive ? Colors.green : Colors.red;

    return Column(
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold),
          textAlign: TextAlign.center),
        const SizedBox(height: 8),
        Text(
          value.toStringAsFixed(1),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold),
          textAlign: TextAlign.center),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              changeIcon,
              size: 12,
              color: changeColor),
            const SizedBox(width: 4),
            Text(
              '${change.abs().toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 12,
                color: changeColor)),
          ]),
      ]);
  }

  /// بناء أحدث التحليلات
  Widget _buildLatestAnalyses() {
    if (_latestAnalyses.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'أحدث التحليلات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold)),
            TextButton(
              onPressed: () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const MarketAnalysisScreen()));
              },
              child: const Text('عرض المزيد')),
          ]),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _latestAnalyses.length,
          itemBuilder: (context, index) {
            final analysis = _latestAnalyses[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 16),
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12)),
              child: InkWell(
                onTap: () {
                  Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) => const MarketAnalysisScreen()));
                },
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              analysis.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis)),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4),
                            decoration: BoxDecoration(
                              color: _getAnalysisTypeColor(analysis.type)
                                  .withAlpha(25), // 0.1 * 255 = 25
                              borderRadius: BorderRadius.circular(16)),
                            child: Text(
                              _getAnalysisTypeText(analysis.type),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: _getAnalysisTypeColor(analysis.type)))),
                        ]),
                      const SizedBox(height: 8),
                      Text(
                        analysis.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600]),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis),
                      const SizedBox(height: 8),
                      if (analysis.insights.isNotEmpty)
                        Text(
                          'أبرز الاستنتاجات: ${analysis.insights.first}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontStyle: FontStyle.italic),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis),
                    ]))));
          }),
      ]);
  }

  /// بناء أحدث التنبؤات
  Widget _buildLatestPredictions() {
    if (_latestPredictions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'أحدث التنبؤات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold)),
            TextButton(
              onPressed: () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const MarketPredictionScreen()));
              },
              child: const Text('عرض المزيد')),
          ]),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _latestPredictions.length,
          itemBuilder: (context, index) {
            final prediction = _latestPredictions[index];
            final changeColor =
                prediction.changePercentage >= 0 ? Colors.green : Colors.red;
            final changeIcon = prediction.changePercentage >= 0
                ? Icons.arrow_upward
                : Icons.arrow_downward;

            return Card(
              margin: const EdgeInsets.only(bottom: 16),
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12)),
              child: InkWell(
                onTap: () {
                  Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) => const MarketPredictionScreen()));
                },
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              prediction.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis)),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4),
                            decoration: BoxDecoration(
                              color: _getPredictionTypeColor(prediction.type)
                                  .withAlpha(25), // 0.1 * 255 = 25
                              borderRadius: BorderRadius.circular(16)),
                            child: Text(
                              _getPredictionTypeText(prediction.type),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: _getPredictionTypeColor(prediction.type)))),
                        ]),
                      const SizedBox(height: 8),
                      Text(
                        prediction.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600]),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Text(
                            'التغيير المتوقع: ',
                            style: TextStyle(
                              fontSize: 14)),
                          Icon(
                            changeIcon,
                            size: 14,
                            color: changeColor),
                          const SizedBox(width: 4),
                          Text(
                            '${prediction.changePercentage.abs().toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: changeColor)),
                        ]),
                    ]))));
          }),
      ]);
  }

  /// بناء التوصيات
  Widget _buildRecommendations() {
    if (_recommendations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'توصيات لك',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold)),
            TextButton(
              onPressed: () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const RecommendationsScreen()));
              },
              child: const Text('عرض المزيد')),
          ]),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _recommendations.length,
            itemBuilder: (context, index) {
              final recommendation = _recommendations[index];
              return Container(
                width: 200,
                margin: const EdgeInsets.only(right: 16),
                child: Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => const RecommendationsScreen()));
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 100,
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(12)),
                            image: recommendation.itemImage != null
                                ? DecorationImage(
                                    image:
                                        NetworkImage(recommendation.itemImage!),
                                    fit: BoxFit.cover)
                                : null,
                            color: Colors.grey[300]),
                          child: recommendation.itemImage == null
                              ? Center(
                                  child: Icon(
                                    _getRecommendationTypeIcon(
                                        recommendation.type),
                                    size: 40,
                                    color: Colors.grey))
                              : null),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                recommendation.itemTitle,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis),
                              const SizedBox(height: 4),
                              Text(
                                recommendation.reason,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600]),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.thumb_up,
                                    size: 12,
                                    color: Colors.blue),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${(recommendation.relevanceScore * 100).toInt()}% ملائم',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.blue)),
                                ]),
                            ])),
                      ]))));
            })),
      ]);
  }

  /// الحصول على نص نوع التحليل
  String _getAnalysisTypeText(AnalysisType type) {
    switch (type) {
      case AnalysisType.price:
        return 'الأسعار';
      case AnalysisType.supplyDemand:
        return 'العرض والطلب';
      case AnalysisType.areas:
        return 'المناطق';
      case AnalysisType.investmentReturn:
        return 'عائد الاستثمار';
      case AnalysisType.investors:
        return 'المستثمرين';
      case AnalysisType.developers:
        return 'المطورين';
    }
  }

  /// الحصول على لون نوع التحليل
  Color _getAnalysisTypeColor(AnalysisType type) {
    switch (type) {
      case AnalysisType.price:
        return Colors.blue;
      case AnalysisType.supplyDemand:
        return Colors.green;
      case AnalysisType.areas:
        return Colors.teal;
      case AnalysisType.investmentReturn:
        return Colors.orange;
      case AnalysisType.investors:
        return Colors.indigo;
      case AnalysisType.developers:
        return Colors.brown;
    }
  }

  /// الحصول على نص نوع التنبؤ
  String _getPredictionTypeText(PredictionType type) {
    switch (type) {
      case PredictionType.salePrices:
        return 'أسعار البيع';
      case PredictionType.rentalPrices:
        return 'أسعار الإيجار';
      case PredictionType.supply:
        return 'العرض';
      case PredictionType.demand:
        return 'الطلب';
      case PredictionType.transactions:
        return 'الصفقات';
      case PredictionType.investmentReturn:
        return 'العائد الاستثماري';
      case PredictionType.growthRate:
        return 'معدل النمو';
      case PredictionType.occupancyRate:
        return 'معدل الإشغال';
    }
  }

  /// الحصول على لون نوع التنبؤ
  Color _getPredictionTypeColor(PredictionType type) {
    switch (type) {
      case PredictionType.salePrices:
        return Colors.blue;
      case PredictionType.rentalPrices:
        return Colors.green;
      case PredictionType.supply:
        return Colors.orange;
      case PredictionType.demand:
        return Colors.purple;
      case PredictionType.transactions:
        return Colors.teal;
      case PredictionType.investmentReturn:
        return Colors.red;
      case PredictionType.growthRate:
        return Colors.indigo;
      case PredictionType.occupancyRate:
        return Colors.brown;
    }
  }

  /// الحصول على أيقونة نوع التوصية
  IconData _getRecommendationTypeIcon(RecommendationType type) {
    switch (type) {
      case RecommendationType.estate:
        return Icons.home;
      case RecommendationType.area:
        return Icons.location_on;
      case RecommendationType.agent:
        return Icons.person;
      case RecommendationType.developer:
        return Icons.business;
      case RecommendationType.investment:
        return Icons.trending_up;
      case RecommendationType.article:
        return Icons.article;
      case RecommendationType.forum:
        return Icons.forum;
    }
  }
}

/// مكون خطأ مخصص
class ErrorWidgetCustom extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;

  const ErrorWidgetCustom({
    super.key,
    required this.message,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 60),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('إعادة المحاولة')),
          ])));
  }
}
