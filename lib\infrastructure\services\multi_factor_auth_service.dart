import 'dart:async';
import 'dart:math';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:otp/otp.dart';
import 'package:base32/base32.dart';

/// خدمة المصادقة متعددة العوامل
class MultiFactorAuthService {
  static final MultiFactorAuthService _instance =
      MultiFactorAuthService._internal();

  factory MultiFactorAuthService() {
    return _instance;
  }

  MultiFactorAuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final LocalAuthentication _localAuth = LocalAuthentication();

  late SharedPreferences _prefs;

  bool _isTwoFactorAuthEnabled = false;
  bool _isBiometricAuthEnabled = false;

  /// تهيئة خدمة المصادقة متعددة العوامل
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();

    // تحميل إعدادات المصادقة
    _loadAuthSettings();
  }

  /// تحميل إعدادات المصادقة
  void _loadAuthSettings() {
    _isTwoFactorAuthEnabled =
        _prefs.getBool('two_factor_auth_enabled') ?? false;
    _isBiometricAuthEnabled = _prefs.getBool('biometric_auth_enabled') ?? false;
  }

  /// التحقق مما إذا كانت المصادقة الثنائية مفعلة
  bool isTwoFactorAuthEnabled() {
    return _isTwoFactorAuthEnabled;
  }

  /// التحقق مما إذا كانت المصادقة البيومترية مفعلة
  bool isBiometricAuthEnabled() {
    return _isBiometricAuthEnabled;
  }

  /// التحقق مما إذا كانت المصادقة البيومترية متاحة
  Future<bool> isBiometricAuthAvailable() async {
    final canCheckBiometrics = await _localAuth.canCheckBiometrics;
    final isDeviceSupported = await _localAuth.isDeviceSupported();
    return canCheckBiometrics && isDeviceSupported;
  }

  /// الحصول على أنواع المصادقة البيومترية المتاحة
  Future<List<BiometricType>> getAvailableBiometrics() async {
    return await _localAuth.getAvailableBiometrics();
  }

  /// تفعيل المصادقة البيومترية
  Future<bool> enableBiometricAuth() async {
    final isAvailable = await isBiometricAuthAvailable();
    if (!isAvailable) {
      return false;
    }

    // التحقق من المصادقة البيومترية
    final authenticated = await authenticateWithBiometrics(
      'تفعيل المصادقة البيومترية',
      'يرجى استخدام بصمة الإصبع أو بصمة الوجه للتحقق من هويتك');

    if (authenticated) {
      _isBiometricAuthEnabled = true;
      await _prefs.setBool('biometric_auth_enabled', true);
      return true;
    }

    return false;
  }

  /// تعطيل المصادقة البيومترية
  Future<void> disableBiometricAuth() async {
    _isBiometricAuthEnabled = false;
    await _prefs.setBool('biometric_auth_enabled', false);
  }

  /// المصادقة باستخدام البيومترية
  Future<bool> authenticateWithBiometrics(String title, String message) async {
    try {
      return await _localAuth.authenticate(
        localizedReason: message,
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true));
    } on PlatformException catch (e) {
      print('خطأ في المصادقة البيومترية: $e');
      return false;
    }
  }

  /// تفعيل المصادقة الثنائية
  Future<String> enableTwoFactorAuth() async {
    // إنشاء مفتاح سري
    final secret = _generateTOTPSecret();

    // تخزين المفتاح السري
    await _secureStorage.write(key: 'totp_secret', value: secret);

    // تفعيل المصادقة الثنائية
    _isTwoFactorAuthEnabled = true;
    await _prefs.setBool('two_factor_auth_enabled', true);

    return secret;
  }

  /// تعطيل المصادقة الثنائية
  Future<void> disableTwoFactorAuth() async {
    // حذف المفتاح السري
    await _secureStorage.delete(key: 'totp_secret');

    // تعطيل المصادقة الثنائية
    _isTwoFactorAuthEnabled = false;
    await _prefs.setBool('two_factor_auth_enabled', false);
  }

  /// إنشاء مفتاح سري للمصادقة الثنائية
  String _generateTOTPSecret() {
    final random = Random.secure();
    final bytes = List<int>.generate(20, (_) => random.nextInt(256));
    final uint8List = Uint8List.fromList(bytes);
    return base32.encode(uint8List);
  }

  /// إنشاء رمز TOTP
  String generateTOTPCode(String secret) {
    return OTP.generateTOTPCodeString(
      secret,
      DateTime.now().millisecondsSinceEpoch,
      length: 6,
      interval: 30,
      algorithm: Algorithm.SHA1,
      isGoogle: true);
  }

  /// التحقق من رمز TOTP
  bool verifyTOTPCode(String secret, String code) {
    final generatedCode = generateTOTPCode(secret);
    return generatedCode == code;
  }

  /// الحصول على رابط QR للمصادقة الثنائية
  String getTOTPQRCodeUrl(String secret, String email, String issuer) {
    return 'otpauth://totp/$issuer:$email?secret=$secret&issuer=$issuer';
  }

  /// إرسال رمز التحقق عبر البريد الإلكتروني
  Future<void> sendEmailVerificationCode(String email) async {
    // في التطبيق الحقيقي، يجب إرسال رمز التحقق إلى البريد الإلكتروني
    // هنا نقوم بإنشاء رمز تحقق وتخزينه محلياً للتبسيط

    final code = _generateVerificationCode();
    await _secureStorage.write(key: 'email_verification_code', value: code);

    // تخزين وقت إرسال الرمز
    await _secureStorage.write(
      key: 'email_verification_code_time',
      value: DateTime.now().millisecondsSinceEpoch.toString());

    print('تم إرسال رمز التحقق: $code');
  }

  /// إرسال رمز التحقق عبر الرسائل النصية
  Future<void> sendSMSVerificationCode(String phoneNumber) async {
    // في التطبيق الحقيقي، يجب إرسال رمز التحقق إلى رقم الهاتف
    // هنا نقوم بإنشاء رمز تحقق وتخزينه محلياً للتبسيط

    final code = _generateVerificationCode();
    await _secureStorage.write(key: 'sms_verification_code', value: code);

    // تخزين وقت إرسال الرمز
    await _secureStorage.write(
      key: 'sms_verification_code_time',
      value: DateTime.now().millisecondsSinceEpoch.toString());

    print('تم إرسال رمز التحقق: $code');
  }

  /// التحقق من رمز التحقق عبر البريد الإلكتروني
  Future<bool> verifyEmailCode(String code) async {
    final storedCode =
        await _secureStorage.read(key: 'email_verification_code');
    if (storedCode == null) {
      return false;
    }

    // التحقق من وقت إرسال الرمز
    final timeString =
        await _secureStorage.read(key: 'email_verification_code_time');
    if (timeString == null) {
      return false;
    }

    final time = int.parse(timeString);
    final now = DateTime.now().millisecondsSinceEpoch;

    // التحقق من صلاحية الرمز (10 دقائق)
    if (now - time > 10 * 60 * 1000) {
      return false;
    }

    return storedCode == code;
  }

  /// التحقق من رمز التحقق عبر الرسائل النصية
  Future<bool> verifySMSCode(String code) async {
    final storedCode = await _secureStorage.read(key: 'sms_verification_code');
    if (storedCode == null) {
      return false;
    }

    // التحقق من وقت إرسال الرمز
    final timeString =
        await _secureStorage.read(key: 'sms_verification_code_time');
    if (timeString == null) {
      return false;
    }

    final time = int.parse(timeString);
    final now = DateTime.now().millisecondsSinceEpoch;

    // التحقق من صلاحية الرمز (10 دقائق)
    if (now - time > 10 * 60 * 1000) {
      return false;
    }

    return storedCode == code;
  }

  /// إنشاء رمز التحقق
  String _generateVerificationCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString(); // رمز من 6 أرقام
  }

  /// تسجيل الدخول مع المصادقة متعددة العوامل
  Future<User?> signInWithMFA(String email, String password) async {
    try {
      // تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password);

      // التحقق من المصادقة الثنائية إذا كانت مفعلة
      if (_isTwoFactorAuthEnabled) {
        // في التطبيق الحقيقي، يجب طلب رمز التحقق من المستخدم
        // هنا نفترض أن المستخدم قد أدخل الرمز بنجاح

        // الحصول على المفتاح السري
        final secret = await _secureStorage.read(key: 'totp_secret');
        if (secret == null) {
          await _auth.signOut();
          return null;
        }

        // في التطبيق الحقيقي، يجب التحقق من الرمز المدخل
        // هنا نفترض أن الرمز صحيح
      }

      // التحقق من المصادقة البيومترية إذا كانت مفعلة
      if (_isBiometricAuthEnabled) {
        final authenticated = await authenticateWithBiometrics(
          'تسجيل الدخول',
          'يرجى استخدام بصمة الإصبع أو بصمة الوجه للتحقق من هويتك');

        if (!authenticated) {
          await _auth.signOut();
          return null;
        }
      }

      return userCredential.user;
    } on FirebaseAuthException catch (e) {
      print('خطأ في تسجيل الدخول: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدم الحالي
  User? getCurrentUser() {
    return _auth.currentUser;
  }

  /// التحقق مما إذا كان المستخدم مسجل الدخول
  bool isUserLoggedIn() {
    return _auth.currentUser != null;
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    await _auth.signOut();
  }
}

/// امتدادات لتسهيل استخدام خدمة المصادقة متعددة العوامل
extension MultiFactorAuthServiceExtensions on MultiFactorAuthService {
  /// الحصول على وقت تجديد رمز TOTP
  int getTOTPRemainingSeconds() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final interval = 30 * 1000; // 30 ثانية
    return interval - (now % interval) ~/ 1000;
  }

  /// الحصول على نسبة الوقت المتبقي لرمز TOTP
  double getTOTPRemainingPercentage() {
    final remainingSeconds = getTOTPRemainingSeconds();
    return remainingSeconds / 30.0;
  }
}

/// امتدادات لتسهيل استخدام خدمة المصادقة متعددة العوامل في واجهة المستخدم
extension MultiFactorAuthUIExtensions on MultiFactorAuthService {
  /// الحصول على وصف نوع المصادقة البيومترية
  String getBiometricTypeDescription(BiometricType type) {
    switch (type) {
      case BiometricType.face:
        return 'بصمة الوجه';
      case BiometricType.fingerprint:
        return 'بصمة الإصبع';
      case BiometricType.iris:
        return 'بصمة العين';
      case BiometricType.strong:
        return 'مصادقة قوية';
      case BiometricType.weak:
        return 'مصادقة ضعيفة';
      default:
        return 'مصادقة بيومترية';
    }
  }

  /// الحصول على أيقونة نوع المصادقة البيومترية
  String getBiometricTypeIcon(BiometricType type) {
    switch (type) {
      case BiometricType.face:
        return 'assets/icons/face_id.png';
      case BiometricType.fingerprint:
        return 'assets/icons/fingerprint.png';
      case BiometricType.iris:
        return 'assets/icons/iris.png';
      case BiometricType.strong:
      case BiometricType.weak:
      default:
        return 'assets/icons/biometric.png';
    }
  }
}
