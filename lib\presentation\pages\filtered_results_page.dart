import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/theme/app_colors.dart';
import '../../domain/entities/estate.dart';
import '../../domain/models/smart_filter_model.dart';
import '../../domain/services/advanced_search_service.dart';
import '../widgets/estate_card.dart';
import '../widgets/loading_widget.dart';
import '../widgets/empty_state_widget.dart';
import 'smart_filter_page.dart';

class FilteredResultsPage extends StatefulWidget {
  final SmartFilterModel filter;

  const FilteredResultsPage({
    super.key,
    required this.filter,
  });

  @override
  State<FilteredResultsPage> createState() => _FilteredResultsPageState();
}

class _FilteredResultsPageState extends State<FilteredResultsPage> {
  final AdvancedSearchService _searchService = AdvancedSearchService();
  List<Estate> _results = [];
  bool _isLoading = true;
  String? _error;
  SmartFilterModel _currentFilter = const SmartFilterModel();

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.filter;
    _performSearch();
  }

  Future<void> _performSearch() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final criteria = _filterToCriteria(_currentFilter);
      final results = await _searchService.searchProperties(criteria);

      setState(() {
        _results = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Map<String, dynamic> _filterToCriteria(SmartFilterModel filter) {
    final criteria = <String, dynamic>{};

    // تطبيق فلاتر نوع الاستغلال
    if (filter.usageType != null) {
      criteria['usageType'] = filter.usageType;
    }

    // تطبيق فلاتر نوع العقار (mainCategory يُستخدم كـ propertyType)
    if (filter.mainCategory != null) {
      criteria['propertyType'] = filter.mainCategory;
      // لا نرسل mainCategory لتجنب التضارب مع فلتر نوع الاستغلال
    }

    if (filter.subCategory != null) {
      criteria['subCategory'] = filter.subCategory;
    }

    // تطبيق فلاتر الموقع
    if (filter.location != null) {
      criteria['location'] = filter.location;
    }
    if (filter.governorate != null) {
      criteria['governorate'] = filter.governorate;
      criteria['location'] = filter.governorate; // للتوافق مع البحث
    }
    if (filter.area != null) {
      criteria['area'] = filter.area;
    }

    // تطبيق فلاتر السعر
    if (filter.minPrice != null) {
      criteria['priceMin'] = filter.minPrice;
      criteria['minPrice'] = filter.minPrice;
    }
    if (filter.maxPrice != null) {
      criteria['priceMax'] = filter.maxPrice;
      criteria['maxPrice'] = filter.maxPrice;
    }

    // تطبيق فلاتر المساحة
    if (filter.minArea != null) {
      criteria['areaMin'] = filter.minArea;
      criteria['minArea'] = filter.minArea;
    }
    if (filter.maxArea != null) {
      criteria['areaMax'] = filter.maxArea;
      criteria['maxArea'] = filter.maxArea;
    }

    // تطبيق فلاتر عدد الغرف والحمامات
    if (filter.numberOfRooms != null) {
      criteria['rooms'] = filter.numberOfRooms;
      criteria['numberOfRooms'] = filter.numberOfRooms;
    }
    if (filter.numberOfBathrooms != null) {
      criteria['bathrooms'] = filter.numberOfBathrooms;
      criteria['numberOfBathrooms'] = filter.numberOfBathrooms;
    }

    if (filter.floorNumber != null) {
      criteria['floorNumber'] = filter.floorNumber;
    }
    if (filter.buildingAge != null) {
      criteria['buildingAge'] = filter.buildingAge;
    }

    // إضافة المميزات
    filter.features.forEach((key, value) {
      if (value) {
        criteria[key] = true;
      }
    });

    // إضافة الترتيب
    criteria['sortBy'] = filter.sortBy;
    criteria['descending'] = filter.descending;

    debugPrint('🔍 معايير البحث المطبقة: $criteria'); // للتتبع

    return criteria;
  }

  void _showFilterDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SmartFilterPage(
          initialFilter: _currentFilter,
          onApplyFilter: (filter) {
            setState(() {
              _currentFilter = filter;
            });
            _performSearch();
          })));
  }

  String _getFilterSummary() {
    final parts = <String>[];

    if (_currentFilter.usageType != null) {
      parts.add(UsageTypes.getDisplayName(_currentFilter.usageType!));
    }
    if (_currentFilter.mainCategory != null) {
      parts.add(_currentFilter.mainCategory!);
    }
    if (_currentFilter.subCategory != null) {
      parts.add(_currentFilter.subCategory!);
    }
    if (_currentFilter.governorate != null) {
      parts.add(_currentFilter.governorate!);
    }
    if (_currentFilter.area != null) {
      parts.add(_currentFilter.area!);
    }

    if (parts.isEmpty) {
      return 'جميع العقارات';
    }

    return parts.join(' • ');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'نتائج البحث',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog),
        ]),
      body: Column(
        children: [
          // ملخص الفلتر
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getFilterSummary(),
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800)),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      '${_results.length} عقار',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600)),
                    const Spacer(),
                    if (_currentFilter.hasActiveFilters)
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _currentFilter = _currentFilter.reset();
                          });
                          _performSearch();
                        },
                        icon: const Icon(Icons.clear, size: 16),
                        label: Text(
                          'مسح الفلاتر',
                          style: GoogleFonts.cairo(fontSize: 12)),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey.shade600,
                          padding: const EdgeInsets.symmetric(horizontal: 8))),
                  ]),
              ])),

          // النتائج
          Expanded(
            child: _buildContent()),
        ]));
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في البحث',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600)),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500),
              textAlign: TextAlign.center),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _performSearch,
              child: Text(
                'إعادة المحاولة',
                style: GoogleFonts.cairo())),
          ]));
    }

    if (_results.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.search_off,
        title: 'لا توجد نتائج',
        description: 'لم نجد أي عقارات تطابق معايير البحث المحددة',
        buttonText: 'تعديل الفلتر',
        onButtonPressed: _showFilterDialog);
    }

    return RefreshIndicator(
      onRefresh: _performSearch,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _results.length,
        itemBuilder: (context, index) {
          final estate = _results[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: EstateCard(estate: estate));
        }));
  }
}
