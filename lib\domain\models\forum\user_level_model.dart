import 'package:flutter/material.dart';

/// نموذج مستوى المستخدم
class UserLevelModel {
  /// معرف المستوى
  final String id;

  /// اسم المستوى
  final String name;

  /// وصف المستوى
  final String description;

  /// النقاط المطلوبة للوصول إلى هذا المستوى
  final int requiredPoints;

  /// النقاط المطلوبة للمستوى التالي
  final int nextLevelPoints;

  /// لون المستوى
  final Color color;

  /// رمز المستوى
  final IconData icon;

  /// مزايا المستوى
  final List<String> perks;

  /// شارة المستوى
  final String badgeUrl;

  /// ترتيب المستوى
  final int order;

  UserLevelModel({
    required this.id,
    required this.name,
    required this.description,
    required this.requiredPoints,
    required this.nextLevelPoints,
    required this.color,
    required this.icon,
    required this.perks,
    required this.badgeUrl,
    required this.order,
  });

  /// الحصول على نسبة التقدم نحو المستوى التالي
  double getProgressPercentage(int currentPoints) {
    if (currentPoints >= nextLevelPoints) return 1.0;
    if (currentPoints <= requiredPoints) return 0.0;

    return (currentPoints - requiredPoints) /
           (nextLevelPoints - requiredPoints);
  }

  /// الحصول على النقاط المتبقية للمستوى التالي
  int getRemainingPoints(int currentPoints) {
    if (currentPoints >= nextLevelPoints) return 0;
    return nextLevelPoints - currentPoints;
  }

  /// قائمة المستويات المتاحة في النظام
  static List<UserLevelModel> getLevels() {
    return [
      UserLevelModel(
        id: 'beginner',
        name: 'مبتدئ',
        description: 'بداية رحلتك في اللوبي',
        requiredPoints: 0,
        nextLevelPoints: 100,
        color: Colors.green.shade300,
        icon: Icons.emoji_events_outlined,
        perks: [
          'إمكانية إنشاء المواضيع',
          'إمكانية الرد على المواضيع',
        ],
        badgeUrl: 'assets/images/badges/beginner.png',
        order: 1),
      UserLevelModel(
        id: 'active',
        name: 'نشط',
        description: 'عضو فعال في اللوبي',
        requiredPoints: 100,
        nextLevelPoints: 500,
        color: Colors.green.shade400, // تغيير من الأزرق إلى الأخضر
        icon: Icons.star_outline,
        perks: [
          'إمكانية إنشاء المواضيع',
          'إمكانية الرد على المواضيع',
          'إمكانية إضافة صور متعددة',
        ],
        badgeUrl: 'assets/images/badges/active.png',
        order: 2),
      UserLevelModel(
        id: 'distinguished',
        name: 'متميز',
        description: 'عضو متميز بمشاركاته القيمة',
        requiredPoints: 500,
        nextLevelPoints: 1000,
        color: Colors.purple.shade300,
        icon: Icons.auto_awesome,
        perks: [
          'جميع مزايا المستوى السابق',
          'إمكانية تثبيت موضوع واحد',
          'إمكانية تغيير لون الاسم',
        ],
        badgeUrl: 'assets/images/badges/distinguished.png',
        order: 3),
      UserLevelModel(
        id: 'professional',
        name: 'محترف',
        description: 'عضو محترف ذو خبرة عالية',
        requiredPoints: 1000,
        nextLevelPoints: 2000,
        color: Colors.orange.shade400,
        icon: Icons.workspace_premium,
        perks: [
          'جميع مزايا المستوى السابق',
          'إمكانية تثبيت موضوعين',
          'إمكانية إضافة توقيع مخصص',
        ],
        badgeUrl: 'assets/images/badges/professional.png',
        order: 4),
      UserLevelModel(
        id: 'expert',
        name: 'خبير',
        description: 'خبير في مجاله مع مساهمات قيمة',
        requiredPoints: 2000,
        nextLevelPoints: 5000,
        color: Colors.red.shade400,
        icon: Icons.military_tech,
        perks: [
          'جميع مزايا المستوى السابق',
          'إمكانية تمييز المواضيع',
          'إمكانية إضافة روابط خارجية',
        ],
        badgeUrl: 'assets/images/badges/expert.png',
        order: 5),
      UserLevelModel(
        id: 'legend',
        name: 'أسطورة',
        description: 'أسطورة المنتدى ومرجع للجميع',
        requiredPoints: 5000,
        nextLevelPoints: 10000,
        color: Colors.amber.shade700,
        icon: Icons.emoji_events,
        perks: [
          'جميع مزايا المستويات السابقة',
          'إمكانية إنشاء استطلاعات رأي',
          'إمكانية تثبيت 5 مواضيع',
          'شارة خاصة بجانب الاسم',
        ],
        badgeUrl: 'assets/images/badges/legend.png',
        order: 6),
    ];
  }

  /// الحصول على المستوى الحالي للمستخدم بناءً على النقاط
  static UserLevelModel getCurrentLevel(int points) {
    final levels = getLevels();

    // البحث عن أعلى مستوى يمكن للمستخدم الوصول إليه بناءً على النقاط
    for (int i = levels.length - 1; i >= 0; i--) {
      if (points >= levels[i].requiredPoints) {
        return levels[i];
      }
    }

    // إذا لم يتم العثور على مستوى مناسب، يتم إرجاع المستوى الأول (مبتدئ)
    return levels.first;
  }

  /// الحصول على المستوى التالي للمستخدم
  static UserLevelModel? getNextLevel(int points) {
    final levels = getLevels();
    final currentLevel = getCurrentLevel(points);

    // البحث عن المستوى التالي
    for (int i = 0; i < levels.length - 1; i++) {
      if (levels[i].id == currentLevel.id) {
        return levels[i + 1];
      }
    }

    // إذا كان المستخدم في أعلى مستوى، لا يوجد مستوى تالي
    return null;
  }
}
