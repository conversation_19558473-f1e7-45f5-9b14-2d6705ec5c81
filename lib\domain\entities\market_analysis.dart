import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../enums/analysis_type.dart';

/// نموذج لتحليلات السوق
class MarketAnalysis extends Equatable {
  /// معرف التحليل
  final String id;

  /// المنطقة
  final String location;

  /// المحافظة
  final String? governorate;

  /// المدينة
  final String? city;

  /// المنطقة
  final String? district;

  /// نوع العقار
  final String propertyType;

  /// نوع التحليل
  final AnalysisType type;

  /// عنوان التحليل
  final String title;

  /// وصف التحليل
  final String description;

  /// بيانات التحليل
  final Map<String, dynamic> data;

  /// رؤى وتحليلات
  final List<String> insights;

  /// توصيات
  final List<String> recommendations;

  /// تاريخ التحليل
  final DateTime analysisDate;

  /// متوسط سعر المتر المربع
  final double averagePricePerSquareMeter;

  /// متوسط سعر البيع
  final double averageSalePrice;

  /// متوسط سعر الإيجار
  final double averageRentalPrice;

  /// نسبة التغير في السعر (سنوياً)
  final double priceChangePercentage;

  /// عدد العقارات المعروضة للبيع
  final int saleListingsCount;

  /// عدد العقارات المعروضة للإيجار
  final int rentalListingsCount;

  /// متوسط مدة العرض (بالأيام)
  final double averageListingDuration;

  /// نسبة العائد على الاستثمار
  final double returnOnInvestment;

  /// نسبة الإشغال
  final double occupancyRate;

  /// معدل نمو السكان
  final double populationGrowthRate;

  /// معدل نمو الطلب
  final double demandGrowthRate;

  /// معدل نمو العرض
  final double supplyGrowthRate;

  /// مؤشر الأداء
  final double performanceIndex;

  /// توقعات الأسعار المستقبلية
  final Map<String, double>? priceForecast;

  /// إحصائيات حسب المساحة
  final Map<String, double>? statsByArea;

  /// إحصائيات حسب عدد الغرف
  final Map<String, double>? statsByRooms;

  /// بيانات إضافية
  final Map<String, dynamic>? additionalData;

  const MarketAnalysis({
    required this.id,
    required this.location,
    this.governorate,
    this.city,
    this.district,
    required this.propertyType,
    required this.type,
    required this.title,
    required this.description,
    required this.data,
    required this.insights,
    required this.recommendations,
    required this.analysisDate,
    required this.averagePricePerSquareMeter,
    required this.averageSalePrice,
    required this.averageRentalPrice,
    required this.priceChangePercentage,
    required this.saleListingsCount,
    required this.rentalListingsCount,
    required this.averageListingDuration,
    required this.returnOnInvestment,
    required this.occupancyRate,
    required this.populationGrowthRate,
    required this.demandGrowthRate,
    required this.supplyGrowthRate,
    required this.performanceIndex,
    this.priceForecast,
    this.statsByArea,
    this.statsByRooms,
    this.additionalData,
  });

  /// إنشاء نسخة معدلة من التحليل
  MarketAnalysis copyWith({
    String? id,
    String? location,
    String? governorate,
    String? city,
    String? district,
    String? propertyType,
    AnalysisType? type,
    String? title,
    String? description,
    Map<String, dynamic>? data,
    List<String>? insights,
    List<String>? recommendations,
    DateTime? analysisDate,
    double? averagePricePerSquareMeter,
    double? averageSalePrice,
    double? averageRentalPrice,
    double? priceChangePercentage,
    int? saleListingsCount,
    int? rentalListingsCount,
    double? averageListingDuration,
    double? returnOnInvestment,
    double? occupancyRate,
    double? populationGrowthRate,
    double? demandGrowthRate,
    double? supplyGrowthRate,
    double? performanceIndex,
    Map<String, double>? priceForecast,
    Map<String, double>? statsByArea,
    Map<String, double>? statsByRooms,
    Map<String, dynamic>? additionalData,
  }) {
    return MarketAnalysis(
      id: id ?? this.id,
      location: location ?? this.location,
      governorate: governorate ?? this.governorate,
      city: city ?? this.city,
      district: district ?? this.district,
      propertyType: propertyType ?? this.propertyType,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      data: data ?? this.data,
      insights: insights ?? this.insights,
      recommendations: recommendations ?? this.recommendations,
      analysisDate: analysisDate ?? this.analysisDate,
      averagePricePerSquareMeter:
          averagePricePerSquareMeter ?? this.averagePricePerSquareMeter,
      averageSalePrice: averageSalePrice ?? this.averageSalePrice,
      averageRentalPrice: averageRentalPrice ?? this.averageRentalPrice,
      priceChangePercentage:
          priceChangePercentage ?? this.priceChangePercentage,
      saleListingsCount: saleListingsCount ?? this.saleListingsCount,
      rentalListingsCount: rentalListingsCount ?? this.rentalListingsCount,
      averageListingDuration:
          averageListingDuration ?? this.averageListingDuration,
      returnOnInvestment: returnOnInvestment ?? this.returnOnInvestment,
      occupancyRate: occupancyRate ?? this.occupancyRate,
      populationGrowthRate: populationGrowthRate ?? this.populationGrowthRate,
      demandGrowthRate: demandGrowthRate ?? this.demandGrowthRate,
      supplyGrowthRate: supplyGrowthRate ?? this.supplyGrowthRate,
      performanceIndex: performanceIndex ?? this.performanceIndex,
      priceForecast: priceForecast ?? this.priceForecast,
      statsByArea: statsByArea ?? this.statsByArea,
      statsByRooms: statsByRooms ?? this.statsByRooms,
      additionalData: additionalData ?? this.additionalData);
  }

  /// تحويل التحليل إلى Map
  Map<String, dynamic> toMap() {
    // Convert the analysis type to a string
    String typeStr;
    switch (type) {
      case AnalysisType.price:
        typeStr = 'price';
        break;
      case AnalysisType.supplyDemand:
        typeStr = 'supplyDemand';
        break;
      case AnalysisType.areas:
        typeStr = 'areas';
        break;
      case AnalysisType.investmentReturn:
        typeStr = 'investmentReturn';
        break;
      case AnalysisType.investors:
        typeStr = 'investors';
        break;
      case AnalysisType.developers:
        typeStr = 'developers';
        break;
    }

    return {
      'id': id,
      'location': location,
      'governorate': governorate,
      'city': city,
      'district': district,
      'propertyType': propertyType,
      'type': typeStr,
      'title': title,
      'description': description,
      'data': data,
      'insights': insights,
      'recommendations': recommendations,
      'analysisDate': Timestamp.fromDate(analysisDate),
      'averagePricePerSquareMeter': averagePricePerSquareMeter,
      'averageSalePrice': averageSalePrice,
      'averageRentalPrice': averageRentalPrice,
      'priceChangePercentage': priceChangePercentage,
      'saleListingsCount': saleListingsCount,
      'rentalListingsCount': rentalListingsCount,
      'averageListingDuration': averageListingDuration,
      'returnOnInvestment': returnOnInvestment,
      'occupancyRate': occupancyRate,
      'populationGrowthRate': populationGrowthRate,
      'demandGrowthRate': demandGrowthRate,
      'supplyGrowthRate': supplyGrowthRate,
      'performanceIndex': performanceIndex,
      'priceForecast': priceForecast,
      'statsByArea': statsByArea,
      'statsByRooms': statsByRooms,
      'additionalData': additionalData,
    };
  }

  /// إنشاء تحليل من Map
  factory MarketAnalysis.fromMap(Map<String, dynamic> map) {
    // Parse the analysis type
    AnalysisType analysisType = AnalysisType.price;
    if (map['type'] != null) {
      final typeStr = map['type'].toString();
      switch (typeStr) {
        case 'price':
          analysisType = AnalysisType.price;
          break;
        case 'supplyDemand':
          analysisType = AnalysisType.supplyDemand;
          break;
        case 'areas':
          analysisType = AnalysisType.areas;
          break;
        case 'investmentReturn':
          analysisType = AnalysisType.investmentReturn;
          break;
        case 'investors':
          analysisType = AnalysisType.investors;
          break;
        case 'developers':
          analysisType = AnalysisType.developers;
          break;
      }
    }

    return MarketAnalysis(
      id: map['id'] ?? '',
      location: map['location'] ?? '',
      governorate: map['governorate'],
      city: map['city'],
      district: map['district'],
      propertyType: map['propertyType'] ?? '',
      type: analysisType,
      title: map['title'] ?? 'تحليل السوق',
      description: map['description'] ?? 'تحليل تفصيلي لسوق العقارات',
      data: map['data'] ?? {},
      insights:
          map['insights'] != null ? List<String>.from(map['insights']) : [],
      recommendations: map['recommendations'] != null
          ? List<String>.from(map['recommendations'])
          : [],
      analysisDate: map['analysisDate'] is Timestamp
          ? (map['analysisDate'] as Timestamp).toDate()
          : DateTime.now(),
      averagePricePerSquareMeter:
          (map['averagePricePerSquareMeter'] ?? 0.0).toDouble(),
      averageSalePrice: (map['averageSalePrice'] ?? 0.0).toDouble(),
      averageRentalPrice: (map['averageRentalPrice'] ?? 0.0).toDouble(),
      priceChangePercentage: (map['priceChangePercentage'] ?? 0.0).toDouble(),
      saleListingsCount: map['saleListingsCount'] ?? 0,
      rentalListingsCount: map['rentalListingsCount'] ?? 0,
      averageListingDuration: (map['averageListingDuration'] ?? 0.0).toDouble(),
      returnOnInvestment: (map['returnOnInvestment'] ?? 0.0).toDouble(),
      occupancyRate: (map['occupancyRate'] ?? 0.0).toDouble(),
      populationGrowthRate: (map['populationGrowthRate'] ?? 0.0).toDouble(),
      demandGrowthRate: (map['demandGrowthRate'] ?? 0.0).toDouble(),
      supplyGrowthRate: (map['supplyGrowthRate'] ?? 0.0).toDouble(),
      performanceIndex: (map['performanceIndex'] ?? 0.0).toDouble(),
      priceForecast: map['priceForecast'] != null
          ? Map<String, double>.from(map['priceForecast']
              .map((key, value) => MapEntry(key, (value as num).toDouble())))
          : null,
      statsByArea: map['statsByArea'] != null
          ? Map<String, double>.from(map['statsByArea']
              .map((key, value) => MapEntry(key, (value as num).toDouble())))
          : null,
      statsByRooms: map['statsByRooms'] != null
          ? Map<String, double>.from(map['statsByRooms']
              .map((key, value) => MapEntry(key, (value as num).toDouble())))
          : null,
      additionalData: map['additionalData']);
  }

  /// إنشاء تحليل من DocumentSnapshot
  factory MarketAnalysis.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return MarketAnalysis.fromMap(data);
  }

  /// الحصول على نسبة العائد السنوي
  double getAnnualYield() {
    if (averageSalePrice <= 0) {
      return 0.0;
    }

    return (averageRentalPrice * 12 / averageSalePrice) * 100;
  }

  /// الحصول على فترة استرداد رأس المال (بالسنوات)
  double getPaybackPeriod() {
    if (averageRentalPrice <= 0) {
      return 0.0;
    }

    return averageSalePrice / (averageRentalPrice * 12);
  }

  /// الحصول على مؤشر الاستثمار
  double getInvestmentScore() {
    // حساب مؤشر الاستثمار بناءً على عدة عوامل
    double score = 0.0;

    // العائد على الاستثمار (30%)
    score += returnOnInvestment * 0.3;

    // نسبة التغير في السعر (20%)
    score += priceChangePercentage * 0.2;

    // نسبة الإشغال (15%)
    score += occupancyRate * 0.15;

    // معدل نمو الطلب (15%)
    score += demandGrowthRate * 0.15;

    // مؤشر الأداء (20%)
    score += performanceIndex * 0.2;

    return score;
  }

  /// الحصول على تصنيف الاستثمار
  String getInvestmentRating() {
    final score = getInvestmentScore();

    if (score >= 80) {
      return 'ممتاز';
    } else if (score >= 60) {
      return 'جيد جداً';
    } else if (score >= 40) {
      return 'جيد';
    } else if (score >= 20) {
      return 'متوسط';
    } else {
      return 'ضعيف';
    }
  }

  /// الحصول على توصية الاستثمار
  String getInvestmentRecommendation() {
    final score = getInvestmentScore();

    if (score >= 70) {
      return 'شراء';
    } else if (score >= 50) {
      return 'احتفاظ';
    } else {
      return 'بيع';
    }
  }

  @override
  List<Object?> get props => [
        id,
        location,
        governorate,
        city,
        district,
        propertyType,
        type,
        title,
        description,
        data,
        insights,
        recommendations,
        analysisDate,
        averagePricePerSquareMeter,
        averageSalePrice,
        averageRentalPrice,
        priceChangePercentage,
        saleListingsCount,
        rentalListingsCount,
        averageListingDuration,
        returnOnInvestment,
        occupancyRate,
        populationGrowthRate,
        demandGrowthRate,
        supplyGrowthRate,
        performanceIndex,
        priceForecast,
        statsByArea,
        statsByRooms,
        additionalData,
      ];
}
