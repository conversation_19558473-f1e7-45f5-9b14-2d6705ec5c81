import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// Widget to display a loading state with shimmer effect.
class LoadingWidget extends StatelessWidget {
  final bool isListView;

  const LoadingWidget({
    super.key,
    this.isListView = true,
  });

  @override
  Widget build(BuildContext context) {
    return isListView ? _buildListLoading() : _buildGridLoading();
  }

  Widget _buildListLoading() {
    return ListView.builder(
      itemCount: 5,
      padding: const EdgeInsets.all(16),
      shrinkWrap: true, // إضافة هذه الخاصية لتحديد الارتفاع تلقائيًا
      physics: const NeverScrollableScrollPhysics(), // منع التمرير المزدوج
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8)))));
      });
  }

  Widget _buildGridLoading() {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10),
      itemCount: 6,
      padding: const EdgeInsets.all(16),
      shrinkWrap: true, // إضافة هذه الخاصية لتحديد الارتفاع تلقائيًا
      physics: const NeverScrollableScrollPhysics(), // منع التمرير المزدوج
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8))));
      });
  }
}
