// lib/presentation/widgets/property_request/statistics/property_request_bar_chart.dart
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:google_fonts/google_fonts.dart';

/// مخطط شريطي لإحصائيات طلبات العقارات
class PropertyRequestBarChart extends StatelessWidget {
  /// عناوين الأعمدة
  final List<String> titles;

  /// قيم الأعمدة
  final List<double> values;

  /// لون الأعمدة
  final Color color;

  /// لاحقة القيمة
  final String? suffix;

  /// إنشاء مخطط شريطي لإحصائيات طلبات العقارات
  const PropertyRequestBarChart({
    super.key,
    required this.titles,
    required this.values,
    required this.color,
    this.suffix,
  });

  @override
  Widget build(BuildContext context) {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxY(),
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              return BarTooltipItem(
                '${titles[groupIndex]}\n',
                GoogleFonts.cairo(
                  color: Colors.white,
                  fontWeight: FontWeight.bold),
                children: [
                  TextSpan(
                    text: '${rod.toY.toStringAsFixed(1)}${suffix ?? ''}',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500)),
                ]);
            })),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value < 0 || value >= titles.length) {
                  return const SizedBox.shrink();
                }
                return Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    titles[value.toInt()],
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center));
              },
              reservedSize: 40)),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.bold));
              },
              reservedSize: 40)),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false))),
        gridData: FlGridData(
          show: true,
          horizontalInterval: _getInterval(),
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1);
          }),
        borderData: FlBorderData(
          show: false),
        barGroups: _buildBarGroups()));
  }

  /// بناء مجموعات الأعمدة
  List<BarChartGroupData> _buildBarGroups() {
    return List.generate(titles.length, (index) {
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: values[index],
            color: color,
            width: 20,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4))),
        ]);
    });
  }

  /// الحصول على أقصى قيمة للمحور Y
  double _getMaxY() {
    if (values.isEmpty) {
      return 10;
    }

    final maxValue = values.reduce((a, b) => a > b ? a : b);
    return maxValue * 1.2; // إضافة هامش
  }

  /// الحصول على الفاصل بين خطوط الشبكة
  double _getInterval() {
    final maxY = _getMaxY();

    if (maxY <= 5) {
      return 1;
    } else if (maxY <= 20) {
      return 2;
    } else if (maxY <= 50) {
      return 5;
    } else if (maxY <= 100) {
      return 10;
    } else if (maxY <= 500) {
      return 50;
    } else {
      return 100;
    }
  }
}
