import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';
import 'package:kuwait_corners/domain/entities/estate_factory.dart';
import 'package:kuwait_corners/domain/entities/estate_converter.dart';
import 'package:kuwait_corners/presentation/widgets/estate_card.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';

/// صفحة عرض جميع العقارات مع فلترة متقدمة
class PropertiesPage extends StatefulWidget {
  const PropertiesPage({super.key});

  @override
  State<PropertiesPage> createState() => _PropertiesPageState();
}

class _PropertiesPageState extends State<PropertiesPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  String _selectedLocation = 'الكل';
  String _selectedPriceRange = 'الكل';
  String _sortBy = 'الأحدث';

  final List<String> _categories = [
    'الكل',
    'منزل للبيع',
    'منزل للايجار',
    'شقة للبيع',
    'شقة للايجار',
    'أرض للبيع',
    'مخزن للايجار',
    'مكتب للايجار',
  ];

  final List<String> _locations = [
    'الكل',
    'محافظة العاصمة',
    'محافظة حولي',
    'محافظة مبارك الكبير',
    'محافظة الأحمدي',
    'محافظة الفروانية',
    'محافظة الجهراء',
  ];

  final List<String> _priceRanges = [
    'الكل',
    'أقل من 100 د.ك',
    '100 - 500 د.ك',
    '500 - 1000 د.ك',
    '1000 - 5000 د.ك',
    'أكثر من 5000 د.ك',
  ];

  final List<String> _sortOptions = [
    'الأحدث',
    'الأقدم',
    'السعر: من الأقل للأعلى',
    'السعر: من الأعلى للأقل',
    'الأكثر مشاهدة',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(
            child: _buildPropertiesList()),
        ]),
      floatingActionButton: _buildFloatingActionButton());
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'العقارات',
        style: CairoTextStyles.appBarTitle),
      backgroundColor: AppColors.primary,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.map),
          onPressed: _showMapView),
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showAdvancedFilters),
      ]);
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1)),
        ]),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن عقار...',
              hintStyle: const TextStyle(),
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      })
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!)),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary))),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            }),
          const SizedBox(height: 16),
          // الفلاتر السريعة
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('الفئة', _selectedCategory, _categories, (value) {
                  setState(() {
                    _selectedCategory = value;
                  });
                }),
                const SizedBox(width: 8),
                _buildFilterChip('المنطقة', _selectedLocation, _locations, (value) {
                  setState(() {
                    _selectedLocation = value;
                  });
                }),
                const SizedBox(width: 8),
                _buildFilterChip('السعر', _selectedPriceRange, _priceRanges, (value) {
                  setState(() {
                    _selectedPriceRange = value;
                  });
                }),
                const SizedBox(width: 8),
                _buildFilterChip('الترتيب', _sortBy, _sortOptions, (value) {
                  setState(() {
                    _sortBy = value;
                  });
                }),
              ])),
        ]));
  }

  Widget _buildFilterChip(String label, String value, List<String> options, Function(String) onChanged) {
    return GestureDetector(
      onTap: () => _showFilterDialog(label, value, options, onChanged),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: value != options.first ? AppColors.primary : Colors.grey[200],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: value != options.first ? AppColors.primary : Colors.grey[400]!)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              value == options.first ? label : value,
              style: TextStyle(
                fontSize: 12,
                color: value != options.first ? Colors.white : Colors.grey[700],
                fontWeight: FontWeight.w500)),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down,
              size: 16,
              color: value != options.first ? Colors.white : Colors.grey[700]),
          ])));
  }

  Widget _buildPropertiesList() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getPropertiesStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل العقارات');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState();
        }

        final properties = snapshot.data!.docs;
        final filteredProperties = _filterProperties(properties);

        if (filteredProperties.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredProperties.length,
            itemBuilder: (context, index) {
              final propertyDoc = filteredProperties[index];

              try {
                final estateBase = EstateFactory.createFromSnapshot(propertyDoc);
                final estate = EstateConverter.toLegacyEstate(estateBase);
                if (estate != null) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: EstateCard(
                      estate: estate,
                      onTap: () => _navigateToDetails(estate)));
                }
                return const SizedBox.shrink();
              } catch (e) {
                return const SizedBox.shrink();
              }
            }));
      });
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.home_outlined,
            size: 80,
            color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد عقارات متاحة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600])),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير الفلاتر أو البحث',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500])),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _clearFilters,
            icon: const Icon(Icons.clear_all),
            label: const Text('مسح الفلاتر'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white)),
        ]));
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[600])),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {});
            },
            child: const Text('إعادة المحاولة')),
        ]));
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () {
        Navigator.pushNamed(context, '/add-property');
      },
      backgroundColor: AppColors.primary,
      child: const Icon(Icons.add, color: Colors.white));
  }

  Stream<QuerySnapshot> _getPropertiesStream() {
    Query query = FirebaseFirestore.instance
        .collection('estates')
        .where('isActive', isEqualTo: true);

    // تطبيق الترتيب
    switch (_sortBy) {
      case 'الأحدث':
        query = query.orderBy('createdAt', descending: true);
        break;
      case 'الأقدم':
        query = query.orderBy('createdAt', descending: false);
        break;
      case 'السعر: من الأقل للأعلى':
        query = query.orderBy('price', descending: false);
        break;
      case 'السعر: من الأعلى للأقل':
        query = query.orderBy('price', descending: true);
        break;
      case 'الأكثر مشاهدة':
        query = query.orderBy('views', descending: true);
        break;
      default:
        query = query.orderBy('createdAt', descending: true);
    }

    return query.limit(50).snapshots();
  }

  List<QueryDocumentSnapshot> _filterProperties(List<QueryDocumentSnapshot> properties) {
    List<QueryDocumentSnapshot> filtered = properties;

    // تطبيق فلتر الفئة
    if (_selectedCategory != 'الكل') {
      filtered = filtered.where((property) {
        final data = property.data() as Map<String, dynamic>;
        final category = '${data['subCategory'] ?? ''}';
        return category == _selectedCategory;
      }).toList();
    }

    // تطبيق فلتر المنطقة
    if (_selectedLocation != 'الكل') {
      filtered = filtered.where((property) {
        final data = property.data() as Map<String, dynamic>;
        final location = data['location'] ?? '';
        return location.contains(_selectedLocation);
      }).toList();
    }

    // تطبيق فلتر السعر
    if (_selectedPriceRange != 'الكل') {
      filtered = filtered.where((property) {
        final data = property.data() as Map<String, dynamic>;
        final price = (data['price'] ?? 0).toDouble();

        switch (_selectedPriceRange) {
          case 'أقل من 100 د.ك':
            return price < 100;
          case '100 - 500 د.ك':
            return price >= 100 && price <= 500;
          case '500 - 1000 د.ك':
            return price >= 500 && price <= 1000;
          case '1000 - 5000 د.ك':
            return price >= 1000 && price <= 5000;
          case 'أكثر من 5000 د.ك':
            return price > 5000;
          default:
            return true;
        }
      }).toList();
    }

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      final searchTerm = _searchQuery.toLowerCase();
      filtered = filtered.where((property) {
        final data = property.data() as Map<String, dynamic>;
        final title = (data['title'] ?? '').toString().toLowerCase();
        final description = (data['description'] ?? '').toString().toLowerCase();
        final location = (data['location'] ?? '').toString().toLowerCase();

        return title.contains(searchTerm) ||
               description.contains(searchTerm) ||
               location.contains(searchTerm);
      }).toList();
    }

    return filtered;
  }

  void _showFilterDialog(String title, String currentValue, List<String> options, Function(String) onChanged) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'اختيار $title'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: options.length,
            itemBuilder: (context, index) {
              final option = options[index];
              return RadioListTile<String>(
                title: Text(
                  option),
                value: option,
                groupValue: currentValue,
                onChanged: (value) {
                  onChanged(value!);
                  Navigator.pop(context);
                });
            }))));
  }

  void _clearFilters() {
    setState(() {
      _selectedCategory = 'الكل';
      _selectedLocation = 'الكل';
      _selectedPriceRange = 'الكل';
      _sortBy = 'الأحدث';
      _searchController.clear();
      _searchQuery = '';
    });
  }

  void _showMapView() {
    Navigator.pushNamed(context, '/map-view');
  }

  void _showAdvancedFilters() {
    Navigator.pushNamed(context, '/advanced-search');
  }

  void _navigateToDetails(Estate estate) {
    Navigator.pushNamed(
      context,
      '/estate-details',
      arguments: estate);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}