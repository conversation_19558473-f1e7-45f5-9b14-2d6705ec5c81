import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/routes/app_routes.dart';
import 'package:kuwait_corners/core/services/ad_limit_notification_service.dart';
import 'package:kuwait_corners/core/services/enhanced_subscription_service.dart';
import 'package:kuwait_corners/core/services/loyalty_program_service.dart';
import 'package:kuwait_corners/core/services/user_verification_service.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/domain/entities/user_profile.dart';
import 'package:kuwait_corners/domain/repositories/user_repository.dart';
import 'package:kuwait_corners/presentation/pages/edit_profile_page.dart';
import 'package:kuwait_corners/presentation/widgets/enhanced_progress_indicator.dart';
import 'package:url_launcher/url_launcher.dart';

/// صفحة الملف الشخصي
class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final UserVerificationService _verificationService =
      UserVerificationService();
  final LoyaltyProgramService _loyaltyService = LoyaltyProgramService();
  final EnhancedSubscriptionService _subscriptionService = EnhancedSubscriptionService();
  final AdLimitNotificationService _notificationService = AdLimitNotificationService();

  // حالة الصفحة
  bool _isLoading = true;
  String? _errorMessage;
  UserProfile? _userProfile;
  VerificationStatus _verificationStatus = VerificationStatus.unverified;
  LoyaltyProgramData? _loyaltyData;
  bool _notificationsEnabled = true;
  bool _isLoadingNotificationSettings = true;

  // بيانات إضافية
  int _adsCount = 0;
  int _favoritesCount = 0;
  int _viewsCount = 0;
  Map<String, dynamic> _additionalData = {};

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadNotificationSettings();
  }

  /// تحميل إعدادات الإشعارات
  Future<void> _loadNotificationSettings() async {
    setState(() {
      _isLoadingNotificationSettings = true;
    });

    try {
      final isEnabled = await _notificationService.areNotificationsEnabled();

      setState(() {
        _notificationsEnabled = isEnabled;
        _isLoadingNotificationSettings = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingNotificationSettings = false;
      });
    }
  }

  /// تبديل حالة الإشعارات
  Future<void> _toggleNotifications() async {
    try {
      final isEnabled = await _notificationService.toggleNotifications();

      setState(() {
        _notificationsEnabled = isEnabled;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEnabled ? 'تم تفعيل الإشعارات' : 'تم تعطيل الإشعارات'),
          duration: const Duration(seconds: 2)));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: $e'),
          backgroundColor: Colors.red));
    }
  }

  /// تحميل بيانات المستخدم
  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          _errorMessage = 'لم يتم تسجيل الدخول';
        });
        return;
      }

      // تحميل الملف الشخصي
      final userRepository = RepositoryProvider.of<UserRepository>(context);
      final userProfile = await userRepository.getUserProfile(user.uid);

      // تحميل حالة التحقق
      final verificationStatus =
          await _verificationService.getCurrentUserVerificationStatus();

      // تحميل بيانات برنامج الولاء
      final loyaltyData = await _loyaltyService.getCurrentUserLoyaltyData();

      // تحميل البيانات الإضافية
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();
      final userData = userDoc.data() ?? {};

      // تحميل عدد الإعلانات
      final estatesSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .where('userId', isEqualTo: user.uid)
          .get();

      // تحميل عدد المفضلة
      final favoritesSnapshot = await FirebaseFirestore.instance
          .collection('favorites')
          .where('userId', isEqualTo: user.uid)
          .get();

      // تحميل عدد المشاهدات
      int viewsCount = 0;
      for (final doc in estatesSnapshot.docs) {
        viewsCount += (doc.data()['viewsCount'] as int?) ?? 0;
      }

      setState(() {
        _userProfile = userProfile;
        _verificationStatus = verificationStatus;
        _loyaltyData = loyaltyData;
        _adsCount = estatesSnapshot.docs.length;
        _favoritesCount = favoritesSnapshot.docs.length;
        _viewsCount = viewsCount;
        _additionalData = userData;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// الحصول على نص حالة التحقق
  String _getVerificationStatusText() {
    switch (_verificationStatus) {
      case VerificationStatus.unverified:
        return 'غير محقق';
      case VerificationStatus.pending:
        return 'قيد المراجعة';
      case VerificationStatus.verified:
        return 'محقق';
      case VerificationStatus.rejected:
        return 'مرفوض';
    }
  }

  /// الحصول على لون حالة التحقق
  Color _getVerificationStatusColor() {
    switch (_verificationStatus) {
      case VerificationStatus.unverified:
        return Colors.grey;
      case VerificationStatus.pending:
        return Colors.orange;
      case VerificationStatus.verified:
        return Colors.green;
      case VerificationStatus.rejected:
        return Colors.red;
    }
  }

  /// الحصول على نص مستوى الولاء
  String _getLoyaltyLevelText() {
    if (_loyaltyData == null) {
      return 'برونزي';
    }

    switch (_loyaltyData!.level) {
      case LoyaltyLevel.bronze:
        return 'برونزي';
      case LoyaltyLevel.silver:
        return 'فضي';
      case LoyaltyLevel.gold:
        return 'ذهبي';
      case LoyaltyLevel.platinum:
        return 'بلاتيني';
      case LoyaltyLevel.diamond:
        return 'ماسي';
      case LoyaltyLevel.vip:
        return 'VIP';
    }
  }

  /// الحصول على لون مستوى الولاء
  Color _getLoyaltyLevelColor() {
    if (_loyaltyData == null) {
      return const Color(0xFFCD7F32); // لون برونزي
    }

    switch (_loyaltyData!.level) {
      case LoyaltyLevel.bronze:
        return const Color(0xFFCD7F32); // لون برونزي
      case LoyaltyLevel.silver:
        return const Color(0xFFC0C0C0); // لون فضي
      case LoyaltyLevel.gold:
        return const Color(0xFFFFD700); // لون ذهبي
      case LoyaltyLevel.platinum:
        return const Color(0xFFE5E4E2); // لون بلاتيني
      case LoyaltyLevel.diamond:
        return const Color(0xFFB9F2FF); // لون ماسي
      case LoyaltyLevel.vip:
        return const Color(0xFF9C27B0); // لون VIP
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          // زر تحديث البيانات
          IconButton(
            onPressed: _loadUserData,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث'),
        ]),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, color: Colors.red, size: 48),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red)),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadUserData,
                        child: const Text('إعادة المحاولة')),
                    ]))
              : _userProfile == null
                  ? const Center(child: Text('لا توجد بيانات'))
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // بطاقة الملف الشخصي
                          _buildProfileCard(),

                          const SizedBox(height: 24),

                          // بطاقة التحقق
                          _buildVerificationCard(),

                          const SizedBox(height: 24),

                          // بطاقة برنامج الولاء
                          _buildLoyaltyCard(),

                          const SizedBox(height: 24),

                          // بطاقة الإحصائيات
                          _buildStatsCard(),

                          const SizedBox(height: 24),

                          // بطاقة معلومات إضافية حسب نوع الحساب
                          _buildAdditionalInfoCard(),

                          const SizedBox(height: 24),

                          // بطاقة إعدادات الإشعارات
                          _buildNotificationsCard(),
                        ])));
  }

  /// بناء بطاقة الملف الشخصي
  Widget _buildProfileCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // صورة الملف الشخصي
            CircleAvatar(
              radius: 50,
              backgroundColor: Colors.grey.shade200,
              backgroundImage: _userProfile!.profileImageUrl.isNotEmpty
                  ? NetworkImage(_userProfile!.profileImageUrl)
                  : null,
              child: _userProfile!.profileImageUrl.isEmpty
                  ? const Icon(Icons.person, size: 50, color: Colors.grey)
                  : null),

            const SizedBox(height: 16),

            // اسم المستخدم
            Text(
              _userProfile!.fullNameOrCompanyName,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold),
              textAlign: TextAlign.center),

            const SizedBox(height: 8),

            // نوع المستخدم
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(16)),
              child: Text(
                _getUserTypeText(_userProfile!.userType),
                style: TextStyle(
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.bold))),

            const SizedBox(height: 16),

            // معلومات الاتصال
            _buildInfoRow(Icons.email, _userProfile!.email),
            const SizedBox(height: 8),
            _buildInfoRow(Icons.phone, _userProfile!.phone),
            const SizedBox(height: 8),
            _buildInfoRow(Icons.location_on, _userProfile!.address),

            const SizedBox(height: 16),

            // زر تعديل الملف الشخصي
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () async {
                  // التنقل إلى صفحة تعديل الملف الشخصي
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EditProfilePage(
                        userProfile: _userProfile!)));

                  // إعادة تحميل البيانات إذا تم التعديل
                  if (result == true) {
                    _loadUserData();
                  }
                },
                icon: const Icon(Icons.edit),
                label: const Text('تعديل الملف الشخصي'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12)))),
          ])));
  }

  /// بناء بطاقة التحقق
  Widget _buildVerificationCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            Row(
              children: [
                Icon(
                  Icons.verified_user,
                  color: _getVerificationStatusColor()),
                const SizedBox(width: 8),
                const Text(
                  'حالة التحقق',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),

            const SizedBox(height: 16),

            // حالة التحقق
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getVerificationStatusColor().withAlpha(25),
                    borderRadius: BorderRadius.circular(16)),
                  child: Text(
                    _getVerificationStatusText(),
                    style: TextStyle(
                      color: _getVerificationStatusColor(),
                      fontWeight: FontWeight.bold))),
                const Spacer(),
                if (_verificationStatus == VerificationStatus.unverified ||
                    _verificationStatus == VerificationStatus.rejected)
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pushNamed(context, AppRoutes.userVerification);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white),
                    child: const Text('طلب التحقق')),
              ]),

            const SizedBox(height: 16),

            // وصف حالة التحقق
            Text(
              _getVerificationStatusDescription(),
              style: TextStyle(
                color: Colors.grey.shade600)),
          ])));
  }

  /// بناء بطاقة برنامج الولاء
  Widget _buildLoyaltyCard() {
    if (_loyaltyData == null) {
      return const SizedBox.shrink();
    }

    final nextLevelName = _loyaltyData!.getNextLevelName();
    final pointsToNextLevel = _loyaltyData!.getPointsToNextLevel();
    final progress = _loyaltyData!.getProgressToNextLevel();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            Row(
              children: [
                Icon(
                  Icons.loyalty,
                  color: _getLoyaltyLevelColor()),
                const SizedBox(width: 8),
                const Text(
                  'برنامج الولاء',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(context, AppRoutes.loyaltyProgram);
                  },
                  child: const Text('عرض التفاصيل')),
              ]),

            const SizedBox(height: 16),

            // مستوى الولاء والنقاط
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المستوى: ${_getLoyaltyLevelText()}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _getLoyaltyLevelColor())),
                    const SizedBox(height: 4),
                    Text(
                      'النقاط: ${_loyaltyData!.points}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold)),
                  ]),
                const Spacer(),
                EnhancedCircularProgressIndicator(
                  value: progress,
                  title: '',
                  size: 60,
                  strokeWidth: 8,
                  progressColor: _getLoyaltyLevelColor(),
                  backgroundColor: Colors.grey.shade200,
                  percentageFontSize: 14,
                  showPercentage: true),
              ]),

            const SizedBox(height: 16),

            // التقدم نحو المستوى التالي
            if (_loyaltyData!.level != LoyaltyLevel.platinum) ...[
              Text(
                'التقدم نحو المستوى $nextLevelName',
                style: const TextStyle(
                  fontSize: 14)),
              const SizedBox(height: 8),
              EnhancedLinearProgressIndicator(
                value: progress,
                title: '',
                height: 8,
                progressColor: _getLoyaltyLevelColor(),
                backgroundColor: Colors.grey.shade200,
                showPercentage: false),
              const SizedBox(height: 8),
              Text(
                'تحتاج إلى $pointsToNextLevel نقطة إضافية للوصول إلى المستوى $nextLevelName',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600)),
            ] else ...[
              const Center(
                child: Text(
                  'تهانينا! لقد وصلت إلى أعلى مستوى',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold))),
            ],

            const SizedBox(height: 16),

            // زر دعوة الأصدقاء
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  Navigator.pushNamed(context, AppRoutes.referralProgram);
                },
                icon: const Icon(Icons.people),
                label: const Text('دعوة الأصدقاء واكسب نقاط'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12)))),
          ])));
  }

  /// بناء بطاقة الإحصائيات
  Widget _buildStatsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            const Row(
              children: [
                Icon(Icons.bar_chart),
                SizedBox(width: 8),
                Text(
                  'إحصائيات الحساب',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),

            const SizedBox(height: 16),

            // الإحصائيات
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الإعلانات',
                    _adsCount.toString(),
                    Icons.campaign,
                    Colors.blue)),
                Expanded(
                  child: _buildStatItem(
                    'المفضلة',
                    _favoritesCount.toString(),
                    Icons.favorite,
                    Colors.red)),
                Expanded(
                  child: _buildStatItem(
                    'المشاهدات',
                    _viewsCount.toString(),
                    Icons.visibility,
                    Colors.purple)),
              ]),
          ])));
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 14))),
      ]);
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(
      String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withAlpha(25),
            shape: BoxShape.circle),
          child: Icon(
            icon,
            color: color,
            size: 24)),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600)),
      ]);
  }

  /// الحصول على نص نوع المستخدم
  String _getUserTypeText(String userType) {
    switch (userType) {
      case 'owner':
        return 'مالك عقار';
      case 'investor':
        return 'مستثمر';
      case 'company':
        return 'شركة عقارية';
      case 'agent':
        return 'وكيل عقاري';
      default:
        return 'مستخدم عادي';
    }
  }

  /// الحصول على وصف حالة التحقق
  String _getVerificationStatusDescription() {
    switch (_verificationStatus) {
      case VerificationStatus.unverified:
        return 'حسابك غير محقق. قم بطلب التحقق للحصول على مزايا إضافية وزيادة مصداقية إعلاناتك.';
      case VerificationStatus.pending:
        return 'طلب التحقق الخاص بك قيد المراجعة. سيتم إشعارك عند الانتهاء من المراجعة.';
      case VerificationStatus.verified:
        return 'تم التحقق من حسابك. يمكنك الآن الاستفادة من جميع مزايا الحسابات الموثقة.';
      case VerificationStatus.rejected:
        return 'تم رفض طلب التحقق الخاص بك. يمكنك تقديم طلب جديد مع التأكد من صحة المعلومات والمستندات المقدمة.';
    }
  }

  /// بناء بطاقة المعلومات الإضافية حسب نوع الحساب
  Widget _buildAdditionalInfoCard() {
    if (_userProfile == null) {
      return const SizedBox.shrink();
    }

    switch (_userProfile!.userType) {
      case 'agent':
        return _buildAgentInfoCard();
      case 'company':
        return _buildCompanyInfoCard();
      case 'owner':
        return _buildOwnerInfoCard();
      default:
        return const SizedBox.shrink();
    }
  }

  /// بناء بطاقة معلومات الوكيل العقاري
  Widget _buildAgentInfoCard() {
    final licenseNumber = _additionalData['licenseNumber'] ?? 'غير متوفر';
    final experienceYears =
        _additionalData['experienceYears']?.toString() ?? 'غير متوفر';
    final specialty = _additionalData['specialty'] ?? 'غير متوفر';
    final website = _additionalData['website'] ?? 'غير متوفر';

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            const Row(
              children: [
                Icon(Icons.business_center),
                SizedBox(width: 8),
                Text(
                  'معلومات الوكيل العقاري',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),

            const SizedBox(height: 16),

            // رقم الترخيص
            _buildInfoRow(Icons.badge, 'رقم الترخيص: $licenseNumber'),
            const SizedBox(height: 8),

            // سنوات الخبرة
            _buildInfoRow(Icons.work_history, 'سنوات الخبرة: $experienceYears'),
            const SizedBox(height: 8),

            // التخصص
            _buildInfoRow(Icons.category, 'التخصص: $specialty'),
            const SizedBox(height: 8),

            // الموقع الإلكتروني
            if (website != 'غير متوفر')
              InkWell(
                onTap: () {
                  // فتح الموقع الإلكتروني
                  final url =
                      website.startsWith('http') ? website : 'https://$website';
                  launchUrl(Uri.parse(url));
                },
                child: Row(
                  children: [
                    const Icon(Icons.language, size: 16, color: Colors.blue),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        website,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                          decoration: TextDecoration.underline))),
                  ])),
          ])));
  }

  /// بناء بطاقة معلومات الشركة العقارية
  Widget _buildCompanyInfoCard() {
    final companyName = _additionalData['companyName'] ?? 'غير متوفر';
    final commercialRegister =
        _additionalData['commercialRegister'] ?? 'غير متوفر';
    final website = _additionalData['website'] ?? 'غير متوفر';
    final foundedYear = _additionalData['foundedYear'] ?? 'غير متوفر';
    final employeesCount =
        _additionalData['employeesCount']?.toString() ?? 'غير متوفر';

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            const Row(
              children: [
                Icon(Icons.business),
                SizedBox(width: 8),
                Text(
                  'معلومات الشركة العقارية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),

            const SizedBox(height: 16),

            // اسم الشركة
            _buildInfoRow(Icons.business, 'اسم الشركة: $companyName'),
            const SizedBox(height: 8),

            // رقم السجل التجاري
            _buildInfoRow(
                Icons.numbers, 'رقم السجل التجاري: $commercialRegister'),
            const SizedBox(height: 8),

            // سنة التأسيس
            if (foundedYear != 'غير متوفر')
              _buildInfoRow(Icons.date_range, 'سنة التأسيس: $foundedYear'),
            if (foundedYear != 'غير متوفر') const SizedBox(height: 8),

            // عدد الموظفين
            if (employeesCount != 'غير متوفر')
              _buildInfoRow(Icons.people, 'عدد الموظفين: $employeesCount'),
            if (employeesCount != 'غير متوفر') const SizedBox(height: 8),

            // الموقع الإلكتروني
            if (website != 'غير متوفر')
              InkWell(
                onTap: () {
                  // فتح الموقع الإلكتروني
                  final url =
                      website.startsWith('http') ? website : 'https://$website';
                  launchUrl(Uri.parse(url));
                },
                child: Row(
                  children: [
                    const Icon(Icons.language, size: 16, color: Colors.blue),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        website,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                          decoration: TextDecoration.underline))),
                  ])),
          ])));
  }

  /// بناء بطاقة معلومات مالك العقار
  Widget _buildOwnerInfoCard() {
    final specialty = _additionalData['specialty'] ?? 'غير متوفر';
    final joinedDate = _additionalData['createdAt'] != null
        ? ((_additionalData['createdAt'] as Timestamp)
            .toDate()
            .toString()
            .split(' ')[0])
        : 'غير متوفر';

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            const Row(
              children: [
                Icon(Icons.home),
                SizedBox(width: 8),
                Text(
                  'معلومات مالك العقار',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),

            const SizedBox(height: 16),

            // نوع العقارات
            _buildInfoRow(Icons.category, 'نوع العقارات: $specialty'),
            const SizedBox(height: 8),

            // عدد العقارات
            _buildInfoRow(Icons.home_work, 'عدد العقارات: $_adsCount'),
            const SizedBox(height: 8),

            // تاريخ الانضمام
            _buildInfoRow(Icons.date_range, 'تاريخ الانضمام: $joinedDate'),
          ])));
  }

  /// بناء بطاقة إعدادات الإشعارات
  Widget _buildNotificationsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            Row(
              children: [
                Icon(
                  Icons.notifications,
                  color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'إعدادات الإشعارات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),

            const SizedBox(height: 16),

            // إشعارات حد الإعلانات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إشعارات حد الإعلانات',
                        style: TextStyle(
                          fontWeight: FontWeight.bold)),
                      SizedBox(height: 4),
                      Text(
                        'تنبيهك عند اقتراب نفاد الإعلانات المتاحة',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey)),
                    ])),
                Switch(
                  value: _notificationsEnabled,
                  onChanged: _isLoadingNotificationSettings
                      ? null
                      : (value) {
                          _toggleNotifications();
                        },
                  activeColor: Theme.of(context).primaryColor),
              ]),

            const SizedBox(height: 8),

            // معلومات إضافية
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.blue.shade200,
                  width: 1)),
              child: Row(
                children: [
                  Icon(
                    Icons.info,
                    color: Colors.blue.shade800,
                    size: 18),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'ستتلقى إشعارًا عندما يتبقى لديك 3 إعلانات أو أقل',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue))),
                ])),
          ])));
  }
}
