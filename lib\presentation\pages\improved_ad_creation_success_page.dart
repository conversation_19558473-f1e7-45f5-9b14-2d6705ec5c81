// lib/presentation/pages/improved_ad_creation_success_page.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import 'package:intl/intl.dart';

import '../../core/theme/app_colors.dart';
import '../../core/services/notification_service.dart';
import '../../domain/models/notification_model.dart' as domain;
import '../../core/routes/app_routes.dart';
import '../widgets/improved_ad_creation_progress.dart';

/// صفحة نجاح إنشاء الإعلان المحسنة
/// تعرض رسالة نجاح وتنتقل تلقائياً إلى صفحة "عقاراتي" بعد فترة قصيرة
class ImprovedAdCreationSuccessPage extends StatefulWidget {
  /// معرف الإعلان الذي تم إنشاؤه
  final String adId;

  /// ما إذا كان الإعلان جديداً أو تم تحديثه
  final bool isNewAd;

  /// ما إذا تم التحقق من الدفع
  final bool isPaymentVerified;

  const ImprovedAdCreationSuccessPage({
    super.key,
    required this.adId,
    this.isNewAd = true,
    this.isPaymentVerified = false,
  });

  @override
  State<ImprovedAdCreationSuccessPage> createState() => _ImprovedAdCreationSuccessPageState();
}

class _ImprovedAdCreationSuccessPageState extends State<ImprovedAdCreationSuccessPage> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  Timer? _redirectTimer;
  Timer? _countdownTimer;
  int _countdown = 3;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2));

    // تشغيل الرسوم المتحركة
    _animationController.forward();

    // إعداد مؤقت العد التنازلي
    _startCountdown();

    // إعداد مؤقت للانتقال التلقائي بعد 3 ثوانٍ
    _redirectTimer = Timer(const Duration(seconds: 3), _navigateToMyProperties);

    // عرض إشعار فوري
    WidgetsBinding.instance.addPostFrameCallback((_) {
      NotificationService.showInAppNotification(
        context,
        title: widget.isNewAd ? 'تم إنشاء الإعلان بنجاح!' : 'تم تحديث الإعلان بنجاح!',
        body: widget.isPaymentVerified
            ? 'إعلانك متاح الآن للمهتمين'
            : 'سيتم مراجعة إعلانك خلال يوم عمل واحد',
        type: widget.isPaymentVerified
            ? domain.NotificationType.system
            : domain.NotificationType.other,
        duration: const Duration(seconds: 5),
      );
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _redirectTimer?.cancel();
    _countdownTimer?.cancel();
    super.dispose();
  }

  /// بدء العد التنازلي
  void _startCountdown() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_countdown > 0) {
            _countdown--;
          } else {
            timer.cancel();
          }
        });
      } else {
        timer.cancel();
      }
    });
  }

  /// الانتقال إلى صفحة "عقاراتي"
  void _navigateToMyProperties() {
    Navigator.pushNamedAndRemoveUntil(
      context,
      AppRoutes.myProperties,
      (route) => route.settings.name == AppRoutes.home);
  }

  /// الحصول على التاريخ الحالي بتنسيق مناسب
  String _getFormattedDate() {
    final now = DateTime.now();
    final formatter = DateFormat('yyyy-MM-dd HH:mm', 'ar');
    return formatter.format(now);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      // مؤشر التقدم أسفل الصفحة
      bottomNavigationBar: ImprovedAdCreationProgress(
        currentStep: 5, // تعديل الخطوة لتكون 5 بدلاً من 6
        onStepTap: (step) {
          // لا نسمح بالرجوع في هذه المرحلة
        }),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                // رسوم متحركة للنجاح
                Lottie.asset(
                  'assets/animations/success.json',
                  controller: _animationController,
                  width: 200,
                  height: 200,
                  repeat: false),

                const SizedBox(height: 32),

                // عنوان النجاح
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primary.withAlpha(50),
                        AppColors.primary.withAlpha(10),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight),
                    borderRadius: BorderRadius.circular(20)),
                  child: Text(
                    widget.isNewAd ? 'تم إنشاء الإعلان بنجاح!' : 'تم تحديث الإعلان بنجاح!',
                    style: GoogleFonts.cairo(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary),
                    textAlign: TextAlign.center)),

                // حالة التحقق من الدفع والمراجعة
                if (!widget.isPaymentVerified)
                  Container(
                    margin: const EdgeInsets.only(top: 16),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.amber.shade50,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.amber.shade200.withAlpha(76),
                          blurRadius: 8,
                          offset: const Offset(0, 2)),
                      ]),
                    child: Column(
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.amber.shade100,
                                shape: BoxShape.circle),
                              child: Icon(Icons.pending_actions, color: Colors.amber.shade800, size: 20)),
                            const SizedBox(width: 12),
                            Text(
                              "إعلانك تحت المراجعة",
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.amber.shade800)),
                          ]),
                        const SizedBox(height: 8),
                        Text(
                          "سيتم مراجعة إعلانك والتحقق من التحويل البنكي خلال يوم عمل واحد",
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.grey.shade700),
                          textAlign: TextAlign.center),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
                            const SizedBox(width: 6),
                            Text(
                              "سيتم إشعارك عند اكتمال المراجعة",
                              style: GoogleFonts.cairo(
                                fontSize: 13,
                                fontStyle: FontStyle.italic,
                                color: Colors.grey.shade600)),
                          ]),
                      ])),

                const SizedBox(height: 16),

                // رسالة توضيحية
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    widget.isPaymentVerified
                        ? (widget.isNewAd
                            ? 'تم إنشاء إعلانك بنجاح وتم التحقق من الدفع. سيتم عرض إعلانك للمهتمين. يمكنك إدارة إعلاناتك من صفحة "عقاراتي".'
                            : 'تم تحديث إعلانك بنجاح وتم التحقق من الدفع. يمكنك متابعة إعلاناتك من صفحة "عقاراتي".')
                        : (widget.isNewAd
                            ? 'تم تسجيل إعلانك بنجاح. سيتم نشر الإعلان بعد مراجعته والتحقق من التحويل البنكي.'
                            : 'تم تحديث إعلانك بنجاح. سيتم نشر التحديثات بعد مراجعتها والتحقق من التحويل البنكي.'),
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.grey.shade700,
                      height: 1.5),
                    textAlign: TextAlign.center)),

                const SizedBox(height: 32),

                // معلومات الانتقال التلقائي
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(30)),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary))),
                      const SizedBox(width: 12),
                      Text(
                        _countdown > 0
                          ? 'سيتم الانتقال تلقائياً إلى صفحة "عقاراتي" خلال $_countdown ثانية...'
                          : 'جاري الانتقال إلى صفحة "عقاراتي"...',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade700)),
                    ])),

                const SizedBox(height: 32),

                // أزرار التنقل
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // زر التوجه للرئيسية
                    Container(
                      width: 140,
                      height: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.secondary.withAlpha(40),
                            blurRadius: 12,
                            offset: const Offset(0, 4)),
                        ]),
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pushNamedAndRemoveUntil(
                            context,
                            '/home',
                            (route) => false);
                        },
                        icon: const Icon(Icons.home, size: 18),
                        label: Text(
                          'الرئيسية',
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                            fontSize: 14)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.secondary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25))))),

                    // زر الانتقال إلى عقاراتي
                    Container(
                      width: 140,
                      height: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withAlpha(40),
                            blurRadius: 12,
                            offset: const Offset(0, 4)),
                        ]),
                      child: ElevatedButton.icon(
                        onPressed: _navigateToMyProperties,
                        icon: const Icon(Icons.home_work, size: 18),
                        label: Text(
                          'عقاراتي',
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                            fontSize: 14)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25))))),
                  ]),

                // معلومات إضافية
                const SizedBox(height: 24),
                if (!widget.isPaymentVerified)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: Colors.grey.shade200)),
                    child: Column(
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.support_agent, color: AppColors.primary, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              "هل تحتاج إلى مساعدة؟",
                              style: GoogleFonts.cairo(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                                color: Colors.grey.shade800)),
                          ]),
                        const SizedBox(height: 8),
                        Text(
                          "يمكنك التواصل مع فريق الدعم على الرقم +965 9929 8821 في حال وجود أي استفسار حول حالة إعلانك",
                          style: GoogleFonts.cairo(
                            fontSize: 13,
                            color: Colors.grey.shade600),
                          textAlign: TextAlign.center),
                      ])),

                // معلومات الإعلان
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.grey.shade200)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: AppColors.primary, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            "معلومات الإعلان",
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              color: Colors.grey.shade800)),
                        ]),
                      const SizedBox(height: 12),

                      // معرف الإعلان
                      Row(
                        children: [
                          Text(
                            "معرف الإعلان:",
                            style: GoogleFonts.cairo(
                              fontSize: 13,
                              color: Colors.grey.shade700)),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              widget.adId,
                              style: GoogleFonts.cairo(
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade900),
                              overflow: TextOverflow.ellipsis)),
                        ]),
                      const SizedBox(height: 8),

                      // تاريخ الإنشاء
                      Row(
                        children: [
                          Text(
                            "تاريخ الإنشاء:",
                            style: GoogleFonts.cairo(
                              fontSize: 13,
                              color: Colors.grey.shade700)),
                          const SizedBox(width: 8),
                          Text(
                            _getFormattedDate(),
                            style: GoogleFonts.cairo(
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade900)),
                        ]),
                      const SizedBox(height: 8),

                      // حالة الإعلان
                      Row(
                        children: [
                          Text(
                            "حالة الإعلان:",
                            style: GoogleFonts.cairo(
                              fontSize: 13,
                              color: Colors.grey.shade700)),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: widget.isPaymentVerified ? Colors.green.shade100 : Colors.amber.shade100,
                              borderRadius: BorderRadius.circular(12)),
                            child: Text(
                              widget.isPaymentVerified ? "مفعل" : "قيد المراجعة",
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: widget.isPaymentVerified ? Colors.green.shade800 : Colors.amber.shade800))),
                        ]),
                    ])),
                ]))))));
  }
}
