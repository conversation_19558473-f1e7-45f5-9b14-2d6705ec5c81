import 'package:equatable/equatable.dart';

/// كيان البنك
class Bank extends Equatable {
  final String id;
  final String name;
  final String logoUrl;
  final String description;
  final List<String> supportedMortgageTypes;
  final double minInterestRate;
  final double maxInterestRate;
  final double minDownPaymentPercentage;
  final int maxLoanTerm;
  final String website;
  final String phoneNumber;
  final String email;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان البنك
  const Bank({
    required this.id,
    required this.name,
    required this.logoUrl,
    required this.description,
    required this.supportedMortgageTypes,
    required this.minInterestRate,
    required this.maxInterestRate,
    required this.minDownPaymentPercentage,
    required this.maxLoanTerm,
    required this.website,
    required this.phoneNumber,
    required this.email,
    this.additionalInfo,
  });

  /// إنشاء كيان البنك من JSON
  factory Bank.fromJson(Map<String, dynamic> json) {
    return Bank(
      id: json['id'] as String,
      name: json['name'] as String,
      logoUrl: json['logoUrl'] as String,
      description: json['description'] as String,
      supportedMortgageTypes: List<String>.from(json['supportedMortgageTypes']),
      minInterestRate: json['minInterestRate'] as double,
      maxInterestRate: json['maxInterestRate'] as double,
      minDownPaymentPercentage: json['minDownPaymentPercentage'] as double,
      maxLoanTerm: json['maxLoanTerm'] as int,
      website: json['website'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان البنك إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'logoUrl': logoUrl,
      'description': description,
      'supportedMortgageTypes': supportedMortgageTypes,
      'minInterestRate': minInterestRate,
      'maxInterestRate': maxInterestRate,
      'minDownPaymentPercentage': minDownPaymentPercentage,
      'maxLoanTerm': maxLoanTerm,
      'website': website,
      'phoneNumber': phoneNumber,
      'email': email,
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان البنك مع تعديل بعض الخصائص
  Bank copyWith({
    String? id,
    String? name,
    String? logoUrl,
    String? description,
    List<String>? supportedMortgageTypes,
    double? minInterestRate,
    double? maxInterestRate,
    double? minDownPaymentPercentage,
    int? maxLoanTerm,
    String? website,
    String? phoneNumber,
    String? email,
    Map<String, dynamic>? additionalInfo,
  }) {
    return Bank(
      id: id ?? this.id,
      name: name ?? this.name,
      logoUrl: logoUrl ?? this.logoUrl,
      description: description ?? this.description,
      supportedMortgageTypes: supportedMortgageTypes ?? this.supportedMortgageTypes,
      minInterestRate: minInterestRate ?? this.minInterestRate,
      maxInterestRate: maxInterestRate ?? this.maxInterestRate,
      minDownPaymentPercentage: minDownPaymentPercentage ?? this.minDownPaymentPercentage,
      maxLoanTerm: maxLoanTerm ?? this.maxLoanTerm,
      website: website ?? this.website,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  @override
  List<Object?> get props => [
        id,
        name,
        logoUrl,
        description,
        supportedMortgageTypes,
        minInterestRate,
        maxInterestRate,
        minDownPaymentPercentage,
        maxLoanTerm,
        website,
        phoneNumber,
        email,
        additionalInfo,
      ];
}
