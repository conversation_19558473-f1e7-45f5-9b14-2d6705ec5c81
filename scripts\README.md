# سكريبتات إدارة الإصدارات - تطبيق Krea

هذا المجلد يحتوي على السكريبتات المطلوبة لإدارة وبناء إصدارات تطبيق Krea.

## 📁 الملفات المتاحة

### 🔧 سكريبتات الإعداد

#### `prepare_release.bat`
**الوصف:** يحضر المشروع للإصدار ويتحقق من جميع المتطلبات  
**الاستخدام:**
```bash
scripts\prepare_release.bat
```
**ما يفعله:**
- يتحقق من تثبيت Flutter و Java
- ينظف المشروع ويحدث التبعيات
- يشغل code generation
- يرشدك للخطوات التالية

#### `create_keystore.bat`
**الوصف:** ينشئ مفتاح التوقيع الرقمي المطلوب للنشر  
**الاستخدام:**
```bash
scripts\create_keystore.bat
```
**ما يفعله:**
- ينشئ ملف keystore جديد
- ينشئ ملف key.properties
- يحفظ إعدادات التوقيع

**⚠️ تحذير:** احتفظ بكلمات المرور في مكان آمن!

### 🏗️ سكريبتات البناء

#### `build_release.bat`
**الوصف:** يبني إصدار الإنتاج من التطبيق  
**الاستخدام:**
```bash
scripts\build_release.bat
```
**الخيارات:**
1. **APK** - للاختبار المحلي
2. **App Bundle (AAB)** - للنشر على Google Play (مُفضل)
3. **كلاهما** - ينشئ APK و AAB

**المخرجات:**
- APK: `build\app\outputs\flutter-apk\`
- AAB: `build\app\outputs\bundle\release\app-release.aab`

### ✅ سكريبتات التحقق

#### `check_release_readiness.bat`
**الوصف:** يتحقق من جاهزية المشروع للإصدار  
**الاستخدام:**
```bash
scripts\check_release_readiness.bat
```
**ما يتحقق منه:**
- وجود الملفات الأساسية
- إعدادات Android
- إعدادات Firebase
- مفتاح التوقيع
- تثبيت Flutter
- التبعيات
- ملفات الأصول

## 🚀 سير العمل المُوصى به

### للمرة الأولى:
1. `prepare_release.bat` - تحضير المشروع
2. `create_keystore.bat` - إنشاء مفتاح التوقيع
3. `check_release_readiness.bat` - التحقق من الجاهزية
4. `build_release.bat` - بناء التطبيق

### للإصدارات اللاحقة:
1. `check_release_readiness.bat` - التحقق من الجاهزية
2. `build_release.bat` - بناء التطبيق

## 📋 متطلبات النظام

- **Windows** (السكريبتات مكتوبة لـ Windows)
- **Flutter SDK** 3.6.1 أو أحدث
- **Java JDK** 8 أو أحدث
- **Android SDK** مع Build Tools

## 🔒 أمان الملفات

**ملفات يجب عدم مشاركتها:**
- `android/key.properties`
- `android/upload-keystore.jks`
- أي ملفات تحتوي على كلمات مرور

**ملفات آمنة للمشاركة:**
- `android/key.properties.template`
- جميع السكريبتات في هذا المجلد

## 🆘 استكشاف الأخطاء

### مشكلة: "Flutter غير مثبت"
**الحل:** تأكد من تثبيت Flutter وإضافته إلى PATH

### مشكلة: "Java غير مثبت"
**الحل:** ثبت Java JDK وأضفه إلى PATH

### مشكلة: "فشل في بناء التطبيق"
**الحل:** 
1. تشغيل `flutter clean`
2. تشغيل `flutter pub get`
3. التحقق من سجلات الأخطاء

### مشكلة: "مفتاح التوقيع غير موجود"
**الحل:** تشغيل `create_keystore.bat`

## 📞 الدعم

للحصول على المساعدة:
1. راجع `docs/RELEASE_GUIDE.md`
2. تحقق من سجلات الأخطاء
3. اتصل بفريق التطوير
