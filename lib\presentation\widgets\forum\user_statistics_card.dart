import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/user_statistics_model.dart';

/// بطاقة إحصائيات المستخدم
class UserStatisticsCard extends StatelessWidget {
  /// إحصائيات المستخدم
  final UserStatisticsModel statistics;

  /// ما إذا كانت البطاقة مصغرة
  final bool isCompact;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;

  /// دالة يتم استدعاؤها عند النقر على زر المتابعة
  final VoidCallback? onFollowTap;

  /// ما إذا كان المستخدم متابع
  final bool isFollowing;

  const UserStatisticsCard({
    super.key,
    required this.statistics,
    this.isCompact = false,
    this.onTap,
    this.onFollowTap,
    this.isFollowing = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2)),
          ]),
        child: isCompact ? _buildCompactContent() : _buildFullContent(context)));
  }

  /// بناء المحتوى المصغر
  Widget _buildCompactContent() {
    return Row(
      children: [
        // صورة المستخدم
        _buildUserAvatar(),
        const SizedBox(width: 12),

        // معلومات المستخدم
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                statistics.userName,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16)),
              const SizedBox(height: 4),
              Row(
                children: [
                  _buildStatItem(Icons.star, statistics.level.toString(), 'المستوى'),
                  const SizedBox(width: 12),
                  _buildStatItem(
                    Icons.forum,
                    statistics.topicsCount.toString(),
                    'المواضيع'),
                  const SizedBox(width: 12),
                  _buildStatItem(
                    Icons.comment,
                    statistics.postsCount.toString(),
                    'المشاركات'),
                ]),
            ])),

        // زر المتابعة
        if (onFollowTap != null)
          IconButton(
            onPressed: onFollowTap,
            icon: Icon(
              isFollowing ? Icons.person_remove : Icons.person_add,
              color: isFollowing ? Colors.red : AppColors.primary),
            tooltip: isFollowing ? 'إلغاء المتابعة' : 'متابعة'),
      ]);
  }

  /// بناء المحتوى الكامل
  Widget _buildFullContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // معلومات المستخدم
        Row(
          children: [
            _buildUserAvatar(radius: 30),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statistics.userName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18)),
                  const SizedBox(height: 4),
                  Text(
                    'عضو منذ ${DateFormat('yyyy/MM/dd').format(statistics.joinDate)}',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12)),
                  const SizedBox(height: 2),
                  Text(
                    'آخر نشاط: ${DateFormat('yyyy/MM/dd HH:mm').format(statistics.lastActivityDate)}',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12)),
                ])),
            if (onFollowTap != null)
              ElevatedButton.icon(
                onPressed: onFollowTap,
                icon: Icon(
                  isFollowing ? Icons.person_remove : Icons.person_add,
                  size: 16),
                label: Text(isFollowing ? 'إلغاء المتابعة' : 'متابعة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isFollowing ? Colors.red : AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8))),
          ]),
        const SizedBox(height: 16),

        // شريط المستوى
        _buildLevelProgressBar(),
        const SizedBox(height: 16),

        // الإحصائيات
        _buildStatisticsGrid(),
        const SizedBox(height: 16),

        // الإنجازات
        if (statistics.achievements != null && statistics.achievements!.isNotEmpty) ...[
          const Text(
            'الإنجازات',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16)),
          const SizedBox(height: 8),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: statistics.achievements!.length,
              itemBuilder: (context, index) {
                final achievementMap = statistics.achievements![index];
                final name = achievementMap['name'] as String? ?? '';
                final imageUrl = achievementMap['imageUrl'] as String?;
                final isCompleted = achievementMap['isCompleted'] as bool? ?? false;

                return Tooltip(
                  message: name,
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isCompleted
                            ? AppColors.primary
                            : Colors.grey.shade300)),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (imageUrl != null)
                          Image.network(
                            imageUrl,
                            width: 32,
                            height: 32)
                        else
                          Icon(
                            Icons.emoji_events,
                            color: isCompleted
                                ? Colors.amber
                                : Colors.grey.shade400,
                            size: 32),
                        const SizedBox(height: 4),
                        Text(
                          name,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: isCompleted
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: isCompleted
                                ? AppColors.primary
                                : Colors.grey.shade600),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis),
                      ])));
              })),
        ],
      ]);
  }

  /// بناء صورة المستخدم
  Widget _buildUserAvatar({double radius = 20}) {
    return CircleAvatar(
      radius: radius,
      backgroundImage: statistics.userImage != null
          ? CachedNetworkImageProvider(statistics.userImage!)
          : null,
      backgroundColor: Color.fromRGBO(
        AppColors.primary.r.toInt(),
        AppColors.primary.g.toInt(),
        AppColors.primary.b.toInt(),
        0.1),
      child: statistics.userImage == null
          ? Text(
              statistics.userName.isNotEmpty
                  ? statistics.userName[0].toUpperCase()
                  : '?',
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
                fontSize: radius * 0.8))
          : null);
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(IconData icon, String value, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12)),
        const SizedBox(width: 2),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 12)),
      ]);
  }

  /// بناء شريط تقدم المستوى
  Widget _buildLevelProgressBar() {
    // تحديد النقاط المطلوبة للمستوى التالي (100 نقطة لكل مستوى)
    final nextLevelPoints = (int.tryParse(statistics.level) ?? 1) * 100;
    final progress = statistics.points / nextLevelPoints;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 20),
                const SizedBox(width: 4),
                Text(
                  'المستوى ${statistics.level}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold)),
              ]),
            Text(
              '${statistics.points}/$nextLevelPoints نقطة',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12)),
          ]),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey.shade200,
          valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
          minHeight: 8,
          borderRadius: BorderRadius.circular(4)),
      ]);
  }

  /// بناء شبكة الإحصائيات
  Widget _buildStatisticsGrid() {
    return GridView.count(
      crossAxisCount: 3,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          Icons.forum,
          statistics.topicsCount.toString(),
          'المواضيع'),
        _buildStatCard(
          Icons.comment,
          statistics.postsCount.toString(),
          'المشاركات'),
        _buildStatCard(
          Icons.thumb_up,
          statistics.totalLikesGiven.toString(),
          'الإعجابات'),
        _buildStatCard(
          Icons.favorite,
          (statistics.totalTopicLikes + statistics.totalPostLikes).toString(),
          'الإعجابات المستلمة'),
        _buildStatCard(
          Icons.bookmark,
          statistics.topicBookmarksCount.toString(),
          'المحفوظات'),
        _buildStatCard(
          Icons.visibility,
          statistics.viewsCount.toString(),
          'المشاهدات'),
      ]);
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(IconData icon, String value, String label) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: AppColors.primary,
            size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14)),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 12),
            textAlign: TextAlign.center),
        ]));
  }
}
