import 'dart:developer' as developer;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';

import '../../domain/models/forum/poll_model.dart';
import '../../domain/models/forum/poll_option_model.dart';
import 'rewards_service.dart';

/// خدمة إدارة الاستطلاعات والتصويت
class PollService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final RewardsService _rewardsService = RewardsService();
  final Uuid _uuid = const Uuid();

  /// إنشاء استطلاع رأي جديد
  Future<PollModel?> createPoll({
    required String topicId,
    required String question,
    String? description,
    required List<String> options,
    required bool allowMultipleChoices,
    required String createdBy,
    DateTime? endDate,
  }) async {
    try {
      final pollId = _uuid.v4();
      
      // إنشاء خيارات الاستطلاع
      final pollOptions = options.map((option) => PollOptionModel(
        id: _uuid.v4(),
        text: option,
        votesCount: 0,
        votedBy: [],
      )).toList();

      final poll = PollModel(
        id: pollId,
        question: question,
        description: description,
        options: pollOptions,
        allowMultipleChoices: allowMultipleChoices,
        startDate: DateTime.now(),
        endDate: endDate,
        createdBy: createdBy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ الاستطلاع في Firestore
      await _firestore.collection('forum_polls').doc(pollId).set(poll.toMap());

      // ربط الاستطلاع بالموضوع
      await _firestore.collection('forum_topics').doc(topicId).update({
        'polls': FieldValue.arrayUnion([pollId]),
        'updatedAt': Timestamp.now(),
      });

      // منح نقاط لمنشئ الاستطلاع
      await _rewardsService.awardPointsForActivity(createdBy, 'create_poll');

      developer.log('تم إنشاء استطلاع رأي جديد: $pollId');
      return poll;
    } catch (e) {
      developer.log('خطأ في إنشاء الاستطلاع: $e');
      return null;
    }
  }

  /// التصويت في استطلاع
  Future<bool> voteInPoll({
    required String pollId,
    required List<String> optionIds,
    required String userId,
  }) async {
    try {
      final pollRef = _firestore.collection('forum_polls').doc(pollId);
      
      return await _firestore.runTransaction((transaction) async {
        final pollDoc = await transaction.get(pollRef);
        if (!pollDoc.exists) return false;

        final poll = PollModel.fromFirestore(pollDoc);
        
        // فحص إذا كان الاستطلاع منتهي الصلاحية
        if (poll.endDate != null && DateTime.now().isAfter(poll.endDate!)) {
          return false;
        }

        // فحص إذا كان المستخدم صوت بالفعل
        final hasVoted = poll.options.any((option) => option.votedBy.contains(userId));
        if (hasVoted) {
          return false; // المستخدم صوت بالفعل
        }

        // فحص الخيارات المتعددة
        if (!poll.allowMultipleChoices && optionIds.length > 1) {
          return false;
        }

        // تحديث الخيارات
        final updatedOptions = poll.options.map((option) {
          if (optionIds.contains(option.id)) {
            return option.copyWith(
              votesCount: option.votesCount + 1,
              votedBy: [...option.votedBy, userId],
            );
          }
          return option;
        }).toList();

        // تحديث الاستطلاع
        transaction.update(pollRef, {
          'options': updatedOptions.map((option) => option.toMap()).toList(),
          'updatedAt': Timestamp.now(),
        });

        // تسجيل التصويت
        await _logVote(pollId, optionIds, userId);

        // منح نقاط للمصوت
        await _rewardsService.awardPointsForActivity(userId, 'vote_poll');

        return true;
      });
    } catch (e) {
      developer.log('خطأ في التصويت: $e');
      return false;
    }
  }

  /// إلغاء التصويت
  Future<bool> removeVote({
    required String pollId,
    required String userId,
  }) async {
    try {
      final pollRef = _firestore.collection('forum_polls').doc(pollId);
      
      return await _firestore.runTransaction((transaction) async {
        final pollDoc = await transaction.get(pollRef);
        if (!pollDoc.exists) return false;

        final poll = PollModel.fromFirestore(pollDoc);
        
        // تحديث الخيارات
        final updatedOptions = poll.options.map((option) {
          if (option.votedBy.contains(userId)) {
            final newVotedBy = option.votedBy.where((id) => id != userId).toList();
            return option.copyWith(
              votesCount: option.votesCount - 1,
              votedBy: newVotedBy,
            );
          }
          return option;
        }).toList();

        // تحديث الاستطلاع
        transaction.update(pollRef, {
          'options': updatedOptions.map((option) => option.toMap()).toList(),
          'updatedAt': Timestamp.now(),
        });

        // حذف سجل التصويت
        await _removeVoteLog(pollId, userId);

        return true;
      });
    } catch (e) {
      developer.log('خطأ في إلغاء التصويت: $e');
      return false;
    }
  }

  /// الحصول على استطلاع بواسطة المعرف
  Future<PollModel?> getPollById(String pollId) async {
    try {
      final doc = await _firestore.collection('forum_polls').doc(pollId).get();
      if (doc.exists) {
        return PollModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      developer.log('خطأ في الحصول على الاستطلاع: $e');
      return null;
    }
  }

  /// الحصول على استطلاعات موضوع
  Future<List<PollModel>> getTopicPolls(String topicId) async {
    try {
      final topicDoc = await _firestore.collection('forum_topics').doc(topicId).get();
      if (!topicDoc.exists) return [];

      final pollIds = List<String>.from(topicDoc.data()?['polls'] ?? []);
      if (pollIds.isEmpty) return [];

      final polls = <PollModel>[];
      for (final pollId in pollIds) {
        final poll = await getPollById(pollId);
        if (poll != null) {
          polls.add(poll);
        }
      }

      return polls;
    } catch (e) {
      developer.log('خطأ في الحصول على استطلاعات الموضوع: $e');
      return [];
    }
  }

  /// الحصول على نتائج الاستطلاع
  Future<Map<String, dynamic>> getPollResults(String pollId) async {
    try {
      final poll = await getPollById(pollId);
      if (poll == null) return {};

      final totalVotes = poll.options.fold<int>(0, (sum, option) => sum + option.votesCount);
      
      final results = poll.options.map((option) {
        final percentage = totalVotes > 0 ? (option.votesCount / totalVotes * 100) : 0.0;
        return {
          'id': option.id,
          'text': option.text,
          'votes': option.votesCount,
          'percentage': percentage,
        };
      }).toList();

      return {
        'pollId': pollId,
        'question': poll.question,
        'totalVotes': totalVotes,
        'results': results,
        'isActive': poll.endDate == null || DateTime.now().isBefore(poll.endDate!),
      };
    } catch (e) {
      developer.log('خطأ في الحصول على نتائج الاستطلاع: $e');
      return {};
    }
  }

  /// فحص إذا كان المستخدم صوت في الاستطلاع
  Future<bool> hasUserVoted(String pollId, String userId) async {
    try {
      final poll = await getPollById(pollId);
      if (poll == null) return false;

      return poll.options.any((option) => option.votedBy.contains(userId));
    } catch (e) {
      developer.log('خطأ في فحص التصويت: $e');
      return false;
    }
  }

  /// الحصول على خيارات المستخدم المصوت عليها
  Future<List<String>> getUserVotedOptions(String pollId, String userId) async {
    try {
      final poll = await getPollById(pollId);
      if (poll == null) return [];

      final votedOptions = <String>[];
      for (final option in poll.options) {
        if (option.votedBy.contains(userId)) {
          votedOptions.add(option.id);
        }
      }

      return votedOptions;
    } catch (e) {
      developer.log('خطأ في الحصول على خيارات المستخدم: $e');
      return [];
    }
  }

  /// تسجيل التصويت
  Future<void> _logVote(String pollId, List<String> optionIds, String userId) async {
    try {
      await _firestore.collection('poll_votes').add({
        'pollId': pollId,
        'optionIds': optionIds,
        'userId': userId,
        'timestamp': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في تسجيل التصويت: $e');
    }
  }

  /// حذف سجل التصويت
  Future<void> _removeVoteLog(String pollId, String userId) async {
    try {
      final snapshot = await _firestore
          .collection('poll_votes')
          .where('pollId', isEqualTo: pollId)
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in snapshot.docs) {
        await doc.reference.delete();
      }
    } catch (e) {
      developer.log('خطأ في حذف سجل التصويت: $e');
    }
  }

  /// الحصول على الاستطلاعات النشطة
  Future<List<PollModel>> getActivePolls({int limit = 10}) async {
    try {
      final now = Timestamp.now();
      final snapshot = await _firestore
          .collection('forum_polls')
          .where('endDate', isGreaterThan: now)
          .orderBy('endDate')
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) => PollModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على الاستطلاعات النشطة: $e');
      return [];
    }
  }

  /// الحصول على الاستطلاعات المنتهية
  Future<List<PollModel>> getExpiredPolls({int limit = 10}) async {
    try {
      final now = Timestamp.now();
      final snapshot = await _firestore
          .collection('forum_polls')
          .where('endDate', isLessThan: now)
          .orderBy('endDate', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) => PollModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على الاستطلاعات المنتهية: $e');
      return [];
    }
  }
}
