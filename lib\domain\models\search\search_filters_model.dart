// lib/domain/models/search/search_filters_model.dart
import 'package:equatable/equatable.dart';

/// نموذج فلاتر البحث
class SearchFiltersModel extends Equatable {
  /// نوع العقار
  final String? propertyType;

  /// الحد الأدنى للسعر
  final double? minPrice;

  /// الحد الأقصى للسعر
  final double? maxPrice;

  /// المناطق المفضلة
  final List<String>? preferredLocations;

  /// الحد الأدنى لعدد الغرف
  final int? minRooms;

  /// الحد الأدنى لعدد الحمامات
  final int? minBathrooms;

  /// الحد الأدنى للمساحة
  final double? minArea;

  /// هل يشترط وجود تكييف مركزي
  final bool? hasCentralAC;

  /// هل يشترط وجود غرفة خادمة
  final bool? hasMaidRoom;

  /// هل يشترط وجود مرآب
  final bool? hasGarage;

  /// هل يشترط وجود مسبح
  final bool? hasSwimmingPool;

  /// هل يشترط وجود مصعد
  final bool? hasElevator;

  /// هل يشترط أن يكون مفروش بالكامل
  final bool? isFullyFurnished;

  /// تاريخ البداية للبحث
  final DateTime? startDate;

  /// تاريخ النهاية للبحث
  final DateTime? endDate;

  /// ترتيب النتائج
  final String sortBy;

  /// ترتيب تنازلي
  final bool descending;

  /// نطاق البحث الجغرافي (بالكيلومتر)
  final double? searchRadius;

  /// خط العرض للموقع المرجعي
  final double? latitude;

  /// خط الطول للموقع المرجعي
  final double? longitude;

  const SearchFiltersModel({
    this.propertyType,
    this.minPrice,
    this.maxPrice,
    this.preferredLocations,
    this.minRooms,
    this.minBathrooms,
    this.minArea,
    this.hasCentralAC,
    this.hasMaidRoom,
    this.hasGarage,
    this.hasSwimmingPool,
    this.hasElevator,
    this.isFullyFurnished,
    this.startDate,
    this.endDate,
    this.sortBy = 'createdAt',
    this.descending = true,
    this.searchRadius,
    this.latitude,
    this.longitude,
  });

  /// إنشاء نسخة محدثة من الفلاتر
  SearchFiltersModel copyWith({
    String? propertyType,
    double? minPrice,
    double? maxPrice,
    List<String>? preferredLocations,
    int? minRooms,
    int? minBathrooms,
    double? minArea,
    bool? hasCentralAC,
    bool? hasMaidRoom,
    bool? hasGarage,
    bool? hasSwimmingPool,
    bool? hasElevator,
    bool? isFullyFurnished,
    DateTime? startDate,
    DateTime? endDate,
    String? sortBy,
    bool? descending,
    double? searchRadius,
    double? latitude,
    double? longitude,
  }) {
    return SearchFiltersModel(
      propertyType: propertyType ?? this.propertyType,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      preferredLocations: preferredLocations ?? this.preferredLocations,
      minRooms: minRooms ?? this.minRooms,
      minBathrooms: minBathrooms ?? this.minBathrooms,
      minArea: minArea ?? this.minArea,
      hasCentralAC: hasCentralAC ?? this.hasCentralAC,
      hasMaidRoom: hasMaidRoom ?? this.hasMaidRoom,
      hasGarage: hasGarage ?? this.hasGarage,
      hasSwimmingPool: hasSwimmingPool ?? this.hasSwimmingPool,
      hasElevator: hasElevator ?? this.hasElevator,
      isFullyFurnished: isFullyFurnished ?? this.isFullyFurnished,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      sortBy: sortBy ?? this.sortBy,
      descending: descending ?? this.descending,
      searchRadius: searchRadius ?? this.searchRadius,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'propertyType': propertyType,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'preferredLocations': preferredLocations,
      'minRooms': minRooms,
      'minBathrooms': minBathrooms,
      'minArea': minArea,
      'hasCentralAC': hasCentralAC,
      'hasMaidRoom': hasMaidRoom,
      'hasGarage': hasGarage,
      'hasSwimmingPool': hasSwimmingPool,
      'hasElevator': hasElevator,
      'isFullyFurnished': isFullyFurnished,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'sortBy': sortBy,
      'descending': descending,
      'searchRadius': searchRadius,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  /// إنشاء من Map
  factory SearchFiltersModel.fromMap(Map<String, dynamic> map) {
    return SearchFiltersModel(
      propertyType: map['propertyType'],
      minPrice: map['minPrice']?.toDouble(),
      maxPrice: map['maxPrice']?.toDouble(),
      preferredLocations: map['preferredLocations'] != null
          ? List<String>.from(map['preferredLocations'])
          : null,
      minRooms: map['minRooms']?.toInt(),
      minBathrooms: map['minBathrooms']?.toInt(),
      minArea: map['minArea']?.toDouble(),
      hasCentralAC: map['hasCentralAC'],
      hasMaidRoom: map['hasMaidRoom'],
      hasGarage: map['hasGarage'],
      hasSwimmingPool: map['hasSwimmingPool'],
      hasElevator: map['hasElevator'],
      isFullyFurnished: map['isFullyFurnished'],
      startDate: map['startDate'] != null ? DateTime.parse(map['startDate']) : null,
      endDate: map['endDate'] != null ? DateTime.parse(map['endDate']) : null,
      sortBy: map['sortBy'] ?? 'createdAt',
      descending: map['descending'] ?? true,
      searchRadius: map['searchRadius']?.toDouble(),
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
    );
  }

  /// التحقق من وجود فلاتر نشطة
  bool get hasActiveFilters {
    return propertyType != null ||
        minPrice != null ||
        maxPrice != null ||
        (preferredLocations != null && preferredLocations!.isNotEmpty) ||
        minRooms != null ||
        minBathrooms != null ||
        minArea != null ||
        hasCentralAC == true ||
        hasMaidRoom == true ||
        hasGarage == true ||
        hasSwimmingPool == true ||
        hasElevator == true ||
        isFullyFurnished == true ||
        startDate != null ||
        endDate != null ||
        searchRadius != null;
  }

  /// عدد الفلاتر النشطة
  int get activeFiltersCount {
    int count = 0;
    if (propertyType != null) count++;
    if (minPrice != null) count++;
    if (maxPrice != null) count++;
    if (preferredLocations != null && preferredLocations!.isNotEmpty) count++;
    if (minRooms != null) count++;
    if (minBathrooms != null) count++;
    if (minArea != null) count++;
    if (hasCentralAC == true) count++;
    if (hasMaidRoom == true) count++;
    if (hasGarage == true) count++;
    if (hasSwimmingPool == true) count++;
    if (hasElevator == true) count++;
    if (isFullyFurnished == true) count++;
    if (startDate != null) count++;
    if (endDate != null) count++;
    if (searchRadius != null) count++;
    return count;
  }

  /// إعادة تعيين جميع الفلاتر
  SearchFiltersModel reset() {
    return const SearchFiltersModel();
  }

  @override
  List<Object?> get props => [
        propertyType,
        minPrice,
        maxPrice,
        preferredLocations,
        minRooms,
        minBathrooms,
        minArea,
        hasCentralAC,
        hasMaidRoom,
        hasGarage,
        hasSwimmingPool,
        hasElevator,
        isFullyFurnished,
        startDate,
        endDate,
        sortBy,
        descending,
        searchRadius,
        latitude,
        longitude,
      ];
}
