import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import 'simple_forum_background.dart';

/// مكون رأس المنتدى الحديث
class ModernForumHeader extends StatelessWidget {
  /// عنوان الرأس
  final String title;

  /// وصف الرأس (اختياري)
  final String? description;

  /// أيقونة الرأس (اختياري)
  final IconData? icon;

  /// لون الخلفية (اختياري)
  final Color? backgroundColor;

  /// دالة يتم استدعاؤها عند النقر على زر البحث
  final VoidCallback? onSearchTap;

  /// دالة يتم استدعاؤها عند النقر على زر الإشعارات
  final VoidCallback? onNotificationsTap;

  /// دالة يتم استدعاؤها عند النقر على زر الأقسام
  final VoidCallback? onCategoriesTap;

  /// دالة يتم استدعاؤها عند تقديم البحث
  final Function(String)? onSearchSubmitted;

  /// ما إذا كان البحث نشطاً
  final bool isSearchActive;

  /// تحكم النص للبحث
  final TextEditingController? searchController;

  /// دالة يتم استدعاؤها عند إلغاء البحث
  final VoidCallback? onSearchCancel;

  const ModernForumHeader({
    super.key,
    required this.title,
    this.description,
    this.icon,
    this.backgroundColor,
    this.onSearchTap,
    this.onNotificationsTap,
    this.onCategoriesTap,
    this.onSearchSubmitted,
    this.isSearchActive = false,
    this.searchController,
    this.onSearchCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5)),
        ]),
      clipBehavior: Clip.hardEdge,
      child: Stack(
        children: [
          // الخلفية البسيطة
          SimpleForumBackground(
            primaryColor: backgroundColor ?? AppColors.primary,
            secondaryColor: (backgroundColor ?? AppColors.primary).withOpacity(0.8)),

          // المحتوى
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // شريط العنوان مع أزرار الإجراءات
                Row(
                  children: [
                    if (!isSearchActive) ...[
                      // أيقونة اللوبي
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12)),
                        child: Icon(
                          icon ?? Icons.forum,
                          color: Colors.white,
                          size: 24)),
                      const SizedBox(width: 12),

                      // عنوان اللوبي
                      Expanded(
                        child: Text(
                          title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 22,
                            fontWeight: FontWeight.bold))),
                    ] else ...[
                      // حقل البحث
                      Expanded(
                        child: Container(
                          height: 44,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12)),
                          child: TextField(
                            controller: searchController,
                            style: const TextStyle(color: Colors.white),
                            decoration: InputDecoration(
                              hintText: 'ابحث في المنتدى...',
                              hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
                              prefixIcon: const Icon(Icons.search, color: Colors.white),
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12)),
                            onSubmitted: onSearchSubmitted))),
                    ],

                    // أزرار الإجراءات
                    const SizedBox(width: 8),
                    if (!isSearchActive)
                      IconButton(
                        onPressed: onSearchTap,
                        icon: const Icon(Icons.search, color: Colors.white),
                        tooltip: 'بحث')
                    else
                      IconButton(
                        onPressed: onSearchCancel,
                        icon: const Icon(Icons.close, color: Colors.white),
                        tooltip: 'إلغاء البحث'),

                    if (onCategoriesTap != null)
                      IconButton(
                        onPressed: onCategoriesTap,
                        icon: const Icon(Icons.category_outlined, color: Colors.white),
                        tooltip: 'الأقسام'),

                    IconButton(
                      onPressed: onNotificationsTap,
                      icon: const Icon(Icons.notifications_outlined, color: Colors.white),
                      tooltip: 'الإشعارات'),
                  ]),

                // الوصف (إذا كان موجوداً)
                if (description != null && !isSearchActive) ...[
                  const SizedBox(height: 16),
                  Text(
                    description!,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14)),
                ],
            ])),
        ]));
  }
}
