// lib/presentation/pages/account_upgrade_page.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/presentation/widgets/loading_indicator.dart';

class AccountUpgradePage extends StatefulWidget {
  const AccountUpgradePage({super.key});

  @override
  State<AccountUpgradePage> createState() => _AccountUpgradePageState();
}

class _AccountUpgradePageState extends State<AccountUpgradePage> {
  String _selectedUpgrade = "none"; // الخيارات: none, نحاسي, فضي, ذهبي
  bool _isLoading = false;
  bool _isInitialLoading = true;
  String _currentUpgrade = "none";

  // معلومات الخطط
  final Map<String, Map<String, dynamic>> _planDetails = {
    "نحاسي": {
      "name": "الخطة النحاسية",
      "color": const Color(0xFFCD7F32), // لون نحاسي
      "price": 5.0,
      "duration": 30,
      "features": [
        "نشر 5 إعلانات شهرياً",
        "إضافة 5 صور لكل إعلان",
        "عرض الإعلان لمدة 30 يوم",
        "دعم فني عبر البريد الإلكتروني",
      ],
      "icon": Icons.workspace_premium,
    },
    "فضي": {
      "name": "الخطة الفضية",
      "color": const Color(0xFFC0C0C0), // لون فضي
      "price": 15.0,
      "duration": 30,
      "features": [
        "نشر 15 إعلان شهرياً",
        "إضافة 10 صور لكل إعلان",
        "عرض الإعلان لمدة 45 يوم",
        "إمكانية إضافة فيديو للإعلان",
        "تثبيت إعلان واحد في القسم",
        "دعم فني على مدار الساعة",
      ],
      "icon": Icons.diamond,
    },
    "ذهبي": {
      "name": "الخطة الذهبية",
      "color": const Color(0xFFFFD700), // لون ذهبي
      "price": 30.0,
      "duration": 30,
      "features": [
        "نشر عدد غير محدود من الإعلانات",
        "إضافة 20 صورة لكل إعلان",
        "عرض الإعلان لمدة 60 يوم",
        "إمكانية إضافة فيديو للإعلان",
        "تثبيت 3 إعلانات في الأقسام",
        "إعلان مميز في الصفحة الرئيسية",
        "شارة VIP على الحساب",
        "دعم فني متميز على مدار الساعة",
      ],
      "icon": Icons.stars,
    },
  };

  @override
  void initState() {
    super.initState();
    _loadCurrentUpgrade();
  }

  // تحميل حالة الترقية الحالية
  Future<void> _loadCurrentUpgrade() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userData = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        if (userData.exists && userData.data()!.containsKey('upgradeStatus')) {
          setState(() {
            _currentUpgrade = userData.data()!['upgradeStatus'] ?? "none";
            _selectedUpgrade = _currentUpgrade;
          });
        }
      }
    } catch (e) {
      // تجاهل الأخطاء
    } finally {
      setState(() {
        _isInitialLoading = false;
      });
    }
  }

  // حفظ حالة الترقية في بيانات المستخدم
  Future<void> _saveUpgrade() async {
    if (_selectedUpgrade == "none" || _selectedUpgrade == _currentUpgrade) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .update({
          'upgradeStatus': _selectedUpgrade,
        });

        setState(() {
          _currentUpgrade = _selectedUpgrade;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  "تم ترقية الحساب إلى ${_planDetails[_selectedUpgrade]!['name']} بنجاح"),
              backgroundColor: AppColors.success));
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("حدث خطأ أثناء ترقية الحساب: $e"),
            backgroundColor: AppColors.error));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text("ترقية الحساب"),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.black87)),
      body: _isInitialLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الصفحة
                  Text(
                    "اختر خطة الترقية المناسبة لك",
                    style: GoogleFonts.cairo(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary)),

                  const SizedBox(height: 8),

                  // وصف الصفحة
                  Text(
                    "ترقية حسابك تمنحك المزيد من المميزات والإمكانيات لعرض عقاراتك بشكل أفضل",
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: AppColors.textSecondary)),

                  const SizedBox(height: 24),

                  // بطاقات الخطط
                  ..._planDetails.entries
                      .map((entry) => _buildPlanCard(entry.key)),

                  const SizedBox(height: 32),

                  // زر التأكيد
                  if (_selectedUpgrade != _currentUpgrade)
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveUpgrade,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                          elevation: 2),
                        child: _isLoading
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2))
                            : Text(
                                "تأكيد الترقية",
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold)))),
                ])));
  }

  // بناء بطاقة الخطة
  Widget _buildPlanCard(String planKey) {
    final plan = _planDetails[planKey]!;
    final isSelected = _selectedUpgrade == planKey;
    final isCurrent = _currentUpgrade == planKey;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedUpgrade = planKey;
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: isSelected
                    ? Color.fromRGBO(
                        plan['color'].red,
                        plan['color'].green,
                        plan['color'].blue,
                        0.3)
                    : Colors.black.withAlpha(13),
                blurRadius: 10,
                offset: const Offset(0, 4)),
            ],
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isSelected
                  ? [
                      Colors.white,
                      plan['color'].withOpacity(0.1),
                    ]
                  : [
                      Colors.white,
                      Colors.white,
                    ]),
            border: Border.all(
              color: isSelected ? plan['color'] : Colors.grey.withAlpha(50),
              width: isSelected ? 2 : 1)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                decoration: BoxDecoration(
                  color: plan['color'].withOpacity(0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16))),
                child: Row(
                  children: [
                    Icon(
                      plan['icon'],
                      color: plan['color'],
                      size: 28),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            plan['name'],
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary)),
                          Text(
                            "${plan['price']} د.ك / ${plan['duration']} يوم",
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: AppColors.textSecondary)),
                        ])),
                    Radio<String>(
                      value: planKey,
                      groupValue: _selectedUpgrade,
                      activeColor: plan['color'],
                      onChanged: (value) {
                        setState(() {
                          _selectedUpgrade = value!;
                        });
                      }),
                  ])),

              // محتوى البطاقة
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (isCurrent)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: AppColors.success.withAlpha(25),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: AppColors.success.withAlpha(75))),
                        child: Text(
                          "الخطة الحالية",
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppColors.success))),
                    Text(
                      "المميزات:",
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary)),
                    const SizedBox(height: 12),
                    ...List.generate(
                      plan['features'].length,
                      (index) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: plan['color'],
                              size: 18),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                plan['features'][index],
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: AppColors.textPrimary))),
                          ]))),
                  ])),
            ]))));
  }
}
