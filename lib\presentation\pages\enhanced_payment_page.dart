// lib/presentation/pages/enhanced_payment_page.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../core/models/plan_model.dart';
import '../../core/models/user_subscription_model.dart';
import '../../core/routes/app_routes.dart';
import '../../core/services/enhanced_subscription_service.dart';
import '../../core/theme/app_colors.dart';
import '../../domain/entities/estate.dart';

/// صفحة الدفع المحسنة
class EnhancedPaymentPage extends StatefulWidget {
  /// العقار المراد الدفع له
  final Estate estate;

  /// المبلغ الإجمالي للدفع
  final double totalAmount;

  /// الميزات المختارة
  final List<String> selectedFeatures;

  /// ما إذا كان تحديثًا لإعلان موجود
  final bool isUpdate;

  const EnhancedPaymentPage({
    super.key,
    required this.estate,
    required this.selectedFeatures,
    required this.totalAmount,
    this.isUpdate = false,
  });

  @override
  State<EnhancedPaymentPage> createState() => _EnhancedPaymentPageState();
}

class _EnhancedPaymentPageState extends State<EnhancedPaymentPage> {
  final bool _isProcessing = false;
  final bool _isCompleted = false;

  // خدمة الاشتراكات
  final _subscriptionService = EnhancedSubscriptionService();

  // متغيرات الاشتراك
  UserSubscriptionModel? _subscription;
  PlanModel? _plan;
  bool _isLoadingSubscription = true;

  @override
  void initState() {
    super.initState();

    // تحميل معلومات الاشتراك
    _loadSubscriptionInfo();
  }

  /// تحميل معلومات الاشتراك
  Future<void> _loadSubscriptionInfo() async {
    setState(() {
      _isLoadingSubscription = true;
    });

    try {
      // الحصول على الاشتراك الحالي
      final subscription = await _subscriptionService.getCurrentSubscription();

      if (subscription != null) {
        // الحصول على الباقة الحالية
        final plans = await _subscriptionService.getAllPlans();
        final plan = plans.firstWhere(
          (plan) => plan.id == subscription.planId,
          orElse: () => plans.first);

        setState(() {
          _subscription = subscription;
          _plan = plan;
          _isLoadingSubscription = false;
        });
      } else {
        setState(() {
          _isLoadingSubscription = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingSubscription = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "الدفع",
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0),
      body: _isCompleted ? _buildSuccessView() : _buildPaymentView());
  }

  /// الانتقال إلى صفحة التحويل البنكي
  void _navigateToBankTransfer() {
    Navigator.pushNamed(
      context,
      AppRoutes.bankTransfer,
      arguments: {
        'estate': widget.estate,
        'selectedFeatures': widget.selectedFeatures,
        'totalAmount': widget.totalAmount,
        'isUpdate': widget.isUpdate,
      });
  }

  /// الانتقال إلى صفحة دفع ومض
  void _navigateToWamdPayment() {
    Navigator.pushNamed(
      context,
      AppRoutes.wamdPayment,
      arguments: {
        'estate': widget.estate,
        'selectedFeatures': widget.selectedFeatures,
        'totalAmount': widget.totalAmount,
        'isUpdate': widget.isUpdate,
      });
  }

  /// معالجة الدفع
  Future<void> _processPayment() async {
    if (_isProcessing) return;

    // الانتقال إلى صفحة التحويل البنكي
    _navigateToBankTransfer();
  }

  /// بناء واجهة الدفع
  Widget _buildPaymentView() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // بطاقة تفاصيل الدفع
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "تفاصيل الدفع",
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 16),

                  // تفاصيل المبلغ
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "المبلغ الإجمالي:",
                        style: GoogleFonts.cairo(
                          fontSize: 16)),
                      Text(
                        "${widget.totalAmount.toStringAsFixed(2)} د.ك",
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary)),
                    ]),

                  const SizedBox(height: 16),

                  // معلومات الباقة
                  if (_plan != null && _subscription != null) ...[
                    const Divider(),
                    const SizedBox(height: 12),

                    // عنوان مع أيقونة
                    Row(
                      children: [
                        Icon(
                          Icons.workspace_premium,
                          color: _plan!.color,
                          size: 20),
                        const SizedBox(width: 8),
                        Text(
                          "معلومات الباقة",
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: _plan!.color)),
                      ]),

                    const SizedBox(height: 12),

                    // بطاقة معلومات الباقة
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _plan!.color.withAlpha(15),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _plan!.color.withAlpha(50),
                          width: 1)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // اسم الباقة
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "الباقة الحالية:",
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: Colors.grey.shade700)),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 4),
                                decoration: BoxDecoration(
                                  color: _plan!.color.withAlpha(30),
                                  borderRadius: BorderRadius.circular(8)),
                                child: Text(
                                  _plan!.nameAr,
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: _plan!.color))),
                            ]),

                          const SizedBox(height: 10),

                          // عدد الإعلانات المتبقية
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "الإعلانات المتبقية:",
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: Colors.grey.shade700)),
                              Row(
                                children: [
                                  Icon(
                                    _subscription!.remainingAds > 0
                                        ? Icons.check_circle
                                        : Icons.warning,
                                    size: 16,
                                    color: _subscription!.remainingAds > 0
                                        ? Colors.green
                                        : Colors.amber),
                                  const SizedBox(width: 4),
                                  Text(
                                    "${_subscription!.remainingAds}/${_subscription!.allowedAds}",
                                    style: GoogleFonts.cairo(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: _subscription!.remainingAds > 0
                                          ? Colors.green
                                          : Colors.amber)),
                                ]),
                            ]),

                          const SizedBox(height: 10),

                          // مدة الإعلان
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "مدة عرض الإعلان:",
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: Colors.grey.shade700)),
                              Text(
                                "${_subscription!.adDurationDays} يوم",
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey.shade800)),
                            ]),

                          // تاريخ انتهاء الاشتراك
                          ...[
                          const SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "تاريخ انتهاء الاشتراك:",
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: Colors.grey.shade700)),
                              Text(
                                "${_subscription!.endDate.day}/${_subscription!.endDate.month}/${_subscription!.endDate.year}",
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey.shade800)),
                            ]),
                        ],
                        ])),

                    // تنبيه عند اقتراب نفاد الإعلانات
                    if (_subscription!.remainingAds <= 3 && _subscription!.remainingAds > 0) ...[
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Colors.amber.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.amber.shade200,
                            width: 1)),
                        child: Row(
                          children: [
                            Icon(
                              Icons.warning_amber,
                              color: Colors.amber.shade800,
                              size: 18),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                "تبقى لديك ${_subscription!.remainingAds} إعلانات فقط. يمكنك ترقية باقتك للحصول على المزيد.",
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.amber.shade900))),
                          ])),
                    ],
                  ],
                ]))),

          const SizedBox(height: 24),

          // زر الدفع عبر ومض
          ElevatedButton(
            onPressed: _isProcessing ? null : _navigateToWamdPayment,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              disabledBackgroundColor: Colors.grey,
            ),
            child: _isProcessing
                ? const CircularProgressIndicator(color: Colors.white)
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.flash_on,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        "الدفع عبر ومض (فوري وآمن)",
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
          ),

          const SizedBox(height: 16),

          // معلومات ومض
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.verified, color: Colors.green.shade700, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        "خدمة ومض معتمدة من البنك المركزي الكويتي",
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.green.shade800,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.flash_on, color: Colors.green.shade700, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        "دفع فوري وآمن باستخدام رقم الهاتف فقط",
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.green.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملاحظة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200)),
            child: Text(
              "ملاحظة: الدفع عبر ومض فوري وآمن. سيتم تفعيل إعلانك فور إتمام الدفع.",
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.blue.shade800),
              textAlign: TextAlign.center)),


        ]));
  }

  /// بناء واجهة النجاح
  Widget _buildSuccessView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.account_balance,
            color: AppColors.primary,
            size: 80),
          const SizedBox(height: 16),
          Text(
            "تم تسجيل طلب الدفع",
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Text(
            "سيتم التحقق من التحويل البنكي خلال يوم عمل واحد",
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.grey.shade600),
            textAlign: TextAlign.center),
        ]));
  }
}
