// lib/data/datasources/estate_remote_data_source_impl.dart
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

import '../datasources/estate_remote_data_source.dart';
import '../models/estate_model.dart';

/// إعدادات التطوير
class _DevSettings {
  /// عرض جميع الإعلانات (حتى غير المدفوعة) في بيئة التطوير
  static const bool showAllEstatesInDev = true;

  /// التحقق من بيئة التطوير
  static bool get isDevelopment {
    // في بيئة التطوير، نعرض جميع الإعلانات للاختبار
    // في بيئة الإنتاج، نعرض فقط الإعلانات المدفوعة
    return true; // تغيير إلى false في الإنتاج
  }
}

class EstateRemoteDataSourceImpl implements EstateRemoteDataSource {
  final FirebaseFirestore firestore;
  final FirebaseStorage storage;

  EstateRemoteDataSourceImpl(this.firestore, this.storage);

  @override
  Future<List<EstateModel>> getAllEstates() async {
    final snapshot = await firestore.collection('estates').get();
    return snapshot.docs.map((doc) {
      final data = doc.data();
      return EstateModel.fromJson({...data, "id": doc.id});
    }).toList();
  }

  @override
  Future<Map<String, dynamic>> getPaginatedEstates({
    int limit = 10,
    String? lastDocumentId,
    int page = 1,
    int pageSize = 10,
    String sortBy = "createdAt",
    bool sortAscending = false,
    Map<String, dynamic>? filters,
    String? searchQuery,
  }) async {
    // إنشاء استعلام أساسي
    Query query = firestore.collection('estates');

    // في بيئة التطوير، عرض جميع الإعلانات للاختبار
    // في بيئة الإنتاج، عرض فقط الإعلانات المدفوعة
    if (_DevSettings.isDevelopment && _DevSettings.showAllEstatesInDev) {
      // في بيئة التطوير: عرض جميع الإعلانات (مدفوعة وغير مدفوعة)
      // لا نضيف فلتر isPaymentVerified
      debugPrint('🔧 وضع التطوير: عرض جميع الإعلانات');
    } else {
      // في بيئة الإنتاج: عرض فقط الإعلانات المدفوعة
      query = query.where('isPaymentVerified', isEqualTo: true);
      debugPrint('🚀 وضع الإنتاج: عرض الإعلانات المدفوعة فقط');
    }

    query = query
        .orderBy(sortBy, descending: !sortAscending)
        .limit(pageSize);

    // إضافة البحث النصي إذا كان متوفراً
    if (searchQuery != null && searchQuery.isNotEmpty) {
      // تحويل نص البحث إلى حروف صغيرة للبحث بغض النظر عن حالة الأحرف
      final searchLower = searchQuery.toLowerCase();

      // إضافة فلتر للبحث في العنوان والوصف والموقع
      // ملاحظة: هذا تنفيذ بسيط، للبحث المتقدم يمكن استخدام Firebase Functions أو Algolia
      query = query.where('searchableText', arrayContains: searchLower);
    }

    // إضافة الفلاتر إذا كانت متوفرة
    if (filters != null) {
      // تطبيق الفلاتر على الاستعلام
      filters.forEach((key, value) {
        if (value != null) {
          // فلاتر السعر
          if (key == 'minPrice') {
            query = query.where('price', isGreaterThanOrEqualTo: value);
          } else if (key == 'maxPrice') {
            query = query.where('price', isLessThanOrEqualTo: value);
          }
          // فلاتر التصنيف والموقع
          else if (key == 'mainCategory' ||
              key == 'subCategory' ||
              key == 'location' ||
              key == 'propertyType') {
            query = query.where(key, isEqualTo: value);
          }
          // فلاتر التجهيزات (قيم منطقية)
          else if (key == 'isFeatured' ||
              key == 'hasGarage' ||
              key == 'hasCentralAC' ||
              key == 'hasMaidRoom' ||
              key == 'hasElevator' ||
              key == 'hasSwimmingPool' ||
              key == 'hasBalcony' ||
              key == 'isFullyFurnished') {
            query = query.where(key, isEqualTo: value);
          }
          // فلتر نوع المعلن
          else if (key == 'postedByUserType') {
            query = query.where('userType', isEqualTo: value);
          }
          // فلتر الحالة
          else if (key == 'status') {
            query = query.where(key, isEqualTo: value);
          }
          // فلاتر المساحة
          else if (key == 'minArea') {
            query = query.where('area', isGreaterThanOrEqualTo: value);
          } else if (key == 'maxArea') {
            query = query.where('area', isLessThanOrEqualTo: value);
          }
          // فلاتر عدد الغرف
          else if (key == 'minRooms') {
            query = query.where('numberOfRooms', isGreaterThanOrEqualTo: value);
          } else if (key == 'maxRooms') {
            query = query.where('numberOfRooms', isLessThanOrEqualTo: value);
          }
          // فلاتر عدد الحمامات
          else if (key == 'minBathrooms') {
            query =
                query.where('numberOfBathrooms', isGreaterThanOrEqualTo: value);
          } else if (key == 'maxBathrooms') {
            query =
                query.where('numberOfBathrooms', isLessThanOrEqualTo: value);
          }
          // فلتر عمر البناء
          else if (key == 'maxBuildingAge') {
            query = query.where('buildingAge', isLessThanOrEqualTo: value);
          }
        }
      });
    }

    // إذا كان هناك معرف للمستند الأخير، نستخدمه للتحميل المتدرج
    if (lastDocumentId != null) {
      // الحصول على المستند الأخير
      DocumentSnapshot lastDocSnapshot =
          await firestore.collection('estates').doc(lastDocumentId).get();

      // استخدام startAfter للحصول على المستندات التالية
      query = query.startAfterDocument(lastDocSnapshot);
    }

    // تنفيذ الاستعلام مع معالجة الأخطاء
    try {
      final querySnapshot = await query.get();
      debugPrint('📊 تم جلب ${querySnapshot.docs.length} عقار من Firebase');

      final estates = querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return EstateModel.fromJson({...data, "id": doc.id});
      }).toList();

      // تحديد ما إذا كان هناك المزيد من العقارات
      bool hasMore = querySnapshot.docs.length >= pageSize;

      // الحصول على معرف آخر مستند (إذا كان هناك مستندات)
      String? newLastDocumentId;
      if (querySnapshot.docs.isNotEmpty) {
        newLastDocumentId = querySnapshot.docs.last.id;
      }

      debugPrint('✅ تم جلب البيانات بنجاح. hasMore: $hasMore');

      // إرجاع النتائج
      return {
        'estates': estates,
        'lastDocumentId': newLastDocumentId,
        'hasMore': hasMore,
      };
    } catch (e) {
      debugPrint('❌ خطأ في جلب العقارات: $e');
      rethrow;
    }
  }

  @override
  Future<void> createEstate(EstateModel model) async {
    debugPrint('🔥 EstateRemoteDataSourceImpl: بدء حفظ العقار في Firebase...');

    // تحويل النموذج إلى JSON
    debugPrint('📝 تحويل EstateModel إلى JSON...');
    final jsonData = model.toJson();
    debugPrint('✅ تم تحويل البيانات إلى JSON بنجاح');
    debugPrint('📊 بيانات JSON: ${jsonData.keys.toList()}');

    // إنشاء مستند جديد في مجموعة 'estates' دون تحديد id؛ سيتم توليد id تلقائيًا.
    debugPrint('💾 إضافة المستند إلى مجموعة estates...');
    final docRef = await firestore.collection('estates').add(jsonData);
    debugPrint('✅ تم إنشاء المستند بنجاح. معرف المستند: ${docRef.id}');

    // بعد الإنشاء، نقوم بتحديث حقل id ليطابق معرف المستند.
    debugPrint('🔄 تحديث حقل id في المستند...');
    await docRef.update({'id': docRef.id});
    debugPrint('✅ تم تحديث حقل id بنجاح');

    debugPrint('🎉 تم حفظ العقار في Firebase بنجاح!');
  }

  @override
  Future<void> updateEstate(EstateModel model) async {
    await firestore.collection('estates').doc(model.id).update(model.toJson());
  }

  @override
  Future<void> deleteEstate(String id) async {
    await firestore.collection('estates').doc(id).delete();
  }

  @override
  Future<List<String>> uploadImages(List<File> images) async {
    List<String> downloadUrls = [];
    for (File imageFile in images) {
      // قراءة الصورة
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image != null) {
        // ضغط الصورة وتغيير حجمها
        final resizedImage = img.copyResize(image,
            width: 800, // تحديد عرض مناسب
            interpolation: img.Interpolation.linear);

        // تحويل الصورة إلى بيانات JPEG مضغوطة
        final compressedBytes = img.encodeJpg(resizedImage, quality: 80);

        // إنشاء اسم فريد للملف
        final fileName =
            '${DateTime.now().millisecondsSinceEpoch}_${imageFile.path.split('/').last}';
        final ref = storage.ref().child('estates/$fileName');

        // رفع الصورة المضغوطة
        await ref.putData(
            compressedBytes, SettableMetadata(contentType: 'image/jpeg'));

        final downloadUrl = await ref.getDownloadURL();
        downloadUrls.add(downloadUrl);
      } else {
        // إذا فشل تحميل الصورة، نرفع الملف الأصلي
        final fileName = imageFile.path.split('/').last;
        final ref = storage.ref().child('estates/$fileName');
        await ref.putFile(imageFile);
        final downloadUrl = await ref.getDownloadURL();
        downloadUrls.add(downloadUrl);
      }
    }
    return downloadUrls;
  }

  @override
  Future<EstateModel?> getEstateById(String id) async {
    try {
      final docSnapshot = await firestore.collection('estates').doc(id).get();
      if (!docSnapshot.exists) {
        return null;
      }
      final data = docSnapshot.data() as Map<String, dynamic>;
      return EstateModel.fromJson({...data, "id": docSnapshot.id});
    } catch (e) {
      // تسجيل الخطأ (يمكن استخدام مكتبة تسجيل مناسبة في الإنتاج)
      // print('خطأ في الحصول على العقار: $e');
      return null;
    }
  }
}
