import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../core/theme/app_colors.dart';
import '../../domain/entities/virtual_tour.dart';
import '../../data/repositories_impl/virtual_tour_repository_impl.dart';
import '../widgets/virtual_tour_viewer.dart';

/// صفحة إدارة الجولات الافتراضية
class VirtualToursManagementPage extends StatefulWidget {
  final String? estateId;

  const VirtualToursManagementPage({
    super.key,
    this.estateId,
  });

  @override
  State<VirtualToursManagementPage> createState() => _VirtualToursManagementPageState();
}

class _VirtualToursManagementPageState extends State<VirtualToursManagementPage> {
  final VirtualTourRepositoryImpl _repository = VirtualTourRepositoryImpl();
  List<VirtualTour> _tours = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadTours();
  }

  Future<void> _loadTours() async {
    setState(() => _isLoading = true);
    
    try {
      List<VirtualTour> tours;
      if (widget.estateId != null) {
        tours = await _repository.getEstateVirtualTours(widget.estateId!);
      } else {
        tours = await _repository.getMostViewedVirtualTours(limit: 50);
      }
      
      setState(() {
        _tours = tours;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الجولات: $e')),
        );
      }
    }
  }

  List<VirtualTour> get _filteredTours {
    if (_searchQuery.isEmpty) return _tours;
    
    return _tours.where((tour) {
      return tour.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             (tour.description?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          widget.estateId != null ? 'جولات العقار الافتراضية' : 'إدارة الجولات الافتراضية',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTours,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),
          
          // قائمة الجولات
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredTours.isEmpty
                    ? _buildEmptyState()
                    : _buildToursList(),
          ),
        ],
      ),
      floatingActionButton: widget.estateId != null
          ? FloatingActionButton.extended(
              onPressed: () => _showAddTourDialog(),
              icon: const Icon(Icons.add),
              label: Text(
                'إضافة جولة',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            )
          : null,
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: TextField(
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: 'البحث في الجولات...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.primary),
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.threed_rotation,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty 
                ? 'لا توجد جولات تطابق البحث'
                : 'لا توجد جولات افتراضية',
            style: GoogleFonts.cairo(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.estateId != null 
                ? 'أضف جولة افتراضية لهذا العقار'
                : 'لم يتم إنشاء أي جولات افتراضية بعد',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          if (widget.estateId != null) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showAddTourDialog(),
              icon: const Icon(Icons.add),
              label: Text(
                'إضافة جولة افتراضية',
                style: GoogleFonts.cairo(),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildToursList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredTours.length,
      itemBuilder: (context, index) {
        final tour = _filteredTours[index];
        return _buildTourCard(tour);
      },
    );
  }

  Widget _buildTourCard(VirtualTour tour) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // معاينة الجولة
          VirtualTourPreview(
            tour: tour,
            onTap: () => _viewTour(tour),
          ),
          
          // معلومات الجولة
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tour.title,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (tour.description != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              tour.description!,
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: tour.isActive ? Colors.green : Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        tour.isActive ? 'نشط' : 'غير نشط',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // إحصائيات الجولة
                Row(
                  children: [
                    _buildStatItem(Icons.visibility, '${tour.viewsCount}', 'مشاهدة'),
                    const SizedBox(width: 16),
                    _buildStatItem(Icons.category, tour.getTourTypeName(), ''),
                    const Spacer(),
                    Text(
                      _formatDate(tour.createdAt),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // أزرار الإجراءات
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _viewTour(tour),
                        icon: const Icon(Icons.play_arrow),
                        label: Text(
                          'عرض',
                          style: GoogleFonts.cairo(),
                        ),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: BorderSide(color: AppColors.primary),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _editTour(tour),
                        icon: const Icon(Icons.edit),
                        label: Text(
                          'تعديل',
                          style: GoogleFonts.cairo(),
                        ),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.orange,
                          side: const BorderSide(color: Colors.orange),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => _deleteTour(tour),
                      icon: const Icon(Icons.delete),
                      color: Colors.red,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade700,
          ),
        ),
        if (label.isNotEmpty) ...[
          const SizedBox(width: 2),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _viewTour(VirtualTour tour) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text(
              tour.title,
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          body: VirtualTourViewer(
            estateId: tour.estateId,
            height: double.infinity,
          ),
        ),
      ),
    );
  }

  void _editTour(VirtualTour tour) {
    // TODO: تطبيق صفحة تعديل الجولة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطبيق تعديل الجولة قريباً')),
    );
  }

  void _deleteTour(VirtualTour tour) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'حذف الجولة الافتراضية',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من حذف الجولة "${tour.title}"؟ لا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _repository.deleteVirtualTour(tour.id);
                await _loadTours();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم حذف الجولة بنجاح')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('خطأ في حذف الجولة: $e')),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddTourDialog() {
    // TODO: تطبيق نافذة إضافة جولة جديدة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطبيق إضافة الجولة قريباً')),
    );
  }
}
