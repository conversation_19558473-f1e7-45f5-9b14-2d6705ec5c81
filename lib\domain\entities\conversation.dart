import 'package:cloud_firestore/cloud_firestore.dart';
import 'message.dart';

/// نوع المحادثة
enum ConversationType {
  /// محادثة فردية
  individual,

  /// محادثة جماعية
  group,

  /// محادثة مع وكيل عقاري
  agent,

  /// محادثة مع مالك عقار
  owner,

  /// محادثة مع شركة عقارية
  company,

  /// محادثة دعم فني
  support,

  /// محادثة خاصة بين مستخدمين
  private,

  /// محادثة متعلقة بعقار
  estate,

  /// محادثة متعلقة بطلب عقار
  propertyRequest,

  /// محادثة متعلقة بعرض على طلب عقار
  propertyOffer,
}

/// حالة المحادثة
enum ConversationStatus {
  /// نشطة
  active,

  /// مؤرشفة
  archived,

  /// محظورة
  blocked,

  /// محذوفة
  deleted,
}

/// نموذج المحادثة
class Conversation {
  /// معرف المحادثة
  final String id;

  /// عنوان المحادثة
  final String title;

  /// صورة المحادثة
  final String? image;

  /// نوع المحادثة
  final ConversationType type;

  /// حالة المحادثة
  final ConversationStatus status;

  /// معرفات المشاركين
  final List<String> participantIds;

  /// أسماء المشاركين
  final List<String> participantNames;

  /// صور المشاركين
  final List<String?> participantImages;

  /// معرف آخر رسالة
  final String? lastMessageId;

  /// محتوى آخر رسالة
  final String? lastMessageContent;

  /// نوع آخر رسالة
  final MessageType? lastMessageType;

  /// معرف مرسل آخر رسالة
  final String? lastMessageSenderId;

  /// اسم مرسل آخر رسالة
  final String? lastMessageSenderName;

  /// وقت آخر رسالة
  final DateTime? lastMessageTimestamp;

  /// ما إذا كانت آخر رسالة مقروءة
  final bool? lastMessageIsRead;

  /// عدد الرسائل غير المقروءة
  final Map<String, int>? unreadCount;

  /// وقت إنشاء المحادثة
  final DateTime createdAt;

  /// معرف منشئ المحادثة
  final String createdBy;

  /// وقت آخر تحديث للمحادثة
  final DateTime updatedAt;

  /// ما إذا كانت المحادثة مثبتة
  final bool isPinned;

  /// ما إذا كانت المحادثة مكتومة
  final bool isMuted;

  /// إعدادات الإشعارات
  final Map<String, bool>? notificationSettings;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  /// معرف العقار المرتبط بالمحادثة
  final String? estateId;

  /// عنوان العقار المرتبط بالمحادثة
  final String? estateTitle;

  /// صورة العقار المرتبط بالمحادثة
  final String? estateImage;

  /// معرف العرض المرتبط بالمحادثة
  final String? offerId;

  /// معرف طلب العقار المرتبط بالمحادثة
  final String? propertyRequestId;

  /// عنوان طلب العقار المرتبط بالمحادثة
  final String? propertyRequestTitle;

  /// معرف عرض طلب العقار المرتبط بالمحادثة
  final String? propertyOfferId;

  /// المشاركين الذين يكتبون حالياً
  final List<String>? typingParticipants;

  Conversation({
    required this.id,
    required this.title,
    this.image,
    required this.type,
    required this.status,
    required this.participantIds,
    required this.participantNames,
    required this.participantImages,
    this.lastMessageId,
    this.lastMessageContent,
    this.lastMessageType,
    this.lastMessageSenderId,
    this.lastMessageSenderName,
    this.lastMessageTimestamp,
    this.lastMessageIsRead,
    this.unreadCount,
    required this.createdAt,
    required this.createdBy,
    required this.updatedAt,
    this.isPinned = false,
    this.isMuted = false,
    this.notificationSettings,
    this.metadata,
    this.estateId,
    this.estateTitle,
    this.estateImage,
    this.offerId,
    this.propertyRequestId,
    this.propertyRequestTitle,
    this.propertyOfferId,
    this.typingParticipants,
  });

  /// إنشاء نسخة معدلة من المحادثة
  Conversation copyWith({
    String? id,
    String? title,
    String? image,
    ConversationType? type,
    ConversationStatus? status,
    List<String>? participantIds,
    List<String>? participantNames,
    List<String?>? participantImages,
    String? lastMessageId,
    String? lastMessageContent,
    MessageType? lastMessageType,
    String? lastMessageSenderId,
    String? lastMessageSenderName,
    DateTime? lastMessageTimestamp,
    bool? lastMessageIsRead,
    Map<String, int>? unreadCount,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    bool? isPinned,
    bool? isMuted,
    Map<String, bool>? notificationSettings,
    Map<String, dynamic>? metadata,
    String? estateId,
    String? estateTitle,
    String? estateImage,
    String? offerId,
    String? propertyRequestId,
    String? propertyRequestTitle,
    String? propertyOfferId,
    List<String>? typingParticipants,
  }) {
    return Conversation(
      id: id ?? this.id,
      title: title ?? this.title,
      image: image ?? this.image,
      type: type ?? this.type,
      status: status ?? this.status,
      participantIds: participantIds ?? this.participantIds,
      participantNames: participantNames ?? this.participantNames,
      participantImages: participantImages ?? this.participantImages,
      lastMessageId: lastMessageId ?? this.lastMessageId,
      lastMessageContent: lastMessageContent ?? this.lastMessageContent,
      lastMessageType: lastMessageType ?? this.lastMessageType,
      lastMessageSenderId: lastMessageSenderId ?? this.lastMessageSenderId,
      lastMessageSenderName:
          lastMessageSenderName ?? this.lastMessageSenderName,
      lastMessageTimestamp: lastMessageTimestamp ?? this.lastMessageTimestamp,
      lastMessageIsRead: lastMessageIsRead ?? this.lastMessageIsRead,
      unreadCount: unreadCount ?? this.unreadCount,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      isPinned: isPinned ?? this.isPinned,
      isMuted: isMuted ?? this.isMuted,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      metadata: metadata ?? this.metadata,
      estateId: estateId ?? this.estateId,
      estateTitle: estateTitle ?? this.estateTitle,
      estateImage: estateImage ?? this.estateImage,
      offerId: offerId ?? this.offerId,
      propertyRequestId: propertyRequestId ?? this.propertyRequestId,
      propertyRequestTitle: propertyRequestTitle ?? this.propertyRequestTitle,
      propertyOfferId: propertyOfferId ?? this.propertyOfferId,
      typingParticipants: typingParticipants ?? this.typingParticipants);
  }

  /// تحويل المحادثة إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'image': image,
      'type': type.index,
      'status': status.index,
      'participantIds': participantIds,
      'participantNames': participantNames,
      'participantImages': participantImages,
      'lastMessageId': lastMessageId,
      'lastMessageContent': lastMessageContent,
      'lastMessageType': lastMessageType?.index,
      'lastMessageSenderId': lastMessageSenderId,
      'lastMessageSenderName': lastMessageSenderName,
      'lastMessageTimestamp': lastMessageTimestamp?.millisecondsSinceEpoch,
      'lastMessageIsRead': lastMessageIsRead,
      'unreadCount': unreadCount,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'createdBy': createdBy,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'isPinned': isPinned,
      'isMuted': isMuted,
      'notificationSettings': notificationSettings,
      'metadata': metadata,
      'estateId': estateId,
      'estateTitle': estateTitle,
      'estateImage': estateImage,
      'offerId': offerId,
      'propertyRequestId': propertyRequestId,
      'propertyRequestTitle': propertyRequestTitle,
      'propertyOfferId': propertyOfferId,
      'typingParticipants': typingParticipants,
    };
  }

  /// إنشاء محادثة من خريطة
  factory Conversation.fromMap(Map<String, dynamic> map) {
    return Conversation(
      id: map['id'],
      title: map['title'],
      image: map['image'],
      type: ConversationType.values[map['type']],
      status: ConversationStatus.values[map['status']],
      participantIds: List<String>.from(map['participantIds']),
      participantNames: List<String>.from(map['participantNames']),
      participantImages: List<String?>.from(map['participantImages']),
      lastMessageId: map['lastMessageId'],
      lastMessageContent: map['lastMessageContent'],
      lastMessageType: map['lastMessageType'] != null
          ? MessageType.values[map['lastMessageType']]
          : null,
      lastMessageSenderId: map['lastMessageSenderId'],
      lastMessageSenderName: map['lastMessageSenderName'],
      lastMessageTimestamp: map['lastMessageTimestamp'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastMessageTimestamp'])
          : null,
      lastMessageIsRead: map['lastMessageIsRead'],
      unreadCount: map['unreadCount'] != null
          ? Map<String, int>.from(map['unreadCount'])
          : null,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      createdBy: map['createdBy'],
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      isPinned: map['isPinned'] ?? false,
      isMuted: map['isMuted'] ?? false,
      notificationSettings: map['notificationSettings'] != null
          ? Map<String, bool>.from(map['notificationSettings'])
          : null,
      metadata: map['metadata'],
      estateId: map['estateId'],
      estateTitle: map['estateTitle'],
      estateImage: map['estateImage'],
      offerId: map['offerId'],
      propertyRequestId: map['propertyRequestId'],
      propertyRequestTitle: map['propertyRequestTitle'],
      propertyOfferId: map['propertyOfferId'],
      typingParticipants: map['typingParticipants'] != null
          ? List<String>.from(map['typingParticipants'])
          : null);
  }

  /// إنشاء محادثة من وثيقة Firestore
  factory Conversation.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Conversation.fromMap({
      'id': doc.id,
      ...data,
    });
  }

  /// تحويل المحادثة إلى JSON
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء محادثة من JSON
  factory Conversation.fromJson(Map<String, dynamic> json) =>
      Conversation.fromMap(json);

  /// تحديث المحادثة بآخر رسالة
  Conversation updateWithLastMessage(Message message) {
    final newUnreadCount = Map<String, int>.from(unreadCount ?? {});

    // زيادة عدد الرسائل غير المقروءة لجميع المشاركين ما عدا المرسل
    for (final participantId in participantIds) {
      if (participantId != message.senderId) {
        newUnreadCount[participantId] =
            (newUnreadCount[participantId] ?? 0) + 1;
      }
    }

    return copyWith(
      lastMessageId: message.id,
      lastMessageContent: message.content,
      lastMessageType: message.type,
      lastMessageSenderId: message.senderId,
      lastMessageSenderName: message.senderName,
      lastMessageTimestamp: message.timestamp,
      lastMessageIsRead: false,
      unreadCount: newUnreadCount,
      updatedAt: message.timestamp);
  }

  /// تعيين الرسائل كمقروءة لمستخدم معين
  Conversation markAsRead(String userId) {
    final newUnreadCount = Map<String, int>.from(unreadCount ?? {});
    newUnreadCount[userId] = 0;

    return copyWith(
      unreadCount: newUnreadCount,
      lastMessageIsRead: true);
  }

  /// الحصول على عدد الرسائل غير المقروءة لمستخدم معين
  int getUnreadCountForUser(String userId) {
    return unreadCount?[userId] ?? 0;
  }

  /// التحقق مما إذا كان المستخدم مشارك في المحادثة
  bool isParticipant(String userId) {
    return participantIds.contains(userId);
  }

  /// الحصول على معرفات المشاركين الآخرين
  List<String> getOtherParticipantIds(String userId) {
    return participantIds.where((id) => id != userId).toList();
  }

  /// الحصول على عنوان المحادثة لمستخدم معين
  String getTitleForUser(String userId) {
    if (type == ConversationType.individual) {
      final otherParticipantIndex =
          participantIds.indexWhere((id) => id != userId);
      if (otherParticipantIndex != -1) {
        return participantNames[otherParticipantIndex];
      }
    }
    return title;
  }

  /// الحصول على صورة المحادثة لمستخدم معين
  String? getImageForUser(String userId) {
    if (type == ConversationType.individual) {
      final otherParticipantIndex =
          participantIds.indexWhere((id) => id != userId);
      if (otherParticipantIndex != -1) {
        return participantImages[otherParticipantIndex];
      }
    }
    return image;
  }

  /// إضافة مشارك إلى المحادثة
  Conversation addParticipant(
      String userId, String userName, String? userImage) {
    if (participantIds.contains(userId)) {
      return this;
    }

    final newParticipantIds = List<String>.from(participantIds)..add(userId);
    final newParticipantNames = List<String>.from(participantNames)
      ..add(userName);
    final newParticipantImages = List<String?>.from(participantImages)
      ..add(userImage);
    final newUnreadCount = Map<String, int>.from(unreadCount ?? {})
      ..putIfAbsent(userId, () => 0);

    return copyWith(
      participantIds: newParticipantIds,
      participantNames: newParticipantNames,
      participantImages: newParticipantImages,
      unreadCount: newUnreadCount,
      updatedAt: DateTime.now());
  }

  /// إزالة مشارك من المحادثة
  Conversation removeParticipant(String userId) {
    final index = participantIds.indexOf(userId);
    if (index == -1) {
      return this;
    }

    final newParticipantIds = List<String>.from(participantIds)
      ..removeAt(index);
    final newParticipantNames = List<String>.from(participantNames)
      ..removeAt(index);
    final newParticipantImages = List<String?>.from(participantImages)
      ..removeAt(index);
    final newUnreadCount = Map<String, int>.from(unreadCount ?? {})
      ..remove(userId);

    return copyWith(
      participantIds: newParticipantIds,
      participantNames: newParticipantNames,
      participantImages: newParticipantImages,
      unreadCount: newUnreadCount,
      updatedAt: DateTime.now());
  }

  /// تحديث معلومات مشارك في المحادثة
  Conversation updateParticipant(
      String userId, String? userName, String? userImage) {
    final index = participantIds.indexOf(userId);
    if (index == -1) {
      return this;
    }

    final newParticipantNames = List<String>.from(participantNames);
    final newParticipantImages = List<String?>.from(participantImages);

    if (userName != null) {
      newParticipantNames[index] = userName;
    }

    if (userImage != null) {
      newParticipantImages[index] = userImage;
    }

    return copyWith(
      participantNames: newParticipantNames,
      participantImages: newParticipantImages,
      updatedAt: DateTime.now());
  }

  /// إضافة مشارك يكتب حالياً
  Conversation addTypingParticipant(String userId) {
    if (typingParticipants?.contains(userId) ?? false) {
      return this;
    }

    final newTypingParticipants = List<String>.from(typingParticipants ?? [])
      ..add(userId);

    return copyWith(
      typingParticipants: newTypingParticipants);
  }

  /// إزالة مشارك يكتب حالياً
  Conversation removeTypingParticipant(String userId) {
    if (!(typingParticipants?.contains(userId) ?? false)) {
      return this;
    }

    final newTypingParticipants = List<String>.from(typingParticipants ?? [])
      ..remove(userId);

    return copyWith(
      typingParticipants: newTypingParticipants);
  }
}
