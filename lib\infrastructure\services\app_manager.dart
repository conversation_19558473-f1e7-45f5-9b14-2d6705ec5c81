import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'cache_manager.dart';
import 'data_cache_service.dart';
import 'image_service.dart';
import 'multi_factor_auth_service.dart';
import 'performance_service.dart';


/// مدير التطبيق
class AppManager {
  static final AppManager _instance = AppManager._internal();

  factory AppManager() {
    return _instance;
  }

  AppManager._internal();

  late AppCacheManager _cacheManager;
  late DataCacheService _dataCacheService;
  late ImageService _imageService;
  late MultiFactorAuthService _multiFactorAuthService;
  late PerformanceService _performanceService;


  late SharedPreferences _prefs;

  bool _isInitialized = false;
  bool _isOfflineMode = false;

  /// تهيئة مدير التطبيق
  Future<void> init() async {
    if (_isInitialized) {
      return;
    }

    // تهيئة Firebase
    await Firebase.initializeApp();

    // تهيئة SharedPreferences
    _prefs = await SharedPreferences.getInstance();

    // تهيئة الخدمات
    _cacheManager = AppCacheManager();
    await _cacheManager.init();

    _dataCacheService = DataCacheService();
    await _dataCacheService.init();

    _imageService = ImageService();

    _multiFactorAuthService = MultiFactorAuthService();
    await _multiFactorAuthService.init();

    _performanceService = PerformanceService();
    await _performanceService.init();



    // التحقق من حالة الاتصال
    await _checkConnectivity();

    // تحميل إعدادات التطبيق
    _loadAppSettings();

    // بدء مراقبة الأداء
    _performanceService.startPerformanceMonitoring();

    _isInitialized = true;
  }

  /// التحقق من حالة الاتصال
  Future<void> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    _isOfflineMode = connectivityResult == ConnectivityResult.none;
  }

  /// تحميل إعدادات التطبيق
  void _loadAppSettings() {
    // تحميل إعدادات التطبيق من SharedPreferences
  }

  /// الحصول على مدير التخزين المؤقت
  AppCacheManager get cacheManager => _cacheManager;

  /// الحصول على خدمة التخزين المؤقت للبيانات
  DataCacheService get dataCacheService => _dataCacheService;

  /// الحصول على خدمة الصور
  ImageService get imageService => _imageService;

  /// الحصول على خدمة المصادقة متعددة العوامل
  MultiFactorAuthService get multiFactorAuthService => _multiFactorAuthService;

  /// الحصول على خدمة الأداء
  PerformanceService get performanceService => _performanceService;



  /// التحقق مما إذا كان التطبيق في وضع عدم الاتصال
  bool get isOfflineMode => _isOfflineMode;

  /// تعيين وضع عدم الاتصال
  set isOfflineMode(bool value) {
    _isOfflineMode = value;
    _prefs.setBool('offline_mode', value);
  }

  /// التحقق مما إذا كان المستخدم مسجل الدخول
  bool isUserLoggedIn() {
    return FirebaseAuth.instance.currentUser != null;
  }

  /// الحصول على المستخدم الحالي
  User? getCurrentUser() {
    return FirebaseAuth.instance.currentUser;
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    await FirebaseAuth.instance.signOut();
  }

  /// تحرير الموارد
  void dispose() {
    _performanceService.dispose();
  }

  /// تحسين أداء التطبيق
  Future<void> optimizePerformance() async {
    await _performanceService.optimizePerformance();
    await _dataCacheService.optimizeCache();
  }

  /// تنظيف التخزين المؤقت
  Future<void> clearCache() async {
    await _cacheManager.clearCache();
  }

  /// الحصول على حجم التخزين المؤقت
  Future<String> getCacheSize() async {
    return await _cacheManager.getReadableCacheSize();
  }
}

/// مزود مدير التطبيق
class AppManagerProvider extends InheritedWidget {
  final AppManager appManager;

  const AppManagerProvider({
    super.key,
    required this.appManager,
    required super.child,
  });

  static AppManagerProvider of(BuildContext context) {
    final AppManagerProvider? result =
        context.dependOnInheritedWidgetOfExactType<AppManagerProvider>();
    assert(result != null, 'No AppManagerProvider found in context');
    return result!;
  }

  @override
  bool updateShouldNotify(AppManagerProvider oldWidget) {
    return appManager != oldWidget.appManager;
  }
}

/// امتدادات لتسهيل استخدام مدير التطبيق
extension AppManagerExtensions on BuildContext {
  /// الحصول على مدير التطبيق
  AppManager get appManager => AppManagerProvider.of(this).appManager;

  /// الحصول على مدير التخزين المؤقت
  AppCacheManager get cacheManager => appManager.cacheManager;

  /// الحصول على خدمة التخزين المؤقت للبيانات
  DataCacheService get dataCacheService => appManager.dataCacheService;

  /// الحصول على خدمة الصور
  ImageService get imageService => appManager.imageService;

  /// الحصول على خدمة المصادقة متعددة العوامل
  MultiFactorAuthService get multiFactorAuthService =>
      appManager.multiFactorAuthService;

  /// الحصول على خدمة الأداء
  PerformanceService get performanceService => appManager.performanceService;


}
