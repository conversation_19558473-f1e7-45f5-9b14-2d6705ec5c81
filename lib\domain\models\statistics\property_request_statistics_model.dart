// lib/domain/models/statistics/property_request_statistics_model.dart
import 'package:equatable/equatable.dart';

/// نموذج إحصائيات طلبات العقارات
class PropertyRequestStatisticsModel extends Equatable {
  /// إجمالي عدد الطلبات
  final int totalRequests;

  /// عدد الطلبات النشطة
  final int activeRequests;

  /// عدد الطلبات التي تم حلها
  final int resolvedRequests;

  /// عدد الطلبات المغلقة
  final int closedRequests;

  /// إجمالي عدد العروض المقدمة
  final int totalOffers;

  /// عدد العروض المقبولة
  final int acceptedOffers;

  /// عدد العروض المرفوضة
  final int rejectedOffers;

  /// عدد العروض المعلقة
  final int pendingOffers;

  /// إجمالي عدد العروض المستلمة
  final int totalOffersReceived;

  /// عدد العروض المقبولة المستلمة
  final int acceptedOffersReceived;

  /// عدد العروض المرفوضة المستلمة
  final int rejectedOffersReceived;

  /// عدد العروض المعلقة المستلمة
  final int pendingOffersReceived;

  /// متوسط وقت الاستجابة (بالساعات)
  final double averageResponseTime;

  /// متوسط عدد العروض لكل طلب
  final double averageOffersPerRequest;

  /// إنشاء نموذج إحصائيات طلبات العقارات
  const PropertyRequestStatisticsModel({
    this.totalRequests = 0,
    this.activeRequests = 0,
    this.resolvedRequests = 0,
    this.closedRequests = 0,
    this.totalOffers = 0,
    this.acceptedOffers = 0,
    this.rejectedOffers = 0,
    this.pendingOffers = 0,
    this.totalOffersReceived = 0,
    this.acceptedOffersReceived = 0,
    this.rejectedOffersReceived = 0,
    this.pendingOffersReceived = 0,
    this.averageResponseTime = 0,
    this.averageOffersPerRequest = 0,
  });

  /// نسبة الطلبات النشطة
  double get activeRequestsPercentage =>
      totalRequests > 0 ? activeRequests / totalRequests : 0;

  /// نسبة الطلبات التي تم حلها
  double get resolvedRequestsPercentage =>
      totalRequests > 0 ? resolvedRequests / totalRequests : 0;

  /// نسبة الطلبات المغلقة
  double get closedRequestsPercentage =>
      totalRequests > 0 ? closedRequests / totalRequests : 0;

  /// نسبة العروض المقبولة
  double get acceptedOffersPercentage =>
      totalOffers > 0 ? acceptedOffers / totalOffers : 0;

  /// نسبة العروض المرفوضة
  double get rejectedOffersPercentage =>
      totalOffers > 0 ? rejectedOffers / totalOffers : 0;

  /// نسبة العروض المعلقة
  double get pendingOffersPercentage =>
      totalOffers > 0 ? pendingOffers / totalOffers : 0;

  /// نسبة العروض المقبولة المستلمة
  double get acceptedOffersReceivedPercentage =>
      totalOffersReceived > 0 ? acceptedOffersReceived / totalOffersReceived : 0;

  /// نسبة العروض المرفوضة المستلمة
  double get rejectedOffersReceivedPercentage =>
      totalOffersReceived > 0 ? rejectedOffersReceived / totalOffersReceived : 0;

  /// نسبة العروض المعلقة المستلمة
  double get pendingOffersReceivedPercentage =>
      totalOffersReceived > 0 ? pendingOffersReceived / totalOffersReceived : 0;

  @override
  List<Object?> get props => [
        totalRequests,
        activeRequests,
        resolvedRequests,
        closedRequests,
        totalOffers,
        acceptedOffers,
        rejectedOffers,
        pendingOffers,
        totalOffersReceived,
        acceptedOffersReceived,
        rejectedOffersReceived,
        pendingOffersReceived,
        averageResponseTime,
        averageOffersPerRequest,
      ];

  /// نسخ النموذج مع تعديل بعض الخصائص
  PropertyRequestStatisticsModel copyWith({
    int? totalRequests,
    int? activeRequests,
    int? resolvedRequests,
    int? closedRequests,
    int? totalOffers,
    int? acceptedOffers,
    int? rejectedOffers,
    int? pendingOffers,
    int? totalOffersReceived,
    int? acceptedOffersReceived,
    int? rejectedOffersReceived,
    int? pendingOffersReceived,
    double? averageResponseTime,
    double? averageOffersPerRequest,
  }) {
    return PropertyRequestStatisticsModel(
      totalRequests: totalRequests ?? this.totalRequests,
      activeRequests: activeRequests ?? this.activeRequests,
      resolvedRequests: resolvedRequests ?? this.resolvedRequests,
      closedRequests: closedRequests ?? this.closedRequests,
      totalOffers: totalOffers ?? this.totalOffers,
      acceptedOffers: acceptedOffers ?? this.acceptedOffers,
      rejectedOffers: rejectedOffers ?? this.rejectedOffers,
      pendingOffers: pendingOffers ?? this.pendingOffers,
      totalOffersReceived: totalOffersReceived ?? this.totalOffersReceived,
      acceptedOffersReceived: acceptedOffersReceived ?? this.acceptedOffersReceived,
      rejectedOffersReceived: rejectedOffersReceived ?? this.rejectedOffersReceived,
      pendingOffersReceived: pendingOffersReceived ?? this.pendingOffersReceived,
      averageResponseTime: averageResponseTime ?? this.averageResponseTime,
      averageOffersPerRequest: averageOffersPerRequest ?? this.averageOffersPerRequest);
  }
}
