import 'package:equatable/equatable.dart';

/// كيان معلومات النقل
class TransportationInfo extends Equatable {
  final List<TransportationStation> stations;
  final List<TransportationLine> lines;
  final Map<String, dynamic> walkabilityScores;
  final Map<String, dynamic> trafficInfo;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان معلومات النقل
  const TransportationInfo({
    required this.stations,
    required this.lines,
    required this.walkabilityScores,
    required this.trafficInfo,
    this.additionalInfo,
  });

  /// إنشاء كيان معلومات النقل من JSON
  factory TransportationInfo.fromJson(Map<String, dynamic> json) {
    return TransportationInfo(
      stations: (json['stations'] as List)
          .map((station) => TransportationStation.fromJson(station))
          .toList(),
      lines: (json['lines'] as List)
          .map((line) => TransportationLine.fromJson(line))
          .toList(),
      walkabilityScores: json['walkabilityScores'] as Map<String, dynamic>,
      trafficInfo: json['trafficInfo'] as Map<String, dynamic>,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان معلومات النقل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'stations': stations.map((station) => station.toJson()).toList(),
      'lines': lines.map((line) => line.toJson()).toList(),
      'walkabilityScores': walkabilityScores,
      'trafficInfo': trafficInfo,
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان معلومات النقل مع تعديل بعض الخصائص
  TransportationInfo copyWith({
    List<TransportationStation>? stations,
    List<TransportationLine>? lines,
    Map<String, dynamic>? walkabilityScores,
    Map<String, dynamic>? trafficInfo,
    Map<String, dynamic>? additionalInfo,
  }) {
    return TransportationInfo(
      stations: stations ?? this.stations,
      lines: lines ?? this.lines,
      walkabilityScores: walkabilityScores ?? this.walkabilityScores,
      trafficInfo: trafficInfo ?? this.trafficInfo,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// الحصول على محطات النقل حسب النوع
  List<TransportationStation> getStationsByType(String type) {
    return stations.where((station) => station.type == type).toList();
  }

  /// الحصول على خطوط النقل حسب النوع
  List<TransportationLine> getLinesByType(String type) {
    return lines.where((line) => line.type == type).toList();
  }

  /// الحصول على درجة قابلية المشي
  int getWalkabilityScore() {
    return walkabilityScores['overall'] as int? ?? 0;
  }

  /// الحصول على درجة الازدحام المروري
  int getTrafficCongestionScore() {
    return trafficInfo['congestionScore'] as int? ?? 0;
  }

  @override
  List<Object?> get props => [
        stations,
        lines,
        walkabilityScores,
        trafficInfo,
        additionalInfo,
      ];
}

/// كيان محطة النقل
class TransportationStation extends Equatable {
  final String id;
  final String name;
  final String type;
  final double latitude;
  final double longitude;
  final double distance;
  final List<String> lineIds;
  final Map<String, dynamic>? operatingHours;
  final Map<String, dynamic>? facilities;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان محطة النقل
  const TransportationStation({
    required this.id,
    required this.name,
    required this.type,
    required this.latitude,
    required this.longitude,
    required this.distance,
    required this.lineIds,
    this.operatingHours,
    this.facilities,
    this.additionalInfo,
  });

  /// إنشاء كيان محطة النقل من JSON
  factory TransportationStation.fromJson(Map<String, dynamic> json) {
    return TransportationStation(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      latitude: json['latitude'] as double,
      longitude: json['longitude'] as double,
      distance: json['distance'] as double,
      lineIds: List<String>.from(json['lineIds'] as List),
      operatingHours: json['operatingHours'] as Map<String, dynamic>?,
      facilities: json['facilities'] as Map<String, dynamic>?,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان محطة النقل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'latitude': latitude,
      'longitude': longitude,
      'distance': distance,
      'lineIds': lineIds,
      'operatingHours': operatingHours,
      'facilities': facilities,
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان محطة النقل مع تعديل بعض الخصائص
  TransportationStation copyWith({
    String? id,
    String? name,
    String? type,
    double? latitude,
    double? longitude,
    double? distance,
    List<String>? lineIds,
    Map<String, dynamic>? operatingHours,
    Map<String, dynamic>? facilities,
    Map<String, dynamic>? additionalInfo,
  }) {
    return TransportationStation(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      distance: distance ?? this.distance,
      lineIds: lineIds ?? this.lineIds,
      operatingHours: operatingHours ?? this.operatingHours,
      facilities: facilities ?? this.facilities,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// الحصول على وصف نوع المحطة
  String getTypeDescription() {
    switch (type) {
      case 'bus':
        return 'محطة حافلات';
      case 'metro':
        return 'محطة مترو';
      case 'train':
        return 'محطة قطار';
      case 'taxi':
        return 'موقف سيارات أجرة';
      case 'bike':
        return 'محطة دراجات';
      default:
        return 'محطة نقل';
    }
  }

  /// التحقق مما إذا كانت المحطة تعمل حاليًا
  bool isCurrentlyOperating() {
    if (operatingHours == null) {
      return true;
    }

    final now = DateTime.now();
    final dayOfWeek = now.weekday.toString();
    final currentHours = operatingHours![dayOfWeek] as Map<String, dynamic>?;

    if (currentHours == null) {
      return false;
    }

    final openTime = currentHours['open'] as String;
    final closeTime = currentHours['close'] as String;

    final openHour = int.parse(openTime.split(':')[0]);
    final openMinute = int.parse(openTime.split(':')[1]);
    final closeHour = int.parse(closeTime.split(':')[0]);
    final closeMinute = int.parse(closeTime.split(':')[1]);

    final openDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      openHour,
      openMinute);
    final closeDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      closeHour,
      closeMinute);

    return now.isAfter(openDateTime) && now.isBefore(closeDateTime);
  }

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        latitude,
        longitude,
        distance,
        lineIds,
        operatingHours,
        facilities,
        additionalInfo,
      ];
}

/// كيان خط النقل
class TransportationLine extends Equatable {
  final String id;
  final String name;
  final String type;
  final String operator;
  final List<String> stationIds;
  final String color;
  final Map<String, dynamic>? schedule;
  final Map<String, dynamic>? fare;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان خط النقل
  const TransportationLine({
    required this.id,
    required this.name,
    required this.type,
    required this.operator,
    required this.stationIds,
    required this.color,
    this.schedule,
    this.fare,
    this.additionalInfo,
  });

  /// إنشاء كيان خط النقل من JSON
  factory TransportationLine.fromJson(Map<String, dynamic> json) {
    return TransportationLine(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      operator: json['operator'] as String,
      stationIds: List<String>.from(json['stationIds'] as List),
      color: json['color'] as String,
      schedule: json['schedule'] as Map<String, dynamic>?,
      fare: json['fare'] as Map<String, dynamic>?,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان خط النقل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'operator': operator,
      'stationIds': stationIds,
      'color': color,
      'schedule': schedule,
      'fare': fare,
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان خط النقل مع تعديل بعض الخصائص
  TransportationLine copyWith({
    String? id,
    String? name,
    String? type,
    String? operator,
    List<String>? stationIds,
    String? color,
    Map<String, dynamic>? schedule,
    Map<String, dynamic>? fare,
    Map<String, dynamic>? additionalInfo,
  }) {
    return TransportationLine(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      operator: operator ?? this.operator,
      stationIds: stationIds ?? this.stationIds,
      color: color ?? this.color,
      schedule: schedule ?? this.schedule,
      fare: fare ?? this.fare,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// الحصول على وصف نوع الخط
  String getTypeDescription() {
    switch (type) {
      case 'bus':
        return 'خط حافلات';
      case 'metro':
        return 'خط مترو';
      case 'train':
        return 'خط قطار';
      case 'taxi':
        return 'خط سيارات أجرة';
      case 'bike':
        return 'مسار دراجات';
      default:
        return 'خط نقل';
    }
  }

  /// الحصول على أجرة الخط
  double getFareAmount() {
    if (fare == null) {
      return 0.0;
    }

    return fare!['amount'] as double? ?? 0.0;
  }

  /// الحصول على عدد المحطات
  int getStationsCount() {
    return stationIds.length;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        operator,
        stationIds,
        color,
        schedule,
        fare,
        additionalInfo,
      ];
}
