import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kuwait_corners/core/services/user_verification_service.dart';
import 'package:kuwait_corners/presentation/widgets/enhanced_progress_indicator.dart';
import 'package:kuwait_corners/domain/entities/user.dart' as domain_user;

/// صفحة التحقق من المستخدم
/// تتيح للمستخدم تقديم طلب للتحقق من هويته
class UserVerificationPage extends StatefulWidget {
  const UserVerificationPage({super.key});

  @override
  State<UserVerificationPage> createState() => _UserVerificationPageState();
}

class _UserVerificationPageState extends State<UserVerificationPage> {
  final UserVerificationService _verificationService =
      UserVerificationService();
  final _formKey = GlobalKey<FormState>();

  // حالة الصفحة
  bool _isLoading = false;
  String? _errorMessage;
  bool _isSuccess = false;

  // بيانات النموذج
  domain_user.UserType _selectedUserType = domain_user.UserType.seeker;
  final List<File> _documents = [];
  final Map<String, dynamic> _additionalInfo = {};

  // وحدات التحكم في النص
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _licenseController = TextEditingController();
  final _companyController = TextEditingController();
  final _addressController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _checkVerificationStatus();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _licenseController.dispose();
    _companyController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  /// التحقق من حالة التحقق الحالية للمستخدم
  Future<void> _checkVerificationStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final status =
          await _verificationService.getCurrentUserVerificationStatus();

      if (status == VerificationStatus.verified) {
        // المستخدم محقق بالفعل، الانتقال إلى الصفحة الرئيسية
        if (mounted) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('حسابك محقق بالفعل')));
        }
      } else if (status == VerificationStatus.pending) {
        // المستخدم لديه طلب قيد المراجعة
        if (mounted) {
          setState(() {
            _isSuccess = true;
          });
        }
      }

      // الحصول على نوع المستخدم الحالي
      final userType = await _verificationService.getCurrentUserType();
      if (mounted) {
        setState(() {
          _selectedUserType = userType;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء التحقق من حالة التحقق';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _documents.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء اختيار الصورة';
      });
    }
  }

  /// التقاط صورة باستخدام الكاميرا
  Future<void> _takePhoto() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.camera);

      if (pickedFile != null) {
        setState(() {
          _documents.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء التقاط الصورة';
      });
    }
  }

  /// إزالة صورة من القائمة
  void _removeImage(int index) {
    setState(() {
      _documents.removeAt(index);
    });
  }

  /// تقديم طلب التحقق
  Future<void> _submitVerificationRequest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_documents.isEmpty) {
      setState(() {
        _errorMessage = 'يرجى إرفاق صورة هوية أو رخصة على الأقل';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // جمع المعلومات الإضافية
      _additionalInfo['name'] = _nameController.text;
      _additionalInfo['phone'] = _phoneController.text;

      if (_selectedUserType == domain_user.UserType.agent ||
          _selectedUserType == domain_user.UserType.company) {
        _additionalInfo['licenseNumber'] = _licenseController.text;
      }

      if (_selectedUserType == domain_user.UserType.company) {
        _additionalInfo['companyName'] = _companyController.text;
      }

      _additionalInfo['address'] = _addressController.text;

      // تقديم الطلب
      final requestId = await _verificationService.submitVerificationRequest(
        userType: _selectedUserType,
        documents: _documents,
        additionalInfo: _additionalInfo);

      if (requestId != null) {
        setState(() {
          _isSuccess = true;
        });
      } else {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء تقديم الطلب. يرجى المحاولة مرة أخرى.';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// إلغاء طلب التحقق الحالي
  Future<void> _cancelVerificationRequest() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _verificationService.cancelVerificationRequest();

      if (success) {
        setState(() {
          _isSuccess = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إلغاء طلب التحقق بنجاح')));
        }
      } else {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء إلغاء الطلب';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التحقق من الحساب')),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _isSuccess
              ? _buildSuccessView()
              : _buildVerificationForm());
  }

  /// بناء نموذج التحقق
  Widget _buildVerificationForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مؤشر التقدم
            EnhancedProgressIndicator(
              currentStep: 1,
              totalSteps: 2,
              stepTitles: const ['المعلومات', 'المراجعة']),

            const SizedBox(height: 24),

            // عنوان الصفحة
            const Text(
              'التحقق من الحساب',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold)),

            const SizedBox(height: 8),

            // وصف الصفحة
            const Text(
              'يرجى تقديم المعلومات والمستندات المطلوبة للتحقق من حسابك. سيتم مراجعة طلبك خلال 24-48 ساعة.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey)),

            const SizedBox(height: 24),

            // رسالة الخطأ
            if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200)),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Colors.red.shade700))),
                  ])),

            if (_errorMessage != null) const SizedBox(height: 16),

            // نوع المستخدم
            const Text(
              'نوع الحساب',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),

            const SizedBox(height: 8),

            // اختيار نوع المستخدم
            Card(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.grey.shade300)),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  children: [
                    RadioListTile<domain_user.UserType>(
                      title: const Text('باحث عن عقار'),
                      subtitle: const Text(
                          'للأفراد الذين يبحثون عن عقارات'),
                      value: domain_user.UserType.seeker,
                      groupValue: _selectedUserType,
                      onChanged: (value) {
                        setState(() {
                          _selectedUserType = value!;
                        });
                      }),
                    RadioListTile<domain_user.UserType>(
                      title: const Text('مستثمر'),
                      subtitle: const Text('للمستثمرين العقاريين والوسطاء المرخصين'),
                      value: domain_user.UserType.agent,
                      groupValue: _selectedUserType,
                      onChanged: (value) {
                        setState(() {
                          _selectedUserType = value!;
                        });
                      }),
                    RadioListTile<domain_user.UserType>(
                      title: const Text('شركة عقارية'),
                      subtitle: const Text('للشركات العقارية المرخصة'),
                      value: domain_user.UserType.company,
                      groupValue: _selectedUserType,
                      onChanged: (value) {
                        setState(() {
                          _selectedUserType = value!;
                        });
                      }),
                    RadioListTile<domain_user.UserType>(
                      title: const Text('مالك عقار'),
                      subtitle: const Text('لملاك العقارات المتعددة'),
                      value: domain_user.UserType.owner,
                      groupValue: _selectedUserType,
                      onChanged: (value) {
                        setState(() {
                          _selectedUserType = value!;
                        });
                      }),
                  ]))),

            const SizedBox(height: 24),

            // المعلومات الشخصية
            const Text(
              'المعلومات الشخصية',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),

            const SizedBox(height: 8),

            // نموذج المعلومات الشخصية
            Card(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.grey.shade300)),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  children: [
                    // الاسم الكامل
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'الاسم الكامل',
                        hintText: 'أدخل الاسم الكامل',
                        prefixIcon: Icon(Icons.person)),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الاسم الكامل';
                        }
                        return null;
                      }),

                    const SizedBox(height: 16),

                    // رقم الهاتف
                    TextFormField(
                      controller: _phoneController,
                      decoration: const InputDecoration(
                        labelText: 'رقم الهاتف',
                        hintText: 'أدخل رقم الهاتف',
                        prefixIcon: Icon(Icons.phone)),
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال رقم الهاتف';
                        }
                        return null;
                      }),

                    // رقم الترخيص (للوكلاء والشركات)
                    if (_selectedUserType == domain_user.UserType.agent ||
                        _selectedUserType == domain_user.UserType.company) ...[
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _licenseController,
                        decoration: const InputDecoration(
                          labelText: 'رقم الترخيص',
                          hintText: 'أدخل رقم الترخيص',
                          prefixIcon: Icon(Icons.badge)),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال رقم الترخيص';
                          }
                          return null;
                        }),
                    ],

                    // اسم الشركة (للشركات فقط)
                    if (_selectedUserType == domain_user.UserType.company) ...[
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _companyController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الشركة',
                          hintText: 'أدخل اسم الشركة',
                          prefixIcon: Icon(Icons.business)),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال اسم الشركة';
                          }
                          return null;
                        }),
                    ],

                    const SizedBox(height: 16),

                    // العنوان
                    TextFormField(
                      controller: _addressController,
                      decoration: const InputDecoration(
                        labelText: 'العنوان',
                        hintText: 'أدخل العنوان',
                        prefixIcon: Icon(Icons.location_on)),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال العنوان';
                        }
                        return null;
                      }),
                  ]))),

            const SizedBox(height: 24),

            // المستندات
            const Text(
              'المستندات المطلوبة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),

            const SizedBox(height: 8),

            // قائمة المستندات المطلوبة
            Card(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.grey.shade300)),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // وصف المستندات المطلوبة
                    const Text(
                      'يرجى إرفاق المستندات التالية:',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold)),

                    const SizedBox(height: 8),

                    // قائمة المستندات المطلوبة
                    if (_selectedUserType == domain_user.UserType.seeker ||
                        _selectedUserType == domain_user.UserType.owner)
                      const Text('• صورة من البطاقة المدنية (الوجهين)'),

                    if (_selectedUserType == domain_user.UserType.agent)
                      const Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('• صورة من البطاقة المدنية (الوجهين)'),
                          Text('• صورة من رخصة المستثمر العقاري أو الوسيط'),
                        ]),

                    if (_selectedUserType == domain_user.UserType.company)
                      const Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('• صورة من البطاقة المدنية للمسؤول (الوجهين)'),
                          Text('• صورة من الرخصة التجارية'),
                          Text('• صورة من شهادة السجل التجاري'),
                        ]),

                    const SizedBox(height: 16),

                    // أزرار إضافة المستندات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _pickImage,
                          icon: const Icon(Icons.photo_library),
                          label: const Text('اختيار من المعرض'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white)),
                        ElevatedButton.icon(
                          onPressed: _takePhoto,
                          icon: const Icon(Icons.camera_alt),
                          label: const Text('التقاط صورة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white)),
                      ]),

                    const SizedBox(height: 16),

                    // عرض المستندات المرفقة
                    if (_documents.isNotEmpty) ...[
                      const Text(
                        'المستندات المرفقة:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 100,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: _documents.length,
                          itemBuilder: (context, index) {
                            return Stack(
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    image: DecorationImage(
                                      image: FileImage(_documents[index]),
                                      fit: BoxFit.cover))),
                                Positioned(
                                  top: 0,
                                  right: 8,
                                  child: GestureDetector(
                                    onTap: () => _removeImage(index),
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: const BoxDecoration(
                                        color: Colors.red,
                                        shape: BoxShape.circle),
                                      child: const Icon(
                                        Icons.close,
                                        color: Colors.white,
                                        size: 16)))),
                              ]);
                          })),
                    ],
                  ]))),

            const SizedBox(height: 24),

            // زر تقديم الطلب
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _submitVerificationRequest,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black87,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8))),
                child: const Text(
                  'تقديم طلب التحقق',
                  style: TextStyle(fontSize: 16)))),
          ])));
  }

  /// بناء صفحة النجاح
  Widget _buildSuccessView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 80),

            const SizedBox(height: 24),

            const Text(
              'تم تقديم طلب التحقق بنجاح',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold),
              textAlign: TextAlign.center),

            const SizedBox(height: 16),

            const Text(
              'سيتم مراجعة طلبك خلال 24-48 ساعة. سيتم إشعارك عند الانتهاء من المراجعة.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey),
              textAlign: TextAlign.center),

            const SizedBox(height: 32),

            // زر إلغاء الطلب
            OutlinedButton(
              onPressed: _cancelVerificationRequest,
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Colors.red),
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
              child: const Text(
                'إلغاء الطلب',
                style: TextStyle(color: Colors.red))),

            const SizedBox(height: 16),

            // زر العودة
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black87,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
              child: const Text('العودة إلى الصفحة الرئيسية')),
          ])));
  }
}
