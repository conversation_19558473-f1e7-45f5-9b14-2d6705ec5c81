import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج الإنجازات في المنتدى
class Achievement {
  final String id;
  final String title;
  final String description;
  final String iconName;
  final String category;
  final int pointsRequired;
  final int pointsReward;
  final String? badgeColor;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.iconName,
    required this.category,
    required this.pointsRequired,
    required this.pointsReward,
    this.badgeColor,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// إنشاء Achievement من Firestore Document
  factory Achievement.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Achievement(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      iconName: data['iconName'] ?? 'star',
      category: data['category'] ?? 'general',
      pointsRequired: data['pointsRequired'] ?? 0,
      pointsReward: data['pointsReward'] ?? 0,
      badgeColor: data['badgeColor'],
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  /// تحويل Achievement إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'iconName': iconName,
      'category': category,
      'pointsRequired': pointsRequired,
      'pointsReward': pointsReward,
      'badgeColor': badgeColor,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'metadata': metadata,
    };
  }

  /// إنشاء نسخة محدثة من Achievement
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    String? iconName,
    String? category,
    int? pointsRequired,
    int? pointsReward,
    String? badgeColor,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      category: category ?? this.category,
      pointsRequired: pointsRequired ?? this.pointsRequired,
      pointsReward: pointsReward ?? this.pointsReward,
      badgeColor: badgeColor ?? this.badgeColor,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Achievement && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Achievement(id: $id, title: $title, category: $category, pointsRequired: $pointsRequired)';
  }
}

/// نموذج إنجاز المستخدم
class UserAchievement {
  final String id;
  final String userId;
  final String achievementId;
  final DateTime unlockedAt;
  final int pointsEarned;
  final Map<String, dynamic>? metadata;

  const UserAchievement({
    required this.id,
    required this.userId,
    required this.achievementId,
    required this.unlockedAt,
    required this.pointsEarned,
    this.metadata,
  });

  /// إنشاء UserAchievement من Firestore Document
  factory UserAchievement.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserAchievement(
      id: doc.id,
      userId: data['userId'] ?? '',
      achievementId: data['achievementId'] ?? '',
      unlockedAt: (data['unlockedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      pointsEarned: data['pointsEarned'] ?? 0,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  /// تحويل UserAchievement إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'achievementId': achievementId,
      'unlockedAt': Timestamp.fromDate(unlockedAt),
      'pointsEarned': pointsEarned,
      'metadata': metadata,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserAchievement && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserAchievement(id: $id, userId: $userId, achievementId: $achievementId)';
  }
}

/// فئات الإنجازات المختلفة
enum AchievementCategory {
  posting('posting', 'المشاركات'),
  engagement('engagement', 'التفاعل'),
  reputation('reputation', 'السمعة'),
  community('community', 'المجتمع'),
  special('special', 'خاص'),
  milestone('milestone', 'معالم');

  const AchievementCategory(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static AchievementCategory fromString(String value) {
    return AchievementCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => AchievementCategory.special,
    );
  }
}

/// أنواع الأيقونات المتاحة للإنجازات
enum AchievementIcon {
  star('star'),
  trophy('trophy'),
  medal('medal'),
  crown('crown'),
  diamond('diamond'),
  fire('fire'),
  heart('heart'),
  thumbsUp('thumbs_up'),
  comment('comment'),
  share('share'),
  bookmark('bookmark'),
  verified('verified');

  const AchievementIcon(this.value);
  
  final String value;

  static AchievementIcon fromString(String value) {
    return AchievementIcon.values.firstWhere(
      (icon) => icon.value == value,
      orElse: () => AchievementIcon.star,
    );
  }
}

/// ألوان الشارات المختلفة
enum BadgeColor {
  bronze('bronze', '#CD7F32'),
  silver('silver', '#C0C0C0'),
  gold('gold', '#FFD700'),
  platinum('platinum', '#E5E4E2'),
  diamond('diamond', '#B9F2FF');

  const BadgeColor(this.name, this.hexColor);
  
  final String name;
  final String hexColor;

  static BadgeColor fromString(String name) {
    return BadgeColor.values.firstWhere(
      (color) => color.name == name,
      orElse: () => BadgeColor.bronze,
    );
  }
}
