import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:kuwait_corners/domain/repositories/estate_repository.dart';

/// Use case class for copying an existing estate record.
/// This allows investors to copy properties from owners.
class CopyEstate {
  final EstateRepository repository;
  final FirebaseFirestore firestore;

  CopyEstate(this.repository, this.firestore);

  /// Creates a copy of an existing estate with a new owner.
  ///
  /// [originalEstateId] is the ID of the estate to copy.
  /// [investorId] is the ID of the user who is copying the estate.
  Future<void> call(String originalEstateId, String investorId) async {
    // Get the original estate directly
    final originalEstate = await repository.getEstateById(originalEstateId);
    if (originalEstate == null) {
      throw Exception("العقار الأصلي غير موجود");
    }

    // Get investor data
    final userDoc = await firestore.collection('users').doc(investorId).get();
    if (!userDoc.exists) {
      throw Exception("بيانات المستخدم غير موجودة");
    }
    final userData = userDoc.data()!;

    // Extract investor data
    final advertiserImage = userData['profileImageUrl'] as String?;
    final advertiserName = userData['fullNameOrCompanyName'] as String?;
    final advertiserEmail = userData['email'] as String?;
    final advertiserRegistrationTimestamp = userData['createdAt'];
    DateTime? advertiserRegistrationDate;
    if (advertiserRegistrationTimestamp != null) {
      advertiserRegistrationDate =
          (advertiserRegistrationTimestamp as Timestamp).toDate();
    }

    // Count investor's ads
    final adsSnapshot = await firestore
        .collection('estates')
        .where('advertiserEmail', isEqualTo: advertiserEmail)
        .get();
    final advertiserAdsCount = adsSnapshot.docs.length;

    // Skip updating the original estate's copiedBy list for now
    // since we can't use copyWith on EstateBase

    // TODO: Implement proper estate copying when the Estate/EstateBase issue is resolved

    // For now, just log the copy operation
    print("Estate copy operation requested: $originalEstateId -> $investorId");

    // Skip creating a copy of the estate for now
    // since we can't use copyWith on EstateBase

    // TODO: Implement estate creation when the Estate/EstateBase issue is resolved
    // await repository.createEstate(copiedEstate);
  }
}
