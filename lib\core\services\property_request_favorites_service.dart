// lib/core/services/property_request_favorites_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/models/property_request/property_request_model.dart';
import '../../domain/models/favorites/favorite_property_request_model.dart';
import 'realtime_notification_service.dart';

/// خدمة مفضلة طلبات العقارات
class PropertyRequestFavoritesService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final RealtimeNotificationService _notificationService = RealtimeNotificationService();

  /// إضافة طلب عقار للمفضلة
  Future<void> addToFavorites(String requestId) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لإضافة للمفضلة');
    }

    try {
      // التحقق من عدم وجود الطلب في المفضلة مسبقاً
      final existingFavorite = await _firestore
          .collection('favorite_property_requests')
          .where('userId', isEqualTo: user.uid)
          .where('requestId', isEqualTo: requestId)
          .get();

      if (existingFavorite.docs.isNotEmpty) {
        throw Exception('الطلب موجود في المفضلة مسبقاً');
      }

      // الحصول على بيانات الطلب
      final requestDoc = await _firestore
          .collection('property_requests')
          .doc(requestId)
          .get();

      if (!requestDoc.exists) {
        throw Exception('طلب العقار غير موجود');
      }

      final request = PropertyRequestModel.fromFirestore(requestDoc);

      // إنشاء نموذج المفضلة
      final favorite = FavoritePropertyRequestModel(
        id: '',
        userId: user.uid,
        requestId: requestId,
        requestTitle: request.title,
        requestDescription: request.description,
        propertyType: request.propertyType,
        preferredLocations: request.preferredLocations,
        minPrice: request.minPrice,
        maxPrice: request.maxPrice,
        requestOwnerId: request.userId,
        requestOwnerName: request.userName,
        addedAt: DateTime.now(),
        isNotificationEnabled: true,
      );

      // إضافة للمفضلة
      await _firestore
          .collection('favorite_property_requests')
          .add(favorite.toFirestore());

      // تحديث عداد المفضلة في الطلب
      await _firestore
          .collection('property_requests')
          .doc(requestId)
          .update({
        'favoritesCount': FieldValue.increment(1),
      });

      // إرسال إشعار لصاحب الطلب
      if (request.userId != user.uid) {
        await _notificationService.sendNotification(
          recipientId: request.userId,
          type: RealtimeNotificationType.requestFavorited,
          title: 'إضافة للمفضلة',
          body: 'تم إضافة طلبك "${request.title}" للمفضلة',
          data: {
            'requestId': requestId,
            'userId': user.uid,
          },
        );
      }
    } catch (e) {
      throw Exception('خطأ في إضافة للمفضلة: $e');
    }
  }

  /// إزالة طلب عقار من المفضلة
  Future<void> removeFromFavorites(String requestId) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لإزالة من المفضلة');
    }

    try {
      // البحث عن المفضلة
      final favoriteQuery = await _firestore
          .collection('favorite_property_requests')
          .where('userId', isEqualTo: user.uid)
          .where('requestId', isEqualTo: requestId)
          .get();

      if (favoriteQuery.docs.isEmpty) {
        throw Exception('الطلب غير موجود في المفضلة');
      }

      // حذف المفضلة
      await _firestore
          .collection('favorite_property_requests')
          .doc(favoriteQuery.docs.first.id)
          .delete();

      // تحديث عداد المفضلة في الطلب
      await _firestore
          .collection('property_requests')
          .doc(requestId)
          .update({
        'favoritesCount': FieldValue.increment(-1),
      });
    } catch (e) {
      throw Exception('خطأ في إزالة من المفضلة: $e');
    }
  }

  /// التحقق من وجود طلب في المفضلة
  Future<bool> isFavorite(String requestId) async {
    final user = _auth.currentUser;
    if (user == null) return false;

    try {
      final favoriteQuery = await _firestore
          .collection('favorite_property_requests')
          .where('userId', isEqualTo: user.uid)
          .where('requestId', isEqualTo: requestId)
          .get();

      return favoriteQuery.docs.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على قائمة المفضلة
  Future<List<FavoritePropertyRequestModel>> getFavoriteRequests({
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? sortBy = 'addedAt',
    bool descending = true,
  }) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لعرض المفضلة');
    }

    try {
      Query query = _firestore
          .collection('favorite_property_requests')
          .where('userId', isEqualTo: user.uid)
          .orderBy(sortBy!, descending: descending)
          .limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => FavoritePropertyRequestModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('خطأ في جلب المفضلة: $e');
    }
  }

  /// الحصول على عدد المفضلة
  Future<int> getFavoritesCount() async {
    final user = _auth.currentUser;
    if (user == null) return 0;

    try {
      final snapshot = await _firestore
          .collection('favorite_property_requests')
          .where('userId', isEqualTo: user.uid)
          .get();

      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  /// البحث في المفضلة
  Future<List<FavoritePropertyRequestModel>> searchFavorites(
    String query, {
    int limit = 20,
  }) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول للبحث في المفضلة');
    }

    try {
      final snapshot = await _firestore
          .collection('favorite_property_requests')
          .where('userId', isEqualTo: user.uid)
          .get();

      final favorites = snapshot.docs
          .map((doc) => FavoritePropertyRequestModel.fromFirestore(doc))
          .toList();

      // فلترة النتائج محلياً
      final filteredFavorites = favorites.where((favorite) {
        final searchText = query.toLowerCase();
        return favorite.requestTitle.toLowerCase().contains(searchText) ||
               favorite.requestDescription.toLowerCase().contains(searchText) ||
               favorite.propertyType.toLowerCase().contains(searchText) ||
               favorite.preferredLocations.any((location) => 
                   location.toLowerCase().contains(searchText));
      }).take(limit).toList();

      return filteredFavorites;
    } catch (e) {
      throw Exception('خطأ في البحث في المفضلة: $e');
    }
  }

  /// تحديث إعدادات الإشعارات للمفضلة
  Future<void> updateNotificationSettings(
    String requestId,
    bool enableNotifications,
  ) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لتحديث الإعدادات');
    }

    try {
      final favoriteQuery = await _firestore
          .collection('favorite_property_requests')
          .where('userId', isEqualTo: user.uid)
          .where('requestId', isEqualTo: requestId)
          .get();

      if (favoriteQuery.docs.isEmpty) {
        throw Exception('الطلب غير موجود في المفضلة');
      }

      await _firestore
          .collection('favorite_property_requests')
          .doc(favoriteQuery.docs.first.id)
          .update({
        'isNotificationEnabled': enableNotifications,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('خطأ في تحديث إعدادات الإشعارات: $e');
    }
  }

  /// مسح جميع المفضلة
  Future<void> clearAllFavorites() async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لمسح المفضلة');
    }

    try {
      final snapshot = await _firestore
          .collection('favorite_property_requests')
          .where('userId', isEqualTo: user.uid)
          .get();

      final batch = _firestore.batch();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
        
        // تحديث عداد المفضلة في الطلب
        final favorite = FavoritePropertyRequestModel.fromFirestore(doc);
        final requestRef = _firestore
            .collection('property_requests')
            .doc(favorite.requestId);
        batch.update(requestRef, {
          'favoritesCount': FieldValue.increment(-1),
        });
      }

      await batch.commit();
    } catch (e) {
      throw Exception('خطأ في مسح المفضلة: $e');
    }
  }

  /// الحصول على إحصائيات المفضلة
  Future<Map<String, dynamic>> getFavoritesStatistics() async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لعرض الإحصائيات');
    }

    try {
      final snapshot = await _firestore
          .collection('favorite_property_requests')
          .where('userId', isEqualTo: user.uid)
          .get();

      final favorites = snapshot.docs
          .map((doc) => FavoritePropertyRequestModel.fromFirestore(doc))
          .toList();

      // إحصائيات حسب نوع العقار
      final propertyTypeStats = <String, int>{};
      for (final favorite in favorites) {
        propertyTypeStats[favorite.propertyType] = 
            (propertyTypeStats[favorite.propertyType] ?? 0) + 1;
      }

      // إحصائيات حسب المنطقة
      final locationStats = <String, int>{};
      for (final favorite in favorites) {
        for (final location in favorite.preferredLocations) {
          locationStats[location] = (locationStats[location] ?? 0) + 1;
        }
      }

      // إحصائيات حسب نطاق السعر
      final priceRangeStats = <String, int>{
        'أقل من 500': 0,
        '500-1000': 0,
        '1000-2000': 0,
        '2000-5000': 0,
        'أكثر من 5000': 0,
      };

      for (final favorite in favorites) {
        final maxPrice = favorite.maxPrice ?? 0;
        if (maxPrice < 500) {
          priceRangeStats['أقل من 500'] = priceRangeStats['أقل من 500']! + 1;
        } else if (maxPrice < 1000) {
          priceRangeStats['500-1000'] = priceRangeStats['500-1000']! + 1;
        } else if (maxPrice < 2000) {
          priceRangeStats['1000-2000'] = priceRangeStats['1000-2000']! + 1;
        } else if (maxPrice < 5000) {
          priceRangeStats['2000-5000'] = priceRangeStats['2000-5000']! + 1;
        } else {
          priceRangeStats['أكثر من 5000'] = priceRangeStats['أكثر من 5000']! + 1;
        }
      }

      return {
        'totalFavorites': favorites.length,
        'propertyTypeStats': propertyTypeStats,
        'locationStats': locationStats,
        'priceRangeStats': priceRangeStats,
        'recentFavorites': favorites
            .where((f) => DateTime.now().difference(f.addedAt).inDays <= 7)
            .length,
      };
    } catch (e) {
      throw Exception('خطأ في جلب إحصائيات المفضلة: $e');
    }
  }
}
