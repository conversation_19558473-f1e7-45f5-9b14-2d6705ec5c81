@echo off
chcp 65001 >nul
echo ========================================
echo Release Readiness Check - Krea App
echo ========================================
echo.

set "errors=0"

echo Checking required files and configurations...
echo.

:: التحقق من ملفات المشروع الأساسية
echo [1/10] التحقق من ملفات المشروع...
if not exist "pubspec.yaml" (
    echo ❌ ملف pubspec.yaml غير موجود
    set /a errors+=1
) else (
    echo ✅ pubspec.yaml موجود
)

if not exist "lib\main.dart" (
    echo ❌ ملف main.dart غير موجود
    set /a errors+=1
) else (
    echo ✅ main.dart موجود
)

:: التحقق من إعدادات Android
echo.
echo [2/10] التحقق من إعدادات Android...
if not exist "android\app\build.gradle" (
    echo ❌ ملف build.gradle غير موجود
    set /a errors+=1
) else (
    echo ✅ build.gradle موجود
    
    :: التحقق من applicationId
    findstr /C:"com.krea.app" android\app\build.gradle >nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ applicationId محدث إلى com.krea.app
    ) else (
        echo ❌ applicationId لم يتم تحديثه
        set /a errors+=1
    )
)

:: التحقق من Firebase
echo.
echo [3/10] التحقق من إعدادات Firebase...
if not exist "android\app\google-services.json" (
    echo ❌ ملف google-services.json غير موجود
    set /a errors+=1
) else (
    echo ✅ google-services.json موجود
)

:: التحقق من ProGuard
echo.
echo [4/10] التحقق من إعدادات ProGuard...
if not exist "android\app\proguard-rules.pro" (
    echo ❌ ملف proguard-rules.pro غير موجود
    set /a errors+=1
) else (
    echo ✅ proguard-rules.pro موجود
)

:: التحقق من مفتاح التوقيع
echo.
echo [5/10] التحقق من مفتاح التوقيع...
if not exist "android\key.properties" (
    echo ⚠️  ملف key.properties غير موجود (مطلوب للإنتاج)
    echo    يمكنك إنشاؤه بتشغيل: scripts\create_keystore.bat
) else (
    echo ✅ key.properties موجود
    
    if not exist "android\upload-keystore.jks" (
        echo ❌ ملف keystore غير موجود
        set /a errors+=1
    ) else (
        echo ✅ keystore موجود
    )
)

:: التحقق من Flutter
echo.
echo [6/10] التحقق من Flutter...
flutter --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Flutter غير مثبت أو غير متاح
    set /a errors+=1
) else (
    echo ✅ Flutter متاح
)

:: التحقق من التبعيات
echo.
echo [7/10] التحقق من التبعيات...
if not exist ".dart_tool\package_config.json" (
    echo ⚠️  التبعيات غير محدثة، تشغيل flutter pub get...
    flutter pub get >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم تحديث التبعيات
    ) else (
        echo ❌ فشل في تحديث التبعيات
        set /a errors+=1
    )
) else (
    echo ✅ التبعيات محدثة
)

:: التحقق من الأصول
echo.
echo [8/10] التحقق من ملفات الأصول...
if not exist "assets\images" (
    echo ❌ مجلد assets\images غير موجود
    set /a errors+=1
) else (
    echo ✅ مجلد الصور موجود
)

:: التحقق من ملف البيئة
echo.
echo [9/10] التحقق من ملف البيئة...
if not exist ".env" (
    echo ⚠️  ملف .env غير موجود (اختياري)
) else (
    echo ✅ ملف .env موجود
)

:: التحقق من الوثائق
echo.
echo [10/10] التحقق من الوثائق...
if not exist "docs\RELEASE_GUIDE.md" (
    echo ❌ دليل الإصدار غير موجود
    set /a errors+=1
) else (
    echo ✅ دليل الإصدار موجود
)

echo.
echo ========================================
if %errors% EQU 0 (
    echo ✅ جميع الفحوصات نجحت! المشروع جاهز للإصدار
    echo.
    echo الخطوات التالية:
    echo 1. تشغيل scripts\build_release.bat
    echo 2. رفع الملف على Google Play Console
    echo 3. إعداد الاختبار المغلق
) else (
    echo ❌ تم العثور على %errors% مشكلة/مشاكل
    echo يرجى إصلاح المشاكل قبل المتابعة
)
echo ========================================
echo.

pause
