import 'dart:io';

import 'package:firebase_storage/firebase_storage.dart';

import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path_util;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

/// خدمة إدارة صور المنتدى
class ForumImageService {
  /// مثيل Firebase Storage
  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// مسار تخزين صور المنتدى
  static const String _forumImagesPath = 'forum_images';

  /// مسار تخزين صور المواضيع
  static const String _topicImagesPath = '$_forumImagesPath/topics';

  /// مسار تخزين صور المشاركات
  static const String _postImagesPath = '$_forumImagesPath/posts';

  /// مسار تخزين صور المستخدمين
  static const String _userImagesPath = '$_forumImagesPath/users';

  /// الحد الأقصى لعرض الصورة (بالبكسل)
  static const int _maxImageWidth = 1200;

  /// الحد الأقصى لارتفاع الصورة (بالبكسل)
  static const int _maxImageHeight = 1200;

  /// جودة ضغط الصورة (0-100)
  static const int _imageQuality = 80;

  /// رفع صورة موضوع
  Future<String> uploadTopicImage(File imageFile, String topicId) async {
    final compressedImage = await _compressImage(imageFile);
    final fileName =
        '${topicId}_${const Uuid().v4()}${path_util.extension(imageFile.path)}';
    final storageRef = _storage.ref().child('$_topicImagesPath/$fileName');

    final uploadTask = storageRef.putFile(compressedImage);
    final snapshot = await uploadTask;

    return await snapshot.ref.getDownloadURL();
  }

  /// رفع صورة مشاركة
  Future<String> uploadPostImage(File imageFile, String postId) async {
    final compressedImage = await _compressImage(imageFile);
    final fileName =
        '${postId}_${const Uuid().v4()}${path_util.extension(imageFile.path)}';
    final storageRef = _storage.ref().child('$_postImagesPath/$fileName');

    final uploadTask = storageRef.putFile(compressedImage);
    final snapshot = await uploadTask;

    return await snapshot.ref.getDownloadURL();
  }

  /// رفع صورة مستخدم
  Future<String> uploadUserImage(File imageFile, String userId) async {
    final compressedImage = await _compressImage(imageFile);
    final fileName =
        '${userId}_${const Uuid().v4()}${path_util.extension(imageFile.path)}';
    final storageRef = _storage.ref().child('$_userImagesPath/$fileName');

    final uploadTask = storageRef.putFile(compressedImage);
    final snapshot = await uploadTask;

    return await snapshot.ref.getDownloadURL();
  }

  /// رفع مجموعة من الصور
  Future<List<String>> uploadImages(List<File> imageFiles, String path) async {
    final List<String> imageUrls = [];

    for (final imageFile in imageFiles) {
      final compressedImage = await _compressImage(imageFile);
      final fileName =
          '${const Uuid().v4()}${path_util.extension(imageFile.path)}';
      final storageRef = _storage.ref().child('$path/$fileName');

      final uploadTask = storageRef.putFile(compressedImage);
      final snapshot = await uploadTask;

      final url = await snapshot.ref.getDownloadURL();
      imageUrls.add(url);
    }

    return imageUrls;
  }

  /// حذف صورة
  Future<void> deleteImage(String imageUrl) async {
    try {
      final ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      // تجاهل الأخطاء عند حذف الصور
    }
  }

  /// حذف مجموعة من الصور
  Future<void> deleteImages(List<String> imageUrls) async {
    for (final imageUrl in imageUrls) {
      await deleteImage(imageUrl);
    }
  }

  /// ضغط الصورة
  Future<File> _compressImage(File imageFile) async {
    final tempDir = await getTemporaryDirectory();
    final targetPath = '${tempDir.path}/${path_util.basename(imageFile.path)}';

    final result = await FlutterImageCompress.compressAndGetFile(
      imageFile.path,
      targetPath,
      minWidth: _maxImageWidth,
      minHeight: _maxImageHeight,
      quality: _imageQuality);

    if (result == null) {
      return imageFile;
    }

    return File(result.path);
  }
}
