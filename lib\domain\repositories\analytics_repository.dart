import '../entities/market_analysis.dart';
import '../entities/market_prediction.dart';
import '../entities/price_estimation.dart';
import '../entities/recommendation.dart';
import '../enums/analysis_type.dart';
import '../enums/analysis_period.dart';

/// واجهة مستودع التحليلات
abstract class AnalyticsRepository {
  /// الحصول على تحليل السوق بواسطة المعرف
  ///
  /// [analysisId] معرف التحليل
  Future<MarketAnalysis?> getMarketAnalysisById(String analysisId);

  /// الحصول على تحليلات السوق
  ///
  /// [type] نوع التحليل
  /// [period] فترة التحليل
  /// [area] المنطقة الجغرافية
  /// [propertyType] نوع العقار
  Future<List<MarketAnalysis>> getMarketAnalyses({
    AnalysisType? type,
    AnalysisPeriod? period,
    String? area,
    String? propertyType,
  });

  /// الحصول على تحليلات السوق بالتحميل المتدرج
  ///
  /// [type] نوع التحليل
  /// [period] فترة التحليل
  /// [area] المنطقة الجغرافية
  /// [propertyType] نوع العقار
  /// [limit] عدد التحليلات في كل صفحة
  /// [lastAnalysisId] معرف آخر تحليل تم تحميله (للصفحات التالية)
  /// يعيد Map تحتوي على:
  /// - 'analyses': قائمة التحليلات
  /// - 'lastAnalysisId': معرف آخر تحليل (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من التحليلات
  Future<Map<String, dynamic>> getMarketAnalysesPaginated({
    AnalysisType? type,
    AnalysisPeriod? period,
    String? area,
    String? propertyType,
    int limit = 20,
    String? lastAnalysisId,
  });

  /// الحصول على أحدث تحليلات السوق
  ///
  /// [limit] عدد التحليلات
  Future<List<MarketAnalysis>> getLatestMarketAnalyses({int limit = 5});

  /// الحصول على تنبؤ السوق بواسطة المعرف
  ///
  /// [predictionId] معرف التنبؤ
  Future<MarketPrediction?> getMarketPredictionById(String predictionId);

  /// الحصول على تنبؤات السوق
  ///
  /// [type] نوع التنبؤ
  /// [period] فترة التنبؤ
  /// [area] المنطقة الجغرافية
  /// [propertyType] نوع العقار
  Future<List<MarketPrediction>> getMarketPredictions({
    PredictionType? type,
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  });

  /// الحصول على تنبؤات السوق بالتحميل المتدرج
  ///
  /// [type] نوع التنبؤ
  /// [period] فترة التنبؤ
  /// [area] المنطقة الجغرافية
  /// [propertyType] نوع العقار
  /// [limit] عدد التنبؤات في كل صفحة
  /// [lastPredictionId] معرف آخر تنبؤ تم تحميله (للصفحات التالية)
  /// يعيد Map تحتوي على:
  /// - 'predictions': قائمة التنبؤات
  /// - 'lastPredictionId': معرف آخر تنبؤ (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من التنبؤات
  Future<Map<String, dynamic>> getMarketPredictionsPaginated({
    PredictionType? type,
    PredictionPeriod? period,
    String? area,
    String? propertyType,
    int limit = 20,
    String? lastPredictionId,
  });

  /// الحصول على أحدث تنبؤات السوق
  ///
  /// [limit] عدد التنبؤات
  Future<List<MarketPrediction>> getLatestMarketPredictions({int limit = 5});

  /// تقدير سعر عقار
  ///
  /// [userId] معرف المستخدم
  /// [type] نوع التقدير
  /// [estateId] معرف العقار (اختياري)
  /// [area] المنطقة
  /// [propertyType] نوع العقار
  /// [size] المساحة
  /// [rooms] عدد الغرف
  /// [bathrooms] عدد الحمامات
  /// [age] عمر العقار
  /// [floor] الطابق
  /// [isFurnished] ما إذا كان العقار مفروش
  /// [isRenovated] ما إذا كان العقار مجدد
  /// [features] المميزات الإضافية
  Future<PriceEstimation> estimatePrice({
    required String userId,
    required PriceEstimationType type,
    String? estateId,
    required String area,
    required String propertyType,
    required double size,
    int? rooms,
    int? bathrooms,
    int? age,
    int? floor,
    bool? isFurnished,
    bool? isRenovated,
    List<String>? features,
  });

  /// الحصول على تقدير السعر بواسطة المعرف
  ///
  /// [estimationId] معرف التقدير
  Future<PriceEstimation?> getPriceEstimationById(String estimationId);

  /// الحصول على تقديرات الأسعار للمستخدم
  ///
  /// [userId] معرف المستخدم
  Future<List<PriceEstimation>> getUserPriceEstimations(String userId);

  /// الحصول على تقديرات الأسعار للمستخدم بالتحميل المتدرج
  ///
  /// [userId] معرف المستخدم
  /// [limit] عدد التقديرات في كل صفحة
  /// [lastEstimationId] معرف آخر تقدير تم تحميله (للصفحات التالية)
  /// يعيد Map تحتوي على:
  /// - 'estimations': قائمة التقديرات
  /// - 'lastEstimationId': معرف آخر تقدير (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من التقديرات
  Future<Map<String, dynamic>> getUserPriceEstimationsPaginated({
    required String userId,
    int limit = 20,
    String? lastEstimationId,
  });

  /// الحصول على توصيات للمستخدم
  ///
  /// [userId] معرف المستخدم
  /// [type] نوع التوصية
  /// [limit] عدد التوصيات
  Future<List<Recommendation>> getUserRecommendations({
    required String userId,
    RecommendationType? type,
    int limit = 10,
  });

  /// تحديث حالة التوصية
  ///
  /// [recommendationId] معرف التوصية
  /// [isViewed] ما إذا كانت التوصية تم عرضها
  /// [isClicked] ما إذا كانت التوصية تم النقر عليها
  /// [isDismissed] ما إذا كانت التوصية تم تجاهلها
  Future<void> updateRecommendationStatus({
    required String recommendationId,
    bool? isViewed,
    bool? isClicked,
    bool? isDismissed,
  });

  /// الحصول على إحصائيات المناطق
  ///
  /// [period] فترة الإحصائيات
  Future<Map<String, dynamic>> getAreasStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
  });

  /// الحصول على إحصائيات أنواع العقارات
  ///
  /// [period] فترة الإحصائيات
  Future<Map<String, dynamic>> getPropertyTypesStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
  });

  /// الحصول على إحصائيات الأسعار
  ///
  /// [period] فترة الإحصائيات
  /// [area] المنطقة الجغرافية
  /// [propertyType] نوع العقار
  Future<Map<String, dynamic>> getPriceStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  });

  /// الحصول على إحصائيات العرض والطلب
  ///
  /// [period] فترة الإحصائيات
  /// [area] المنطقة الجغرافية
  /// [propertyType] نوع العقار
  Future<Map<String, dynamic>> getSupplyDemandStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  });

  /// الحصول على إحصائيات المبيعات
  ///
  /// [period] فترة الإحصائيات
  /// [area] المنطقة الجغرافية
  /// [propertyType] نوع العقار
  Future<Map<String, dynamic>> getSalesStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  });

  /// الحصول على إحصائيات الإيجارات
  ///
  /// [period] فترة الإحصائيات
  /// [area] المنطقة الجغرافية
  /// [propertyType] نوع العقار
  Future<Map<String, dynamic>> getRentalsStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  });

  /// الحصول على إحصائيات العائد الاستثماري
  ///
  /// [period] فترة الإحصائيات
  /// [area] المنطقة الجغرافية
  /// [propertyType] نوع العقار
  Future<Map<String, dynamic>> getInvestmentReturnStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  });

  /// الحصول على مؤشرات السوق
  ///
  /// [period] فترة المؤشرات
  Future<Map<String, dynamic>> getMarketIndicators({
    AnalysisPeriod period = AnalysisPeriod.year,
  });

  /// الحصول على أفضل المناطق للاستثمار
  ///
  /// [limit] عدد المناطق
  Future<List<Map<String, dynamic>>> getTopInvestmentAreas({int limit = 5});

  /// الحصول على أفضل أنواع العقارات للاستثمار
  ///
  /// [limit] عدد أنواع العقارات
  Future<List<Map<String, dynamic>>> getTopInvestmentPropertyTypes(
      {int limit = 5});

  /// الحصول على تقرير السوق
  ///
  /// [period] فترة التقرير
  /// [area] المنطقة الجغرافية
  /// [propertyType] نوع العقار
  Future<Map<String, dynamic>> getMarketReport({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  });

  /// الحصول على تقرير المستخدم
  ///
  /// [userId] معرف المستخدم
  Future<Map<String, dynamic>> getUserReport(String userId);
}
