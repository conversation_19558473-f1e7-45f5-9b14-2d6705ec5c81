import 'package:flutter/material.dart';

/// نوع الإشعار
enum NotificationType {
  /// إشعار عقار جديد
  newEstate,
  
  /// إشعار رسالة جديدة
  newMessage,
  
  /// إشعار تحقق من الحساب
  accountVerification,
  
  /// إشعار عرض جديد على طلب عقار
  newPropertyRequestOffer,
  
  /// إشعار قبول عرض على طلب عقار
  propertyRequestOfferAccepted,
  
  /// إشعار رفض عرض على طلب عقار
  propertyRequestOfferRejected,
  
  /// إشعار تحديث حالة طلب عقار
  propertyRequestStatusUpdate,
  
  /// إشعار آخر
  other,
}

/// نموذج الإشعار
class NotificationModel {
  /// معرف الإشعار
  final String id;
  
  /// عنوان الإشعار
  final String title;
  
  /// نص الإشعار
  final String body;
  
  /// نوع الإشعار
  final NotificationType type;
  
  /// بيانات إضافية للإشعار
  final Map<String, dynamic> data;
  
  /// تاريخ الإشعار
  final DateTime timestamp;
  
  /// ما إذا كان الإشعار مقروء
  bool isRead;

  /// إنشاء نموذج إشعار
  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    required this.timestamp,
    this.isRead = false,
  });

  /// إنشاء نموذج إشعار من Firestore
  factory NotificationModel.fromFirestore(Map<String, dynamic> data, String id) {
    return NotificationModel(
      id: id,
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      type: _getNotificationTypeFromString(data['type'] ?? 'other'),
      data: data['data'] ?? {},
      timestamp: data['timestamp']?.toDate() ?? DateTime.now(),
      isRead: data['isRead'] ?? false);
  }

  /// تحويل نموذج الإشعار إلى Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'body': body,
      'type': type.toString().split('.').last,
      'data': data,
      'timestamp': timestamp,
      'isRead': isRead,
    };
  }

  /// الحصول على نوع الإشعار من النص
  static NotificationType _getNotificationTypeFromString(String typeString) {
    switch (typeString) {
      case 'newEstate':
        return NotificationType.newEstate;
      case 'newMessage':
        return NotificationType.newMessage;
      case 'accountVerification':
        return NotificationType.accountVerification;
      case 'newPropertyRequestOffer':
        return NotificationType.newPropertyRequestOffer;
      case 'propertyRequestOfferAccepted':
        return NotificationType.propertyRequestOfferAccepted;
      case 'propertyRequestOfferRejected':
        return NotificationType.propertyRequestOfferRejected;
      case 'propertyRequestStatusUpdate':
        return NotificationType.propertyRequestStatusUpdate;
      default:
        return NotificationType.other;
    }
  }

  /// الحصول على أيقونة الإشعار
  IconData getNotificationIcon() {
    switch (type) {
      case NotificationType.newEstate:
        return Icons.home;
      case NotificationType.newMessage:
        return Icons.message;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      case NotificationType.newPropertyRequestOffer:
        return Icons.local_offer;
      case NotificationType.propertyRequestOfferAccepted:
        return Icons.check_circle;
      case NotificationType.propertyRequestOfferRejected:
        return Icons.cancel;
      case NotificationType.propertyRequestStatusUpdate:
        return Icons.update;
      case NotificationType.other:
        return Icons.notifications;
    }
  }

  /// الحصول على لون الإشعار
  Color getNotificationColor() {
    switch (type) {
      case NotificationType.newEstate:
        return Colors.blue;
      case NotificationType.newMessage:
        return Colors.green;
      case NotificationType.accountVerification:
        return Colors.purple;
      case NotificationType.newPropertyRequestOffer:
        return Colors.orange;
      case NotificationType.propertyRequestOfferAccepted:
        return Colors.green;
      case NotificationType.propertyRequestOfferRejected:
        return Colors.red;
      case NotificationType.propertyRequestStatusUpdate:
        return Colors.blue;
      case NotificationType.other:
        return Colors.grey;
    }
  }
}
