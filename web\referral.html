<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kuwait Corners - دعوة للانضمام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .logo {
            width: 120px;
            height: 120px;
            margin: 20px auto;
            display: block;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        p {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 30px;
            font-weight: bold;
            margin: 10px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-primary {
            background-color: #2ecc71;
        }
        .btn-primary:hover {
            background-color: #27ae60;
        }
        .btn-secondary {
            background-color: #e74c3c;
        }
        .btn-secondary:hover {
            background-color: #c0392b;
        }
        .store-badges {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        .store-badge {
            margin: 10px;
            max-width: 180px;
        }
        .footer {
            margin-top: 50px;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="https://firebasestorage.googleapis.com/v0/b/kuwait-corners.appspot.com/o/app_assets%2Flogo.png?alt=media" alt="Kuwait Corners Logo" class="logo">
        <h1>تمت دعوتك للانضمام إلى Kuwait Corners!</h1>
        <p>تطبيق Kuwait Corners هو الوجهة الأولى للعقارات في الكويت. انضم الآن واستفد من مزايا حصرية!</p>
        
        <div id="referral-info">
            <p>تم دعوتك باستخدام رمز الإحالة: <strong id="referral-code"></strong></p>
        </div>
        
        <a href="#" id="open-app" class="btn btn-primary">فتح التطبيق</a>
        <a href="#" id="download-app" class="btn">تحميل التطبيق</a>
        
        <div class="store-badges">
            <a href="https://play.google.com/store/apps/details?id=com.example.kuwait_corners" target="_blank">
                <img src="https://play.google.com/intl/en_us/badges/static/images/badges/ar_badge_web_generic.png" alt="Google Play" class="store-badge">
            </a>
            <a href="https://apps.apple.com/app/id123456789" target="_blank">
                <img src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg" alt="App Store" class="store-badge">
            </a>
        </div>
        
        <div class="footer">
            <p>© 2023 Kuwait Corners. جميع الحقوق محفوظة.</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // استخراج رمز الإحالة من URL
            const urlParams = new URLSearchParams(window.location.search);
            const referralCode = urlParams.get('code');
            
            // عرض رمز الإحالة
            if (referralCode) {
                document.getElementById('referral-code').textContent = referralCode;
            } else {
                document.getElementById('referral-info').style.display = 'none';
            }
            
            // تكوين رابط فتح التطبيق
            const openAppBtn = document.getElementById('open-app');
            if (referralCode) {
                openAppBtn.href = `kuwaitcorners://referral?code=${referralCode}`;
            } else {
                openAppBtn.href = 'kuwaitcorners://';
            }
            
            // تكوين رابط تحميل التطبيق
            const downloadAppBtn = document.getElementById('download-app');
            
            // التحقق من نوع الجهاز
            const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            
            if (/android/i.test(userAgent)) {
                // Android
                downloadAppBtn.href = 'https://play.google.com/store/apps/details?id=com.example.kuwait_corners';
            } else if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
                // iOS
                downloadAppBtn.href = 'https://apps.apple.com/app/id123456789';
            } else {
                // Desktop or other
                downloadAppBtn.href = 'https://kuwaitcorners.com';
            }
            
            // محاولة فتح التطبيق تلقائيًا بعد 1 ثانية
            setTimeout(function() {
                if (referralCode) {
                    window.location.href = `kuwaitcorners://referral?code=${referralCode}`;
                } else {
                    window.location.href = 'kuwaitcorners://';
                }
            }, 1000);
        });
    </script>
</body>
</html>
