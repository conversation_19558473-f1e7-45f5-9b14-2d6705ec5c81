import 'package:equatable/equatable.dart';

/// حالة طلب التمويل العقاري
enum MortgageApplicationStatus {
  pending,
  approved,
  rejected,
  incomplete,
  processing,
  awaitingDocuments,
  awaitingPayment,
  completed,
}

/// كيان طلب التمويل العقاري
class MortgageApplication extends Equatable {
  final String id;
  final String bankId;
  final String bankName;
  final String userId;
  final String? estateId;
  final MortgageApplicationStatus status;
  final String mortgageType;
  final double propertyValue;
  final double loanAmount;
  final double downPayment;
  final int loanTerm;
  final double interestRate;
  final double monthlyPayment;
  final Map<String, dynamic> applicantInfo;
  final Map<String, String>? documents;
  final List<MortgageApplicationNote>? notes;
  final DateTime applicationDate;
  final DateTime? approvalDate;
  final DateTime? rejectionDate;
  final String? rejectionReason;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان طلب التمويل العقاري
  const MortgageApplication({
    required this.id,
    required this.bankId,
    required this.bankName,
    required this.userId,
    this.estateId,
    required this.status,
    required this.mortgageType,
    required this.propertyValue,
    required this.loanAmount,
    required this.downPayment,
    required this.loanTerm,
    required this.interestRate,
    required this.monthlyPayment,
    required this.applicantInfo,
    this.documents,
    this.notes,
    required this.applicationDate,
    this.approvalDate,
    this.rejectionDate,
    this.rejectionReason,
    this.additionalInfo,
  });

  /// إنشاء كيان طلب التمويل العقاري من JSON
  factory MortgageApplication.fromJson(Map<String, dynamic> json) {
    return MortgageApplication(
      id: json['id'] as String,
      bankId: json['bankId'] as String,
      bankName: json['bankName'] as String,
      userId: json['userId'] as String,
      estateId: json['estateId'] as String?,
      status: _parseStatus(json['status'] as String),
      mortgageType: json['mortgageType'] as String,
      propertyValue: json['propertyValue'] as double,
      loanAmount: json['loanAmount'] as double,
      downPayment: json['downPayment'] as double,
      loanTerm: json['loanTerm'] as int,
      interestRate: json['interestRate'] as double,
      monthlyPayment: json['monthlyPayment'] as double,
      applicantInfo: json['applicantInfo'] as Map<String, dynamic>,
      documents: json['documents'] != null
          ? Map<String, String>.from(json['documents'] as Map)
          : null,
      notes: json['notes'] != null
          ? (json['notes'] as List)
              .map((note) => MortgageApplicationNote.fromJson(note))
              .toList()
          : null,
      applicationDate: DateTime.parse(json['applicationDate'] as String),
      approvalDate: json['approvalDate'] != null
          ? DateTime.parse(json['approvalDate'] as String)
          : null,
      rejectionDate: json['rejectionDate'] != null
          ? DateTime.parse(json['rejectionDate'] as String)
          : null,
      rejectionReason: json['rejectionReason'] as String?,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان طلب التمويل العقاري إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bankId': bankId,
      'bankName': bankName,
      'userId': userId,
      'estateId': estateId,
      'status': _statusToString(status),
      'mortgageType': mortgageType,
      'propertyValue': propertyValue,
      'loanAmount': loanAmount,
      'downPayment': downPayment,
      'loanTerm': loanTerm,
      'interestRate': interestRate,
      'monthlyPayment': monthlyPayment,
      'applicantInfo': applicantInfo,
      'documents': documents,
      'notes': notes?.map((note) => note.toJson()).toList(),
      'applicationDate': applicationDate.toIso8601String(),
      'approvalDate': approvalDate?.toIso8601String(),
      'rejectionDate': rejectionDate?.toIso8601String(),
      'rejectionReason': rejectionReason,
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان طلب التمويل العقاري مع تعديل بعض الخصائص
  MortgageApplication copyWith({
    String? id,
    String? bankId,
    String? bankName,
    String? userId,
    String? estateId,
    MortgageApplicationStatus? status,
    String? mortgageType,
    double? propertyValue,
    double? loanAmount,
    double? downPayment,
    int? loanTerm,
    double? interestRate,
    double? monthlyPayment,
    Map<String, dynamic>? applicantInfo,
    Map<String, String>? documents,
    List<MortgageApplicationNote>? notes,
    DateTime? applicationDate,
    DateTime? approvalDate,
    DateTime? rejectionDate,
    String? rejectionReason,
    Map<String, dynamic>? additionalInfo,
  }) {
    return MortgageApplication(
      id: id ?? this.id,
      bankId: bankId ?? this.bankId,
      bankName: bankName ?? this.bankName,
      userId: userId ?? this.userId,
      estateId: estateId ?? this.estateId,
      status: status ?? this.status,
      mortgageType: mortgageType ?? this.mortgageType,
      propertyValue: propertyValue ?? this.propertyValue,
      loanAmount: loanAmount ?? this.loanAmount,
      downPayment: downPayment ?? this.downPayment,
      loanTerm: loanTerm ?? this.loanTerm,
      interestRate: interestRate ?? this.interestRate,
      monthlyPayment: monthlyPayment ?? this.monthlyPayment,
      applicantInfo: applicantInfo ?? this.applicantInfo,
      documents: documents ?? this.documents,
      notes: notes ?? this.notes,
      applicationDate: applicationDate ?? this.applicationDate,
      approvalDate: approvalDate ?? this.approvalDate,
      rejectionDate: rejectionDate ?? this.rejectionDate,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// تحويل حالة الطلب من نص إلى قيمة تعداد
  static MortgageApplicationStatus _parseStatus(String status) {
    switch (status) {
      case 'pending':
        return MortgageApplicationStatus.pending;
      case 'approved':
        return MortgageApplicationStatus.approved;
      case 'rejected':
        return MortgageApplicationStatus.rejected;
      case 'incomplete':
        return MortgageApplicationStatus.incomplete;
      case 'processing':
        return MortgageApplicationStatus.processing;
      case 'awaiting_documents':
        return MortgageApplicationStatus.awaitingDocuments;
      case 'awaiting_payment':
        return MortgageApplicationStatus.awaitingPayment;
      case 'completed':
        return MortgageApplicationStatus.completed;
      default:
        return MortgageApplicationStatus.pending;
    }
  }

  /// تحويل حالة الطلب من قيمة تعداد إلى نص
  static String _statusToString(MortgageApplicationStatus status) {
    switch (status) {
      case MortgageApplicationStatus.pending:
        return 'pending';
      case MortgageApplicationStatus.approved:
        return 'approved';
      case MortgageApplicationStatus.rejected:
        return 'rejected';
      case MortgageApplicationStatus.incomplete:
        return 'incomplete';
      case MortgageApplicationStatus.processing:
        return 'processing';
      case MortgageApplicationStatus.awaitingDocuments:
        return 'awaiting_documents';
      case MortgageApplicationStatus.awaitingPayment:
        return 'awaiting_payment';
      case MortgageApplicationStatus.completed:
        return 'completed';
    }
  }

  /// حساب نسبة القرض إلى القيمة
  double getLoanToValueRatio() {
    return loanAmount / propertyValue;
  }

  /// حساب إجمالي المدفوعات
  double getTotalPayment() {
    return monthlyPayment * loanTerm * 12;
  }

  /// حساب إجمالي الفائدة
  double getTotalInterest() {
    return getTotalPayment() - loanAmount;
  }

  /// التحقق مما إذا كان الطلب مكتمل
  bool isComplete() {
    return status == MortgageApplicationStatus.completed;
  }

  /// التحقق مما إذا كان الطلب تمت الموافقة عليه
  bool isApproved() {
    return status == MortgageApplicationStatus.approved;
  }

  /// التحقق مما إذا كان الطلب مرفوض
  bool isRejected() {
    return status == MortgageApplicationStatus.rejected;
  }

  /// التحقق مما إذا كان الطلب في انتظار المستندات
  bool isAwaitingDocuments() {
    return status == MortgageApplicationStatus.awaitingDocuments;
  }

  @override
  List<Object?> get props => [
        id,
        bankId,
        bankName,
        userId,
        estateId,
        status,
        mortgageType,
        propertyValue,
        loanAmount,
        downPayment,
        loanTerm,
        interestRate,
        monthlyPayment,
        applicantInfo,
        documents,
        notes,
        applicationDate,
        approvalDate,
        rejectionDate,
        rejectionReason,
        additionalInfo,
      ];
}

/// كيان ملاحظة طلب التمويل العقاري
class MortgageApplicationNote extends Equatable {
  final String id;
  final String content;
  final String authorId;
  final String authorName;
  final DateTime createdAt;
  final bool isInternal;

  /// إنشاء كيان ملاحظة طلب التمويل العقاري
  const MortgageApplicationNote({
    required this.id,
    required this.content,
    required this.authorId,
    required this.authorName,
    required this.createdAt,
    this.isInternal = false,
  });

  /// إنشاء كيان ملاحظة طلب التمويل العقاري من JSON
  factory MortgageApplicationNote.fromJson(Map<String, dynamic> json) {
    return MortgageApplicationNote(
      id: json['id'] as String,
      content: json['content'] as String,
      authorId: json['authorId'] as String,
      authorName: json['authorName'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      isInternal: json['isInternal'] as bool? ?? false);
  }

  /// تحويل كيان ملاحظة طلب التمويل العقاري إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'authorId': authorId,
      'authorName': authorName,
      'createdAt': createdAt.toIso8601String(),
      'isInternal': isInternal,
    };
  }

  /// نسخ كيان ملاحظة طلب التمويل العقاري مع تعديل بعض الخصائص
  MortgageApplicationNote copyWith({
    String? id,
    String? content,
    String? authorId,
    String? authorName,
    DateTime? createdAt,
    bool? isInternal,
  }) {
    return MortgageApplicationNote(
      id: id ?? this.id,
      content: content ?? this.content,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      createdAt: createdAt ?? this.createdAt,
      isInternal: isInternal ?? this.isInternal);
  }

  @override
  List<Object?> get props => [
        id,
        content,
        authorId,
        authorName,
        createdAt,
        isInternal,
      ];
}
