import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart' hide Query, Transaction;
import 'package:flutter/foundation.dart';

/// تكوين قاعدة البيانات المحسن
class DatabaseConfig {
  static final DatabaseConfig _instance = DatabaseConfig._internal();
  factory DatabaseConfig() => _instance;
  DatabaseConfig._internal();

  late FirebaseFirestore _firestore;
  late FirebaseDatabase _realtimeDatabase;
  
  bool _isInitialized = false;
  Timer? _maintenanceTimer;

  /// تهيئة قاعدة البيانات مع التحسينات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة Firestore مع إعدادات محسنة
      _firestore = FirebaseFirestore.instance;

      // تكوين إعدادات Firestore
      _firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
        ignoreUndefinedProperties: false,
      );

      // تهيئة Realtime Database
      _realtimeDatabase = FirebaseDatabase.instance;
      
      // تفعيل التخزين المؤقت للـ Realtime Database
      _realtimeDatabase.setPersistenceEnabled(true);
      _realtimeDatabase.setPersistenceCacheSizeBytes(10 * 1024 * 1024); // 10 MB

      // بدء مهام الصيانة الدورية
      _startMaintenanceTasks();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة قاعدة البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة قاعدة البيانات: $e');
      rethrow;
    }
  }

  /// الحصول على مثيل Firestore
  FirebaseFirestore get firestore {
    if (!_isInitialized) {
      throw StateError('يجب تهيئة قاعدة البيانات أولاً');
    }
    return _firestore;
  }

  /// الحصول على مثيل Realtime Database
  FirebaseDatabase get realtimeDatabase {
    if (!_isInitialized) {
      throw StateError('يجب تهيئة قاعدة البيانات أولاً');
    }
    return _realtimeDatabase;
  }

  /// تحسين الاستعلامات بالتجميع
  Future<List<DocumentSnapshot>> batchGet(List<DocumentReference> refs) async {
    if (refs.isEmpty) return [];
    
    // تقسيم المراجع إلى مجموعات (Firestore يدعم حتى 100 مرجع في المرة الواحدة)
    const batchSize = 100;
    final results = <DocumentSnapshot>[];
    
    for (int i = 0; i < refs.length; i += batchSize) {
      final batch = refs.skip(i).take(batchSize).toList();
      final snapshots = await Future.wait(batch.map((ref) => ref.get()));
      results.addAll(snapshots);
    }
    
    return results;
  }

  /// كتابة مجمعة محسنة
  Future<void> batchWrite(List<BatchOperation> operations) async {
    if (operations.isEmpty) return;
    
    // تقسيم العمليات إلى مجموعات (Firestore يدعم حتى 500 عملية في المرة الواحدة)
    const batchSize = 500;
    
    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = _firestore.batch();
      final batchOps = operations.skip(i).take(batchSize);
      
      for (final operation in batchOps) {
        switch (operation.type) {
          case BatchOperationType.set:
            batch.set(operation.reference, operation.data!, operation.setOptions);
            break;
          case BatchOperationType.update:
            batch.update(operation.reference, operation.data!);
            break;
          case BatchOperationType.delete:
            batch.delete(operation.reference);
            break;
        }
      }
      
      await batch.commit();
    }
  }

  /// تنفيذ معاملة محسنة
  Future<T> runOptimizedTransaction<T>(
    Future<T> Function(Transaction transaction) updateFunction, {
    Duration timeout = const Duration(seconds: 30),
    int maxAttempts = 5,
  }) async {
    int attempts = 0;
    
    while (attempts < maxAttempts) {
      try {
        return await _firestore.runTransaction(
          updateFunction,
          timeout: timeout,
        );
      } catch (e) {
        attempts++;
        if (attempts >= maxAttempts) {
          debugPrint('فشل في تنفيذ المعاملة بعد $maxAttempts محاولات: $e');
          rethrow;
        }
        
        // انتظار قبل المحاولة مرة أخرى
        await Future.delayed(Duration(milliseconds: 100 * attempts));
      }
    }
    
    throw StateError('فشل في تنفيذ المعاملة');
  }

  /// تحسين الاستعلامات المركبة
  Future<List<QueryDocumentSnapshot>> optimizedCompoundQuery({
    required String collection,
    required List<QueryCondition> conditions,
    List<QueryOrder>? orderBy,
    int? limit,
    DocumentSnapshot? startAfter,
  }) async {
    Query query = _firestore.collection(collection);
    
    // تطبيق الشروط
    for (final condition in conditions) {
      query = _applyCondition(query, condition);
    }
    
    // تطبيق الترتيب
    if (orderBy != null) {
      for (final order in orderBy) {
        query = query.orderBy(order.field, descending: order.descending);
      }
    }
    
    // تطبيق الحد الأقصى
    if (limit != null) {
      query = query.limit(limit);
    }
    
    // تطبيق نقطة البداية
    if (startAfter != null) {
      query = query.startAfterDocument(startAfter);
    }
    
    final snapshot = await query.get();
    return snapshot.docs;
  }

  /// تطبيق شرط على الاستعلام
  Query _applyCondition(Query query, QueryCondition condition) {
    switch (condition.operator) {
      case ConditionOperator.isEqualTo:
        return query.where(condition.field, isEqualTo: condition.value);
      case ConditionOperator.isNotEqualTo:
        return query.where(condition.field, isNotEqualTo: condition.value);
      case ConditionOperator.isLessThan:
        return query.where(condition.field, isLessThan: condition.value);
      case ConditionOperator.isLessThanOrEqualTo:
        return query.where(condition.field, isLessThanOrEqualTo: condition.value);
      case ConditionOperator.isGreaterThan:
        return query.where(condition.field, isGreaterThan: condition.value);
      case ConditionOperator.isGreaterThanOrEqualTo:
        return query.where(condition.field, isGreaterThanOrEqualTo: condition.value);
      case ConditionOperator.arrayContains:
        return query.where(condition.field, arrayContains: condition.value);
      case ConditionOperator.arrayContainsAny:
        return query.where(condition.field, arrayContainsAny: condition.value);
      case ConditionOperator.whereIn:
        return query.where(condition.field, whereIn: condition.value);
      case ConditionOperator.whereNotIn:
        return query.where(condition.field, whereNotIn: condition.value);
    }
  }

  /// بدء مهام الصيانة الدورية
  void _startMaintenanceTasks() {
    // تنفيذ مهام الصيانة كل ساعة
    _maintenanceTimer = Timer.periodic(
      const Duration(hours: 1),
      (timer) => _performMaintenance(),
    );
  }

  /// تنفيذ مهام الصيانة
  Future<void> _performMaintenance() async {
    try {
      debugPrint('🔧 بدء مهام صيانة قاعدة البيانات...');
      
      // تنظيف البيانات المؤقتة المنتهية الصلاحية
      await _cleanupExpiredData();
      
      // تحسين الفهارس
      await _optimizeIndexes();
      
      // تنظيف الملفات المؤقتة
      await _cleanupTempFiles();
      
      debugPrint('✅ تم إكمال مهام الصيانة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في مهام الصيانة: $e');
    }
  }

  /// تنظيف البيانات المنتهية الصلاحية
  Future<void> _cleanupExpiredData() async {
    final now = DateTime.now();
    final expiredThreshold = now.subtract(const Duration(days: 30));
    
    // تنظيف الإشعارات القديمة
    await _firestore
      .collection('notifications')
      .where('createdAt', isLessThan: Timestamp.fromDate(expiredThreshold))
      .where('isRead', isEqualTo: true)
      .get()
      .then((snapshot) async {
        final batch = _firestore.batch();
        for (final doc in snapshot.docs) {
          batch.delete(doc.reference);
        }
        if (snapshot.docs.isNotEmpty) {
          await batch.commit();
          debugPrint('تم حذف ${snapshot.docs.length} إشعار منتهي الصلاحية');
        }
      });

    // تنظيف سجلات الأمان القديمة
    await _firestore
      .collection('security_logs')
      .where('timestamp', isLessThan: expiredThreshold.toIso8601String())
      .where('isResolved', isEqualTo: true)
      .get()
      .then((snapshot) async {
        final batch = _firestore.batch();
        for (final doc in snapshot.docs) {
          batch.delete(doc.reference);
        }
        if (snapshot.docs.isNotEmpty) {
          await batch.commit();
          debugPrint('تم حذف ${snapshot.docs.length} سجل أمان منتهي الصلاحية');
        }
      });
  }

  /// تحسين الفهارس
  Future<void> _optimizeIndexes() async {
    // هذه الوظيفة تحتاج لتنفيذ عبر Cloud Functions
    // يمكن إضافة منطق لتحليل استخدام الفهارس وتحسينها
    debugPrint('🔍 تحليل استخدام الفهارس...');
  }

  /// تنظيف الملفات المؤقتة
  Future<void> _cleanupTempFiles() async {
    // تنظيف الملفات المؤقتة في Firebase Storage
    debugPrint('🗑️ تنظيف الملفات المؤقتة...');
  }

  /// الحصول على إحصائيات قاعدة البيانات
  Future<Map<String, dynamic>> getDatabaseStats() async {
    final stats = <String, dynamic>{};
    
    try {
      // إحصائيات المستخدمين
      final usersSnapshot = await _firestore.collection('users').count().get();
      stats['totalUsers'] = usersSnapshot.count;
      
      // إحصائيات العقارات
      final estatesSnapshot = await _firestore.collection('estates').count().get();
      stats['totalEstates'] = estatesSnapshot.count;
      
      // إحصائيات العقارات النشطة
      final activeEstatesSnapshot = await _firestore
        .collection('estates')
        .where('isActive', isEqualTo: true)
        .count()
        .get();
      stats['activeEstates'] = activeEstatesSnapshot.count;
      
      // إحصائيات المشاريع
      final projectsSnapshot = await _firestore.collection('projects').count().get();
      stats['totalProjects'] = projectsSnapshot.count;
      
      stats['lastUpdated'] = DateTime.now().toIso8601String();
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات قاعدة البيانات: $e');
    }
    
    return stats;
  }

  /// تنظيف الموارد
  void dispose() {
    _maintenanceTimer?.cancel();
    _maintenanceTimer = null;
  }
}

/// عملية مجمعة
class BatchOperation {
  final BatchOperationType type;
  final DocumentReference reference;
  final Map<String, dynamic>? data;
  final SetOptions? setOptions;

  BatchOperation({
    required this.type,
    required this.reference,
    this.data,
    this.setOptions,
  });

  factory BatchOperation.set(
    DocumentReference reference,
    Map<String, dynamic> data, {
    SetOptions? options,
  }) {
    return BatchOperation(
      type: BatchOperationType.set,
      reference: reference,
      data: data,
      setOptions: options,
    );
  }

  factory BatchOperation.update(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) {
    return BatchOperation(
      type: BatchOperationType.update,
      reference: reference,
      data: data,
    );
  }

  factory BatchOperation.delete(DocumentReference reference) {
    return BatchOperation(
      type: BatchOperationType.delete,
      reference: reference,
    );
  }
}

/// نوع العملية المجمعة
enum BatchOperationType { set, update, delete }

/// شرط الاستعلام
class QueryCondition {
  final String field;
  final ConditionOperator operator;
  final dynamic value;

  QueryCondition({
    required this.field,
    required this.operator,
    required this.value,
  });
}

/// عامل الشرط
enum ConditionOperator {
  isEqualTo,
  isNotEqualTo,
  isLessThan,
  isLessThanOrEqualTo,
  isGreaterThan,
  isGreaterThanOrEqualTo,
  arrayContains,
  arrayContainsAny,
  whereIn,
  whereNotIn,
}

/// ترتيب الاستعلام
class QueryOrder {
  final String field;
  final bool descending;

  QueryOrder({
    required this.field,
    this.descending = false,
  });
}
