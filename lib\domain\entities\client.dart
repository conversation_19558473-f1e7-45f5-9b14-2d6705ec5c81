import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// حالة العميل
enum ClientStatus {
  /// عميل جديد
  new_,
  
  /// عميل نشط
  active,
  
  /// عميل غير نشط
  inactive,
  
  /// عميل سابق
  former,
}

/// نموذج للعملاء
class Client extends Equatable {
  /// معرف العميل
  final String id;
  
  /// معرف المستخدم المالك للعميل (الوسيط أو الشركة)
  final String ownerId;
  
  /// اسم العميل
  final String name;
  
  /// رقم الهاتف
  final String phoneNumber;
  
  /// البريد الإلكتروني
  final String? email;
  
  /// صورة العميل
  final String? image;
  
  /// حالة العميل
  final ClientStatus status;
  
  /// نوع العميل (مشتري، مستأجر، بائع، مؤجر)
  final String? type;
  
  /// ملاحظات عن العميل
  final String? notes;
  
  /// تاريخ إنشاء العميل
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث للعميل
  final DateTime? updatedAt;
  
  /// تاريخ آخر تواصل مع العميل
  final DateTime? lastContactDate;
  
  /// معرف المستخدم في النظام (إذا كان العميل مستخدم مسجل)
  final String? userId;
  
  /// معايير البحث للعميل
  final Map<String, dynamic>? searchCriteria;
  
  /// قائمة معرفات العقارات المفضلة للعميل
  final List<String>? favoriteEstates;
  
  /// قائمة معرفات العقارات التي تمت زيارتها
  final List<String>? visitedEstates;
  
  /// قائمة معرفات المواعيد
  final List<String>? appointments;
  
  /// قائمة معرفات العروض
  final List<String>? offers;
  
  /// قائمة معرفات العقارات المباعة/المؤجرة
  final List<String>? soldRentedEstates;
  
  /// الميزانية المتاحة
  final double? budget;
  
  /// معلومات إضافية
  final Map<String, dynamic>? additionalInfo;

  const Client({
    required this.id,
    required this.ownerId,
    required this.name,
    required this.phoneNumber,
    this.email,
    this.image,
    required this.status,
    this.type,
    this.notes,
    required this.createdAt,
    this.updatedAt,
    this.lastContactDate,
    this.userId,
    this.searchCriteria,
    this.favoriteEstates,
    this.visitedEstates,
    this.appointments,
    this.offers,
    this.soldRentedEstates,
    this.budget,
    this.additionalInfo,
  });

  /// إنشاء نسخة معدلة من العميل
  Client copyWith({
    String? id,
    String? ownerId,
    String? name,
    String? phoneNumber,
    String? email,
    String? image,
    ClientStatus? status,
    String? type,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastContactDate,
    String? userId,
    Map<String, dynamic>? searchCriteria,
    List<String>? favoriteEstates,
    List<String>? visitedEstates,
    List<String>? appointments,
    List<String>? offers,
    List<String>? soldRentedEstates,
    double? budget,
    Map<String, dynamic>? additionalInfo,
  }) {
    return Client(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      image: image ?? this.image,
      status: status ?? this.status,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastContactDate: lastContactDate ?? this.lastContactDate,
      userId: userId ?? this.userId,
      searchCriteria: searchCriteria ?? this.searchCriteria,
      favoriteEstates: favoriteEstates ?? this.favoriteEstates,
      visitedEstates: visitedEstates ?? this.visitedEstates,
      appointments: appointments ?? this.appointments,
      offers: offers ?? this.offers,
      soldRentedEstates: soldRentedEstates ?? this.soldRentedEstates,
      budget: budget ?? this.budget,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }
  
  /// تحويل العميل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'ownerId': ownerId,
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
      'image': image,
      'status': status.toString().split('.').last.replaceAll('_', ''),
      'type': type,
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'lastContactDate': lastContactDate != null ? Timestamp.fromDate(lastContactDate!) : null,
      'userId': userId,
      'searchCriteria': searchCriteria,
      'favoriteEstates': favoriteEstates,
      'visitedEstates': visitedEstates,
      'appointments': appointments,
      'offers': offers,
      'soldRentedEstates': soldRentedEstates,
      'budget': budget,
      'additionalInfo': additionalInfo,
    };
  }
  
  /// إنشاء عميل من Map
  factory Client.fromMap(Map<String, dynamic> map) {
    return Client(
      id: map['id'] ?? '',
      ownerId: map['ownerId'] ?? '',
      name: map['name'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      email: map['email'],
      image: map['image'],
      status: _getClientStatusFromString(map['status'] ?? 'new'),
      type: map['type'],
      notes: map['notes'],
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
      lastContactDate: map['lastContactDate'] is Timestamp 
          ? (map['lastContactDate'] as Timestamp).toDate() 
          : null,
      userId: map['userId'],
      searchCriteria: map['searchCriteria'],
      favoriteEstates: map['favoriteEstates'] != null 
          ? List<String>.from(map['favoriteEstates']) 
          : null,
      visitedEstates: map['visitedEstates'] != null 
          ? List<String>.from(map['visitedEstates']) 
          : null,
      appointments: map['appointments'] != null 
          ? List<String>.from(map['appointments']) 
          : null,
      offers: map['offers'] != null 
          ? List<String>.from(map['offers']) 
          : null,
      soldRentedEstates: map['soldRentedEstates'] != null 
          ? List<String>.from(map['soldRentedEstates']) 
          : null,
      budget: map['budget'],
      additionalInfo: map['additionalInfo']);
  }
  
  /// إنشاء عميل من DocumentSnapshot
  factory Client.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return Client.fromMap(data);
  }
  
  /// الحصول على حالة العميل من النص
  static ClientStatus _getClientStatusFromString(String statusStr) {
    switch (statusStr) {
      case 'active':
        return ClientStatus.active;
      case 'inactive':
        return ClientStatus.inactive;
      case 'former':
        return ClientStatus.former;
      default:
        return ClientStatus.new_;
    }
  }
  
  /// الحصول على اسم حالة العميل بالعربية
  String getClientStatusName() {
    switch (status) {
      case ClientStatus.active:
        return 'نشط';
      case ClientStatus.inactive:
        return 'غير نشط';
      case ClientStatus.former:
        return 'سابق';
      case ClientStatus.new_:
        return 'جديد';
    }
  }
  
  /// تحديث حالة العميل
  Client updateStatus(ClientStatus newStatus) {
    return copyWith(
      status: newStatus,
      updatedAt: DateTime.now());
  }
  
  /// تحديث تاريخ آخر تواصل
  Client updateLastContactDate() {
    return copyWith(
      lastContactDate: DateTime.now(),
      updatedAt: DateTime.now());
  }
  
  /// إضافة عقار مفضل
  Client addFavoriteEstate(String estateId) {
    final newFavoriteEstates = List<String>.from(favoriteEstates ?? []);
    
    if (!newFavoriteEstates.contains(estateId)) {
      newFavoriteEstates.add(estateId);
    }
    
    return copyWith(
      favoriteEstates: newFavoriteEstates,
      updatedAt: DateTime.now());
  }
  
  /// إزالة عقار مفضل
  Client removeFavoriteEstate(String estateId) {
    if (favoriteEstates == null || !favoriteEstates!.contains(estateId)) {
      return this;
    }
    
    final newFavoriteEstates = List<String>.from(favoriteEstates!);
    newFavoriteEstates.remove(estateId);
    
    return copyWith(
      favoriteEstates: newFavoriteEstates,
      updatedAt: DateTime.now());
  }
  
  /// إضافة عقار تمت زيارته
  Client addVisitedEstate(String estateId) {
    final newVisitedEstates = List<String>.from(visitedEstates ?? []);
    
    if (!newVisitedEstates.contains(estateId)) {
      newVisitedEstates.add(estateId);
    }
    
    return copyWith(
      visitedEstates: newVisitedEstates,
      updatedAt: DateTime.now());
  }
  
  /// إضافة موعد
  Client addAppointment(String appointmentId) {
    final newAppointments = List<String>.from(appointments ?? []);
    
    if (!newAppointments.contains(appointmentId)) {
      newAppointments.add(appointmentId);
    }
    
    return copyWith(
      appointments: newAppointments,
      updatedAt: DateTime.now());
  }
  
  /// إضافة عرض
  Client addOffer(String offerId) {
    final newOffers = List<String>.from(offers ?? []);
    
    if (!newOffers.contains(offerId)) {
      newOffers.add(offerId);
    }
    
    return copyWith(
      offers: newOffers,
      updatedAt: DateTime.now());
  }
  
  /// إضافة عقار مباع/مؤجر
  Client addSoldRentedEstate(String estateId) {
    final newSoldRentedEstates = List<String>.from(soldRentedEstates ?? []);
    
    if (!newSoldRentedEstates.contains(estateId)) {
      newSoldRentedEstates.add(estateId);
    }
    
    return copyWith(
      soldRentedEstates: newSoldRentedEstates,
      updatedAt: DateTime.now());
  }

  @override
  List<Object?> get props => [
    id,
    ownerId,
    name,
    phoneNumber,
    email,
    image,
    status,
    type,
    notes,
    createdAt,
    updatedAt,
    lastContactDate,
    userId,
    searchCriteria,
    favoriteEstates,
    visitedEstates,
    appointments,
    offers,
    soldRentedEstates,
    budget,
    additionalInfo,
  ];
}
