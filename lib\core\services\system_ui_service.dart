import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';

/// خدمة إدارة شريطي النظام العلوي والسفلي
class SystemUIService {
  static SystemUIService? _instance;
  static SystemUIService get instance => _instance ??= SystemUIService._();

  SystemUIService._();

  // حالة إظهار/إخفاء شريطي النظام
  bool _isSystemUIVisible = true;

  // مؤقت للنقر المزدوج
  Timer? _doubleTapTimer;
  int _tapCount = 0;

  // مدة انتظار النقر المزدوج (بالميلي ثانية)
  static const int _doubleTapDelay = 300;

  /// الحصول على حالة إظهار شريطي النظام
  bool get isSystemUIVisible => _isSystemUIVisible;

  /// إخفاء شريطي النظام بشكل دائم مع إزالة الشريط الأسود نهائياً
  Future<void> hideSystemUI() async {
    try {
      // إخفاء شريطي النظام بشكل دائم
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: []);

      // إزالة الشريط الأسود نهائياً مع إعدادات شاملة
      await _removeBlackBar();

      _isSystemUIVisible = false;
      print('System UI permanently hidden with black bar removed');
    } catch (e) {
      print('Error hiding system UI: $e');
    }
  }

  /// إزالة الشريط الأسود نهائياً بطرق متعددة
  Future<void> _removeBlackBar() async {
    try {
      // الطريقة الأولى: إعدادات شاملة للشفافية
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarDividerColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarIconBrightness: Brightness.dark,
          systemNavigationBarContrastEnforced: false,
          systemStatusBarContrastEnforced: false,
        ),
      );

      // الطريقة الثانية: إعادة تطبيق الإعدادات بعد تأخير قصير
      await Future.delayed(const Duration(milliseconds: 100));

      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarDividerColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarIconBrightness: Brightness.dark,
          systemNavigationBarContrastEnforced: false,
          systemStatusBarContrastEnforced: false,
        ),
      );

      // الطريقة الثالثة: إعادة تطبيق وضع الإخفاء
      await Future.delayed(const Duration(milliseconds: 50));
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );

    } catch (e) {
      print('Error removing black bar: $e');
    }
  }

  /// إظهار شريطي النظام العلوي والسفلي
  Future<void> showSystemUI() async {
    try {
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [
          SystemUiOverlay.top,
          SystemUiOverlay.bottom,
        ]);
      _isSystemUIVisible = true;
      print('System UI shown');
    } catch (e) {
      print('Error showing system UI: $e');
    }
  }

  /// تبديل حالة إظهار/إخفاء شريطي النظام
  Future<void> toggleSystemUI() async {
    if (_isSystemUIVisible) {
      await hideSystemUI();
    } else {
      await showSystemUI();
    }
  }

  /// معالجة النقر المزدوج - معطل لإبقاء الشريطين مخفيين دائماً
  void handleDoubleTap() {
    // تم تعطيل وظيفة النقر المزدوج للحفاظ على إخفاء الشريطين
    // لا يتم تنفيذ أي إجراء
    print('Double tap detected but system UI toggle is disabled');
  }

  /// إعادة تعيين عداد النقرات
  void resetTapCount() {
    _doubleTapTimer?.cancel();
    _tapCount = 0;
  }

  /// تطبيق إعدادات شريطي النظام الافتراضية الاحترافية - إخفاء دائم
  Future<void> applyDefaultSystemUI() async {
    try {
      // إخفاء شريطي النظام بشكل دائم مع الحفاظ على المساحة الآمنة
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: []);

      // إزالة الشريط الأسود نهائياً
      await _removeBlackBar();

      _isSystemUIVisible = false;
      print('System UI permanently hidden with black bar removed');
    } catch (e) {
      print('Error applying default system UI: $e');
    }
  }

  /// تطبيق إعدادات شريطي النظام للوضع الداكن
  Future<void> applyDarkSystemUI() async {
    try {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          // شريط الحالة
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,

          // شريط التنقل
          systemNavigationBarColor: Colors.black,
          systemNavigationBarIconBrightness: Brightness.light,
          systemNavigationBarDividerColor: Colors.grey));
    } catch (e) {
      print('Error applying dark system UI: $e');
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _doubleTapTimer?.cancel();
    _tapCount = 0;
  }
}

/// Widget لالتقاط النقر المزدوج على الشاشة
class DoubleTapSystemUIDetector extends StatelessWidget {
  final Widget child;
  final bool enabled;

  const DoubleTapSystemUIDetector({
    super.key,
    required this.child,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!enabled) {
      return child;
    }

    return GestureDetector(
      onTap: () {
        SystemUIService.instance.handleDoubleTap();
      },
      behavior: HitTestBehavior.translucent,
      child: child);
  }
}

/// Widget لعرض مؤشر حالة شريطي النظام
class SystemUIStatusIndicator extends StatefulWidget {
  final Duration displayDuration;
  final bool showOnToggle;

  const SystemUIStatusIndicator({
    super.key,
    this.displayDuration = const Duration(seconds: 2),
    this.showOnToggle = true,
  });

  @override
  State<SystemUIStatusIndicator> createState() => _SystemUIStatusIndicatorState();
}

class _SystemUIStatusIndicatorState extends State<SystemUIStatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isVisible = false;
  Timer? _hideTimer;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this);
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _hideTimer?.cancel();
    super.dispose();
  }

  void _showIndicator() {
    if (!widget.showOnToggle) return;

    setState(() {
      _isVisible = true;
    });

    _animationController.forward();

    _hideTimer?.cancel();
    _hideTimer = Timer(widget.displayDuration, () {
      _animationController.reverse().then((_) {
        if (mounted) {
          setState(() {
            _isVisible = false;
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    // مراقبة تغيير حالة شريطي النظام
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.showOnToggle) {
        _showIndicator();
      }
    });

    if (!_isVisible) {
      return const SizedBox.shrink();
    }

    return Positioned(
      top: MediaQuery.of(context).size.height * 0.1,
      left: 20,
      right: 20,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(20)),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                SystemUIService.instance.isSystemUIVisible
                    ? Icons.visibility
                    : Icons.visibility_off,
                color: Colors.white,
                size: 16),
              const SizedBox(width: 8),
              Text(
                SystemUIService.instance.isSystemUIVisible
                    ? 'شريطا النظام مرئيان'
                    : 'شريطا النظام مخفيان',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500)),
            ]))));
  }
}
