/// ثوابت أنواع المستخدمين الموحدة في التطبيق
/// يجب استخدام هذه الثوابت في جميع أنحاء التطبيق لضمان التوافق
library;

class UserTypeConstants {
  /// باحث عن عقار
  static const String seeker = 'seeker';

  /// وسيط عقاري/مستثمر
  static const String agent = 'agent';

  /// مالك عقار
  static const String owner = 'owner';

  /// شركة عقارية
  static const String company = 'company';

  /// قائمة جميع أنواع المستخدمين المدعومة
  static const List<String> allTypes = [
    seeker,
    agent,
    owner,
    company,
  ];

  /// الحصول على اسم نوع المستخدم بالعربية
  static String getArabicName(String userType) {
    switch (userType) {
      case seeker:
        return 'باحث عن عقار';
      case agent:
        return 'مستثمر';
      case owner:
        return 'مالك عقار';
      case company:
        return 'شركة عقارية';
      default:
        return 'باحث عن عقار';
    }
  }

  /// التحقق من صحة نوع المستخدم
  static bool isValidUserType(String userType) {
    return allTypes.contains(userType);
  }

  /// تطبيع نوع المستخدم (تحويل القيم القديمة إلى الجديدة)
  static String normalizeUserType(String userType) {
    switch (userType.toLowerCase().trim()) {
      // الباحثون عن العقارات
      case 'seeker':
      case 'user':
      case 'regular':
      case 'property_seeker':
      case 'باحث عن عقار':
        return seeker;

      // الوكلاء العقاريون/المستثمرون
      case 'agent':
      case 'investor':
      case 'وسيط عقاري':
      case 'وكيل':
      case 'مستثمر':
        return agent;

      // مالكو العقارات
      case 'owner':
      case 'مالك عقار':
      case 'مالك':
      case 'property_owner':
        return owner;

      // الشركات العقارية
      case 'company':
      case 'شركة عقارية':
      case 'شركة':
      case 'real_estate_company':
        return company;

      default:
        return seeker; // افتراضي
    }
  }

  /// التحقق من إمكانية إضافة إعلانات
  static bool canPostAds(String userType) {
    return userType != seeker;
  }

  /// التحقق من إمكانية الوصول للمفضلة
  static bool canAccessFavorites(String userType) {
    return userType != company;
  }

  /// التحقق من إمكانية الوصول للمقارنة
  static bool canAccessComparison(String userType) {
    return userType != company;
  }

  /// التحقق من إمكانية إدارة العملاء
  static bool canManageClients(String userType) {
    return userType == agent || userType == company;
  }

  /// التحقق من إمكانية تحليل السوق
  static bool canAccessMarketAnalysis(String userType) {
    return userType == agent || userType == company;
  }

  /// التحقق من إمكانية نسخ العقارات
  static bool canCopyProperties(String userType) {
    return userType == agent;
  }

  /// التحقق من إمكانية إدارة المشاريع
  static bool canManageProjects(String userType) {
    return userType == company;
  }

  /// التحقق من إمكانية إدارة فريق العمل
  static bool canManageTeam(String userType) {
    return userType == company;
  }

  /// التحقق من إمكانية الوصول للتقارير
  static bool canAccessReports(String userType) {
    return userType == company;
  }

  /// التحقق من إمكانية رؤية طلبات العقارات (للمستثمرين والمالكين والشركات فقط)
  static bool canViewPropertyRequests(String userType) {
    return userType != seeker;
  }

  /// التحقق من إمكانية إنشاء طلبات العقارات (للباحثين فقط)
  static bool canCreatePropertyRequests(String userType) {
    return userType == seeker;
  }
}
