import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:kuwait_corners/core/services/realtime_notification_service.dart';

/// مكون عرض الإشعارات المباشرة
class RealtimeNotificationsWidget extends StatefulWidget {
  /// عدد الإشعارات المعروضة
  final int limit;

  /// ما إذا كان يجب عرض زر تعليم الكل كمقروء
  final bool showMarkAllAsReadButton;

  /// ما إذا كان يجب عرض زر حذف الإشعارات
  final bool showDeleteButton;

  /// دالة يتم استدعاؤها عند النقر على إشعار
  final Function(RealtimeNotificationModel)? onNotificationTap;

  const RealtimeNotificationsWidget({
    super.key,
    this.limit = 10,
    this.showMarkAllAsReadButton = true,
    this.showDeleteButton = true,
    this.onNotificationTap,
  });

  @override
  State<RealtimeNotificationsWidget> createState() =>
      _RealtimeNotificationsWidgetState();
}

class _RealtimeNotificationsWidgetState
    extends State<RealtimeNotificationsWidget> {
  final RealtimeNotificationService _notificationService =
      RealtimeNotificationService();

  // حالة المكون
  bool _isLoading = true;
  String? _errorMessage;
  List<RealtimeNotificationModel> _notifications = [];

  // اشتراك في تدفق الإشعارات
  StreamSubscription<RealtimeNotificationModel>? _subscription;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
    _subscribeToNotifications();
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  /// تحميل الإشعارات
  Future<void> _loadNotifications() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final notifications = await _notificationService.getNotifications(
        limit: widget.limit);

      setState(() {
        _notifications = notifications;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل الإشعارات';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// الاشتراك في تدفق الإشعارات
  void _subscribeToNotifications() {
    final stream = _notificationService.notificationsStream;
    if (stream == null) {
      return;
    }

    _subscription = stream.listen((notification) {
      setState(() {
        // إضافة الإشعار الجديد في بداية القائمة
        _notifications.insert(0, notification);

        // الحفاظ على عدد الإشعارات ضمن الحد
        if (_notifications.length > widget.limit) {
          _notifications = _notifications.sublist(0, widget.limit);
        }
      });
    });
  }

  /// تعليم إشعار كمقروء
  Future<void> _markAsRead(String notificationId) async {
    try {
      final success = await _notificationService.markAsRead(notificationId);

      if (success) {
        setState(() {
          final index =
              _notifications.indexWhere((n) => n.id == notificationId);
          if (index != -1) {
            final notification = _notifications[index];
            _notifications[index] = RealtimeNotificationModel(
              id: notification.id,
              recipientId: notification.recipientId,
              senderId: notification.senderId,
              senderName: notification.senderName,
              senderPhotoUrl: notification.senderPhotoUrl,
              type: notification.type,
              title: notification.title,
              body: notification.body,
              data: notification.data,
              timestamp: notification.timestamp,
              isRead: true);
          }
        });
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// تعليم جميع الإشعارات كمقروءة
  Future<void> _markAllAsRead() async {
    try {
      final success = await _notificationService.markAllAsRead();

      if (success) {
        setState(() {
          _notifications = _notifications.map((notification) {
            return RealtimeNotificationModel(
              id: notification.id,
              recipientId: notification.recipientId,
              senderId: notification.senderId,
              senderName: notification.senderName,
              senderPhotoUrl: notification.senderPhotoUrl,
              type: notification.type,
              title: notification.title,
              body: notification.body,
              data: notification.data,
              timestamp: notification.timestamp,
              isRead: true);
          }).toList();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تعليم جميع الإشعارات كمقروءة')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('حدث خطأ أثناء تعليم الإشعارات كمقروءة')));
    }
  }

  /// حذف إشعار
  Future<void> _deleteNotification(String notificationId) async {
    try {
      final success =
          await _notificationService.deleteNotification(notificationId);

      if (success) {
        setState(() {
          _notifications.removeWhere((n) => n.id == notificationId);
        });
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// حذف جميع الإشعارات
  Future<void> _deleteAllNotifications() async {
    // عرض مربع حوار التأكيد
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف جميع الإشعارات'),
        content: const Text('هل أنت متأكد من حذف جميع الإشعارات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء')),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red),
            child: const Text('حذف')),
        ]));

    if (confirmed != true) {
      return;
    }

    try {
      final success = await _notificationService.deleteAllNotifications();

      if (success) {
        setState(() {
          _notifications = [];
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف جميع الإشعارات')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('حدث خطأ أثناء حذف الإشعارات')));
    }
  }

  /// الحصول على أيقونة الإشعار
  IconData _getNotificationIcon(RealtimeNotificationType type) {
    switch (type) {
      case RealtimeNotificationType.newMessage:
        return Icons.message;
      case RealtimeNotificationType.estateStatusUpdate:
        return Icons.home_work;
      case RealtimeNotificationType.estatePriceChange:
        return Icons.attach_money;
      case RealtimeNotificationType.newComment:
        return Icons.comment;
      case RealtimeNotificationType.newReply:
        return Icons.reply;
      case RealtimeNotificationType.newRating:
        return Icons.star;
      case RealtimeNotificationType.requestStatusUpdate:
        return Icons.update;
      default:
        return Icons.notifications;
    }
  }

  /// الحصول على لون الإشعار
  Color _getNotificationColor(RealtimeNotificationType type) {
    switch (type) {
      case RealtimeNotificationType.newMessage:
        return Colors.blue;
      case RealtimeNotificationType.estateStatusUpdate:
        return Colors.green;
      case RealtimeNotificationType.estatePriceChange:
        return Colors.orange;
      case RealtimeNotificationType.newComment:
        return Colors.purple;
      case RealtimeNotificationType.newReply:
        return Colors.indigo;
      case RealtimeNotificationType.newRating:
        return Colors.amber;
      case RealtimeNotificationType.requestStatusUpdate:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  /// تنسيق وقت الإشعار
  String _formatNotificationTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 7) {
      return DateFormat('yyyy/MM/dd').format(time);
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  @override
  Widget build(BuildContext context) {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : _errorMessage != null
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 48),
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red)),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadNotifications,
                      child: const Text('إعادة المحاولة')),
                  ]))
            : Column(
                children: [
                  // أزرار التحكم
                  if (widget.showMarkAllAsReadButton || widget.showDeleteButton)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (widget.showMarkAllAsReadButton)
                            TextButton.icon(
                              onPressed: _notifications.any((n) => !n.isRead)
                                  ? _markAllAsRead
                                  : null,
                              icon: const Icon(Icons.done_all, size: 16),
                              label: const Text('تعليم الكل كمقروء'))
                          else
                            const SizedBox(),
                          if (widget.showDeleteButton)
                            TextButton.icon(
                              onPressed: _notifications.isNotEmpty
                                  ? _deleteAllNotifications
                                  : null,
                              icon: const Icon(Icons.delete_sweep,
                                  size: 16, color: Colors.red),
                              label: const Text('حذف الكل',
                                  style: TextStyle(color: Colors.red)))
                          else
                            const SizedBox(),
                        ])),

                  // قائمة الإشعارات
                  Expanded(
                    child: _notifications.isEmpty
                        ? _buildEmptyNotifications()
                        : RefreshIndicator(
                            onRefresh: _loadNotifications,
                            child: ListView.separated(
                              padding: const EdgeInsets.all(8),
                              itemCount: _notifications.length,
                              separatorBuilder: (context, index) =>
                                  const Divider(height: 1),
                              itemBuilder: (context, index) {
                                final notification = _notifications[index];

                                return Dismissible(
                                  key: Key(notification.id),
                                  direction: DismissDirection.endToStart,
                                  background: Container(
                                    alignment: Alignment.centerRight,
                                    padding: const EdgeInsets.only(right: 16),
                                    color: Colors.red,
                                    child: const Icon(
                                      Icons.delete,
                                      color: Colors.white)),
                                  onDismissed: (direction) {
                                    _deleteNotification(notification.id);
                                  },
                                  child: _buildNotificationItem(notification));
                              }))),
                ]);
  }

  /// بناء عنصر إشعار
  Widget _buildNotificationItem(RealtimeNotificationModel notification) {
    final formattedTime = _formatNotificationTime(notification.timestamp);

    return InkWell(
      onTap: () {
        // تعليم الإشعار كمقروء
        if (!notification.isRead) {
          _markAsRead(notification.id);
        }

        // استدعاء دالة النقر
        if (widget.onNotificationTap != null) {
          widget.onNotificationTap!(notification);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        color: notification.isRead ? null : Colors.blue.shade50,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // أيقونة الإشعار
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color:
                    _getNotificationColor(notification.type).withValues(alpha: 0.1),
                shape: BoxShape.circle),
              child: Icon(
                _getNotificationIcon(notification.type),
                color: _getNotificationColor(notification.type),
                size: 20)),

            const SizedBox(width: 12),

            // محتوى الإشعار
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الإشعار
                  Text(
                    notification.title,
                    style: TextStyle(
                      fontWeight: notification.isRead
                          ? FontWeight.normal
                          : FontWeight.bold,
                      fontSize: 16)),

                  const SizedBox(height: 4),

                  // نص الإشعار
                  Text(
                    notification.body,
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14)),

                  const SizedBox(height: 4),

                  // معلومات المرسل والوقت
                  Row(
                    children: [
                      if (notification.senderName != null) ...[
                        Text(
                          notification.senderName!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                            fontWeight: FontWeight.bold)),
                        const SizedBox(width: 8),
                        Text(
                          '•',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500)),
                        const SizedBox(width: 8),
                      ],
                      Icon(
                        Icons.access_time,
                        size: 12,
                        color: Colors.grey.shade500),
                      const SizedBox(width: 4),
                      Text(
                        formattedTime,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade500)),
                    ]),
                ])),

            // زر تعليم كمقروء/غير مقروء
            if (!notification.isRead)
              IconButton(
                onPressed: () => _markAsRead(notification.id),
                icon: const Icon(Icons.done),
                tooltip: 'تعليم كمقروء',
                iconSize: 20,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints()),
          ])));
  }

  /// بناء عرض عدم وجود إشعارات
  Widget _buildEmptyNotifications() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off,
            size: 64,
            color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600)),
          const SizedBox(height: 8),
          Text(
            'ستظهر الإشعارات الجديدة هنا',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600)),
        ]));
  }
}

/// مكون زر الإشعارات مع عداد
class NotificationBadge extends StatefulWidget {
  /// أيقونة الزر
  final IconData icon;

  /// حجم الأيقونة
  final double iconSize;

  /// لون الأيقونة
  final Color? iconColor;

  /// لون الشارة
  final Color badgeColor;

  /// دالة يتم استدعاؤها عند النقر على الزر
  final VoidCallback? onPressed;

  const NotificationBadge({
    super.key,
    this.icon = Icons.notifications,
    this.iconSize = 24,
    this.iconColor,
    this.badgeColor = Colors.red,
    this.onPressed,
  });

  @override
  State<NotificationBadge> createState() => _NotificationBadgeState();
}

class _NotificationBadgeState extends State<NotificationBadge> {
  final RealtimeNotificationService _notificationService =
      RealtimeNotificationService();

  // عدد الإشعارات غير المقروءة
  int _unreadCount = 0;

  // اشتراك في تدفق الإشعارات
  StreamSubscription<RealtimeNotificationModel>? _subscription;

  @override
  void initState() {
    super.initState();
    _updateUnreadCount();
    _subscribeToNotifications();
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  /// تحديث عدد الإشعارات غير المقروءة
  void _updateUnreadCount() {
    setState(() {
      _unreadCount = _notificationService.unreadCount;
    });
  }

  /// الاشتراك في تدفق الإشعارات
  void _subscribeToNotifications() {
    final stream = _notificationService.notificationsStream;
    if (stream == null) {
      return;
    }

    _subscription = stream.listen((notification) {
      if (!notification.isRead) {
        setState(() {
          _unreadCount = _notificationService.unreadCount;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        IconButton(
          icon: Icon(
            widget.icon,
            size: widget.iconSize,
            color: widget.iconColor),
          onPressed: widget.onPressed,
          tooltip: 'الإشعارات'),
        if (_unreadCount > 0)
          Positioned(
            top: 4,
            right: 4,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: widget.badgeColor,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: widget.badgeColor.withValues(alpha: 0.3),
                    blurRadius: 4,
                    spreadRadius: 1),
                ]),
              constraints: const BoxConstraints(
                minWidth: 18,
                minHeight: 18),
              child: Center(
                child: Text(
                  _unreadCount > 9 ? '9+' : '$_unreadCount',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold))))),
      ]);
  }
}
