// file: lib/presentation/widgets/banner_widget.dart

import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class BannerWidget extends StatelessWidget {
  /// رابط البانر الذي يتم جلبه من Firebase أو إعدادات الإدارة.
  final String? bannerUrl;

  const BannerWidget({super.key, this.bannerUrl});

  @override
  Widget build(BuildContext context) {
    // التحقق من صحة الرابط إذا كان موجوداً وينتهي بصيغة png أو gif.
    if (bannerUrl != null &&
        (bannerUrl!.toLowerCase().endsWith('.png') ||
            bannerUrl!.toLowerCase().endsWith('.gif'))) {
      return Image.network(
        bannerUrl!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          // في حال حدوث خطأ أثناء تحميل الصورة، يتم عرض تأثير shimmer.
          return _buildShimmer();
        });
    } else {
      // إذا لم يتوفر رابط صالح، يتم عرض منطقة shimmer كـ placeholder.
      return _buildShimmer();
    }
  }

  Widget _buildShimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        height: 150, // يمكن تعديل الارتفاع وفق التصميم
        width: double.infinity,
        color: Colors.white));
  }
}
