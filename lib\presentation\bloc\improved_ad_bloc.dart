// lib/presentation/bloc/improved_ad_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:io';

import '../../domain/entities/estate.dart';
import '../../domain/repositories/estate_repository.dart';
import '../../domain/usecases/create_estate_new.dart';

// Events
abstract class ImprovedAdEvent extends Equatable {
  const ImprovedAdEvent();
  @override
  List<Object?> get props => [];
}

class SetMainCategory extends ImprovedAdEvent {
  final String mainCategory;
  const SetMainCategory(this.mainCategory);
  @override
  List<Object?> get props => [mainCategory];
}

class SetSubCategory extends ImprovedAdEvent {
  final String subCategory;
  const SetSubCategory(this.subCategory);
  @override
  List<Object?> get props => [subCategory];
}

class SetUsageType extends ImprovedAdEvent {
  final String usageType;
  const SetUsageType(this.usageType);
  @override
  List<Object?> get props => [usageType];
}

class AddImages extends ImprovedAdEvent {
  final List<String> imagePaths;
  const AddImages(this.imagePaths);
  @override
  List<Object?> get props => [imagePaths];
}

class SetBasicDetails extends ImprovedAdEvent {
  final String title;
  final double price;
  final String governorate;
  final String city;
  final int piece;
  final String description;

  const SetBasicDetails({
    required this.title,
    required this.price,
    required this.governorate,
    required this.city,
    required this.piece,
    required this.description,
  });

  @override
  List<Object?> get props => [title, price, governorate, city, piece, description];
}

class SetInternalDetails extends ImprovedAdEvent {
  final String? rebound;
  final int? numberOfRooms;
  final String? internalLocation;
  final String? salon;
  final double? area;
  final int? floorNumber;
  final int? numberOfBathrooms;
  final int? buildingAge;
  final int? numberOfFloors;
  final String? propertyType;

  const SetInternalDetails({
    this.rebound,
    this.numberOfRooms,
    this.internalLocation,
    this.salon,
    this.area,
    this.floorNumber,
    this.numberOfBathrooms,
    this.buildingAge,
    this.numberOfFloors,
    this.propertyType,
  });

  @override
  List<Object?> get props => [
    rebound, numberOfRooms, internalLocation, salon, area,
    floorNumber, numberOfBathrooms, buildingAge, numberOfFloors, propertyType
  ];
}

class SetExtraFeatures extends ImprovedAdEvent {
  final bool autoRepublish;
  final bool kuwaitCornersPin;
  final bool movingAd;
  final bool vipBadge;
  final bool pinnedOnHome;
  final String? discountCode;
  final bool hasGarage;
  final bool hasCentralAC;
  final bool hasMaidRoom;
  final bool isFullyFurnished;
  final bool hasSecurity;
  final bool allowPets;
  final bool hasElevator;
  final bool hasSwimmingPool;
  final bool hasBalcony;

  const SetExtraFeatures({
    required this.autoRepublish,
    required this.kuwaitCornersPin,
    required this.movingAd,
    required this.vipBadge,
    required this.pinnedOnHome,
    this.discountCode,
    required this.hasGarage,
    required this.hasCentralAC,
    required this.hasMaidRoom,
    required this.isFullyFurnished,
    required this.hasSecurity,
    required this.allowPets,
    required this.hasElevator,
    required this.hasSwimmingPool,
    required this.hasBalcony,
  });

  @override
  List<Object?> get props => [
    autoRepublish, kuwaitCornersPin, movingAd, vipBadge, pinnedOnHome,
    discountCode, hasGarage, hasCentralAC, hasMaidRoom, isFullyFurnished,
    hasSecurity, allowPets, hasElevator, hasSwimmingPool, hasBalcony
  ];
}

class SetAdSettings extends ImprovedAdEvent {
  final String userType;
  final bool hidePhone;
  final List<String> extraPhones;
  final bool shareLocation;
  final double? lat;
  final double? lng;

  const SetAdSettings({
    required this.userType,
    required this.hidePhone,
    required this.extraPhones,
    required this.shareLocation,
    this.lat,
    this.lng,
  });

  @override
  List<Object?> get props => [userType, hidePhone, extraPhones, shareLocation, lat, lng];
}

class SetSelectedPlan extends ImprovedAdEvent {
  final String planId;
  final double planPrice;
  final int days;

  const SetSelectedPlan({
    required this.planId,
    required this.planPrice,
    required this.days,
  });

  @override
  List<Object?> get props => [planId, planPrice, days];
}

class SubmitAd extends ImprovedAdEvent {}

class UpdateEstate extends ImprovedAdEvent {
  final String estateId;
  const UpdateEstate(this.estateId);
  @override
  List<Object?> get props => [estateId];
}

// State
class ImprovedAdState extends Equatable {
  final String mainCategory;
  final String subCategory;
  final String usageType;
  final List<String> imagePaths;
  final String title;
  final double price;
  final String governorate;
  final String city;
  final int piece;
  final String description;
  final String userType;
  final bool hidePhone;
  final List<String> extraPhones;
  final bool shareLocation;
  final double? lat;
  final double? lng;
  final String planId;
  final double planPrice;
  final int planDays;
  final bool autoRepublish;
  final bool kuwaitCornersPin;
  final bool movingAd;
  final bool vipBadge;
  final bool pinnedOnHome;
  final String? discountCode;
  final bool hasGarage;
  final bool hasCentralAC;
  final bool hasMaidRoom;
  final bool isFullyFurnished;
  final bool hasSecurity;
  final bool allowPets;
  final bool hasElevator;
  final bool hasSwimmingPool;
  final bool hasBalcony;
  final String? internalRebound;
  final int? internalNumberOfRooms;
  final String? internalLocation;
  final String? internalSalon;
  final double? internalArea;
  final int? internalFloorNumber;
  final int? internalNumberOfBathrooms;
  final int? internalBuildingAge;
  final int? internalNumberOfFloors;
  final String? internalPropertyType;
  final bool isSubmitting;
  final bool isSuccess;
  final String? errorMessage;
  final String? createdEstateId;

  const ImprovedAdState({
    this.mainCategory = '',
    this.subCategory = '',
    this.usageType = '',
    this.imagePaths = const [],
    this.title = '',
    this.price = 0.0,
    this.governorate = '',
    this.city = '',
    this.piece = 1,
    this.description = '',
    this.userType = '',
    this.hidePhone = false,
    this.extraPhones = const [],
    this.shareLocation = false,
    this.lat,
    this.lng,
    this.planId = '',
    this.planPrice = 0.0,
    this.planDays = 30,
    this.autoRepublish = false,
    this.kuwaitCornersPin = false,
    this.movingAd = false,
    this.vipBadge = false,
    this.pinnedOnHome = false,
    this.discountCode,
    this.hasGarage = false,
    this.hasCentralAC = false,
    this.hasMaidRoom = false,
    this.isFullyFurnished = false,
    this.hasSecurity = false,
    this.allowPets = false,
    this.hasElevator = false,
    this.hasSwimmingPool = false,
    this.hasBalcony = false,
    this.internalRebound,
    this.internalNumberOfRooms,
    this.internalLocation,
    this.internalSalon,
    this.internalArea,
    this.internalFloorNumber,
    this.internalNumberOfBathrooms,
    this.internalBuildingAge,
    this.internalNumberOfFloors,
    this.internalPropertyType,
    this.isSubmitting = false,
    this.isSuccess = false,
    this.errorMessage,
    this.createdEstateId,
  });

  ImprovedAdState copyWith({
    String? mainCategory,
    String? subCategory,
    String? usageType,
    List<String>? imagePaths,
    String? title,
    double? price,
    String? governorate,
    String? city,
    int? piece,
    String? description,
    String? userType,
    bool? hidePhone,
    List<String>? extraPhones,
    bool? shareLocation,
    double? lat,
    double? lng,
    String? planId,
    double? planPrice,
    int? planDays,
    bool? autoRepublish,
    bool? kuwaitCornersPin,
    bool? movingAd,
    bool? vipBadge,
    bool? pinnedOnHome,
    String? discountCode,
    bool? hasGarage,
    bool? hasCentralAC,
    bool? hasMaidRoom,
    bool? isFullyFurnished,
    bool? hasSecurity,
    bool? allowPets,
    bool? hasElevator,
    bool? hasSwimmingPool,
    bool? hasBalcony,
    String? internalRebound,
    int? internalNumberOfRooms,
    String? internalLocation,
    String? internalSalon,
    double? internalArea,
    int? internalFloorNumber,
    int? internalNumberOfBathrooms,
    int? internalBuildingAge,
    int? internalNumberOfFloors,
    String? internalPropertyType,
    bool? isSubmitting,
    bool? isSuccess,
    String? errorMessage,
    String? createdEstateId,
  }) {
    return ImprovedAdState(
      mainCategory: mainCategory ?? this.mainCategory,
      subCategory: subCategory ?? this.subCategory,
      usageType: usageType ?? this.usageType,
      imagePaths: imagePaths ?? this.imagePaths,
      title: title ?? this.title,
      price: price ?? this.price,
      governorate: governorate ?? this.governorate,
      city: city ?? this.city,
      piece: piece ?? this.piece,
      description: description ?? this.description,
      userType: userType ?? this.userType,
      hidePhone: hidePhone ?? this.hidePhone,
      extraPhones: extraPhones ?? this.extraPhones,
      shareLocation: shareLocation ?? this.shareLocation,
      lat: lat ?? this.lat,
      lng: lng ?? this.lng,
      planId: planId ?? this.planId,
      planPrice: planPrice ?? this.planPrice,
      planDays: planDays ?? this.planDays,
      autoRepublish: autoRepublish ?? this.autoRepublish,
      kuwaitCornersPin: kuwaitCornersPin ?? this.kuwaitCornersPin,
      movingAd: movingAd ?? this.movingAd,
      vipBadge: vipBadge ?? this.vipBadge,
      pinnedOnHome: pinnedOnHome ?? this.pinnedOnHome,
      discountCode: discountCode ?? this.discountCode,
      hasGarage: hasGarage ?? this.hasGarage,
      hasCentralAC: hasCentralAC ?? this.hasCentralAC,
      hasMaidRoom: hasMaidRoom ?? this.hasMaidRoom,
      isFullyFurnished: isFullyFurnished ?? this.isFullyFurnished,
      hasSecurity: hasSecurity ?? this.hasSecurity,
      allowPets: allowPets ?? this.allowPets,
      hasElevator: hasElevator ?? this.hasElevator,
      hasSwimmingPool: hasSwimmingPool ?? this.hasSwimmingPool,
      hasBalcony: hasBalcony ?? this.hasBalcony,
      internalRebound: internalRebound ?? this.internalRebound,
      internalNumberOfRooms: internalNumberOfRooms ?? this.internalNumberOfRooms,
      internalLocation: internalLocation ?? this.internalLocation,
      internalSalon: internalSalon ?? this.internalSalon,
      internalArea: internalArea ?? this.internalArea,
      internalFloorNumber: internalFloorNumber ?? this.internalFloorNumber,
      internalNumberOfBathrooms: internalNumberOfBathrooms ?? this.internalNumberOfBathrooms,
      internalBuildingAge: internalBuildingAge ?? this.internalBuildingAge,
      internalNumberOfFloors: internalNumberOfFloors ?? this.internalNumberOfFloors,
      internalPropertyType: internalPropertyType ?? this.internalPropertyType,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isSuccess: isSuccess ?? this.isSuccess,
      errorMessage: errorMessage,
      createdEstateId: createdEstateId ?? this.createdEstateId);
  }

  @override
  List<Object?> get props => [
    mainCategory, subCategory, usageType, imagePaths, title, price,
    governorate, city, piece, description, userType, hidePhone,
    extraPhones, shareLocation, lat, lng, planId, planPrice, planDays,
    autoRepublish, kuwaitCornersPin, movingAd, vipBadge, pinnedOnHome,
    discountCode, hasGarage, hasCentralAC, hasMaidRoom, isFullyFurnished,
    hasSecurity, allowPets, hasElevator, hasSwimmingPool, hasBalcony,
    internalRebound, internalNumberOfRooms, internalLocation, internalSalon,
    internalArea, internalFloorNumber, internalNumberOfBathrooms,
    internalBuildingAge, internalNumberOfFloors, internalPropertyType,
    isSubmitting, isSuccess, errorMessage, createdEstateId
  ];
}

// BLoC
class ImprovedAdBloc extends Bloc<ImprovedAdEvent, ImprovedAdState> {
  final CreateEstateNew createEstate;
  final EstateRepository estateRepository;

  ImprovedAdBloc({
    required this.createEstate,
    required this.estateRepository,
  }) : super(const ImprovedAdState()) {
    on<SetMainCategory>(_onSetMainCategory);
    on<SetSubCategory>(_onSetSubCategory);
    on<SetUsageType>(_onSetUsageType);
    on<AddImages>(_onAddImages);
    on<SetBasicDetails>(_onSetBasicDetails);
    on<SetInternalDetails>(_onSetInternalDetails);
    on<SetExtraFeatures>(_onSetExtraFeatures);
    on<SetAdSettings>(_onSetAdSettings);
    on<SetSelectedPlan>(_onSetSelectedPlan);
    on<SubmitAd>(_onSubmitAd);
    on<UpdateEstate>(_onUpdateEstate);
  }

  void _onSetMainCategory(SetMainCategory event, Emitter<ImprovedAdState> emit) {
    emit(state.copyWith(mainCategory: event.mainCategory));
  }

  void _onSetSubCategory(SetSubCategory event, Emitter<ImprovedAdState> emit) {
    emit(state.copyWith(subCategory: event.subCategory));
  }

  void _onSetUsageType(SetUsageType event, Emitter<ImprovedAdState> emit) {
    emit(state.copyWith(usageType: event.usageType));
  }

  void _onAddImages(AddImages event, Emitter<ImprovedAdState> emit) {
    final merged = [...state.imagePaths, ...event.imagePaths];
    emit(state.copyWith(imagePaths: merged));
  }

  void _onSetBasicDetails(SetBasicDetails event, Emitter<ImprovedAdState> emit) {
    emit(state.copyWith(
      title: event.title,
      price: event.price,
      governorate: event.governorate,
      city: event.city,
      piece: event.piece,
      description: event.description,
      errorMessage: null));
  }

  void _onSetInternalDetails(SetInternalDetails event, Emitter<ImprovedAdState> emit) {
    emit(state.copyWith(
      internalRebound: event.rebound,
      internalNumberOfRooms: event.numberOfRooms,
      internalLocation: event.internalLocation,
      internalSalon: event.salon,
      internalArea: event.area,
      internalFloorNumber: event.floorNumber,
      internalNumberOfBathrooms: event.numberOfBathrooms,
      internalBuildingAge: event.buildingAge,
      internalNumberOfFloors: event.numberOfFloors,
      internalPropertyType: event.propertyType));
  }

  void _onSetExtraFeatures(SetExtraFeatures event, Emitter<ImprovedAdState> emit) {
    emit(state.copyWith(
      autoRepublish: event.autoRepublish,
      kuwaitCornersPin: event.kuwaitCornersPin,
      movingAd: event.movingAd,
      vipBadge: event.vipBadge,
      pinnedOnHome: event.pinnedOnHome,
      discountCode: event.discountCode,
      hasGarage: event.hasGarage,
      hasCentralAC: event.hasCentralAC,
      hasMaidRoom: event.hasMaidRoom,
      isFullyFurnished: event.isFullyFurnished,
      hasSecurity: event.hasSecurity,
      allowPets: event.allowPets,
      hasElevator: event.hasElevator,
      hasSwimmingPool: event.hasSwimmingPool,
      hasBalcony: event.hasBalcony));
  }

  void _onSetAdSettings(SetAdSettings event, Emitter<ImprovedAdState> emit) {
    emit(state.copyWith(
      userType: event.userType,
      hidePhone: event.hidePhone,
      extraPhones: event.extraPhones,
      shareLocation: event.shareLocation,
      lat: event.lat,
      lng: event.lng,
      errorMessage: null));
  }

  void _onSetSelectedPlan(SetSelectedPlan event, Emitter<ImprovedAdState> emit) {
    emit(state.copyWith(
      planId: event.planId,
      planPrice: event.planPrice,
      planDays: event.days));
  }

  Future<void> _onSubmitAd(SubmitAd event, Emitter<ImprovedAdState> emit) async {
    try {
      print('🚀 بدء عملية إنشاء الإعلان...');
      emit(state.copyWith(isSubmitting: true, errorMessage: null));

      // رفع الصور والحصول على الروابط
      print('📸 رفع الصور... عدد الصور: ${state.imagePaths.length}');
      final imageFiles = state.imagePaths.map((path) => File(path)).toList();
      final downloadUrls = await estateRepository.uploadImages(imageFiles);
      print('✅ تم رفع الصور بنجاح. الروابط: $downloadUrls');

      // تحديد التواريخ بناءً على مدة الباقة
      final now = DateTime.now();
      final duration = Duration(days: state.planDays);
      final featureStartDate = now;
      final featureEndDate = now.add(duration);

      // الحصول على معرف المستخدم الحالي
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) {
        throw Exception("المستخدم غير مسجل الدخول");
      }
      print('👤 معرف المستخدم: $userId');

      // إنشاء كائن Estate
      print('🏠 إنشاء كائن العقار...');
      final estate = Estate(
        id: '',
        title: state.title,
        description: state.description,
        price: state.price,
        location: "${state.governorate} - ${state.city} - ق.${state.piece}",
        photoUrls: downloadUrls,
        isFeatured: true,
        planType: state.planId,
        startDate: featureStartDate,
        endDate: featureEndDate,
        createdAt: now,
        mainCategory: state.mainCategory,
        subCategory: state.subCategory,
        postedByUserType: state.userType,
        hidePhone: state.hidePhone,
        extraPhones: state.extraPhones,
        shareLocation: state.shareLocation,
        lat: state.lat,
        lng: state.lng,
        hasGarage: state.hasGarage,
        hasCentralAC: state.hasCentralAC,
        hasMaidRoom: state.hasMaidRoom,
        isFullyFurnished: state.isFullyFurnished,
        hasSecurity: state.hasSecurity,
        allowPets: state.allowPets,
        hasElevator: state.hasElevator,
        hasSwimmingPool: state.hasSwimmingPool,
        hasBalcony: state.hasBalcony,
        rebound: state.internalRebound,
        numberOfRooms: state.internalNumberOfRooms,
        internalLocation: state.internalLocation,
        salon: state.internalSalon,
        area: state.internalArea,
        floorNumber: state.internalFloorNumber,
        numberOfBathrooms: state.internalNumberOfBathrooms,
        buildingAge: state.internalBuildingAge,
        numberOfFloors: state.internalNumberOfFloors,
        propertyType: state.internalPropertyType,
        usageType: state.usageType,
        purpose: state.usageType,
        isPaymentVerified: false);

      print('📝 بيانات العقار:');
      print('  العنوان: ${estate.title}');
      print('  السعر: ${estate.price}');
      print('  الموقع: ${estate.location}');
      print('  التصنيف الرئيسي: ${estate.mainCategory}');
      print('  التصنيف الفرعي: ${estate.subCategory}');
      print('  نوع الاستغلال: ${estate.usageType}');

      // إنشاء الإعلان
      print('💾 حفظ الإعلان في قاعدة البيانات...');
      final estateId = await createEstate(estate, userId);
      print('✅ تم إنشاء الإعلان بنجاح. معرف الإعلان: $estateId');

      emit(state.copyWith(
        isSubmitting: false,
        isSuccess: true,
        createdEstateId: estateId, // إضافة معرف الإعلان المُنشأ
      ));
    } catch (e) {
      print('❌ خطأ في إنشاء الإعلان: $e');
      emit(state.copyWith(
        isSubmitting: false,
        errorMessage: e.toString()));
    }
  }

  Future<void> _onUpdateEstate(UpdateEstate event, Emitter<ImprovedAdState> emit) async {
    // TODO: تنفيذ تحديث العقار
    emit(state.copyWith(isSuccess: true));
  }
}
