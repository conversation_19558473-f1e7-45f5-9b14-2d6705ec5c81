import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../domain/entities/estate.dart';
import '../../domain/services/property_owner_service.dart';

class PropertyOwnerDashboard extends StatefulWidget {
  const PropertyOwnerDashboard({super.key});

  @override
  State<PropertyOwnerDashboard> createState() => _PropertyOwnerDashboardState();
}

class _PropertyOwnerDashboardState extends State<PropertyOwnerDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final PropertyOwnerService _ownerService = PropertyOwnerService();

  // بيانات لوحة التحكم
  Map<String, dynamic> _dashboardData = {};
  List<Estate> _myProperties = [];
  List<Map<String, dynamic>> _inquiries = [];
  List<Map<String, dynamic>> _viewings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadDashboardData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل بيانات لوحة التحكم
  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final futures = await Future.wait([
        _ownerService.getDashboardStatistics(),
        _ownerService.getMyProperties(),
        _ownerService.getInquiries(),
        _ownerService.getScheduledViewings(),
      ]);

      setState(() {
        _dashboardData = futures[0] as Map<String, dynamic>;
        _myProperties = futures[1] as List<Estate>;
        _inquiries = futures[2] as List<Map<String, dynamic>>;
        _viewings = futures[3] as List<Map<String, dynamic>>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'حدث خطأ في تحميل البيانات: $e',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'لوحة تحكم المالك',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: const Icon(Icons.dashboard),
              text: 'الرئيسية'),
            Tab(
              icon: const Icon(Icons.home_work),
              text: 'عقاراتي'),
            Tab(
              icon: const Icon(Icons.question_answer),
              text: 'الاستفسارات'),
            Tab(
              icon: const Icon(Icons.calendar_today),
              text: 'المعاينات'),
          ]),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.pushNamed(context, '/improved-ad-creation');
            }),
        ]),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDashboardTab(),
                _buildPropertiesTab(),
                _buildInquiriesTab(),
                _buildViewingsTab(),
              ]));
  }

  /// بناء تبويب الرئيسية
  Widget _buildDashboardTab() {
    return RefreshIndicator(
      onRefresh: _loadDashboardData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقات الإحصائيات
            _buildStatisticsCards(),
            const SizedBox(height: 24),

            // الرسم البياني للمشاهدات
            _buildViewsChart(),
            const SizedBox(height: 24),

            // الأنشطة الأخيرة
            _buildRecentActivities(),
            const SizedBox(height: 24),

            // نصائح وتوصيات
            _buildTipsAndRecommendations(),
          ])));
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatisticsCards() {
    final stats = [
      {
        'title': 'إجمالي العقارات',
        'value': _dashboardData['totalProperties']?.toString() ?? '0',
        'icon': Icons.home_work,
        'color': Colors.blue,
      },
      {
        'title': 'المشاهدات الشهرية',
        'value': _dashboardData['monthlyViews']?.toString() ?? '0',
        'icon': Icons.visibility,
        'color': Colors.green,
      },
      {
        'title': 'الاستفسارات',
        'value': _dashboardData['totalInquiries']?.toString() ?? '0',
        'icon': Icons.question_answer,
        'color': Colors.orange,
      },
      {
        'title': 'المعاينات المجدولة',
        'value': _dashboardData['scheduledViewings']?.toString() ?? '0',
        'icon': Icons.calendar_today,
        'color': Colors.purple,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.5),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return _buildStatCard(
          title: stat['title'] as String,
          value: stat['value'] as String,
          icon: stat['icon'] as IconData,
          color: stat['color'] as Color);
      });
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(
                  icon,
                  color: color,
                  size: 24)),
              Icon(
                Icons.trending_up,
                color: Colors.green,
                size: 16),
            ]),
          const Spacer(),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color)),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600)),
        ]));
  }

  /// بناء الرسم البياني للمشاهدات
  Widget _buildViewsChart() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: Theme.of(context).primaryColor,
                size: 20),
              const SizedBox(width: 8),
              Text(
                'إحصائيات المشاهدات',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(show: false),
                titlesData: FlTitlesData(show: false),
                borderData: FlBorderData(show: false),
                lineBarsData: [
                  LineChartBarData(
                    spots: _generateViewsData(),
                    isCurved: true,
                    color: Theme.of(context).primaryColor,
                    barWidth: 3,
                    dotData: FlDotData(show: false),
                    belowBarData: BarAreaData(
                      show: true,
                      color: Theme.of(context).primaryColor.withOpacity(0.1))),
                ]))),
        ]));
  }

  /// إنشاء بيانات المشاهدات للرسم البياني
  List<FlSpot> _generateViewsData() {
    // بيانات تجريبية - يمكن استبدالها ببيانات حقيقية من Firebase
    return [
      const FlSpot(0, 10),
      const FlSpot(1, 15),
      const FlSpot(2, 12),
      const FlSpot(3, 20),
      const FlSpot(4, 18),
      const FlSpot(5, 25),
      const FlSpot(6, 30),
    ];
  }

  /// بناء الأنشطة الأخيرة
  Widget _buildRecentActivities() {
    final activities = [
      {
        'title': 'استفسار جديد على منزل الجهراء',
        'time': 'منذ ساعتين',
        'icon': Icons.question_answer,
        'color': Colors.blue,
      },
      {
        'title': 'تم جدولة معاينة لشقة حولي',
        'time': 'منذ 4 ساعات',
        'icon': Icons.calendar_today,
        'color': Colors.green,
      },
      {
        'title': '15 مشاهدة جديدة لأرض الفروانية',
        'time': 'أمس',
        'icon': Icons.visibility,
        'color': Colors.orange,
      },
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.history,
                color: Theme.of(context).primaryColor,
                size: 20),
              const SizedBox(width: 8),
              Text(
                'الأنشطة الأخيرة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 16),
          ...activities.map((activity) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: (activity['color'] as Color).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8)),
                    child: Icon(
                      activity['icon'] as IconData,
                      color: activity['color'] as Color,
                      size: 16)),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          activity['title'] as String,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.w500)),
                        Text(
                          activity['time'] as String,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.grey.shade600)),
                      ])),
                ]));
          }),
        ]));
  }

  /// بناء النصائح والتوصيات
  Widget _buildTipsAndRecommendations() {
    final tips = [
      {
        'title': 'تحسين جودة الصور',
        'description': 'أضف صور عالية الجودة لزيادة المشاهدات بنسبة 40%',
        'icon': Icons.camera_alt,
        'color': Colors.blue,
      },
      {
        'title': 'تحديث الأسعار',
        'description': 'راجع أسعار العقارات المشابهة في منطقتك',
        'icon': Icons.price_change,
        'color': Colors.green,
      },
      {
        'title': 'الرد السريع',
        'description': 'الرد على الاستفسارات خلال ساعة يزيد فرص البيع',
        'icon': Icons.speed,
        'color': Colors.orange,
      },
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb,
                color: Theme.of(context).primaryColor,
                size: 20),
              const SizedBox(width: 8),
              Text(
                'نصائح لتحسين الأداء',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 16),
          ...tips.map((tip) {
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (tip['color'] as Color).withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: (tip['color'] as Color).withOpacity(0.2))),
              child: Row(
                children: [
                  Icon(
                    tip['icon'] as IconData,
                    color: tip['color'] as Color,
                    size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          tip['title'] as String,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: tip['color'] as Color)),
                        const SizedBox(height: 2),
                        Text(
                          tip['description'] as String,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.grey.shade700)),
                      ])),
                ]));
          }),
        ]));
  }

  /// بناء تبويب العقارات
  Widget _buildPropertiesTab() {
    if (_myProperties.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.home_work,
              size: 80,
              color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد عقارات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                color: Colors.grey.shade600)),
            const SizedBox(height: 8),
            Text(
              'أضف عقارك الأول لتبدأ',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500)),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pushNamed(context, '/improved-ad-creation');
              },
              icon: const Icon(Icons.add),
              label: Text(
                'إضافة عقار',
                style: GoogleFonts.cairo())),
          ]));
    }

    return RefreshIndicator(
      onRefresh: _loadDashboardData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _myProperties.length,
        itemBuilder: (context, index) {
          final property = _myProperties[index];
          return _buildPropertyCard(property);
        }));
  }

  /// بناء بطاقة العقار
  Widget _buildPropertyCard(Estate property) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // صورة العقار
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    width: 80,
                    height: 80,
                    color: Colors.grey.shade200,
                    child: property.photoUrls.isNotEmpty
                        ? Image.network(
                            property.photoUrls.first,
                            fit: BoxFit.cover)
                        : const Icon(Icons.home, color: Colors.grey))),
                const SizedBox(width: 16),

                // تفاصيل العقار
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        property.title,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis),
                      const SizedBox(height: 4),
                      Text(
                        property.location,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade600)),
                      const SizedBox(height: 4),
                      Text(
                        '${NumberFormat('#,###').format(property.price)} د.ك',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor)),
                    ])),

                // أزرار الإجراءات
                PopupMenuButton<String>(
                  onSelected: (value) => _handlePropertyAction(value, property),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          const Icon(Icons.edit, size: 20),
                          const SizedBox(width: 8),
                          Text('تعديل', style: GoogleFonts.cairo()),
                        ])),
                    PopupMenuItem(
                      value: 'promote',
                      child: Row(
                        children: [
                          const Icon(Icons.campaign, size: 20),
                          const SizedBox(width: 8),
                          Text('ترويج', style: GoogleFonts.cairo()),
                        ])),
                    PopupMenuItem(
                      value: 'statistics',
                      child: Row(
                        children: [
                          const Icon(Icons.analytics, size: 20),
                          const SizedBox(width: 8),
                          Text('إحصائيات', style: GoogleFonts.cairo()),
                        ])),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          const Icon(Icons.delete, size: 20, color: Colors.red),
                          const SizedBox(width: 8),
                          Text(
                            'حذف',
                            style: GoogleFonts.cairo(color: Colors.red)),
                        ])),
                  ]),
              ]),

            const SizedBox(height: 12),

            // إحصائيات سريعة
            Row(
              children: [
                _buildQuickStat(
                  icon: Icons.visibility,
                  label: 'مشاهدات',
                  value: '${property.viewsCount ?? 0}',
                  color: Colors.blue),
                const SizedBox(width: 16),
                _buildQuickStat(
                  icon: Icons.question_answer,
                  label: 'استفسارات',
                  value: '${property.inquiriesCount ?? 0}',
                  color: Colors.green),
                const SizedBox(width: 16),
                _buildQuickStat(
                  icon: Icons.favorite,
                  label: 'مفضلة',
                  value: '${property.favoritesCount ?? 0}',
                  color: Colors.red),
              ]),
          ])));
  }

  /// بناء إحصائية سريعة
  Widget _buildQuickStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          '$value $label',
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.grey.shade600)),
      ]);
  }

  /// بناء تبويب الاستفسارات
  Widget _buildInquiriesTab() {
    // TODO: Implement inquiries tab
    return const Center(
      child: Text('تبويب الاستفسارات - قيد التطوير'));
  }

  /// بناء تبويب المعاينات
  Widget _buildViewingsTab() {
    // TODO: Implement viewings tab
    return const Center(
      child: Text('تبويب المعاينات - قيد التطوير'));
  }

  /// التعامل مع إجراءات العقار
  void _handlePropertyAction(String action, Estate property) {
    switch (action) {
      case 'edit':
        // الانتقال لصفحة التعديل
        Navigator.pushNamed(
          context,
          '/edit-property',
          arguments: property);
        break;
      case 'promote':
        // عرض خيارات الترويج
        _showPromoteDialog(property);
        break;
      case 'statistics':
        // عرض إحصائيات مفصلة
        _showStatisticsDialog(property);
        break;
      case 'delete':
        // تأكيد الحذف
        _showDeleteConfirmation(property);
        break;
    }
  }

  /// عرض حوار الترويج
  void _showPromoteDialog(Estate property) {
    // TODO: Implement promote dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم تفعيل خاصية الترويج قريباً',
          style: GoogleFonts.cairo())));
  }

  /// عرض حوار الإحصائيات
  void _showStatisticsDialog(Estate property) {
    // TODO: Implement statistics dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم تفعيل خاصية الإحصائيات المفصلة قريباً',
          style: GoogleFonts.cairo())));
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(Estate property) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
        content: Text(
          'هل أنت متأكد من حذف هذا العقار؟ لا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo())),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteProperty(property);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo())),
        ]));
  }

  /// حذف العقار
  Future<void> _deleteProperty(Estate property) async {
    try {
      await _ownerService.deleteProperty(property.id);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم حذف العقار بنجاح',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.green));
      _loadDashboardData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'فشل في حذف العقار: $e',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
    }
  }
}
