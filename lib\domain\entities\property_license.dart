import 'package:equatable/equatable.dart';

/// حالة رخصة العقار
enum PropertyLicenseStatus {
  active,
  expired,
  suspended,
  revoked,
  pending,
}

/// كيان رخصة العقار
class PropertyLicense extends Equatable {
  final String id;
  final String userId;
  final String? estateId;
  final PropertyLicenseStatus status;
  final String licenseNumber;
  final String licenseType;
  final String propertyType;
  final String area;
  final String address;
  final String? ownerName;
  final String? ownerIdNumber;
  final DateTime issueDate;
  final DateTime expiryDate;
  final String? certificateUrl;
  final List<String>? allowedActivities;
  final Map<String, dynamic>? restrictions;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان رخصة العقار
  const PropertyLicense({
    required this.id,
    required this.userId,
    this.estateId,
    required this.status,
    required this.licenseNumber,
    required this.licenseType,
    required this.propertyType,
    required this.area,
    required this.address,
    this.ownerName,
    this.ownerIdNumber,
    required this.issueDate,
    required this.expiryDate,
    this.certificateUrl,
    this.allowedActivities,
    this.restrictions,
    this.additionalInfo,
  });

  /// إنشاء كيان رخصة العقار من JSON
  factory PropertyLicense.fromJson(Map<String, dynamic> json) {
    return PropertyLicense(
      id: json['id'] as String,
      userId: json['userId'] as String,
      estateId: json['estateId'] as String?,
      status: _parseStatus(json['status'] as String),
      licenseNumber: json['licenseNumber'] as String,
      licenseType: json['licenseType'] as String,
      propertyType: json['propertyType'] as String,
      area: json['area'] as String,
      address: json['address'] as String,
      ownerName: json['ownerName'] as String?,
      ownerIdNumber: json['ownerIdNumber'] as String?,
      issueDate: DateTime.parse(json['issueDate'] as String),
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      certificateUrl: json['certificateUrl'] as String?,
      allowedActivities: json['allowedActivities'] != null
          ? List<String>.from(json['allowedActivities'] as List)
          : null,
      restrictions: json['restrictions'] as Map<String, dynamic>?,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان رخصة العقار إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'estateId': estateId,
      'status': _statusToString(status),
      'licenseNumber': licenseNumber,
      'licenseType': licenseType,
      'propertyType': propertyType,
      'area': area,
      'address': address,
      'ownerName': ownerName,
      'ownerIdNumber': ownerIdNumber,
      'issueDate': issueDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'certificateUrl': certificateUrl,
      'allowedActivities': allowedActivities,
      'restrictions': restrictions,
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان رخصة العقار مع تعديل بعض الخصائص
  PropertyLicense copyWith({
    String? id,
    String? userId,
    String? estateId,
    PropertyLicenseStatus? status,
    String? licenseNumber,
    String? licenseType,
    String? propertyType,
    String? area,
    String? address,
    String? ownerName,
    String? ownerIdNumber,
    DateTime? issueDate,
    DateTime? expiryDate,
    String? certificateUrl,
    List<String>? allowedActivities,
    Map<String, dynamic>? restrictions,
    Map<String, dynamic>? additionalInfo,
  }) {
    return PropertyLicense(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      estateId: estateId ?? this.estateId,
      status: status ?? this.status,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      licenseType: licenseType ?? this.licenseType,
      propertyType: propertyType ?? this.propertyType,
      area: area ?? this.area,
      address: address ?? this.address,
      ownerName: ownerName ?? this.ownerName,
      ownerIdNumber: ownerIdNumber ?? this.ownerIdNumber,
      issueDate: issueDate ?? this.issueDate,
      expiryDate: expiryDate ?? this.expiryDate,
      certificateUrl: certificateUrl ?? this.certificateUrl,
      allowedActivities: allowedActivities ?? this.allowedActivities,
      restrictions: restrictions ?? this.restrictions,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// تحويل حالة الرخصة من نص إلى قيمة تعداد
  static PropertyLicenseStatus _parseStatus(String status) {
    switch (status) {
      case 'active':
        return PropertyLicenseStatus.active;
      case 'expired':
        return PropertyLicenseStatus.expired;
      case 'suspended':
        return PropertyLicenseStatus.suspended;
      case 'revoked':
        return PropertyLicenseStatus.revoked;
      case 'pending':
        return PropertyLicenseStatus.pending;
      default:
        return PropertyLicenseStatus.pending;
    }
  }

  /// تحويل حالة الرخصة من قيمة تعداد إلى نص
  static String _statusToString(PropertyLicenseStatus status) {
    switch (status) {
      case PropertyLicenseStatus.active:
        return 'active';
      case PropertyLicenseStatus.expired:
        return 'expired';
      case PropertyLicenseStatus.suspended:
        return 'suspended';
      case PropertyLicenseStatus.revoked:
        return 'revoked';
      case PropertyLicenseStatus.pending:
        return 'pending';
    }
  }

  /// التحقق مما إذا كانت الرخصة سارية
  bool isActive() {
    return status == PropertyLicenseStatus.active && DateTime.now().isBefore(expiryDate);
  }

  /// التحقق مما إذا كانت الرخصة منتهية
  bool isExpired() {
    return status == PropertyLicenseStatus.expired || DateTime.now().isAfter(expiryDate);
  }

  /// التحقق مما إذا كانت الرخصة معلقة
  bool isSuspended() {
    return status == PropertyLicenseStatus.suspended;
  }

  /// التحقق مما إذا كانت الرخصة ملغاة
  bool isRevoked() {
    return status == PropertyLicenseStatus.revoked;
  }

  /// الحصول على المدة المتبقية لصلاحية الرخصة بالأيام
  int getRemainingDays() {
    final now = DateTime.now();
    return expiryDate.difference(now).inDays;
  }

  /// التحقق مما إذا كان النشاط مسموح به
  bool isActivityAllowed(String activity) {
    return allowedActivities?.contains(activity) ?? false;
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        estateId,
        status,
        licenseNumber,
        licenseType,
        propertyType,
        area,
        address,
        ownerName,
        ownerIdNumber,
        issueDate,
        expiryDate,
        certificateUrl,
        allowedActivities,
        restrictions,
        additionalInfo,
      ];
}
