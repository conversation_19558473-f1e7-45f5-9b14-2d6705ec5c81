// lib/presentation/widgets/property_request/location_selector.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

// import '../../../core/constants/app_colors.dart';

/// محدد المناطق
class LocationSelector extends StatefulWidget {
  /// المناطق المحددة
  final List<String> selectedLocations;

  /// دالة عند تغيير المناطق المحددة
  final ValueChanged<List<String>> onLocationsChanged;

  const LocationSelector({
    super.key,
    required this.selectedLocations,
    required this.onLocationsChanged,
  });

  @override
  State<LocationSelector> createState() => _LocationSelectorState();
}

class _LocationSelectorState extends State<LocationSelector> {
  late List<String> _selectedLocations;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // قائمة المناطق في الكويت (من البيانات الرسمية المحدثة)
  final List<String> _allLocations = [
    // محافظة العاصمة
    'مدينة الكويت',
    'دسمان',
    'الشرق',
    'المرقاب',
    'الصوابر',
    'القبلة',
    'الصالحية',
    'بنيد القار',
    'الدوحة',
    'ميناء الدوحة',
    'الدسمة',
    'الدعية',
    'الفيحاء',
    'جبلة',
    'كيفان',
    'الخالدية',
    'المنصورية',
    'النزهة',
    'القادسية',
    'قرطبة',
    'الروضة',
    'الشامية',
    'الشويخ',
    'الشويخ الصناعية',
    'ميناء الشويخ',
    'الصليبيخات',
    'السرة',
    'العديلية',
    'غرناطة',
    'النهضة',
    'شمال غرب الصليبيخات',
    'اليرموك',
    'القيروان',
    'مدينة جابر الأحمد',
    'الري',
    'جزيرة فيلكا',
    'جزيرة أوها',
    'جزيرة ميشان',
    'جزيرة أم النمل',
    'حدائق السور',

    // محافظة حولي
    'حولي',
    'السالمية',
    'الجابرية',
    'الرميثية',
    'سلوى',
    'بيان',
    'مشرف',
    'الشعب',
    'الشهداء',
    'حطين',
    'سلام',
    'الزهراء',
    'مبارك العبدالله',
    'الصديق',
    'البدع',
    'أنجفة',
    'منطقة الوزارات',

    // محافظة مبارك الكبير
    'مبارك الكبير',
    'صباح السالم',
    'العدان',
    'القرين',
    'القصور',
    'الفنيطيس',
    'المسيلة',
    'أبو الحصانية',
    'أبو فطيرة',
    'المسايل',
    'وسطي',
    'صبحان الصناعية',
    'غرب أبو فطيرة الحرفية',

    // محافظة الأحمدي
    'الأحمدي',
    'أبو حليفة',
    'ميناء عبد الله',
    'علي صباح السالم',
    'العقيلة',
    'بر الأحمدي',
    'بنيدر',
    'الظهر',
    'الفحيحيل',
    'فهد الأحمد',
    'هدية',
    'جابر العلي',
    'الجليعة',
    'الخيران',
    'المهبولة',
    'المنقف',
    'المقوع',
    'الوفرة السكنية',
    'النويصيب',
    'الرقة',
    'صباح الأحمد',
    'مدينة صباح الأحمد البحرية',
    'الصباحية',
    'جنوب الصباحية',
    'الشعيبة الصناعية',
    'الوفرة',
    'الزور',
    'الفنطاس',
    'الشدادية الصناعية',

    // محافظة الفروانية
    'الفروانية',
    'عبد الله المبارك',
    'منطقة المطار',
    'الأندلس',
    'العارضية',
    'العارضية الحرفية',
    'إشبيلية',
    'الضجيج',
    'الفردوس',
    'جليب الشيوخ',
    'خيطان',
    'العمرية',
    'الرابية',
    'الري',
    'الرقعي',
    'الرحاب',
    'صباح الناصر',
    'جامعة صباح السالم',
    'غرب عبد الله المبارك',
    'جنوب عبد الله المبارك',
    'الصليبية الصناعية',

    // محافظة الجهراء
    'الجهراء',
    'العبدلي',
    'المطلاع',
    'كاظمة',
    'بحرة',
    'كبد',
    'الشقايا',
    'النهضة',
    'أمغرة الصناعية',
    'بر الجهراء',
    'الجهراء الصناعية الحرفية',
    'النعيم',
    'النسيم',
    'العيون',
    'القصر',
    'جابر الأحمد',
    'سعد العبد الله',
    'السالمي',
    'الصبية',
    'الصليبية',
    'المنطقة الزراعية الصليبية',
    'الصليبية السكنية',
    'تيماء',
    'الواحة',
    'جزيرة بوبيان',
    'جزيرة وربة',
  ];

  @override
  void initState() {
    super.initState();
    _selectedLocations = List.from(widget.selectedLocations);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحديث المناطق المحددة
  void _updateSelectedLocations() {
    widget.onLocationsChanged(_selectedLocations);
  }

  /// إضافة منطقة
  void _addLocation(String location) {
    if (!_selectedLocations.contains(location)) {
      setState(() {
        _selectedLocations.add(location);
        _searchController.clear();
        _searchQuery = '';
      });
      _updateSelectedLocations();
    }
  }

  /// إزالة منطقة
  void _removeLocation(String location) {
    setState(() {
      _selectedLocations.remove(location);
    });
    _updateSelectedLocations();
  }

  /// الحصول على المناطق المفلترة
  List<String> get _filteredLocations {
    if (_searchQuery.isEmpty) {
      return _allLocations;
    }

    return _allLocations.where((location) {
      return location.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // المناطق المحددة
        if (_selectedLocations.isNotEmpty)
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _selectedLocations.map((location) {
              return Chip(
                label: Text(
                  location,
                  style: GoogleFonts.cairo(
                    fontSize: 12)),
                backgroundColor: Colors.blue.withOpacity(0.1),
                deleteIconColor: Colors.blue,
                onDeleted: () => _removeLocation(location));
            }).toList()),

        if (_selectedLocations.isNotEmpty)
          const SizedBox(height: 16),

        // حقل البحث
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث عن منطقة',
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8))),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          }),

        const SizedBox(height: 8),

        // قائمة المناطق
        Container(
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8)),
          child: _filteredLocations.isEmpty
              ? Center(
                  child: Text(
                    'لا توجد نتائج',
                    style: GoogleFonts.cairo(
                      color: Colors.grey)))
              : ListView.builder(
                  itemCount: _filteredLocations.length,
                  itemBuilder: (context, index) {
                    final location = _filteredLocations[index];
                    final isSelected = _selectedLocations.contains(location);

                    return ListTile(
                      title: Text(
                        location,
                        style: GoogleFonts.cairo(
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal)),
                      trailing: isSelected
                          ? const Icon(
                              Icons.check_circle,
                              color: Colors.blue)
                          : null,
                      onTap: () {
                        if (isSelected) {
                          _removeLocation(location);
                        } else {
                          _addLocation(location);
                        }
                      });
                  })),
      ]);
  }
}
