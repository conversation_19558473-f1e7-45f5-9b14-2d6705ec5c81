import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج للجولة الافتراضية
class VirtualTour extends Equatable {
  /// معرف الجولة
  final String id;
  
  /// معرف العقار
  final String estateId;
  
  /// عنوان الجولة
  final String title;
  
  /// وصف الجولة
  final String? description;
  
  /// رابط الجولة
  final String tourUrl;
  
  /// نوع الجولة (360، فيديو، مخطط تفاعلي)
  final String tourType;
  
  /// صورة مصغرة للجولة
  final String? thumbnailUrl;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ التحديث
  final DateTime? updatedAt;
  
  /// معرف المستخدم الذي أنشأ الجولة
  final String createdBy;
  
  /// ما إذا كانت الجولة نشطة
  final bool isActive;
  
  /// عدد المشاهدات
  final int viewsCount;
  
  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  const VirtualTour({
    required this.id,
    required this.estateId,
    required this.title,
    this.description,
    required this.tourUrl,
    required this.tourType,
    this.thumbnailUrl,
    required this.createdAt,
    this.updatedAt,
    required this.createdBy,
    this.isActive = true,
    this.viewsCount = 0,
    this.metadata,
  });

  /// إنشاء نسخة معدلة من الجولة
  VirtualTour copyWith({
    String? id,
    String? estateId,
    String? title,
    String? description,
    String? tourUrl,
    String? tourType,
    String? thumbnailUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    bool? isActive,
    int? viewsCount,
    Map<String, dynamic>? metadata,
  }) {
    return VirtualTour(
      id: id ?? this.id,
      estateId: estateId ?? this.estateId,
      title: title ?? this.title,
      description: description ?? this.description,
      tourUrl: tourUrl ?? this.tourUrl,
      tourType: tourType ?? this.tourType,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      isActive: isActive ?? this.isActive,
      viewsCount: viewsCount ?? this.viewsCount,
      metadata: metadata ?? this.metadata);
  }

  /// تحويل الجولة إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'estateId': estateId,
      'title': title,
      'description': description,
      'tourUrl': tourUrl,
      'tourType': tourType,
      'thumbnailUrl': thumbnailUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdBy': createdBy,
      'isActive': isActive,
      'viewsCount': viewsCount,
      'metadata': metadata,
    };
  }

  /// إنشاء جولة من Map
  factory VirtualTour.fromMap(Map<String, dynamic> map) {
    return VirtualTour(
      id: map['id'] ?? '',
      estateId: map['estateId'] ?? '',
      title: map['title'] ?? '',
      description: map['description'],
      tourUrl: map['tourUrl'] ?? '',
      tourType: map['tourType'] ?? '360',
      thumbnailUrl: map['thumbnailUrl'],
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
      createdBy: map['createdBy'] ?? '',
      isActive: map['isActive'] ?? true,
      viewsCount: map['viewsCount'] ?? 0,
      metadata: map['metadata']);
  }

  /// إنشاء جولة من DocumentSnapshot
  factory VirtualTour.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return VirtualTour.fromMap(data);
  }

  /// زيادة عدد المشاهدات
  VirtualTour incrementViews() {
    return copyWith(viewsCount: viewsCount + 1);
  }

  /// الحصول على اسم نوع الجولة بالعربية
  String getTourTypeName() {
    switch (tourType) {
      case '360':
        return 'جولة 360 درجة';
      case 'video':
        return 'جولة فيديو';
      case 'interactive':
        return 'مخطط تفاعلي';
      default:
        return 'جولة افتراضية';
    }
  }

  @override
  List<Object?> get props => [
    id,
    estateId,
    title,
    description,
    tourUrl,
    tourType,
    thumbnailUrl,
    createdAt,
    updatedAt,
    createdBy,
    isActive,
    viewsCount,
    metadata,
  ];
}
