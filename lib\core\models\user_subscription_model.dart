// lib/core/models/user_subscription_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';

import 'plan_model.dart';

/// نموذج بيانات اشتراك المستخدم
class UserSubscriptionModel {
  /// معرف الاشتراك
  final String id;
  
  /// معرف المستخدم
  final String userId;
  
  /// معرف الباقة
  final String planId;
  
  /// نوع الباقة
  final PlanType planType;
  
  /// تاريخ بدء الاشتراك
  final DateTime startDate;
  
  /// تاريخ انتهاء الاشتراك
  final DateTime endDate;
  
  /// عدد الإعلانات المسموح بها
  final int allowedAds;
  
  /// عدد الإعلانات المتبقية
  int remainingAds;
  
  /// عدد الصور المسموح بها لكل إعلان
  final int allowedImagesPerAd;
  
  /// مدة عرض الإعلان (بالأيام)
  final int adDurationDays;
  
  /// ما إذا كان الاشتراك نشطًا
  bool isActive;
  
  /// ما إذا كان التجديد التلقائي مفعلًا
  bool autoRenew;
  
  /// معرف عملية الدفع
  final String? paymentId;
  
  /// سعر الاشتراك
  final double price;
  
  /// عملة الاشتراك
  final String currency;
  
  /// تاريخ آخر تجديد
  DateTime? lastRenewalDate;
  
  /// تاريخ التجديد القادم
  DateTime? nextRenewalDate;
  
  /// الميزات المفعلة
  final Map<String, bool> enabledFeatures;
  
  /// بيانات إضافية
  final Map<String, dynamic> metadata;

  UserSubscriptionModel({
    required this.id,
    required this.userId,
    required this.planId,
    required this.planType,
    required this.startDate,
    required this.endDate,
    required this.allowedAds,
    required this.remainingAds,
    required this.allowedImagesPerAd,
    required this.adDurationDays,
    required this.isActive,
    required this.autoRenew,
    this.paymentId,
    required this.price,
    required this.currency,
    this.lastRenewalDate,
    this.nextRenewalDate,
    required this.enabledFeatures,
    required this.metadata,
  });

  /// إنشاء نموذج من بيانات Firestore
  factory UserSubscriptionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserSubscriptionModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      planId: data['planId'] ?? '',
      planType: _getPlanTypeFromString(data['planType'] ?? 'free'),
      startDate: (data['startDate'] as Timestamp).toDate(),
      endDate: (data['endDate'] as Timestamp).toDate(),
      allowedAds: data['allowedAds'] ?? 0,
      remainingAds: data['remainingAds'] ?? 0,
      allowedImagesPerAd: data['allowedImagesPerAd'] ?? 0,
      adDurationDays: data['adDurationDays'] ?? 0,
      isActive: data['isActive'] ?? false,
      autoRenew: data['autoRenew'] ?? false,
      paymentId: data['paymentId'],
      price: (data['price'] ?? 0).toDouble(),
      currency: data['currency'] ?? 'KWD',
      lastRenewalDate: data['lastRenewalDate'] != null
          ? (data['lastRenewalDate'] as Timestamp).toDate()
          : null,
      nextRenewalDate: data['nextRenewalDate'] != null
          ? (data['nextRenewalDate'] as Timestamp).toDate()
          : null,
      enabledFeatures: Map<String, bool>.from(data['enabledFeatures'] ?? {}),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}));
  }

  /// تحويل النموذج إلى بيانات Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'planId': planId,
      'planType': planType.toString().split('.').last,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'allowedAds': allowedAds,
      'remainingAds': remainingAds,
      'allowedImagesPerAd': allowedImagesPerAd,
      'adDurationDays': adDurationDays,
      'isActive': isActive,
      'autoRenew': autoRenew,
      'paymentId': paymentId,
      'price': price,
      'currency': currency,
      'lastRenewalDate': lastRenewalDate != null
          ? Timestamp.fromDate(lastRenewalDate!)
          : null,
      'nextRenewalDate': nextRenewalDate != null
          ? Timestamp.fromDate(nextRenewalDate!)
          : null,
      'enabledFeatures': enabledFeatures,
      'metadata': metadata,
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  /// الحصول على نسخة محدثة من النموذج
  UserSubscriptionModel copyWith({
    String? id,
    String? userId,
    String? planId,
    PlanType? planType,
    DateTime? startDate,
    DateTime? endDate,
    int? allowedAds,
    int? remainingAds,
    int? allowedImagesPerAd,
    int? adDurationDays,
    bool? isActive,
    bool? autoRenew,
    String? paymentId,
    double? price,
    String? currency,
    DateTime? lastRenewalDate,
    DateTime? nextRenewalDate,
    Map<String, bool>? enabledFeatures,
    Map<String, dynamic>? metadata,
  }) {
    return UserSubscriptionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      planId: planId ?? this.planId,
      planType: planType ?? this.planType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      allowedAds: allowedAds ?? this.allowedAds,
      remainingAds: remainingAds ?? this.remainingAds,
      allowedImagesPerAd: allowedImagesPerAd ?? this.allowedImagesPerAd,
      adDurationDays: adDurationDays ?? this.adDurationDays,
      isActive: isActive ?? this.isActive,
      autoRenew: autoRenew ?? this.autoRenew,
      paymentId: paymentId ?? this.paymentId,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      lastRenewalDate: lastRenewalDate ?? this.lastRenewalDate,
      nextRenewalDate: nextRenewalDate ?? this.nextRenewalDate,
      enabledFeatures: enabledFeatures ?? this.enabledFeatures,
      metadata: metadata ?? this.metadata);
  }

  /// التحقق مما إذا كان الاشتراك منتهي الصلاحية
  bool isExpired() {
    return DateTime.now().isAfter(endDate);
  }

  /// التحقق مما إذا كانت الميزة مفعلة
  bool isFeatureEnabled(String featureId) {
    return enabledFeatures[featureId] ?? false;
  }

  /// تفعيل ميزة
  void enableFeature(String featureId) {
    enabledFeatures[featureId] = true;
  }

  /// تعطيل ميزة
  void disableFeature(String featureId) {
    enabledFeatures[featureId] = false;
  }

  /// تقليل عدد الإعلانات المتبقية
  bool decrementRemainingAds() {
    if (remainingAds > 0) {
      remainingAds--;
      return true;
    }
    return false;
  }

  /// الحصول على نوع الباقة من النص
  static PlanType _getPlanTypeFromString(String typeStr) {
    switch (typeStr.toLowerCase()) {
      case 'bronze':
      case 'nahas':
      case 'نحاسي':
      case 'basic':
        return PlanType.bronze;
      case 'silver':
      case 'fidda':
      case 'فضي':
      case 'premium':
        return PlanType.silver;
      case 'gold':
      case 'thahab':
      case 'ذهبي':
      case 'vip':
        return PlanType.gold;
      case 'free':
      case 'مجاني':
      default:
        return PlanType.free;
    }
  }
}
