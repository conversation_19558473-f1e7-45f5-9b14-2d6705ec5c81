import 'package:flutter/material.dart';


/// نوع العنصر الذي يتم التفاعل معه
enum ReactionItemType {
  /// موضوع
  topic,

  /// مشاركة
  post,

  /// تعليق
  comment,
}

/// نوع التفاعل
enum ReactionType {
  /// إعجاب
  like,

  /// حب
  love,

  /// ضحك
  haha,

  /// تعجب
  wow,

  /// حزن
  sad,

  /// غضب
  angry,

  /// دعم
  support,

  /// شكر
  thanks,
}

/// معلومات التفاعل
class ReactionInfo {
  /// نوع التفاعل
  final ReactionType type;

  /// اسم التفاعل
  final String name;

  /// أيقونة التفاعل
  final IconData icon;

  /// رمز التفاعل
  final String emoji;

  /// لون التفاعل
  final Color color;

  const ReactionInfo({
    required this.type,
    required this.name,
    required this.icon,
    required this.emoji,
    required this.color,
  });
}

/// قائمة التفاعلات المتاحة
const List<ReactionInfo> availableReactions = [
  ReactionInfo(
    type: ReactionType.like,
    name: 'إعجاب',
    icon: Icons.thumb_up,
    emoji: '👍',
    color: Colors.green, // تغيير من الأزرق إلى الأخضر
  ),
  ReactionInfo(
    type: ReactionType.love,
    name: 'حب',
    icon: Icons.favorite,
    emoji: '❤️',
    color: Colors.red),
  ReactionInfo(
    type: ReactionType.haha,
    name: 'ضحك',
    icon: Icons.sentiment_very_satisfied,
    emoji: '😂',
    color: Colors.amber),
  ReactionInfo(
    type: ReactionType.wow,
    name: 'تعجب',
    icon: Icons.sentiment_satisfied_alt,
    emoji: '😮',
    color: Colors.orange),
  ReactionInfo(
    type: ReactionType.sad,
    name: 'حزن',
    icon: Icons.sentiment_dissatisfied,
    emoji: '😢',
    color: Colors.indigo),
  ReactionInfo(
    type: ReactionType.angry,
    name: 'غضب',
    icon: Icons.sentiment_very_dissatisfied,
    emoji: '😡',
    color: Colors.deepOrange),
  ReactionInfo(
    type: ReactionType.support,
    name: 'دعم',
    icon: Icons.volunteer_activism,
    emoji: '🤝',
    color: Colors.green),
  ReactionInfo(
    type: ReactionType.thanks,
    name: 'شكر',
    icon: Icons.emoji_emotions,
    emoji: '🙏',
    color: Colors.purple),
];

/// زر التفاعلات
class ReactionsButton extends StatefulWidget {
  /// معرف العنصر
  final String itemId;

  /// نوع العنصر
  final ReactionItemType itemType;

  /// التفاعلات الحالية
  final Map<String, List<String>>? reactions;

  /// معرف المستخدم الحالي
  final String? currentUserId;

  /// دالة يتم استدعاؤها عند إضافة تفاعل
  final Function(String, ReactionType) onReactionAdded;

  /// دالة يتم استدعاؤها عند إزالة تفاعل
  final Function(String, ReactionType) onReactionRemoved;

  /// حجم الزر
  final double size;

  /// ما إذا كان الزر مصغراً
  final bool isCompact;

  const ReactionsButton({
    super.key,
    required this.itemId,
    required this.itemType,
    this.reactions,
    this.currentUserId,
    required this.onReactionAdded,
    required this.onReactionRemoved,
    this.size = 24,
    this.isCompact = false,
  });

  @override
  State<ReactionsButton> createState() => _ReactionsButtonState();
}

class _ReactionsButtonState extends State<ReactionsButton> {
  bool _isReactionsVisible = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _hideReactions();
    super.dispose();
  }

  /// الحصول على التفاعل الحالي للمستخدم
  ReactionType? _getCurrentUserReaction() {
    if (widget.currentUserId == null || widget.reactions == null) {
      return null;
    }

    for (final entry in widget.reactions!.entries) {
      if (entry.value.contains(widget.currentUserId)) {
        return _getReactionTypeFromString(entry.key);
      }
    }

    return null;
  }

  /// الحصول على نوع التفاعل من السلسلة النصية
  ReactionType? _getReactionTypeFromString(String reactionString) {
    for (final reaction in ReactionType.values) {
      if (reaction.name == reactionString) {
        return reaction;
      }
    }
    return null;
  }

  /// الحصول على معلومات التفاعل من النوع
  ReactionInfo _getReactionInfo(ReactionType type) {
    return availableReactions.firstWhere(
      (reaction) => reaction.type == type,
      orElse: () => availableReactions.first);
  }

  /// إظهار التفاعلات
  void _showReactions() {
    if (_overlayEntry != null) {
      return;
    }

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx - 150 + size.width / 2,
        top: offset.dy - 60,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, -60),
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(30),
            color: Colors.white,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30)),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: availableReactions.map((reaction) {
                  return _buildReactionButton(reaction);
                }).toList()))))));

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isReactionsVisible = true;
    });
  }

  /// إخفاء التفاعلات
  void _hideReactions() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
    setState(() {
      _isReactionsVisible = false;
    });
  }

  /// بناء زر التفاعل
  Widget _buildReactionButton(ReactionInfo reaction) {
    return InkWell(
      onTap: () {
        _hideReactions();
        final currentReaction = _getCurrentUserReaction();
        if (currentReaction == reaction.type) {
          widget.onReactionRemoved(widget.itemId, reaction.type);
        } else {
          if (currentReaction != null) {
            widget.onReactionRemoved(widget.itemId, currentReaction);
          }
          widget.onReactionAdded(widget.itemId, reaction.type);
        }
      },
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              reaction.emoji,
              style: const TextStyle(fontSize: 20)),
            if (!widget.isCompact) ...[
              const SizedBox(height: 4),
              Text(
                reaction.name,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey.shade700)),
            ],
          ])));
  }

  @override
  Widget build(BuildContext context) {
    final currentReaction = _getCurrentUserReaction();
    final ReactionInfo displayReaction = currentReaction != null
        ? _getReactionInfo(currentReaction)
        : availableReactions.first;

    return CompositedTransformTarget(
      link: _layerLink,
      child: InkWell(
        onTap: () {
          if (_isReactionsVisible) {
            _hideReactions();
          } else {
            _showReactions();
          }
        },
        onLongPress: _showReactions,
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                currentReaction != null ? displayReaction.icon : Icons.thumb_up_outlined,
                size: widget.size,
                color: currentReaction != null
                    ? displayReaction.color
                    : Colors.grey.shade600),
              if (!widget.isCompact && widget.reactions != null) ...[
                const SizedBox(width: 4),
                Text(
                  _getTotalReactionsCount().toString(),
                  style: TextStyle(
                    color: currentReaction != null
                        ? displayReaction.color
                        : Colors.grey.shade700,
                    fontSize: 12,
                    fontWeight: FontWeight.bold)),
              ],
            ]))));
  }

  /// الحصول على إجمالي عدد التفاعلات
  int _getTotalReactionsCount() {
    if (widget.reactions == null) {
      return 0;
    }

    int count = 0;
    for (final entry in widget.reactions!.entries) {
      count += entry.value.length;
    }
    return count;
  }
}
