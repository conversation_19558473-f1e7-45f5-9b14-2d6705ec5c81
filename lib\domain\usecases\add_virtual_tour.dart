import 'dart:io';
import '../entities/virtual_tour.dart';
import '../repositories/virtual_tour_repository.dart';

/// حالة استخدام لإضافة جولة افتراضية للعقار
class AddVirtualTour {
  final VirtualTourRepository repository;

  AddVirtualTour(this.repository);

  /// إضافة جولة افتراضية جديدة للعقار
  /// [tour] هي الجولة الافتراضية المراد إضافتها
  /// [thumbnailFile] هو ملف الصورة المصغرة للجولة (اختياري)
  /// يعيد معرف الجولة الافتراضية الجديدة
  Future<String> call(VirtualTour tour, {File? thumbnailFile}) async {
    // التحقق من صحة البيانات
    if (tour.estateId.isEmpty) {
      throw Exception('معرف العقار مطلوب');
    }
    
    if (tour.title.isEmpty) {
      throw Exception('عنوان الجولة مطلوب');
    }
    
    if (tour.tourUrl.isEmpty) {
      throw Exception('رابط الجولة مطلوب');
    }
    
    if (tour.createdBy.isEmpty) {
      throw Exception('معرف المستخدم مطلوب');
    }
    
    // إضافة الجولة الافتراضية
    return await repository.addVirtualTour(tour, thumbnailFile: thumbnailFile);
  }
}
