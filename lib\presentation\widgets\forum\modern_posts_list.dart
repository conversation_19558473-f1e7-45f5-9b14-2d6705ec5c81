import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/post_model.dart';
import 'modern_post_card.dart';

/// قائمة عرض مشاركات المنتدى الحديثة
class ModernPostsList extends StatelessWidget {
  /// قائمة المشاركات
  final List<PostModel> posts;

  /// دالة يتم استدعاؤها عند النقر على زر الإعجاب
  final Function(PostModel)? onLikeTap;

  /// دالة يتم استدعاؤها عند النقر على زر الرد
  final Function(PostModel)? onReplyTap;

  /// دالة يتم استدعاؤها عند النقر على زر الإبلاغ
  final Function(PostModel)? onReportTap;

  /// دالة يتم استدعاؤها عند النقر على زر التعديل
  final Function(PostModel)? onEditTap;

  /// دالة يتم استدعاؤها عند النقر على زر الحذف
  final Function(PostModel)? onDeleteTap;

  /// دالة يتم استدعاؤها عند النقر على زر تعيين كأفضل إجابة
  final Function(PostModel)? onMarkAsBestAnswerTap;

  /// معرف المستخدم الحالي
  final String? currentUserId;

  /// معرف صاحب الموضوع
  final String? topicOwnerId;

  /// ما إذا كان يتم تحميل المزيد من المشاركات
  final bool isLoadingMore;

  /// دالة يتم استدعاؤها عند التمرير للأسفل لتحميل المزيد
  final VoidCallback? onLoadMore;

  /// متحكم التمرير
  final ScrollController? scrollController;

  /// ما إذا كان يتم عرض الردود المتداخلة
  final bool showNestedReplies;

  const ModernPostsList({
    super.key,
    required this.posts,
    this.onLikeTap,
    this.onReplyTap,
    this.onReportTap,
    this.onEditTap,
    this.onDeleteTap,
    this.onMarkAsBestAnswerTap,
    this.currentUserId,
    this.topicOwnerId,
    this.isLoadingMore = false,
    this.onLoadMore,
    this.scrollController,
    this.showNestedReplies = true,
  });

  @override
  Widget build(BuildContext context) {
    if (posts.isEmpty) {
      return _buildEmptyView();
    }

    // تنظيم المشاركات في شكل شجرة إذا كان showNestedReplies = true
    if (showNestedReplies) {
      final rootPosts = posts.where((post) => post.parentId == null).toList();
      final Map<String, List<PostModel>> repliesMap = {};
      
      for (final post in posts.where((post) => post.parentId != null)) {
        if (!repliesMap.containsKey(post.parentId)) {
          repliesMap[post.parentId!] = [];
        }
        repliesMap[post.parentId]!.add(post);
      }
      
      return _buildNestedList(rootPosts, repliesMap);
    }
    
    // عرض المشاركات في قائمة مسطحة
    return _buildFlatList();
  }

  /// بناء عرض فارغ
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.forum_outlined,
            size: 64,
            color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد مشاركات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600)),
          const SizedBox(height: 8),
          Text(
            'كن أول من يضيف مشاركة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600)),
        ]));
  }

  /// بناء قائمة مسطحة
  Widget _buildFlatList() {
    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(8),
      itemCount: posts.length + (isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == posts.length) {
          return _buildLoadingIndicator();
        }

        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 375),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: _buildPostCard(posts[index]))));
      });
  }

  /// بناء قائمة متداخلة
  Widget _buildNestedList(List<PostModel> rootPosts, Map<String, List<PostModel>> repliesMap) {
    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(8),
      itemCount: rootPosts.length + (isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == rootPosts.length) {
          return _buildLoadingIndicator();
        }

        final post = rootPosts[index];
        final replies = repliesMap[post.id] ?? [];
        
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 375),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // المشاركة الرئيسية
                  _buildPostCard(post),
                  
                  // الردود
                  if (replies.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(right: 16.0),
                      child: Column(
                        children: _buildReplies(replies, repliesMap, 1))),
                ]))));
      });
  }

  /// بناء الردود بشكل متداخل
  List<Widget> _buildReplies(List<PostModel> replies, Map<String, List<PostModel>> repliesMap, int level) {
    final List<Widget> widgets = [];
    
    for (final reply in replies) {
      widgets.add(_buildPostCard(reply, isReply: true, indentLevel: level));
      
      final nestedReplies = repliesMap[reply.id] ?? [];
      if (nestedReplies.isNotEmpty && level < 3) { // تحديد عمق التداخل
        widgets.addAll(_buildReplies(nestedReplies, repliesMap, level + 1));
      }
    }
    
    return widgets;
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingIndicator() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: CircularProgressIndicator(
          color: AppColors.primary)));
  }

  /// بناء بطاقة المشاركة
  Widget _buildPostCard(PostModel post, {bool isReply = false, int indentLevel = 0}) {
    final isCurrentUser = post.userId == currentUserId;
    final isTopicOwner = post.userId == topicOwnerId;
    final isLiked = post.likedBy?.contains(currentUserId) ?? false;

    return ModernPostCard(
      post: post,
      onLikeTap: onLikeTap != null ? () => onLikeTap!(post) : null,
      onReplyTap: onReplyTap != null ? () => onReplyTap!(post) : null,
      onReportTap: onReportTap != null ? () => onReportTap!(post) : null,
      onEditTap: onEditTap != null ? () => onEditTap!(post) : null,
      onDeleteTap: onDeleteTap != null ? () => onDeleteTap!(post) : null,
      onMarkAsBestAnswerTap: onMarkAsBestAnswerTap != null ? () => onMarkAsBestAnswerTap!(post) : null,
      isCurrentUser: isCurrentUser,
      isTopicOwner: topicOwnerId == currentUserId,
      isLiked: isLiked,
      isReply: isReply,
      indentLevel: indentLevel);
  }
}
