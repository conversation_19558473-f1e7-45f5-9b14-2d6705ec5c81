import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/notification_model.dart';

/// بطاقة الإشعار
class NotificationCard extends StatelessWidget {
  /// نموذج الإشعار
  final NotificationModel notification;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;

  /// دالة يتم استدعاؤها عند النقر على زر الحذف
  final VoidCallback? onDelete;

  /// دالة يتم استدعاؤها عند النقر على زر التعليم كمقروء
  final VoidCallback? onMarkAsRead;

  const NotificationCard({
    super.key,
    required this.notification,
    this.onTap,
    this.onDelete,
    this.onMarkAsRead,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: notification.isRead ? 0 : 2,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: notification.isRead
              ? Colors.transparent
              : Color(notification.getColor()).withOpacity(0.5),
          width: notification.isRead ? 0 : 1)),
      color: notification.isRead
          ? Colors.white
          : Color(notification.getColor()).withOpacity(0.05),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // أيقونة الإشعار
              _buildNotificationIcon(),
              const SizedBox(width: 12),
              
              // محتوى الإشعار
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان الإشعار
                    Text(
                      notification.title,
                      style: TextStyle(
                        fontWeight: notification.isRead
                            ? FontWeight.normal
                            : FontWeight.bold,
                        fontSize: 16)),
                    const SizedBox(height: 4),
                    
                    // محتوى الإشعار
                    Text(
                      notification.body,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14)),
                    const SizedBox(height: 8),
                    
                    // وقت الإشعار
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 12,
                          color: Colors.grey.shade500),
                        const SizedBox(width: 4),
                        Text(
                          _formatTime(notification.createdAt),
                          style: TextStyle(
                            color: Colors.grey.shade500,
                            fontSize: 12)),
                        const Spacer(),
                        
                        // زر التعليم كمقروء
                        if (!notification.isRead && onMarkAsRead != null)
                          InkWell(
                            onTap: onMarkAsRead,
                            borderRadius: BorderRadius.circular(4),
                            child: Padding(
                              padding: const EdgeInsets.all(4),
                              child: Text(
                                'تعليم كمقروء',
                                style: TextStyle(
                                  color: AppColors.primary,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold)))),
                        
                        // زر الحذف
                        if (onDelete != null) ...[
                          const SizedBox(width: 8),
                          InkWell(
                            onTap: onDelete,
                            borderRadius: BorderRadius.circular(4),
                            child: Padding(
                              padding: const EdgeInsets.all(4),
                              child: Icon(
                                Icons.delete,
                                size: 16,
                                color: Colors.red.shade400))),
                        ],
                      ]),
                  ])),
            ]))));
  }

  /// بناء أيقونة الإشعار
  Widget _buildNotificationIcon() {
    if (notification.imageUrl != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: CachedNetworkImage(
          imageUrl: notification.imageUrl!,
          width: 50,
          height: 50,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            width: 50,
            height: 50,
            color: Colors.grey.shade200,
            child: const Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2)))),
          errorWidget: (context, url, error) => Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Color(notification.getColor()).withOpacity(0.1),
              shape: BoxShape.circle),
            child: Icon(
              _getNotificationIcon(),
              size: 24,
              color: Color(notification.getColor())))));
    } else {
      return Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: Color(notification.getColor()).withOpacity(0.1),
          shape: BoxShape.circle),
        child: Icon(
          _getNotificationIcon(),
          size: 24,
          color: Color(notification.getColor())));
    }
  }

  /// الحصول على أيقونة الإشعار
  IconData _getNotificationIcon() {
    switch (notification.type) {
      case NotificationType.topicLike:
      case NotificationType.postLike:
        return Icons.favorite;
      case NotificationType.topicReply:
      case NotificationType.postReply:
        return Icons.reply;
      case NotificationType.userFollow:
      case NotificationType.topicFollow:
        return Icons.person_add;
      case NotificationType.bestAnswer:
        return Icons.verified;
      case NotificationType.achievement:
        return Icons.emoji_events;
      case NotificationType.levelUp:
        return Icons.trending_up;
      case NotificationType.adminNotification:
        return Icons.admin_panel_settings;
      case NotificationType.general:
      default:
        return Icons.notifications;
    }
  }

  /// تنسيق الوقت
  String _formatTime(DateTime time) {
    timeago.setLocaleMessages('ar', timeago.ArMessages());
    return timeago.format(time, locale: 'ar');
  }
}
