import 'package:equatable/equatable.dart';

/// نموذج خيارات الفلتر للوبي
class ForumFilterOptionsModel extends Equatable {
  /// معرف الفئة للفلتر
  final String? categoryId;

  /// نوع الفرز
  final ForumSortType sortType;

  /// اتجاه الفرز
  final ForumSortDirection sortDirection;

  /// فلتر حسب المستخدم
  final String? userId;

  /// فلتر حسب الكلمات المفتاحية
  final List<String>? keywords;

  /// فلتر حسب التاريخ (من)
  final DateTime? dateFrom;

  /// فلتر حسب التاريخ (إلى)
  final DateTime? dateTo;

  /// فلتر المواضيع المميزة فقط
  final bool featuredOnly;

  /// فلتر المواضيع المثبتة فقط
  final bool pinnedOnly;

  /// فلتر المواضيع المحلولة فقط
  final bool solvedOnly;

  /// فلتر المواضيع التي تحتوي على صور فقط
  final bool withImagesOnly;

  /// فلتر المواضيع حسب عدد الردود (الحد الأدنى)
  final int? minReplies;

  /// فلتر المواضيع حسب عدد الردود (الحد الأقصى)
  final int? maxReplies;

  /// فلتر المواضيع حسب عدد المشاهدات (الحد الأدنى)
  final int? minViews;

  /// فلتر المواضيع حسب عدد المشاهدات (الحد الأقصى)
  final int? maxViews;

  /// فلتر المواضيع حسب عدد الإعجابات (الحد الأدنى)
  final int? minLikes;

  /// فلتر المواضيع حسب عدد الإعجابات (الحد الأقصى)
  final int? maxLikes;

  /// فلتر المواضيع التي قام المستخدم بالإعجاب بها
  final bool likedByUser;

  /// فلتر المواضيع التي قام المستخدم بحفظها
  final bool bookmarkedByUser;

  /// فلتر المواضيع التي قام المستخدم بمتابعتها
  final bool followedByUser;

  /// فلتر المواضيع حسب نوع المحتوى
  final List<ForumContentType>? contentTypes;

  const ForumFilterOptionsModel({
    this.categoryId,
    this.sortType = ForumSortType.dateCreated,
    this.sortDirection = ForumSortDirection.descending,
    this.userId,
    this.keywords,
    this.dateFrom,
    this.dateTo,
    this.featuredOnly = false,
    this.pinnedOnly = false,
    this.solvedOnly = false,
    this.withImagesOnly = false,
    this.minReplies,
    this.maxReplies,
    this.minViews,
    this.maxViews,
    this.minLikes,
    this.maxLikes,
    this.likedByUser = false,
    this.bookmarkedByUser = false,
    this.followedByUser = false,
    this.contentTypes,
  });

  /// إنشاء نسخة معدلة من خيارات الفلتر
  ForumFilterOptionsModel copyWith({
    String? categoryId,
    ForumSortType? sortType,
    ForumSortDirection? sortDirection,
    String? userId,
    List<String>? keywords,
    DateTime? dateFrom,
    DateTime? dateTo,
    bool? featuredOnly,
    bool? pinnedOnly,
    bool? solvedOnly,
    bool? withImagesOnly,
    int? minReplies,
    int? maxReplies,
    int? minViews,
    int? maxViews,
    int? minLikes,
    int? maxLikes,
    bool? likedByUser,
    bool? bookmarkedByUser,
    bool? followedByUser,
    List<ForumContentType>? contentTypes,
  }) {
    return ForumFilterOptionsModel(
      categoryId: categoryId ?? this.categoryId,
      sortType: sortType ?? this.sortType,
      sortDirection: sortDirection ?? this.sortDirection,
      userId: userId ?? this.userId,
      keywords: keywords ?? this.keywords,
      dateFrom: dateFrom ?? this.dateFrom,
      dateTo: dateTo ?? this.dateTo,
      featuredOnly: featuredOnly ?? this.featuredOnly,
      pinnedOnly: pinnedOnly ?? this.pinnedOnly,
      solvedOnly: solvedOnly ?? this.solvedOnly,
      withImagesOnly: withImagesOnly ?? this.withImagesOnly,
      minReplies: minReplies ?? this.minReplies,
      maxReplies: maxReplies ?? this.maxReplies,
      minViews: minViews ?? this.minViews,
      maxViews: maxViews ?? this.maxViews,
      minLikes: minLikes ?? this.minLikes,
      maxLikes: maxLikes ?? this.maxLikes,
      likedByUser: likedByUser ?? this.likedByUser,
      bookmarkedByUser: bookmarkedByUser ?? this.bookmarkedByUser,
      followedByUser: followedByUser ?? this.followedByUser,
      contentTypes: contentTypes ?? this.contentTypes);
  }

  /// تحويل خيارات الفلتر إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'categoryId': categoryId,
      'sortType': sortType.index,
      'sortDirection': sortDirection.index,
      'userId': userId,
      'keywords': keywords,
      'dateFrom': dateFrom?.millisecondsSinceEpoch,
      'dateTo': dateTo?.millisecondsSinceEpoch,
      'featuredOnly': featuredOnly,
      'pinnedOnly': pinnedOnly,
      'solvedOnly': solvedOnly,
      'withImagesOnly': withImagesOnly,
      'minReplies': minReplies,
      'maxReplies': maxReplies,
      'minViews': minViews,
      'maxViews': maxViews,
      'minLikes': minLikes,
      'maxLikes': maxLikes,
      'likedByUser': likedByUser,
      'bookmarkedByUser': bookmarkedByUser,
      'followedByUser': followedByUser,
      'contentTypes': contentTypes?.map((e) => e.index).toList(),
    };
  }

  /// إنشاء خيارات فلتر من خريطة
  factory ForumFilterOptionsModel.fromMap(Map<String, dynamic> map) {
    return ForumFilterOptionsModel(
      categoryId: map['categoryId'],
      sortType: ForumSortType.values[map['sortType'] ?? 0],
      sortDirection: ForumSortDirection.values[map['sortDirection'] ?? 1],
      userId: map['userId'],
      keywords: map['keywords'] != null ? List<String>.from(map['keywords']) : null,
      dateFrom: map['dateFrom'] != null ? DateTime.fromMillisecondsSinceEpoch(map['dateFrom']) : null,
      dateTo: map['dateTo'] != null ? DateTime.fromMillisecondsSinceEpoch(map['dateTo']) : null,
      featuredOnly: map['featuredOnly'] ?? false,
      pinnedOnly: map['pinnedOnly'] ?? false,
      solvedOnly: map['solvedOnly'] ?? false,
      withImagesOnly: map['withImagesOnly'] ?? false,
      minReplies: map['minReplies'],
      maxReplies: map['maxReplies'],
      minViews: map['minViews'],
      maxViews: map['maxViews'],
      minLikes: map['minLikes'],
      maxLikes: map['maxLikes'],
      likedByUser: map['likedByUser'] ?? false,
      bookmarkedByUser: map['bookmarkedByUser'] ?? false,
      followedByUser: map['followedByUser'] ?? false,
      contentTypes: map['contentTypes'] != null
          ? List<ForumContentType>.from(
              map['contentTypes'].map((x) => ForumContentType.values[x]))
          : null);
  }

  /// خيارات الفلتر الافتراضية
  static ForumFilterOptionsModel get defaultOptions => const ForumFilterOptionsModel();

  @override
  List<Object?> get props => [
        categoryId,
        sortType,
        sortDirection,
        userId,
        keywords,
        dateFrom,
        dateTo,
        featuredOnly,
        pinnedOnly,
        solvedOnly,
        withImagesOnly,
        minReplies,
        maxReplies,
        minViews,
        maxViews,
        minLikes,
        maxLikes,
        likedByUser,
        bookmarkedByUser,
        followedByUser,
        contentTypes,
      ];
}

/// أنواع الفرز للوبي
enum ForumSortType {
  /// تاريخ الإنشاء
  dateCreated,

  /// تاريخ آخر تحديث
  lastUpdated,

  /// عدد المشاهدات
  views,

  /// عدد الردود
  replies,

  /// عدد الإعجابات
  likes,

  /// الأكثر تفاعلاً
  activity,

  /// الأبجدية
  alphabetical,
}

/// اتجاهات الفرز للوبي
enum ForumSortDirection {
  /// تصاعدي
  ascending,

  /// تنازلي
  descending,
}

/// أنواع محتوى اللوبي
enum ForumContentType {
  /// نص
  text,

  /// صور
  images,

  /// روابط
  links,

  /// استطلاع رأي
  poll,

  /// فيديو
  video,

  /// ملفات
  files,
}
