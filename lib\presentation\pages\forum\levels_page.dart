import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:percent_indicator/percent_indicator.dart';

import '../../../domain/models/forum/user_level_model.dart';
import '../../../domain/models/forum/user_statistics_model.dart';
import '../../../presentation/providers/auth_provider.dart' as app_auth;
import '../../../presentation/providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/forum/level_card.dart';

/// صفحة المستويات
class LevelsPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/levels';

  /// معرف المستخدم
  final String? userId;

  const LevelsPage({
    super.key,
    this.userId,
  });

  @override
  State<LevelsPage> createState() => _LevelsPageState();
}

class _LevelsPageState extends State<LevelsPage> {
  bool _isLoading = true;
  UserStatisticsModel? _statistics;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);

      final userId = widget.userId ?? authProvider.user?.uid;
      if (userId == null) {
        setState(() {
          _error = 'يجب تسجيل الدخول أولاً';
          _isLoading = false;
        });
        return;
      }

      // تحميل إحصائيات المستخدم
      final statistics = await forumProvider.getUserStatistics(userId);

      setState(() {
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ أثناء تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المستويات والنقاط')),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _error.isNotEmpty
              ? ErrorView(
                  message: _error,
                  onRetry: _loadData)
              : _statistics == null
                  ? const Center(child: Text('لا توجد بيانات'))
                  : _buildContent());
  }

  /// بناء محتوى الصفحة
  Widget _buildContent() {
    final currentLevel = UserLevelModel.getCurrentLevel(_statistics!.points);
    final nextLevel = UserLevelModel.getNextLevel(_statistics!.points);
    final remainingPoints = UserLevelModel.getRemainingPoints(_statistics!.points);
    final progress = UserLevelModel.getLevelProgress(_statistics!.points);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة المستوى الحالي
          _buildCurrentLevelCard(currentLevel, nextLevel, remainingPoints, progress),
          const SizedBox(height: 24),

          // معلومات النقاط
          _buildPointsInfo(),
          const SizedBox(height: 24),

          // قائمة المستويات
          const Text(
            'جميع المستويات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          _buildLevelsList(currentLevel),
        ]));
  }

  /// بناء بطاقة المستوى الحالي
  Widget _buildCurrentLevelCard(
    UserLevelModel currentLevel,
    UserLevelModel? nextLevel,
    int remainingPoints,
    double progress) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: currentLevel.color,
          width: 2)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // معلومات المستوى
            Row(
              children: [
                // أيقونة المستوى
                CircularPercentIndicator(
                  radius: 40,
                  lineWidth: 8,
                  percent: progress,
                  center: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: currentLevel.color.withOpacity(0.1),
                      shape: BoxShape.circle),
                    child: Icon(
                      currentLevel.icon,
                      size: 30,
                      color: currentLevel.color)),
                  progressColor: currentLevel.color,
                  backgroundColor: Colors.grey.shade200),
                const SizedBox(width: 16),

                // معلومات المستوى
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المستوى الحالي',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14)),
                      const SizedBox(height: 4),
                      Text(
                        currentLevel.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: currentLevel.color,
                          fontSize: 20)),
                      const SizedBox(height: 4),
                      Text(
                        currentLevel.description,
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 14)),
                    ])),
              ]),
            const SizedBox(height: 16),

            // شريط التقدم
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'المستوى ${currentLevel.id}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14)),
                    if (nextLevel != null)
                      Text(
                        'المستوى ${nextLevel.id}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14))
                    else
                      const Text(
                        'أعلى مستوى',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14)),
                  ]),
                const SizedBox(height: 8),
                LinearPercentIndicator(
                  lineHeight: 10,
                  percent: progress,
                  progressColor: currentLevel.color,
                  backgroundColor: Colors.grey.shade200,
                  barRadius: const Radius.circular(5),
                  padding: EdgeInsets.zero,
                  animation: true,
                  animationDuration: 1000),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${_statistics!.points} نقطة',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14)),
                    if (nextLevel != null)
                      Text(
                        '${nextLevel.requiredPoints} نقطة',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14)),
                  ]),
              ]),
            const SizedBox(height: 16),

            // معلومات المستوى التالي
            if (nextLevel != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200)),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.grey.shade600,
                      size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'تحتاج إلى $remainingPoints نقطة للوصول إلى المستوى التالي: ${nextLevel.name}',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 14))),
                  ])),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: currentLevel.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: currentLevel.color.withOpacity(0.3))),
                child: Row(
                  children: [
                    Icon(
                      Icons.emoji_events,
                      color: currentLevel.color,
                      size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'تهانينا! لقد وصلت إلى أعلى مستوى في المنتدى.',
                        style: TextStyle(
                          color: currentLevel.color,
                          fontWeight: FontWeight.bold,
                          fontSize: 14))),
                  ])),
            ],
          ])));
  }

  /// بناء معلومات النقاط
  Widget _buildPointsInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'كيفية كسب النقاط',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Card(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildPointItem(
                  'إنشاء موضوع جديد',
                  '10 نقاط',
                  Icons.post_add,
                  Colors.green, // تغيير من الأزرق إلى الأخضر
                ),
                const Divider(),
                _buildPointItem(
                  'كتابة مشاركة',
                  '5 نقاط',
                  Icons.comment,
                  Colors.green),
                const Divider(),
                _buildPointItem(
                  'الحصول على إعجاب',
                  '2 نقطة',
                  Icons.favorite,
                  Colors.red),
                const Divider(),
                _buildPointItem(
                  'تعيين مشاركتك كأفضل إجابة',
                  '20 نقطة',
                  Icons.verified,
                  Colors.purple),
                const Divider(),
                _buildPointItem(
                  'إكمال إنجاز',
                  '50 نقطة',
                  Icons.emoji_events,
                  Colors.amber),
                const Divider(),
                _buildPointItem(
                  'النشاط اليومي',
                  '1 نقطة',
                  Icons.calendar_today,
                  Colors.teal),
              ]))),
      ]);
  }

  /// بناء عنصر النقاط
  Widget _buildPointItem(String title, String points, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle),
            child: Icon(
              icon,
              color: color,
              size: 20)),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 14))),
          Text(
            points,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14)),
        ]));
  }

  /// بناء قائمة المستويات
  Widget _buildLevelsList(UserLevelModel currentLevel) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: UserLevelModel.levels.length,
      itemBuilder: (context, index) {
        final level = UserLevelModel.levels[index];
        final isCurrentLevel = level.id == currentLevel.id;
        final isLocked = _statistics!.points < level.requiredPoints;

        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: LevelCard(
            level: level,
            currentPoints: _statistics!.points,
            isCurrentLevel: isCurrentLevel,
            isLocked: isLocked,
            size: LevelCardSize.medium,
            onTap: () => _showLevelDetails(level, isCurrentLevel, isLocked)));
      });
  }

  /// عرض تفاصيل المستوى
  void _showLevelDetails(UserLevelModel level, bool isCurrentLevel, bool isLocked) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(level.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            LevelCard(
              level: level,
              currentPoints: _statistics!.points,
              isCurrentLevel: isCurrentLevel,
              isLocked: isLocked,
              size: LevelCardSize.large),
          ]),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق')),
        ]));
  }
}
