import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/constants/auth_messages.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/presentation/pages/login_page.dart';

import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// صفحة التحقق من البريد الإلكتروني مع إمكانية إعادة الإرسال
class EmailVerificationPage extends StatefulWidget {
  final String email;

  const EmailVerificationPage({super.key, required this.email});

  @override
  State<EmailVerificationPage> createState() => _EmailVerificationPageState();
}

class _EmailVerificationPageState extends State<EmailVerificationPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isLoading = false;
  int _resendCooldown = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// إعادة إرسال رسالة التحقق
  void _resendVerification() {
    if (_resendCooldown > 0) return;

    context.read<AuthBloc>().add(
      ResendVerificationRequested(email: widget.email));

    // بدء العد التنازلي
    setState(() {
      _resendCooldown = 60;
    });

    _startCooldownTimer();
  }

  /// بدء مؤقت العد التنازلي
  void _startCooldownTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _resendCooldown > 0) {
        setState(() {
          _resendCooldown--;
        });
        _startCooldownTimer();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("تحقق من البريد الإلكتروني"),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.primary),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Colors.grey.shade50,
              Colors.grey.shade100,
            ])),
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: BlocListener<AuthBloc, AuthState>(
              listener: (context, state) {
                if (state is AuthLoading) {
                  setState(() {
                    _isLoading = true;
                  });
                } else {
                  setState(() {
                    _isLoading = false;
                  });
                }

                if (state is VerificationEmailSent) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(AuthMessages.verificationEmailSent),
                      backgroundColor: AppColors.success,
                      duration: Duration(seconds: 3)));
                } else if (state is VerificationEmailFailed) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: AppColors.error,
                      duration: const Duration(seconds: 3)));
                }
              },
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Container(
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(20),
                          blurRadius: 20,
                          offset: const Offset(0, 8)),
                      ]),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // أيقونة البريد الإلكتروني
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.primary.withValues(alpha: 0.1)),
                          child: const Icon(
                            Icons.mark_email_unread,
                            size: 60,
                            color: AppColors.primary)),
                        const SizedBox(height: 32),

                        // العنوان
                        const Text(
                          "تحقق من بريدك الإلكتروني",
                          style: TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary),
                          textAlign: TextAlign.center),
                        const SizedBox(height: 16),

                        // الوصف
                        Text(
                          "تم إرسال رسالة تحقق إلى:\n${widget.email}",
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade600,
                            height: 1.5),
                          textAlign: TextAlign.center),
                        const SizedBox(height: 8),

                        const Text(
                          AuthMessages.emailVerificationInstructions,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                            height: 1.4),
                          textAlign: TextAlign.center),
                        const SizedBox(height: 40),

                        // زر إعادة الإرسال
                        SizedBox(
                          width: double.infinity,
                          height: 55,
                          child: ElevatedButton.icon(
                            onPressed: _resendCooldown > 0 || _isLoading
                                ? null
                                : _resendVerification,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors.white,
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16))),
                            icon: _isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2))
                                : const Icon(Icons.refresh, size: 20),
                            label: Text(
                              _resendCooldown > 0
                                  ? AuthMessages.resendCooldown(_resendCooldown)
                                  : AuthMessages.resendVerification,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold)))),
                        const SizedBox(height: 24),

                        // زر العودة لتسجيل الدخول
                        TextButton.icon(
                          onPressed: () {
                            Navigator.pushAndRemoveUntil(
                              context,
                              MaterialPageRoute(builder: (_) => const LoginPage()),
                              (route) => false);
                          },
                          icon: const Icon(Icons.arrow_back, size: 18),
                          label: const Text(
                            AuthMessages.goToLogin,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500)),
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.primary)),
                      ])))))))));
  }
}
