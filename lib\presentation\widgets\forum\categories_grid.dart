import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/category_model.dart';

/// شبكة عرض فئات المنتدى
class CategoriesGrid extends StatelessWidget {
  /// قائمة الفئات
  final List<CategoryModel> categories;

  /// دالة يتم استدعاؤها عند النقر على فئة
  final Function(CategoryModel) onCategoryTap;

  /// ما إذا كان يتم عرض الفئات الفرعية فقط
  final bool subCategoriesOnly;

  /// معرف الفئة الأب (إذا كان يتم عرض الفئات الفرعية فقط)
  final String? parentCategoryId;

  const CategoriesGrid({
    super.key,
    required this.categories,
    required this.onCategoryTap,
    this.subCategoriesOnly = false,
    this.parentCategoryId,
  });

  @override
  Widget build(BuildContext context) {
    // فلترة الفئات حسب الفئة الأب
    final filteredCategories = subCategoriesOnly
        ? categories.where((c) => c.parentId == parentCategoryId).toList()
        : categories.where((c) => c.parentId == null).toList();

    // ترتيب الفئات حسب الترتيب
    filteredCategories.sort((a, b) => a.order.compareTo(b.order));

    return AnimationLimiter(
      child: GridView.builder(
        padding: EdgeInsets.all(16),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16),
        itemCount: filteredCategories.length,
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredGrid(
            position: index,
            duration: const Duration(milliseconds: 375),
            columnCount: 2,
            child: ScaleAnimation(
              child: FadeInAnimation(
                child: _buildCategoryCard(context, filteredCategories[index]))));
        }));
  }

  /// بناء بطاقة الفئة
  Widget _buildCategoryCard(BuildContext context, CategoryModel category) {
    // تحديد ما إذا كانت الفئة تحتوي على فئات فرعية
    final hasSubCategories =
        category.subCategories != null && category.subCategories!.isNotEmpty;

    // تحديد لون الفئة
    final categoryColor = category.getColorObject();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: categoryColor.withOpacity(0.3),
          width: 1)),
      child: InkWell(
        onTap: () => onCategoryTap(category),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                categoryColor.withOpacity(0.1),
              ])),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: categoryColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8)),
                    child: Icon(
                      _getCategoryIcon(category.icon),
                      color: categoryColor,
                      size: 20)),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      category.name,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis)),
                ]),

              SizedBox(height: 8),

              // وصف الفئة
              Text(
                category.description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade700),
                maxLines: 2,
                overflow: TextOverflow.ellipsis),

              Spacer(),

              // إحصائيات الفئة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildStatItem(
                    Icons.topic_outlined,
                    '${category.topicsCount}',
                    'موضوع'),
                  _buildStatItem(
                    Icons.forum_outlined,
                    '${category.postsCount}',
                    'مشاركة'),
                ]),

              SizedBox(height: 8),

              // آخر مشاركة
              if (category.lastPost != null)
                _buildLastPost(context, category.lastPost!),

              // الفئات الفرعية
              if (hasSubCategories) _buildSubCategories(category),
            ]))));
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(IconData icon, String count, String label) {
    return Row(
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.grey.shade600),
        SizedBox(width: 4),
        Text(
          count,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800)),
        SizedBox(width: 2),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600)),
      ]);
  }

  /// بناء آخر مشاركة
  Widget _buildLastPost(BuildContext context, LastPost lastPost) {
    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          CircleAvatar(
            radius: 12,
            backgroundImage: lastPost.userImage != null
                ? NetworkImage(lastPost.userImage!)
                : null,
            backgroundColor: AppColors.primary.withValues(alpha: 0.2),
            child: lastPost.userImage == null
                ? Icon(Icons.person, size: 12, color: AppColors.primary)
                : null),
          SizedBox(width: 4),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  lastPost.topicTitle,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
                Text(
                  '${lastPost.userName} - ${timeago.format(lastPost.timestamp, locale: 'ar')}',
                  style: TextStyle(
                    fontSize: 8,
                    color: Colors.grey.shade600),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
              ])),
        ]));
  }

  /// بناء الفئات الفرعية
  Widget _buildSubCategories(CategoryModel category) {
    if (category.subCategories == null || category.subCategories!.isEmpty) {
      return SizedBox.shrink();
    }

    // الحصول على الفئات الفرعية
    final subCategories = categories
        .where((c) => category.subCategories!.contains(c.id))
        .toList();

    if (subCategories.isEmpty) {
      return SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8),
        Text(
          'الفئات الفرعية:',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade700)),
        SizedBox(height: 4),
        Wrap(
          spacing: 4,
          runSpacing: 4,
          children: subCategories.map((subCategory) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: subCategory.getColorObject().withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: subCategory.getColorObject().withOpacity(0.3),
                  width: 1)),
              child: Text(
                subCategory.name,
                style: TextStyle(
                  fontSize: 8,
                  color: subCategory.getColorObject())));
          }).toList()),
      ]);
  }

  /// الحصول على أيقونة الفئة
  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'home':
        return Icons.home;
      case 'business':
        return Icons.business;
      case 'apartment':
        return Icons.apartment;
      case 'house':
        return Icons.house;
      case 'villa':
        return Icons.villa;
      case 'location_city':
        return Icons.location_city;
      case 'store':
        return Icons.store;
      case 'local_offer':
        return Icons.local_offer;
      case 'attach_money':
        return Icons.attach_money;
      case 'question_answer':
        return Icons.question_answer;
      case 'forum':
        return Icons.forum;
      case 'chat':
        return Icons.chat;
      case 'info':
        return Icons.info;
      case 'help':
        return Icons.help;
      case 'announcement':
        return Icons.announcement;
      default:
        return Icons.category;
    }
  }
}
