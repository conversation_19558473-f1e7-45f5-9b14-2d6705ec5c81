import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../core/theme/app_colors.dart';

/// ورقة التعليقات على طلب العقار
class PropertyRequestCommentsSheet extends StatefulWidget {
  final String requestId;
  final String requestTitle;

  const PropertyRequestCommentsSheet({
    super.key,
    required this.requestId,
    required this.requestTitle,
  });

  @override
  State<PropertyRequestCommentsSheet> createState() => _PropertyRequestCommentsSheetState();
}

class _PropertyRequestCommentsSheetState extends State<PropertyRequestCommentsSheet> {
  final TextEditingController _commentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isLoading = false;
  List<Map<String, dynamic>> _comments = [];

  @override
  void initState() {
    super.initState();
    _loadComments();
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadComments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;

      // جلب التعليقات من Firebase
      final commentsSnapshot = await _firestore
          .collection('propertyRequestComments')
          .where('requestId', isEqualTo: widget.requestId)
          .orderBy('createdAt', descending: true)
          .get();

      final List<Map<String, dynamic>> loadedComments = [];

      for (final doc in commentsSnapshot.docs) {
        final data = doc.data();
        final commentData = {
          'id': doc.id,
          'userId': data['userId'] ?? '',
          'userName': data['userName'] ?? 'مستخدم',
          'userImage': data['userImage'],
          'content': data['content'] ?? '',
          'createdAt': (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          'likesCount': data['likesCount'] ?? 0,
          'isLiked': false, // سيتم تحديثها لاحقاً
        };

        // التحقق من إعجاب المستخدم الحالي
        if (currentUser != null) {
          final likeDoc = await _firestore
              .collection('propertyRequestComments')
              .doc(doc.id)
              .collection('likes')
              .doc(currentUser.uid)
              .get();

          commentData['isLiked'] = likeDoc.exists;
        }

        loadedComments.add(commentData);
      }

      if (mounted) {
        setState(() {
          _comments = loadedComments;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل التعليقات: $e'),
            backgroundColor: Colors.red));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _addComment() async {
    final content = _commentController.text.trim();
    if (content.isEmpty) return;

    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // جلب معلومات المستخدم من Firebase
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      final userData = userDoc.data();
      final userName = userData?['fullName'] ?? currentUser.displayName ?? 'مستخدم';
      final userImage = userData?['profileImage'] ?? currentUser.photoURL;

      // إضافة التعليق إلى Firebase
      final commentRef = await _firestore.collection('propertyRequestComments').add({
        'requestId': widget.requestId,
        'userId': currentUser.uid,
        'userName': userName,
        'userImage': userImage,
        'content': content,
        'createdAt': FieldValue.serverTimestamp(),
        'likesCount': 0,
      });

      // إضافة التعليق للقائمة المحلية
      final newComment = {
        'id': commentRef.id,
        'userId': currentUser.uid,
        'userName': userName,
        'userImage': userImage,
        'content': content,
        'createdAt': DateTime.now(),
        'likesCount': 0,
        'isLiked': false,
      };

      if (mounted) {
        setState(() {
          _comments.insert(0, newComment);
        });

        _commentController.clear();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة التعليق بنجاح'),
            backgroundColor: Colors.green));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة التعليق: $e'),
            backgroundColor: Colors.red));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.white, AppColors.orangeCardBackground],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30)),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, -10)),
        ]),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(child: _buildCommentsList()),
          _buildCommentInput(),
        ]));
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppColors.orangeGradient,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30)),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4)),
        ]),
      child: Column(
        children: [
          Container(
            width: 50,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(3))),
          const SizedBox(height: 20),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12)),
                child: const Icon(
                  Icons.comment_rounded,
                  color: Colors.white,
                  size: 24)),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'التعليقات',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white))),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12)),
                child: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.close_rounded,
                    color: Colors.white,
                    size: 24))),
            ]),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(12)),
            child: Row(
              children: [
                Icon(
                  Icons.assignment_outlined,
                  color: Colors.white.withValues(alpha: 0.9),
                  size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.requestTitle,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                      fontWeight: FontWeight.w600),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis)),
              ])),
        ]));
  }

  Widget _buildCommentsList() {
    if (_isLoading && _comments.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_comments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.comment_outlined,
              size: 64,
              color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد تعليقات بعد',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey[600])),
            const SizedBox(height: 8),
            Text(
              'كن أول من يعلق على هذا الطلب',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[500])),
          ]));
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _comments.length,
      itemBuilder: (context, index) {
        final comment = _comments[index];
        return _buildCommentItem(comment);
      });
  }

  Widget _buildCommentItem(Map<String, dynamic> comment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, AppColors.orangeCardBackground],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4)),
        ],
        border: Border.all(
          color: AppColors.primaryOrange.withValues(alpha: 0.2),
          width: 1)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    gradient: AppColors.orangeGradient,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryOrange.withValues(alpha: 0.3),
                        blurRadius: 6,
                        offset: const Offset(0, 2)),
                    ]),
                  child: CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.transparent,
                    backgroundImage: comment['userImage'] != null
                        ? NetworkImage(comment['userImage'])
                        : null,
                    child: comment['userImage'] == null
                        ? const Icon(
                            Icons.person,
                            size: 20,
                            color: Colors.white)
                        : null)),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        comment['userName'],
                        style: GoogleFonts.cairo(
                          fontWeight: FontWeight.bold,
                          fontSize: 15,
                          color: AppColors.textPrimary)),
                      const SizedBox(height: 2),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.primaryOrange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8)),
                        child: Text(
                          _formatTime(comment['createdAt']),
                          style: GoogleFonts.cairo(
                            fontSize: 11,
                            color: AppColors.primaryOrange,
                            fontWeight: FontWeight.w600))),
                    ])),
              ]),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12)),
              child: Text(
                comment['content'],
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  height: 1.6,
                  color: AppColors.textPrimary))),
            const SizedBox(height: 12),
            Row(
              children: [
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => _toggleLike(comment['id']),
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6),
                      decoration: BoxDecoration(
                        color: comment['isLiked']
                            ? Colors.red.withValues(alpha: 0.1)
                            : AppColors.primaryOrange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: comment['isLiked']
                              ? Colors.red.withValues(alpha: 0.3)
                              : AppColors.primaryOrange.withValues(alpha: 0.3),
                          width: 1)),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            comment['isLiked']
                                ? Icons.favorite
                                : Icons.favorite_border,
                            size: 16,
                            color: comment['isLiked']
                                ? Colors.red
                                : AppColors.primaryOrange),
                          const SizedBox(width: 6),
                          Text(
                            '${comment['likesCount']}',
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: comment['isLiked']
                                  ? Colors.red
                                  : AppColors.primaryOrange)),
                        ])))),
                const SizedBox(width: 12),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => _replyToComment(comment),
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6),
                      decoration: BoxDecoration(
                        color: AppColors.primaryOrange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: AppColors.primaryOrange.withValues(alpha: 0.3),
                          width: 1)),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.reply_rounded,
                            size: 16,
                            color: AppColors.primaryOrange),
                          const SizedBox(width: 6),
                          Text(
                            'رد',
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: AppColors.primaryOrange)),
                        ])))),
              ]),
          ])));
  }

  Widget _buildCommentInput() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, AppColors.orangeCardBackground],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withValues(alpha: 0.15),
            blurRadius: 15,
            offset: const Offset(0, -8)),
        ]),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryOrange.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2)),
                ],
                border: Border.all(
                  color: AppColors.primaryOrange.withValues(alpha: 0.2),
                  width: 1)),
              child: TextField(
                controller: _commentController,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.textPrimary),
                decoration: InputDecoration(
                  hintText: 'اكتب تعليقك...',
                  hintStyle: GoogleFonts.cairo(
                    color: AppColors.textSecondary),
                  prefixIcon: Container(
                    margin: const EdgeInsets.all(8),
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      gradient: AppColors.orangeGradient,
                      borderRadius: BorderRadius.circular(15)),
                    child: const Icon(
                      Icons.edit_rounded,
                      color: Colors.white,
                      size: 16)),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25),
                    borderSide: BorderSide.none),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12)),
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _addComment()))),
          const SizedBox(width: 12),
          Container(
            decoration: BoxDecoration(
              gradient: AppColors.orangeGradient,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryOrange.withValues(alpha: 0.4),
                  blurRadius: 10,
                  offset: const Offset(0, 4)),
              ]),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _isLoading ? null : _addComment,
                borderRadius: BorderRadius.circular(25),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
                      : const Icon(
                          Icons.send_rounded,
                          color: Colors.white,
                          size: 20))))),
        ]));
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _toggleLike(String commentId) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    final commentIndex = _comments.indexWhere((c) => c['id'] == commentId);
    if (commentIndex == -1) return;

    final comment = _comments[commentIndex];
    final isLiked = comment['isLiked'] as bool;

    try {
      final likeRef = _firestore
          .collection('propertyRequestComments')
          .doc(commentId)
          .collection('likes')
          .doc(currentUser.uid);

      if (isLiked) {
        // إزالة الإعجاب
        await likeRef.delete();
        await _firestore.collection('propertyRequestComments').doc(commentId).update({
          'likesCount': FieldValue.increment(-1),
        });
      } else {
        // إضافة الإعجاب
        await likeRef.set({
          'userId': currentUser.uid,
          'createdAt': FieldValue.serverTimestamp(),
        });
        await _firestore.collection('propertyRequestComments').doc(commentId).update({
          'likesCount': FieldValue.increment(1),
        });
      }

      // تحديث الواجهة
      if (mounted) {
        setState(() {
          comment['isLiked'] = !isLiked;
          comment['likesCount'] = (comment['likesCount'] as int) + (isLiked ? -1 : 1);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الإعجاب: $e'),
            backgroundColor: Colors.red));
      }
    }
  }

  void _replyToComment(Map<String, dynamic> comment) {
    _commentController.text = '@${comment['userName']} ';
    _commentController.selection = TextSelection.fromPosition(
      TextPosition(offset: _commentController.text.length));
  }
}
