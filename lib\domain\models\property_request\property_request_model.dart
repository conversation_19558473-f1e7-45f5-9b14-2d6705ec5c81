// lib/domain/models/property_request/property_request_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

import '../forum/topic_model.dart';

/// حالة طلب العقار
enum RequestStatus {
  /// مفتوح
  open,

  /// مغلق
  closed,

  /// تم حله
  resolved
}

/// نموذج طلب عقار
class PropertyRequestModel extends Equatable {
  /// معرف الطلب
  final String id;

  /// عنوان الطلب
  final String title;

  /// وصف الطلب
  final String description;

  /// معرف المستخدم
  final String userId;

  /// اسم المستخدم
  final String userName;

  /// صورة المستخدم
  final String? userImage;

  /// نوع العقار المطلوب
  final String propertyType;

  /// الحد الأدنى للسعر
  final double? minPrice;

  /// الحد الأقصى للسعر
  final double? maxPrice;

  /// المناطق المفضلة
  final List<String> preferredLocations;

  /// الحد الأدنى لعدد الغرف
  final int? minRooms;

  /// الحد الأدنى لعدد الحمامات
  final int? minBathrooms;

  /// الحد الأدنى للمساحة
  final double? minArea;

  /// هل يشترط وجود تكييف مركزي
  final bool hasCentralAC;

  /// هل يشترط وجود غرفة خادمة
  final bool hasMaidRoom;

  /// هل يشترط وجود مرآب
  final bool hasGarage;

  /// هل يشترط وجود مسبح
  final bool hasSwimmingPool;

  /// هل يشترط وجود مصعد
  final bool hasElevator;

  /// هل يشترط أن يكون مفروش بالكامل
  final bool isFullyFurnished;

  /// متطلبات إضافية
  final String? additionalRequirements;

  /// تاريخ الحاجة للعقار
  final DateTime? neededBy;

  /// حالة الطلب
  final RequestStatus status;

  /// عدد العروض
  final int offersCount;

  /// عدد المشاهدات
  final int viewsCount;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// تاريخ التحديث
  final DateTime updatedAt;

  /// الصور
  final List<String>? images;

  /// المرفقات
  final List<Map<String, dynamic>>? attachments;

  /// الكلمات المفتاحية
  final List<String>? tags;

  const PropertyRequestModel({
    required this.id,
    required this.title,
    required this.description,
    required this.userId,
    required this.userName,
    this.userImage,
    required this.propertyType,
    this.minPrice,
    this.maxPrice,
    required this.preferredLocations,
    this.minRooms,
    this.minBathrooms,
    this.minArea,
    this.hasCentralAC = false,
    this.hasMaidRoom = false,
    this.hasGarage = false,
    this.hasSwimmingPool = false,
    this.hasElevator = false,
    this.isFullyFurnished = false,
    this.additionalRequirements,
    this.neededBy,
    this.status = RequestStatus.open,
    this.offersCount = 0,
    this.viewsCount = 0,
    required this.createdAt,
    required this.updatedAt,
    this.images,
    this.attachments,
    this.tags,
  });

  /// إنشاء نسخة معدلة من الطلب
  PropertyRequestModel copyWith({
    String? id,
    String? title,
    String? description,
    String? userId,
    String? userName,
    String? userImage,
    String? propertyType,
    double? minPrice,
    double? maxPrice,
    List<String>? preferredLocations,
    int? minRooms,
    int? minBathrooms,
    double? minArea,
    bool? hasCentralAC,
    bool? hasMaidRoom,
    bool? hasGarage,
    bool? hasSwimmingPool,
    bool? hasElevator,
    bool? isFullyFurnished,
    String? additionalRequirements,
    DateTime? neededBy,
    RequestStatus? status,
    int? offersCount,
    int? viewsCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? images,
    List<Map<String, dynamic>>? attachments,
    List<String>? tags,
  }) {
    return PropertyRequestModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      propertyType: propertyType ?? this.propertyType,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      preferredLocations: preferredLocations ?? this.preferredLocations,
      minRooms: minRooms ?? this.minRooms,
      minBathrooms: minBathrooms ?? this.minBathrooms,
      minArea: minArea ?? this.minArea,
      hasCentralAC: hasCentralAC ?? this.hasCentralAC,
      hasMaidRoom: hasMaidRoom ?? this.hasMaidRoom,
      hasGarage: hasGarage ?? this.hasGarage,
      hasSwimmingPool: hasSwimmingPool ?? this.hasSwimmingPool,
      hasElevator: hasElevator ?? this.hasElevator,
      isFullyFurnished: isFullyFurnished ?? this.isFullyFurnished,
      additionalRequirements: additionalRequirements ?? this.additionalRequirements,
      neededBy: neededBy ?? this.neededBy,
      status: status ?? this.status,
      offersCount: offersCount ?? this.offersCount,
      viewsCount: viewsCount ?? this.viewsCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      images: images ?? this.images,
      attachments: attachments ?? this.attachments,
      tags: tags ?? this.tags);
  }

  /// تحويل الطلب إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'propertyType': propertyType,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'preferredLocations': preferredLocations,
      'minRooms': minRooms,
      'minBathrooms': minBathrooms,
      'minArea': minArea,
      'hasCentralAC': hasCentralAC,
      'hasMaidRoom': hasMaidRoom,
      'hasGarage': hasGarage,
      'hasSwimmingPool': hasSwimmingPool,
      'hasElevator': hasElevator,
      'isFullyFurnished': isFullyFurnished,
      'additionalRequirements': additionalRequirements,
      'neededBy': neededBy?.millisecondsSinceEpoch,
      'status': status.index,
      'offersCount': offersCount,
      'viewsCount': viewsCount,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'images': images,
      'attachments': attachments,
      'tags': tags,
    };
  }

  /// إنشاء طلب من خريطة
  factory PropertyRequestModel.fromMap(Map<String, dynamic> map) {
    return PropertyRequestModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'],
      propertyType: map['propertyType'] ?? '',
      minPrice: map['minPrice']?.toDouble(),
      maxPrice: map['maxPrice']?.toDouble(),
      preferredLocations: List<String>.from(map['preferredLocations'] ?? []),
      minRooms: map['minRooms'],
      minBathrooms: map['minBathrooms'],
      minArea: map['minArea']?.toDouble(),
      hasCentralAC: map['hasCentralAC'] ?? false,
      hasMaidRoom: map['hasMaidRoom'] ?? false,
      hasGarage: map['hasGarage'] ?? false,
      hasSwimmingPool: map['hasSwimmingPool'] ?? false,
      hasElevator: map['hasElevator'] ?? false,
      isFullyFurnished: map['isFullyFurnished'] ?? false,
      additionalRequirements: map['additionalRequirements'],
      neededBy: map['neededBy'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['neededBy'])
          : null,
      status: RequestStatus.values[map['status'] ?? 0],
      offersCount: map['offersCount'] ?? 0,
      viewsCount: map['viewsCount'] ?? 0,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      images: map['images'] != null ? List<String>.from(map['images']) : null,
      attachments: map['attachments'] != null
          ? List<Map<String, dynamic>>.from(
              map['attachments']?.map((x) => Map<String, dynamic>.from(x)))
          : null,
      tags: map['tags'] != null ? List<String>.from(map['tags']) : null);
  }

  /// إنشاء طلب من وثيقة Firestore
  factory PropertyRequestModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      throw Exception('Document data was null');
    }

    return PropertyRequestModel.fromMap({
      'id': doc.id,
      ...data,
    });
  }

  /// تحويل الطلب إلى موضوع منتدى
  TopicModel toTopicModel() {
    return TopicModel(
      id: id,
      categoryId: 'property_requests',
      categoryName: 'طلبات العقارات',
      title: title,
      content: description,
      userId: userId,
      userName: userName,
      userImage: userImage,
      status: TopicStatus.open,
      type: TopicType.propertyRequest,
      createdAt: createdAt,
      updatedAt: updatedAt,
      images: images,
      attachments: attachments,
      tags: tags,
      // Store property request data in a map
      location: {
        'propertyType': propertyType,
        'minPrice': minPrice,
        'maxPrice': maxPrice,
        'preferredLocations': preferredLocations,
        'minRooms': minRooms,
        'minBathrooms': minBathrooms,
        'minArea': minArea,
        'hasCentralAC': hasCentralAC,
        'hasMaidRoom': hasMaidRoom,
        'hasGarage': hasGarage,
        'hasSwimmingPool': hasSwimmingPool,
        'hasElevator': hasElevator,
        'isFullyFurnished': isFullyFurnished,
        'additionalRequirements': additionalRequirements,
        'neededBy': neededBy?.millisecondsSinceEpoch,
        'requestStatus': status.index,
      });
  }

  /// Create property request from topic model
  factory PropertyRequestModel.fromTopicModel(TopicModel topic) {
    final location = topic.location ?? {};

    return PropertyRequestModel(
      id: topic.id,
      title: topic.title,
      description: topic.content,
      userId: topic.userId,
      userName: topic.userName,
      userImage: topic.userImage,
      propertyType: location['propertyType'] ?? '',
      minPrice: location['minPrice']?.toDouble(),
      maxPrice: location['maxPrice']?.toDouble(),
      preferredLocations: location['preferredLocations'] != null
          ? List<String>.from(location['preferredLocations'])
          : [],
      minRooms: location['minRooms'],
      minBathrooms: location['minBathrooms'],
      minArea: location['minArea']?.toDouble(),
      hasCentralAC: location['hasCentralAC'] ?? false,
      hasMaidRoom: location['hasMaidRoom'] ?? false,
      hasGarage: location['hasGarage'] ?? false,
      hasSwimmingPool: location['hasSwimmingPool'] ?? false,
      hasElevator: location['hasElevator'] ?? false,
      isFullyFurnished: location['isFullyFurnished'] ?? false,
      additionalRequirements: location['additionalRequirements'],
      neededBy: location['neededBy'] != null
          ? DateTime.fromMillisecondsSinceEpoch(location['neededBy'])
          : null,
      status: location['requestStatus'] != null
          ? RequestStatus.values[location['requestStatus']]
          : RequestStatus.open,
      offersCount: topic.repliesCount,
      viewsCount: topic.viewsCount,
      createdAt: topic.createdAt,
      updatedAt: topic.updatedAt,
      images: topic.images,
      attachments: topic.attachments,
      tags: topic.tags);
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        userId,
        userName,
        userImage,
        propertyType,
        minPrice,
        maxPrice,
        preferredLocations,
        minRooms,
        minBathrooms,
        minArea,
        hasCentralAC,
        hasMaidRoom,
        hasGarage,
        hasSwimmingPool,
        hasElevator,
        isFullyFurnished,
        additionalRequirements,
        neededBy,
        status,
        offersCount,
        viewsCount,
        createdAt,
        updatedAt,
        images,
        attachments,
        tags,
      ];
}
