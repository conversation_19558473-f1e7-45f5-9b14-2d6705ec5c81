import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../core/theme/app_colors.dart';
import '../../domain/entities/estate.dart';
import '../../domain/services/premium_ads_manager_service.dart';

class PremiumAdsDashboard extends StatefulWidget {
  const PremiumAdsDashboard({super.key});

  @override
  State<PremiumAdsDashboard> createState() => _PremiumAdsDashboardState();
}

class _PremiumAdsDashboardState extends State<PremiumAdsDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final PremiumAdsManagerService _managerService = PremiumAdsManagerService();

  // بيانات لوحة التحكم
  Map<String, dynamic> _dashboardStats = {};
  List<Estate> _premiumAds = [];
  Map<String, dynamic> _performanceReport = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadDashboardData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل بيانات لوحة التحكم
  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // جلب البيانات بشكل متوازي
      final futures = await Future.wait([
        _managerService.getPremiumAdsStats('current_user'), // سيتم استبدالها بـ user ID الحقيقي
        _managerService.getUserPremiumAds('current_user'),
        _managerService.getPremiumAdsPerformanceReport('current_user'),
      ]);

      setState(() {
        _dashboardStats = futures[0] as Map<String, dynamic>;
        _premiumAds = futures[1] as List<Estate>;
        _performanceReport = futures[2] as Map<String, dynamic>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'حدث خطأ في تحميل البيانات: $e',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'لوحة تحكم الإعلانات المدفوعة',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'نظرة عامة'),
            Tab(text: 'إعلاناتي'),
            Tab(text: 'الأداء'),
            Tab(text: 'التوصيات'),
          ]),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData),
          IconButton(
            icon: const Icon(Icons.add_circle),
            onPressed: _showUpgradeOptions),
        ]),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildMyAdsTab(),
                _buildPerformanceTab(),
                _buildRecommendationsTab(),
              ]));
  }

  /// بناء تبويب النظرة العامة
  Widget _buildOverviewTab() {
    return RefreshIndicator(
      onRefresh: _loadDashboardData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقات الإحصائيات الرئيسية
            _buildStatsCards(),
            const SizedBox(height: 24),

            // الرسم البياني للأداء
            _buildPerformanceChart(),
            const SizedBox(height: 24),

            // توزيع الميزات المدفوعة
            _buildFeatureDistribution(),
            const SizedBox(height: 24),

            // أفضل الإعلانات أداءً
            _buildTopPerformingAds(),
          ])));
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatsCards() {
    final stats = [
      {
        'title': 'إجمالي الإعلانات',
        'value': _dashboardStats['totalAds']?.toString() ?? '0',
        'icon': Icons.campaign,
        'color': Colors.blue,
      },
      {
        'title': 'إجمالي المشاهدات',
        'value': NumberFormat('#,###').format(_dashboardStats['totalViews'] ?? 0),
        'icon': Icons.visibility,
        'color': Colors.green,
      },
      {
        'title': 'إجمالي النقرات',
        'value': NumberFormat('#,###').format(_dashboardStats['totalClicks'] ?? 0),
        'icon': Icons.mouse,
        'color': Colors.orange,
      },
      {
        'title': 'معدل النقر',
        'value': '${(_dashboardStats['ctr'] ?? 0).toStringAsFixed(1)}%',
        'icon': Icons.trending_up,
        'color': Colors.purple,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.5),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return _buildStatCard(
          title: stat['title'] as String,
          value: stat['value'] as String,
          icon: stat['icon'] as IconData,
          color: stat['color'] as Color);
      });
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(
                  icon,
                  color: color,
                  size: 24)),
              Icon(
                Icons.trending_up,
                color: Colors.green,
                size: 16),
            ]),
          const Spacer(),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color)),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600)),
        ]));
  }

  /// بناء الرسم البياني للأداء
  Widget _buildPerformanceChart() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: AppColors.primary,
                size: 20),
              const SizedBox(width: 8),
              Text(
                'أداء الإعلانات (آخر 7 أيام)',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(show: true),
                titlesData: FlTitlesData(show: false),
                borderData: FlBorderData(show: false),
                lineBarsData: [
                  // خط المشاهدات
                  LineChartBarData(
                    spots: _generateViewsData(),
                    isCurved: true,
                    color: Colors.blue,
                    barWidth: 3,
                    dotData: FlDotData(show: false),
                    belowBarData: BarAreaData(
                      show: true,
                      color: Colors.blue.withOpacity(0.1))),
                  // خط النقرات
                  LineChartBarData(
                    spots: _generateClicksData(),
                    isCurved: true,
                    color: Colors.orange,
                    barWidth: 3,
                    dotData: FlDotData(show: false)),
                ]))),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildChartLegend('المشاهدات', Colors.blue),
              _buildChartLegend('النقرات', Colors.orange),
            ]),
        ]));
  }

  /// بناء وسيلة إيضاح الرسم البياني
  Widget _buildChartLegend(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle)),
        const SizedBox(width: 4),
        Text(
          label,
          style: GoogleFonts.cairo(fontSize: 12)),
      ]);
  }

  /// إنشاء بيانات المشاهدات للرسم البياني من Firebase
  List<FlSpot> _generateViewsData() {
    // TODO: جلب البيانات الحقيقية من Firebase
    // يجب استبدال هذا بجلب بيانات المشاهدات الفعلية من قاعدة البيانات
    return [
      const FlSpot(0, 50),
      const FlSpot(1, 75),
      const FlSpot(2, 60),
      const FlSpot(3, 90),
      const FlSpot(4, 85),
      const FlSpot(5, 110),
      const FlSpot(6, 95),
    ];
  }

  /// إنشاء بيانات النقرات للرسم البياني
  List<FlSpot> _generateClicksData() {
    return [
      const FlSpot(0, 5),
      const FlSpot(1, 8),
      const FlSpot(2, 6),
      const FlSpot(3, 12),
      const FlSpot(4, 10),
      const FlSpot(5, 15),
      const FlSpot(6, 13),
    ];
  }

  /// بناء توزيع الميزات المدفوعة
  Widget _buildFeatureDistribution() {
    final featureBreakdown = _performanceReport['featureBreakdown'] as Map<String, dynamic>? ?? {};

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.pie_chart,
                color: AppColors.primary,
                size: 20),
              const SizedBox(width: 8),
              Text(
                'توزيع الميزات المدفوعة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 16),
          if (featureBreakdown.isEmpty)
            Center(
              child: Text(
                'لا توجد ميزات مدفوعة نشطة',
                style: GoogleFonts.cairo(
                  color: Colors.grey.shade600)))
          else
            ...featureBreakdown.entries.map((entry) {
              return _buildFeatureItem(entry.key, entry.value);
            }),
        ]));
  }

  /// بناء عنصر ميزة
  Widget _buildFeatureItem(String feature, int count) {
    final featureNames = {
      'vip': 'VIP',
      'featured': 'مميز',
      'pinned': 'مثبت',
      'pinnedHome': 'مثبت في الرئيسية',
      'moving': 'متحرك',
    };

    final featureColors = {
      'vip': Colors.amber,
      'featured': Colors.orange,
      'pinned': Colors.blue,
      'pinnedHome': Colors.green,
      'moving': Colors.purple,
    };

    final featureName = featureNames[feature] ?? feature;
    final color = featureColors[feature] ?? Colors.grey;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3))),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              featureName,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w500))),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12)),
            child: Text(
              count.toString(),
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white))),
        ]));
  }

  /// بناء أفضل الإعلانات أداءً
  Widget _buildTopPerformingAds() {
    final topAds = _performanceReport['topPerformingAds'] as List<dynamic>? ?? [];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star,
                color: AppColors.primary,
                size: 20),
              const SizedBox(width: 8),
              Text(
                'أفضل الإعلانات أداءً',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 16),
          if (topAds.isEmpty)
            Center(
              child: Text(
                'لا توجد بيانات أداء متاحة',
                style: GoogleFonts.cairo(
                  color: Colors.grey.shade600)))
          else
            ...topAds.take(3).map((ad) {
              return _buildTopAdItem(ad as Map<String, dynamic>);
            }),
        ]));
  }

  /// بناء عنصر أفضل إعلان
  Widget _buildTopAdItem(Map<String, dynamic> ad) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8)),
            child: Icon(
              Icons.trending_up,
              color: AppColors.primary,
              size: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  ad['title'] ?? 'إعلان',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '${ad['views']} مشاهدة',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600)),
                    const SizedBox(width: 8),
                    Text(
                      '${ad['clicks']} نقرة',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600)),
                  ]),
              ])),
          Text(
            '${(ad['ctr'] ?? 0).toStringAsFixed(1)}%',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.primary)),
        ]));
  }

  /// بناء تبويب إعلاناتي
  Widget _buildMyAdsTab() {
    // TODO: Implement my ads tab
    return const Center(
      child: Text('تبويب إعلاناتي - قيد التطوير'));
  }

  /// بناء تبويب الأداء
  Widget _buildPerformanceTab() {
    // TODO: Implement performance tab
    return const Center(
      child: Text('تبويب الأداء - قيد التطوير'));
  }

  /// بناء تبويب التوصيات
  Widget _buildRecommendationsTab() {
    final recommendations = _performanceReport['recommendations'] as List<dynamic>? ?? [];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'توصيات لتحسين الأداء',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          if (recommendations.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 80,
                    color: Colors.green),
                  const SizedBox(height: 16),
                  Text(
                    'ممتاز! لا توجد توصيات حالياً',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.green)),
                ]))
          else
            ...recommendations.map((recommendation) {
              return _buildRecommendationCard(recommendation as String);
            }),
        ]));
  }

  /// بناء بطاقة توصية
  Widget _buildRecommendationCard(String recommendation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8)),
            child: Icon(
              Icons.lightbulb,
              color: Colors.orange,
              size: 24)),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              recommendation,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade800))),
        ]));
  }

  /// عرض خيارات الترقية
  void _showUpgradeOptions() {
    // TODO: Implement upgrade options dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم تفعيل خيارات الترقية قريباً',
          style: GoogleFonts.cairo())));
  }
}
