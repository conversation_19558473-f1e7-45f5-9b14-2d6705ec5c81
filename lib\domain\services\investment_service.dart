import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';


class InvestmentService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// الحصول على ملخص المحفظة الاستثمارية
  Future<Map<String, dynamic>> getPortfolioSummary() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return {};

      // جلب الاستثمارات
      final investmentsSnapshot = await _firestore
          .collection('investments')
          .where('investorId', isEqualTo: user.uid)
          .get();

      double totalValue = 0;
      double totalInitialValue = 0;
      double monthlyIncome = 0;

      for (final doc in investmentsSnapshot.docs) {
        final data = doc.data();
        totalValue += (data['currentValue'] as num?)?.toDouble() ?? 0;
        totalInitialValue += (data['initialValue'] as num?)?.toDouble() ?? 0;
        monthlyIncome += (data['monthlyIncome'] as num?)?.toDouble() ?? 0;
      }

      final totalReturn = totalValue - totalInitialValue;
      final returnPercentage = totalInitialValue > 0 
          ? (totalReturn / totalInitialValue) * 100 
          : 0;

      return {
        'totalValue': totalValue,
        'totalInitialValue': totalInitialValue,
        'totalReturn': totalReturn,
        'returnPercentage': returnPercentage,
        'monthlyIncome': monthlyIncome,
        'investmentCount': investmentsSnapshot.docs.length,
      };
    } catch (e) {
      print('Error getting portfolio summary: $e');
      return {};
    }
  }

  /// الحصول على استثمارات المستخدم
  Future<List<Map<String, dynamic>>> getMyInvestments() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('investments')
          .where('investorId', isEqualTo: user.uid)
          .orderBy('createdAt', descending: true)
          .get();

      final investments = <Map<String, dynamic>>[];

      for (final doc in snapshot.docs) {
        final data = doc.data();
        data['id'] = doc.id;

        // جلب تفاصيل العقار المرتبط
        if (data['propertyId'] != null) {
          final propertyDoc = await _firestore
              .collection('estates')
              .doc(data['propertyId'])
              .get();

          if (propertyDoc.exists) {
            final propertyData = propertyDoc.data()!;
            data['title'] = propertyData['title'];
            data['location'] = propertyData['location'];
            data['propertyType'] = propertyData['propertyType'];
            data['photoUrls'] = propertyData['photoUrls'];
          }
        }

        // حساب العائد
        final initialValue = (data['initialValue'] as num?)?.toDouble() ?? 0;
        final currentValue = (data['currentValue'] as num?)?.toDouble() ?? 0;
        final returnAmount = currentValue - initialValue;
        final returnPercentage = initialValue > 0 ? (returnAmount / initialValue) * 100 : 0;

        data['returnAmount'] = returnAmount;
        data['returnPercentage'] = returnPercentage;

        investments.add(data);
      }

      return investments;
    } catch (e) {
      print('Error getting my investments: $e');
      return [];
    }
  }

  /// الحصول على الفرص الاستثمارية
  Future<List<Map<String, dynamic>>> getInvestmentOpportunities() async {
    try {
      // جلب العقارات المناسبة للاستثمار
      final snapshot = await _firestore
          .collection('estates')
          .where('isInvestmentOpportunity', isEqualTo: true)
          .where('isAvailable', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(20)
          .get();

      final opportunities = <Map<String, dynamic>>[];

      for (final doc in snapshot.docs) {
        final data = doc.data();
        data['id'] = doc.id;

        // حساب العائد المتوقع
        final price = (data['price'] as num?)?.toDouble() ?? 0;
        final expectedRent = (data['expectedRent'] as num?)?.toDouble() ?? 0;
        final expectedReturn = expectedRent > 0 ? (expectedRent * 12 / price) * 100 : 0;

        data['expectedReturn'] = expectedReturn;
        data['riskLevel'] = _calculateRiskLevel(data);
        data['investmentScore'] = _calculateInvestmentScore(data);

        opportunities.add(data);
      }

      // ترتيب حسب نقاط الاستثمار
      opportunities.sort((a, b) => 
          (b['investmentScore'] as double).compareTo(a['investmentScore'] as double));

      return opportunities;
    } catch (e) {
      print('Error getting investment opportunities: $e');
      return [];
    }
  }

  /// حساب مستوى المخاطر
  String _calculateRiskLevel(Map<String, dynamic> propertyData) {
    int riskScore = 0;

    // عمر البناء
    final buildingAge = propertyData['buildingAge'] as String?;
    if (buildingAge != null) {
      if (buildingAge.contains('جديد') || buildingAge.contains('أقل من 5')) {
        riskScore += 1;
      } else if (buildingAge.contains('5-10') || buildingAge.contains('10-15')) {
        riskScore += 2;
      } else {
        riskScore += 3;
      }
    }

    // الموقع
    final location = propertyData['location'] as String? ?? '';
    final premiumAreas = ['الجابرية', 'السالمية', 'حولي', 'الفنطاس'];
    if (premiumAreas.any((area) => location.contains(area))) {
      riskScore += 1;
    } else {
      riskScore += 2;
    }

    // السعر
    final price = (propertyData['price'] as num?)?.toDouble() ?? 0;
    if (price > 200000) {
      riskScore += 2;
    } else if (price > 100000) {
      riskScore += 1;
    }

    if (riskScore <= 3) return 'منخفض';
    if (riskScore <= 5) return 'متوسط';
    return 'مرتفع';
  }

  /// حساب نقاط الاستثمار
  double _calculateInvestmentScore(Map<String, dynamic> propertyData) {
    double score = 0;

    // العائد المتوقع
    final expectedReturn = (propertyData['expectedReturn'] as num?)?.toDouble() ?? 0;
    score += expectedReturn * 2;

    // الموقع
    final location = propertyData['location'] as String? ?? '';
    final premiumAreas = ['الجابرية', 'السالمية', 'حولي', 'الفنطاس'];
    if (premiumAreas.any((area) => location.contains(area))) {
      score += 20;
    }

    // نوع العقار
    final propertyType = propertyData['propertyType'] as String? ?? '';
    if (propertyType == 'شقة') {
      score += 15;
    } else if (propertyType == 'فيلا') {
      score += 10;
    }

    // المرافق
    if (propertyData['hasCentralAC'] == true) score += 5;
    if (propertyData['hasElevator'] == true) score += 5;
    if (propertyData['hasGarage'] == true) score += 3;

    // عمر البناء
    final buildingAge = propertyData['buildingAge'] as String?;
    if (buildingAge != null && (buildingAge.contains('جديد') || buildingAge.contains('أقل من 5'))) {
      score += 10;
    }

    return score;
  }

  /// إضافة استثمار جديد
  Future<bool> addInvestment({
    required String propertyId,
    required double initialValue,
    required double expectedReturn,
    String? notes,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من وجود العقار
      final propertyDoc = await _firestore
          .collection('estates')
          .doc(propertyId)
          .get();

      if (!propertyDoc.exists) {
        throw Exception('العقار غير موجود');
      }

      // إضافة الاستثمار
      await _firestore.collection('investments').add({
        'investorId': user.uid,
        'propertyId': propertyId,
        'initialValue': initialValue,
        'currentValue': initialValue, // نفس القيمة الأولية في البداية
        'expectedReturn': expectedReturn,
        'monthlyIncome': 0, // سيتم تحديثه لاحقاً
        'totalReturn': 0,
        'status': 'active',
        'notes': notes,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error adding investment: $e');
      rethrow;
    }
  }

  /// تحديث قيمة الاستثمار
  Future<bool> updateInvestmentValue(String investmentId, double newValue) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من ملكية الاستثمار
      final investmentDoc = await _firestore
          .collection('investments')
          .doc(investmentId)
          .get();

      if (!investmentDoc.exists) {
        throw Exception('الاستثمار غير موجود');
      }

      final investmentData = investmentDoc.data()!;
      if (investmentData['investorId'] != user.uid) {
        throw Exception('ليس لديك صلاحية لتعديل هذا الاستثمار');
      }

      // حساب العائد الجديد
      final initialValue = (investmentData['initialValue'] as num).toDouble();
      final totalReturn = newValue - initialValue;

      // تحديث الاستثمار
      await _firestore.collection('investments').doc(investmentId).update({
        'currentValue': newValue,
        'totalReturn': totalReturn,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error updating investment value: $e');
      rethrow;
    }
  }

  /// إضافة دخل شهري
  Future<bool> addMonthlyIncome(String investmentId, double income) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من ملكية الاستثمار
      final investmentDoc = await _firestore
          .collection('investments')
          .doc(investmentId)
          .get();

      if (!investmentDoc.exists) {
        throw Exception('الاستثمار غير موجود');
      }

      final investmentData = investmentDoc.data()!;
      if (investmentData['investorId'] != user.uid) {
        throw Exception('ليس لديك صلاحية لتعديل هذا الاستثمار');
      }

      // إضافة سجل الدخل
      await _firestore
          .collection('investments')
          .doc(investmentId)
          .collection('incomeHistory')
          .add({
        'amount': income,
        'date': FieldValue.serverTimestamp(),
        'type': 'rental', // يمكن أن يكون rental, sale, etc.
        'notes': '',
      });

      // تحديث إجمالي الدخل الشهري
      final currentMonthlyIncome = (investmentData['monthlyIncome'] as num?)?.toDouble() ?? 0;
      await _firestore.collection('investments').doc(investmentId).update({
        'monthlyIncome': currentMonthlyIncome + income,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error adding monthly income: $e');
      rethrow;
    }
  }

  /// حساب العائد على الاستثمار (ROI)
  Future<Map<String, dynamic>> calculateROI(String investmentId) async {
    try {
      final investmentDoc = await _firestore
          .collection('investments')
          .doc(investmentId)
          .get();

      if (!investmentDoc.exists) {
        throw Exception('الاستثمار غير موجود');
      }

      final data = investmentDoc.data()!;
      final initialValue = (data['initialValue'] as num).toDouble();
      final currentValue = (data['currentValue'] as num).toDouble();
      final monthlyIncome = (data['monthlyIncome'] as num?)?.toDouble() ?? 0;

      // حساب إجمالي الدخل من التاريخ
      final incomeSnapshot = await _firestore
          .collection('investments')
          .doc(investmentId)
          .collection('incomeHistory')
          .get();

      double totalIncome = 0;
      for (final doc in incomeSnapshot.docs) {
        totalIncome += (doc.data()['amount'] as num).toDouble();
      }

      // حساب العائد الإجمالي
      final totalReturn = (currentValue - initialValue) + totalIncome;
      final roiPercentage = (totalReturn / initialValue) * 100;

      // حساب العائد السنوي
      final createdAt = (data['createdAt'] as Timestamp).toDate();
      final daysSinceCreation = DateTime.now().difference(createdAt).inDays;
      final yearsSinceCreation = daysSinceCreation / 365.0;
      final annualizedReturn = yearsSinceCreation > 0 ? roiPercentage / yearsSinceCreation : 0;

      return {
        'totalReturn': totalReturn,
        'roiPercentage': roiPercentage,
        'annualizedReturn': annualizedReturn,
        'totalIncome': totalIncome,
        'capitalGain': currentValue - initialValue,
        'monthlyIncome': monthlyIncome,
        'daysSinceCreation': daysSinceCreation,
      };
    } catch (e) {
      print('Error calculating ROI: $e');
      return {};
    }
  }

  /// تحليل السوق للاستثمار
  Future<Map<String, dynamic>> getMarketAnalysis(String location, String propertyType) async {
    try {
      // جلب العقارات المشابهة في نفس المنطقة
      final snapshot = await _firestore
          .collection('estates')
          .where('location', isEqualTo: location)
          .where('propertyType', isEqualTo: propertyType)
          .get();

      if (snapshot.docs.isEmpty) {
        return {
          'averagePrice': 0,
          'averagePricePerSqm': 0,
          'totalProperties': 0,
          'priceRange': {'min': 0, 'max': 0},
        };
      }

      final prices = <double>[];
      final pricesPerSqm = <double>[];

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final price = (data['price'] as num?)?.toDouble() ?? 0;
        final area = (data['area'] as num?)?.toDouble() ?? 1;
        
        if (price > 0) {
          prices.add(price);
          pricesPerSqm.add(price / area);
        }
      }

      if (prices.isEmpty) {
        return {
          'averagePrice': 0,
          'averagePricePerSqm': 0,
          'totalProperties': 0,
          'priceRange': {'min': 0, 'max': 0},
        };
      }

      prices.sort();
      pricesPerSqm.sort();

      final averagePrice = prices.reduce((a, b) => a + b) / prices.length;
      final averagePricePerSqm = pricesPerSqm.reduce((a, b) => a + b) / pricesPerSqm.length;

      return {
        'averagePrice': averagePrice,
        'averagePricePerSqm': averagePricePerSqm,
        'totalProperties': prices.length,
        'priceRange': {
          'min': prices.first,
          'max': prices.last,
        },
        'medianPrice': prices[prices.length ~/ 2],
        'location': location,
        'propertyType': propertyType,
      };
    } catch (e) {
      print('Error getting market analysis: $e');
      return {};
    }
  }

  /// الحصول على توقعات السوق
  Future<Map<String, dynamic>> getMarketForecast(String location) async {
    try {
      // هذه دالة تجريبية - في التطبيق الحقيقي يمكن ربطها بـ APIs خارجية
      // أو استخدام خوارزميات التعلم الآلي لتحليل البيانات التاريخية

      final analysisData = await getMarketAnalysis(location, 'شقة');
      final averagePrice = analysisData['averagePrice'] as double;

      // توقعات تجريبية بناءً على متوسط نمو السوق
      final growthRate = 0.05; // 5% نمو سنوي متوقع
      final oneYearForecast = averagePrice * (1 + growthRate);
      final threeYearForecast = averagePrice * (1 + growthRate * 3);
      final fiveYearForecast = averagePrice * (1 + growthRate * 5);

      return {
        'currentAveragePrice': averagePrice,
        'oneYearForecast': oneYearForecast,
        'threeYearForecast': threeYearForecast,
        'fiveYearForecast': fiveYearForecast,
        'expectedGrowthRate': growthRate * 100,
        'marketTrend': 'صاعد', // يمكن حسابه بناءً على البيانات التاريخية
        'riskLevel': 'متوسط',
        'recommendation': 'مناسب للاستثمار طويل المدى',
        'location': location,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error getting market forecast: $e');
      return {};
    }
  }

  /// حذف استثمار
  Future<bool> deleteInvestment(String investmentId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من ملكية الاستثمار
      final investmentDoc = await _firestore
          .collection('investments')
          .doc(investmentId)
          .get();

      if (!investmentDoc.exists) {
        throw Exception('الاستثمار غير موجود');
      }

      final investmentData = investmentDoc.data()!;
      if (investmentData['investorId'] != user.uid) {
        throw Exception('ليس لديك صلاحية لحذف هذا الاستثمار');
      }

      // حذف الاستثمار وتاريخ الدخل
      final batch = _firestore.batch();
      
      batch.delete(investmentDoc.reference);
      
      // حذف تاريخ الدخل
      final incomeSnapshot = await _firestore
          .collection('investments')
          .doc(investmentId)
          .collection('incomeHistory')
          .get();

      for (final doc in incomeSnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error deleting investment: $e');
      rethrow;
    }
  }

  /// الحصول على تقرير الأداء
  Future<Map<String, dynamic>> getPerformanceReport() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return {};

      final investments = await getMyInvestments();
      
      if (investments.isEmpty) {
        return {
          'totalInvestments': 0,
          'totalValue': 0,
          'totalReturn': 0,
          'bestPerforming': null,
          'worstPerforming': null,
          'averageReturn': 0,
        };
      }

      // حساب الإحصائيات
      double totalValue = 0;
      double totalInitialValue = 0;
      double bestReturn = double.negativeInfinity;
      double worstReturn = double.infinity;
      Map<String, dynamic>? bestInvestment;
      Map<String, dynamic>? worstInvestment;

      for (final investment in investments) {
        final currentValue = (investment['currentValue'] as num?)?.toDouble() ?? 0;
        final initialValue = (investment['initialValue'] as num?)?.toDouble() ?? 0;
        final returnPercentage = (investment['returnPercentage'] as num?)?.toDouble() ?? 0;

        totalValue += currentValue;
        totalInitialValue += initialValue;

        if (returnPercentage > bestReturn) {
          bestReturn = returnPercentage;
          bestInvestment = investment;
        }

        if (returnPercentage < worstReturn) {
          worstReturn = returnPercentage;
          worstInvestment = investment;
        }
      }

      final totalReturn = totalValue - totalInitialValue;
      final averageReturn = totalInitialValue > 0 ? (totalReturn / totalInitialValue) * 100 : 0;

      return {
        'totalInvestments': investments.length,
        'totalValue': totalValue,
        'totalInitialValue': totalInitialValue,
        'totalReturn': totalReturn,
        'averageReturn': averageReturn,
        'bestPerforming': bestInvestment,
        'worstPerforming': worstInvestment,
        'bestReturn': bestReturn,
        'worstReturn': worstReturn,
      };
    } catch (e) {
      print('Error getting performance report: $e');
      return {};
    }
  }
}
