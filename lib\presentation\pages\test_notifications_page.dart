import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../core/services/notification_service.dart';
import '../../core/theme/app_colors.dart';
import '../../domain/models/notification_model.dart' as domain;

/// صفحة اختبار نظام الإشعارات
class TestNotificationsPage extends StatefulWidget {
  const TestNotificationsPage({super.key});

  @override
  State<TestNotificationsPage> createState() => _TestNotificationsPageState();
}

class _TestNotificationsPageState extends State<TestNotificationsPage> {
  late NotificationService _notificationService;

  @override
  void initState() {
    super.initState();
    _notificationService = RepositoryProvider.of<NotificationService>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'اختبار الإشعارات',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'اختبار أنواع الإشعارات المختلفة',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            
            // إشعار عقار جديد
            _buildNotificationButton(
              title: 'إشعار عقار جديد',
              subtitle: 'عرض إشعار عن عقار جديد',
              icon: Icons.home,
              color: Colors.green,
              onPressed: () => _showTestNotification(
                title: 'عقار جديد متاح',
                body: 'تم إضافة عقار جديد يطابق معايير البحث الخاصة بك',
                type: domain.NotificationType.newProperty,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // إشعار رسالة جديدة
            _buildNotificationButton(
              title: 'إشعار رسالة جديدة',
              subtitle: 'عرض إشعار عن رسالة جديدة',
              icon: Icons.message,
              color: Colors.blue,
              onPressed: () => _showTestNotification(
                title: 'رسالة جديدة',
                body: 'لديك رسالة جديدة من أحد المستخدمين',
                type: domain.NotificationType.newMessage,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // إشعار عرض جديد
            _buildNotificationButton(
              title: 'إشعار عرض جديد',
              subtitle: 'عرض إشعار عن عرض جديد على طلب عقار',
              icon: Icons.local_offer,
              color: Colors.orange,
              onPressed: () => _showTestNotification(
                title: 'عرض جديد على طلبك',
                body: 'تم تقديم عرض جديد على طلب العقار الخاص بك',
                type: domain.NotificationType.newPropertyRequestOffer,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // إشعار تحقق من الحساب
            _buildNotificationButton(
              title: 'إشعار تحقق من الحساب',
              subtitle: 'عرض إشعار عن تحقق من الحساب',
              icon: Icons.verified_user,
              color: Colors.purple,
              onPressed: () => _showTestNotification(
                title: 'تم تحقق حسابك',
                body: 'تم تحقق حسابك بنجاح، يمكنك الآن الاستفادة من جميع الميزات',
                type: domain.NotificationType.accountVerification,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // إشعار نظام
            _buildNotificationButton(
              title: 'إشعار نظام',
              subtitle: 'عرض إشعار نظام',
              icon: Icons.notifications_active,
              color: Colors.red,
              onPressed: () => _showTestNotification(
                title: 'تحديث النظام',
                body: 'تم تحديث النظام إلى الإصدار الجديد مع ميزات محسنة',
                type: domain.NotificationType.system,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // اختبار الإشعار المحلي
            ElevatedButton.icon(
              onPressed: _showLocalNotification,
              icon: const Icon(Icons.phone_android),
              label: Text(
                'اختبار الإشعار المحلي',
                style: GoogleFonts.cairo(fontSize: 14),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Card(
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.cairo(fontSize: 12),
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onPressed,
      ),
    );
  }

  void _showTestNotification({
    required String title,
    required String body,
    required domain.NotificationType type,
  }) {
    NotificationService.showInAppNotification(
      context,
      title: title,
      body: body,
      type: type,
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم النقر على الإشعار: $title'),
            backgroundColor: AppColors.success,
          ),
        );
      },
    );
  }

  void _showLocalNotification() async {
    await _notificationService.showLocalNotification(
      title: 'إشعار محلي تجريبي',
      body: 'هذا إشعار محلي يظهر حتى عندما يكون التطبيق مغلق',
      type: NotificationType.general,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إرسال الإشعار المحلي'),
        backgroundColor: AppColors.success,
      ),
    );
  }
}
