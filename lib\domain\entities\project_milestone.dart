import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// حالة المعلم
enum MilestoneStatus {
  notStarted,   // لم يبدأ
  inProgress,   // قيد التنفيذ
  completed,    // مكتمل
  delayed,      // متأخر
  cancelled,    // ملغي
}

/// أولوية المعلم
enum MilestonePriority {
  low,          // منخفضة
  medium,       // متوسطة
  high,         // عالية
  critical,     // حرجة
}

/// نوع المعلم
enum MilestoneType {
  planning,     // تخطيط
  design,       // تصميم
  approval,     // موافقة
  construction, // إنشاء
  inspection,   // فحص
  delivery,     // تسليم
  payment,      // دفع
  documentation, // توثيق
  other,        // أخرى
}

/// نموذج معلم المشروع
class ProjectMilestone extends Equatable {
  /// معرف المعلم
  final String id;
  
  /// معرف المشروع
  final String projectId;
  
  /// اسم المعلم
  final String name;
  
  /// وصف المعلم
  final String description;
  
  /// نوع المعلم
  final MilestoneType type;
  
  /// حالة المعلم
  final MilestoneStatus status;
  
  /// أولوية المعلم
  final MilestonePriority priority;
  
  /// تاريخ البداية المتوقع
  final DateTime? startDate;
  
  /// تاريخ الانتهاء المتوقع
  final DateTime? dueDate;
  
  /// تاريخ الانتهاء الفعلي
  final DateTime? completedDate;
  
  /// نسبة الإنجاز (0-100)
  final double progress;
  
  /// الميزانية المخصصة
  final double? budget;
  
  /// التكلفة الفعلية
  final double? actualCost;
  
  /// معرف المسؤول عن المعلم
  final String? assignedToId;
  
  /// اسم المسؤول عن المعلم
  final String? assignedToName;
  
  /// معرف منشئ المعلم
  final String createdById;
  
  /// اسم منشئ المعلم
  final String createdByName;
  
  /// قائمة معرفات المهام المرتبطة
  final List<String> taskIds;
  
  /// قائمة معرفات الوثائق المرتبطة
  final List<String> documentIds;
  
  /// قائمة المتطلبات
  final List<String> requirements;
  
  /// قائمة المخرجات المتوقعة
  final List<String> deliverables;
  
  /// المعالم المعتمدة عليها
  final List<String> dependencies;
  
  /// العلامات
  final List<String> tags;
  
  /// ملاحظات
  final String? notes;
  
  /// هل المعلم حرج
  final bool isCritical;
  
  /// هل المعلم قابل للتأجيل
  final bool isPostponable;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث
  final DateTime updatedAt;
  
  /// معلومات إضافية
  final Map<String, dynamic>? additionalInfo;

  const ProjectMilestone({
    required this.id,
    required this.projectId,
    required this.name,
    required this.description,
    required this.type,
    required this.status,
    required this.priority,
    this.startDate,
    this.dueDate,
    this.completedDate,
    this.progress = 0.0,
    this.budget,
    this.actualCost,
    this.assignedToId,
    this.assignedToName,
    required this.createdById,
    required this.createdByName,
    this.taskIds = const [],
    this.documentIds = const [],
    this.requirements = const [],
    this.deliverables = const [],
    this.dependencies = const [],
    this.tags = const [],
    this.notes,
    this.isCritical = false,
    this.isPostponable = true,
    required this.createdAt,
    required this.updatedAt,
    this.additionalInfo,
  });

  /// إنشاء نسخة معدلة من المعلم
  ProjectMilestone copyWith({
    String? id,
    String? projectId,
    String? name,
    String? description,
    MilestoneType? type,
    MilestoneStatus? status,
    MilestonePriority? priority,
    DateTime? startDate,
    DateTime? dueDate,
    DateTime? completedDate,
    double? progress,
    double? budget,
    double? actualCost,
    String? assignedToId,
    String? assignedToName,
    String? createdById,
    String? createdByName,
    List<String>? taskIds,
    List<String>? documentIds,
    List<String>? requirements,
    List<String>? deliverables,
    List<String>? dependencies,
    List<String>? tags,
    String? notes,
    bool? isCritical,
    bool? isPostponable,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalInfo,
  }) {
    return ProjectMilestone(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      startDate: startDate ?? this.startDate,
      dueDate: dueDate ?? this.dueDate,
      completedDate: completedDate ?? this.completedDate,
      progress: progress ?? this.progress,
      budget: budget ?? this.budget,
      actualCost: actualCost ?? this.actualCost,
      assignedToId: assignedToId ?? this.assignedToId,
      assignedToName: assignedToName ?? this.assignedToName,
      createdById: createdById ?? this.createdById,
      createdByName: createdByName ?? this.createdByName,
      taskIds: taskIds ?? this.taskIds,
      documentIds: documentIds ?? this.documentIds,
      requirements: requirements ?? this.requirements,
      deliverables: deliverables ?? this.deliverables,
      dependencies: dependencies ?? this.dependencies,
      tags: tags ?? this.tags,
      notes: notes ?? this.notes,
      isCritical: isCritical ?? this.isCritical,
      isPostponable: isPostponable ?? this.isPostponable,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  /// تحويل المعلم إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'projectId': projectId,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'startDate': startDate != null ? Timestamp.fromDate(startDate!) : null,
      'dueDate': dueDate != null ? Timestamp.fromDate(dueDate!) : null,
      'completedDate': completedDate != null ? Timestamp.fromDate(completedDate!) : null,
      'progress': progress,
      'budget': budget,
      'actualCost': actualCost,
      'assignedToId': assignedToId,
      'assignedToName': assignedToName,
      'createdById': createdById,
      'createdByName': createdByName,
      'taskIds': taskIds,
      'documentIds': documentIds,
      'requirements': requirements,
      'deliverables': deliverables,
      'dependencies': dependencies,
      'tags': tags,
      'notes': notes,
      'isCritical': isCritical,
      'isPostponable': isPostponable,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalInfo': additionalInfo,
    };
  }

  /// إنشاء معلم من Map
  factory ProjectMilestone.fromMap(Map<String, dynamic> map) {
    return ProjectMilestone(
      id: map['id'] ?? '',
      projectId: map['projectId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      type: MilestoneType.values.firstWhere(
        (e) => e.toString().split('.').last == map['type'],
        orElse: () => MilestoneType.other,
      ),
      status: MilestoneStatus.values.firstWhere(
        (e) => e.toString().split('.').last == map['status'],
        orElse: () => MilestoneStatus.notStarted,
      ),
      priority: MilestonePriority.values.firstWhere(
        (e) => e.toString().split('.').last == map['priority'],
        orElse: () => MilestonePriority.medium,
      ),
      startDate: map['startDate'] is Timestamp 
          ? (map['startDate'] as Timestamp).toDate() 
          : null,
      dueDate: map['dueDate'] is Timestamp 
          ? (map['dueDate'] as Timestamp).toDate() 
          : null,
      completedDate: map['completedDate'] is Timestamp 
          ? (map['completedDate'] as Timestamp).toDate() 
          : null,
      progress: (map['progress'] ?? 0.0).toDouble(),
      budget: map['budget']?.toDouble(),
      actualCost: map['actualCost']?.toDouble(),
      assignedToId: map['assignedToId'],
      assignedToName: map['assignedToName'],
      createdById: map['createdById'] ?? '',
      createdByName: map['createdByName'] ?? '',
      taskIds: List<String>.from(map['taskIds'] ?? []),
      documentIds: List<String>.from(map['documentIds'] ?? []),
      requirements: List<String>.from(map['requirements'] ?? []),
      deliverables: List<String>.from(map['deliverables'] ?? []),
      dependencies: List<String>.from(map['dependencies'] ?? []),
      tags: List<String>.from(map['tags'] ?? []),
      notes: map['notes'],
      isCritical: map['isCritical'] ?? false,
      isPostponable: map['isPostponable'] ?? true,
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : DateTime.now(),
      additionalInfo: map['additionalInfo'],
    );
  }

  /// إنشاء معلم من DocumentSnapshot
  factory ProjectMilestone.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return ProjectMilestone.fromMap(data);
  }

  /// التحقق من انتهاء موعد المعلم
  bool isOverdue() {
    if (dueDate == null || status == MilestoneStatus.completed) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  /// التحقق من اقتراب موعد المعلم
  bool isDueSoon({int daysThreshold = 7}) {
    if (dueDate == null || status == MilestoneStatus.completed) return false;
    final now = DateTime.now();
    final difference = dueDate!.difference(now).inDays;
    return difference <= daysThreshold && difference >= 0;
  }

  @override
  List<Object?> get props => [
    id, projectId, name, description, type, status, priority,
    startDate, dueDate, completedDate, progress, budget, actualCost,
    assignedToId, assignedToName, createdById, createdByName,
    taskIds, documentIds, requirements, deliverables, dependencies,
    tags, notes, isCritical, isPostponable, createdAt, updatedAt,
    additionalInfo,
  ];
}
