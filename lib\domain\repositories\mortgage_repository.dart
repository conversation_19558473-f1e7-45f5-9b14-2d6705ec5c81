import '../entities/mortgage_calculation.dart';

/// واجهة مستودع التمويل العقاري
abstract class MortgageRepository {
  /// إنشاء حساب تمويل عقاري جديد
  Future<String> createMortgageCalculation(MortgageCalculation calculation);
  
  /// تحديث حساب تمويل عقاري موجود
  Future<void> updateMortgageCalculation(MortgageCalculation calculation);
  
  /// حذف حساب تمويل عقاري
  Future<void> deleteMortgageCalculation(String calculationId);
  
  /// الحصول على حساب تمويل عقاري بواسطة المعرف
  Future<MortgageCalculation?> getMortgageCalculationById(String calculationId);
  
  /// الحصول على حسابات التمويل العقاري للمستخدم
  Future<List<MortgageCalculation>> getUserMortgageCalculations(String userId);
  
  /// حفظ حساب تمويل عقاري
  Future<void> saveMortgageCalculation(String calculationId, String name);
  
  /// إلغاء حفظ حساب تمويل عقاري
  Future<void> unsaveMortgageCalculation(String calculationId);
  
  /// إضافة ملاحظة إلى حساب تمويل عقاري
  Future<void> addMortgageCalculationNote(String calculationId, String note);
  
  /// الحصول على معدلات الفائدة الحالية
  Future<Map<String, double>> getCurrentInterestRates();
  
  /// الحصول على معلومات البنوك والتمويل
  Future<List<Map<String, dynamic>>> getBanksInformation();
  
  /// الحصول على شروط التمويل
  Future<Map<String, dynamic>> getMortgageRequirements();
  
  /// التحقق من أهلية التمويل
  Future<Map<String, dynamic>> checkMortgageEligibility({
    required double income,
    required double expenses,
    required int age,
    required String nationality,
    required String employmentType,
    required int employmentDuration,
  });
  
  /// مشاركة حساب تمويل عقاري
  Future<String> shareMortgageCalculation(String calculationId);
  
  /// الحصول على حساب تمويل عقاري مشترك
  Future<MortgageCalculation?> getSharedMortgageCalculation(String shareId);
}
