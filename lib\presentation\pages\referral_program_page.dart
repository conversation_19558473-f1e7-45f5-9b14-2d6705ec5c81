import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:kuwait_corners/core/services/referral_program_service.dart';

/// صفحة برنامج الإحالة
/// تتيح للمستخدم مشاركة رمز الإحالة الخاص به ومتابعة الإحالات
class ReferralProgramPage extends StatefulWidget {
  const ReferralProgramPage({super.key});

  @override
  State<ReferralProgramPage> createState() => _ReferralProgramPageState();
}

class _ReferralProgramPageState extends State<ReferralProgramPage> {
  final ReferralProgramService _referralService = ReferralProgramService();

  // حالة الصفحة
  bool _isLoading = true;
  String? _errorMessage;
  ReferralData? _referralData;
  String? _directReferralLink;
  String? _webReferralLink;
  String? _universalReferralLink; // الرابط العالمي الذي يعمل على جميع الأجهزة
  List<Map<String, dynamic>> _referredUsers = [];

  // وحدة التحكم في النص
  final _referralCodeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadReferralData();
  }

  @override
  void dispose() {
    _referralCodeController.dispose();
    super.dispose();
  }

  /// تحميل بيانات الإحالة
  Future<void> _loadReferralData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final referralData = await _referralService.getCurrentUserReferralData();
      final referredUsers = await _referralService.getReferredUsers();

      setState(() {
        _referralData = referralData;
        _referredUsers = referredUsers;

        if (referralData != null) {
          _referralCodeController.text = referralData.referralCode;
        }
      });

      // إنشاء روابط الإحالة
      final links = await _referralService.createReferralLinks();

      setState(() {
        _directReferralLink = links['directLink'];
        _webReferralLink = links['webLink'];
        _universalReferralLink = links['universalLink'];
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل بيانات الإحالة';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// نسخ رمز الإحالة إلى الحافظة
  void _copyReferralCode() {
    if (_referralData != null) {
      Clipboard.setData(ClipboardData(text: _referralData!.referralCode));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم نسخ رمز الإحالة')));
    }
  }

  /// نسخ رابط الإحالة إلى الحافظة
  void _copyReferralLink() {
    final linkToCopy = _universalReferralLink ?? _webReferralLink;
    if (linkToCopy != null) {
      Clipboard.setData(ClipboardData(text: linkToCopy));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم نسخ رابط الإحالة')));
    }
  }

  /// مشاركة رابط الإحالة
  void _shareReferralLink() {
    if (_universalReferralLink != null) {
      // استخدام الرابط العالمي الذي يعمل على جميع الأجهزة
      Share.share(
        'انضم إلى Krea واحصل على مزايا حصرية!\n\n'
        'استخدم رمز الإحالة الخاص بي: ${_referralData!.referralCode}\n\n'
        'أو انقر على الرابط: $_universalReferralLink',
        subject: 'دعوة للانضمام إلى Krea');
    } else if (_webReferralLink != null) {
      // استخدام رابط الويب إذا لم يتوفر الرابط العالمي
      Share.share(
        'انضم إلى Krea واحصل على مزايا حصرية!\n\n'
        'استخدم رمز الإحالة الخاص بي: ${_referralData!.referralCode}\n\n'
        'أو انقر على الرابط: $_webReferralLink',
        subject: 'دعوة للانضمام إلى Krea');
    } else if (_referralData != null) {
      // استخدام رمز الإحالة فقط إذا لم تتوفر الروابط
      Share.share(
        'انضم إلى Krea واحصل على مزايا حصرية!\n\n'
        'استخدم رمز الإحالة الخاص بي: ${_referralData!.referralCode}',
        subject: 'دعوة للانضمام إلى Krea');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('برنامج الإحالة')),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, color: Colors.red, size: 48),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red)),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadReferralData,
                        child: const Text('إعادة المحاولة')),
                    ]))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // بطاقة الإحالة
                      _buildReferralCard(),

                      const SizedBox(height: 24),

                      // قسم كيفية عمل البرنامج
                      const Text(
                        'كيف يعمل برنامج الإحالة؟',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold)),

                      const SizedBox(height: 16),

                      // خطوات البرنامج
                      _buildStepsCard(),

                      const SizedBox(height: 24),

                      // قسم المستخدمين المحالين
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'المستخدمون المحالون',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold)),
                          Text(
                            'العدد: ${_referredUsers.length}',
                            style: TextStyle(
                              color: Colors.grey.shade600)),
                        ]),

                      const SizedBox(height: 16),

                      // قائمة المستخدمين المحالين
                      _referredUsers.isEmpty
                          ? _buildEmptyReferralsCard()
                          : _buildReferredUsersCard(),
                    ])));
  }

  /// بناء بطاقة الإحالة
  Widget _buildReferralCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    shape: BoxShape.circle),
                  child: Icon(
                    Icons.people,
                    color: Colors.blue.shade700)),
                const SizedBox(width: 12),
                const Text(
                  'ادعُ أصدقاءك واكسب نقاط',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),

            const SizedBox(height: 16),

            // وصف البرنامج
            const Text(
              'احصل على 30 نقطة لكل صديق ينضم باستخدام رمز الإحالة الخاص بك، وسيحصل صديقك على 20 نقطة أيضًا!',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey)),

            const SizedBox(height: 24),

            // رمز الإحالة
            const Text(
              'رمز الإحالة الخاص بك',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold)),

            const SizedBox(height: 8),

            // حقل رمز الإحالة
            TextField(
              controller: _referralCodeController,
              readOnly: true,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                letterSpacing: 2),
              decoration: InputDecoration(
                filled: true,
                fillColor: Colors.grey.shade100,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none),
                suffixIcon: IconButton(
                  onPressed: _copyReferralCode,
                  icon: const Icon(Icons.copy),
                  tooltip: 'نسخ الرمز'))),

            const SizedBox(height: 24),

            // رابط الإحالة
            const Text(
              'رابط الإحالة',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold)),

            const SizedBox(height: 8),

            // حقل رابط الإحالة
            TextField(
              readOnly: true,
              controller: TextEditingController(
                  text: _universalReferralLink ?? _webReferralLink),
              decoration: InputDecoration(
                filled: true,
                fillColor: Colors.grey.shade100,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none),
                hintText: 'جاري إنشاء الرابط...',
                suffixIcon: IconButton(
                  onPressed: _copyReferralLink,
                  icon: const Icon(Icons.copy),
                  tooltip: 'نسخ الرابط'))),

            const SizedBox(height: 24),

            // زر المشاركة
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _shareReferralLink,
                icon: const Icon(Icons.share),
                label: const Text('مشاركة مع الأصدقاء'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black87,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8))))),
          ])));
  }

  /// بناء بطاقة خطوات البرنامج
  Widget _buildStepsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // الخطوة 1
            _buildStepItem(
              number: 1,
              title: 'شارك رمز الإحالة',
              description:
                  'شارك رمز الإحالة الخاص بك مع أصدقائك عبر وسائل التواصل الاجتماعي أو الرسائل.',
              icon: Icons.share),

            const SizedBox(height: 16),

            // الخطوة 2
            _buildStepItem(
              number: 2,
              title: 'انضمام الأصدقاء',
              description:
                  'عندما ينضم أصدقاؤك إلى التطبيق ويستخدمون رمز الإحالة الخاص بك، سيتم تسجيلهم كمستخدمين محالين.',
              icon: Icons.person_add),

            const SizedBox(height: 16),

            // الخطوة 3
            _buildStepItem(
              number: 3,
              title: 'اكسب النقاط',
              description:
                  'ستحصل على 30 نقطة لكل صديق ينضم باستخدام رمز الإحالة الخاص بك، وسيحصل صديقك على 20 نقطة أيضًا!',
              icon: Icons.star),
          ])));
  }

  /// بناء عنصر خطوة
  Widget _buildStepItem({
    required int number,
    required String title,
    required String description,
    required IconData icon,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // رقم الخطوة
        Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            color: Colors.blue,
            shape: BoxShape.circle),
          child: Center(
            child: Text(
              '$number',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold)))),

        const SizedBox(width: 16),

        // تفاصيل الخطوة
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, size: 16, color: Colors.blue),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16)),
                ]),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600)),
            ])),
      ]);
  }

  /// بناء بطاقة المستخدمين المحالين
  Widget _buildReferredUsersCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _referredUsers.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final user = _referredUsers[index];
          final name = user['name'] as String? ?? 'مستخدم';
          final photoURL = user['photoURL'] as String?;
          final date = (user['date'] as Timestamp).toDate();
          final rewarded = user['rewarded'] as bool? ?? false;

          return ListTile(
            leading: CircleAvatar(
              backgroundImage: photoURL != null ? NetworkImage(photoURL) : null,
              child: photoURL == null ? const Icon(Icons.person) : null),
            title: Text(name),
            subtitle: Text(
              'انضم في ${DateFormat('yyyy/MM/dd').format(date)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600)),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: rewarded ? Colors.green.shade50 : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(16)),
              child: Text(
                rewarded ? '+30 نقطة' : 'قيد الانتظار',
                style: TextStyle(
                  color:
                      rewarded ? Colors.green.shade700 : Colors.grey.shade700,
                  fontWeight: FontWeight.bold,
                  fontSize: 12))));
        }));
  }

  /// بناء بطاقة عدم وجود مستخدمين محالين
  Widget _buildEmptyReferralsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.people_outline,
              size: 48,
              color: Colors.grey.shade400),
            const SizedBox(height: 16),
            const Text(
              'لم تقم بدعوة أي مستخدمين بعد',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold),
              textAlign: TextAlign.center),
            const SizedBox(height: 8),
            Text(
              'شارك رمز الإحالة الخاص بك مع أصدقائك للحصول على نقاط إضافية',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600),
              textAlign: TextAlign.center),
            const SizedBox(height: 16),
            OutlinedButton.icon(
              onPressed: _shareReferralLink,
              icon: const Icon(Icons.share),
              label: const Text('مشاركة الآن'),
              style: OutlinedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12))),
          ])));
  }
}
