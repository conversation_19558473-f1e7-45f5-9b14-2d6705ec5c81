import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../domain/services/investment_service.dart';

class InvestmentPortfolioPage extends StatefulWidget {
  const InvestmentPortfolioPage({super.key});

  @override
  State<InvestmentPortfolioPage> createState() => _InvestmentPortfolioPageState();
}

class _InvestmentPortfolioPageState extends State<InvestmentPortfolioPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final InvestmentService _investmentService = InvestmentService();

  // بيانات المحفظة
  Map<String, dynamic> _portfolioData = {};
  List<Map<String, dynamic>> _investments = [];
  List<Map<String, dynamic>> _opportunities = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadPortfolioData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل بيانات المحفظة
  Future<void> _loadPortfolioData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final futures = await Future.wait([
        _investmentService.getPortfolioSummary(),
        _investmentService.getMyInvestments(),
        _investmentService.getInvestmentOpportunities(),
      ]);

      setState(() {
        _portfolioData = futures[0] as Map<String, dynamic>;
        _investments = futures[1] as List<Map<String, dynamic>>;
        _opportunities = futures[2] as List<Map<String, dynamic>>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'حدث خطأ في تحميل البيانات: $e',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'محفظة الاستثمارات',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: const Icon(Icons.dashboard),
              text: 'نظرة عامة'),
            Tab(
              icon: const Icon(Icons.account_balance_wallet),
              text: 'استثماراتي'),
            Tab(
              icon: const Icon(Icons.trending_up),
              text: 'الفرص'),
          ]),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPortfolioData),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showDetailedAnalysis),
        ]),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildInvestmentsTab(),
                _buildOpportunitiesTab(),
              ]));
  }

  /// بناء تبويب النظرة العامة
  Widget _buildOverviewTab() {
    return RefreshIndicator(
      onRefresh: _loadPortfolioData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ملخص المحفظة
            _buildPortfolioSummary(),
            const SizedBox(height: 24),

            // الرسم البياني للأداء
            _buildPerformanceChart(),
            const SizedBox(height: 24),

            // توزيع الاستثمارات
            _buildInvestmentDistribution(),
            const SizedBox(height: 24),

            // أفضل الاستثمارات أداءً
            _buildTopPerformingInvestments(),
          ])));
  }

  /// بناء ملخص المحفظة
  Widget _buildPortfolioSummary() {
    final totalValue = _portfolioData['totalValue'] ?? 0.0;
    final totalReturn = _portfolioData['totalReturn'] ?? 0.0;
    final returnPercentage = _portfolioData['returnPercentage'] ?? 0.0;
    final monthlyIncome = _portfolioData['monthlyIncome'] ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: Colors.white,
                size: 24),
              const SizedBox(width: 8),
              Text(
                'إجمالي قيمة المحفظة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.white70)),
            ]),
          const SizedBox(height: 8),
          Text(
            '${NumberFormat('#,###').format(totalValue)} د.ك',
            style: GoogleFonts.cairo(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white)),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'إجمالي العائد',
                  '${NumberFormat('#,###').format(totalReturn)} د.ك',
                  returnPercentage >= 0 ? Icons.trending_up : Icons.trending_down,
                  returnPercentage >= 0 ? Colors.green.shade300 : Colors.red.shade300)),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryItem(
                  'الدخل الشهري',
                  '${NumberFormat('#,###').format(monthlyIncome)} د.ك',
                  Icons.calendar_month,
                  Colors.blue.shade300)),
            ]),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8)),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  returnPercentage >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                  color: Colors.white,
                  size: 16),
                const SizedBox(width: 4),
                Text(
                  '${returnPercentage.toStringAsFixed(2)}%',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white)),
                const SizedBox(width: 4),
                Text(
                  'معدل العائد',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white70)),
              ])),
        ]));
  }

  /// بناء عنصر ملخص
  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 4),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.white70)),
          ]),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white)),
      ]);
  }

  /// بناء الرسم البياني للأداء
  Widget _buildPerformanceChart() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.show_chart,
                color: Theme.of(context).primaryColor,
                size: 20),
              const SizedBox(width: 8),
              Text(
                'أداء المحفظة (آخر 12 شهر)',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(show: true),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: false)),
                  rightTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: false)),
                  topTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: false)),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: false))),
                borderData: FlBorderData(show: false),
                lineBarsData: [
                  LineChartBarData(
                    spots: _generatePerformanceData(),
                    isCurved: true,
                    color: Theme.of(context).primaryColor,
                    barWidth: 3,
                    dotData: FlDotData(show: false),
                    belowBarData: BarAreaData(
                      show: true,
                      color: Theme.of(context).primaryColor.withOpacity(0.1))),
                ]))),
        ]));
  }

  /// إنشاء بيانات الأداء للرسم البياني
  List<FlSpot> _generatePerformanceData() {
    // بيانات تجريبية - يمكن استبدالها ببيانات حقيقية
    return [
      const FlSpot(0, 100),
      const FlSpot(1, 105),
      const FlSpot(2, 102),
      const FlSpot(3, 108),
      const FlSpot(4, 112),
      const FlSpot(5, 115),
      const FlSpot(6, 118),
      const FlSpot(7, 122),
      const FlSpot(8, 120),
      const FlSpot(9, 125),
      const FlSpot(10, 128),
      const FlSpot(11, 132),
    ];
  }

  /// بناء توزيع الاستثمارات
  Widget _buildInvestmentDistribution() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.pie_chart,
                color: Theme.of(context).primaryColor,
                size: 20),
              const SizedBox(width: 8),
              Text(
                'توزيع الاستثمارات',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: SizedBox(
                  height: 150,
                  child: PieChart(
                    PieChartData(
                      sections: _generatePieChartSections(),
                      centerSpaceRadius: 40,
                      sectionsSpace: 2)))),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  children: [
                    _buildLegendItem('شقق', Colors.blue, '45%'),
                    _buildLegendItem('فلل', Colors.green, '30%'),
                    _buildLegendItem('أراضي', Colors.orange, '15%'),
                    _buildLegendItem('تجاري', Colors.purple, '10%'),
                  ])),
            ]),
        ]));
  }

  /// إنشاء أقسام الرسم البياني الدائري
  List<PieChartSectionData> _generatePieChartSections() {
    return [
      PieChartSectionData(
        value: 45,
        color: Colors.blue,
        radius: 50,
        showTitle: false),
      PieChartSectionData(
        value: 30,
        color: Colors.green,
        radius: 50,
        showTitle: false),
      PieChartSectionData(
        value: 15,
        color: Colors.orange,
        radius: 50,
        showTitle: false),
      PieChartSectionData(
        value: 10,
        color: Colors.purple,
        radius: 50,
        showTitle: false),
    ];
  }

  /// بناء عنصر وسيلة الإيضاح
  Widget _buildLegendItem(String label, Color color, String percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: GoogleFonts.cairo(fontSize: 12))),
          Text(
            percentage,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.bold)),
        ]));
  }

  /// بناء أفضل الاستثمارات أداءً
  Widget _buildTopPerformingInvestments() {
    final topInvestments = _investments.take(3).toList();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star,
                color: Theme.of(context).primaryColor,
                size: 20),
              const SizedBox(width: 8),
              Text(
                'أفضل الاستثمارات أداءً',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 16),
          if (topInvestments.isEmpty)
            Center(
              child: Text(
                'لا توجد استثمارات بعد',
                style: GoogleFonts.cairo(
                  color: Colors.grey.shade600)))
          else
            ...topInvestments.map((investment) {
              return _buildTopInvestmentItem(investment);
            }),
        ]));
  }

  /// بناء عنصر أفضل استثمار
  Widget _buildTopInvestmentItem(Map<String, dynamic> investment) {
    final returnPercentage = investment['returnPercentage'] ?? 0.0;
    final isPositive = returnPercentage >= 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isPositive ? Colors.green.shade100 : Colors.red.shade100,
              borderRadius: BorderRadius.circular(8)),
            child: Icon(
              isPositive ? Icons.trending_up : Icons.trending_down,
              color: isPositive ? Colors.green : Colors.red,
              size: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  investment['title'] ?? 'استثمار',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
                Text(
                  investment['location'] ?? '',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600)),
              ])),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${returnPercentage.toStringAsFixed(1)}%',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: isPositive ? Colors.green : Colors.red)),
              Text(
                '${NumberFormat('#,###').format(investment['currentValue'] ?? 0)} د.ك',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey.shade600)),
            ]),
        ]));
  }

  /// بناء تبويب الاستثمارات
  Widget _buildInvestmentsTab() {
    if (_investments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet,
              size: 80,
              color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد استثمارات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                color: Colors.grey.shade600)),
            const SizedBox(height: 8),
            Text(
              'ابدأ استثمارك الأول اليوم',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500)),
          ]));
    }

    return RefreshIndicator(
      onRefresh: _loadPortfolioData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _investments.length,
        itemBuilder: (context, index) {
          final investment = _investments[index];
          return _buildInvestmentCard(investment);
        }));
  }

  /// بناء بطاقة الاستثمار
  Widget _buildInvestmentCard(Map<String, dynamic> investment) {
    final returnPercentage = investment['returnPercentage'] ?? 0.0;
    final isPositive = returnPercentage >= 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        investment['title'] ?? 'استثمار',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      Text(
                        investment['location'] ?? '',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade600)),
                    ])),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isPositive ? Colors.green.shade100 : Colors.red.shade100,
                    borderRadius: BorderRadius.circular(12)),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositive ? Icons.trending_up : Icons.trending_down,
                        size: 16,
                        color: isPositive ? Colors.green : Colors.red),
                      const SizedBox(width: 4),
                      Text(
                        '${returnPercentage.toStringAsFixed(1)}%',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: isPositive ? Colors.green : Colors.red)),
                    ])),
              ]),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInvestmentStat(
                    'القيمة الأولية',
                    '${NumberFormat('#,###').format(investment['initialValue'] ?? 0)} د.ك',
                    Icons.account_balance)),
                Expanded(
                  child: _buildInvestmentStat(
                    'القيمة الحالية',
                    '${NumberFormat('#,###').format(investment['currentValue'] ?? 0)} د.ك',
                    Icons.account_balance_wallet)),
              ]),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInvestmentStat(
                    'الدخل الشهري',
                    '${NumberFormat('#,###').format(investment['monthlyIncome'] ?? 0)} د.ك',
                    Icons.calendar_month)),
                Expanded(
                  child: _buildInvestmentStat(
                    'إجمالي العائد',
                    '${NumberFormat('#,###').format(investment['totalReturn'] ?? 0)} د.ك',
                    Icons.trending_up)),
              ]),
          ])));
  }

  /// بناء إحصائية الاستثمار
  Widget _buildInvestmentStat(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: Colors.grey.shade600),
            const SizedBox(width: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey.shade600)),
          ]),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.bold)),
      ]);
  }

  /// بناء تبويب الفرص
  Widget _buildOpportunitiesTab() {
    // TODO: Implement opportunities tab
    return const Center(
      child: Text('تبويب الفرص الاستثمارية - قيد التطوير'));
  }

  /// عرض التحليل المفصل
  void _showDetailedAnalysis() {
    // TODO: Implement detailed analysis
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم تفعيل التحليل المفصل قريباً',
          style: GoogleFonts.cairo())));
  }
}
