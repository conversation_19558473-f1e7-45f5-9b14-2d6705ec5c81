import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';

import '../../domain/entities/estate.dart';
import '../../domain/services/favorites_service.dart';
// import '../../domain/services/property_comparison_service.dart'; // معلق مؤقتاً
import '../widgets/estate_details_page.dart';
// import 'property_comparison_page.dart'; // معلق مؤقتاً

class FavoritesPage extends StatefulWidget {
  const FavoritesPage({super.key});

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final FavoritesService _favoritesService = FavoritesService();
  // final PropertyComparisonService _comparisonService = PropertyComparisonService(); // معلق مؤقتاً

  List<Estate> _favoriteProperties = [];
  List<Estate> _recentlyViewed = [];
  List<Estate> _priceAlerts = [];
  final List<Estate> _selectedForComparison = [];

  bool _isLoading = true;
  String _sortBy = 'date'; // date, price, area
  bool _isAscending = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final futures = await Future.wait([
        _favoritesService.getFavoriteProperties(),
        _favoritesService.getRecentlyViewedProperties(),
        _favoritesService.getPriceAlertProperties(),
      ]);

      setState(() {
        _favoriteProperties = futures[0];
        _recentlyViewed = futures[1];
        _priceAlerts = futures[2];
        _isLoading = false;
      });

      _sortProperties();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ في تحميل البيانات: $e',
              style: CairoTextStyles.bodyMedium),
            backgroundColor: Colors.red));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'المفضلة',
          style: CairoTextStyles.appBarTitle),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: [
            Tab(
              icon: const Icon(Icons.favorite),
              text: 'المفضلة (${_favoriteProperties.length})'),
            Tab(
              icon: const Icon(Icons.history),
              text: 'المشاهدة الأخيرة (${_recentlyViewed.length})'),
            Tab(
              icon: const Icon(Icons.notifications),
              text: 'تنبيهات الأسعار (${_priceAlerts.length})'),
            Tab(
              icon: const Icon(Icons.compare),
              text: 'للمقارنة (${_selectedForComparison.length})'),
          ]),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() {
                if (_sortBy == value) {
                  _isAscending = !_isAscending;
                } else {
                  _sortBy = value;
                  _isAscending = false;
                }
              });
              _sortProperties();
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'date',
                child: Row(
                  children: [
                    Icon(
                      _sortBy == 'date'
                          ? (_isAscending ? Icons.arrow_upward : Icons.arrow_downward)
                          : Icons.date_range,
                      size: 20),
                    const SizedBox(width: 8),
                    Text('التاريخ', style: CairoTextStyles.bodyMedium),
                  ])),
              PopupMenuItem(
                value: 'price',
                child: Row(
                  children: [
                    Icon(
                      _sortBy == 'price'
                          ? (_isAscending ? Icons.arrow_upward : Icons.arrow_downward)
                          : Icons.attach_money,
                      size: 20),
                    const SizedBox(width: 8),
                    Text('السعر', style: CairoTextStyles.bodyMedium),
                  ])),
              PopupMenuItem(
                value: 'area',
                child: Row(
                  children: [
                    Icon(
                      _sortBy == 'area'
                          ? (_isAscending ? Icons.arrow_upward : Icons.arrow_downward)
                          : Icons.square_foot,
                      size: 20),
                    const SizedBox(width: 8),
                    Text('المساحة', style: CairoTextStyles.bodyMedium),
                  ])),
            ]),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData),
        ]),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildFavoritesTab(),
                _buildRecentlyViewedTab(),
                _buildPriceAlertsTab(),
                _buildComparisonTab(),
              ]),
      // زر المقارنة العائم - معلق مؤقتاً
      /*
      floatingActionButton: _selectedForComparison.isNotEmpty
          ? FloatingActionButton.extended(
              onPressed: _compareSelectedProperties,
              icon: const Icon(Icons.compare_arrows),
              label: Text(
                'مقارنة (${_selectedForComparison.length})',
                style: GoogleFonts.cairo()),
              backgroundColor: Theme.of(context).primaryColor)
          : null,
      */
    );
  }

  /// بناء تبويب المفضلة
  Widget _buildFavoritesTab() {
    if (_favoriteProperties.isEmpty) {
      return _buildEmptyState(
        icon: Icons.favorite_border,
        title: 'لا توجد عقارات مفضلة',
        subtitle: 'أضف عقارات إلى المفضلة لتظهر هنا');
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _favoriteProperties.length,
        itemBuilder: (context, index) {
          final property = _favoriteProperties[index];
          return _buildPropertyCard(
            property,
            showRemoveFromFavorites: true,
            showAddToComparison: true);
        }));
  }

  /// بناء تبويب المشاهدة الأخيرة
  Widget _buildRecentlyViewedTab() {
    if (_recentlyViewed.isEmpty) {
      return _buildEmptyState(
        icon: Icons.history,
        title: 'لا توجد عقارات مشاهدة مؤخراً',
        subtitle: 'العقارات التي تشاهدها ستظهر هنا');
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _recentlyViewed.length,
        itemBuilder: (context, index) {
          final property = _recentlyViewed[index];
          return _buildPropertyCard(
            property,
            showAddToFavorites: true,
            showAddToComparison: true);
        }));
  }

  /// بناء تبويب تنبيهات الأسعار
  Widget _buildPriceAlertsTab() {
    if (_priceAlerts.isEmpty) {
      return _buildEmptyState(
        icon: Icons.notifications_none,
        title: 'لا توجد تنبيهات أسعار',
        subtitle: 'أضف تنبيهات للعقارات لمتابعة تغييرات الأسعار');
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _priceAlerts.length,
        itemBuilder: (context, index) {
          final property = _priceAlerts[index];
          return _buildPropertyCard(
            property,
            showPriceAlert: true,
            showAddToFavorites: true);
        }));
  }

  /// بناء تبويب المقارنة
  Widget _buildComparisonTab() {
    return Column(
      children: [
        if (_selectedForComparison.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'العقارات المحددة للمقارنة',
                  style: CairoTextStyles.headlineSmall),
                // زر مسح المقارنة - معلق مؤقتاً
                /*
                TextButton(
                  onPressed: _clearComparison,
                  child: Text(
                    'مسح الكل',
                    style: GoogleFonts.cairo(color: Colors.red))),
                */
              ])),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _selectedForComparison.length,
              itemBuilder: (context, index) {
                final property = _selectedForComparison[index];
                return _buildPropertyCard(
                  property,
                  showRemoveFromComparison: true);
              })),
        ] else
          Expanded(
            child: _buildEmptyState(
              icon: Icons.compare_arrows,
              title: 'لا توجد عقارات للمقارنة',
              subtitle: 'أضف عقارات للمقارنة من الصفحة الرئيسية')),
      ]);
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            title,
            style: CairoTextStyles.headlineSmall.copyWith(
              color: Colors.grey.shade600)),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: CairoTextStyles.bodyMedium.copyWith(
              color: Colors.grey.shade500),
            textAlign: TextAlign.center),
        ]));
  }

  /// بناء بطاقة العقار
  Widget _buildPropertyCard(
    Estate property, {
    bool showAddToFavorites = false,
    bool showRemoveFromFavorites = false,
    bool showAddToComparison = false,
    bool showRemoveFromComparison = false,
    bool showPriceAlert = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _navigateToPropertyDetails(property),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة العقار
            ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: Stack(
                  children: [
                    // الصورة
                    property.photoUrls.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: property.photoUrls.first,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            placeholder: (context, url) => Container(
                              color: Colors.grey.shade200,
                              child: const Center(
                                child: CircularProgressIndicator())),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey.shade200,
                              child: const Icon(Icons.error)))
                        : Container(
                            color: Colors.grey.shade200,
                            child: const Icon(
                              Icons.home,
                              size: 40,
                              color: Colors.grey)),

                    // أزرار الإجراءات
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Row(
                        children: [
                          if (showAddToFavorites)
                            _buildActionButton(
                              icon: Icons.favorite_border,
                              onPressed: () => _addToFavorites(property),
                              backgroundColor: Colors.white,
                              iconColor: Colors.red),
                          if (showRemoveFromFavorites)
                            _buildActionButton(
                              icon: Icons.favorite,
                              onPressed: () => _removeFromFavorites(property),
                              backgroundColor: Colors.red,
                              iconColor: Colors.white),
                          if (showAddToComparison)
                            _buildActionButton(
                              icon: Icons.compare_arrows,
                              onPressed: () => _addToComparison(property),
                              backgroundColor: Colors.white,
                              iconColor: Colors.blue),
                          if (showRemoveFromComparison)
                            _buildActionButton(
                              icon: Icons.remove_circle,
                              onPressed: () => _removeFromComparison(property),
                              backgroundColor: Colors.red,
                              iconColor: Colors.white),
                        ])),

                    // تنبيه السعر
                    if (showPriceAlert)
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(12)),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.notifications,
                                size: 16,
                                color: Colors.white),
                              const SizedBox(width: 4),
                              Text(
                                'تنبيه سعر',
                                style: CairoTextStyles.bodySmall.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold)),
                            ]))),
                  ]))),

            // تفاصيل العقار
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    property.title,
                    style: CairoTextStyles.titleMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          property.location,
                          style: CairoTextStyles.bodyMedium.copyWith(
                            color: Colors.grey.shade600),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis)),
                    ]),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${NumberFormat('#,###').format(property.price)} د.ك',
                        style: CairoTextStyles.headlineSmall.copyWith(
                          color: Theme.of(context).primaryColor)),
                      Row(
                        children: [
                          Icon(
                            Icons.square_foot,
                            size: 16,
                            color: Colors.grey.shade600),
                          const SizedBox(width: 4),
                          Text(
                            '${property.area} م²',
                            style: CairoTextStyles.bodyMedium.copyWith(
                              color: Colors.grey.shade600)),
                          const SizedBox(width: 12),
                          Icon(
                            Icons.bed,
                            size: 16,
                            color: Colors.grey.shade600),
                          const SizedBox(width: 4),
                          Text(
                            '${property.numberOfRooms}',
                            style: CairoTextStyles.bodyMedium.copyWith(
                              color: Colors.grey.shade600)),
                        ]),
                    ]),
                ])),
          ])));
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color backgroundColor,
    required Color iconColor,
  }) {
    return Container(
      margin: const EdgeInsets.only(left: 4),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: backgroundColor,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 4,
                offset: const Offset(0, 2)),
            ]),
          child: Icon(
            icon,
            size: 20,
            color: iconColor))));
  }

  /// ترتيب العقارات
  void _sortProperties() {
    switch (_sortBy) {
      case 'price':
        _favoriteProperties.sort((a, b) => _isAscending
            ? a.price.compareTo(b.price)
            : b.price.compareTo(a.price));
        break;
      case 'area':
        _favoriteProperties.sort((a, b) => _isAscending
            ? (a.area ?? 0).compareTo(b.area ?? 0)
            : (b.area ?? 0).compareTo(a.area ?? 0));
        break;
      case 'date':
      default:
        _favoriteProperties.sort((a, b) => _isAscending
            ? a.createdAt.compareTo(b.createdAt)
            : b.createdAt.compareTo(a.createdAt));
        break;
    }
    setState(() {});
  }

  /// الانتقال لصفحة تفاصيل العقار
  void _navigateToPropertyDetails(Estate property) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EstateDetailsPage(estate: property)));
  }

  /// إضافة للمفضلة
  Future<void> _addToFavorites(Estate property) async {
    try {
      await _favoritesService.addToFavorites(property.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إضافة العقار للمفضلة',
              style: CairoTextStyles.bodyMedium),
            backgroundColor: Colors.green));
      }
      _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في إضافة العقار للمفضلة',
              style: CairoTextStyles.bodyMedium),
            backgroundColor: Colors.red));
      }
    }
  }

  /// إزالة من المفضلة
  Future<void> _removeFromFavorites(Estate property) async {
    try {
      await _favoritesService.removeFromFavorites(property.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إزالة العقار من المفضلة',
              style: CairoTextStyles.bodyMedium),
            backgroundColor: Colors.orange));
      }
      _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في إزالة العقار من المفضلة',
              style: CairoTextStyles.bodyMedium),
            backgroundColor: Colors.red));
      }
    }
  }

  /// إضافة للمقارنة
  Future<void> _addToComparison(Estate property) async {
    try {
      // await _comparisonService.addToComparison(property.id); // معلق مؤقتاً
      setState(() {
        _selectedForComparison.add(property);
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم إضافة العقار للمقارنة',
            style: CairoTextStyles.bodyMedium),
          backgroundColor: Colors.blue));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            e.toString(),
            style: CairoTextStyles.bodyMedium),
          backgroundColor: Colors.red));
    }
  }

  /// إزالة من المقارنة
  Future<void> _removeFromComparison(Estate property) async {
    try {
      // await _comparisonService.removeFromComparison(property.id); // معلق مؤقتاً
      setState(() {
        _selectedForComparison.removeWhere((p) => p.id == property.id);
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم إزالة العقار من المقارنة',
            style: CairoTextStyles.bodyMedium),
          backgroundColor: Colors.orange));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'فشل في إزالة العقار من المقارنة',
            style: CairoTextStyles.bodyMedium),
          backgroundColor: Colors.red));
    }
  }

  // دوال المقارنة - معلقة مؤقتاً
  /*
  /// مقارنة العقارات المحددة
  void _compareSelectedProperties() {
    if (_selectedForComparison.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يجب اختيار عقارين على الأقل للمقارنة',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.orange));
      return;
    }

    // الانتقال لصفحة المقارنة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PropertyComparisonPage(
          properties: _selectedForComparison)));
  }

  /// مسح المقارنة
  void _clearComparison() {
    setState(() {
      _selectedForComparison.clear();
    });
    _comparisonService.clearComparison();
  }
  */
}
