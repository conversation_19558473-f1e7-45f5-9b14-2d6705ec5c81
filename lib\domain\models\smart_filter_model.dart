import 'package:equatable/equatable.dart';

/// نموذج الفلتر الذكي للعقارات
class SmartFilterModel extends Equatable {
  /// نوع الاستغلال (بيع، إيجار، مبادلة، دولي)
  final String? usageType;

  /// التصنيف الرئيسي (سكني، تجاري، أرض)
  final String? mainCategory;

  /// التصنيف الفرعي (شقة، منزل، مكتب، محل، الخ)
  final String? subCategory;

  /// نوع العقار (شقة، منزل، أرض، الخ)
  final String? propertyType;

  /// الموقع
  final String? location;

  /// المحافظة
  final String? governorate;

  /// المنطقة
  final String? area;

  /// الحد الأدنى للسعر
  final double? minPrice;

  /// الحد الأقصى للسعر
  final double? maxPrice;

  /// الحد الأدنى للمساحة
  final double? minArea;

  /// الحد الأقصى للمساحة
  final double? maxArea;

  /// عدد الغرف
  final int? numberOfRooms;

  /// عدد الحمامات
  final int? numberOfBathrooms;

  /// رقم الطابق
  final int? floorNumber;

  /// عمر البناء
  final int? buildingAge;

  /// المميزات
  final Map<String, bool> features;

  /// نوع الفرز
  final String sortBy;

  /// اتجاه الفرز (تصاعدي/تنازلي)
  final bool descending;

  const SmartFilterModel({
    this.usageType,
    this.mainCategory,
    this.subCategory,
    this.propertyType,
    this.location,
    this.governorate,
    this.area,
    this.minPrice,
    this.maxPrice,
    this.minArea,
    this.maxArea,
    this.numberOfRooms,
    this.numberOfBathrooms,
    this.floorNumber,
    this.buildingAge,
    this.features = const {},
    this.sortBy = 'createdAt',
    this.descending = true,
  });

  /// إنشاء نسخة معدلة من الفلتر
  SmartFilterModel copyWith({
    String? usageType,
    String? mainCategory,
    String? subCategory,
    String? propertyType,
    String? location,
    String? governorate,
    String? area,
    double? minPrice,
    double? maxPrice,
    double? minArea,
    double? maxArea,
    int? numberOfRooms,
    int? numberOfBathrooms,
    int? floorNumber,
    int? buildingAge,
    Map<String, bool>? features,
    String? sortBy,
    bool? descending,
  }) {
    return SmartFilterModel(
      usageType: usageType ?? this.usageType,
      mainCategory: mainCategory ?? this.mainCategory,
      subCategory: subCategory ?? this.subCategory,
      propertyType: propertyType ?? this.propertyType,
      location: location ?? this.location,
      governorate: governorate ?? this.governorate,
      area: area ?? this.area,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      minArea: minArea ?? this.minArea,
      maxArea: maxArea ?? this.maxArea,
      numberOfRooms: numberOfRooms ?? this.numberOfRooms,
      numberOfBathrooms: numberOfBathrooms ?? this.numberOfBathrooms,
      floorNumber: floorNumber ?? this.floorNumber,
      buildingAge: buildingAge ?? this.buildingAge,
      features: features ?? this.features,
      sortBy: sortBy ?? this.sortBy,
      descending: descending ?? this.descending);
  }

  /// تحويل إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'usageType': usageType,
      'mainCategory': mainCategory,
      'subCategory': subCategory,
      'propertyType': propertyType,
      'location': location,
      'governorate': governorate,
      'area': area,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'minArea': minArea,
      'maxArea': maxArea,
      'numberOfRooms': numberOfRooms,
      'numberOfBathrooms': numberOfBathrooms,
      'floorNumber': floorNumber,
      'buildingAge': buildingAge,
      'features': features,
      'sortBy': sortBy,
      'descending': descending,
    };
  }

  /// إنشاء من خريطة
  factory SmartFilterModel.fromMap(Map<String, dynamic> map) {
    return SmartFilterModel(
      usageType: map['usageType'],
      mainCategory: map['mainCategory'],
      subCategory: map['subCategory'],
      propertyType: map['propertyType'],
      location: map['location'],
      governorate: map['governorate'],
      area: map['area'],
      minPrice: map['minPrice']?.toDouble(),
      maxPrice: map['maxPrice']?.toDouble(),
      minArea: map['minArea']?.toDouble(),
      maxArea: map['maxArea']?.toDouble(),
      numberOfRooms: map['numberOfRooms']?.toInt(),
      numberOfBathrooms: map['numberOfBathrooms']?.toInt(),
      floorNumber: map['floorNumber']?.toInt(),
      buildingAge: map['buildingAge']?.toInt(),
      features: Map<String, bool>.from(map['features'] ?? {}),
      sortBy: map['sortBy'] ?? 'createdAt',
      descending: map['descending'] ?? true);
  }

  /// إعادة تعيين الفلتر
  SmartFilterModel reset() {
    return const SmartFilterModel();
  }

  /// التحقق من وجود فلاتر نشطة
  bool get hasActiveFilters {
    return usageType != null ||
           mainCategory != null ||
           subCategory != null ||
           propertyType != null ||
           location != null ||
           governorate != null ||
           area != null ||
           minPrice != null ||
           maxPrice != null ||
           minArea != null ||
           maxArea != null ||
           numberOfRooms != null ||
           numberOfBathrooms != null ||
           floorNumber != null ||
           buildingAge != null ||
           features.values.any((value) => value);
  }

  @override
  List<Object?> get props => [
        usageType,
        mainCategory,
        subCategory,
        propertyType,
        location,
        governorate,
        area,
        minPrice,
        maxPrice,
        minArea,
        maxArea,
        numberOfRooms,
        numberOfBathrooms,
        floorNumber,
        buildingAge,
        features,
        sortBy,
        descending,
      ];
}

/// أنواع الاستغلال
class UsageTypes {
  static const String sale = 'sale';
  static const String rent = 'rent';
  static const String swap = 'swap';
  static const String international = 'international';

  static const List<String> all = [sale, rent, swap, international];

  static String getDisplayName(String type) {
    switch (type) {
      case sale:
        return 'بيع';
      case rent:
        return 'إيجار';
      case swap:
        return 'مبادلة';
      case international:
        return 'دولي';
      default:
        return type;
    }
  }
}

/// أنواع الفرز
class SortTypes {
  static const String createdAt = 'createdAt';
  static const String price = 'price';
  static const String area = 'area';
  static const String views = 'views';
  static const String location = 'location';

  static const List<String> all = [createdAt, price, area, views, location];

  static String getDisplayName(String type) {
    switch (type) {
      case createdAt:
        return 'تاريخ الإنشاء';
      case price:
        return 'السعر';
      case area:
        return 'المساحة';
      case views:
        return 'المشاهدات';
      case location:
        return 'الموقع';
      default:
        return type;
    }
  }
}
