// lib/presentation/pages/property_request/edit_property_request_page.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';


import '../../../domain/models/property_request/property_request_model.dart';
import '../../providers/property_request_provider.dart';
import '../../widgets/enhanced_progress_indicator.dart';
import '../../widgets/property_request/property_type_selector.dart';
import '../../widgets/property_request/location_selector.dart';

/// صفحة تعديل طلب عقار
class EditPropertyRequestPage extends StatefulWidget {
  /// طلب العقار المراد تعديله
  final PropertyRequestModel request;

  const EditPropertyRequestPage({
    super.key,
    required this.request,
  });

  @override
  State<EditPropertyRequestPage> createState() => _EditPropertyRequestPageState();
}

class _EditPropertyRequestPageState extends State<EditPropertyRequestPage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _minPriceController;
  late TextEditingController _maxPriceController;
  late TextEditingController _minRoomsController;
  late TextEditingController _minBathroomsController;
  late TextEditingController _minAreaController;
  late TextEditingController _additionalRequirementsController;

  late String _selectedPropertyType;
  late List<String> _selectedLocations;
  late DateTime? _neededByDate;
  late bool _hasCentralAC;
  late bool _hasMaidRoom;
  late bool _hasGarage;
  late bool _hasSwimmingPool;
  late bool _hasElevator;
  late bool _isFullyFurnished;

  final List<File> _newImages = [];
  List<String> _existingImages = [];
  final List<String> _imagesToDelete = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // تهيئة القيم من الطلب الحالي
    _titleController = TextEditingController(text: widget.request.title);
    _descriptionController = TextEditingController(text: widget.request.description);
    _minPriceController = TextEditingController(
      text: widget.request.minPrice?.toString() ?? '');
    _maxPriceController = TextEditingController(
      text: widget.request.maxPrice?.toString() ?? '');
    _minRoomsController = TextEditingController(
      text: widget.request.minRooms?.toString() ?? '');
    _minBathroomsController = TextEditingController(
      text: widget.request.minBathrooms?.toString() ?? '');
    _minAreaController = TextEditingController(
      text: widget.request.minArea?.toString() ?? '');
    _additionalRequirementsController = TextEditingController(
      text: widget.request.additionalRequirements ?? '');

    _selectedPropertyType = widget.request.propertyType;
    _selectedLocations = List.from(widget.request.preferredLocations);
    _neededByDate = widget.request.neededBy;
    _hasCentralAC = widget.request.hasCentralAC;
    _hasMaidRoom = widget.request.hasMaidRoom;
    _hasGarage = widget.request.hasGarage;
    _hasSwimmingPool = widget.request.hasSwimmingPool;
    _hasElevator = widget.request.hasElevator;
    _isFullyFurnished = widget.request.isFullyFurnished;

    if (widget.request.images != null) {
      _existingImages = List.from(widget.request.images!);
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _minPriceController.dispose();
    _maxPriceController.dispose();
    _minRoomsController.dispose();
    _minBathroomsController.dispose();
    _minAreaController.dispose();
    _additionalRequirementsController.dispose();
    super.dispose();
  }

  /// اختيار الصور
  Future<void> _pickImages() async {
    final picker = ImagePicker();
    final pickedFiles = await picker.pickMultiImage();

    if (pickedFiles.isNotEmpty) {
      setState(() {
        _newImages.addAll(
          pickedFiles.map((file) => File(file.path)).toList());
      });
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.camera);

    if (pickedFile != null) {
      setState(() {
        _newImages.add(File(pickedFile.path));
      });
    }
  }

  /// حذف صورة جديدة
  void _removeNewImage(int index) {
    setState(() {
      _newImages.removeAt(index);
    });
  }

  /// حذف صورة موجودة
  void _removeExistingImage(int index) {
    setState(() {
      final imageUrl = _existingImages[index];
      _imagesToDelete.add(imageUrl);
      _existingImages.removeAt(index);
    });
  }

  /// اختيار تاريخ الحاجة
  Future<void> _selectNeededByDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _neededByDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.green)),
          child: child!);
      });

    if (pickedDate != null) {
      setState(() {
        _neededByDate = pickedDate;
      });
    }
  }

  /// تحديث طلب العقار
  Future<void> _updatePropertyRequest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedPropertyType.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يرجى اختيار نوع العقار',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
      return;
    }

    if (_selectedLocations.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يرجى اختيار منطقة واحدة على الأقل',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء نموذج طلب العقار المحدث
      final updatedRequest = widget.request.copyWith(
        title: _titleController.text,
        description: _descriptionController.text,
        propertyType: _selectedPropertyType,
        minPrice: _minPriceController.text.isNotEmpty
            ? double.parse(_minPriceController.text)
            : null,
        maxPrice: _maxPriceController.text.isNotEmpty
            ? double.parse(_maxPriceController.text)
            : null,
        preferredLocations: _selectedLocations,
        minRooms: _minRoomsController.text.isNotEmpty
            ? int.parse(_minRoomsController.text)
            : null,
        minBathrooms: _minBathroomsController.text.isNotEmpty
            ? int.parse(_minBathroomsController.text)
            : null,
        minArea: _minAreaController.text.isNotEmpty
            ? double.parse(_minAreaController.text)
            : null,
        hasCentralAC: _hasCentralAC,
        hasMaidRoom: _hasMaidRoom,
        hasGarage: _hasGarage,
        hasSwimmingPool: _hasSwimmingPool,
        hasElevator: _hasElevator,
        isFullyFurnished: _isFullyFurnished,
        additionalRequirements: _additionalRequirementsController.text.isNotEmpty
            ? _additionalRequirementsController.text
            : null,
        neededBy: _neededByDate,
        updatedAt: DateTime.now());

      // تحديث الطلب
      final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
      final success = await provider.updatePropertyRequest(
        updatedRequest,
        newImages: _newImages,
        imagesToDelete: _imagesToDelete.isNotEmpty ? _imagesToDelete : null);

      if (success && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تحديث الطلب بنجاح',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.green));
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل تحديث الطلب',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'حدث خطأ: $e',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تعديل طلب عقار',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        centerTitle: true),
      body: _isLoading
          ? Center(
              child: EnhancedProgressIndicator(
                currentStep: 1,
                totalSteps: 1,
                stepTitles: ['جاري التحميل']))
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان الطلب
                    TextFormField(
                      controller: _titleController,
                      decoration: InputDecoration(
                        labelText: 'عنوان الطلب',
                        hintText: 'مثال: أبحث عن شقة في السالمية',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8))),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال عنوان الطلب';
                        }
                        return null;
                      }),
                    const SizedBox(height: 16),

                    // وصف الطلب
                    TextFormField(
                      controller: _descriptionController,
                      decoration: InputDecoration(
                        labelText: 'وصف الطلب',
                        hintText: 'اكتب تفاصيل أكثر عن العقار الذي تبحث عنه',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8))),
                      maxLines: 5,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال وصف الطلب';
                        }
                        return null;
                      }),
                    const SizedBox(height: 16),

                    // نوع العقار
                    Text(
                      'نوع العقار',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    PropertyTypeSelector(
                      selectedType: _selectedPropertyType,
                      onTypeSelected: (type) {
                        setState(() {
                          _selectedPropertyType = type;
                        });
                      }),
                    const SizedBox(height: 16),

                    // المناطق المفضلة
                    Text(
                      'المناطق المفضلة',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    LocationSelector(
                      selectedLocations: _selectedLocations,
                      onLocationsChanged: (locations) {
                        setState(() {
                          _selectedLocations = locations;
                        });
                      }),
                    const SizedBox(height: 16),

                    // نطاق السعر
                    Text(
                      'نطاق السعر (د.ك)',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _minPriceController,
                            decoration: InputDecoration(
                              labelText: 'الحد الأدنى',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8))),
                            keyboardType: TextInputType.number)),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _maxPriceController,
                            decoration: InputDecoration(
                              labelText: 'الحد الأقصى',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8))),
                            keyboardType: TextInputType.number)),
                      ]),
                    const SizedBox(height: 16),

                    // عدد الغرف والحمامات
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _minRoomsController,
                            decoration: InputDecoration(
                              labelText: 'الحد الأدنى لعدد الغرف',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8))),
                            keyboardType: TextInputType.number)),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _minBathroomsController,
                            decoration: InputDecoration(
                              labelText: 'الحد الأدنى لعدد الحمامات',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8))),
                            keyboardType: TextInputType.number)),
                      ]),
                    const SizedBox(height: 16),

                    // المساحة
                    TextFormField(
                      controller: _minAreaController,
                      decoration: InputDecoration(
                        labelText: 'الحد الأدنى للمساحة (متر مربع)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8))),
                      keyboardType: TextInputType.number),
                    const SizedBox(height: 16),

                    // المميزات
                    Text(
                      'المميزات المطلوبة',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 16,
                      runSpacing: 8,
                      children: [
                        _buildFeatureCheckbox(
                          'تكييف مركزي',
                          _hasCentralAC,
                          (value) {
                            setState(() {
                              _hasCentralAC = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'غرفة خادمة',
                          _hasMaidRoom,
                          (value) {
                            setState(() {
                              _hasMaidRoom = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مرآب',
                          _hasGarage,
                          (value) {
                            setState(() {
                              _hasGarage = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مسبح',
                          _hasSwimmingPool,
                          (value) {
                            setState(() {
                              _hasSwimmingPool = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مصعد',
                          _hasElevator,
                          (value) {
                            setState(() {
                              _hasElevator = value ?? false;
                            });
                          }),
                        _buildFeatureCheckbox(
                          'مفروش بالكامل',
                          _isFullyFurnished,
                          (value) {
                            setState(() {
                              _isFullyFurnished = value ?? false;
                            });
                          }),
                      ]),
                    const SizedBox(height: 16),

                    // متطلبات إضافية
                    TextFormField(
                      controller: _additionalRequirementsController,
                      decoration: InputDecoration(
                        labelText: 'متطلبات إضافية',
                        hintText: 'أي متطلبات أخرى ترغب في إضافتها',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8))),
                      maxLines: 3),
                    const SizedBox(height: 16),

                    // تاريخ الحاجة
                    InkWell(
                      onTap: _selectNeededByDate,
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'مطلوب بحلول',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8)),
                          suffixIcon: const Icon(Icons.calendar_today)),
                        child: Text(
                          _neededByDate != null
                              ? DateFormat('yyyy/MM/dd').format(_neededByDate!)
                              : 'اختر تاريخ',
                          style: GoogleFonts.cairo()))),
                    const SizedBox(height: 16),

                    // الصور الحالية
                    if (_existingImages.isNotEmpty) ...[
                      Text(
                        'الصور الحالية',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 100,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: _existingImages.length,
                          itemBuilder: (context, index) {
                            return Stack(
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8)),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: CachedNetworkImage(
                                      imageUrl: _existingImages[index],
                                      fit: BoxFit.cover,
                                      placeholder: (context, url) => const Center(
                                        child: CircularProgressIndicator()),
                                      errorWidget: (context, url, error) => const Icon(
                                        Icons.error,
                                        color: Colors.red)))),
                                Positioned(
                                  top: 0,
                                  right: 8,
                                  child: InkWell(
                                    onTap: () => _removeExistingImage(index),
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: const BoxDecoration(
                                        color: Colors.red,
                                        shape: BoxShape.circle),
                                      child: const Icon(
                                        Icons.close,
                                        size: 16,
                                        color: Colors.white)))),
                              ]);
                          })),
                      const SizedBox(height: 16),
                    ],

                    // إضافة صور جديدة
                    Text(
                      'إضافة صور جديدة',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton.icon(
                          onPressed: _pickImages,
                          icon: const Icon(Icons.photo_library),
                          label: Text(
                            'اختيار من المعرض',
                            style: GoogleFonts.cairo()),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white)),
                        const SizedBox(width: 16),
                        ElevatedButton.icon(
                          onPressed: _takePhoto,
                          icon: const Icon(Icons.camera_alt),
                          label: Text(
                            'التقاط صورة',
                            style: GoogleFonts.cairo()),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white)),
                      ]),
                    const SizedBox(height: 8),
                    if (_newImages.isNotEmpty)
                      SizedBox(
                        height: 100,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: _newImages.length,
                          itemBuilder: (context, index) {
                            return Stack(
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    image: DecorationImage(
                                      image: FileImage(_newImages[index]),
                                      fit: BoxFit.cover))),
                                Positioned(
                                  top: 0,
                                  right: 8,
                                  child: InkWell(
                                    onTap: () => _removeNewImage(index),
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: const BoxDecoration(
                                        color: Colors.red,
                                        shape: BoxShape.circle),
                                      child: const Icon(
                                        Icons.close,
                                        size: 16,
                                        color: Colors.white)))),
                              ]);
                          })),
                    const SizedBox(height: 24),

                    // زر التحديث
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _updatePropertyRequest,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8))),
                        child: Text(
                          'تحديث الطلب',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold)))),
                  ]))));
  }

  /// بناء مربع اختيار الميزة
  Widget _buildFeatureCheckbox(
    String label,
    bool value,
    ValueChanged<bool?> onChanged) {
    return SizedBox(
      width: 150,
      child: CheckboxListTile(
        title: Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 14)),
        value: value,
        onChanged: onChanged,
        contentPadding: EdgeInsets.zero,
        controlAffinity: ListTileControlAffinity.leading,
        dense: true,
        activeColor: Colors.green));
  }
}
