import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as path;

import '../../../core/errors/exceptions.dart';
import '../data_cache_service.dart';

/// عميل API أساسي للتعامل مع الخدمات الخارجية
class ApiClient {
  final http.Client _httpClient;
  final FlutterSecureStorage _secureStorage;
  final DataCacheService _cacheService;
  final Connectivity _connectivity;

  ApiClient({
    http.Client? httpClient,
    FlutterSecureStorage? secureStorage,
    DataCacheService? cacheService,
    Connectivity? connectivity,
  })  : _httpClient = httpClient ?? http.Client(),
        _secureStorage = secureStorage ?? const FlutterSecureStorage(),
        _cacheService = cacheService ?? DataCacheService(),
        _connectivity = connectivity ?? Connectivity();

  /// إرسال طلب GET
  Future<dynamic> get(
    String url, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    bool requiresAuth = false,
    bool cache = false,
    Duration? cacheDuration,
  }) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        // إذا كان التخزين المؤقت مفعل، نحاول استرداد البيانات من التخزين المؤقت
        if (cache) {
          final cachedData = _cacheService.getCachedApiResponse(url);
          if (cachedData != null) {
            return cachedData;
          }
        }
        throw NoInternetException();
      }

      // إعداد رأس الطلب
      final requestHeaders = await _prepareHeaders(headers, requiresAuth);

      // إعداد عنوان URL مع معلمات الاستعلام
      final uri = _buildUri(url, queryParameters);

      // إرسال الطلب
      final response = await _httpClient.get(uri, headers: requestHeaders);

      // معالجة الاستجابة
      final data = _handleResponse(response);

      // تخزين البيانات في التخزين المؤقت إذا كان مفعلاً
      if (cache) {
        await _cacheService.cacheApiResponse(url, data, expiry: cacheDuration);
      }

      return data;
    } on SocketException {
      throw NoInternetException();
    } catch (e) {
      rethrow;
    }
  }

  /// إرسال طلب POST
  Future<dynamic> post(
    String url, {
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    bool requiresAuth = false,
  }) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        throw NoInternetException();
      }

      // إعداد رأس الطلب
      final requestHeaders = await _prepareHeaders(headers, requiresAuth);

      // إعداد عنوان URL
      final uri = Uri.parse(url);

      // تحويل الجسم إلى JSON
      final jsonBody = body != null ? jsonEncode(body) : null;

      // إرسال الطلب
      final response = await _httpClient.post(
        uri,
        headers: requestHeaders,
        body: jsonBody);

      // معالجة الاستجابة
      return _handleResponse(response);
    } on SocketException {
      throw NoInternetException();
    } catch (e) {
      rethrow;
    }
  }

  /// إرسال طلب PUT
  Future<dynamic> put(
    String url, {
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    bool requiresAuth = false,
  }) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        throw NoInternetException();
      }

      // إعداد رأس الطلب
      final requestHeaders = await _prepareHeaders(headers, requiresAuth);

      // إعداد عنوان URL
      final uri = Uri.parse(url);

      // تحويل الجسم إلى JSON
      final jsonBody = body != null ? jsonEncode(body) : null;

      // إرسال الطلب
      final response = await _httpClient.put(
        uri,
        headers: requestHeaders,
        body: jsonBody);

      // معالجة الاستجابة
      return _handleResponse(response);
    } on SocketException {
      throw NoInternetException();
    } catch (e) {
      rethrow;
    }
  }

  /// إرسال طلب DELETE
  Future<dynamic> delete(
    String url, {
    Map<String, String>? headers,
    bool requiresAuth = false,
  }) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        throw NoInternetException();
      }

      // إعداد رأس الطلب
      final requestHeaders = await _prepareHeaders(headers, requiresAuth);

      // إعداد عنوان URL
      final uri = Uri.parse(url);

      // إرسال الطلب
      final response = await _httpClient.delete(uri, headers: requestHeaders);

      // معالجة الاستجابة
      return _handleResponse(response);
    } on SocketException {
      throw NoInternetException();
    } catch (e) {
      rethrow;
    }
  }

  /// إعداد رأس الطلب
  Future<Map<String, String>> _prepareHeaders(
    Map<String, String>? headers,
    bool requiresAuth) async {
    final requestHeaders = headers ?? {};

    // إضافة رأس Content-Type إذا لم يكن موجوداً
    if (!requestHeaders.containsKey('Content-Type')) {
      requestHeaders['Content-Type'] = 'application/json';
    }

    // إضافة رأس Accept إذا لم يكن موجوداً
    if (!requestHeaders.containsKey('Accept')) {
      requestHeaders['Accept'] = 'application/json';
    }

    // إضافة رأس Authorization إذا كان الطلب يتطلب المصادقة
    if (requiresAuth) {
      final token = await _secureStorage.read(key: 'auth_token');
      if (token != null) {
        requestHeaders['Authorization'] = 'Bearer $token';
      }
    }

    return requestHeaders;
  }

  /// بناء عنوان URL مع معلمات الاستعلام
  Uri _buildUri(String url, Map<String, dynamic>? queryParameters) {
    final uri = Uri.parse(url);
    if (queryParameters != null) {
      return uri.replace(
        queryParameters: queryParameters.map(
          (key, value) => MapEntry(key, value.toString())));
    }
    return uri;
  }

  /// معالجة الاستجابة
  dynamic _handleResponse(http.Response response) {
    switch (response.statusCode) {
      case 200:
      case 201:
        // محاولة تحليل الاستجابة كـ JSON
        try {
          return jsonDecode(response.body);
        } catch (e) {
          // إذا لم تكن الاستجابة JSON، نعيد النص كما هو
          return response.body;
        }
      case 400:
        throw BadRequestException(response.body);
      case 401:
        throw UnauthorizedException(response.body);
      case 403:
        throw ForbiddenException(response.body);
      case 404:
        throw NotFoundException(response.body);
      case 500:
        throw ServerException(response.body);
      default:
        throw Exception('حدث خطأ غير متوقع: ${response.statusCode}');
    }
  }

  /// تحميل ملفات
  Future<dynamic> upload(
    String url, {
    Map<String, String>? headers,
    Map<String, dynamic>? files,
    Map<String, dynamic>? body,
    bool requiresAuth = false,
  }) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        throw NoInternetException();
      }

      // إعداد رأس الطلب
      final requestHeaders = await _prepareHeaders(headers, requiresAuth);

      // إنشاء طلب متعدد الأجزاء
      final request = http.MultipartRequest('POST', Uri.parse(url));
      request.headers.addAll(requestHeaders);

      // إضافة الملفات
      if (files != null) {
        for (final entry in files.entries) {
          if (entry.value is File) {
            final file = entry.value as File;
            final fileName = path.basename(file.path);
            final fileExtension = path.extension(file.path).replaceAll('.', '');

            request.files.add(
              await http.MultipartFile.fromPath(
                entry.key,
                file.path,
                contentType: MediaType('application', fileExtension),
                filename: fileName));
          }
        }
      }

      // إضافة البيانات الإضافية
      if (body != null) {
        body.forEach((key, value) {
          request.fields[key] = value.toString();
        });
      }

      // إرسال الطلب
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      // معالجة الاستجابة
      return _handleResponse(response);
    } on SocketException {
      throw NoInternetException();
    } catch (e) {
      rethrow;
    }
  }
}
