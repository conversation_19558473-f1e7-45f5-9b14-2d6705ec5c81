import 'package:equatable/equatable.dart';

/// نموذج تحليلات CRM
class CRMAnalytics extends Equatable {
  /// معرف التحليل
  final String id;
  
  /// معرف الوكيل/الشركة
  final String ownerId;
  
  /// نوع التحليل
  final String analysisType;
  
  /// فترة التحليل
  final DateTime startDate;
  final DateTime endDate;
  
  /// إحصائيات العملاء
  final ClientStatistics clientStats;
  
  /// إحصائيات التفاعلات
  final InteractionStatistics interactionStats;
  
  /// إحصائيات المواعيد
  final AppointmentStatistics appointmentStats;
  
  /// إحصائيات المبيعات
  final SalesStatistics salesStats;
  
  /// إحصائيات الأداء
  final PerformanceStatistics performanceStats;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث
  final DateTime updatedAt;

  const CRMAnalytics({
    required this.id,
    required this.ownerId,
    required this.analysisType,
    required this.startDate,
    required this.endDate,
    required this.clientStats,
    required this.interactionStats,
    required this.appointmentStats,
    required this.salesStats,
    required this.performanceStats,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id, ownerId, analysisType, startDate, endDate,
    clientStats, interactionStats, appointmentStats,
    salesStats, performanceStats, createdAt, updatedAt,
  ];
}

/// إحصائيات العملاء
class ClientStatistics extends Equatable {
  /// إجمالي العملاء
  final int totalClients;
  
  /// العملاء الجدد
  final int newClients;
  
  /// العملاء النشطين
  final int activeClients;
  
  /// العملاء غير النشطين
  final int inactiveClients;
  
  /// العملاء المحتملين
  final int leadClients;
  
  /// العملاء المحولين
  final int convertedClients;
  
  /// معدل التحويل
  final double conversionRate;
  
  /// متوسط قيمة العميل
  final double averageClientValue;
  
  /// توزيع العملاء حسب النوع
  final Map<String, int> clientsByType;
  
  /// توزيع العملاء حسب المصدر
  final Map<String, int> clientsBySource;
  
  /// توزيع العملاء حسب المنطقة
  final Map<String, int> clientsByLocation;

  const ClientStatistics({
    required this.totalClients,
    required this.newClients,
    required this.activeClients,
    required this.inactiveClients,
    required this.leadClients,
    required this.convertedClients,
    required this.conversionRate,
    required this.averageClientValue,
    required this.clientsByType,
    required this.clientsBySource,
    required this.clientsByLocation,
  });

  @override
  List<Object?> get props => [
    totalClients, newClients, activeClients, inactiveClients,
    leadClients, convertedClients, conversionRate, averageClientValue,
    clientsByType, clientsBySource, clientsByLocation,
  ];
}

/// إحصائيات التفاعلات
class InteractionStatistics extends Equatable {
  /// إجمالي التفاعلات
  final int totalInteractions;
  
  /// التفاعلات المكتملة
  final int completedInteractions;
  
  /// التفاعلات المعلقة
  final int pendingInteractions;
  
  /// متوسط التفاعلات يومياً
  final double averageInteractionsPerDay;
  
  /// متوسط مدة التفاعل
  final double averageInteractionDuration;
  
  /// توزيع التفاعلات حسب النوع
  final Map<String, int> interactionsByType;
  
  /// توزيع التفاعلات حسب النتيجة
  final Map<String, int> interactionsByOutcome;
  
  /// توزيع التفاعلات حسب الوقت
  final Map<String, int> interactionsByTime;
  
  /// معدل الاستجابة
  final double responseRate;
  
  /// متوسط وقت الاستجابة (بالساعات)
  final double averageResponseTime;

  const InteractionStatistics({
    required this.totalInteractions,
    required this.completedInteractions,
    required this.pendingInteractions,
    required this.averageInteractionsPerDay,
    required this.averageInteractionDuration,
    required this.interactionsByType,
    required this.interactionsByOutcome,
    required this.interactionsByTime,
    required this.responseRate,
    required this.averageResponseTime,
  });

  @override
  List<Object?> get props => [
    totalInteractions, completedInteractions, pendingInteractions,
    averageInteractionsPerDay, averageInteractionDuration,
    interactionsByType, interactionsByOutcome, interactionsByTime,
    responseRate, averageResponseTime,
  ];
}

/// إحصائيات المواعيد
class AppointmentStatistics extends Equatable {
  /// إجمالي المواعيد
  final int totalAppointments;
  
  /// المواعيد المكتملة
  final int completedAppointments;
  
  /// المواعيد الملغية
  final int cancelledAppointments;
  
  /// المواعيد المؤجلة
  final int rescheduledAppointments;
  
  /// معدل الحضور
  final double attendanceRate;
  
  /// معدل الإلغاء
  final double cancellationRate;
  
  /// متوسط المواعيد يومياً
  final double averageAppointmentsPerDay;
  
  /// متوسط مدة الموعد
  final double averageAppointmentDuration;
  
  /// توزيع المواعيد حسب النوع
  final Map<String, int> appointmentsByType;
  
  /// توزيع المواعيد حسب الوقت
  final Map<String, int> appointmentsByTime;

  const AppointmentStatistics({
    required this.totalAppointments,
    required this.completedAppointments,
    required this.cancelledAppointments,
    required this.rescheduledAppointments,
    required this.attendanceRate,
    required this.cancellationRate,
    required this.averageAppointmentsPerDay,
    required this.averageAppointmentDuration,
    required this.appointmentsByType,
    required this.appointmentsByTime,
  });

  @override
  List<Object?> get props => [
    totalAppointments, completedAppointments, cancelledAppointments,
    rescheduledAppointments, attendanceRate, cancellationRate,
    averageAppointmentsPerDay, averageAppointmentDuration,
    appointmentsByType, appointmentsByTime,
  ];
}

/// إحصائيات المبيعات
class SalesStatistics extends Equatable {
  /// إجمالي المبيعات
  final double totalSales;
  
  /// عدد الصفقات
  final int totalDeals;
  
  /// الصفقات المكتملة
  final int completedDeals;
  
  /// الصفقات المعلقة
  final int pendingDeals;
  
  /// متوسط قيمة الصفقة
  final double averageDealValue;
  
  /// معدل إغلاق الصفقات
  final double dealCloseRate;
  
  /// متوسط دورة المبيعات (بالأيام)
  final double averageSalesCycle;
  
  /// العمولات المكتسبة
  final double totalCommissions;
  
  /// توزيع المبيعات حسب النوع
  final Map<String, double> salesByType;
  
  /// توزيع المبيعات حسب الشهر
  final Map<String, double> salesByMonth;

  const SalesStatistics({
    required this.totalSales,
    required this.totalDeals,
    required this.completedDeals,
    required this.pendingDeals,
    required this.averageDealValue,
    required this.dealCloseRate,
    required this.averageSalesCycle,
    required this.totalCommissions,
    required this.salesByType,
    required this.salesByMonth,
  });

  @override
  List<Object?> get props => [
    totalSales, totalDeals, completedDeals, pendingDeals,
    averageDealValue, dealCloseRate, averageSalesCycle,
    totalCommissions, salesByType, salesByMonth,
  ];
}

/// إحصائيات الأداء
class PerformanceStatistics extends Equatable {
  /// نقاط الأداء الإجمالية
  final double totalPerformanceScore;
  
  /// تقييم رضا العملاء
  final double customerSatisfactionScore;
  
  /// معدل الاستجابة
  final double responseRate;
  
  /// معدل المتابعة
  final double followUpRate;
  
  /// معدل تحقيق الأهداف
  final double goalAchievementRate;
  
  /// الإنتاجية اليومية
  final double dailyProductivity;
  
  /// مؤشرات الأداء الرئيسية
  final Map<String, double> kpis;
  
  /// الاتجاهات والتوقعات
  final Map<String, dynamic> trends;

  const PerformanceStatistics({
    required this.totalPerformanceScore,
    required this.customerSatisfactionScore,
    required this.responseRate,
    required this.followUpRate,
    required this.goalAchievementRate,
    required this.dailyProductivity,
    required this.kpis,
    required this.trends,
  });

  @override
  List<Object?> get props => [
    totalPerformanceScore, customerSatisfactionScore,
    responseRate, followUpRate, goalAchievementRate,
    dailyProductivity, kpis, trends,
  ];
}
