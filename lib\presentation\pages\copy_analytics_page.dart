import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:kuwait_corners/core/services/copy_analytics_export_service.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;

/// صفحة تحليلات النسخ للوكلاء
class CopyAnalyticsPage extends StatefulWidget {
  const CopyAnalyticsPage({super.key});

  @override
  State<CopyAnalyticsPage> createState() => _CopyAnalyticsPageState();
}

class _CopyAnalyticsPageState extends State<CopyAnalyticsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'تحليلات النسخ',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white)),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.download_outlined),
            onPressed: _exportAnalytics),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {});
            }),
        ]),
      body: Stack(
        children: [
          // الأشكال الهندسية في الخلفية
          Positioned.fill(
            child: CustomPaint(
              painter: AnalyticsShapesPainter(),
            ),
          ),
          // المحتوى الرئيسي
          Column(
            children: [
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(),
                    _buildPerformanceTab(),
                    _buildRevenueTab(),
                  ])),
            ]),
        ]));
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        labelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          fontSize: 14),
        unselectedLabelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.w500,
          fontSize: 14),
        tabs: const [
          Tab(text: 'نظرة عامة'),
          Tab(text: 'الأداء'),
          Tab(text: 'الإيرادات'),
        ]));
  }

  Widget _buildOverviewTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getCopiedPropertiesStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل البيانات');
        }

        final properties = snapshot.data?.docs ?? [];
        final totalCopied = properties.length;
        final activeCopies = properties.where((p) {
          final data = p.data() as Map<String, dynamic>;
          return data['isActive'] == true && data['isPaid'] == true;
        }).length;
        final pendingPayment = properties.where((p) {
          final data = p.data() as Map<String, dynamic>;
          return data['isPaid'] == false;
        }).length;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatsCard(
                'إجمالي العقارات المنسوخة',
                totalCopied.toString(),
                Icons.content_copy,
                Colors.blue,
                'عقار منسوخ'),
              const SizedBox(height: 16),
              _buildStatsCard(
                'العقارات النشطة',
                activeCopies.toString(),
                Icons.check_circle,
                Colors.green,
                'عقار نشط'),
              const SizedBox(height: 16),
              _buildStatsCard(
                'في انتظار الدفع',
                pendingPayment.toString(),
                Icons.pending_actions,
                Colors.orange,
                'عقار معلق'),
              const SizedBox(height: 24),
              const Text(
                'أحدث العقارات المنسوخة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              _buildRecentCopiesList(properties),
            ]));
      });
  }

  Widget _buildPerformanceTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getCopiedPropertiesStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل بيانات الأداء');
        }

        final properties = snapshot.data?.docs ?? [];

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPerformanceMetrics(properties),
              const SizedBox(height: 24),
              const Text(
                'أداء العقارات حسب النوع',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              _buildPropertyTypePerformance(properties),
            ]));
      });
  }

  Widget _buildRevenueTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getCopiedPropertiesStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل بيانات الإيرادات');
        }

        final properties = snapshot.data?.docs ?? [];

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildRevenueMetrics(properties),
              const SizedBox(height: 24),
              const Text(
                'الإيرادات الشهرية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              _buildMonthlyRevenue(properties),
            ]));
      });
  }

  Widget _buildStatsCard(String title, String value, IconData icon, Color color, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.border, width: 1),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(14),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.12),
              borderRadius: BorderRadius.circular(14)),
            child: Icon(icon, color: color, size: 28)),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 13,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500)),
                const SizedBox(height: 6),
                Text(
                  value,
                  style: GoogleFonts.cairo(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary)),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: GoogleFonts.cairo(
                    fontSize: 11,
                    color: AppColors.textLight,
                    fontWeight: FontWeight.w500)),
              ])),
        ]));
  }

  Widget _buildRecentCopiesList(List<QueryDocumentSnapshot> properties) {
    final recentProperties = properties.take(5).toList();

    return Column(
      children: recentProperties.map((property) {
        final data = property.data() as Map<String, dynamic>;
        final title = data['title'] ?? 'عقار غير محدد';
        final isPaid = data['isPaid'] ?? false;
        final isActive = data['isActive'] ?? false;
        final copiedAt = data['copiedAt'] as Timestamp?;
        final views = data['views'] ?? 0;

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.border, width: 1),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: _getStatusColorForAnalytics(isPaid, isActive).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10)),
                child: Icon(
                  Icons.content_copy,
                  color: _getStatusColorForAnalytics(isPaid, isActive),
                  size: 20)),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis),
                    const SizedBox(height: 4),
                    Text(
                      copiedAt != null
                          ? 'تم النسخ: ${_formatDate(copiedAt.toDate())}'
                          : 'تاريخ غير محدد',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: AppColors.textLight)),
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        Icon(Icons.visibility_outlined, size: 14, color: AppColors.textLight),
                        const SizedBox(width: 4),
                        Text(
                          '$views مشاهدة',
                          style: GoogleFonts.cairo(
                            fontSize: 11,
                            color: AppColors.textLight)),
                      ]),
                  ])),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: _getStatusColorForAnalytics(isPaid, isActive).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: _getStatusColorForAnalytics(isPaid, isActive).withValues(alpha: 0.3))),
                child: Text(
                  _getStatusTextForAnalytics(isPaid, isActive),
                  style: GoogleFonts.cairo(
                    fontSize: 10,
                    color: _getStatusColorForAnalytics(isPaid, isActive),
                    fontWeight: FontWeight.w600))),
            ]));
      }).toList());
  }

  Widget _buildPerformanceMetrics(List<QueryDocumentSnapshot> properties) {
    final totalViews = properties.fold<int>(0, (total, property) {
      final data = property.data() as Map<String, dynamic>;
      return total + ((data['views'] ?? 0) as int);
    });

    final avgViews = properties.isNotEmpty ? (totalViews / properties.length).round() : 0;
    final successRate = properties.isNotEmpty
        ? ((properties.where((p) => (p.data() as Map<String, dynamic>)['isPaid'] == true).length / properties.length) * 100).round()
        : 0;

    return Column(
      children: [
        _buildStatsCard(
          'إجمالي المشاهدات',
          totalViews.toString(),
          Icons.visibility,
          Colors.purple,
          'مشاهدة'),
        const SizedBox(height: 16),
        _buildStatsCard(
          'متوسط المشاهدات',
          avgViews.toString(),
          Icons.trending_up,
          Colors.blue,
          'مشاهدة لكل عقار'),
        const SizedBox(height: 16),
        _buildStatsCard(
          'معدل النجاح',
          '$successRate%',
          Icons.check_circle,
          Colors.green,
          'من العقارات المدفوعة'),
      ]);
  }

  Widget _buildPropertyTypePerformance(List<QueryDocumentSnapshot> properties) {
    final typeStats = <String, int>{};

    for (final property in properties) {
      final data = property.data() as Map<String, dynamic>;
      final type = data['subCategory'] ?? 'غير محدد';
      typeStats[type] = (typeStats[type] ?? 0) + 1;
    }

    return Column(
      children: typeStats.entries.map((entry) => Card(
        margin: const EdgeInsets.only(bottom: 8),
        child: ListTile(
          title: Text(
            entry.key),
          trailing: Text(
            '${entry.value} عقار',
            style: const TextStyle(
              fontWeight: FontWeight.bold))))).toList());
  }

  Widget _buildRevenueMetrics(List<QueryDocumentSnapshot> properties) {
    final paidProperties = properties.where((p) => (p.data() as Map<String, dynamic>)['isPaid'] == true);
    final totalRevenue = paidProperties.length * 50; // افتراض 50 د.ك لكل عقار
    final pendingRevenue = (properties.length - paidProperties.length) * 50;

    return Column(
      children: [
        _buildStatsCard(
          'الإيرادات المحققة',
          '$totalRevenue د.ك',
          Icons.attach_money,
          Colors.green,
          'من العقارات المدفوعة'),
        const SizedBox(height: 16),
        _buildStatsCard(
          'الإيرادات المعلقة',
          '$pendingRevenue د.ك',
          Icons.pending,
          Colors.orange,
          'في انتظار الدفع'),
        const SizedBox(height: 16),
        _buildStatsCard(
          'إجمالي الإيرادات المحتملة',
          '${totalRevenue + pendingRevenue} د.ك',
          Icons.trending_up,
          Colors.blue,
          'الإيرادات الكاملة'),
      ]);
  }

  Widget _buildMonthlyRevenue(List<QueryDocumentSnapshot> properties) {
    // حساب الإيرادات الشهرية الحقيقية
    final monthlyRevenue = _calculateMonthlyRevenue(properties);

    if (monthlyRevenue.isEmpty) {
      return _buildNoRevenueMessage();
    }

    return Column(
      children: monthlyRevenue.entries.map((entry) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.border, width: 1),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10)),
                child: Icon(
                  Icons.calendar_month,
                  color: AppColors.primary,
                  size: 20)),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      entry.key,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary)),
                    const SizedBox(height: 4),
                    Text(
                      '${entry.value['count']} عقار مدفوع',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: AppColors.textSecondary)),
                  ])),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3))),
                child: Text(
                  '${entry.value['revenue']} د.ك',
                  style: GoogleFonts.cairo(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: Colors.green))),
            ]));
      }).toList());
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[600])),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {});
            },
            child: const Text('إعادة المحاولة')),
        ]));
  }

  Stream<QuerySnapshot> _getCopiedPropertiesStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('estates')
        .where('ownerId', isEqualTo: currentUser.uid)
        .where('isCopied', isEqualTo: true)
        .orderBy('copiedAt', descending: true)
        .snapshots();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// حساب الإيرادات الشهرية الحقيقية
  Map<String, Map<String, dynamic>> _calculateMonthlyRevenue(List<QueryDocumentSnapshot> properties) {
    final monthlyData = <String, Map<String, dynamic>>{};
    const double pricePerProperty = 50.0; // افتراض 50 د.ك لكل عقار

    for (final property in properties) {
      final data = property.data() as Map<String, dynamic>;
      final isPaid = data['isPaid'] ?? false;
      final copiedAt = data['copiedAt'] as Timestamp?;

      if (isPaid && copiedAt != null) {
        final date = copiedAt.toDate();
        final monthKey = DateFormat('MMMM yyyy', 'ar').format(date);

        if (!monthlyData.containsKey(monthKey)) {
          monthlyData[monthKey] = {
            'count': 0,
            'revenue': 0.0,
          };
        }

        monthlyData[monthKey]!['count'] = monthlyData[monthKey]!['count']! + 1;
        monthlyData[monthKey]!['revenue'] = monthlyData[monthKey]!['revenue']! + pricePerProperty;
      }
    }

    // ترتيب البيانات حسب التاريخ (الأحدث أولاً)
    final sortedEntries = monthlyData.entries.toList()
      ..sort((a, b) {
        try {
          final dateA = DateFormat('MMMM yyyy', 'ar').parse(a.key);
          final dateB = DateFormat('MMMM yyyy', 'ar').parse(b.key);
          return dateB.compareTo(dateA);
        } catch (e) {
          return 0;
        }
      });

    return Map.fromEntries(sortedEntries);
  }

  /// رسالة عدم وجود إيرادات
  Widget _buildNoRevenueMessage() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border, width: 1),
      ),
      child: Column(
        children: [
          Icon(
            Icons.money_off_outlined,
            size: 48,
            color: AppColors.textLight),
          const SizedBox(height: 16),
          Text(
            'لا توجد إيرادات شهرية',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary)),
          const SizedBox(height: 8),
          Text(
            'لم يتم دفع أي عقارات منسوخة بعد',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: AppColors.textLight)),
        ]));
  }

  Future<void> _exportAnalytics() async {
    try {
      // إظهار مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'جاري تصدير تحليلات النسخ...',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w500),
              ),
            ],
          ),
          backgroundColor: AppColors.primary,
          duration: const Duration(seconds: 10),
        ),
      );

      // تصدير التحليلات
      await CopyAnalyticsExportService.exportCopyAnalytics();

      // إخفاء مؤشر التحميل وإظهار رسالة النجاح
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 16),
                Text(
                  'تم تصدير تحليلات النسخ بنجاح!',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // إخفاء مؤشر التحميل وإظهار رسالة الخطأ
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'خطأ في تصدير التحليلات: ${e.toString()}',
                    style: GoogleFonts.cairo(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // دوال مساعدة للحالة والتصميم
  Color _getStatusColorForAnalytics(bool isPaid, bool isActive) {
    if (isPaid && isActive) return Colors.green;
    if (isPaid) return Colors.orange;
    return Colors.red;
  }

  String _getStatusTextForAnalytics(bool isPaid, bool isActive) {
    if (isPaid && isActive) return 'نشط';
    if (isPaid) return 'مدفوع';
    return 'معلق';
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}

/// رسام الأشكال الهندسية لصفحة التحليلات
class AnalyticsShapesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary.withValues(alpha: 0.05)
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = AppColors.primary.withValues(alpha: 0.08)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // إنشاء أشكال متنوعة معبرة عن التحليلات
    _drawAnalyticsShapes(canvas, size, paint, strokePaint);
  }

  void _drawAnalyticsShapes(Canvas canvas, Size size, Paint fillPaint, Paint strokePaint) {
    final random = math.Random(456); // seed ثابت للحصول على نفس النمط

    // رسم أشكال مختلفة
    for (int i = 0; i < 15; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final shapeType = random.nextInt(8);
      final scale = 0.4 + random.nextDouble() * 0.6; // حجم متغير

      canvas.save();
      canvas.translate(x, y);
      canvas.scale(scale);

      switch (shapeType) {
        case 0:
          _drawBarChart(canvas, strokePaint);
          break;
        case 1:
          _drawPieChart(canvas, fillPaint, strokePaint);
          break;
        case 2:
          _drawLineChart(canvas, strokePaint);
          break;
        case 3:
          _drawTrendArrow(canvas, fillPaint, strokePaint);
          break;
        case 4:
          _drawPercentageIcon(canvas, strokePaint);
          break;
        case 5:
          _drawCalculatorIcon(canvas, strokePaint);
          break;
        case 6:
          _drawGraphIcon(canvas, strokePaint);
          break;
        case 7:
          _drawTargetIcon(canvas, strokePaint);
          break;
      }

      canvas.restore();
    }
  }

  // رسم مخطط أعمدة
  void _drawBarChart(Canvas canvas, Paint strokePaint) {
    final heights = [8.0, 12.0, 6.0, 15.0, 10.0];
    final width = 3.0;

    for (int i = 0; i < heights.length; i++) {
      final rect = Rect.fromLTWH(
        -10 + i * 5,
        10 - heights[i],
        width,
        heights[i]);
      canvas.drawRect(rect, strokePaint);
    }
  }

  // رسم مخطط دائري
  void _drawPieChart(Canvas canvas, Paint fillPaint, Paint strokePaint) {
    final center = const Offset(0, 0);
    final radius = 12.0;

    // رسم الدائرة الأساسية
    canvas.drawCircle(center, radius, strokePaint);

    // رسم قطاعات
    final angles = [math.pi / 2, math.pi, 3 * math.pi / 2];
    for (final angle in angles) {
      canvas.drawLine(
        center,
        Offset(radius * math.cos(angle), radius * math.sin(angle)),
        strokePaint);
    }
  }

  // رسم مخطط خطي
  void _drawLineChart(Canvas canvas, Paint strokePaint) {
    final path = Path();
    final points = [
      const Offset(-12, 8),
      const Offset(-6, -2),
      const Offset(0, 4),
      const Offset(6, -8),
      const Offset(12, 0),
    ];

    path.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }

    canvas.drawPath(path, strokePaint);

    // رسم النقاط
    for (final point in points) {
      canvas.drawCircle(point, 2, strokePaint);
    }
  }

  // رسم سهم الاتجاه
  void _drawTrendArrow(Canvas canvas, Paint fillPaint, Paint strokePaint) {
    final path = Path();
    path.moveTo(-8, 4);
    path.lineTo(8, -4);
    path.lineTo(4, -8);
    path.moveTo(8, -4);
    path.lineTo(4, 0);

    canvas.drawPath(path, strokePaint);
  }

  // رسم أيقونة النسبة المئوية
  void _drawPercentageIcon(Canvas canvas, Paint strokePaint) {
    // رسم الخط المائل
    canvas.drawLine(const Offset(-8, 8), const Offset(8, -8), strokePaint);

    // رسم الدوائر
    canvas.drawCircle(const Offset(-6, 6), 3, strokePaint);
    canvas.drawCircle(const Offset(6, -6), 3, strokePaint);
  }

  // رسم أيقونة الآلة الحاسبة
  void _drawCalculatorIcon(Canvas canvas, Paint strokePaint) {
    // الإطار الخارجي
    final rect = const Rect.fromLTWH(-8, -10, 16, 20);
    canvas.drawRect(rect, strokePaint);

    // الشاشة
    final screenRect = const Rect.fromLTWH(-6, -8, 12, 4);
    canvas.drawRect(screenRect, strokePaint);

    // الأزرار
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 3; j++) {
        final buttonRect = Rect.fromLTWH(-4 + j * 3, -2 + i * 3, 2, 2);
        canvas.drawRect(buttonRect, strokePaint);
      }
    }
  }

  // رسم أيقونة الرسم البياني
  void _drawGraphIcon(Canvas canvas, Paint strokePaint) {
    // المحاور
    canvas.drawLine(const Offset(-10, 8), const Offset(10, 8), strokePaint);
    canvas.drawLine(const Offset(-8, -10), const Offset(-8, 10), strokePaint);

    // البيانات
    final path = Path();
    path.moveTo(-6, 4);
    path.quadraticBezierTo(0, -6, 6, 2);
    canvas.drawPath(path, strokePaint);
  }

  // رسم أيقونة الهدف
  void _drawTargetIcon(Canvas canvas, Paint strokePaint) {
    // الدوائر المتداخلة
    canvas.drawCircle(const Offset(0, 0), 12, strokePaint);
    canvas.drawCircle(const Offset(0, 0), 8, strokePaint);
    canvas.drawCircle(const Offset(0, 0), 4, strokePaint);

    // النقطة المركزية
    canvas.drawCircle(const Offset(0, 0), 1, strokePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
