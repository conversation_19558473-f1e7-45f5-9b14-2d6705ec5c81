import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../core/theme/app_colors.dart';
import '../../data/kuwait_locations.dart';
import '../../domain/entities/estate.dart';
import '../../domain/services/advanced_search_service.dart';
import '../widgets/estate_card.dart';

class AdvancedSearchPage extends StatefulWidget {
  const AdvancedSearchPage({super.key});

  @override
  State<AdvancedSearchPage> createState() => _AdvancedSearchPageState();
}

class _AdvancedSearchPageState extends State<AdvancedSearchPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final AdvancedSearchService _searchService = AdvancedSearchService();

  // متحكمات النص
  final TextEditingController _keywordController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();

  // فلاتر الموقع
  String? _selectedGovernorate;
  String? _selectedArea;

  // فلاتر البحث
  RangeValues _priceRange = const RangeValues(50, 10000);
  RangeValues _areaRange = const RangeValues(0, 1000);
  int? _selectedRooms;
  int? _selectedBathrooms;
  String? _selectedSubCategory; // نوع العقار (التصنيف الفرعي)
  String? _selectedMainCategory; // نوع الاستغلال (التصنيف الرئيسي)
  int? _selectedFloorNumber; // رقم الطابق
  int? _selectedBuildingAge; // عمر البناء
  final List<String> _selectedAmenities = [];
  bool _searchNearby = false;
  double _searchRadius = 5.0;
  bool _paidAdsOnly = false; // الإعلانات المدفوعة فقط
  bool _availableOnly = true; // الإعلانات المتاحة فقط

  // خريطة البحث
  GoogleMapController? _mapController;
  LatLng? _selectedLocation;
  Set<Marker> _markers = {};

  // فلاتر إضافية
  bool _hasVirtualTour = false;
  bool _hasVideo = false;
  bool _hasFloorPlan = false;

  // نتائج البحث
  List<Estate> _searchResults = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _keywordController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'البحث المتقدم',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: const Icon(Icons.search),
              text: 'فلاتر'),
            Tab(
              icon: const Icon(Icons.map),
              text: 'خريطة'),
            Tab(
              icon: const Icon(Icons.list),
              text: 'النتائج'),
          ]),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSearch),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearFilters),
        ]),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildFiltersTab(),
          _buildMapTab(),
          _buildResultsTab(),
        ]),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, -2)),
          ]),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isSearching ? null : _performSearch,
                icon: _isSearching
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
                    : const Icon(Icons.search),
                label: Text(
                  _isSearching ? 'جاري البحث...' : 'بحث',
                  style: GoogleFonts.cairo()),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12)))),
            const SizedBox(width: 12),
            ElevatedButton(
              onPressed: () => _tabController.animateTo(2),
              child: Text(
                'النتائج (${_searchResults.length})',
                style: GoogleFonts.cairo())),
          ])));
  }

  /// بناء تبويب الفلاتر
  Widget _buildFiltersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // البحث بالكلمات المفتاحية
          _buildSectionTitleWithHelp(
            'البحث بالكلمات المفتاحية',
            'ابحث في عنوان العقار، الوصف، أو أي تفاصيل أخرى'),
          TextField(
            controller: _keywordController,
            decoration: InputDecoration(
              hintText: 'مثال: شقة مفروشة، منزل حديث، أرض تجارية...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8)))),
          const SizedBox(height: 24),

          // الموقع
          _buildSectionTitleWithHelp(
            'الموقع',
            'اختر المحافظة والمنطقة المطلوبة'),

          // المحافظة
          _buildDropdown(
            value: _selectedGovernorate,
            items: KuwaitLocations.governorates,
            hint: 'اختر المحافظة',
            onChanged: (value) {
              setState(() {
                _selectedGovernorate = value;
                _selectedArea = null; // إعادة تعيين المنطقة عند تغيير المحافظة
              });
            }),
          const SizedBox(height: 16),

          // المنطقة
          if (_selectedGovernorate != null)
            _buildDropdown(
              value: _selectedArea,
              items: KuwaitLocations.getAreasByGovernorate(_selectedGovernorate!),
              hint: 'اختر المنطقة',
              onChanged: (value) {
                setState(() {
                  _selectedArea = value;
                });
              }),
          const SizedBox(height: 16),

          // البحث النصي (اختياري)
          TextField(
            controller: _locationController,
            decoration: InputDecoration(
              hintText: 'بحث إضافي بالكلمات المفتاحية...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8)))),
          const SizedBox(height: 16),

          // البحث القريب
          CheckboxListTile(
            title: Text(
              'البحث في المناطق القريبة',
              style: GoogleFonts.cairo()),
            value: _searchNearby,
            onChanged: (value) {
              setState(() {
                _searchNearby = value ?? false;
              });
            }),

          if (_searchNearby) ...[
            Text(
              'نطاق البحث: ${_searchRadius.toStringAsFixed(1)} كم',
              style: GoogleFonts.cairo(fontSize: 14)),
            Slider(
              value: _searchRadius,
              min: 1.0,
              max: 50.0,
              divisions: 49,
              onChanged: (value) {
                setState(() {
                  _searchRadius = value;
                });
              }),
          ],

          const SizedBox(height: 24),

          // نطاق السعر
          _buildSectionTitleWithHelp(
            'نطاق السعر (د.ك)',
            'حدد النطاق السعري المطلوب بالدينار الكويتي'),
          RangeSlider(
            values: _priceRange,
            min: 50,
            max: 10000,
            divisions: 100,
            labels: RangeLabels(
              _priceRange.start.toStringAsFixed(0),
              _priceRange.end.toStringAsFixed(0)),
            onChanged: (values) {
              setState(() {
                _priceRange = values;
              });
            }),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_priceRange.start.toStringAsFixed(0)} د.ك',
                style: GoogleFonts.cairo(fontSize: 12)),
              Text(
                '${_priceRange.end.toStringAsFixed(0)} د.ك',
                style: GoogleFonts.cairo(fontSize: 12)),
            ]),
          const SizedBox(height: 24),

          // نطاق المساحة
          _buildSectionTitle('نطاق المساحة (م²)'),
          RangeSlider(
            values: _areaRange,
            min: 0,
            max: 1000,
            divisions: 100,
            labels: RangeLabels(
              _areaRange.start.toStringAsFixed(0),
              _areaRange.end.toStringAsFixed(0)),
            onChanged: (values) {
              setState(() {
                _areaRange = values;
              });
            }),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_areaRange.start.toStringAsFixed(0)} م²',
                style: GoogleFonts.cairo(fontSize: 12)),
              Text(
                '${_areaRange.end.toStringAsFixed(0)} م²',
                style: GoogleFonts.cairo(fontSize: 12)),
            ]),
          const SizedBox(height: 24),

          // عدد الغرف
          _buildSectionTitle('عدد الغرف'),
          _buildNumberSelector(
            selectedValue: _selectedRooms,
            options: [1, 2, 3, 4, 5, 6],
            onChanged: (value) {
              setState(() {
                _selectedRooms = value;
              });
            }),
          const SizedBox(height: 24),

          // عدد الحمامات
          _buildSectionTitle('عدد الحمامات'),
          _buildNumberSelector(
            selectedValue: _selectedBathrooms,
            options: [1, 2, 3, 4, 5],
            onChanged: (value) {
              setState(() {
                _selectedBathrooms = value;
              });
            }),
          const SizedBox(height: 24),

          // نوع الاستغلال (التصنيف الرئيسي)
          _buildSectionTitleWithHelp(
            'نوع الاستغلال',
            'اختر الغرض من العقار (بيع، إيجار، بدل، إلخ)'),
          _buildDropdown(
            value: _selectedMainCategory,
            items: [
              'عقار للبيع',
              'عقار للايجار',
              'عقار للبدل',
              'عقار دولي',
              'عقار للاستثمار'
            ],
            hint: 'اختر نوع الاستغلال',
            onChanged: (value) {
              setState(() {
                _selectedMainCategory = value;
              });
            }),
          const SizedBox(height: 24),

          // نوع العقار (التصنيف الفرعي)
          _buildSectionTitleWithHelp(
            'نوع العقار',
            'اختر نوع العقار المطلوب (شقة، منزل، أرض، إلخ)'),
          _buildDropdown(
            value: _selectedSubCategory,
            items: ['شقة', 'منزل', 'بيت', 'أرض', 'مكتب', 'محل تجاري', 'مخزن'],
            hint: 'اختر نوع العقار',
            onChanged: (value) {
              setState(() {
                _selectedSubCategory = value;
              });
            }),
          const SizedBox(height: 24),

          // رقم الطابق
          _buildSectionTitleWithHelp(
            'رقم الطابق',
            'اختر رقم الطابق المطلوب (0 = الطابق الأرضي)'),
          _buildNumberSelector(
            selectedValue: _selectedFloorNumber,
            options: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            onChanged: (value) {
              setState(() {
                _selectedFloorNumber = value;
              });
            }),
          const SizedBox(height: 24),

          // عمر البناء
          _buildSectionTitleWithHelp(
            'عمر البناء (بالسنوات)',
            'اختر عمر البناء المطلوب (0 = جديد)'),
          _buildNumberSelector(
            selectedValue: _selectedBuildingAge,
            options: [0, 1, 2, 3, 4, 5, 10, 15, 20, 25, 30],
            onChanged: (value) {
              setState(() {
                _selectedBuildingAge = value;
              });
            }),
          const SizedBox(height: 24),

          // خيارات إضافية
          _buildSectionTitleWithHelp(
            'خيارات إضافية',
            'خيارات لتخصيص نتائج البحث حسب حالة الإعلان'),
          CheckboxListTile(
            title: Text(
              'الإعلانات المدفوعة فقط',
              style: GoogleFonts.cairo()),
            subtitle: Text(
              'عرض الإعلانات المميزة والمدفوعة فقط',
              style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600)),
            value: _paidAdsOnly,
            onChanged: (value) {
              setState(() {
                _paidAdsOnly = value ?? false;
              });
            }),
          CheckboxListTile(
            title: Text(
              'الإعلانات المتاحة فقط',
              style: GoogleFonts.cairo()),
            subtitle: Text(
              'إخفاء العقارات المباعة أو المؤجرة',
              style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600)),
            value: _availableOnly,
            onChanged: (value) {
              setState(() {
                _availableOnly = value ?? true;
              });
            }),
          const SizedBox(height: 24),

          // المرافق والمميزات
          _buildSectionTitleWithHelp(
            'المرافق والمميزات',
            'اختر المرافق والمميزات المطلوبة في العقار'),
          _buildAmenitiesSelector(),
        ]));
  }

  /// بناء تبويب الخريطة
  Widget _buildMapTab() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'اختر المنطقة على الخريطة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text(
                'انقر على الخريطة لتحديد منطقة البحث',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade600)),
            ])),
        Expanded(
          child: GoogleMap(
            initialCameraPosition: const CameraPosition(
              target: LatLng(29.3759, 47.9774), // الكويت
              zoom: 10),
            onMapCreated: (controller) {
              _mapController = controller;
            },
            onTap: (position) {
              setState(() {
                _selectedLocation = position;
                _markers = {
                  Marker(
                    markerId: const MarkerId('search_location'),
                    position: position,
                    infoWindow: const InfoWindow(
                      title: 'منطقة البحث')),
                };
              });
            },
            markers: _markers,
            circles: _selectedLocation != null && _searchNearby
                ? {
                    Circle(
                      circleId: const CircleId('search_radius'),
                      center: _selectedLocation!,
                      radius: _searchRadius * 1000, // تحويل إلى متر
                      fillColor: Theme.of(context).primaryColor.withOpacity(0.2),
                      strokeColor: Theme.of(context).primaryColor,
                      strokeWidth: 2),
                  }
                : {})),
      ]);
  }

  /// بناء تبويب النتائج
  Widget _buildResultsTab() {
    if (_searchResults.isEmpty && !_isSearching) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج بحث',
              style: GoogleFonts.cairo(
                fontSize: 18,
                color: Colors.grey.shade600)),
            const SizedBox(height: 8),
            Text(
              'قم بتعديل فلاتر البحث وحاول مرة أخرى',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500)),
          ]));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final property = _searchResults[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: EstateCard(estate: property));
      });
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor)));
  }

  /// بناء عنوان القسم مع نافذة مساعدة
  Widget _buildSectionTitleWithHelp(String title, String helpText) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor))),
          IconButton(
            icon: Icon(
              Icons.help_outline,
              color: Colors.grey.shade600,
              size: 20),
            onPressed: () => _showHelpDialog(title, helpText)),
        ]));
  }

  /// عرض نافذة المساعدة
  void _showHelpDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          title,
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
        content: Text(
          content,
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'فهمت',
              style: GoogleFonts.cairo(color: AppColors.primary))),
        ]));
  }

  /// بناء منتقي الأرقام
  Widget _buildNumberSelector({
    required int? selectedValue,
    required List<int> options,
    required ValueChanged<int?> onChanged,
  }) {
    return Wrap(
      spacing: 8,
      children: [
        // خيار "أي عدد"
        FilterChip(
          label: Text('أي عدد', style: GoogleFonts.cairo(fontSize: 12)),
          selected: selectedValue == null,
          onSelected: (selected) {
            if (selected) onChanged(null);
          }),
        // خيارات الأرقام
        ...options.map((number) {
          return FilterChip(
            label: Text('$number', style: GoogleFonts.cairo(fontSize: 12)),
            selected: selectedValue == number,
            onSelected: (selected) {
              onChanged(selected ? number : null);
            });
        }),
      ]);
  }

  /// بناء قائمة منسدلة
  Widget _buildDropdown({
    required String? value,
    required List<String> items,
    required String hint,
    required ValueChanged<String?> onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8))),
      hint: Text(hint, style: GoogleFonts.cairo()),
      items: items.map((item) {
        return DropdownMenuItem(
          value: item,
          child: Text(item, style: GoogleFonts.cairo()));
      }).toList(),
      onChanged: onChanged);
  }

  /// بناء منتقي المرافق
  Widget _buildAmenitiesSelector() {
    final amenities = [
      'تكييف مركزي',
      'مصعد',
      'مرآب',
      'غرفة خادمة',
      'مفروش',
      'حديقة',
      'مسبح',
      'صالة رياضة',
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: amenities.map((amenity) {
        return FilterChip(
          label: Text(amenity, style: GoogleFonts.cairo(fontSize: 12)),
          selected: _selectedAmenities.contains(amenity),
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _selectedAmenities.add(amenity);
              } else {
                _selectedAmenities.remove(amenity);
              }
            });
          });
      }).toList());
  }

  // تم حذف دالة _buildPropertyCard واستبدالها بـ EstateCard

  /// تنفيذ البحث
  Future<void> _performSearch() async {
    setState(() {
      _isSearching = true;
    });

    try {
      final searchCriteria = {
        'keyword': _keywordController.text,
        'location': _locationController.text,
        'governorate': _selectedGovernorate,
        'area': _selectedArea,
        'priceMin': _priceRange.start,
        'priceMax': _priceRange.end,
        'areaMin': _areaRange.start,
        'areaMax': _areaRange.end,
        'rooms': _selectedRooms,
        'bathrooms': _selectedBathrooms,
        'mainCategory': _selectedMainCategory, // نوع الاستغلال
        'subCategory': _selectedSubCategory, // نوع العقار
        'propertyType': _selectedSubCategory, // نوع العقار (للتوافق مع خدمة البحث)
        'floorNumber': _selectedFloorNumber,
        'buildingAge': _selectedBuildingAge,
        'isPaidAd': _paidAdsOnly ? true : null,
        'isAvailable': _availableOnly ? true : null,
        'amenities': _selectedAmenities,
        'searchLocation': _selectedLocation,
        'searchRadius': _searchNearby ? _searchRadius : null,
        'hasVirtualTour': _hasVirtualTour,
        'hasVideo': _hasVideo,
        'hasFloorPlan': _hasFloorPlan,
      };

      // طباعة معايير البحث للتشخيص
      print('🔍 معايير البحث المتقدم:');
      print('  propertyType: ${searchCriteria['propertyType']}');
      print('  subCategory: ${searchCriteria['subCategory']}');
      print('  mainCategory: ${searchCriteria['mainCategory']}');

      final results = await _searchService.searchProperties(searchCriteria);

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });

      // الانتقال لتبويب النتائج
      _tabController.animateTo(2);
    } catch (e) {
      setState(() {
        _isSearching = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'حدث خطأ في البحث: $e',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
    }
  }

  /// حفظ البحث
  void _saveSearch() {
    // TODO: Implement save search functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم حفظ البحث بنجاح',
          style: GoogleFonts.cairo()),
        backgroundColor: Colors.green));
  }

  /// مسح الفلاتر
  void _clearFilters() {
    setState(() {
      _keywordController.clear();
      _locationController.clear();
      _selectedGovernorate = null;
      _selectedArea = null;
      _priceRange = const RangeValues(50, 10000);
      _areaRange = const RangeValues(0, 1000);
      _selectedRooms = null;
      _selectedBathrooms = null;
      _selectedMainCategory = null; // نوع الاستغلال
      _selectedSubCategory = null; // نوع العقار
      _selectedFloorNumber = null;
      _selectedBuildingAge = null;
      _paidAdsOnly = false;
      _availableOnly = true;
      _selectedAmenities.clear();
      _searchNearby = false;
      _searchRadius = 5.0;
      _selectedLocation = null;
      _markers.clear();
      _searchResults.clear();
      _hasVirtualTour = false;
      _hasVideo = false;
      _hasFloorPlan = false;
    });
  }
}
