import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// أنواع الإشعارات
enum NotificationType {
  /// إشعار عام
  general,
  
  /// إشعار عن عقار جديد
  newEstate,
  
  /// إشعار عن تغيير في سعر عقار
  priceChange,
  
  /// إشعار عن عقار جديد يطابق معايير البحث
  matchingEstate,
  
  /// إشعار عن موعد جديد
  newAppointment,
  
  /// إشعار عن تغيير في حالة موعد
  appointmentStatusChange,
  
  /// إشعار عن عرض جديد
  newOffer,
  
  /// إشعار عن تغيير في حالة عرض
  offerStatusChange,
  
  /// إشعار عن رسالة جديدة
  newMessage,
  
  /// إشعار عن تقييم جديد
  newReview,
  
  /// إشعار عن تعليق جديد
  newComment,
  
  /// إشعار عن انتهاء صلاحية إعلان
  estateExpiration,
  
  /// إشعار عن انتهاء صلاحية اشتراك
  subscriptionExpiration,
}

/// نموذج للإشعارات
class Notification extends Equatable {
  /// معرف الإشعار
  final String id;
  
  /// معرف المستخدم
  final String userId;
  
  /// عنوان الإشعار
  final String title;
  
  /// نص الإشعار
  final String body;
  
  /// نوع الإشعار
  final NotificationType type;
  
  /// معرف العنصر المرتبط بالإشعار (عقار، موعد، عرض، الخ)
  final String? relatedItemId;
  
  /// نوع العنصر المرتبط بالإشعار
  final String? relatedItemType;
  
  /// بيانات إضافية
  final Map<String, dynamic>? data;
  
  /// ما إذا كان الإشعار مقروء
  final bool isRead;
  
  /// تاريخ إنشاء الإشعار
  final DateTime createdAt;
  
  /// تاريخ قراءة الإشعار
  final DateTime? readAt;
  
  /// ما إذا كان الإشعار مهم
  final bool isImportant;
  
  /// ما إذا كان الإشعار تم إرساله
  final bool isSent;
  
  /// ما إذا كان الإشعار تم تسليمه
  final bool isDelivered;

  const Notification({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    this.relatedItemId,
    this.relatedItemType,
    this.data,
    this.isRead = false,
    required this.createdAt,
    this.readAt,
    this.isImportant = false,
    this.isSent = false,
    this.isDelivered = false,
  });

  /// إنشاء نسخة معدلة من الإشعار
  Notification copyWith({
    String? id,
    String? userId,
    String? title,
    String? body,
    NotificationType? type,
    String? relatedItemId,
    String? relatedItemType,
    Map<String, dynamic>? data,
    bool? isRead,
    DateTime? createdAt,
    DateTime? readAt,
    bool? isImportant,
    bool? isSent,
    bool? isDelivered,
  }) {
    return Notification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      relatedItemId: relatedItemId ?? this.relatedItemId,
      relatedItemType: relatedItemType ?? this.relatedItemType,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      isImportant: isImportant ?? this.isImportant,
      isSent: isSent ?? this.isSent,
      isDelivered: isDelivered ?? this.isDelivered);
  }
  
  /// تحويل الإشعار إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'body': body,
      'type': type.toString().split('.').last,
      'relatedItemId': relatedItemId,
      'relatedItemType': relatedItemType,
      'data': data,
      'isRead': isRead,
      'createdAt': Timestamp.fromDate(createdAt),
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
      'isImportant': isImportant,
      'isSent': isSent,
      'isDelivered': isDelivered,
    };
  }
  
  /// إنشاء إشعار من Map
  factory Notification.fromMap(Map<String, dynamic> map) {
    return Notification(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      type: _getNotificationTypeFromString(map['type'] ?? 'general'),
      relatedItemId: map['relatedItemId'],
      relatedItemType: map['relatedItemType'],
      data: map['data'],
      isRead: map['isRead'] ?? false,
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      readAt: map['readAt'] is Timestamp 
          ? (map['readAt'] as Timestamp).toDate() 
          : null,
      isImportant: map['isImportant'] ?? false,
      isSent: map['isSent'] ?? false,
      isDelivered: map['isDelivered'] ?? false);
  }
  
  /// إنشاء إشعار من DocumentSnapshot
  factory Notification.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return Notification.fromMap(data);
  }
  
  /// الحصول على نوع الإشعار من النص
  static NotificationType _getNotificationTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'newEstate':
        return NotificationType.newEstate;
      case 'priceChange':
        return NotificationType.priceChange;
      case 'matchingEstate':
        return NotificationType.matchingEstate;
      case 'newAppointment':
        return NotificationType.newAppointment;
      case 'appointmentStatusChange':
        return NotificationType.appointmentStatusChange;
      case 'newOffer':
        return NotificationType.newOffer;
      case 'offerStatusChange':
        return NotificationType.offerStatusChange;
      case 'newMessage':
        return NotificationType.newMessage;
      case 'newReview':
        return NotificationType.newReview;
      case 'newComment':
        return NotificationType.newComment;
      case 'estateExpiration':
        return NotificationType.estateExpiration;
      case 'subscriptionExpiration':
        return NotificationType.subscriptionExpiration;
      default:
        return NotificationType.general;
    }
  }
  
  /// الحصول على اسم نوع الإشعار بالعربية
  String getNotificationTypeName() {
    switch (type) {
      case NotificationType.newEstate:
        return 'عقار جديد';
      case NotificationType.priceChange:
        return 'تغيير في السعر';
      case NotificationType.matchingEstate:
        return 'عقار يطابق بحثك';
      case NotificationType.newAppointment:
        return 'موعد جديد';
      case NotificationType.appointmentStatusChange:
        return 'تغيير في حالة الموعد';
      case NotificationType.newOffer:
        return 'عرض جديد';
      case NotificationType.offerStatusChange:
        return 'تغيير في حالة العرض';
      case NotificationType.newMessage:
        return 'رسالة جديدة';
      case NotificationType.newReview:
        return 'تقييم جديد';
      case NotificationType.newComment:
        return 'تعليق جديد';
      case NotificationType.estateExpiration:
        return 'انتهاء صلاحية إعلان';
      case NotificationType.subscriptionExpiration:
        return 'انتهاء صلاحية اشتراك';
      case NotificationType.general:
        return 'إشعار عام';
    }
  }
  
  /// الحصول على أيقونة نوع الإشعار
  String getNotificationTypeIcon() {
    switch (type) {
      case NotificationType.newEstate:
        return 'assets/icons/notification_new_estate.png';
      case NotificationType.priceChange:
        return 'assets/icons/notification_price_change.png';
      case NotificationType.matchingEstate:
        return 'assets/icons/notification_matching_estate.png';
      case NotificationType.newAppointment:
        return 'assets/icons/notification_appointment.png';
      case NotificationType.appointmentStatusChange:
        return 'assets/icons/notification_appointment_status.png';
      case NotificationType.newOffer:
        return 'assets/icons/notification_offer.png';
      case NotificationType.offerStatusChange:
        return 'assets/icons/notification_offer_status.png';
      case NotificationType.newMessage:
        return 'assets/icons/notification_message.png';
      case NotificationType.newReview:
        return 'assets/icons/notification_review.png';
      case NotificationType.newComment:
        return 'assets/icons/notification_comment.png';
      case NotificationType.estateExpiration:
        return 'assets/icons/notification_expiration.png';
      case NotificationType.subscriptionExpiration:
        return 'assets/icons/notification_subscription.png';
      case NotificationType.general:
        return 'assets/icons/notification_general.png';
    }
  }
  
  /// الحصول على لون نوع الإشعار
  String getNotificationTypeColor() {
    switch (type) {
      case NotificationType.newEstate:
        return '#4CAF50'; // أخضر
      case NotificationType.priceChange:
        return '#2196F3'; // أزرق
      case NotificationType.matchingEstate:
        return '#9C27B0'; // بنفسجي
      case NotificationType.newAppointment:
        return '#FF9800'; // برتقالي
      case NotificationType.appointmentStatusChange:
        return '#FF5722'; // برتقالي داكن
      case NotificationType.newOffer:
        return '#F44336'; // أحمر
      case NotificationType.offerStatusChange:
        return '#E91E63'; // وردي
      case NotificationType.newMessage:
        return '#00BCD4'; // سماوي
      case NotificationType.newReview:
        return '#8BC34A'; // أخضر فاتح
      case NotificationType.newComment:
        return '#CDDC39'; // ليموني
      case NotificationType.estateExpiration:
        return '#FFC107'; // أصفر
      case NotificationType.subscriptionExpiration:
        return '#FF5252'; // أحمر فاتح
      case NotificationType.general:
        return '#607D8B'; // رمادي
    }
  }
  
  /// تحديد ما إذا كان الإشعار مقروء
  Notification markAsRead() {
    return copyWith(
      isRead: true,
      readAt: DateTime.now());
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    title,
    body,
    type,
    relatedItemId,
    relatedItemType,
    data,
    isRead,
    createdAt,
    readAt,
    isImportant,
    isSent,
    isDelivered,
  ];
}
