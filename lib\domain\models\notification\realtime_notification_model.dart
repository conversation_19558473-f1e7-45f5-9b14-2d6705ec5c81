import 'package:flutter/material.dart';

/// نموذج الإشعار في الوقت الحقيقي
class RealtimeNotificationModel {
  /// معرف الإشعار
  final String id;
  
  /// معرف المستخدم المرسل إليه
  final String userId;
  
  /// عنوان الإشعار
  final String title;
  
  /// نص الإشعار
  final String body;
  
  /// نوع الإشعار
  final String type;
  
  /// بيانات إضافية للإشعار
  final Map<String, dynamic> data;
  
  /// تاريخ الإشعار
  final DateTime timestamp;
  
  /// ما إذا كان الإشعار مقروء
  bool isRead;

  /// إنشاء نموذج إشعار في الوقت الحقيقي
  RealtimeNotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    required this.timestamp,
    this.isRead = false,
  });

  /// إنشاء نموذج إشعار في الوقت الحقيقي من Firestore
  factory RealtimeNotificationModel.fromFirestore(Map<String, dynamic> data, String id) {
    return RealtimeNotificationModel(
      id: id,
      userId: data['userId'] ?? '',
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      type: data['type'] ?? 'other',
      data: data['data'] ?? {},
      timestamp: data['timestamp']?.toDate() ?? DateTime.now(),
      isRead: data['isRead'] ?? false);
  }

  /// تحويل نموذج الإشعار في الوقت الحقيقي إلى Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'title': title,
      'body': body,
      'type': type,
      'data': data,
      'timestamp': timestamp,
      'isRead': isRead,
    };
  }

  /// الحصول على أيقونة الإشعار
  IconData getNotificationIcon() {
    switch (type) {
      case 'newEstate':
        return Icons.home;
      case 'newMessage':
        return Icons.message;
      case 'accountVerification':
        return Icons.verified_user;
      case 'newPropertyRequestOffer':
        return Icons.local_offer;
      case 'propertyRequestOfferAccepted':
        return Icons.check_circle;
      case 'propertyRequestOfferRejected':
        return Icons.cancel;
      case 'propertyRequestStatusUpdate':
        return Icons.update;
      default:
        return Icons.notifications;
    }
  }

  /// الحصول على لون الإشعار
  Color getNotificationColor() {
    switch (type) {
      case 'newEstate':
        return Colors.blue;
      case 'newMessage':
        return Colors.green;
      case 'accountVerification':
        return Colors.purple;
      case 'newPropertyRequestOffer':
        return Colors.orange;
      case 'propertyRequestOfferAccepted':
        return Colors.green;
      case 'propertyRequestOfferRejected':
        return Colors.red;
      case 'propertyRequestStatusUpdate':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
