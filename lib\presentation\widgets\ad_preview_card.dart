// lib/presentation/widgets/ad_preview_card.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

/// بطاقة معاينة الإعلان
/// تعرض معاينة للإعلان قبل النشر
class AdPreviewCard extends StatelessWidget {
  /// عنوان الإعلان
  final String title;
  
  /// وصف الإعلان
  final String description;
  
  /// سعر العقار
  final double price;
  
  /// موقع العقار
  final String location;
  
  /// مسارات الصور المحلية
  final List<String> imagePaths;
  
  /// التصنيف الرئيسي
  final String mainCategory;
  
  /// التصنيف الفرعي
  final String subCategory;
  
  /// مساحة العقار
  final double? area;
  
  /// عدد الغرف
  final int? numberOfRooms;
  
  /// عدد الحمامات
  final int? numberOfBathrooms;
  
  /// عمر البناء
  final int? buildingAge;
  
  /// ما إذا كان العقار يحتوي على مرآب
  final bool hasGarage;
  
  /// ما إذا كان العقار يحتوي على تكييف مركزي
  final bool hasCentralAC;
  
  /// ما إذا كان العقار يحتوي على غرفة خادمة
  final bool hasMaidRoom;
  
  /// ما إذا كان العقار مفروش بالكامل
  final bool isFullyFurnished;

  const AdPreviewCard({
    super.key,
    required this.title,
    required this.description,
    required this.price,
    required this.location,
    required this.imagePaths,
    required this.mainCategory,
    required this.subCategory,
    this.area,
    this.numberOfRooms,
    this.numberOfBathrooms,
    this.buildingAge,
    this.hasGarage = false,
    this.hasCentralAC = false,
    this.hasMaidRoom = false,
    this.isFullyFurnished = false,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final primaryColor = Theme.of(context).primaryColor;
    
    // تنسيق السعر
    final priceFormat = NumberFormat("#,##0", "ar");
    final formattedPrice = priceFormat.format(price);
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.antiAlias,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // صور العقار
          SizedBox(
            height: 200,
            child: Stack(
              children: [
                // عارض الصور
                PageView.builder(
                  itemCount: imagePaths.isNotEmpty ? imagePaths.length : 1,
                  itemBuilder: (context, index) {
                    return imagePaths.isNotEmpty
                        ? Image.file(
                            File(imagePaths[index]),
                            fit: BoxFit.cover)
                        : Container(
                            color: Colors.grey.shade300,
                            child: Icon(
                              Icons.image_not_supported,
                              size: 50,
                              color: Colors.grey.shade600));
                  }),
                
                // طبقة تدرج شفافة في أسفل الصورة
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 80,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ])))),
                
                // معلومات العقار الأساسية فوق الطبقة الشفافة
                Positioned(
                  bottom: 16,
                  left: 16,
                  right: 16,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // السعر
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: primaryColor,
                          borderRadius: BorderRadius.circular(4)),
                        child: Text(
                          "$formattedPrice د.ك",
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white))),
                      const SizedBox(height: 4),
                      // العنوان
                      Text(
                        title,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.5),
                              blurRadius: 4,
                              offset: const Offset(0, 2)),
                          ]),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis),
                    ])),
                
                // شارة التصنيف
                Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(4)),
                    child: Text(
                      subCategory,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.white)))),
              ])),
          
          // تفاصيل العقار
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الموقع
                Row(
                  children: [
                    Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        location,
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade600),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis)),
                  ]),
                
                const SizedBox(height: 12),
                
                // المميزات الرئيسية
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (area != null)
                      _buildFeature(Icons.square_foot, "${area!.toInt()} م²"),
                    if (numberOfRooms != null)
                      _buildFeature(Icons.bed, "$numberOfRooms غرفة"),
                    if (numberOfBathrooms != null)
                      _buildFeature(Icons.bathtub, "$numberOfBathrooms حمام"),
                    if (buildingAge != null)
                      _buildFeature(Icons.calendar_today, "$buildingAge سنة"),
                  ]),
                
                const SizedBox(height: 12),
                
                // الوصف
                Text(
                  description,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade800),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis),
                
                const SizedBox(height: 12),
                
                // التجهيزات الإضافية
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    if (hasGarage)
                      _buildTag("مرآب"),
                    if (hasCentralAC)
                      _buildTag("تكييف مركزي"),
                    if (hasMaidRoom)
                      _buildTag("غرفة خادمة"),
                    if (isFullyFurnished)
                      _buildTag("مفروش بالكامل"),
                  ]),
              ])),
        ]));
  }
  
  /// بناء عنصر ميزة
  Widget _buildFeature(IconData icon, String text) {
    return Column(
      children: [
        Icon(icon, size: 20, color: Colors.grey.shade600),
        const SizedBox(height: 4),
        Text(
          text,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.grey.shade600)),
      ]);
  }
  
  /// بناء علامة (تاج)
  Widget _buildTag(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(4)),
      child: Text(
        text,
        style: GoogleFonts.cairo(
          fontSize: 12,
          color: Colors.grey.shade800)));
  }
}
