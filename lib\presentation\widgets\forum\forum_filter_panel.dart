import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/filter_options_model.dart';
import '../../providers/forum_provider.dart';

/// لوحة فلترة المنتدى
class ForumFilterPanel extends StatefulWidget {
  /// خيارات الفلترة الحالية
  final ForumFilterOptionsModel currentOptions;

  /// دالة يتم استدعاؤها عند تغيير خيارات الفلترة
  final Function(ForumFilterOptionsModel) onFilterChanged;

  const ForumFilterPanel({
    super.key,
    required this.currentOptions,
    required this.onFilterChanged,
  });

  @override
  State<ForumFilterPanel> createState() => _ForumFilterPanelState();
}

class _ForumFilterPanelState extends State<ForumFilterPanel>
    with SingleTickerProviderStateMixin {
  late ForumFilterOptionsModel _filterOptions;
  late TabController _tabController;
  final DateFormat _dateFormat = DateFormat('yyyy/MM/dd');

  @override
  void initState() {
    super.initState();
    _filterOptions = widget.currentOptions;
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 5)),
        ]),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // رأس اللوحة
          _buildHeader(),

          // علامات التبويب
          TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppColors.primary,
            tabs: [
              Tab(text: 'الأساسية'),
              Tab(text: 'متقدمة'),
              Tab(text: 'التاريخ'),
              Tab(text: 'المحتوى'),
            ]),

          // محتوى علامات التبويب
          SizedBox(
            height: 300,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildBasicFilters(),
                _buildAdvancedFilters(),
                _buildDateFilters(),
                _buildContentFilters(),
              ])),

          // أزرار التحكم
          _buildControlButtons(),
        ]));
  }

  /// بناء رأس اللوحة
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      child: Row(
        children: [
          Icon(Icons.filter_list, color: AppColors.primary),
          SizedBox(width: 8),
          Text(
            'فلترة وترتيب المواضيع',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primary)),
          Spacer(),
          IconButton(
            icon: Icon(Icons.close, color: Colors.grey),
            onPressed: () => Navigator.pop(context),
            tooltip: 'إغلاق'),
        ]));
  }

  /// بناء الفلاتر الأساسية
  Widget _buildBasicFilters() {
    final forumProvider = Provider.of<ForumProvider>(context);
    final categories = forumProvider.categories;

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // فلترة حسب الفئة
          Text(
            'الفئة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16)),
          SizedBox(height: 8),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8)),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String?>(
                isExpanded: true,
                value: _filterOptions.categoryId,
                hint: Text('جميع الفئات'),
                items: [
                  DropdownMenuItem<String?>(
                    value: null,
                    child: Text('جميع الفئات')),
                  ...categories.map((category) => DropdownMenuItem<String?>(
                        value: category.id,
                        child: Text(category.name))),
                ],
                onChanged: (value) {
                  setState(() {
                    _filterOptions = _filterOptions.copyWith(categoryId: value);
                  });
                  widget.onFilterChanged(_filterOptions);
                }))),
          SizedBox(height: 16),

          // فلترة حسب نوع الفرز
          Text(
            'ترتيب حسب',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16)),
          SizedBox(height: 8),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8)),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<ForumSortType>(
                isExpanded: true,
                value: _filterOptions.sortType,
                items: ForumSortType.values
                    .map((type) => DropdownMenuItem<ForumSortType>(
                          value: type,
                          child: Text(_getSortTypeName(type))))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _filterOptions = _filterOptions.copyWith(sortType: value);
                    });
                    widget.onFilterChanged(_filterOptions);
                  }
                }))),
          SizedBox(height: 16),

          // فلترة حسب اتجاه الفرز
          Row(
            children: [
              Expanded(
                child: RadioListTile<ForumSortDirection>(
                  title: Text('تصاعدي'),
                  value: ForumSortDirection.ascending,
                  groupValue: _filterOptions.sortDirection,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _filterOptions =
                            _filterOptions.copyWith(sortDirection: value);
                      });
                      widget.onFilterChanged(_filterOptions);
                    }
                  })),
              Expanded(
                child: RadioListTile<ForumSortDirection>(
                  title: Text('تنازلي'),
                  value: ForumSortDirection.descending,
                  groupValue: _filterOptions.sortDirection,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _filterOptions =
                            _filterOptions.copyWith(sortDirection: value);
                      });
                      widget.onFilterChanged(_filterOptions);
                    }
                  })),
            ]),

          // فلتر حسب الكلمات المفتاحية
          SizedBox(height: 16),
          Text(
            'الكلمات المفتاحية',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16)),
          SizedBox(height: 8),
          TextField(
            decoration: InputDecoration(
              hintText: 'أدخل الكلمات المفتاحية مفصولة بفواصل',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8)),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 12, vertical: 12)),
            onChanged: (value) {
              final keywords = value.isNotEmpty
                  ? value.split(',').map((e) => e.trim()).toList()
                  : null;
              setState(() {
                _filterOptions = _filterOptions.copyWith(keywords: keywords);
              });
              widget.onFilterChanged(_filterOptions);
            },
            controller: TextEditingController(
              text: _filterOptions.keywords?.join(', ') ?? '')),
        ]));
  }

  /// بناء الفلاتر المتقدمة
  Widget _buildAdvancedFilters() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // فلترة المواضيع المميزة
          SwitchListTile(
            title: Text('المواضيع المميزة فقط'),
            value: _filterOptions.featuredOnly,
            onChanged: (value) {
              setState(() {
                _filterOptions = _filterOptions.copyWith(featuredOnly: value);
              });
              widget.onFilterChanged(_filterOptions);
            }),

          // فلترة المواضيع المثبتة
          SwitchListTile(
            title: Text('المواضيع المثبتة فقط'),
            value: _filterOptions.pinnedOnly,
            onChanged: (value) {
              setState(() {
                _filterOptions = _filterOptions.copyWith(pinnedOnly: value);
              });
              widget.onFilterChanged(_filterOptions);
            }),

          // فلترة المواضيع المحلولة
          SwitchListTile(
            title: Text('المواضيع المحلولة فقط'),
            value: _filterOptions.solvedOnly,
            onChanged: (value) {
              setState(() {
                _filterOptions = _filterOptions.copyWith(solvedOnly: value);
              });
              widget.onFilterChanged(_filterOptions);
            }),

          // فلترة المواضيع التي تحتوي على صور
          SwitchListTile(
            title: Text('المواضيع التي تحتوي على صور فقط'),
            value: _filterOptions.withImagesOnly,
            onChanged: (value) {
              setState(() {
                _filterOptions = _filterOptions.copyWith(withImagesOnly: value);
              });
              widget.onFilterChanged(_filterOptions);
            }),

          Divider(),

          // فلترة المواضيع حسب عدد الردود
          Text(
            'عدد الردود',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16)),
          SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    labelText: 'الحد الأدنى',
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 12)),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final minReplies = int.tryParse(value);
                    setState(() {
                      _filterOptions =
                          _filterOptions.copyWith(minReplies: minReplies);
                    });
                    widget.onFilterChanged(_filterOptions);
                  },
                  controller: TextEditingController(
                    text: _filterOptions.minReplies?.toString() ?? ''))),
              SizedBox(width: 16),
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    labelText: 'الحد الأقصى',
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 12)),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final maxReplies = int.tryParse(value);
                    setState(() {
                      _filterOptions =
                          _filterOptions.copyWith(maxReplies: maxReplies);
                    });
                    widget.onFilterChanged(_filterOptions);
                  },
                  controller: TextEditingController(
                    text: _filterOptions.maxReplies?.toString() ?? ''))),
            ]),
        ]));
  }

  /// بناء فلاتر التاريخ
  Widget _buildDateFilters() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تاريخ النشر',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16)),
          SizedBox(height: 16),

          // تاريخ البداية
          ListTile(
            title: Text('من تاريخ'),
            subtitle: Text(
              _filterOptions.dateFrom != null
                  ? _dateFormat.format(_filterOptions.dateFrom!)
                  : 'غير محدد'),
            trailing: Icon(Icons.calendar_today),
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: _filterOptions.dateFrom ?? DateTime.now(),
                firstDate: DateTime(2020),
                lastDate: DateTime.now());
              if (date != null) {
                setState(() {
                  _filterOptions = _filterOptions.copyWith(dateFrom: date);
                });
                widget.onFilterChanged(_filterOptions);
              }
            }),

          // تاريخ النهاية
          ListTile(
            title: Text('إلى تاريخ'),
            subtitle: Text(
              _filterOptions.dateTo != null
                  ? _dateFormat.format(_filterOptions.dateTo!)
                  : 'غير محدد'),
            trailing: Icon(Icons.calendar_today),
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: _filterOptions.dateTo ?? DateTime.now(),
                firstDate: DateTime(2020),
                lastDate: DateTime.now());
              if (date != null) {
                setState(() {
                  _filterOptions = _filterOptions.copyWith(dateTo: date);
                });
                widget.onFilterChanged(_filterOptions);
              }
            }),
        ]));
  }

  /// بناء فلاتر المحتوى
  Widget _buildContentFilters() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نوع المحتوى',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16)),
          SizedBox(height: 8),

          // أنواع المحتوى
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: ForumContentType.values.map((type) {
              final isSelected =
                  _filterOptions.contentTypes?.contains(type) ?? false;
              return FilterChip(
                label: Text(_getContentTypeName(type)),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    final List<ForumContentType> contentTypes =
                        List.from(_filterOptions.contentTypes ?? []);
                    if (selected) {
                      contentTypes.add(type);
                    } else {
                      contentTypes.remove(type);
                    }
                    _filterOptions = _filterOptions.copyWith(
                      contentTypes: contentTypes.isEmpty ? null : contentTypes);
                  });
                  widget.onFilterChanged(_filterOptions);
                },
                selectedColor: AppColors.primary.withValues(alpha: 0.2),
                checkmarkColor: AppColors.primary);
            }).toList()),

          SizedBox(height: 24),

          Text(
            'تفاعلات المستخدم',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16)),
          SizedBox(height: 8),

          // المواضيع التي أعجبت المستخدم
          SwitchListTile(
            title: Text('المواضيع التي أعجبتني'),
            value: _filterOptions.likedByUser,
            onChanged: (value) {
              setState(() {
                _filterOptions = _filterOptions.copyWith(likedByUser: value);
              });
              widget.onFilterChanged(_filterOptions);
            }),

          // المواضيع التي حفظها المستخدم
          SwitchListTile(
            title: Text('المواضيع المحفوظة'),
            value: _filterOptions.bookmarkedByUser,
            onChanged: (value) {
              setState(() {
                _filterOptions =
                    _filterOptions.copyWith(bookmarkedByUser: value);
              });
              widget.onFilterChanged(_filterOptions);
            }),

          // المواضيع التي يتابعها المستخدم
          SwitchListTile(
            title: Text('المواضيع التي أتابعها'),
            value: _filterOptions.followedByUser,
            onChanged: (value) {
              setState(() {
                _filterOptions = _filterOptions.copyWith(followedByUser: value);
              });
              widget.onFilterChanged(_filterOptions);
            }),
        ]));
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(16))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () {
              setState(() {
                _filterOptions = ForumFilterOptionsModel.defaultOptions;
              });
              widget.onFilterChanged(_filterOptions);
            },
            child: Text('إعادة تعيين')),
          SizedBox(width: 16),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white),
            child: Text('تطبيق')),
        ]));
  }

  /// الحصول على اسم نوع الفرز
  String _getSortTypeName(ForumSortType type) {
    switch (type) {
      case ForumSortType.dateCreated:
        return 'تاريخ الإنشاء';
      case ForumSortType.lastUpdated:
        return 'آخر تحديث';
      case ForumSortType.views:
        return 'عدد المشاهدات';
      case ForumSortType.replies:
        return 'عدد الردود';
      case ForumSortType.likes:
        return 'عدد الإعجابات';
      case ForumSortType.activity:
        return 'الأكثر تفاعلاً';
      case ForumSortType.alphabetical:
        return 'الترتيب الأبجدي';
    }
  }

  /// الحصول على اسم نوع المحتوى
  String _getContentTypeName(ForumContentType type) {
    switch (type) {
      case ForumContentType.text:
        return 'نص';
      case ForumContentType.images:
        return 'صور';
      case ForumContentType.links:
        return 'روابط';
      case ForumContentType.poll:
        return 'استطلاع رأي';
      case ForumContentType.video:
        return 'فيديو';
      case ForumContentType.files:
        return 'ملفات';
    }
  }
}
