import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../providers/forum_provider.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/forum/forum_statistics_widget.dart';
import '../../widgets/forum/user_statistics_card.dart';

/// صفحة إحصائيات المنتدى
class ForumStatisticsPage extends StatefulWidget {
  const ForumStatisticsPage({super.key});

  @override
  State<ForumStatisticsPage> createState() => _ForumStatisticsPageState();
}

class _ForumStatisticsPageState extends State<ForumStatisticsPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  List<Map<String, dynamic>> _topUsers = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // تحميل إحصائيات المنتدى عند تهيئة الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ForumProvider>(context, listen: false).fetchForumStatistics();
    });

    _loadTopUsers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل أفضل المستخدمين
  Future<void> _loadTopUsers() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // في الإصدار الحقيقي، يجب استرجاع البيانات من الخدمة
      // هنا نستخدم بيانات وهمية للتوافق
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _topUsers = [
          {
            'userId': 'user1',
            'userName': 'أحمد محمد',
            'userImage': null,
            'level': 5,
            'points': 520,
            'topicsCount': 15,
            'postsCount': 48,
            'likesCount': 120,
          },
          {
            'userId': 'user2',
            'userName': 'سارة علي',
            'userImage': null,
            'level': 4,
            'points': 430,
            'topicsCount': 8,
            'postsCount': 62,
            'likesCount': 95,
          },
          {
            'userId': 'user3',
            'userName': 'محمد خالد',
            'userImage': null,
            'level': 6,
            'points': 680,
            'topicsCount': 22,
            'postsCount': 37,
            'likesCount': 150,
          },
          {
            'userId': 'user4',
            'userName': 'فاطمة أحمد',
            'userImage': null,
            'level': 3,
            'points': 320,
            'topicsCount': 5,
            'postsCount': 28,
            'likesCount': 60,
          },
          {
            'userId': 'user5',
            'userName': 'عمر حسن',
            'userImage': null,
            'level': 7,
            'points': 820,
            'topicsCount': 30,
            'postsCount': 75,
            'likesCount': 210,
          },
        ];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'إحصائيات المنتدى',
        showBackButton: true),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildForumStatisticsTab(),
                _buildTopUsersTab(),
              ])),
        ]));
  }

  /// بناء شريط التبويب
  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey,
        indicatorColor: AppColors.primary,
        tabs: const [
          Tab(
            icon: Icon(Icons.bar_chart),
            text: 'إحصائيات المنتدى'),
          Tab(
            icon: Icon(Icons.people),
            text: 'أفضل المستخدمين'),
        ]));
  }

  /// بناء تبويب إحصائيات المنتدى
  Widget _buildForumStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const ForumStatisticsWidget(
            isCompact: false),
          const SizedBox(height: 24),
          _buildRecentActivitySection(),
          const SizedBox(height: 24),
          _buildPopularTopicsSection(),
        ]));
  }

  /// بناء تبويب أفضل المستخدمين
  Widget _buildTopUsersTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ: $_errorMessage',
              style: const TextStyle(
                color: Colors.red),
              textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadTopUsers,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white)),
          ]));
    }

    if (_topUsers.isEmpty) {
      return const Center(
        child: Text(
          'لا يوجد مستخدمين متاحين',
          style: TextStyle(
            color: Colors.grey)));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _topUsers.length,
      itemBuilder: (context, index) {
        final user = _topUsers[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildUserCard(user, index + 1));
      });
  }

  /// بناء بطاقة مستخدم
  Widget _buildUserCard(Map<String, dynamic> user, int rank) {
    return Stack(
      children: [
        UserStatisticsCard(
          statistics: _mapToUserStatistics(user),
          isCompact: false,
          onTap: () {
            // التنقل إلى صفحة المستخدم
          }),
        Positioned(
          top: 16,
          left: 16,
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getRankColor(rank),
              shape: BoxShape.circle),
            child: Center(
              child: Text(
                rank.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold))))),
      ]);
  }

  /// تحويل خريطة إلى نموذج إحصائيات المستخدم
  dynamic _mapToUserStatistics(Map<String, dynamic> map) {
    // في الإصدار الحقيقي، يجب استخدام نموذج UserStatisticsModel
    // هنا نستخدم خريطة للتوافق
    return map;
  }

  /// الحصول على لون الترتيب
  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return Colors.amber;
      case 2:
        return Colors.grey.shade400;
      case 3:
        return Colors.brown.shade300;
      default:
        return AppColors.primary; // تغيير من الأزرق إلى الأخضر
    }
  }

  /// بناء قسم النشاط الأخير
  Widget _buildRecentActivitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'النشاط الأخير',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2)),
            ]),
          child: Column(
            children: [
              _buildActivityItem(
                icon: Icons.topic,
                title: 'تم إنشاء موضوع جديد',
                subtitle: 'أفضل المناطق السكنية في الكويت',
                time: 'منذ 5 دقائق',
                color: AppColors.primary),
              const Divider(),
              _buildActivityItem(
                icon: Icons.comment,
                title: 'تم إضافة مشاركة جديدة',
                subtitle: 'في موضوع: نصائح لشراء عقار',
                time: 'منذ 15 دقيقة',
                color: Colors.green),
              const Divider(),
              _buildActivityItem(
                icon: Icons.poll,
                title: 'تم إنشاء استطلاع جديد',
                subtitle: 'أي منطقة تفضل للسكن؟',
                time: 'منذ ساعة',
                color: Colors.orange),
            ])),
      ]);
  }

  /// بناء عنصر نشاط
  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required String time,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Color.fromRGBO(
                color.r.toInt(),
                color.g.toInt(),
                color.b.toInt(),
                0.1),
              shape: BoxShape.circle),
            child: Icon(
              icon,
              color: color)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold)),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12)),
              ])),
          Text(
            time,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 12)),
        ]));
  }

  /// بناء قسم المواضيع الشائعة
  Widget _buildPopularTopicsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المواضيع الشائعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2)),
            ]),
          child: Column(
            children: [
              _buildPopularTopicItem(
                title: 'أفضل المناطق السكنية في الكويت',
                views: 1250,
                replies: 45,
                likes: 120),
              const Divider(),
              _buildPopularTopicItem(
                title: 'نصائح لشراء عقار',
                views: 980,
                replies: 32,
                likes: 85),
              const Divider(),
              _buildPopularTopicItem(
                title: 'أسعار العقارات في 2023',
                views: 1500,
                replies: 60,
                likes: 150),
            ])),
      ]);
  }

  /// بناء عنصر موضوع شائع
  Widget _buildPopularTopicItem({
    required String title,
    required int views,
    required int replies,
    required int likes,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16)),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildTopicStat(
                icon: Icons.visibility,
                value: views.toString(),
                label: 'مشاهدة'),
              const SizedBox(width: 16),
              _buildTopicStat(
                icon: Icons.comment,
                value: replies.toString(),
                label: 'رد'),
              const SizedBox(width: 16),
              _buildTopicStat(
                icon: Icons.favorite,
                value: likes.toString(),
                label: 'إعجاب'),
            ]),
        ]));
  }

  /// بناء إحصائية موضوع
  Widget _buildTopicStat({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14)),
        const SizedBox(width: 2),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 12)),
      ]);
  }
}
