import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:kuwait_corners/presentation/widgets/project_background_widget.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

/// صفحة إدارة فريق العمل للشركات العقارية
class TeamManagementPage extends StatefulWidget {
  const TeamManagementPage({super.key});

  @override
  State<TeamManagementPage> createState() => _TeamManagementPageState();
}

class _TeamManagementPageState extends State<TeamManagementPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'فريق العمل',
          style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showTeamAnalytics,
            tooltip: 'تحليلات الفريق'),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _exportTeamData,
            tooltip: 'تصدير البيانات'),
        ]),
      body: ProjectBackgroundWidget(
        child: Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildTeamList()),
          ])),
      floatingActionButton: FloatingActionButton(
        onPressed: _addTeamMember,
        backgroundColor: AppColors.primary,
        tooltip: 'إضافة عضو فريق',
        child: const Icon(Icons.person_add, color: Colors.white)));
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث عن عضو في الفريق...',
          hintStyle: CairoTextStyles.hint,
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  })
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!)),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.primary))),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        }));
  }

  Widget _buildTeamList() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getTeamMembersStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل أعضاء الفريق');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState();
        }

        final teamMembers = snapshot.data!.docs;
        final filteredMembers = _filterTeamMembers(teamMembers);

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredMembers.length,
            itemBuilder: (context, index) {
              final memberDoc = filteredMembers[index];
              final memberData = memberDoc.data() as Map<String, dynamic>;
              return _buildTeamMemberCard(memberData, memberDoc.id);
            }));
      });
  }

  Widget _buildTeamMemberCard(Map<String, dynamic> memberData, String memberId) {
    final name = memberData['fullName'] ?? 'غير محدد';
    final role = _getRoleDisplayName(memberData['role'] ?? 'salesAgent');
    final email = memberData['email'] ?? '';
    final phone = memberData['phoneNumber'] ?? '';
    final status = memberData['status'] ?? 'active';
    final isActive = status == 'active';
    final totalSales = (memberData['totalSales'] ?? 0.0).toDouble();
    final soldCount = memberData['soldRentedCount'] ?? 0;
    final clientsCount = memberData['clientsCount'] ?? 0;
    final rating = (memberData['averageRating'] ?? 0.0).toDouble();
    final joinDate = memberData['joinDate'] as Timestamp?;
    final imageUrl = memberData['profileImage'] ?? '';

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: InkWell(
        onTap: () => _viewMemberDetails(memberId),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: الصورة والمعلومات الأساسية
              Row(
                children: [
                  // صورة العضو مع gradient
                  Container(
                    width: 45,
                    height: 45,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.7)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: imageUrl.isNotEmpty
                        ? ClipOval(
                            child: Image.network(
                              imageUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Icon(
                                    Icons.person,
                                    color: Colors.white,
                                    size: 22,
                                  ),
                            ),
                          )
                        : Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 22,
                          ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                name,
                                style: CairoTextStyles.bodyLarge.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: _getStatusColor(status).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: _getStatusColor(status)),
                              ),
                              child: Text(
                                _getStatusDisplayName(status),
                                style: CairoTextStyles.labelSmall.copyWith(
                                  color: _getStatusColor(status),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          role,
                          style: CairoTextStyles.bodyMedium.copyWith(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (rating > 0) ...[
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(Icons.star, size: 14, color: Colors.amber),
                              const SizedBox(width: 4),
                              Text(
                                rating.toStringAsFixed(1),
                                style: CairoTextStyles.bodySmall.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.amber[700],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // الإحصائيات المدمجة
              Row(
                children: [
                  Expanded(
                    child: _buildCompactStat(
                      Icons.attach_money,
                      '${totalSales.toStringAsFixed(0)} د.ك',
                      'المبيعات',
                      AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildCompactStat(
                      Icons.home_work,
                      '$soldCount',
                      'العقارات',
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildCompactStat(
                      Icons.people,
                      '$clientsCount',
                      'العملاء',
                      Colors.blue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // معلومات الاتصال والتاريخ
              if (email.isNotEmpty || phone.isNotEmpty || joinDate != null)
                Row(
                  children: [
                    if (email.isNotEmpty) ...[
                      Icon(Icons.email, size: 14, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          email,
                          style: CairoTextStyles.bodySmall.copyWith(
                            color: Colors.grey[600],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                    if (phone.isNotEmpty) ...[
                      const SizedBox(width: 8),
                      Icon(Icons.phone, size: 14, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        phone,
                        style: CairoTextStyles.bodySmall.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                    if (joinDate != null) ...[
                      const Spacer(),
                      Icon(Icons.calendar_today, size: 14, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(joinDate.toDate()),
                        style: CairoTextStyles.bodySmall.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              const SizedBox(height: 8),
              // أزرار الإجراءات المدمجة
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _viewMemberDetails(memberId),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('التفاصيل', style: TextStyle(fontSize: 12)),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () => _showMemberAnalytics(memberId),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('تحليلات', style: TextStyle(fontSize: 12)),
                  ),
                  const SizedBox(width: 8),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMemberAction(value, memberId),
                    icon: Icon(Icons.more_vert, color: Colors.grey[600], size: 20),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: isActive ? 'deactivate' : 'activate',
                        child: Row(
                          children: [
                            Icon(isActive ? Icons.pause : Icons.play_arrow, size: 16),
                            const SizedBox(width: 8),
                            Text(isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.group_outlined,
            size: 80,
            color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            'لا يوجد أعضاء في الفريق',
            style: CairoTextStyles.headlineSmall.copyWith(
              color: Colors.grey)),
        ]));
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: CairoTextStyles.headlineSmall.copyWith(
              color: Colors.red[600])),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {});
            },
            child: Text('إعادة المحاولة', style: CairoTextStyles.button)),
        ]));
  }

  // Firebase Data Streams
  Stream<QuerySnapshot> _getTeamMembersStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('team_members')
        .where('companyId', isEqualTo: currentUser.uid)
        .orderBy('fullName')
        .snapshots();
  }

  List<QueryDocumentSnapshot> _filterTeamMembers(List<QueryDocumentSnapshot> members) {
    if (_searchQuery.isEmpty) return members;

    final searchTerm = _searchQuery.toLowerCase();
    return members.where((member) {
      final data = member.data() as Map<String, dynamic>;
      final name = (data['fullName'] ?? '').toString().toLowerCase();
      final role = (data['role'] ?? '').toString().toLowerCase();
      final email = (data['email'] ?? '').toString().toLowerCase();
      final phone = (data['phoneNumber'] ?? '').toString().toLowerCase();

      return name.contains(searchTerm) ||
             role.contains(searchTerm) ||
             email.contains(searchTerm) ||
             phone.contains(searchTerm);
    }).toList();
  }

  // Helper Methods
  String _getRoleDisplayName(String role) {
    const roleNames = {
      'manager': 'مدير',
      'supervisor': 'مشرف',
      'salesAgent': 'وكيل مبيعات',
      'marketer': 'مسوق',
      'customerService': 'خدمة العملاء',
      'admin': 'مسؤول إداري',
    };
    return roleNames[role] ?? 'غير محدد';
  }

  String _getStatusDisplayName(String status) {
    const statusNames = {
      'active': 'نشط',
      'inactive': 'غير نشط',
      'onLeave': 'في إجازة',
      'suspended': 'معلق',
    };
    return statusNames[status] ?? 'غير محدد';
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.grey;
      case 'onLeave':
        return Colors.orange;
      case 'suspended':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, size: 20, color: AppColors.primary),
          const SizedBox(height: 4),
          Text(
            value,
            style: CairoTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold)),
          Text(
            label,
            style: CairoTextStyles.bodySmall.copyWith(
              color: Colors.grey[600]),
            textAlign: TextAlign.center),
        ]));
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Action Methods
  void _viewMemberDetails(String memberId) {
    Navigator.pushNamed(context, '/member-details', arguments: memberId);
  }

  void _editMember(String memberId) {
    Navigator.pushNamed(context, '/edit-member', arguments: memberId);
  }

  void _handleMemberAction(String action, String memberId) {
    switch (action) {
      case 'edit':
        _editMember(memberId);
        break;
      case 'activate':
      case 'deactivate':
        _toggleMemberStatus(memberId, action == 'activate');
        break;
      case 'delete':
        _deleteMember(memberId);
        break;
    }
  }

  Widget _buildCompactStat(IconData icon, String value, String label, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            value,
            style: CairoTextStyles.bodySmall.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: CairoTextStyles.labelSmall.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showMemberAnalytics(String memberId) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildMemberAnalyticsSheet(memberId),
    );
  }

  Widget _buildMemberAnalyticsSheet(String memberId) {
    return DraggableScrollableSheet(
      initialChildSize: 0.8,
      maxChildSize: 0.95,
      minChildSize: 0.5,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.analytics, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'تحليلات أداء العضو',
                      style: CairoTextStyles.headlineSmall.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              const Divider(),
              // Content
              Expanded(
                child: FutureBuilder<DocumentSnapshot>(
                  future: FirebaseFirestore.instance
                      .collection('team_members')
                      .doc(memberId)
                      .get(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const LoadingWidget();
                    }

                    if (!snapshot.hasData || !snapshot.data!.exists) {
                      return const Center(
                        child: Text('لم يتم العثور على العضو'),
                      );
                    }

                    final memberData = snapshot.data!.data() as Map<String, dynamic>;
                    return _buildMemberAnalyticsContent(memberData, scrollController);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _toggleMemberStatus(String memberId, bool activate) async {
    try {
      await FirebaseFirestore.instance
          .collection('team_members')
          .doc(memberId)
          .update({'status': activate ? 'active' : 'inactive'});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(activate ? 'تم تفعيل العضو' : 'تم إلغاء تفعيل العضو')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ في تحديث حالة العضو')));
      }
    }
  }

  void _deleteMember(String memberId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف', style: CairoTextStyles.headlineSmall),
        content: Text('هل أنت متأكد من حذف هذا العضو؟', style: CairoTextStyles.bodyMedium),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: CairoTextStyles.labelMedium)),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text('حذف', style: CairoTextStyles.labelMedium.copyWith(color: Colors.red))),
        ]));

    if (confirmed == true) {
      try {
        await FirebaseFirestore.instance
            .collection('team_members')
            .doc(memberId)
            .delete();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف العضو بنجاح')));
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('حدث خطأ في حذف العضو')));
        }
      }
    }
  }

  void _addTeamMember() {
    Navigator.pushNamed(context, '/add-team-member', arguments: '').then((result) {
      if (result == true) {
        setState(() {});
      }
    });
  }

  Widget _buildMemberAnalyticsContent(Map<String, dynamic> memberData, ScrollController scrollController) {
    final name = memberData['fullName'] ?? 'غير محدد';
    final totalSales = (memberData['totalSales'] ?? 0.0).toDouble();
    final soldCount = memberData['soldRentedCount'] ?? 0;
    final clientsCount = memberData['clientsCount'] ?? 0;
    final rating = (memberData['averageRating'] ?? 0.0).toDouble();
    final joinDate = memberData['joinDate'] as Timestamp?;
    final role = _getRoleDisplayName(memberData['role'] ?? 'salesAgent');

    return SingleChildScrollView(
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات العضو
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: CairoTextStyles.headlineSmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.work, color: Colors.white, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      role,
                      style: CairoTextStyles.bodyMedium.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    if (rating > 0) ...[
                      const Spacer(),
                      Icon(Icons.star, color: Colors.amber, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        rating.toStringAsFixed(1),
                        style: CairoTextStyles.bodyMedium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // إحصائيات الأداء
          Text(
            'إحصائيات الأداء',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildAnalyticsCard(
                  'إجمالي المبيعات',
                  '${totalSales.toStringAsFixed(0)} د.ك',
                  Icons.attach_money,
                  AppColors.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildAnalyticsCard(
                  'العقارات المباعة',
                  '$soldCount',
                  Icons.home_work,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildAnalyticsCard(
                  'العملاء',
                  '$clientsCount',
                  Icons.people,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildAnalyticsCard(
                  'متوسط التقييم',
                  rating > 0 ? rating.toStringAsFixed(1) : 'لا يوجد',
                  Icons.star,
                  Colors.amber,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // معلومات إضافية
          Text(
            'معلومات إضافية',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          if (joinDate != null)
            _buildInfoRow(
              'تاريخ الانضمام',
              _formatDate(joinDate.toDate()),
              Icons.calendar_today,
            ),
          _buildInfoRow(
            'معدل المبيعات الشهري',
            _calculateMonthlySales(totalSales, joinDate),
            Icons.trending_up,
          ),
          _buildInfoRow(
            'معدل العملاء الشهري',
            _calculateMonthlyClients(clientsCount, joinDate),
            Icons.group,
          ),
          const SizedBox(height: 24),

          // تقييم الأداء
          Text(
            'تقييم الأداء',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildPerformanceRating(totalSales, soldCount, clientsCount, rating),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: CairoTextStyles.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: CairoTextStyles.bodySmall.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: CairoTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  value,
                  style: CairoTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceRating(double totalSales, int soldCount, int clientsCount, double rating) {
    // حساب تقييم الأداء بناءً على المعايير المختلفة
    double performanceScore = 0;

    // تقييم المبيعات (40%)
    if (totalSales > 100000) {
      performanceScore += 40;
    } else if (totalSales > 50000) performanceScore += 30;
    else if (totalSales > 20000) performanceScore += 20;
    else if (totalSales > 0) performanceScore += 10;

    // تقييم العقارات المباعة (30%)
    if (soldCount > 20) {
      performanceScore += 30;
    } else if (soldCount > 10) performanceScore += 25;
    else if (soldCount > 5) performanceScore += 15;
    else if (soldCount > 0) performanceScore += 10;

    // تقييم العملاء (20%)
    if (clientsCount > 50) {
      performanceScore += 20;
    } else if (clientsCount > 25) performanceScore += 15;
    else if (clientsCount > 10) performanceScore += 10;
    else if (clientsCount > 0) performanceScore += 5;

    // تقييم العملاء (10%)
    if (rating >= 4.5) {
      performanceScore += 10;
    } else if (rating >= 4.0) performanceScore += 8;
    else if (rating >= 3.5) performanceScore += 6;
    else if (rating >= 3.0) performanceScore += 4;

    String performanceLevel;
    Color performanceColor;

    if (performanceScore >= 80) {
      performanceLevel = 'ممتاز';
      performanceColor = AppColors.success;
    } else if (performanceScore >= 60) {
      performanceLevel = 'جيد جداً';
      performanceColor = AppColors.primary;
    } else if (performanceScore >= 40) {
      performanceLevel = 'جيد';
      performanceColor = AppColors.warning;
    } else {
      performanceLevel = 'يحتاج تحسين';
      performanceColor = AppColors.error;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: performanceColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: performanceColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.assessment, color: performanceColor, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مستوى الأداء',
                      style: CairoTextStyles.bodyMedium.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      performanceLevel,
                      style: CairoTextStyles.headlineSmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: performanceColor,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${performanceScore.toInt()}%',
                style: CairoTextStyles.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: performanceColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: performanceScore / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation(performanceColor),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  String _calculateMonthlySales(double totalSales, Timestamp? joinDate) {
    if (joinDate == null || totalSales == 0) return '0 د.ك';

    final now = DateTime.now();
    final joined = joinDate.toDate();
    final monthsWorked = ((now.difference(joined).inDays / 30).ceil()).clamp(1, double.infinity);

    final monthlySales = totalSales / monthsWorked;
    return '${monthlySales.toStringAsFixed(0)} د.ك';
  }

  String _calculateMonthlyClients(int clientsCount, Timestamp? joinDate) {
    if (joinDate == null || clientsCount == 0) return '0';

    final now = DateTime.now();
    final joined = joinDate.toDate();
    final monthsWorked = ((now.difference(joined).inDays / 30).ceil()).clamp(1, double.infinity);

    final monthlyClients = clientsCount / monthsWorked;
    return monthlyClients.toStringAsFixed(1);
  }

  void _showTeamAnalytics() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildTeamAnalyticsSheet(),
    );
  }

  Widget _buildTeamAnalyticsSheet() {
    return DraggableScrollableSheet(
      initialChildSize: 0.9,
      maxChildSize: 0.95,
      minChildSize: 0.5,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.analytics, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'تحليلات الفريق',
                      style: CairoTextStyles.headlineSmall.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              const Divider(),
              // Content
              Expanded(
                child: StreamBuilder<QuerySnapshot>(
                  stream: _getTeamMembersStream(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const LoadingWidget();
                    }

                    if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                      return const Center(
                        child: Text('لا توجد بيانات للتحليل'),
                      );
                    }

                    final teamMembers = snapshot.data!.docs;
                    return _buildTeamAnalyticsContent(teamMembers, scrollController);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTeamAnalyticsContent(List<QueryDocumentSnapshot> teamMembers, ScrollController scrollController) {
    final analytics = _calculateTeamAnalytics(teamMembers);

    return SingleChildScrollView(
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات سريعة
          _buildTeamQuickStats(analytics),
          const SizedBox(height: 24),

          // توزيع المناصب
          Text(
            'توزيع المناصب',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildRoleDistribution(analytics['roleDistribution']),
          const SizedBox(height: 24),

          // تحليل السوق المتقدم
          Text(
            'تحليل السوق',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildMarketAnalysisSection(analytics),
          const SizedBox(height: 24),

          // تحليل الأداء
          Text(
            'تحليل أداء الفريق',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildTeamPerformanceAnalysis(analytics),
          const SizedBox(height: 24),

          // أفضل الأعضاء
          Text(
            'أفضل أعضاء الفريق',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildTopPerformers(teamMembers),
          const SizedBox(height: 24),

          // تحليل الحالات
          Text(
            'توزيع حالات الأعضاء',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildStatusDistribution(analytics['statusDistribution']),
        ],
      ),
    );
  }

  Map<String, dynamic> _calculateTeamAnalytics(List<QueryDocumentSnapshot> teamMembers) {
    Map<String, int> roleDistribution = {};
    Map<String, int> statusDistribution = {};

    double totalSales = 0;
    int totalSoldCount = 0;
    int totalClientsCount = 0;
    double totalRating = 0;
    int ratedMembers = 0;
    int activeMembers = 0;

    // تحليل بيانات العقارات الفعلية من Firebase
    for (var member in teamMembers) {
      final data = member.data() as Map<String, dynamic>;

      // توزيع المناصب
      final role = data['role'] ?? 'salesAgent';
      roleDistribution[role] = (roleDistribution[role] ?? 0) + 1;

      // توزيع الحالات
      final status = data['status'] ?? 'active';
      statusDistribution[status] = (statusDistribution[status] ?? 0) + 1;

      // الإحصائيات المبنية على بيانات العقارات المحفوظة
      totalSales += (data['totalSales'] ?? 0).toDouble();
      totalSoldCount += (data['soldRentedCount'] ?? 0) as int;
      totalClientsCount += (data['clientsCount'] ?? 0) as int;

      final rating = (data['averageRating'] ?? 0).toDouble();
      if (rating > 0) {
        totalRating += rating;
        ratedMembers++;
      }

      if (status == 'active') activeMembers++;
    }

    // إضافة تحليلات السوق المتقدمة
    final marketAnalytics = _calculateMarketInsights(teamMembers);

    return {
      'totalMembers': teamMembers.length,
      'activeMembers': activeMembers,
      'roleDistribution': roleDistribution,
      'statusDistribution': statusDistribution,
      'totalSales': totalSales,
      'totalSoldCount': totalSoldCount,
      'totalClientsCount': totalClientsCount,
      'averageRating': ratedMembers > 0 ? totalRating / ratedMembers : 0,
      'averageSalesPerMember': teamMembers.isNotEmpty ? totalSales / teamMembers.length : 0,
      'averageClientsPerMember': teamMembers.isNotEmpty ? totalClientsCount / teamMembers.length : 0,
      'marketTrends': marketAnalytics['trends'],
      'performanceGrowth': marketAnalytics['growth'],
      'marketShare': marketAnalytics['marketShare'],
    };
  }

  /// حساب رؤى السوق المتقدمة بناءً على بيانات العقارات المحفوظة
  Map<String, dynamic> _calculateMarketInsights(List<QueryDocumentSnapshot> teamMembers) {
    // حساب اتجاهات السوق
    double totalGrowth = 0;
    int growthMembers = 0;

    for (var member in teamMembers) {
      final data = member.data() as Map<String, dynamic>;
      final currentSales = (data['totalSales'] ?? 0).toDouble();
      final previousSales = (data['previousMonthSales'] ?? 0).toDouble();

      if (previousSales > 0) {
        final growth = ((currentSales - previousSales) / previousSales) * 100;
        totalGrowth += growth;
        growthMembers++;
      }
    }

    final averageGrowth = growthMembers > 0 ? totalGrowth / growthMembers : 0;

    return {
      'trends': averageGrowth > 5 ? 'صاعد' : averageGrowth < -5 ? 'هابط' : 'مستقر',
      'growth': averageGrowth,
      'marketShare': _calculateTeamMarketShare(teamMembers),
    };
  }

  /// حساب حصة الفريق في السوق
  double _calculateTeamMarketShare(List<QueryDocumentSnapshot> teamMembers) {
    // في التطبيق الحقيقي، هذا سيتم حسابه بناءً على إجمالي السوق
    // هنا نستخدم تقدير بناءً على أداء الفريق
    final totalTeamSales = teamMembers.fold<double>(0, (total, member) {
      final data = member.data() as Map<String, dynamic>;
      return total + (data['totalSales'] ?? 0).toDouble();
    });

    // تقدير حصة السوق (يمكن تحسينه بربطه ببيانات السوق الفعلية)
    return totalTeamSales > 0 ? (totalTeamSales / 1000000) * 100 : 0; // افتراض أن السوق الإجمالي مليون دينار
  }

  /// بناء قسم تحليل السوق المتقدم
  Widget _buildMarketAnalysisSection(Map<String, dynamic> analytics) {
    final marketTrends = analytics['marketTrends'] ?? 'مستقر';
    final performanceGrowth = (analytics['performanceGrowth'] ?? 0.0) as double;
    final marketShare = (analytics['marketShare'] ?? 0.0) as double;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary.withValues(alpha: 0.1), AppColors.primaryLight.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم مع أيقونة
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.trending_up,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'رؤى السوق المبنية على البيانات الفعلية',
                style: CairoTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // مؤشرات السوق
          Row(
            children: [
              Expanded(
                child: _buildMarketIndicator(
                  'اتجاه السوق',
                  marketTrends,
                  _getTrendIcon(marketTrends),
                  _getTrendColor(marketTrends),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildMarketIndicator(
                  'نمو الأداء',
                  '${performanceGrowth.toStringAsFixed(1)}%',
                  performanceGrowth >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                  performanceGrowth >= 0 ? AppColors.success : AppColors.error,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // حصة السوق
          _buildMarketIndicator(
            'حصة الفريق في السوق',
            '${marketShare.toStringAsFixed(2)}%',
            Icons.pie_chart,
            AppColors.secondary,
          ),
          const SizedBox(height: 16),

          // رسالة توضيحية
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.info, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'التحليلات مبنية على بيانات العقارات المحفوظة في Firebase وأداء أعضاء الفريق الفعلي',
                    style: CairoTextStyles.bodySmall.copyWith(
                      color: AppColors.info,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر السوق
  Widget _buildMarketIndicator(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: CairoTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: CairoTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الاتجاه
  IconData _getTrendIcon(String trend) {
    switch (trend) {
      case 'صاعد':
        return Icons.trending_up;
      case 'هابط':
        return Icons.trending_down;
      default:
        return Icons.trending_flat;
    }
  }

  /// الحصول على لون الاتجاه
  Color _getTrendColor(String trend) {
    switch (trend) {
      case 'صاعد':
        return AppColors.success;
      case 'هابط':
        return AppColors.error;
      default:
        return AppColors.warning;
    }
  }

  void _exportTeamData() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('جاري تصدير بيانات الفريق...')));

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      // جلب بيانات الفريق
      final teamSnapshot = await FirebaseFirestore.instance
          .collection('team_members')
          .where('companyId', isEqualTo: currentUser.uid)
          .get();

      if (teamSnapshot.docs.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('لا توجد بيانات للتصدير')));
        }
        return;
      }

      // إنشاء ملف Excel
      final xlsio.Workbook workbook = xlsio.Workbook();

      // ورقة بيانات الفريق
      final xlsio.Worksheet teamSheet = workbook.worksheets[0];
      teamSheet.name = 'بيانات الفريق';
      _createTeamDataSheet(teamSheet, teamSnapshot.docs);

      // ورقة تحليلات الفريق
      final xlsio.Worksheet analyticsSheet = workbook.worksheets.add();
      analyticsSheet.name = 'تحليلات الفريق';
      _createTeamAnalyticsSheet(analyticsSheet, teamSnapshot.docs);

      // ورقة أداء الأعضاء
      final xlsio.Worksheet performanceSheet = workbook.worksheets.add();
      performanceSheet.name = 'أداء الأعضاء';
      _createPerformanceSheet(performanceSheet, teamSnapshot.docs);

      // حفظ الملف
      final List<int> bytes = workbook.saveAsStream();
      workbook.dispose();

      // مشاركة الملف
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/team_data_${DateTime.now().millisecondsSinceEpoch}.xlsx');
      await file.writeAsBytes(bytes);

      await Share.shareXFiles([XFile(file.path)],
        text: 'بيانات الفريق - ${DateFormat('yyyy-MM-dd').format(DateTime.now())}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تصدير بيانات الفريق بنجاح!')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ في التصدير: $e')));
      }
    }
  }

  Widget _buildTeamQuickStats(Map<String, dynamic> analytics) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الأعضاء',
                  '${analytics['totalMembers']}',
                  Icons.people,
                  Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'الأعضاء النشطين',
                  '${analytics['activeMembers']}',
                  Icons.person,
                  Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المبيعات',
                  '${analytics['totalSales'].toStringAsFixed(0)} د.ك',
                  Icons.attach_money,
                  Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'متوسط التقييم',
                  analytics['averageRating'] > 0
                      ? analytics['averageRating'].toStringAsFixed(1)
                      : 'لا يوجد',
                  Icons.star,
                  Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRoleDistribution(Map<String, int> roleDistribution) {
    if (roleDistribution.isEmpty) {
      return const Text('لا توجد بيانات');
    }

    return Column(
      children: roleDistribution.entries.map((entry) {
        final total = roleDistribution.values.reduce((a, b) => a + b);
        final percentage = (entry.value / total * 100).round();
        final roleDisplayName = _getRoleDisplayName(entry.key);

        Color color;
        switch (entry.key) {
          case 'manager':
            color = AppColors.primary;
            break;
          case 'salesAgent':
            color = AppColors.primaryLight;
            break;
          case 'marketingSpecialist':
            color = Colors.orange;
            break;
          case 'propertyEvaluator':
            color = AppColors.secondary;
            break;
          default:
            color = Colors.grey;
        }

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Row(
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          roleDisplayName,
                          style: CairoTextStyles.bodyMedium,
                        ),
                        Text(
                          '${entry.value} ($percentage%)',
                          style: CairoTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: entry.value / total,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation(color),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTeamPerformanceAnalysis(Map<String, dynamic> analytics) {
    final totalSales = analytics['totalSales'] as double;
    final totalSoldCount = analytics['totalSoldCount'] as int;
    final totalClientsCount = analytics['totalClientsCount'] as int;
    final averageSalesPerMember = analytics['averageSalesPerMember'] as double;
    final averageClientsPerMember = analytics['averageClientsPerMember'] as double;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildPerformanceItem(
                  'إجمالي المبيعات',
                  '${totalSales.toStringAsFixed(0)} د.ك',
                  AppColors.primary,
                  Icons.trending_up,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildPerformanceItem(
                  'العقارات المباعة',
                  '$totalSoldCount',
                  Colors.orange,
                  Icons.home_work,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildPerformanceItem(
                  'إجمالي العملاء',
                  '$totalClientsCount',
                  AppColors.primaryLight,
                  Icons.people,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildPerformanceItem(
                  'متوسط المبيعات/عضو',
                  '${averageSalesPerMember.toStringAsFixed(0)} د.ك',
                  AppColors.success,
                  Icons.person,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildPerformanceItem(
            'متوسط العملاء/عضو',
            averageClientsPerMember.toStringAsFixed(1),
            AppColors.secondary,
            Icons.group,
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceItem(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: CairoTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: CairoTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTopPerformers(List<QueryDocumentSnapshot> teamMembers) {
    // ترتيب الأعضاء حسب المبيعات
    final sortedMembers = teamMembers.where((member) {
      final data = member.data() as Map<String, dynamic>;
      return (data['totalSales'] ?? 0).toDouble() > 0;
    }).toList();

    sortedMembers.sort((a, b) {
      final aData = a.data() as Map<String, dynamic>;
      final bData = b.data() as Map<String, dynamic>;
      final aSales = (aData['totalSales'] ?? 0).toDouble();
      final bSales = (bData['totalSales'] ?? 0).toDouble();
      return bSales.compareTo(aSales);
    });

    final topPerformers = sortedMembers.take(3).toList();

    if (topPerformers.isEmpty) {
      return const Text('لا توجد بيانات أداء');
    }

    return Column(
      children: topPerformers.asMap().entries.map((entry) {
        final index = entry.key;
        final member = entry.value;
        final data = member.data() as Map<String, dynamic>;
        final name = data['fullName'] ?? 'غير محدد';
        final totalSales = (data['totalSales'] ?? 0).toDouble();
        final soldCount = (data['soldRentedCount'] ?? 0) as int;

        Color rankColor;
        IconData rankIcon;
        switch (index) {
          case 0:
            rankColor = Colors.amber;
            rankIcon = Icons.emoji_events;
            break;
          case 1:
            rankColor = Colors.grey;
            rankIcon = Icons.military_tech;
            break;
          case 2:
            rankColor = Colors.brown;
            rankIcon = Icons.workspace_premium;
            break;
          default:
            rankColor = Colors.grey;
            rankIcon = Icons.star;
        }

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: rankColor.withValues(alpha: 0.1),
              child: Icon(rankIcon, color: rankColor),
            ),
            title: Text(name),
            subtitle: Text('$soldCount عقار مباع'),
            trailing: Text(
              '${totalSales.toStringAsFixed(0)} د.ك',
              style: CairoTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildStatusDistribution(Map<String, int> statusDistribution) {
    if (statusDistribution.isEmpty) {
      return const Text('لا توجد بيانات');
    }

    return Row(
      children: statusDistribution.entries.map((entry) {
        Color color;
        IconData icon;
        String displayName;

        switch (entry.key) {
          case 'active':
            color = AppColors.success;
            icon = Icons.check_circle;
            displayName = 'نشط';
            break;
          case 'inactive':
            color = AppColors.error;
            icon = Icons.cancel;
            displayName = 'غير نشط';
            break;
          case 'vacation':
            color = AppColors.warning;
            icon = Icons.beach_access;
            displayName = 'إجازة';
            break;
          case 'suspended':
            color = Colors.grey;
            icon = Icons.pause_circle;
            displayName = 'معلق';
            break;
          default:
            color = Colors.grey;
            icon = Icons.help;
            displayName = entry.key;
        }

        return Expanded(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Column(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(height: 8),
                Text(
                  '${entry.value}',
                  style: CairoTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  displayName,
                  style: CairoTextStyles.bodySmall.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color textColor) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: textColor, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: CairoTextStyles.headlineSmall.copyWith(
              color: textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: CairoTextStyles.bodySmall.copyWith(
              color: textColor.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _createTeamDataSheet(xlsio.Worksheet sheet, List<QueryDocumentSnapshot> docs) {
    // العناوين
    sheet.getRangeByName('A1').setText('الاسم الكامل');
    sheet.getRangeByName('B1').setText('المنصب');
    sheet.getRangeByName('C1').setText('البريد الإلكتروني');
    sheet.getRangeByName('D1').setText('رقم الهاتف');
    sheet.getRangeByName('E1').setText('الحالة');
    sheet.getRangeByName('F1').setText('إجمالي المبيعات');
    sheet.getRangeByName('G1').setText('العقارات المباعة');
    sheet.getRangeByName('H1').setText('عدد العملاء');
    sheet.getRangeByName('I1').setText('التقييم');
    sheet.getRangeByName('J1').setText('تاريخ الانضمام');

    // تنسيق العناوين
    final xlsio.Range headerRange = sheet.getRangeByName('A1:J1');
    headerRange.cellStyle.bold = true;
    headerRange.cellStyle.backColor = '#2E7D32'; // AppColors.primary
    headerRange.cellStyle.fontColor = '#FFFFFF';

    // البيانات
    int row = 2;
    for (var doc in docs) {
      final data = doc.data() as Map<String, dynamic>;

      sheet.getRangeByName('A$row').setText(data['fullName'] ?? '');
      sheet.getRangeByName('B$row').setText(_getRoleDisplayName(data['role'] ?? 'salesAgent'));
      sheet.getRangeByName('C$row').setText(data['email'] ?? '');
      sheet.getRangeByName('D$row').setText(data['phoneNumber'] ?? '');
      sheet.getRangeByName('E$row').setText(_getStatusDisplayName(data['status'] ?? 'active'));
      sheet.getRangeByName('F$row').setNumber((data['totalSales'] ?? 0).toDouble());
      sheet.getRangeByName('G$row').setNumber((data['soldRentedCount'] ?? 0).toDouble());
      sheet.getRangeByName('H$row').setNumber((data['clientsCount'] ?? 0).toDouble());
      sheet.getRangeByName('I$row').setNumber((data['averageRating'] ?? 0).toDouble());

      final joinDate = data['joinDate'] as Timestamp?;
      if (joinDate != null) {
        sheet.getRangeByName('J$row').setText(
          DateFormat('yyyy-MM-dd').format(joinDate.toDate()));
      }

      row++;
    }

    // ضبط عرض الأعمدة
    for (int i = 1; i <= 10; i++) {
      sheet.autoFitColumn(i);
    }
  }

  void _createTeamAnalyticsSheet(xlsio.Worksheet sheet, List<QueryDocumentSnapshot> docs) {
    final analytics = _calculateTeamAnalytics(docs);

    // العناوين الرئيسية
    sheet.getRangeByName('A1').setText('تحليلات الفريق');
    sheet.getRangeByName('A1').cellStyle.bold = true;
    sheet.getRangeByName('A1').cellStyle.fontSize = 16;

    // الإحصائيات العامة
    sheet.getRangeByName('A3').setText('إجمالي الأعضاء');
    sheet.getRangeByName('B3').setText('${analytics['totalMembers']}');

    sheet.getRangeByName('A4').setText('الأعضاء النشطين');
    sheet.getRangeByName('B4').setText('${analytics['activeMembers']}');

    sheet.getRangeByName('A5').setText('إجمالي المبيعات');
    sheet.getRangeByName('B5').setText('${analytics['totalSales'].toStringAsFixed(0)} د.ك');

    sheet.getRangeByName('A6').setText('متوسط التقييم');
    sheet.getRangeByName('B6').setText(analytics['averageRating'] > 0
        ? analytics['averageRating'].toStringAsFixed(1)
        : 'لا يوجد');

    // توزيع المناصب
    sheet.getRangeByName('A8').setText('توزيع المناصب');
    sheet.getRangeByName('A8').cellStyle.bold = true;

    sheet.getRangeByName('A9').setText('المنصب');
    sheet.getRangeByName('B9').setText('العدد');

    int row = 10;
    final roleDistribution = analytics['roleDistribution'] as Map<String, int>;
    for (var entry in roleDistribution.entries) {
      sheet.getRangeByName('A$row').setText(_getRoleDisplayName(entry.key));
      sheet.getRangeByName('B$row').setNumber(entry.value.toDouble());
      row++;
    }

    // توزيع الحالات
    row += 2;
    sheet.getRangeByName('A$row').setText('توزيع الحالات');
    sheet.getRangeByName('A$row').cellStyle.bold = true;

    row++;
    sheet.getRangeByName('A$row').setText('الحالة');
    sheet.getRangeByName('B$row').setText('العدد');

    row++;
    final statusDistribution = analytics['statusDistribution'] as Map<String, int>;
    for (var entry in statusDistribution.entries) {
      sheet.getRangeByName('A$row').setText(_getStatusDisplayName(entry.key));
      sheet.getRangeByName('B$row').setNumber(entry.value.toDouble());
      row++;
    }

    // ضبط عرض الأعمدة
    sheet.autoFitColumn(1);
    sheet.autoFitColumn(2);
  }

  void _createPerformanceSheet(xlsio.Worksheet sheet, List<QueryDocumentSnapshot> docs) {
    // العناوين
    sheet.getRangeByName('A1').setText('الاسم');
    sheet.getRangeByName('B1').setText('المنصب');
    sheet.getRangeByName('C1').setText('إجمالي المبيعات');
    sheet.getRangeByName('D1').setText('العقارات المباعة');
    sheet.getRangeByName('E1').setText('عدد العملاء');
    sheet.getRangeByName('F1').setText('التقييم');
    sheet.getRangeByName('G1').setText('مستوى الأداء');

    // تنسيق العناوين
    final xlsio.Range headerRange = sheet.getRangeByName('A1:G1');
    headerRange.cellStyle.bold = true;
    headerRange.cellStyle.backColor = '#2E7D32'; // AppColors.primary
    headerRange.cellStyle.fontColor = '#FFFFFF';

    // ترتيب الأعضاء حسب الأداء
    final sortedDocs = docs.toList();
    sortedDocs.sort((a, b) {
      final aData = a.data() as Map<String, dynamic>;
      final bData = b.data() as Map<String, dynamic>;
      final aSales = (aData['totalSales'] ?? 0).toDouble();
      final bSales = (bData['totalSales'] ?? 0).toDouble();
      return bSales.compareTo(aSales);
    });

    // البيانات
    int row = 2;
    for (var doc in sortedDocs) {
      final data = doc.data() as Map<String, dynamic>;
      final totalSales = (data['totalSales'] ?? 0).toDouble();
      final soldCount = (data['soldRentedCount'] ?? 0) as int;
      final clientsCount = (data['clientsCount'] ?? 0) as int;
      final rating = (data['averageRating'] ?? 0).toDouble();

      // حساب مستوى الأداء
      double performanceScore = 0;
      if (totalSales > 100000) {
        performanceScore += 40;
      } else if (totalSales > 50000) performanceScore += 30;
      else if (totalSales > 20000) performanceScore += 20;
      else if (totalSales > 0) performanceScore += 10;

      if (soldCount > 20) {
        performanceScore += 30;
      } else if (soldCount > 10) performanceScore += 25;
      else if (soldCount > 5) performanceScore += 15;
      else if (soldCount > 0) performanceScore += 10;

      if (clientsCount > 50) {
        performanceScore += 20;
      } else if (clientsCount > 25) performanceScore += 15;
      else if (clientsCount > 10) performanceScore += 10;
      else if (clientsCount > 0) performanceScore += 5;

      if (rating >= 4.5) {
        performanceScore += 10;
      } else if (rating >= 4.0) performanceScore += 8;
      else if (rating >= 3.5) performanceScore += 6;
      else if (rating >= 3.0) performanceScore += 4;

      String performanceLevel;
      if (performanceScore >= 80) {
        performanceLevel = 'ممتاز';
      } else if (performanceScore >= 60) {
        performanceLevel = 'جيد جداً';
      } else if (performanceScore >= 40) {
        performanceLevel = 'جيد';
      } else {
        performanceLevel = 'يحتاج تحسين';
      }

      sheet.getRangeByName('A$row').setText(data['fullName'] ?? '');
      sheet.getRangeByName('B$row').setText(_getRoleDisplayName(data['role'] ?? 'salesAgent'));
      sheet.getRangeByName('C$row').setNumber(totalSales);
      sheet.getRangeByName('D$row').setNumber(soldCount.toDouble());
      sheet.getRangeByName('E$row').setNumber(clientsCount.toDouble());
      sheet.getRangeByName('F$row').setNumber(rating);
      sheet.getRangeByName('G$row').setText(performanceLevel);

      row++;
    }

    // ضبط عرض الأعمدة
    for (int i = 1; i <= 7; i++) {
      sheet.autoFitColumn(i);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}