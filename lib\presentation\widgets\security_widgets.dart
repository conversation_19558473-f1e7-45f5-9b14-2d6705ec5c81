import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../infrastructure/services/app_manager.dart';
import '../../infrastructure/services/multi_factor_auth_service.dart';


/// مكون المصادقة البيومترية
class BiometricAuthWidget extends StatefulWidget {
  /// العنوان
  final String title;

  /// الرسالة
  final String message;

  /// دالة النجاح
  final VoidCallback onSuccess;

  /// دالة الفشل
  final VoidCallback? onFailure;

  /// دالة الإلغاء
  final VoidCallback? onCancel;

  const BiometricAuthWidget({
    super.key,
    required this.title,
    required this.message,
    required this.onSuccess,
    this.onFailure,
    this.onCancel,
  });

  @override
  _BiometricAuthWidgetState createState() => _BiometricAuthWidgetState();
}

class _BiometricAuthWidgetState extends State<BiometricAuthWidget> {
  bool _isAuthenticating = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    // بدء المصادقة البيومترية تلقائياً
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _authenticate();
    });
  }

  /// المصادقة باستخدام البيومترية
  Future<void> _authenticate() async {
    if (_isAuthenticating) {
      return;
    }

    setState(() {
      _isAuthenticating = true;
      _errorMessage = '';
    });

    try {
      final authenticated =
          await context.multiFactorAuthService.authenticateWithBiometrics(
        widget.title,
        widget.message);

      if (authenticated) {
        widget.onSuccess();
      } else {
        setState(() {
          _errorMessage = 'فشلت المصادقة البيومترية';
        });

        if (widget.onFailure != null) {
          widget.onFailure!();
        }
      }
    } on PlatformException catch (e) {
      setState(() {
        _errorMessage = 'خطأ في المصادقة البيومترية: ${e.message}';
      });

      if (widget.onFailure != null) {
        widget.onFailure!();
      }
    } finally {
      setState(() {
        _isAuthenticating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          widget.title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Text(
          widget.message,
          textAlign: TextAlign.center),
        const SizedBox(height: 24),
        if (_isAuthenticating)
          const CircularProgressIndicator()
        else
          Column(
            children: [
              if (_errorMessage.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    _errorMessage,
                    style: const TextStyle(
                      color: Colors.red),
                    textAlign: TextAlign.center)),
              ElevatedButton(
                onPressed: _authenticate,
                child: const Text('إعادة المحاولة')),
              const SizedBox(height: 8),
              TextButton(
                onPressed: widget.onCancel,
                child: const Text('إلغاء')),
            ]),
      ]);
  }
}

/// مكون المصادقة الثنائية
class TwoFactorAuthWidget extends StatefulWidget {
  /// العنوان
  final String title;

  /// الرسالة
  final String message;

  /// دالة التحقق
  final Future<bool> Function(String code) onVerify;

  /// دالة النجاح
  final VoidCallback onSuccess;

  /// دالة الفشل
  final VoidCallback? onFailure;

  /// دالة الإلغاء
  final VoidCallback? onCancel;

  /// دالة إعادة إرسال الرمز
  final VoidCallback? onResendCode;

  const TwoFactorAuthWidget({
    super.key,
    required this.title,
    required this.message,
    required this.onVerify,
    required this.onSuccess,
    this.onFailure,
    this.onCancel,
    this.onResendCode,
  });

  @override
  _TwoFactorAuthWidgetState createState() => _TwoFactorAuthWidgetState();
}

class _TwoFactorAuthWidgetState extends State<TwoFactorAuthWidget> {
  final TextEditingController _codeController = TextEditingController();
  bool _isVerifying = false;
  String _errorMessage = '';

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  /// التحقق من الرمز
  Future<void> _verifyCode() async {
    final code = _codeController.text.trim();

    if (code.isEmpty) {
      setState(() {
        _errorMessage = 'يرجى إدخال رمز التحقق';
      });
      return;
    }

    setState(() {
      _isVerifying = true;
      _errorMessage = '';
    });

    try {
      final verified = await widget.onVerify(code);

      if (verified) {
        widget.onSuccess();
      } else {
        setState(() {
          _errorMessage = 'رمز التحقق غير صحيح';
        });

        if (widget.onFailure != null) {
          widget.onFailure!();
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في التحقق من الرمز: $e';
      });

      if (widget.onFailure != null) {
        widget.onFailure!();
      }
    } finally {
      setState(() {
        _isVerifying = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          widget.title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Text(
          widget.message,
          textAlign: TextAlign.center),
        const SizedBox(height: 24),
        TextField(
          controller: _codeController,
          decoration: const InputDecoration(
            labelText: 'رمز التحقق',
            border: OutlineInputBorder()),
          keyboardType: TextInputType.number,
          maxLength: 6,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 20,
            letterSpacing: 8)),
        const SizedBox(height: 16),
        if (_errorMessage.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Text(
              _errorMessage,
              style: const TextStyle(
                color: Colors.red),
              textAlign: TextAlign.center)),
        if (_isVerifying)
          const CircularProgressIndicator()
        else
          Column(
            children: [
              ElevatedButton(
                onPressed: _verifyCode,
                child: const Text('تحقق')),
              const SizedBox(height: 8),
              if (widget.onResendCode != null)
                TextButton(
                  onPressed: widget.onResendCode,
                  child: const Text('إعادة إرسال الرمز')),
              if (widget.onCancel != null)
                TextButton(
                  onPressed: widget.onCancel,
                  child: const Text('إلغاء')),
            ]),
      ]);
  }
}

/// مكون إعداد المصادقة الثنائية
class TwoFactorSetupWidget extends StatefulWidget {
  /// العنوان
  final String title;

  /// البريد الإلكتروني
  final String email;

  /// اسم التطبيق
  final String appName;

  /// دالة النجاح
  final VoidCallback onSuccess;

  /// دالة الإلغاء
  final VoidCallback? onCancel;

  const TwoFactorSetupWidget({
    super.key,
    required this.title,
    required this.email,
    required this.appName,
    required this.onSuccess,
    this.onCancel,
  });

  @override
  _TwoFactorSetupWidgetState createState() => _TwoFactorSetupWidgetState();
}

class _TwoFactorSetupWidgetState extends State<TwoFactorSetupWidget> {
  final TextEditingController _codeController = TextEditingController();
  bool _isLoading = true;
  bool _isVerifying = false;
  String _errorMessage = '';
  String _secret = '';
  String _qrCodeUrl = '';

  @override
  void initState() {
    super.initState();

    // تهيئة المصادقة الثنائية
    _initTwoFactorAuth();
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  /// تهيئة المصادقة الثنائية
  Future<void> _initTwoFactorAuth() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تفعيل المصادقة الثنائية
      _secret = await context.multiFactorAuthService.enableTwoFactorAuth();

      // إنشاء رابط QR
      _qrCodeUrl = context.multiFactorAuthService.getTOTPQRCodeUrl(
        _secret,
        widget.email,
        widget.appName);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'خطأ في تهيئة المصادقة الثنائية: $e';
      });
    }
  }

  /// التحقق من الرمز
  Future<void> _verifyCode() async {
    final code = _codeController.text.trim();

    if (code.isEmpty) {
      setState(() {
        _errorMessage = 'يرجى إدخال رمز التحقق';
      });
      return;
    }

    setState(() {
      _isVerifying = true;
      _errorMessage = '';
    });

    try {
      // التحقق من الرمز
      final verified =
          context.multiFactorAuthService.verifyTOTPCode(_secret, code);

      if (verified) {
        widget.onSuccess();
      } else {
        setState(() {
          _errorMessage = 'رمز التحقق غير صحيح';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في التحقق من الرمز: $e';
      });
    } finally {
      setState(() {
        _isVerifying = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          widget.title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        const Text(
          'قم بمسح رمز QR ضوئياً باستخدام تطبيق المصادقة (مثل Google Authenticator أو Authy) ثم أدخل الرمز المعروض في التطبيق.',
          textAlign: TextAlign.center),
        const SizedBox(height: 24),
        if (_isLoading)
          const CircularProgressIndicator()
        else if (_errorMessage.isNotEmpty)
          Text(
            _errorMessage,
            style: const TextStyle(
              color: Colors.red),
            textAlign: TextAlign.center)
        else
          Column(
            children: [
              QrImageView(
                data: _qrCodeUrl,
                version: QrVersions.auto,
                size: 200.0),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('المفتاح السري: '),
                  Flexible(
                    child: Text(
                      _formatSecret(_secret),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold))),
                  IconButton(
                    icon: const Icon(Icons.copy),
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: _secret));
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم نسخ المفتاح السري')));
                    }),
                ]),
              const SizedBox(height: 24),
              TextField(
                controller: _codeController,
                decoration: const InputDecoration(
                  labelText: 'رمز التحقق',
                  border: OutlineInputBorder()),
                keyboardType: TextInputType.number,
                maxLength: 6,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 20,
                  letterSpacing: 8)),
              const SizedBox(height: 16),
              if (_errorMessage.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    _errorMessage,
                    style: const TextStyle(
                      color: Colors.red),
                    textAlign: TextAlign.center)),
              if (_isVerifying)
                const CircularProgressIndicator()
              else
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: _verifyCode,
                      child: const Text('تحقق')),
                    const SizedBox(width: 16),
                    if (widget.onCancel != null)
                      TextButton(
                        onPressed: widget.onCancel,
                        child: const Text('إلغاء')),
                  ]),
            ]),
      ]);
  }

  /// تنسيق المفتاح السري
  String _formatSecret(String secret) {
    final buffer = StringBuffer();
    for (int i = 0; i < secret.length; i++) {
      buffer.write(secret[i]);
      if ((i + 1) % 4 == 0 && i != secret.length - 1) {
        buffer.write(' ');
      }
    }
    return buffer.toString();
  }
}

/// مكون قوة كلمة المرور
class PasswordStrengthWidget extends StatelessWidget {
  /// كلمة المرور
  final String password;

  /// ما إذا كان يجب عرض النصائح
  final bool showTips;

  const PasswordStrengthWidget({
    super.key,
    required this.password,
    this.showTips = true,
  });

  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأمان
    final securityService = context.securityService;

    // الحصول على قوة كلمة المرور
    final strength = securityService.getPasswordStrength(password);

    // الحصول على وصف قوة كلمة المرور
    final description =
        securityService.getPasswordStrengthDescription(password);

    // الحصول على لون قوة كلمة المرور
    final color =
        _getColorFromHex(securityService.getPasswordStrengthColor(password));

    // الحصول على نصائح لتقوية كلمة المرور
    final tips = securityService.getPasswordStrengthTips(password);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'قوة كلمة المرور:',
          style: TextStyle(
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: strength,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color)),
        const SizedBox(height: 4),
        Text(
          description,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold)),
        if (showTips && tips.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              const Text(
                'نصائح لتقوية كلمة المرور:',
                style: TextStyle(
                  fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              ...tips.map((tip) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• '),
                        Expanded(child: Text(tip)),
                      ]))),
            ]),
      ]);
  }

  /// تحويل اللون من سلسلة HEX إلى كائن Color
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }
}

/// مكون عرض أنواع المصادقة البيومترية المتاحة
class AvailableBiometricsWidget extends StatefulWidget {
  /// دالة اختيار نوع المصادقة البيومترية
  final void Function(BiometricType type)? onSelect;

  const AvailableBiometricsWidget({
    super.key,
    this.onSelect,
  });

  @override
  _AvailableBiometricsWidgetState createState() =>
      _AvailableBiometricsWidgetState();
}

class _AvailableBiometricsWidgetState extends State<AvailableBiometricsWidget> {
  bool _isLoading = true;
  List<BiometricType> _availableBiometrics = [];
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    // الحصول على أنواع المصادقة البيومترية المتاحة
    _getAvailableBiometrics();
  }

  /// الحصول على أنواع المصادقة البيومترية المتاحة
  Future<void> _getAvailableBiometrics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // التحقق مما إذا كانت المصادقة البيومترية متاحة
      final isAvailable =
          await context.multiFactorAuthService.isBiometricAuthAvailable();

      if (isAvailable) {
        // الحصول على أنواع المصادقة البيومترية المتاحة
        _availableBiometrics =
            await context.multiFactorAuthService.getAvailableBiometrics();
      } else {
        _errorMessage = 'المصادقة البيومترية غير متاحة على هذا الجهاز';
      }
    } catch (e) {
      _errorMessage = 'خطأ في الحصول على أنواع المصادقة البيومترية: $e';
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator());
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Text(
          _errorMessage,
          style: const TextStyle(
            color: Colors.red),
          textAlign: TextAlign.center));
    }

    if (_availableBiometrics.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد أنواع مصادقة بيومترية متاحة على هذا الجهاز',
          textAlign: TextAlign.center));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أنواع المصادقة البيومترية المتاحة:',
          style: TextStyle(
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        ...List.generate(_availableBiometrics.length, (index) {
          final type = _availableBiometrics[index];
          final description =
              context.multiFactorAuthService.getBiometricTypeDescription(type);

          return ListTile(
            leading: Image.asset(
              context.multiFactorAuthService.getBiometricTypeIcon(type),
              width: 32,
              height: 32),
            title: Text(description),
            onTap:
                widget.onSelect != null ? () => widget.onSelect!(type) : null);
        }),
      ]);
  }
}
