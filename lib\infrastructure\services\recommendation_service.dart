import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/entities/recommendation.dart';
import '../../domain/entities/estate.dart';
import '../../domain/entities/user.dart' as app_user;
import '../../domain/repositories/analytics_repository.dart';
import '../../domain/repositories/estate_repository.dart';
import '../../domain/repositories/user_repository.dart';

/// خدمة التوصيات الذكية
class RecommendationService {
  final AnalyticsRepository _analyticsRepository;
  final EstateRepository _estateRepository;
  final UserRepository _userRepository;
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;

  /// إنشاء خدمة التوصيات الذكية
  RecommendationService(
    this._analyticsRepository,
    this._estateRepository,
    this._userRepository, {
    FirebaseAuth? auth,
    FirebaseFirestore? firestore,
  })  : _auth = auth ?? FirebaseAuth.instance,
        _firestore = firestore ?? FirebaseFirestore.instance;

  /// الحصول على توصيات للمستخدم
  Future<List<Recommendation>> getUserRecommendations({
    required String userId,
    RecommendationType? type,
    int limit = 10,
  }) {
    return _analyticsRepository.getUserRecommendations(
      userId: userId,
      type: type,
      limit: limit);
  }

  /// تحديث حالة التوصية
  Future<void> updateRecommendationStatus({
    required String recommendationId,
    bool? isViewed,
    bool? isClicked,
    bool? isDismissed,
  }) {
    return _analyticsRepository.updateRecommendationStatus(
      recommendationId: recommendationId,
      isViewed: isViewed,
      isClicked: isClicked,
      isDismissed: isDismissed);
  }

  /// الحصول على توصيات العقارات للمستخدم
  Future<List<Recommendation>> getUserEstateRecommendations({
    required String userId,
    int limit = 10,
  }) {
    return _analyticsRepository.getUserRecommendations(
      userId: userId,
      type: RecommendationType.estate,
      limit: limit);
  }

  /// الحصول على توصيات المناطق للمستخدم
  Future<List<Recommendation>> getUserAreaRecommendations({
    required String userId,
    int limit = 10,
  }) {
    return _analyticsRepository.getUserRecommendations(
      userId: userId,
      type: RecommendationType.area,
      limit: limit);
  }

  /// الحصول على توصيات الوكلاء للمستخدم
  Future<List<Recommendation>> getUserAgentRecommendations({
    required String userId,
    int limit = 10,
  }) {
    return _analyticsRepository.getUserRecommendations(
      userId: userId,
      type: RecommendationType.agent,
      limit: limit);
  }

  /// الحصول على توصيات الاستثمار للمستخدم
  Future<List<Recommendation>> getUserInvestmentRecommendations({
    required String userId,
    int limit = 10,
  }) {
    return _analyticsRepository.getUserRecommendations(
      userId: userId,
      type: RecommendationType.investment,
      limit: limit);
  }

  /// الحصول على توصيات المقالات للمستخدم
  Future<List<Recommendation>> getUserArticleRecommendations({
    required String userId,
    int limit = 10,
  }) {
    return _analyticsRepository.getUserRecommendations(
      userId: userId,
      type: RecommendationType.article,
      limit: limit);
  }

  /// الحصول على توصيات المنتدى للمستخدم
  Future<List<Recommendation>> getUserForumRecommendations({
    required String userId,
    int limit = 10,
  }) {
    return _analyticsRepository.getUserRecommendations(
      userId: userId,
      type: RecommendationType.forum,
      limit: limit);
  }

  /// الحصول على العقارات الموصى بها للمستخدم
  Future<List<Estate>> getRecommendedEstates({
    required String userId,
    int limit = 10,
  }) async {
    final recommendations = await getUserEstateRecommendations(
      userId: userId,
      limit: limit);

    final estateIds = recommendations.map((r) => r.itemId).toList();
    final estates = <Estate>[];

    for (final estateId in estateIds) {
      final estateBase = await _estateRepository.getEstateById(estateId);
      if (estateBase != null) {
        // Skip adding the estate for now since we can't convert EstateBase to Estate
        // estates.add(estateBase as Estate);
      }
    }

    return estates;
  }

  /// الحصول على الوكلاء الموصى بهم للمستخدم
  Future<List<app_user.User>> getRecommendedAgents({
    required String userId,
    int limit = 10,
  }) async {
    final recommendations = await getUserAgentRecommendations(
      userId: userId,
      limit: limit);

    final agentIds = recommendations.map((r) => r.itemId).toList();
    final agents = <app_user.User>[];

    for (final agentId in agentIds) {
      // Skip getting the agent for now since getUserById is not implemented
      // final agent = await _userRepository.getUserById(agentId);
      // if (agent != null) {
      //   agents.add(agent);
      // }
    }

    return agents;
  }

  /// تحديث تفضيلات المستخدم
  Future<void> updateUserPreferences({
    required String userId,
    List<String>? preferredAreas,
    List<String>? preferredPropertyTypes,
    double? minPrice,
    double? maxPrice,
    int? minRooms,
    int? minBathrooms,
    double? minSize,
    double? maxSize,
    bool? isFurnished,
    List<String>? preferredFeatures,
  }) async {
    // تحديث تفضيلات المستخدم في قاعدة البيانات
    // هذا سيؤثر على التوصيات المستقبلية

    final preferences = <String, dynamic>{
      'updatedAt': FieldValue.serverTimestamp(),
    };

    if (preferredAreas != null) {
      preferences['preferredAreas'] = preferredAreas;
    }

    if (preferredPropertyTypes != null) {
      preferences['preferredPropertyTypes'] = preferredPropertyTypes;
    }

    if (minPrice != null) {
      preferences['minPrice'] = minPrice;
    }

    if (maxPrice != null) {
      preferences['maxPrice'] = maxPrice;
    }

    if (minRooms != null) {
      preferences['minRooms'] = minRooms;
    }

    if (minBathrooms != null) {
      preferences['minBathrooms'] = minBathrooms;
    }

    if (minSize != null) {
      preferences['minSize'] = minSize;
    }

    if (maxSize != null) {
      preferences['maxSize'] = maxSize;
    }

    if (isFurnished != null) {
      preferences['isFurnished'] = isFurnished;
    }

    if (preferredFeatures != null) {
      preferences['preferredFeatures'] = preferredFeatures;
    }

    await FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('preferences')
        .doc('estate')
        .set(preferences, SetOptions(merge: true));
  }

  /// تسجيل تفاعل المستخدم مع عقار
  Future<void> recordEstateInteraction({
    required String userId,
    required String estateId,
    required String interactionType,
    Map<String, dynamic>? metadata,
  }) async {
    await FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('interactions')
        .add({
      'userId': userId,
      'itemId': estateId,
      'itemType': 'estate',
      'interactionType': interactionType,
      'timestamp': FieldValue.serverTimestamp(),
      'metadata': metadata,
    });
  }

  /// تسجيل تفاعل المستخدم مع وكيل
  Future<void> recordAgentInteraction({
    required String userId,
    required String agentId,
    required String interactionType,
    Map<String, dynamic>? metadata,
  }) async {
    await FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('interactions')
        .add({
      'userId': userId,
      'itemId': agentId,
      'itemType': 'agent',
      'interactionType': interactionType,
      'timestamp': FieldValue.serverTimestamp(),
      'metadata': metadata,
    });
  }

  /// تسجيل بحث المستخدم
  Future<void> recordUserSearch({
    required String userId,
    required String query,
    Map<String, dynamic>? filters,
  }) async {
    await FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('searches')
        .add({
      'userId': userId,
      'query': query,
      'filters': filters,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// تسجيل زيارة المستخدم لصفحة
  Future<void> recordPageVisit({
    required String userId,
    required String pageType,
    required String pageId,
    Map<String, dynamic>? metadata,
  }) async {
    await FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('pageVisits')
        .add({
      'userId': userId,
      'pageType': pageType,
      'pageId': pageId,
      'timestamp': FieldValue.serverTimestamp(),
      'metadata': metadata,
    });
  }

  /// الحصول على تفضيلات المستخدم
  Future<Map<String, dynamic>?> getUserPreferences(String userId) async {
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('preferences')
        .doc('estate')
        .get();

    if (doc.exists) {
      return doc.data();
    }

    return null;
  }

  /// الحصول على تفاعلات المستخدم
  Future<List<Map<String, dynamic>>> getUserInteractions({
    required String userId,
    String? itemType,
    String? interactionType,
    int limit = 20,
  }) async {
    Query query = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('interactions')
        .orderBy('timestamp', descending: true)
        .limit(limit);

    if (itemType != null) {
      query = query.where('itemType', isEqualTo: itemType);
    }

    if (interactionType != null) {
      query = query.where('interactionType', isEqualTo: interactionType);
    }

    final snapshot = await query.get();

    return snapshot.docs
        .map((doc) => doc.data() as Map<String, dynamic>)
        .toList();
  }

  /// الحصول على عمليات بحث المستخدم
  Future<List<Map<String, dynamic>>> getUserSearches({
    required String userId,
    int limit = 20,
  }) async {
    final snapshot = await FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('searches')
        .orderBy('timestamp', descending: true)
        .limit(limit)
        .get();

    return snapshot.docs.map((doc) => doc.data()).toList();
  }

  /// الحصول على زيارات المستخدم للصفحات
  Future<List<Map<String, dynamic>>> getUserPageVisits({
    required String userId,
    String? pageType,
    int limit = 20,
  }) async {
    Query query = _firestore
        .collection('users')
        .doc(userId)
        .collection('pageVisits')
        .orderBy('timestamp', descending: true)
        .limit(limit);

    if (pageType != null) {
      query = query.where('pageType', isEqualTo: pageType);
    }

    final snapshot = await query.get();

    return snapshot.docs
        .map((doc) => doc.data() as Map<String, dynamic>)
        .toList();
  }

  /// الحصول على توصيات ذكية للمستخدم باستخدام الذكاء الاصطناعي
  Future<List<Recommendation>> getAIRecommendations({
    String? userId,
    RecommendationType? type,
    int limit = 10,
  }) async {
    // الحصول على معرف المستخدم الحالي إذا لم يتم تحديده
    final currentUser = _auth.currentUser;
    final effectiveUserId = userId ?? currentUser?.uid;

    if (effectiveUserId == null) {
      throw Exception('يجب تسجيل الدخول للحصول على التوصيات');
    }

    // الحصول على تفضيلات المستخدم
    final preferences = await getUserPreferences(effectiveUserId);

    // الحصول على تفاعلات المستخدم
    final interactions = await getUserInteractions(
      userId: effectiveUserId,
      itemType: 'estate',
      limit: 50);

    // الحصول على عمليات بحث المستخدم
    final searches = await getUserSearches(
      userId: effectiveUserId,
      limit: 20);

    // في التطبيق الحقيقي، سيتم استخدام نموذج ذكاء اصطناعي لتوليد التوصيات
    // هنا نقوم بمحاكاة النتائج

    // تحليل تفضيلات المستخدم
    final userProfile = _analyzeUserProfile(
      preferences: preferences,
      interactions: interactions,
      searches: searches);

    // توليد التوصيات بناءً على نوع التوصية
    if (type == null || type == RecommendationType.estate) {
      return _generateEstateRecommendations(
        userId: effectiveUserId,
        userProfile: userProfile,
        limit: limit);
    } else if (type == RecommendationType.area) {
      return _generateAreaRecommendations(
        userId: effectiveUserId,
        userProfile: userProfile,
        limit: limit);
    } else if (type == RecommendationType.agent) {
      return _generateAgentRecommendations(
        userId: effectiveUserId,
        userProfile: userProfile,
        limit: limit);
    } else if (type == RecommendationType.investment) {
      return _generateInvestmentRecommendations(
        userId: effectiveUserId,
        userProfile: userProfile,
        limit: limit);
    } else {
      // توصيات أخرى
      return _generateGenericRecommendations(
        userId: effectiveUserId,
        type: type,
        userProfile: userProfile,
        limit: limit);
    }
  }

  /// تحليل ملف المستخدم
  Map<String, dynamic> _analyzeUserProfile({
    Map<String, dynamic>? preferences,
    List<Map<String, dynamic>>? interactions,
    List<Map<String, dynamic>>? searches,
  }) {
    // في التطبيق الحقيقي، سيتم استخدام خوارزميات تحليل البيانات
    // هنا نقوم بمحاكاة النتائج

    final random = Random();

    // تحليل المناطق المفضلة
    final preferredAreas = <String, double>{};

    // من التفضيلات
    if (preferences != null && preferences['preferredAreas'] != null) {
      final areas = preferences['preferredAreas'] as List<dynamic>;
      for (final area in areas) {
        preferredAreas[area as String] = 1.0;
      }
    }

    // من التفاعلات
    if (interactions != null) {
      for (final interaction in interactions) {
        final estate =
            interaction['metadata']?['estate'] as Map<String, dynamic>?;
        if (estate != null && estate['area'] != null) {
          final area = estate['area'] as String;
          preferredAreas[area] = (preferredAreas[area] ?? 0.0) + 0.5;
        }
      }
    }

    // من عمليات البحث
    if (searches != null) {
      for (final search in searches) {
        final filters = search['filters'] as Map<String, dynamic>?;
        if (filters != null && filters['area'] != null) {
          final area = filters['area'] as String;
          preferredAreas[area] = (preferredAreas[area] ?? 0.0) + 0.3;
        }
      }
    }

    // تحليل أنواع العقارات المفضلة
    final preferredPropertyTypes = <String, double>{};

    // من التفضيلات
    if (preferences != null && preferences['preferredPropertyTypes'] != null) {
      final types = preferences['preferredPropertyTypes'] as List<dynamic>;
      for (final type in types) {
        preferredPropertyTypes[type as String] = 1.0;
      }
    }

    // من التفاعلات
    if (interactions != null) {
      for (final interaction in interactions) {
        final estate =
            interaction['metadata']?['estate'] as Map<String, dynamic>?;
        if (estate != null && estate['propertyType'] != null) {
          final propertyType = estate['propertyType'] as String;
          preferredPropertyTypes[propertyType] =
              (preferredPropertyTypes[propertyType] ?? 0.0) + 0.5;
        }
      }
    }

    // من عمليات البحث
    if (searches != null) {
      for (final search in searches) {
        final filters = search['filters'] as Map<String, dynamic>?;
        if (filters != null && filters['propertyType'] != null) {
          final propertyType = filters['propertyType'] as String;
          preferredPropertyTypes[propertyType] =
              (preferredPropertyTypes[propertyType] ?? 0.0) + 0.3;
        }
      }
    }

    // تحليل نطاق السعر المفضل
    double minPrice = preferences?['minPrice'] as double? ?? 0.0;
    double maxPrice = preferences?['maxPrice'] as double? ?? double.infinity;

    // تحليل عدد الغرف المفضل
    int minRooms = preferences?['minRooms'] as int? ?? 0;

    // تحليل المساحة المفضلة
    double minSize = preferences?['minSize'] as double? ?? 0.0;
    double maxSize = preferences?['maxSize'] as double? ?? double.infinity;

    // تحليل الميزات المفضلة
    final preferredFeatures = <String, double>{};

    // من التفضيلات
    if (preferences != null && preferences['preferredFeatures'] != null) {
      final features = preferences['preferredFeatures'] as List<dynamic>;
      for (final feature in features) {
        preferredFeatures[feature as String] = 1.0;
      }
    }

    // من التفاعلات
    if (interactions != null) {
      for (final interaction in interactions) {
        final estate =
            interaction['metadata']?['estate'] as Map<String, dynamic>?;
        if (estate != null && estate['features'] != null) {
          final features = estate['features'] as List<dynamic>;
          for (final feature in features) {
            preferredFeatures[feature as String] =
                (preferredFeatures[feature] ?? 0.0) + 0.5;
          }
        }
      }
    }

    // تحليل نوع التفاعل
    final interactionTypes = <String, int>{};

    if (interactions != null) {
      for (final interaction in interactions) {
        final type = interaction['interactionType'] as String;
        interactionTypes[type] = (interactionTypes[type] ?? 0) + 1;
      }
    }

    // تحليل الكلمات المفتاحية من عمليات البحث
    final searchKeywords = <String, int>{};

    if (searches != null) {
      for (final search in searches) {
        final query = search['query'] as String;
        final keywords = query.split(' ');
        for (final keyword in keywords) {
          if (keyword.length > 2) {
            searchKeywords[keyword] = (searchKeywords[keyword] ?? 0) + 1;
          }
        }
      }
    }

    return {
      'preferredAreas': preferredAreas,
      'preferredPropertyTypes': preferredPropertyTypes,
      'priceRange': {
        'min': minPrice,
        'max': maxPrice,
      },
      'minRooms': minRooms,
      'sizeRange': {
        'min': minSize,
        'max': maxSize,
      },
      'preferredFeatures': preferredFeatures,
      'interactionTypes': interactionTypes,
      'searchKeywords': searchKeywords,
      'interestScore': {
        'investment': 0.1 + random.nextDouble() * 0.8,
        'rental': 0.1 + random.nextDouble() * 0.8,
        'purchase': 0.1 + random.nextDouble() * 0.8,
        'luxury': 0.1 + random.nextDouble() * 0.8,
        'affordable': 0.1 + random.nextDouble() * 0.8,
      },
    };
  }

  /// توليد توصيات العقارات
  Future<List<Recommendation>> _generateEstateRecommendations({
    required String userId,
    required Map<String, dynamic> userProfile,
    required int limit,
  }) async {
    // في التطبيق الحقيقي، سيتم استخدام خوارزميات توصية متقدمة
    // هنا نقوم بمحاكاة النتائج

    final random = Random();
    final recommendations = <Recommendation>[];

    // الحصول على المناطق المفضلة
    final preferredAreas = userProfile['preferredAreas'] as Map<String, double>;
    final sortedAreas = preferredAreas.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // الحصول على أنواع العقارات المفضلة
    final preferredPropertyTypes =
        userProfile['preferredPropertyTypes'] as Map<String, double>;
    final sortedPropertyTypes = preferredPropertyTypes.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // توليد توصيات العقارات
    for (int i = 0; i < limit; i++) {
      // اختيار منطقة عشوائية من المناطق المفضلة
      final areaIndex =
          random.nextInt(sortedAreas.length.clamp(1, sortedAreas.length));
      final area = sortedAreas[areaIndex - 1].key;

      // اختيار نوع عقار عشوائي من أنواع العقارات المفضلة
      final propertyTypeIndex = random.nextInt(
          sortedPropertyTypes.length.clamp(1, sortedPropertyTypes.length));
      final propertyType = sortedPropertyTypes[propertyTypeIndex - 1].key;

      // توليد سعر عشوائي
      final price = 100000.0 + random.nextDouble() * 900000.0;

      // توليد معرف عقار عشوائي
      final estateId = 'estate_${random.nextInt(1000)}';

      // إنشاء توصية
      final recommendation = Recommendation(
        id: 'rec_${i + 1}',
        userId: userId,
        type: RecommendationType.estate,
        source: RecommendationSource.userPreferences,
        itemId: estateId,
        itemTitle: 'عقار مميز في $area',
        itemDescription:
            'عقار من نوع $propertyType بسعر ${price.toStringAsFixed(0)} ريال',
        itemImage: 'https://example.com/estate_$i.jpg',
        reason: 'بناءً على تفضيلاتك',
        relevanceScore: 0.5 + random.nextDouble() * 0.5,
        isViewed: false,
        isClicked: false,
        isDismissed: false,
        metadata: {
          'area': area,
          'propertyType': propertyType,
          'price': price,
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        // For compatibility with other parts of the code
        title: 'عقار مميز في $area',
        description:
            'عقار من نوع $propertyType بسعر ${price.toStringAsFixed(0)} ريال',
        imageUrl: 'https://example.com/estate_$i.jpg',
        score: 0.5 + random.nextDouble() * 0.5);

      recommendations.add(recommendation);
    }

    // ترتيب التوصيات حسب درجة التوصية
    recommendations.sort((a, b) => b.score.compareTo(a.score));

    return recommendations;
  }

  /// توليد توصيات المناطق
  Future<List<Recommendation>> _generateAreaRecommendations({
    required String userId,
    required Map<String, dynamic> userProfile,
    required int limit,
  }) async {
    // في التطبيق الحقيقي، سيتم استخدام خوارزميات توصية متقدمة
    // هنا نقوم بمحاكاة النتائج

    final random = Random();
    final recommendations = <Recommendation>[];

    // قائمة المناطق
    final areas = [
      'الرياض',
      'جدة',
      'الدمام',
      'مكة',
      'المدينة',
      'الخبر',
      'الظهران',
      'أبها',
      'الطائف',
      'تبوك',
    ];

    // الحصول على المناطق المفضلة
    final preferredAreas = userProfile['preferredAreas'] as Map<String, double>;

    // استبعاد المناطق المفضلة بالفعل
    final newAreas =
        areas.where((area) => !preferredAreas.containsKey(area)).toList();

    // توليد توصيات المناطق
    for (int i = 0; i < min(limit, newAreas.length); i++) {
      // اختيار منطقة عشوائية
      final areaIndex = random.nextInt(newAreas.length);
      final area = newAreas[areaIndex];

      // إزالة المنطقة من القائمة لتجنب التكرار
      newAreas.removeAt(areaIndex);

      // توليد معرف منطقة عشوائي
      final areaId = 'area_${area.hashCode}';

      // إنشاء توصية
      final recommendation = Recommendation(
        id: 'rec_area_${i + 1}',
        userId: userId,
        type: RecommendationType.area,
        source: RecommendationSource.userPreferences,
        itemId: areaId,
        itemTitle: 'منطقة $area',
        itemDescription: 'اكتشف العقارات المميزة في منطقة $area',
        itemImage: 'https://example.com/area_$i.jpg',
        reason: 'بناءً على تفضيلاتك',
        relevanceScore: 0.5 + random.nextDouble() * 0.5,
        isViewed: false,
        isClicked: false,
        isDismissed: false,
        metadata: {
          'area': area,
          'growthRate': 0.05 + random.nextDouble() * 0.1,
          'avgPrice': 3000 + random.nextDouble() * 2000,
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        // For compatibility with other parts of the code
        title: 'منطقة $area',
        description: 'اكتشف العقارات المميزة في منطقة $area',
        imageUrl: 'https://example.com/area_$i.jpg',
        score: 0.5 + random.nextDouble() * 0.5);

      recommendations.add(recommendation);
    }

    // ترتيب التوصيات حسب درجة التوصية
    recommendations.sort((a, b) => b.score.compareTo(a.score));

    return recommendations;
  }

  /// توليد توصيات الوكلاء
  Future<List<Recommendation>> _generateAgentRecommendations({
    required String userId,
    required Map<String, dynamic> userProfile,
    required int limit,
  }) async {
    // في التطبيق الحقيقي، سيتم استخدام خوارزميات توصية متقدمة
    // هنا نقوم بمحاكاة النتائج

    final random = Random();
    final recommendations = <Recommendation>[];

    // الحصول على المناطق المفضلة
    final preferredAreas = userProfile['preferredAreas'] as Map<String, double>;
    final sortedAreas = preferredAreas.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // الحصول على أنواع العقارات المفضلة
    final preferredPropertyTypes =
        userProfile['preferredPropertyTypes'] as Map<String, double>;

    // توليد توصيات الوكلاء
    for (int i = 0; i < limit; i++) {
      // اختيار منطقة عشوائية من المناطق المفضلة
      final area = sortedAreas.isNotEmpty
          ? sortedAreas[random.nextInt(sortedAreas.length)].key
          : 'الرياض';

      // توليد معرف وكيل عشوائي
      final agentId = 'agent_${random.nextInt(1000)}';

      // توليد تخصصات عشوائية
      final specialties = <String>[];
      preferredPropertyTypes.forEach((type, score) {
        if (random.nextDouble() < 0.7) {
          specialties.add(type);
        }
      });

      if (specialties.isEmpty) {
        specialties.add('شقق');
      }

      // إنشاء توصية
      final recommendation = Recommendation(
        id: 'rec_agent_${i + 1}',
        userId: userId,
        type: RecommendationType.agent,
        source: RecommendationSource.userPreferences,
        itemId: agentId,
        itemTitle: 'وكيل عقاري مميز',
        itemDescription: 'وكيل متخصص في ${specialties.join(', ')} بمنطقة $area',
        itemImage: 'https://example.com/agent_$i.jpg',
        reason: 'بناءً على تفضيلاتك',
        relevanceScore: 0.5 + random.nextDouble() * 0.5,
        isViewed: false,
        isClicked: false,
        isDismissed: false,
        metadata: {
          'area': area,
          'specialties': specialties,
          'rating': 3.5 + random.nextDouble() * 1.5,
          'dealsCount': 10 + random.nextInt(90),
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        // For compatibility with other parts of the code
        title: 'وكيل عقاري مميز',
        description: 'وكيل متخصص في ${specialties.join(', ')} بمنطقة $area',
        imageUrl: 'https://example.com/agent_$i.jpg',
        score: 0.5 + random.nextDouble() * 0.5);

      recommendations.add(recommendation);
    }

    // ترتيب التوصيات حسب درجة التوصية
    recommendations.sort((a, b) => b.score.compareTo(a.score));

    return recommendations;
  }

  /// توليد توصيات الاستثمار
  Future<List<Recommendation>> _generateInvestmentRecommendations({
    required String userId,
    required Map<String, dynamic> userProfile,
    required int limit,
  }) async {
    // في التطبيق الحقيقي، سيتم استخدام خوارزميات توصية متقدمة
    // هنا نقوم بمحاكاة النتائج

    final random = Random();
    final recommendations = <Recommendation>[];

    // الحصول على المناطق المفضلة
    final preferredAreas = userProfile['preferredAreas'] as Map<String, double>;
    final sortedAreas = preferredAreas.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // الحصول على أنواع العقارات المفضلة
    final preferredPropertyTypes =
        userProfile['preferredPropertyTypes'] as Map<String, double>;
    final sortedPropertyTypes = preferredPropertyTypes.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // توليد توصيات الاستثمار
    for (int i = 0; i < limit; i++) {
      // اختيار منطقة عشوائية من المناطق المفضلة
      final area = sortedAreas.isNotEmpty
          ? sortedAreas[random.nextInt(sortedAreas.length)].key
          : 'الرياض';

      // اختيار نوع عقار عشوائي من أنواع العقارات المفضلة
      final propertyType = sortedPropertyTypes.isNotEmpty
          ? sortedPropertyTypes[random.nextInt(sortedPropertyTypes.length)].key
          : 'شقة';

      // توليد عائد استثماري عشوائي
      final roi = 5.0 + random.nextDouble() * 10.0;

      // توليد معرف استثمار عشوائي
      final investmentId = 'investment_${random.nextInt(1000)}';

      // إنشاء توصية
      final recommendation = Recommendation(
        id: 'rec_investment_${i + 1}',
        userId: userId,
        type: RecommendationType.investment,
        source: RecommendationSource.userPreferences,
        itemId: investmentId,
        itemTitle: 'فرصة استثمارية في $area',
        itemDescription:
            'استثمار في $propertyType بعائد سنوي متوقع ${roi.toStringAsFixed(1)}%',
        itemImage: 'https://example.com/investment_$i.jpg',
        reason: 'بناءً على تفضيلاتك',
        relevanceScore: 0.5 + random.nextDouble() * 0.5,
        isViewed: false,
        isClicked: false,
        isDismissed: false,
        metadata: {
          'area': area,
          'propertyType': propertyType,
          'roi': roi,
          'investmentPeriod': 3 + random.nextInt(8),
          'riskLevel': ['منخفض', 'متوسط', 'مرتفع'][random.nextInt(3)],
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        // For compatibility with other parts of the code
        title: 'فرصة استثمارية في $area',
        description:
            'استثمار في $propertyType بعائد سنوي متوقع ${roi.toStringAsFixed(1)}%',
        imageUrl: 'https://example.com/investment_$i.jpg',
        score: 0.5 + random.nextDouble() * 0.5);

      recommendations.add(recommendation);
    }

    // ترتيب التوصيات حسب درجة التوصية
    recommendations.sort((a, b) => b.score.compareTo(a.score));

    return recommendations;
  }

  /// توليد توصيات عامة
  Future<List<Recommendation>> _generateGenericRecommendations({
    required String userId,
    required RecommendationType type,
    required Map<String, dynamic> userProfile,
    required int limit,
  }) async {
    // في التطبيق الحقيقي، سيتم استخدام خوارزميات توصية متقدمة
    // هنا نقوم بمحاكاة النتائج

    final random = Random();
    final recommendations = <Recommendation>[];

    // توليد توصيات عامة
    for (int i = 0; i < limit; i++) {
      // توليد معرف عنصر عشوائي
      final itemId =
          'item_${type.toString().split('.').last}_${random.nextInt(1000)}';

      // إنشاء توصية
      final recommendation = Recommendation(
        id: 'rec_${type.toString().split('.').last}_${i + 1}',
        userId: userId,
        type: type,
        source: RecommendationSource.userPreferences,
        itemId: itemId,
        itemTitle: 'توصية ${_getTypeTitle(type)}',
        itemDescription: 'توصية مخصصة لك بناءً على اهتماماتك',
        itemImage:
            'https://example.com/${type.toString().split('.').last}_$i.jpg',
        reason: 'بناءً على تفضيلاتك',
        relevanceScore: 0.5 + random.nextDouble() * 0.5,
        isViewed: false,
        isClicked: false,
        isDismissed: false,
        metadata: {
          'relevanceScore': 0.7 + random.nextDouble() * 0.3,
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        // For compatibility with other parts of the code
        title: 'توصية ${_getTypeTitle(type)}',
        description: 'توصية مخصصة لك بناءً على اهتماماتك',
        imageUrl:
            'https://example.com/${type.toString().split('.').last}_$i.jpg',
        score: 0.5 + random.nextDouble() * 0.5);

      recommendations.add(recommendation);
    }

    // ترتيب التوصيات حسب درجة التوصية
    recommendations.sort((a, b) => b.score.compareTo(a.score));

    return recommendations;
  }

  /// الحصول على عنوان نوع التوصية
  String _getTypeTitle(RecommendationType type) {
    switch (type) {
      case RecommendationType.estate:
        return 'عقار';
      case RecommendationType.area:
        return 'منطقة';
      case RecommendationType.agent:
        return 'وكيل';
      case RecommendationType.investment:
        return 'استثمار';
      case RecommendationType.article:
        return 'مقال';
      case RecommendationType.forum:
        return 'منتدى';
      default:
        return 'عامة';
    }
  }
}
