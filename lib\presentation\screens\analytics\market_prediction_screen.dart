import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../data/kuwait_locations.dart';
import '../../../domain/entities/market_prediction.dart';
import '../../../domain/repositories/analytics_repository.dart';
import '../../../infrastructure/services/analytics_service.dart';
import '../../widgets/analytics/chart_container.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/error_display_widget.dart';

/// شاشة التنبؤات المستقبلية
class MarketPredictionScreen extends StatefulWidget {
  final PredictionType? initialType;

  /// إنشاء شاشة التنبؤات المستقبلية
  const MarketPredictionScreen({super.key, this.initialType});

  @override
  State<MarketPredictionScreen> createState() => _MarketPredictionScreenState();
}

class _MarketPredictionScreenState extends State<MarketPredictionScreen> {
  PredictionType _selectedType = PredictionType.salePrices;
  PredictionPeriod _selectedPeriod = PredictionPeriod.year;
  String? _selectedArea;
  String? _selectedPropertyType;
  bool _isLoading = false;
  String? _errorMessage;
  List<MarketPrediction> _predictions = [];

  @override
  void initState() {
    super.initState();
    // استخدام النوع الأولي إذا كان متاحًا
    if (widget.initialType != null) {
      _selectedType = widget.initialType!;
    }
    _loadPredictions();
  }

  /// تحميل التنبؤات
  Future<void> _loadPredictions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final analyticsRepository = context.read<AnalyticsRepository>();
      final predictions = await analyticsRepository.getMarketPredictions(
        type: _selectedType,
        period: _selectedPeriod,
        area: _selectedArea,
        propertyType: _selectedPropertyType);

      setState(() {
        _predictions = predictions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل التنبؤات: $e';
        _isLoading = false;
      });
    }
  }

  /// تغيير نوع التنبؤ
  void _changePredictionType(PredictionType type) {
    setState(() {
      _selectedType = type;
    });
    _loadPredictions();
  }

  /// تغيير فترة التنبؤ
  void _changePredictionPeriod(PredictionPeriod period) {
    setState(() {
      _selectedPeriod = period;
    });
    _loadPredictions();
  }

  /// تغيير المنطقة
  void _changeArea(String? area) {
    setState(() {
      _selectedArea = area;
    });
    _loadPredictions();
  }

  /// تغيير نوع العقار
  void _changePropertyType(String? propertyType) {
    setState(() {
      _selectedPropertyType = propertyType;
    });
    _loadPredictions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التنبؤات المستقبلية'),
        centerTitle: true),
      body: Column(
        children: [
          // فلاتر التنبؤ
          _buildFilters(),

          // محتوى التنبؤ
          Expanded(
            child: _buildContent()),
        ]));
  }

  /// بناء الفلاتر
  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // نوع التنبؤ
          const Text(
            'نوع التنبؤ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildTypeChip(
                  PredictionType.salePrices,
                  'أسعار البيع',
                  Icons.monetization_on_outlined),
                _buildTypeChip(
                  PredictionType.rentalPrices,
                  'أسعار الإيجار',
                  Icons.home_outlined),
                _buildTypeChip(
                  PredictionType.supply,
                  'العرض',
                  Icons.trending_up),
                _buildTypeChip(
                  PredictionType.demand,
                  'الطلب',
                  Icons.trending_down),
                _buildTypeChip(
                  PredictionType.investmentReturn,
                  'العائد الاستثماري',
                  Icons.trending_up),
                _buildTypeChip(
                  PredictionType.growthRate,
                  'معدل النمو',
                  Icons.show_chart),
                _buildTypeChip(
                  PredictionType.occupancyRate,
                  'معدل الإشغال',
                  Icons.home_outlined),
              ])),
          const SizedBox(height: 16),

          // فترة التنبؤ
          const Text(
            'الفترة',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildPeriodChip(
                  PredictionPeriod.month,
                  'شهر'),
                _buildPeriodChip(
                  PredictionPeriod.threeMonths,
                  'ربع سنة'),
                _buildPeriodChip(
                  PredictionPeriod.sixMonths,
                  'نصف سنة'),
                _buildPeriodChip(
                  PredictionPeriod.year,
                  'سنة'),
              ])),
          const SizedBox(height: 16),

          // المنطقة ونوع العقار
          Row(
            children: [
              // المنطقة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'المنطقة',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    _buildAreaDropdown(),
                  ])),
              const SizedBox(width: 16),

              // نوع العقار
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'نوع العقار',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    _buildPropertyTypeDropdown(),
                  ])),
            ]),
        ]));
  }

  /// بناء شريحة نوع التنبؤ
  Widget _buildTypeChip(PredictionType type, String label, IconData icon) {
    final isSelected = _selectedType == type;

    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: ChoiceChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey[700]),
            const SizedBox(width: 4),
            Text(label),
          ]),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            _changePredictionType(type);
          }
        },
        backgroundColor: Colors.grey[200],
        selectedColor: Colors.purple,
        labelStyle: TextStyle(
          color: isSelected ? Colors.white : Colors.grey[800],
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal)));
  }

  /// بناء شريحة فترة التنبؤ
  Widget _buildPeriodChip(PredictionPeriod period, String label) {
    final isSelected = _selectedPeriod == period;

    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: ChoiceChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            _changePredictionPeriod(period);
          }
        },
        backgroundColor: Colors.grey[200],
        selectedColor: Colors.purple,
        labelStyle: TextStyle(
          color: isSelected ? Colors.white : Colors.grey[800],
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal)));
  }

  /// بناء قائمة منسدلة للمحافظات الكويتية
  Widget _buildAreaDropdown() {
    // استخدام المحافظات من الملف الذكي
    final areas = KuwaitLocations.governorates;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8)),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String?>(
          value: _selectedArea,
          hint: const Text('جميع المناطق'),
          isExpanded: true,
          icon: const Icon(Icons.keyboard_arrow_down),
          items: [
            const DropdownMenuItem<String?>(
              value: null,
              child: Text('جميع المناطق')),
            ...areas.map((area) => DropdownMenuItem<String?>(
                  value: area,
                  child: Text(area))),
          ],
          onChanged: _changeArea)));
  }

  /// بناء قائمة منسدلة لأنواع العقارات
  Widget _buildPropertyTypeDropdown() {
    // قائمة أنواع العقارات
    final propertyTypes = [
      'شقة',
      'منزل',
      'أرض',
      'عمارة',
      'مكتب',
      'محل تجاري',
      'مخزن',
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8)),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String?>(
          value: _selectedPropertyType,
          hint: const Text('جميع العقارات'),
          isExpanded: true,
          icon: const Icon(Icons.keyboard_arrow_down),
          items: [
            const DropdownMenuItem<String?>(
              value: null,
              child: Text('جميع العقارات')),
            ...propertyTypes.map((type) => DropdownMenuItem<String?>(
                  value: type,
                  child: Text(type))),
          ],
          onChanged: _changePropertyType)));
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator());
    }

    if (_errorMessage != null) {
      return ErrorDisplayWidget(
        message: _errorMessage!,
        onRetry: _loadPredictions);
    }

    if (_predictions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.trending_up,
              size: 64,
              color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'لا توجد تنبؤات متاحة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text(
              'جرب تغيير المعايير أو المحاولة لاحقًا',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600])),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadPredictions,
              child: const Text('إعادة المحاولة')),
          ]));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _predictions.length,
      itemBuilder: (context, index) {
        final prediction = _predictions[index];
        return _buildPredictionCard(prediction);
      });
  }

  /// بناء بطاقة التنبؤ
  Widget _buildPredictionCard(MarketPrediction prediction) {
    final analyticsService = context.read<AnalyticsService>();

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان التنبؤ
            Text(
              prediction.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),

            // وصف التنبؤ
            Text(
              prediction.description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600])),
            const SizedBox(height: 16),

            // الرسم البياني
            ChartContainer(
              title: 'التنبؤ المستقبلي',
              height: 200,
              chart: LineChart(
                analyticsService.createPredictionLineChart(prediction))),
            const SizedBox(height: 16),

            // معلومات التنبؤ
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildInfoColumn(
                  'القيمة الحالية',
                  prediction.currentValue.toStringAsFixed(1),
                  Colors.blue),
                _buildInfoColumn(
                  'التغير المتوقع',
                  '${(prediction.changePercentage * 100).toStringAsFixed(1)}%',
                  prediction.changePercentage >= 0 ? Colors.green : Colors.red),
                _buildInfoColumn(
                  'مستوى الثقة',
                  _getConfidenceLevelText(prediction.confidenceLevel),
                  Colors.purple),
              ]),
            const SizedBox(height: 16),

            // الرؤى
            const Text(
              'الرؤى',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            ...prediction.insights.map((insight) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(Icons.lightbulb_outline,
                          size: 18, color: Colors.amber),
                      const SizedBox(width: 8),
                      Expanded(child: Text(insight)),
                    ]))),
            const SizedBox(height: 16),

            // التوصيات
            const Text(
              'التوصيات',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            ...prediction.recommendations.map((recommendation) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(Icons.recommend,
                          size: 18, color: Colors.green),
                      const SizedBox(width: 8),
                      Expanded(child: Text(recommendation)),
                    ]))),
          ])));
  }

  /// بناء عمود معلومات
  Widget _buildInfoColumn(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600])),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color)),
      ]);
  }

  /// الحصول على نص مستوى الثقة
  String _getConfidenceLevelText(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.low:
        return 'منخفض (40%)';
      case ConfidenceLevel.medium:
        return 'متوسط (60%)';
      case ConfidenceLevel.high:
        return 'عالي (80%)';
      case ConfidenceLevel.veryHigh:
        return 'عالي جداً (95%)';
      default:
        return 'متوسط (60%)';
    }
  }
}
