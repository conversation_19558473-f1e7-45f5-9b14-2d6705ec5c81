// lib/presentation/widgets/property_request/property_request_filter_dialog.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/theme/app_colors.dart';
import '../../../data/kuwait_locations.dart';
import 'property_type_selector.dart';

/// حوار فلترة طلبات العقارات
class PropertyRequestFilterDialog extends StatefulWidget {
  /// نوع العقار المحدد
  final String? selectedPropertyType;

  /// المنطقة المحددة
  final String? selectedLocation;

  /// نطاق السعر
  final RangeValues? priceRange;

  /// الحد الأدنى لعدد الغرف
  final int? minRooms;

  /// الحد الأدنى لعدد الحمامات
  final int? minBathrooms;

  /// الحد الأدنى للمساحة
  final double? minArea;

  /// هل يحتوي على تكييف مركزي
  final bool? hasCentralAC;

  /// هل يحتوي على غرفة خادمة
  final bool? hasMaidRoom;

  /// هل يحتوي على مرآب
  final bool? hasGarage;

  /// هل يحتوي على مسبح
  final bool? hasSwimmingPool;

  /// هل يحتوي على مصعد
  final bool? hasElevator;

  /// هل مفروش بالكامل
  final bool? isFullyFurnished;

  /// ترتيب حسب
  final String sortBy;

  /// ترتيب تنازلي
  final bool descending;

  /// دالة عند تطبيق الفلاتر
  final Function(
    String? propertyType,
    String? location,
    RangeValues? priceRange,
    int? minRooms,
    int? minBathrooms,
    double? minArea,
    bool? hasCentralAC,
    bool? hasMaidRoom,
    bool? hasGarage,
    bool? hasSwimmingPool,
    bool? hasElevator,
    bool? isFullyFurnished,
    String sortBy,
    bool descending) onApply;

  /// دالة عند إعادة تعيين الفلاتر
  final VoidCallback onReset;

  const PropertyRequestFilterDialog({
    super.key,
    this.selectedPropertyType,
    this.selectedLocation,
    this.priceRange,
    this.minRooms,
    this.minBathrooms,
    this.minArea,
    this.hasCentralAC,
    this.hasMaidRoom,
    this.hasGarage,
    this.hasSwimmingPool,
    this.hasElevator,
    this.isFullyFurnished,
    required this.sortBy,
    required this.descending,
    required this.onApply,
    required this.onReset,
  });

  @override
  State<PropertyRequestFilterDialog> createState() => _PropertyRequestFilterDialogState();
}

class _PropertyRequestFilterDialogState extends State<PropertyRequestFilterDialog> {
  late String? _selectedPropertyType;
  late String? _selectedLocation;
  late String? _selectedGovernorate;
  late RangeValues? _priceRange;
  late TextEditingController _minRoomsController;
  late TextEditingController _minBathroomsController;
  late TextEditingController _minAreaController;
  late bool? _hasCentralAC;
  late bool? _hasMaidRoom;
  late bool? _hasGarage;
  late bool? _hasSwimmingPool;
  late bool? _hasElevator;
  late bool? _isFullyFurnished;
  late String _sortBy;
  late bool _descending;

  // فلاتر سريعة
  String? _quickFilter;

  @override
  void initState() {
    super.initState();

    _selectedPropertyType = widget.selectedPropertyType;
    _selectedLocation = widget.selectedLocation;
    _selectedGovernorate = null;
    _priceRange = widget.priceRange ?? const RangeValues(50, 10000);
    _minRoomsController = TextEditingController(
      text: widget.minRooms?.toString() ?? '');
    _minBathroomsController = TextEditingController(
      text: widget.minBathrooms?.toString() ?? '');
    _minAreaController = TextEditingController(
      text: widget.minArea?.toString() ?? '');
    _hasCentralAC = widget.hasCentralAC;
    _hasMaidRoom = widget.hasMaidRoom;
    _hasGarage = widget.hasGarage;
    _hasSwimmingPool = widget.hasSwimmingPool;
    _hasElevator = widget.hasElevator;
    _isFullyFurnished = widget.isFullyFurnished;
    _sortBy = widget.sortBy;
    _descending = widget.descending;
    _quickFilter = null;
  }

  @override
  void dispose() {
    _minRoomsController.dispose();
    _minBathroomsController.dispose();
    _minAreaController.dispose();
    super.dispose();
  }

  /// تطبيق الفلاتر
  void _applyFilters() {
    widget.onApply(
      _selectedPropertyType,
      _selectedLocation,
      _priceRange,
      _minRoomsController.text.isNotEmpty
          ? int.tryParse(_minRoomsController.text)
          : null,
      _minBathroomsController.text.isNotEmpty
          ? int.tryParse(_minBathroomsController.text)
          : null,
      _minAreaController.text.isNotEmpty
          ? double.tryParse(_minAreaController.text)
          : null,
      _hasCentralAC,
      _hasMaidRoom,
      _hasGarage,
      _hasSwimmingPool,
      _hasElevator,
      _isFullyFurnished,
      _sortBy,
      _descending);

    Navigator.pop(context);
  }

  /// إعادة تعيين الفلاتر
  void _resetFilters() {
    widget.onReset();
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.white, AppColors.orangeCardBackground],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryOrange.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10)),
          ]),
        child: Column(
          children: [
            // رأس الحوار العصري
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppColors.orangeGradient,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24))),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12)),
                    child: const Icon(
                      Icons.tune_rounded,
                      color: Colors.white,
                      size: 24)),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'فلتر ذكي للطلبات',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white))),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20)),
                    child: IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.pop(context))),
                ])),

            // محتوى الفلتر
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فلاتر سريعة
                    _buildQuickFilters(),
                    const SizedBox(height: 24),

                    // نوع العقار
                    _buildSectionTitle('نوع العقار', Icons.home_rounded),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppColors.primaryOrange.withValues(alpha: 0.2),
                          width: 1)),
                      child: PropertyTypeSelector(
                        selectedType: _selectedPropertyType ?? '',
                        onTypeSelected: (type) {
                          setState(() {
                            _selectedPropertyType = type;
                          });
                        })),
                    const SizedBox(height: 20),

            // الموقع الذكي
            _buildSectionTitle('الموقع', Icons.location_on_rounded),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.primaryOrange.withValues(alpha: 0.2),
                  width: 1)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // المحافظة
                  Text(
                    'المحافظة',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary)),
                  const SizedBox(height: 8),
                  _buildDropdown(
                    value: _selectedGovernorate,
                    items: KuwaitLocations.governorates,
                    hint: 'اختر المحافظة',
                    onChanged: (value) {
                      setState(() {
                        _selectedGovernorate = value;
                        _selectedLocation = null; // إعادة تعيين المنطقة
                      });
                    }),
                  const SizedBox(height: 16),

                  // المنطقة
                  if (_selectedGovernorate != null) ...[
                    Text(
                      'المنطقة',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary)),
                    const SizedBox(height: 8),
                    _buildDropdown(
                      value: _selectedLocation,
                      items: KuwaitLocations.getAreasByGovernorate(_selectedGovernorate!),
                      hint: 'اختر المنطقة',
                      onChanged: (value) {
                        setState(() {
                          _selectedLocation = value;
                        });
                      }),
                  ],
                ])),
            const SizedBox(height: 20),

            // نطاق السعر
            Text(
              'نطاق السعر (د.ك)',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                Text(
                  _priceRange!.start.toInt().toString(),
                  style: GoogleFonts.cairo()),
                Expanded(
                  child: RangeSlider(
                    values: _priceRange!,
                    min: 0,
                    max: 1000000,
                    divisions: 100,
                    activeColor: Colors.blue,
                    labels: RangeLabels(
                      _priceRange!.start.toInt().toString(),
                      _priceRange!.end.toInt().toString()),
                    onChanged: (values) {
                      setState(() {
                        _priceRange = values;
                      });
                    })),
                Text(
                  _priceRange!.end.toInt().toString(),
                  style: GoogleFonts.cairo()),
              ]),
            const SizedBox(height: 16),

            // عدد الغرف والحمامات
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الحد الأدنى لعدد الغرف',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _minRoomsController,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8))),
                        keyboardType: TextInputType.number),
                    ])),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الحد الأدنى لعدد الحمامات',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _minBathroomsController,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8))),
                        keyboardType: TextInputType.number),
                    ])),
              ]),
            const SizedBox(height: 16),

            // المساحة
            Text(
              'الحد الأدنى للمساحة (متر مربع)',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            TextField(
              controller: _minAreaController,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8))),
              keyboardType: TextInputType.number),
            const SizedBox(height: 16),

            // المميزات
            Text(
              'المميزات',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                _buildFeatureCheckbox(
                  'تكييف مركزي',
                  _hasCentralAC,
                  (value) {
                    setState(() {
                      _hasCentralAC = value;
                    });
                  }),
                _buildFeatureCheckbox(
                  'غرفة خادمة',
                  _hasMaidRoom,
                  (value) {
                    setState(() {
                      _hasMaidRoom = value;
                    });
                  }),
                _buildFeatureCheckbox(
                  'مرآب',
                  _hasGarage,
                  (value) {
                    setState(() {
                      _hasGarage = value;
                    });
                  }),
                _buildFeatureCheckbox(
                  'مسبح',
                  _hasSwimmingPool,
                  (value) {
                    setState(() {
                      _hasSwimmingPool = value;
                    });
                  }),
                _buildFeatureCheckbox(
                  'مصعد',
                  _hasElevator,
                  (value) {
                    setState(() {
                      _hasElevator = value;
                    });
                  }),
                _buildFeatureCheckbox(
                  'مفروش بالكامل',
                  _isFullyFurnished,
                  (value) {
                    setState(() {
                      _isFullyFurnished = value;
                    });
                  }),
              ]),
            const SizedBox(height: 16),

            // الترتيب
            Text(
              'الترتيب',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _sortBy,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8))),
                    items: [
                      DropdownMenuItem(
                        value: 'createdAt',
                        child: Text(
                          'تاريخ النشر',
                          style: GoogleFonts.cairo())),
                      DropdownMenuItem(
                        value: 'viewsCount',
                        child: Text(
                          'عدد المشاهدات',
                          style: GoogleFonts.cairo())),
                      DropdownMenuItem(
                        value: 'offersCount',
                        child: Text(
                          'عدد العروض',
                          style: GoogleFonts.cairo())),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _sortBy = value!;
                      });
                    })),
                const SizedBox(width: 16),
                DropdownButtonFormField<bool>(
                  value: _descending,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8))),
                  items: [
                    DropdownMenuItem(
                      value: true,
                      child: Text(
                        'تنازلي',
                        style: GoogleFonts.cairo())),
                    DropdownMenuItem(
                      value: false,
                      child: Text(
                        'تصاعدي',
                        style: GoogleFonts.cairo())),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _descending = value!;
                    });
                  }),
              ]),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),

            // أزرار التطبيق وإعادة التعيين
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
                border: Border(
                  top: BorderSide(
                    color: AppColors.primaryOrange.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.primaryOrange),
                        borderRadius: BorderRadius.circular(16)),
                      child: OutlinedButton(
                        onPressed: _resetFilters,
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16)),
                          side: BorderSide.none),
                        child: Text(
                          'إعادة تعيين',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryOrange))))),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: AppColors.orangeGradient,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryOrange.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4)),
                        ]),
                      child: ElevatedButton(
                        onPressed: _applyFilters,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16))),
                        child: Text(
                          'تطبيق الفلتر',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white))))),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مربع اختيار الميزة
  Widget _buildFeatureCheckbox(
    String label,
    bool? value,
    ValueChanged<bool?> onChanged) {
    return Container(
      width: 150,
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: value == true
          ? AppColors.primaryOrange.withValues(alpha: 0.1)
          : Colors.white.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value == true
            ? AppColors.primaryOrange
            : AppColors.primaryOrange.withValues(alpha: 0.2),
          width: 1)),
      child: CheckboxListTile(
        title: Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 13,
            fontWeight: value == true ? FontWeight.w600 : FontWeight.normal,
            color: value == true ? AppColors.primaryOrange : AppColors.textPrimary)),
        value: value ?? false,
        onChanged: onChanged,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8),
        controlAffinity: ListTileControlAffinity.leading,
        dense: true,
        activeColor: AppColors.primaryOrange,
        tristate: true));
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: AppColors.orangeGradient,
            borderRadius: BorderRadius.circular(10)),
          child: Icon(icon, color: Colors.white, size: 20)),
        const SizedBox(width: 12),
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary)),
      ]);
  }

  /// بناء الفلاتر السريعة
  Widget _buildQuickFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('فلاتر سريعة', Icons.flash_on_rounded),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildQuickFilterChip('شقق', 'شقة'),
            _buildQuickFilterChip('منازل', 'منزل'),
            _buildQuickFilterChip('مكاتب', 'مكتب'),
            _buildQuickFilterChip('محلات', 'محل تجاري'),
            _buildQuickFilterChip('مفروش', null, isFeature: true, feature: 'furnished'),
            _buildQuickFilterChip('مسبح', null, isFeature: true, feature: 'pool'),
            _buildQuickFilterChip('مرآب', null, isFeature: true, feature: 'garage'),
          ]),
      ]);
  }

  /// بناء رقاقة الفلتر السريع
  Widget _buildQuickFilterChip(String label, String? propertyType, {bool isFeature = false, String? feature}) {
    bool isSelected = false;

    if (isFeature) {
      switch (feature) {
        case 'furnished':
          isSelected = _isFullyFurnished == true;
          break;
        case 'pool':
          isSelected = _hasSwimmingPool == true;
          break;
        case 'garage':
          isSelected = _hasGarage == true;
          break;
      }
    } else {
      isSelected = _selectedPropertyType == propertyType;
    }

    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: isSelected ? Colors.white : AppColors.textPrimary)),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (isFeature) {
            switch (feature) {
              case 'furnished':
                _isFullyFurnished = selected ? true : null;
                break;
              case 'pool':
                _hasSwimmingPool = selected ? true : null;
                break;
              case 'garage':
                _hasGarage = selected ? true : null;
                break;
            }
          } else {
            _selectedPropertyType = selected ? propertyType : null;
          }
        });
      },
      backgroundColor: Colors.white.withValues(alpha: 0.8),
      selectedColor: AppColors.primaryOrange,
      checkmarkColor: Colors.white,
      side: BorderSide(
        color: isSelected
          ? AppColors.primaryOrange
          : AppColors.primaryOrange.withValues(alpha: 0.3),
        width: 1));
  }

  /// بناء قائمة منسدلة
  Widget _buildDropdown({
    required String? value,
    required List<String> items,
    required String hint,
    required Function(String?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primaryOrange.withValues(alpha: 0.3),
          width: 1)),
      child: DropdownButtonFormField<String>(
        value: value,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          hintText: hint,
          hintStyle: GoogleFonts.cairo(
            color: Colors.grey.shade500,
            fontSize: 14)),
        items: items.map((item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.textPrimary)));
        }).toList(),
        onChanged: onChanged,
        style: GoogleFonts.cairo(color: AppColors.textPrimary),
        dropdownColor: Colors.white,
        icon: Icon(
          Icons.keyboard_arrow_down_rounded,
          color: AppColors.primaryOrange)));
  }


}
