import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/topic_model.dart';
import '../../../domain/models/forum/post_model.dart';
import '../../../presentation/providers/auth_provider.dart' as app_auth;
import '../../../presentation/providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/empty_view.dart';
import '../../widgets/forum/modern_topic_card.dart';
import '../../widgets/forum/modern_posts_list.dart';

/// صفحة تفاصيل الموضوع الحديثة
class ModernTopicDetailPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/topic';

  /// معرف الموضوع
  final String topicId;

  const ModernTopicDetailPage({
    super.key,
    required this.topicId,
  });

  @override
  State<ModernTopicDetailPage> createState() => _ModernTopicDetailPageState();
}

class _ModernTopicDetailPageState extends State<ModernTopicDetailPage> {
  final TextEditingController _replyController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isReplying = false;
  String? _replyToPostId;
  String? _replyToUserName;
  bool _showFloatingButton = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);

    // تحميل البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _replyController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  /// استماع لحدث التمرير
  void _scrollListener() {
    if (_scrollController.position.userScrollDirection == ScrollDirection.reverse) {
      if (_showFloatingButton) {
        setState(() {
          _showFloatingButton = false;
        });
      }
    } else if (_scrollController.position.userScrollDirection == ScrollDirection.forward) {
      if (!_showFloatingButton) {
        setState(() {
          _showFloatingButton = true;
        });
      }
    }
  }

  /// تحميل البيانات
  void _loadData() {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    forumProvider.fetchTopic(widget.topicId);
    forumProvider.fetchPosts(widget.topicId);

    // زيادة عدد المشاهدات
    final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
    final userId = authProvider.isLoggedIn ? authProvider.user!.uid : null;
    if (userId != null) {
      forumProvider.incrementTopicViews(widget.topicId, userId);
    }
  }

  /// إرسال رد
  void _submitReply() {
    if (_replyController.text.trim().isEmpty) {
      return;
    }

    final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
    if (!authProvider.isLoggedIn || authProvider.user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
      return;
    }

    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final topic = forumProvider.currentTopic;
    if (topic == null) {
      return;
    }

    final post = PostModel(
      id: '',
      topicId: topic.id,
      topicTitle: topic.title,
      categoryId: topic.categoryId,
      categoryName: topic.categoryName,
      parentId: _replyToPostId,
      content: _replyController.text.trim(),
      userId: authProvider.user!.uid,
      userName: authProvider.user!.displayName ?? 'مستخدم',
      userImage: authProvider.user!.photoURL,
      likesCount: 0,
      repliesCount: 0,
      isPinned: false,
      isBestAnswer: false,
      isReported: false,
      isDeleted: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now());

    forumProvider.addPost(post).then((_) {
      _replyController.clear();
      setState(() {
        _isReplying = false;
        _replyToPostId = null;
        _replyToUserName = null;
      });

      // التمرير إلى أسفل القائمة
      Future.delayed(const Duration(milliseconds: 300), () {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOut);
        }
      });
    });
  }

  /// بدء الرد على مشاركة
  void _startReplyToPost(PostModel post) {
    setState(() {
      _isReplying = true;
      _replyToPostId = post.id;
      _replyToUserName = post.userName;
      _replyController.text = '@${post.userName} ';
    });

    // التركيز على حقل الرد
    FocusScope.of(context).requestFocus(FocusNode());
    Future.delayed(const Duration(milliseconds: 100), () {
      FocusScope.of(context).requestFocus(FocusNode());
    });
  }

  /// إلغاء الرد
  void _cancelReply() {
    setState(() {
      _isReplying = false;
      _replyToPostId = null;
      _replyToUserName = null;
      _replyController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الموضوع'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              final forumProvider = Provider.of<ForumProvider>(context, listen: false);
              final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
              final userId = authProvider.isLoggedIn ? authProvider.user!.uid : null;
              forumProvider.shareTopic(widget.topicId, userId ?? '');
            }),
          PopupMenuButton<String>(
            onSelected: (value) {
              final forumProvider = Provider.of<ForumProvider>(context, listen: false);
              final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
              final userId = authProvider.isLoggedIn ? authProvider.user!.uid : null;

              switch (value) {
                case 'bookmark':
                  if (userId != null) {
                    forumProvider.bookmarkTopic(widget.topicId, userId);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
                  }
                  break;
                case 'follow':
                  if (userId != null) {
                    forumProvider.followTopic(widget.topicId, userId);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
                  }
                  break;
                case 'report':
                  // عرض نافذة الإبلاغ
                  _showReportDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'bookmark',
                child: Row(
                  children: [
                    Icon(Icons.bookmark_border),
                    SizedBox(width: 8),
                    Text('حفظ الموضوع'),
                  ])),
              const PopupMenuItem(
                value: 'follow',
                child: Row(
                  children: [
                    Icon(Icons.notifications_none),
                    SizedBox(width: 8),
                    Text('متابعة الموضوع'),
                  ])),
              const PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.flag_outlined),
                    SizedBox(width: 8),
                    Text('الإبلاغ عن الموضوع'),
                  ])),
            ]),
        ]),
      body: Column(
        children: [
          // الموضوع
          _buildTopicSection(),

          // المشاركات
          Expanded(
            child: _buildPostsSection()),

          // حقل الرد
          _buildReplyField(),
        ]),
      floatingActionButton: AnimatedSlide(
        duration: const Duration(milliseconds: 300),
        offset: _showFloatingButton ? Offset.zero : const Offset(0, 2),
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 300),
          opacity: _showFloatingButton ? 1.0 : 0.0,
          child: FloatingActionButton(
            heroTag: "topic_detail_fab",
            onPressed: () {
              setState(() {
                _isReplying = true;
              });
              // التمرير إلى أسفل القائمة
              Future.delayed(const Duration(milliseconds: 300), () {
                if (_scrollController.hasClients) {
                  _scrollController.animateTo(
                    _scrollController.position.maxScrollExtent,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeOut);
                }
              });
            },
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.reply)))));
  }

  /// بناء قسم الموضوع
  Widget _buildTopicSection() {
    return Consumer<ForumProvider>(
      builder: (context, forumProvider, child) {
        if (forumProvider.currentTopicState == LoadingState.loading) {
          return const SizedBox(
            height: 200,
            child: Center(child: LoadingIndicator()));
        }

        if (forumProvider.currentTopicState == LoadingState.error) {
          return SizedBox(
            height: 200,
            child: ErrorView(
              message: 'حدث خطأ أثناء تحميل الموضوع',
              onRetry: () => forumProvider.fetchTopic(widget.topicId)));
        }

        final topic = forumProvider.currentTopic;
        if (topic == null) {
          return const SizedBox(
            height: 200,
            child: Center(
              child: Text('الموضوع غير موجود')));
        }

        final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
        final currentUserId = authProvider.isLoggedIn ? authProvider.user!.uid : null;
        final isLiked = topic.likedBy?.contains(currentUserId) ?? false;
        final isBookmarked = topic.bookmarkedBy?.contains(currentUserId) ?? false;

        return ModernTopicCard(
          topic: topic,
          onTap: () {},
          onLikeTap: () {
            if (currentUserId != null) {
              forumProvider.likeTopic(topic.id, currentUserId);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
            }
          },
          onBookmarkTap: () {
            if (currentUserId != null) {
              if (isBookmarked) {
                forumProvider.unbookmarkTopic(topic.id, currentUserId);
              } else {
                forumProvider.bookmarkTopic(topic.id, currentUserId);
              }
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
            }
          },
          onShareTap: () {
            forumProvider.shareTopic(topic.id, currentUserId ?? '');
          },
          isLiked: isLiked,
          isBookmarked: isBookmarked);
      });
  }

  /// بناء قسم المشاركات
  Widget _buildPostsSection() {
    return Consumer<ForumProvider>(
      builder: (context, forumProvider, child) {
        if (forumProvider.postsState == LoadingState.loading && forumProvider.posts.isEmpty) {
          return const Center(child: LoadingIndicator());
        }

        if (forumProvider.postsState == LoadingState.error) {
          return ErrorView(
            message: 'حدث خطأ أثناء تحميل المشاركات',
            onRetry: () => forumProvider.fetchPosts(widget.topicId));
        }

        if (forumProvider.posts.isEmpty) {
          return const EmptyView(
            message: 'لا توجد مشاركات بعد');
        }

        final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
        final currentUserId = authProvider.isLoggedIn ? authProvider.user!.uid : null;
        final topic = forumProvider.currentTopic;

        return ModernPostsList(
          posts: forumProvider.posts,
          onLikeTap: (post) {
            if (currentUserId != null) {
              forumProvider.likePost(post.id, currentUserId);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
            }
          },
          onReplyTap: (post) {
            _startReplyToPost(post);
          },
          onReportTap: (post) {
            _showReportPostDialog(post.id);
          },
          onEditTap: (post) {
            // التنقل إلى صفحة تعديل المشاركة
            Navigator.pushNamed(
              context,
              '/forum/edit-post',
              arguments: post.id).then((_) => _loadData());
          },
          onDeleteTap: (post) {
            _showDeletePostDialog(post.id);
          },
          onMarkAsBestAnswerTap: (post) {
            if (topic != null && currentUserId == topic.userId) {
              forumProvider.markPostAsBestAnswer(post.id, topic.id);
            }
          },
          currentUserId: currentUserId,
          topicOwnerId: topic?.userId,
          isLoadingMore: forumProvider.postsState == LoadingState.loading && forumProvider.posts.isNotEmpty,
          onLoadMore: () {
            forumProvider.fetchMorePosts(widget.topicId);
          },
          scrollController: _scrollController,
          showNestedReplies: true);
      });
  }

  /// بناء حقل الرد
  Widget _buildReplyField() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: _isReplying ? null : 0,
      child: Visibility(
        visible: _isReplying,
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, -2)),
            ]),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_replyToUserName != null) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8)),
                  child: Row(
                    children: [
                      Text(
                        'الرد على: $_replyToUserName',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700)),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close, size: 16),
                        onPressed: _cancelReply,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints()),
                    ])),
                const SizedBox(height: 8),
              ],
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _replyController,
                      decoration: InputDecoration(
                        hintText: 'اكتب رداً...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(24),
                          borderSide: BorderSide(color: Colors.grey.shade300)),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12)),
                      maxLines: 3,
                      minLines: 1)),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.send),
                    onPressed: _submitReply,
                    color: AppColors.primary),
                ]),
            ]))));
  }

  /// عرض نافذة الإبلاغ عن الموضوع
  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإبلاغ عن الموضوع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('يرجى اختيار سبب الإبلاغ:'),
            const SizedBox(height: 16),
            _buildReportOption('محتوى غير لائق'),
            _buildReportOption('محتوى مسيء'),
            _buildReportOption('محتوى مكرر'),
            _buildReportOption('معلومات خاطئة'),
            _buildReportOption('سبب آخر'),
          ]),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء')),
        ]));
  }

  /// عرض نافذة الإبلاغ عن مشاركة
  void _showReportPostDialog(String postId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإبلاغ عن المشاركة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('يرجى اختيار سبب الإبلاغ:'),
            const SizedBox(height: 16),
            _buildReportPostOption(postId, 'محتوى غير لائق'),
            _buildReportPostOption(postId, 'محتوى مسيء'),
            _buildReportPostOption(postId, 'محتوى مكرر'),
            _buildReportPostOption(postId, 'معلومات خاطئة'),
            _buildReportPostOption(postId, 'سبب آخر'),
          ]),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء')),
        ]));
  }

  /// عرض نافذة حذف مشاركة
  void _showDeletePostDialog(String postId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المشاركة'),
        content: const Text('هل أنت متأكد من رغبتك في حذف هذه المشاركة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء')),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final forumProvider = Provider.of<ForumProvider>(context, listen: false);
              forumProvider.deletePost(postId).then((_) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم حذف المشاركة بنجاح')));
              });
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف')),
        ]));
  }

  /// بناء خيار الإبلاغ عن الموضوع
  Widget _buildReportOption(String reason) {
    return ListTile(
      title: Text(reason),
      onTap: () {
        Navigator.pop(context);
        final forumProvider = Provider.of<ForumProvider>(context, listen: false);
        final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
        final userId = authProvider.isLoggedIn ? authProvider.user!.uid : null;

        if (userId != null) {
          forumProvider.reportTopic(widget.topicId, userId, reason).then((_) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم إرسال البلاغ بنجاح')));
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
        }
      });
  }

  /// بناء خيار الإبلاغ عن مشاركة
  Widget _buildReportPostOption(String postId, String reason) {
    return ListTile(
      title: Text(reason),
      onTap: () {
        Navigator.pop(context);
        final forumProvider = Provider.of<ForumProvider>(context, listen: false);
        final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
        final userId = authProvider.isLoggedIn ? authProvider.user!.uid : null;

        if (userId != null) {
          forumProvider.reportPost(postId, userId, reason).then((_) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم إرسال البلاغ بنجاح')));
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
        }
      });
  }
}
