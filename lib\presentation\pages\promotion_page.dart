import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/models/subscription_model.dart';
import 'package:kuwait_corners/core/services/subscription_service.dart';
import 'package:kuwait_corners/presentation/widgets/custom_app_bar.dart';
import 'package:kuwait_corners/presentation/widgets/custom_button.dart';
import 'package:kuwait_corners/presentation/widgets/loading_indicator.dart';

/// صفحة الترويج للإعلان
class PromotionPage extends StatefulWidget {
  final String adId;
  final String adTitle;

  const PromotionPage({
    super.key,
    required this.adId,
    required this.adTitle,
  });

  @override
  State<PromotionPage> createState() => _PromotionPageState();
}

class _PromotionPageState extends State<PromotionPage> {
  final SubscriptionService _subscriptionService = SubscriptionService();
  bool _isLoading = true;
  List<Map<String, dynamic>> _availablePromotions = [];
  List<PromotionModel> _currentPromotions = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final availablePromotions =
          await _subscriptionService.getAvailablePromotions();
      final currentPromotions =
          await _subscriptionService.getAdPromotions(widget.adId);

      setState(() {
        _availablePromotions = availablePromotions;
        _currentPromotions = currentPromotions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحميل البيانات: $e')));
      }
    }
  }

  /// شراء ترويج
  Future<void> _purchasePromotion(PromotionType type) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // هنا يتم تنفيذ عملية الدفع
      // في هذا المثال، نفترض أن عملية الدفع تمت بنجاح
      final paymentId = 'payment_${DateTime.now().millisecondsSinceEpoch}';

      final promotion = await _subscriptionService.purchasePromotion(
        widget.adId,
        type,
        paymentId);

      if (promotion != null) {
        await _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم شراء الترويج بنجاح')));
        }
      } else {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('فشل شراء الترويج')));
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء شراء الترويج: $e')));
      }
    }
  }

  /// إلغاء ترويج
  Future<void> _cancelPromotion(String promotionId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _subscriptionService.cancelPromotion(promotionId);

      if (success) {
        await _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إلغاء الترويج بنجاح')));
        }
      } else {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('فشل إلغاء الترويج')));
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء إلغاء الترويج: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'ترويج الإعلان',
        showBackButton: true),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ترويج الإعلان: ${widget.adTitle}',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 16),
                  _buildCurrentPromotions(),
                  const SizedBox(height: 24),
                  const Text(
                    'الترويجات المتاحة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 16),
                  _buildAvailablePromotions(),
                ])));
  }

  /// بناء واجهة الترويجات الحالية
  Widget _buildCurrentPromotions() {
    if (_currentPromotions.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            'لا توجد ترويجات نشطة لهذا الإعلان',
            textAlign: TextAlign.center)));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الترويجات النشطة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        ...List<Widget>.from(
          _currentPromotions.map(
            (promotion) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          promotion.getPromotionTypeName(),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold)),
                        _buildPromotionBadge(promotion.type),
                      ]),
                    const SizedBox(height: 8),
                    _buildInfoRow(
                        'تاريخ البدء:', _formatDate(promotion.startDate)),
                    _buildInfoRow(
                        'تاريخ الانتهاء:', _formatDate(promotion.endDate)),
                    _buildInfoRow('الأيام المتبقية:',
                        '${promotion.getRemainingDays()} يوم'),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        CustomButton(
                          text: 'إلغاء الترويج',
                          onPressed: () => _cancelPromotion(promotion.id),
                          color: Colors.red,
                          width: 120),
                      ]),
                  ]))))),
      ]);
  }

  /// بناء واجهة الترويجات المتاحة
  Widget _buildAvailablePromotions() {
    return Column(
      children: _availablePromotions.map((promotion) {
        final type = promotion['type'] as PromotionType;
        final hasActivePromotion =
            _currentPromotions.any((p) => p.type == type);

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12)),
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      promotion['name'] as String,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
                    _buildPromotionBadge(type),
                  ]),
                const SizedBox(height: 8),
                Text(
                  promotion['description'] as String,
                  style: TextStyle(
                    color: Colors.grey[600])),
                const SizedBox(height: 16),
                _buildInfoRow('المدة:', '${promotion['durationDays']} يوم'),
                _buildInfoRow(
                    'السعر:', '${promotion['price']} ${promotion['currency']}'),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (!hasActivePromotion)
                      CustomButton(
                        text: 'اشترك الآن',
                        onPressed: () => _purchasePromotion(type),
                        width: 120),
                    if (hasActivePromotion)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(20)),
                        child: const Text(
                          'نشط حالياً',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold))),
                  ]),
              ])));
      }).toList());
  }

  /// بناء شارة نوع الترويج
  Widget _buildPromotionBadge(PromotionType type) {
    Color color;

    switch (type) {
      case PromotionType.featured:
        color = Colors.teal;
        break;
      case PromotionType.pinned:
        color = Colors.blue;
        break;
      case PromotionType.highlighted:
        color = Colors.orange;
        break;
      case PromotionType.video:
        color = Colors.red;
        break;
      case PromotionType.premium:
        color = Colors.purple;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 6),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(20)),
      child: Text(
        _getPromotionTypeShortName(type),
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold)));
  }

  /// الحصول على الاسم المختصر لنوع الترويج
  String _getPromotionTypeShortName(PromotionType type) {
    switch (type) {
      case PromotionType.featured:
        return 'بارز';
      case PromotionType.pinned:
        return 'مثبت';
      case PromotionType.highlighted:
        return 'مميز';
      case PromotionType.video:
        return 'فيديو';
      case PromotionType.premium:
        return 'متميز';
    }
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.grey)),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold)),
        ]));
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }
}
