// lib/presentation/widgets/property_request/property_offer_card.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;

// import '../../../core/constants/app_colors.dart';
import '../../../domain/models/property_request/property_offer_model.dart';

/// بطاقة عرض عقار
class PropertyOfferCard extends StatelessWidget {
  /// العرض
  final PropertyOfferModel offer;

  /// هل المستخدم الحالي هو صاحب الطلب
  final bool isOwner;

  /// دالة عند قبول العرض
  final VoidCallback? onAccept;

  /// دالة عند رفض العرض
  final VoidCallback? onReject;

  const PropertyOfferCard({
    super.key,
    required this.offer,
    this.isOwner = false,
    this.onAccept,
    this.onReject,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صور العرض
          if (offer.photoUrls != null && offer.photoUrls!.isNotEmpty)
            SizedBox(
              height: 150,
              child: PageView.builder(
                itemCount: offer.photoUrls!.length,
                itemBuilder: (context, index) {
                  return ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12)),
                    child: CachedNetworkImage(
                      imageUrl: offer.photoUrls![index],
                      width: double.infinity,
                      height: 150,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        width: double.infinity,
                        height: 150,
                        color: Colors.grey.shade200,
                        child: const Icon(
                          Icons.image,
                          color: Colors.grey,
                          size: 48)),
                      errorWidget: (context, url, error) => Container(
                        width: double.infinity,
                        height: 150,
                        color: Colors.grey.shade200,
                        child: const Icon(
                          Icons.error,
                          color: Colors.red,
                          size: 48))));
                }))
          else
            Container(
              width: double.infinity,
              height: 150,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12))),
              child: const Icon(
                Icons.home,
                color: Colors.grey,
                size: 48)),

          // تفاصيل العرض
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        offer.title,
                        style: GoogleFonts.cairo(
                          fontWeight: FontWeight.bold,
                          fontSize: 16),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis)),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(offer.status).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8)),
                      child: Text(
                        _getStatusText(offer.status),
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: _getStatusColor(offer.status)))),
                  ]),
                const SizedBox(height: 8),
                Text(
                  offer.description,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade800),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis),
                const SizedBox(height: 12),

                // السعر
                Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: Colors.grey.shade600),
                    const SizedBox(width: 4),
                    Text(
                      '${offer.price.toStringAsFixed(0)} د.ك',
                      style: GoogleFonts.cairo(
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                        fontSize: 16)),
                  ]),
                const SizedBox(height: 8),

                // العنوان
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: Colors.grey.shade600),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        offer.address,
                        style: GoogleFonts.cairo(
                          color: Colors.grey.shade600,
                          fontSize: 14),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis)),
                  ]),
                const SizedBox(height: 8),

                // المميزات
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    _buildFeatureChip('${offer.rooms} غرف', Icons.bedroom_parent),
                    _buildFeatureChip('${offer.bathrooms} حمامات', Icons.bathroom),
                    _buildFeatureChip('${offer.area} م²', Icons.square_foot),
                    if (offer.hasCentralAC)
                      _buildFeatureChip('تكييف مركزي', Icons.ac_unit),
                    if (offer.hasMaidRoom)
                      _buildFeatureChip('غرفة خادمة', Icons.person),
                    if (offer.hasGarage)
                      _buildFeatureChip('مرآب', Icons.garage),
                    if (offer.hasSwimmingPool)
                      _buildFeatureChip('مسبح', Icons.pool),
                    if (offer.hasElevator)
                      _buildFeatureChip('مصعد', Icons.elevator),
                    if (offer.isFullyFurnished)
                      _buildFeatureChip('مفروش بالكامل', Icons.chair),
                  ]),

                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 12),

                // معلومات المستخدم والوقت
                Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.grey.shade200,
                      backgroundImage: offer.userImage != null
                          ? NetworkImage(offer.userImage!)
                          : null,
                      child: offer.userImage == null
                          ? const Icon(Icons.person, color: Colors.grey, size: 16)
                          : null),
                    const SizedBox(width: 8),
                    Text(
                      offer.userName,
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        fontSize: 14)),
                    const Spacer(),
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: Colors.grey.shade600),
                    const SizedBox(width: 4),
                    Text(
                      timeago.format(offer.createdAt, locale: 'ar'),
                      style: GoogleFonts.cairo(
                        color: Colors.grey.shade600,
                        fontSize: 12)),
                  ]),

                // أزرار القبول والرفض (للمالك فقط)
                if (isOwner && offer.status == OfferStatus.pending) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: onAccept,
                          icon: const Icon(Icons.check),
                          label: Text(
                            'قبول العرض',
                            style: GoogleFonts.cairo()),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white))),
                      const SizedBox(width: 16),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: onReject,
                          icon: const Icon(Icons.close),
                          label: Text(
                            'رفض العرض',
                            style: GoogleFonts.cairo()),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red,
                            side: const BorderSide(color: Colors.red)))),
                    ]),
                ],
              ])),
        ]));
  }

  /// بناء شريحة ميزة
  Widget _buildFeatureChip(String label, IconData icon) {
    return Chip(
      avatar: Icon(
        icon,
        size: 16,
        color: Colors.blue),
      label: Text(
        label,
        style: GoogleFonts.cairo(
          fontSize: 12)),
      backgroundColor: Colors.grey.shade100,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact);
  }

  /// الحصول على نص حالة العرض
  String _getStatusText(OfferStatus status) {
    switch (status) {
      case OfferStatus.pending:
        return 'قيد الانتظار';
      case OfferStatus.accepted:
        return 'مقبول';
      case OfferStatus.rejected:
        return 'مرفوض';
    }
  }

  /// الحصول على لون حالة العرض
  Color _getStatusColor(OfferStatus status) {
    switch (status) {
      case OfferStatus.pending:
        return Colors.orange;
      case OfferStatus.accepted:
        return Colors.green;
      case OfferStatus.rejected:
        return Colors.red;
    }
  }
}
