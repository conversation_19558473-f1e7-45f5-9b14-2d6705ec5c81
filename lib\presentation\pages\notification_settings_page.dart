import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/services/notification_service.dart';

/// صفحة إعدادات الإشعارات
/// تتيح للمستخدم تخصيص إعدادات الإشعارات
class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  final NotificationService _notificationService = NotificationService();
  
  // حالة الصفحة
  bool _isLoading = true;
  String? _errorMessage;
  Map<String, bool> _settings = {};
  
  @override
  void initState() {
    super.initState();
    _loadSettings();
  }
  
  /// تحميل إعدادات الإشعارات
  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    
    try {
      final settings = await _notificationService.getNotificationSettings();
      
      setState(() {
        _settings = settings;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل إعدادات الإشعارات';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// حفظ إعدادات الإشعارات
  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      await _notificationService.updateNotificationSettings(_settings);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم حفظ إعدادات الإشعارات')));
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء حفظ إعدادات الإشعارات';
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('حدث خطأ أثناء حفظ إعدادات الإشعارات')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// تحديث إعداد
  void _updateSetting(String key, bool value) {
    setState(() {
      _settings[key] = value;
    });
  }
  
  /// تحديث جميع الإعدادات
  void _updateAllSettings(bool value) {
    setState(() {
      for (final key in _settings.keys) {
        _settings[key] = value;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات'),
        actions: [
          // زر حفظ الإعدادات
          TextButton.icon(
            onPressed: _saveSettings,
            icon: const Icon(Icons.save),
            label: const Text('حفظ')),
        ]),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, color: Colors.red, size: 48),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red)),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadSettings,
                        child: const Text('إعادة المحاولة')),
                    ]))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // وصف الصفحة
                      const Text(
                        'يمكنك تخصيص أنواع الإشعارات التي ترغب في تلقيها. قم بتفعيل أو تعطيل الإشعارات حسب احتياجاتك.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey)),
                      
                      const SizedBox(height: 24),
                      
                      // زر تفعيل/تعطيل جميع الإشعارات
                      Row(
                        children: [
                          const Text(
                            'جميع الإشعارات',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold)),
                          const Spacer(),
                          Switch(
                            value: _settings.values.every((value) => value),
                            onChanged: (value) {
                              _updateAllSettings(value);
                            }),
                        ]),
                      
                      const Divider(),
                      
                      // قائمة إعدادات الإشعارات
                      _buildNotificationSettingItem(
                        title: 'الإشعارات العامة',
                        subtitle: 'إشعارات عامة حول التطبيق والتحديثات',
                        icon: Icons.notifications,
                        color: Colors.teal,
                        settingKey: 'general'),
                      
                      _buildNotificationSettingItem(
                        title: 'عقارات جديدة',
                        subtitle: 'إشعارات حول العقارات الجديدة المضافة',
                        icon: Icons.home,
                        color: Colors.green,
                        settingKey: 'newEstate'),
                      
                      _buildNotificationSettingItem(
                        title: 'تحديثات العقارات',
                        subtitle: 'إشعارات حول تحديثات العقارات التي تتابعها',
                        icon: Icons.update,
                        color: Colors.blue,
                        settingKey: 'estateUpdate'),
                      
                      _buildNotificationSettingItem(
                        title: 'الرسائل الجديدة',
                        subtitle: 'إشعارات حول الرسائل الجديدة الواردة',
                        icon: Icons.message,
                        color: Colors.orange,
                        settingKey: 'newMessage'),
                      
                      _buildNotificationSettingItem(
                        title: 'التحقق من الحساب',
                        subtitle: 'إشعارات حول حالة التحقق من حسابك',
                        icon: Icons.verified_user,
                        color: Colors.purple,
                        settingKey: 'accountVerification'),
                      
                      _buildNotificationSettingItem(
                        title: 'العروض الخاصة',
                        subtitle: 'إشعارات حول العروض والخصومات الخاصة',
                        icon: Icons.local_offer,
                        color: Colors.red,
                        settingKey: 'specialOffer'),
                      
                      _buildNotificationSettingItem(
                        title: 'التذكيرات',
                        subtitle: 'إشعارات تذكير حول الإعلانات والمواعيد',
                        icon: Icons.alarm,
                        color: Colors.amber,
                        settingKey: 'reminder'),
                      
                      _buildNotificationSettingItem(
                        title: 'إشعارات النظام',
                        subtitle: 'إشعارات مهمة حول النظام والأمان',
                        icon: Icons.system_update,
                        color: Colors.grey,
                        settingKey: 'system'),
                    ])));
  }
  
  /// بناء عنصر إعداد الإشعار
  Widget _buildNotificationSettingItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required String settingKey,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // أيقونة الإعداد
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle),
            child: Icon(
              icon,
              color: color,
              size: 20)),
          
          const SizedBox(width: 16),
          
          // تفاصيل الإعداد
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600)),
              ])),
          
          // مفتاح التبديل
          Switch(
            value: _settings[settingKey] ?? true,
            onChanged: (value) {
              _updateSetting(settingKey, value);
            }),
        ]));
  }
}
