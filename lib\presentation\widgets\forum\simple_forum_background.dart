import 'package:flutter/material.dart';

/// خلفية بسيطة للمنتدى مع تدرج لوني
class SimpleForumBackground extends StatelessWidget {
  /// لون الخلفية الأساسي
  final Color primaryColor;

  /// لون الخلفية الثانوي
  final Color secondaryColor;

  const SimpleForumBackground({
    super.key,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            primaryColor,
            secondaryColor,
          ])),
      child: Stack(
        children: [
          // تأثير التدرج الإضافي
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.topRight,
                radius: 1.5,
                colors: [
                  Colors.white.withOpacity(0.05),
                  Colors.transparent,
                ]))),

          // أشكال هندسية ثابتة
          Positioned(
            top: 30,
            right: 40,
            child: _buildStaticShape(
              Icons.circle_outlined,
              size: 20,
              opacity: 0.1)),
          Positioned(
            top: 80,
            left: 60,
            child: _buildStaticShape(
              Icons.square_outlined,
              size: 16,
              opacity: 0.08)),
          Positioned(
            top: 120,
            right: 80,
            child: _buildStaticText(
              '!',
              size: 18,
              opacity: 0.12)),
          Positioned(
            top: 50,
            left: 120,
            child: _buildStaticText(
              '؟',
              size: 16,
              opacity: 0.1)),
          Positioned(
            top: 100,
            right: 120,
            child: _buildStaticText(
              '،',
              size: 14,
              opacity: 0.08)),
          Positioned(
            top: 140,
            left: 40,
            child: _buildStaticText(
              '؛',
              size: 15,
              opacity: 0.09)),
        ]));
  }

  Widget _buildStaticShape(IconData icon, {required double size, required double opacity}) {
    return Icon(
      icon,
      size: size,
      color: Colors.white.withOpacity(opacity));
  }

  Widget _buildStaticText(String text, {required double size, required double opacity}) {
    return Text(
      text,
      style: TextStyle(
        fontSize: size,
        color: Colors.white.withOpacity(opacity),
        fontWeight: FontWeight.bold));
  }
}
