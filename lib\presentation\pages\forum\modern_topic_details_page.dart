import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/post_model.dart';
import '../../../domain/models/forum/topic_model.dart';
import '../../../domain/models/forum/poll_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/empty_view.dart';
import '../../widgets/forum/modern_post_card.dart';
import '../../widgets/forum/poll_widget.dart';
import '../forum/create_edit_poll_page.dart';

/// صفحة تفاصيل الموضوع الحديثة
class ModernTopicDetailsPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/topic';

  /// معرف الموضوع
  final String topicId;

  const ModernTopicDetailsPage({super.key, required this.topicId});

  @override
  State<ModernTopicDetailsPage> createState() => _ModernTopicDetailsPageState();
}

class _ModernTopicDetailsPageState extends State<ModernTopicDetailsPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _replyController = TextEditingController();
  bool _isReplying = false;
  bool _isBookmarked = false;
  bool _isFollowing = false;
  bool _isLiked = false;
  bool _showReplyInput = false;
  bool _showAppBar = true;
  double _headerHeight = 0;
  String? _replyToUserName;

  @override
  void initState() {
    super.initState();

    // تهيئة timeago بالعربية
    timeago.setLocaleMessages('ar', timeago.ArMessages());

    // إضافة مستمع للتمرير لتحميل المزيد من المشاركات
    _scrollController.addListener(_scrollListener);

    // جلب البيانات عند تحميل الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _replyController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // جلب تفاصيل الموضوع
    forumProvider.fetchTopic(widget.topicId);

    // جلب المشاركات
    forumProvider.fetchPosts(widget.topicId, refresh: true);

    // التحقق مما إذا كان المستخدم يتابع الموضوع
    if (authProvider.isLoggedIn) {
      _isFollowing = await forumProvider.isFollowingTopic(
        widget.topicId,
        authProvider.user!.uid);

      // التحقق مما إذا كان المستخدم قد أضاف إشارة مرجعية للموضوع
      _isBookmarked = await forumProvider.isTopicBookmarked(
        widget.topicId,
        authProvider.user!.uid);

      // التحقق مما إذا كان المستخدم قد أعجب بالموضوع
      _isLiked = await forumProvider.isTopicLiked(
        widget.topicId,
        authProvider.user!.uid);

      setState(() {});
    }
  }

  /// مستمع التمرير لتحميل المزيد من المشاركات
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      if (forumProvider.postsState != LoadingState.loading &&
          forumProvider.hasMorePosts) {
        forumProvider.fetchPosts(widget.topicId);
      }
    }

    // إظهار/إخفاء شريط التطبيق عند التمرير
    setState(() {
      _showAppBar = _scrollController.position.pixels <= 100;
    });
  }

  /// إرسال رد
  Future<void> _sendReply() async {
    if (_replyController.text.trim().isEmpty) return;

    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول للرد على الموضوع')));
      return;
    }

    setState(() {
      _isReplying = true;
    });

    try {
      final post = PostModel(
        id: '',
        topicId: widget.topicId,
        topicTitle: forumProvider.currentTopic?.title ?? '',
        categoryId: forumProvider.currentTopic?.categoryId ?? '',
        categoryName: forumProvider.currentTopic?.categoryName ?? '',
        userId: authProvider.user!.uid,
        userName: authProvider.user!.displayName ?? 'مستخدم',
        userImage: authProvider.user!.photoURL,
        content: _replyController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());

      await forumProvider.createPost(post);

      _replyController.clear();
      setState(() {
        _showReplyInput = false;
        _replyToUserName = null;
      });

      // تمرير للأسفل لرؤية الرد الجديد
      await Future.delayed(const Duration(milliseconds: 300));
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('حدث خطأ أثناء إرسال الرد')));
    } finally {
      setState(() {
        _isReplying = false;
      });
    }
  }

  /// متابعة الموضوع
  Future<void> _toggleFollow() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول لمتابعة الموضوع')));
      return;
    }

    setState(() {
      _isFollowing = !_isFollowing;
    });

    try {
      if (_isFollowing) {
        await forumProvider.followTopic(widget.topicId, authProvider.user!.uid);
      } else {
        await forumProvider.unfollowTopic(
            widget.topicId, authProvider.user!.uid);
      }
    } catch (e) {
      setState(() {
        _isFollowing = !_isFollowing;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('حدث خطأ أثناء تغيير حالة المتابعة')));
    }
  }

  /// إضافة إشارة مرجعية للموضوع
  Future<void> _toggleBookmark() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول لإضافة إشارة مرجعية')));
      return;
    }

    setState(() {
      _isBookmarked = !_isBookmarked;
    });

    try {
      if (_isBookmarked) {
        await forumProvider.bookmarkTopic(
            widget.topicId, authProvider.user!.uid);
      } else {
        await forumProvider.unbookmarkTopic(
            widget.topicId, authProvider.user!.uid);
      }
    } catch (e) {
      setState(() {
        _isBookmarked = !_isBookmarked;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('حدث خطأ أثناء تغيير حالة الإشارة المرجعية')));
    }
  }

  /// الإعجاب بالموضوع
  Future<void> _toggleLike() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول للإعجاب بالموضوع')));
      return;
    }

    setState(() {
      _isLiked = !_isLiked;
    });

    try {
      if (_isLiked) {
        await forumProvider.likeTopic(widget.topicId, authProvider.user!.uid);
      } else {
        await forumProvider.unlikeTopic(widget.topicId, authProvider.user!.uid);
      }
    } catch (e) {
      setState(() {
        _isLiked = !_isLiked;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('حدث خطأ أثناء تغيير حالة الإعجاب')));
    }
  }

  /// مشاركة الموضوع
  Future<void> _shareTopic() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final topic = forumProvider.currentTopic;

    if (topic == null) return;

    // مشاركة الموضوع
    await Share.share(
      'شاهد هذا الموضوع: ${topic.title}\n\nتطبيق Krea',
      subject: topic.title);

    // تسجيل المشاركة في Firebase إذا كان المستخدم مسجل الدخول
    if (authProvider.isLoggedIn) {
      await forumProvider.shareTopic(widget.topicId, authProvider.user!.uid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<ForumProvider>(
        builder: (context, forumProvider, child) {
          if (forumProvider.topicState == LoadingState.loading) {
            return const Center(child: LoadingIndicator());
          } else if (forumProvider.topicState == LoadingState.error) {
            return ErrorView(
              message: 'حدث خطأ في تحميل الموضوع',
              onRetry: () => forumProvider.fetchTopic(widget.topicId));
          } else if (forumProvider.topicState == LoadingState.empty ||
              forumProvider.currentTopic == null) {
            return const EmptyView(
              message: 'الموضوع غير موجود',
              icon: Icons.forum_outlined);
          }

          final topic = forumProvider.currentTopic!;

          return Stack(
            children: [
              // محتوى الصفحة
              CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // رأس الصفحة
                  _buildSliverAppBar(topic),

                  // محتوى الموضوع
                  SliverToBoxAdapter(
                    child: _buildTopicContent(topic)),

                  // استطلاعات الرأي
                  if (topic.polls != null && topic.polls!.isNotEmpty)
                    SliverToBoxAdapter(
                      child: _buildPolls(topic)),

                  // قائمة المشاركات
                  _buildPostsList(forumProvider),
                ]),

              // شريط التطبيق المتحرك
              AnimatedPositioned(
                duration: const Duration(milliseconds: 300),
                top: _showAppBar ? 0 : -60,
                left: 0,
                right: 0,
                child: _buildFloatingAppBar(topic)),

              // حقل الرد
              if (_showReplyInput)
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: _buildReplyInput()),

              // زر الرد العائم
              if (!_showReplyInput)
                Positioned(
                  bottom: 16,
                  right: 16,
                  child: FloatingActionButton(
                    heroTag: "topic_details_fab",
                    onPressed: () {
                      setState(() {
                        _showReplyInput = true;
                        _replyToUserName = null;
                      });
                    },
                    backgroundColor: AppColors.primary,
                    child: const Icon(Icons.reply, color: Colors.white))),
            ]);
        }));
  }

  /// بناء شريط التطبيق المتحرك
  Widget _buildFloatingAppBar(TopicModel topic) {
    return SafeArea(
      child: Container(
        height: 60,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2)),
          ]),
        child: Row(
          children: [
            // زر الرجوع
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context)),

            // عنوان الموضوع
            Expanded(
              child: Text(
                topic.title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16),
                maxLines: 1,
                overflow: TextOverflow.ellipsis)),

            // أزرار الإجراءات
            IconButton(
              icon: Icon(_isBookmarked ? Icons.bookmark : Icons.bookmark_border),
              color: _isBookmarked ? AppColors.primary : null,
              onPressed: _toggleBookmark),
            IconButton(
              icon: Icon(_isFollowing ? Icons.notifications_active : Icons.notifications_none),
              color: _isFollowing ? AppColors.primary : null,
              onPressed: _toggleFollow),
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _shareTopic),
          ])));
  }

  /// بناء رأس الصفحة
  Widget _buildSliverAppBar(TopicModel topic) {
    return SliverAppBar(
      expandedHeight: 300,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      elevation: 0,
      automaticallyImplyLeading: false,
      flexibleSpace: LayoutBuilder(
        builder: (context, constraints) {
          _headerHeight = constraints.biggest.height;
          return FlexibleSpaceBar(
            background: Stack(
              children: [
                // صورة الخلفية
                if (topic.images != null && topic.images!.isNotEmpty)
                  Positioned.fill(
                    child: CachedNetworkImage(
                      imageUrl: topic.images![0],
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: AppColors.primary),
                      errorWidget: (context, url, error) => Container(
                        color: AppColors.primary)))
                else
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            AppColors.primary,
                            AppColors.primary.withOpacity(0.8),
                          ])))),

                // طبقة التعتيم
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.3),
                          Colors.black.withOpacity(0.7),
                        ])))),

                // معلومات الموضوع
                Positioned(
                  bottom: 16,
                  left: 16,
                  right: 16,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // الفئة
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(20)),
                        child: Text(
                          topic.categoryName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold))),
                      const SizedBox(height: 8),

                      // العنوان
                      Text(
                        topic.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          height: 1.3)),
                      const SizedBox(height: 16),

                      // معلومات الكاتب والتاريخ
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 20,
                            backgroundColor: Colors.white.withOpacity(0.2),
                            backgroundImage: topic.userImage != null
                                ? NetworkImage(topic.userImage!)
                                : null,
                            child: topic.userImage == null
                                ? const Icon(Icons.person, color: Colors.white)
                                : null),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  topic.userName,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold)),
                                const SizedBox(height: 2),
                                Text(
                                  timeago.format(topic.createdAt, locale: 'ar'),
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.8),
                                    fontSize: 12)),
                              ])),
                        ]),
                    ])),

                // زر الرجوع
                Positioned(
                  top: MediaQuery.of(context).padding.top + 8,
                  left: 8,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      shape: BoxShape.circle),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.pop(context)))),
              ]));
        }));
  }

  /// بناء محتوى الموضوع
  Widget _buildTopicContent(TopicModel topic) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // محتوى الموضوع
          Text(
            topic.content,
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
              color: Colors.black87)),

          // صور الموضوع (إذا كانت موجودة وأكثر من صورة واحدة)
          if (topic.images != null && topic.images!.length > 1) ...[
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: topic.images!.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: InkWell(
                      onTap: () {
                        // عرض الصورة بشكل كامل
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => Scaffold(
                              appBar: AppBar(
                                backgroundColor: Colors.black,
                                iconTheme: const IconThemeData(color: Colors.white)),
                              backgroundColor: Colors.black,
                              body: Center(
                                child: InteractiveViewer(
                                  child: CachedNetworkImage(
                                    imageUrl: topic.images![index],
                                    placeholder: (context, url) => const Center(
                                      child: CircularProgressIndicator(
                                        color: Colors.white)),
                                    errorWidget: (context, url, error) => const Icon(
                                      Icons.error,
                                      color: Colors.white)))))));
                      },
                      child: Hero(
                        tag: 'topic_image_${topic.images![index]}',
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: CachedNetworkImage(
                            imageUrl: topic.images![index],
                            width: 200,
                            height: 200,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: Colors.grey.shade200,
                              child: const Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: AppColors.primary))),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey.shade200,
                              child: const Icon(Icons.error, color: Colors.red)))))));
                })),
          ],

          // الوسوم
          if (topic.tags != null && topic.tags!.isNotEmpty) ...[
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ...topic.tags!.map((tag) => Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.blue.shade100,
                      width: 1)),
                  child: Text(
                    tag,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade700)))),
              ]),
          ],

          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 8),

          // إحصائيات وأزرار التفاعل
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // الإحصائيات
              Row(
                children: [
                  _buildStatItem(Icons.remove_red_eye_outlined, topic.viewsCount.toString()),
                  const SizedBox(width: 16),
                  _buildStatItem(Icons.forum_outlined, topic.repliesCount.toString()),
                  const SizedBox(width: 16),
                  _buildStatItem(Icons.favorite_outline, topic.likesCount.toString()),
                ]),

              // أزرار التفاعل
              Row(
                children: [
                  IconButton(
                    icon: Icon(
                      _isLiked ? Icons.favorite : Icons.favorite_border,
                      color: _isLiked ? Colors.red : Colors.grey.shade600),
                    onPressed: _toggleLike),
                  IconButton(
                    icon: Icon(
                      _isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                      color: _isBookmarked ? AppColors.primary : Colors.grey.shade600),
                    onPressed: _toggleBookmark),
                  IconButton(
                    icon: Icon(
                      Icons.share_outlined,
                      color: Colors.grey.shade600),
                    onPressed: _shareTopic),
                ]),
            ]),
        ]));
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(IconData icon, String count) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(
          count,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800)),
      ]);
  }

  /// بناء قائمة المشاركات
  Widget _buildPostsList(ForumProvider forumProvider) {
    if (forumProvider.postsState == LoadingState.loading &&
        forumProvider.posts.isEmpty) {
      return const SliverFillRemaining(
        child: Center(child: LoadingIndicator()));
    } else if (forumProvider.postsState == LoadingState.error) {
      return SliverFillRemaining(
        child: ErrorView(
          message: 'حدث خطأ في تحميل المشاركات',
          onRetry: () => forumProvider.fetchPosts(widget.topicId, refresh: true)));
    } else if (forumProvider.postsState == LoadingState.empty) {
      return const SliverFillRemaining(
        child: EmptyView(
          message: 'لا توجد ردود على هذا الموضوع',
          icon: Icons.forum_outlined));
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == 0) {
            // عنوان قسم الردود
            return Container(
              margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.1),
                  width: 1)),
              child: Row(
                children: [
                  Icon(
                    Icons.forum_outlined,
                    color: AppColors.primary,
                    size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'الردود (${forumProvider.posts.length})',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: AppColors.primary)),
                ]));
          }

          final postIndex = index - 1;

          if (postIndex == forumProvider.posts.length) {
            // مؤشر تحميل المزيد
            if (forumProvider.hasMorePosts) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: LoadingIndicator()));
            } else {
              return const SizedBox.shrink();
            }
          }

          final post = forumProvider.posts[postIndex];

          return AnimationConfiguration.staggeredList(
            position: postIndex,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: ModernPostCard(
                  post: post,
                  onReplyTap: () {
                    setState(() {
                      _showReplyInput = true;
                      _replyToUserName = post.userName;
                      _replyController.text = 'رداً على @${post.userName}: ';
                    });
                  },
                  onLikeTap: () {
                    // الإعجاب بالمشاركة
                  },
                  isCurrentUser: post.userId == Provider.of<AuthProvider>(context, listen: false).user?.uid))));
        },
        childCount: forumProvider.posts.length + 2, // +1 للعنوان و+1 لمؤشر التحميل
      ));
  }

  /// بناء حقل الرد
  Widget _buildReplyInput() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -3)),
        ]),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الرد (إذا كان رداً على مستخدم معين)
          if (_replyToUserName != null) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8)),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'الرد على: $_replyToUserName',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 12)),
                  const SizedBox(width: 8),
                  InkWell(
                    onTap: () {
                      setState(() {
                        _replyToUserName = null;
                        _replyController.text = '';
                      });
                    },
                    child: Icon(
                      Icons.close,
                      size: 16,
                      color: AppColors.primary)),
                ])),
            const SizedBox(height: 8),
          ],

          // حقل الرد
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // زر إغلاق حقل الرد
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  shape: BoxShape.circle),
                child: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _showReplyInput = false;
                      _replyToUserName = null;
                      _replyController.text = '';
                    });
                  },
                  color: Colors.grey.shade700)),
              const SizedBox(width: 8),

              // حقل النص
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: Colors.grey.shade300,
                      width: 1)),
                  child: TextField(
                    controller: _replyController,
                    decoration: InputDecoration(
                      hintText: 'اكتب رداً...',
                      hintStyle: TextStyle(
                        color: Colors.grey.shade500),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      suffixIcon: IconButton(
                        icon: Icon(
                          Icons.photo_camera,
                          color: Colors.grey.shade600),
                        onPressed: () {
                          // إضافة صورة للرد
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('سيتم دعم إضافة الصور قريباً')));
                        })),
                    maxLines: 4,
                    minLines: 1,
                    textInputAction: TextInputAction.newline,
                    style: const TextStyle(
                      fontSize: 15)))),
              const SizedBox(width: 8),

              // زر الإرسال
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary,
                      AppColors.primary.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight),
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 3)),
                  ]),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _isReplying ? null : _sendReply,
                    borderRadius: BorderRadius.circular(30),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      child: _isReplying
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2))
                          : const Icon(
                              Icons.send,
                              color: Colors.white,
                              size: 24))))),
            ]),
        ]));
  }

  /// بناء استطلاعات الرأي
  Widget _buildPolls(TopicModel topic) {
    if (topic.polls == null || topic.polls!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'استطلاعات الرأي',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          ...topic.polls!.map((poll) => Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: PollWidget(
              poll: poll,
              topicId: topic.id,
              onPollUpdated: (updatedPoll) {
                _updatePoll(topic, updatedPoll);
              }))),

          // زر إضافة استطلاع رأي
          if (_canCreatePoll(topic))
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Center(
                child: ElevatedButton.icon(
                  onPressed: () => _createPoll(topic),
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة استطلاع رأي جديد'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8))))),
        ]));
  }

  /// التحقق مما إذا كان المستخدم يمكنه إنشاء استطلاع رأي
  bool _canCreatePoll(TopicModel topic) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // التحقق مما إذا كان المستخدم مسجل الدخول وهو صاحب الموضوع
    if (!authProvider.isLoggedIn || authProvider.user!.uid != topic.userId) {
      return false;
    }

    return true;
  }

  /// إنشاء استطلاع رأي
  Future<void> _createPoll(TopicModel topic) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول لإنشاء استطلاع رأي')));
      return;
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateEditPollPage(
          topicId: topic.id)));

    if (result != null && mounted) {
      // تحديث الموضوع بعد إنشاء الاستطلاع
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      forumProvider.fetchTopic(topic.id);
    }
  }

  /// تحديث استطلاع رأي
  void _updatePoll(TopicModel topic, PollModel updatedPoll) {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    if (topic.polls != null) {
      final polls = List<PollModel>.from(topic.polls!);
      final pollIndex = polls.indexWhere((poll) => poll.id == updatedPoll.id);

      if (pollIndex != -1) {
        polls[pollIndex] = updatedPoll;

        final updatedTopic = topic.copyWith(polls: polls);
        forumProvider.updateCurrentTopic(updatedTopic);
      }
    }
  }
}
