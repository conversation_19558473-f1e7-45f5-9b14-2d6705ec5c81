import '../entities/market_analysis.dart';
import '../entities/property_valuation.dart';

/// واجهة مستودع تحليلات السوق وتقييم العقارات
abstract class MarketRepository {
  /// الحصول على تحليل السوق
  Future<MarketAnalysis> getMarketAnalysis(
    String location,
    String propertyType);
  
  /// الحصول على تحليل السوق بواسطة المعرف
  Future<MarketAnalysis?> getMarketAnalysisById(String analysisId);
  
  /// الحصول على تحليلات السوق للمنطقة
  Future<List<MarketAnalysis>> getMarketAnalysesForLocation(String location);
  
  /// الحصول على تحليلات السوق لنوع العقار
  Future<List<MarketAnalysis>> getMarketAnalysesForPropertyType(String propertyType);
  
  /// الحصول على تحليلات السوق الأخيرة
  Future<List<MarketAnalysis>> getLatestMarketAnalyses({int limit = 10});
  
  /// إنشاء تقييم عقار
  Future<String> createPropertyValuation(PropertyValuation valuation);
  
  /// تحديث تقييم عقار
  Future<void> updatePropertyValuation(PropertyValuation valuation);
  
  /// حذف تقييم عقار
  Future<void> deletePropertyValuation(String valuationId);
  
  /// الحصول على تقييم عقار بواسطة المعرف
  Future<PropertyValuation?> getPropertyValuationById(String valuationId);
  
  /// الحصول على تقييمات العقارات للمستخدم
  Future<List<PropertyValuation>> getUserPropertyValuations(String userId);
  
  /// الحصول على تقييم عقار بواسطة معرف العقار
  Future<PropertyValuation?> getPropertyValuationByEstateId(String estateId);
  
  /// تقييم عقار تلقائياً
  Future<PropertyValuation> autoValuateProperty({
    required String propertyType,
    required String location,
    required double area,
    int? numberOfRooms,
    int? numberOfBathrooms,
    int? buildingAge,
    String? finishingType,
    int? floorNumber,
    String? facingDirection,
    String? viewType,
    List<String>? features,
  });
  
  /// التحقق من تقييم عقار
  Future<void> verifyPropertyValuation(
    String valuationId,
    String verifierId,
    String verifierName);
  
  /// الحصول على متوسط أسعار المتر المربع
  Future<Map<String, double>> getAveragePricesPerSquareMeter(
    List<String> locations,
    List<String> propertyTypes);
  
  /// الحصول على اتجاهات الأسعار
  Future<Map<String, List<Map<String, dynamic>>>> getPriceTrends(
    String location,
    String propertyType,
    int months);
  
  /// الحصول على مؤشرات السوق
  Future<Map<String, dynamic>> getMarketIndicators();
  
  /// الحصول على توقعات الأسعار المستقبلية
  Future<Map<String, double>> getPriceForecast(
    String location,
    String propertyType,
    int months);
  
  /// الحصول على العقارات المشابهة
  Future<List<Map<String, dynamic>>> getSimilarProperties({
    required String propertyType,
    required String location,
    required double area,
    int? numberOfRooms,
    double? maxDistance,
    int? limit,
  });
  
  /// الحصول على تقرير تحليل السوق
  Future<Map<String, dynamic>> getMarketAnalysisReport(
    String location,
    String propertyType);
  
  /// الحصول على تقرير تقييم العقار
  Future<Map<String, dynamic>> getPropertyValuationReport(String valuationId);
}
