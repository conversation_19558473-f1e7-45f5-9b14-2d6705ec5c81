import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../domain/entities/estate.dart';
import '../../data/models/estate_model.dart';

/// خدمة محسنة لاستعلامات قاعدة البيانات مع تحسينات الأداء
class EnhancedDatabaseService {
  static final EnhancedDatabaseService _instance = EnhancedDatabaseService._internal();

  factory EnhancedDatabaseService() => _instance;

  EnhancedDatabaseService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // إعدادات التخزين المؤقت للاستعلامات
  static const Duration _queryCacheDuration = Duration(minutes: 5);
  final Map<String, QueryCacheEntry> _queryCache = {};

  // إعدادات تحسين الأداء
  static const int _batchSize = 500;
  static const Duration _connectionTimeout = Duration(seconds: 30);

  // مراقبة الأداء
  final Map<String, List<Duration>> _queryPerformance = {};
  final Map<String, int> _queryCount = {};

  // تجميع الاستعلامات
  final Map<String, List<Completer<dynamic>>> _pendingQueries = {};
  Timer? _batchTimer;

  /// الحصول على العقارات مع فلترة محسنة
  Future<Map<String, dynamic>> getOptimizedEstates({
    int limit = 10,
    String? lastDocumentId,
    int page = 1,
    int pageSize = 10,
    String sortBy = "createdAt",
    bool sortAscending = false,
    Map<String, dynamic>? filters,
    String? searchQuery,
    bool useCache = true,
  }) async {
    // إنشاء مفتاح فريد للاستعلام
    final queryKey = _generateQueryKey(
      limit: limit,
      lastDocumentId: lastDocumentId,
      page: page,
      pageSize: pageSize,
      sortBy: sortBy,
      sortAscending: sortAscending,
      filters: filters,
      searchQuery: searchQuery);

    // فحص التخزين المؤقت أولاً
    if (useCache && _queryCache.containsKey(queryKey)) {
      final cachedEntry = _queryCache[queryKey]!;
      if (!cachedEntry.isExpired) {
        return cachedEntry.data;
      } else {
        _queryCache.remove(queryKey);
      }
    }

    try {
      // بناء الاستعلام المحسن
      Query query = _buildOptimizedQuery(
        sortBy: sortBy,
        sortAscending: sortAscending,
        filters: filters,
        searchQuery: searchQuery);

      // تطبيق الترقيم
      query = query.limit(pageSize);

      if (lastDocumentId != null && lastDocumentId.isNotEmpty) {
        final lastDoc = await _firestore.collection('estates').doc(lastDocumentId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      // تنفيذ الاستعلام
      final querySnapshot = await query.get();

      // تحويل النتائج
      final estates = <Estate>[];
      for (final doc in querySnapshot.docs) {
        try {
          final estateModel = EstateModel.fromJson({
            'id': doc.id,
            ...doc.data() as Map<String, dynamic>,
          });

          final estate = _convertEstateModelToEstate(estateModel);
          if (estate != null) {
            estates.add(estate);
          }
        } catch (e) {
          print('خطأ في تحويل العقار ${doc.id}: $e');
          continue;
        }
      }

      // تحديد ما إذا كان هناك المزيد من البيانات
      final hasMore = querySnapshot.docs.length == pageSize;
      final newLastDocumentId = querySnapshot.docs.isNotEmpty
          ? querySnapshot.docs.last.id
          : null;

      final result = {
        'estates': estates,
        'hasMore': hasMore,
        'lastDocumentId': newLastDocumentId,
        'totalCount': estates.length,
      };

      // حفظ في التخزين المؤقت
      if (useCache) {
        _queryCache[queryKey] = QueryCacheEntry(
          data: result,
          timestamp: DateTime.now());
      }

      return result;
    } catch (e) {
      throw Exception('خطأ في جلب العقارات: ${e.toString()}');
    }
  }

  /// بناء استعلام محسن مع فهارس مناسبة
  Query _buildOptimizedQuery({
    required String sortBy,
    required bool sortAscending,
    Map<String, dynamic>? filters,
    String? searchQuery,
  }) {
    Query query = _firestore.collection('estates');

    // فلتر الإعلانات المدفوعة فقط (في الإنتاج)
    query = query.where('isPaymentVerified', isEqualTo: true);

    // تطبيق الفلاتر المحسنة
    if (filters != null && filters.isNotEmpty) {
      query = _applyOptimizedFilters(query, filters);
    }

    // تطبيق البحث النصي (إذا كان متوفراً)
    if (searchQuery != null && searchQuery.trim().isNotEmpty) {
      query = _applyTextSearch(query, searchQuery.trim());
    }

    // تطبيق الترتيب
    query = query.orderBy(sortBy, descending: !sortAscending);

    return query;
  }

  /// تطبيق فلاتر محسنة
  Query _applyOptimizedFilters(Query query, Map<String, dynamic> filters) {
    // فلتر التصنيف الرئيسي
    if (filters.containsKey('mainCategory') && filters['mainCategory'] != null) {
      query = query.where('mainCategory', isEqualTo: filters['mainCategory']);
    }

    // فلتر التصنيف الفرعي
    if (filters.containsKey('subCategory') && filters['subCategory'] != null) {
      query = query.where('subCategory', arrayContains: filters['subCategory']);
    }

    // فلتر نطاق السعر
    if (filters.containsKey('minPrice') && filters['minPrice'] != null) {
      query = query.where('price', isGreaterThanOrEqualTo: filters['minPrice']);
    }
    if (filters.containsKey('maxPrice') && filters['maxPrice'] != null) {
      query = query.where('price', isLessThanOrEqualTo: filters['maxPrice']);
    }

    // فلتر الموقع
    if (filters.containsKey('governorate') && filters['governorate'] != null) {
      query = query.where('governorate', isEqualTo: filters['governorate']);
    }

    // فلتر نوع العقار
    if (filters.containsKey('propertyType') && filters['propertyType'] != null) {
      query = query.where('propertyType', isEqualTo: filters['propertyType']);
    }

    // فلتر عدد الغرف
    if (filters.containsKey('minRooms') && filters['minRooms'] != null) {
      query = query.where('numberOfRooms', isGreaterThanOrEqualTo: filters['minRooms']);
    }
    if (filters.containsKey('maxRooms') && filters['maxRooms'] != null) {
      query = query.where('numberOfRooms', isLessThanOrEqualTo: filters['maxRooms']);
    }

    // فلتر المساحة
    if (filters.containsKey('minArea') && filters['minArea'] != null) {
      query = query.where('area', isGreaterThanOrEqualTo: filters['minArea']);
    }
    if (filters.containsKey('maxArea') && filters['maxArea'] != null) {
      query = query.where('area', isLessThanOrEqualTo: filters['maxArea']);
    }

    return query;
  }

  /// تطبيق البحث النصي
  Query _applyTextSearch(Query query, String searchQuery) {
    // البحث في العنوان والوصف والموقع
    // ملاحظة: Firestore لا يدعم البحث النصي الكامل، لذا نستخدم array-contains للكلمات المفتاحية
    final keywords = searchQuery.toLowerCase().split(' ');

    if (keywords.isNotEmpty) {
      // البحث في الكلمات المفتاحية المحفوظة
      query = query.where('searchKeywords', arrayContainsAny: keywords);
    }

    return query;
  }

  /// تحويل EstateModel إلى Estate
  Estate? _convertEstateModelToEstate(EstateModel model) {
    try {
      return Estate(
        id: model.id,
        title: model.title,
        description: model.description,
        price: model.price,
        location: model.location,
        photoUrls: model.photoUrls,
        isFeatured: model.isFeatured,
        planType: model.planType,
        startDate: model.startDate,
        endDate: model.endDate,
        createdAt: model.createdAt,
        mainCategory: model.mainCategory,
        subCategory: model.subCategory,
        postedByUserType: model.postedByUserType,
        hidePhone: model.hidePhone,
        extraPhones: model.extraPhones,
        shareLocation: model.shareLocation,
        lat: model.lat,
        lng: model.lng,
        hasCentralAC: model.hasCentralAC,
        hasSecurity: model.hasSecurity,
        allowPets: model.allowPets,
        hasElevator: model.hasElevator,
        hasSwimmingPool: model.hasSwimmingPool,
        hasMaidRoom: model.hasMaidRoom,
        hasGarage: model.hasGarage,
        hasBalcony: model.hasBalcony,
        isFullyFurnished: model.isFullyFurnished,
        rebound: model.rebound,
        numberOfRooms: model.numberOfRooms,
        internalLocation: model.internalLocation,
        salon: model.salon,
        area: model.area,
        floorNumber: model.floorNumber,
        numberOfBathrooms: model.numberOfBathrooms,
        buildingAge: model.buildingAge,
        isAvailable: true, // قيمة افتراضية
        isCopied: false, // قيمة افتراضية
        copiedAt: null, // قيمة افتراضية
        isPaidAd: false, // قيمة افتراضية
        isPaymentVerified: model.isPaymentVerified);
    } catch (e) {
      debugPrint('خطأ في تحويل EstateModel إلى Estate: $e');
      return null;
    }
  }

  /// تحسين الاستعلامات بالتجميع
  Future<List<T>> batchQuery<T>({
    required String collection,
    required List<String> documentIds,
    required T Function(Map<String, dynamic>) fromJson,
    Duration? timeout,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      final results = <T>[];

      // تقسيم المعرفات إلى مجموعات صغيرة (Firestore يدعم حتى 10 في whereIn)
      const batchSize = 10;
      for (int i = 0; i < documentIds.length; i += batchSize) {
        final batch = documentIds.skip(i).take(batchSize).toList();

        final query = _firestore
          .collection(collection)
          .where(FieldPath.documentId, whereIn: batch);

        final snapshot = await query.get();

        for (final doc in snapshot.docs) {
          try {
            final data = doc.data();
            data['id'] = doc.id;
            results.add(fromJson(data));
          } catch (e) {
            debugPrint('خطأ في تحويل المستند ${doc.id}: $e');
          }
        }
      }

      _recordQueryPerformance('batchQuery_$collection', stopwatch.elapsed);
      return results;
    } catch (e) {
      debugPrint('خطأ في الاستعلام المجمع: $e');
      return [];
    }
  }

  /// استعلام محسن مع فهرسة
  Future<List<T>> optimizedQuery<T>({
    required String collection,
    required T Function(Map<String, dynamic>) fromJson,
    List<QueryFilter>? filters,
    List<QuerySort>? sorts,
    int? limit,
    DocumentSnapshot? startAfter,
    bool useCache = true,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      // إنشاء مفتاح التخزين المؤقت
      final cacheKey = _generateOptimizedCacheKey(collection, filters, sorts, limit);

      // فحص التخزين المؤقت
      if (useCache && _queryCache.containsKey(cacheKey)) {
        final cachedEntry = _queryCache[cacheKey]!;
        if (!cachedEntry.isExpired) {
          return List<T>.from(cachedEntry.data['results']);
        }
      }

      // بناء الاستعلام
      Query query = _firestore.collection(collection);

      // تطبيق الفلاتر
      if (filters != null) {
        for (final filter in filters) {
          query = _applyFilter(query, filter);
        }
      }

      // تطبيق الترتيب
      if (sorts != null) {
        for (final sort in sorts) {
          query = query.orderBy(sort.field, descending: sort.descending);
        }
      }

      // تطبيق الحد الأقصى
      if (limit != null) {
        query = query.limit(limit);
      }

      // تطبيق نقطة البداية للصفحات
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final snapshot = await query.get();
      final results = <T>[];

      for (final doc in snapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          data['id'] = doc.id;
          results.add(fromJson(data));
        } catch (e) {
          debugPrint('خطأ في تحويل المستند ${doc.id}: $e');
        }
      }

      // حفظ في التخزين المؤقت
      if (useCache) {
        _queryCache[cacheKey] = QueryCacheEntry(
          data: {'results': results},
          timestamp: DateTime.now(),
        );
      }

      _recordQueryPerformance('optimizedQuery_$collection', stopwatch.elapsed);
      return results;
    } catch (e) {
      debugPrint('خطأ في الاستعلام المحسن: $e');
      return [];
    }
  }

  /// تطبيق فلتر على الاستعلام
  Query _applyFilter(Query query, QueryFilter filter) {
    switch (filter.operator) {
      case FilterOperator.isEqualTo:
        return query.where(filter.field, isEqualTo: filter.value);
      case FilterOperator.isNotEqualTo:
        return query.where(filter.field, isNotEqualTo: filter.value);
      case FilterOperator.isLessThan:
        return query.where(filter.field, isLessThan: filter.value);
      case FilterOperator.isLessThanOrEqualTo:
        return query.where(filter.field, isLessThanOrEqualTo: filter.value);
      case FilterOperator.isGreaterThan:
        return query.where(filter.field, isGreaterThan: filter.value);
      case FilterOperator.isGreaterThanOrEqualTo:
        return query.where(filter.field, isGreaterThanOrEqualTo: filter.value);
      case FilterOperator.arrayContains:
        return query.where(filter.field, arrayContains: filter.value);
      case FilterOperator.arrayContainsAny:
        return query.where(filter.field, arrayContainsAny: filter.value);
      case FilterOperator.whereIn:
        return query.where(filter.field, whereIn: filter.value);
      case FilterOperator.whereNotIn:
        return query.where(filter.field, whereNotIn: filter.value);
      case FilterOperator.isNull:
        return query.where(filter.field, isNull: true);
      case FilterOperator.isNotNull:
        return query.where(filter.field, isNull: false);
    }
  }

  /// تسجيل أداء الاستعلام
  void _recordQueryPerformance(String queryType, Duration duration) {
    _queryPerformance.putIfAbsent(queryType, () => []);
    _queryPerformance[queryType]!.add(duration);

    _queryCount[queryType] = (_queryCount[queryType] ?? 0) + 1;

    // الاحتفاظ بآخر 100 قياس فقط
    if (_queryPerformance[queryType]!.length > 100) {
      _queryPerformance[queryType]!.removeAt(0);
    }
  }

  /// الحصول على إحصائيات الأداء
  Map<String, dynamic> getPerformanceStats() {
    final stats = <String, dynamic>{};

    for (final entry in _queryPerformance.entries) {
      final durations = entry.value;
      if (durations.isNotEmpty) {
        final totalMs = durations.fold<int>(0, (sum, d) => sum + d.inMilliseconds);
        final avgMs = totalMs / durations.length;
        final maxMs = durations.map((d) => d.inMilliseconds).reduce((a, b) => a > b ? a : b);
        final minMs = durations.map((d) => d.inMilliseconds).reduce((a, b) => a < b ? a : b);

        stats[entry.key] = {
          'count': _queryCount[entry.key] ?? 0,
          'averageMs': avgMs.round(),
          'maxMs': maxMs,
          'minMs': minMs,
          'totalMs': totalMs,
        };
      }
    }

    return stats;
  }

  /// إنشاء مفتاح التخزين المؤقت المحسن
  String _generateOptimizedCacheKey(
    String collection,
    List<QueryFilter>? filters,
    List<QuerySort>? sorts,
    int? limit,
  ) {
    final parts = [collection];

    if (filters != null) {
      for (final filter in filters) {
        parts.add('${filter.field}_${filter.operator}_${filter.value}');
      }
    }

    if (sorts != null) {
      for (final sort in sorts) {
        parts.add('sort_${sort.field}_${sort.descending}');
      }
    }

    if (limit != null) {
      parts.add('limit_$limit');
    }

    return parts.join('_');
  }

  /// إنشاء مفتاح فريد للاستعلام
  String _generateQueryKey({
    required int limit,
    String? lastDocumentId,
    required int page,
    required int pageSize,
    required String sortBy,
    required bool sortAscending,
    Map<String, dynamic>? filters,
    String? searchQuery,
  }) {
    final parts = [
      'limit:$limit',
      'lastDoc:${lastDocumentId ?? 'null'}',
      'page:$page',
      'pageSize:$pageSize',
      'sortBy:$sortBy',
      'sortAsc:$sortAscending',
      'filters:${filters?.toString() ?? 'null'}',
      'search:${searchQuery ?? 'null'}',
    ];

    return parts.join('|');
  }

  /// مسح التخزين المؤقت للاستعلامات المنتهية الصلاحية
  void clearExpiredQueryCache() {
    final expiredKeys = _queryCache.entries
        .where((entry) => entry.value.isExpired)
        .map((entry) => entry.key)
        .toList();

    for (final key in expiredKeys) {
      _queryCache.remove(key);
    }
  }

  /// مسح جميع التخزين المؤقت للاستعلامات
  void clearAllQueryCache() {
    _queryCache.clear();
  }

  /// الحصول على إحصائيات التخزين المؤقت
  Map<String, dynamic> getQueryCacheStats() {
    final totalEntries = _queryCache.length;
    final expiredEntries = _queryCache.values.where((entry) => entry.isExpired).length;

    return {
      'totalEntries': totalEntries,
      'activeEntries': totalEntries - expiredEntries,
      'expiredEntries': expiredEntries,
    };
  }
}

/// مدخل التخزين المؤقت للاستعلام
class QueryCacheEntry {
  final Map<String, dynamic> data;
  final DateTime timestamp;

  QueryCacheEntry({
    required this.data,
    required this.timestamp,
  });

  bool get isExpired {
    return DateTime.now().difference(timestamp) > EnhancedDatabaseService._queryCacheDuration;
  }
}

/// فلتر الاستعلام
class QueryFilter {
  final String field;
  final FilterOperator operator;
  final dynamic value;

  QueryFilter({
    required this.field,
    required this.operator,
    required this.value,
  });
}

/// عامل الفلتر
enum FilterOperator {
  isEqualTo,
  isNotEqualTo,
  isLessThan,
  isLessThanOrEqualTo,
  isGreaterThan,
  isGreaterThanOrEqualTo,
  arrayContains,
  arrayContainsAny,
  whereIn,
  whereNotIn,
  isNull,
  isNotNull,
}

/// ترتيب الاستعلام
class QuerySort {
  final String field;
  final bool descending;

  QuerySort({
    required this.field,
    this.descending = false,
  });
}
