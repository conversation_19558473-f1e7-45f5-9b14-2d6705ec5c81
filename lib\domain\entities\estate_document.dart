import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// أنواع المستندات
enum DocumentType {
  /// صك ملكية
  deed,
  
  /// رخصة بناء
  buildingPermit,
  
  /// مخطط
  plan,
  
  /// عقد إيجار
  leaseContract,
  
  /// تقرير فني
  technicalReport,
  
  /// شهادة إتمام بناء
  completionCertificate,
  
  /// أخرى
  other,
}

/// نموذج للمستندات والملفات المرفقة بالعقار
class EstateDocument extends Equatable {
  /// معرف المستند
  final String id;
  
  /// معرف العقار
  final String estateId;
  
  /// اسم المستند
  final String name;
  
  /// وصف المستند
  final String? description;
  
  /// نوع المستند
  final DocumentType type;
  
  /// رابط الملف
  final String fileUrl;
  
  /// حجم الملف بالبايت
  final int? fileSize;
  
  /// نوع الملف (pdf, jpg, png, etc.)
  final String? fileType;
  
  /// تاريخ الإضافة
  final DateTime createdAt;
  
  /// تاريخ التحديث
  final DateTime? updatedAt;
  
  /// معرف المستخدم الذي أضاف المستند
  final String uploadedBy;
  
  /// ما إذا كان المستند عام (يمكن للجميع رؤيته)
  final bool isPublic;

  const EstateDocument({
    required this.id,
    required this.estateId,
    required this.name,
    this.description,
    required this.type,
    required this.fileUrl,
    this.fileSize,
    this.fileType,
    required this.createdAt,
    this.updatedAt,
    required this.uploadedBy,
    this.isPublic = false,
  });

  /// إنشاء نسخة معدلة من المستند
  EstateDocument copyWith({
    String? id,
    String? estateId,
    String? name,
    String? description,
    DocumentType? type,
    String? fileUrl,
    int? fileSize,
    String? fileType,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uploadedBy,
    bool? isPublic,
  }) {
    return EstateDocument(
      id: id ?? this.id,
      estateId: estateId ?? this.estateId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      fileUrl: fileUrl ?? this.fileUrl,
      fileSize: fileSize ?? this.fileSize,
      fileType: fileType ?? this.fileType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      isPublic: isPublic ?? this.isPublic);
  }

  /// تحويل المستند إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'estateId': estateId,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'fileUrl': fileUrl,
      'fileSize': fileSize,
      'fileType': fileType,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'uploadedBy': uploadedBy,
      'isPublic': isPublic,
    };
  }

  /// إنشاء مستند من Map
  factory EstateDocument.fromMap(Map<String, dynamic> map) {
    return EstateDocument(
      id: map['id'] ?? '',
      estateId: map['estateId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      type: _getDocumentTypeFromString(map['type'] ?? 'other'),
      fileUrl: map['fileUrl'] ?? '',
      fileSize: map['fileSize'],
      fileType: map['fileType'],
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
      uploadedBy: map['uploadedBy'] ?? '',
      isPublic: map['isPublic'] ?? false);
  }

  /// إنشاء مستند من DocumentSnapshot
  factory EstateDocument.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return EstateDocument.fromMap(data);
  }

  /// الحصول على نوع المستند من النص
  static DocumentType _getDocumentTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'deed':
        return DocumentType.deed;
      case 'buildingPermit':
        return DocumentType.buildingPermit;
      case 'plan':
        return DocumentType.plan;
      case 'leaseContract':
        return DocumentType.leaseContract;
      case 'technicalReport':
        return DocumentType.technicalReport;
      case 'completionCertificate':
        return DocumentType.completionCertificate;
      default:
        return DocumentType.other;
    }
  }

  /// الحصول على اسم نوع المستند بالعربية
  String getDocumentTypeName() {
    switch (type) {
      case DocumentType.deed:
        return 'صك ملكية';
      case DocumentType.buildingPermit:
        return 'رخصة بناء';
      case DocumentType.plan:
        return 'مخطط';
      case DocumentType.leaseContract:
        return 'عقد إيجار';
      case DocumentType.technicalReport:
        return 'تقرير فني';
      case DocumentType.completionCertificate:
        return 'شهادة إتمام بناء';
      case DocumentType.other:
        return 'مستند آخر';
    }
  }

  @override
  List<Object?> get props => [
    id,
    estateId,
    name,
    description,
    type,
    fileUrl,
    fileSize,
    fileType,
    createdAt,
    updatedAt,
    uploadedBy,
    isPublic,
  ];
}
