import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../entities/estate.dart';

/// أنواع الصفحات لتحديد استراتيجية العرض
enum PageType {
  homePage,
  categoryPage,
  searchResults,
  userProfile,
  favorites,
  comparison,
}

/// نوع الإعلان المدفوع مع نقاط الأولوية
enum PremiumAdType {
  vip(1000, 'VIP'),
  pinnedOnHome(800, 'مثبت في الرئيسية'),
  featured(600, 'مميز'),
  kuwaitCornersPin(400, 'مثبت في الأقسام'),
  movingAd(200, 'متحرك'),
  normal(0, 'عادي');

  const PremiumAdType(this.baseScore, this.displayName);
  final int baseScore;
  final String displayName;
}

/// خدمة خوارزمية الإعلانات المميزة المتقدمة
class PremiumAdsAlgorithmService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// إعدادات التوزيع لكل نوع صفحة
  static const Map<PageType, Map<PremiumAdType, double>> distributionRules = {
    PageType.homePage: {
      PremiumAdType.vip: 0.30,
      PremiumAdType.pinnedOnHome: 0.25,
      PremiumAdType.featured: 0.20,
      PremiumAdType.kuwaitCornersPin: 0.15,
      PremiumAdType.normal: 0.10,
    },
    PageType.categoryPage: {
      PremiumAdType.kuwaitCornersPin: 0.40,
      PremiumAdType.featured: 0.30,
      PremiumAdType.vip: 0.20,
      PremiumAdType.normal: 0.10,
    },
    PageType.searchResults: {
      PremiumAdType.featured: 0.35,
      PremiumAdType.vip: 0.30,
      PremiumAdType.kuwaitCornersPin: 0.25,
      PremiumAdType.normal: 0.10,
    },
  };

  /// الحصول على الإعلانات مرتبة حسب الخوارزمية المتقدمة
  Future<List<Estate>> getOptimizedAds({
    required PageType pageType,
    required int limit,
    String? category,
    String? searchQuery,
    Map<String, dynamic>? filters,
  }) async {
    try {
      // جلب جميع الإعلانات المتاحة
      final allAds = await _fetchAvailableAds(
        category: category,
        searchQuery: searchQuery,
        filters: filters);

      // تطبيق خوارزمية التسجيل والترتيب
      final scoredAds = await _scoreAndRankAds(allAds, pageType);

      // تطبيق خوارزمية التوزيع الذكي
      final distributedAds = _applySmartDistribution(
        scoredAds,
        pageType,
        limit);

      // تسجيل الإحصائيات
      await _logAdDisplayStats(distributedAds, pageType);

      return distributedAds;
    } catch (e) {
      print('Error in getOptimizedAds: $e');
      return [];
    }
  }

  /// جلب الإعلانات المتاحة مع الفلاتر
  Future<List<Estate>> _fetchAvailableAds({
    String? category,
    String? searchQuery,
    Map<String, dynamic>? filters,
  }) async {
    Query query = _firestore.collection('estates');

    // فلتر الإعلانات المدفوعة والمتاحة
    query = query.where('isPaymentVerified', isEqualTo: true);

    // فلتر الإعلانات غير المنتهية الصلاحية
    query = query.where('endDate', isGreaterThan: Timestamp.now());

    // فلتر الفئة
    if (category != null && category.isNotEmpty) {
      query = query.where('mainCategory', isEqualTo: category);
    }

    // تطبيق فلاتر إضافية
    if (filters != null) {
      filters.forEach((key, value) {
        if (value != null) {
          query = query.where(key, isEqualTo: value);
        }
      });
    }

    final snapshot = await query.get();

    List<Estate> estates = snapshot.docs
        .map((doc) => _mapToEstate(doc.data() as Map<String, dynamic>, doc.id))
        .toList();

    // فلتر البحث النصي (إذا لزم الأمر)
    if (searchQuery != null && searchQuery.isNotEmpty) {
      estates = _applyTextSearch(estates, searchQuery);
    }

    return estates;
  }

  /// تطبيق البحث النصي
  List<Estate> _applyTextSearch(List<Estate> estates, String query) {
    final searchTerms = query.toLowerCase().split(' ');

    return estates.where((estate) {
      final searchableText = '${estate.title} ${estate.description} ${estate.location}'.toLowerCase();
      return searchTerms.every((term) => searchableText.contains(term));
    }).toList();
  }

  /// تسجيل ونقاط الإعلانات
  Future<List<EstateWithScore>> _scoreAndRankAds(
    List<Estate> estates,
    PageType pageType) async {
    final List<EstateWithScore> scoredEstates = [];

    for (final estate in estates) {
      final score = await _calculateAdScore(estate, pageType);
      scoredEstates.add(EstateWithScore(estate, score));
    }

    // ترتيب حسب النقاط (الأعلى أولاً)
    scoredEstates.sort((a, b) => b.score.compareTo(a.score));

    return scoredEstates;
  }

  /// حساب نقاط الإعلان
  Future<double> _calculateAdScore(Estate estate, PageType pageType) async {
    double score = 0;

    // النقاط الأساسية حسب نوع الإعلان
    score += _getBasePremiumScore(estate);

    // نقاط إضافية للحداثة
    score += _getRecencyScore(estate);

    // نقاط الأداء والتفاعل
    score += await _getPerformanceScore(estate);

    // نقاط جودة المحتوى
    score += _getContentQualityScore(estate);

    // نقاط الصلة بالصفحة
    score += _getPageRelevanceScore(estate, pageType);

    // نقاط المدة المتبقية للباقة
    score += _getRemainingDurationScore(estate);

    // نقاط التنوع (لتجنب هيمنة مُعلن واحد)
    score += await _getDiversityScore(estate);

    return score;
  }

  /// النقاط الأساسية للإعلانات المدفوعة
  double _getBasePremiumScore(Estate estate) {
    if (estate.vipBadge == true) {
      return PremiumAdType.vip.baseScore.toDouble();
    } else if (estate.pinnedOnHome == true) {
      return PremiumAdType.pinnedOnHome.baseScore.toDouble();
    } else if (estate.isFeatured == true) {
      return PremiumAdType.featured.baseScore.toDouble();
    } else if (estate.kuwaitCornersPin == true) {
      return PremiumAdType.kuwaitCornersPin.baseScore.toDouble();
    } else if (estate.movingAd == true) {
      return PremiumAdType.movingAd.baseScore.toDouble();
    }
    return PremiumAdType.normal.baseScore.toDouble();
  }

  /// نقاط الحداثة
  double _getRecencyScore(Estate estate) {
    final now = DateTime.now();
    final createdAt = estate.createdAt ?? now;
    final hoursSinceCreation = now.difference(createdAt).inHours;

    if (hoursSinceCreation <= 24) {
      return 50.0; // إعلان جديد (آخر 24 ساعة)
    } else if (hoursSinceCreation <= 72) {
      return 30.0; // إعلان حديث (آخر 3 أيام)
    } else if (hoursSinceCreation <= 168) {
      return 15.0; // إعلان من الأسبوع الماضي
    }
    return 0.0;
  }

  /// نقاط الأداء والتفاعل
  Future<double> _getPerformanceScore(Estate estate) async {
    try {
      // جلب إحصائيات الأداء
      final statsDoc = await _firestore
          .collection('adStats')
          .doc(estate.id)
          .get();

      if (!statsDoc.exists) return 0.0;

      final stats = statsDoc.data()!;
      final views = (stats['views'] as num?)?.toInt() ?? 0;
      final clicks = (stats['clicks'] as num?)?.toInt() ?? 0;
      final inquiries = (stats['inquiries'] as num?)?.toInt() ?? 0;
      final favorites = (stats['favorites'] as num?)?.toInt() ?? 0;

      double score = 0;

      // نقاط المشاهدات
      if (views > 1000) {
        score += 30;
      } else if (views > 500) score += 20;
      else if (views > 100) score += 10;

      // نقاط النقرات
      if (clicks > 100) {
        score += 25;
      } else if (clicks > 50) score += 15;
      else if (clicks > 10) score += 8;

      // نقاط الاستفسارات
      score += inquiries * 5; // 5 نقاط لكل استفسار

      // نقاط المفضلة
      score += favorites * 3; // 3 نقاط لكل إضافة للمفضلة

      // معدل التحويل (CTR)
      if (views > 0) {
        final ctr = (clicks / views) * 100;
        if (ctr > 5) {
          score += 20;
        } else if (ctr > 2) score += 10;
      }

      return score;
    } catch (e) {
      print('Error calculating performance score: $e');
      return 0.0;
    }
  }

  /// نقاط جودة المحتوى
  double _getContentQualityScore(Estate estate) {
    double score = 0;

    // نقاط عدد الصور
    final imageCount = estate.photoUrls.length;
    if (imageCount >= 10) {
      score += 15;
    } else if (imageCount >= 5) score += 10;
    else if (imageCount >= 3) score += 5;

    // نقاط طول الوصف
    final descriptionLength = estate.description.length;
    if (descriptionLength >= 200) {
      score += 10;
    } else if (descriptionLength >= 100) score += 5;

    // نقاط اكتمال البيانات
    int completedFields = 0;
    if (estate.area != null && estate.area! > 0) completedFields++;
    if (estate.rooms != null && estate.rooms! > 0) completedFields++;
    if (estate.bathrooms != null && estate.bathrooms! > 0) completedFields++;
    if (estate.buildingAge != null && estate.buildingAge!.toString().isNotEmpty) completedFields++;
    if (estate.propertyType != null && estate.propertyType!.isNotEmpty) completedFields++;

    score += completedFields * 2; // 2 نقطة لكل حقل مكتمل

    // نقاط الموقع الجغرافي
    if (estate.latitude != null && estate.longitude != null) {
      score += 5;
    }

    return score;
  }

  /// نقاط الصلة بالصفحة
  double _getPageRelevanceScore(Estate estate, PageType pageType) {
    switch (pageType) {
      case PageType.homePage:
        // تفضيل الإعلانات المثبتة في الرئيسية
        if (estate.pinnedOnHome == true) return 20;
        if (estate.vipBadge == true) return 15;
        return 0;

      case PageType.categoryPage:
        // تفضيل الإعلانات المثبتة في الأقسام
        if (estate.kuwaitCornersPin == true) return 25;
        if (estate.isFeatured == true) return 15;
        return 0;

      case PageType.searchResults:
        // تفضيل الإعلانات المميزة في البحث
        if (estate.isFeatured == true) return 20;
        if (estate.vipBadge == true) return 15;
        return 0;

      default:
        return 0;
    }
  }

  /// نقاط المدة المتبقية للباقة
  double _getRemainingDurationScore(Estate estate) {
    if (estate.endDate == null) return 0;

    final now = DateTime.now();
    final endDate = estate.endDate!;
    final daysRemaining = endDate.difference(now).inDays;

    if (daysRemaining > 20) {
      return 20;
    } else if (daysRemaining > 10) return 15;
    else if (daysRemaining > 5) return 10;
    else if (daysRemaining > 0) return 5;

    return 0; // منتهي الصلاحية
  }

  /// نقاط التنوع (لتجنب هيمنة مُعلن واحد)
  Future<double> _getDiversityScore(Estate estate) async {
    try {
      // حساب عدد الإعلانات الحالية للمُعلن في النتائج المعروضة
      final recentDisplaysSnapshot = await _firestore
          .collection('adDisplayLogs')
          .where('advertiserId', isEqualTo: estate.userId)
          .where('timestamp', isGreaterThan: Timestamp.fromDate(
            DateTime.now().subtract(const Duration(hours: 1))
          ))
          .get();

      final recentDisplays = recentDisplaysSnapshot.docs.length;

      // تقليل النقاط كلما زادت الإعلانات المعروضة مؤخراً
      if (recentDisplays == 0) {
        return 10; // لم يتم عرض إعلانات مؤخراً
      } else if (recentDisplays <= 2) return 5;
      else if (recentDisplays <= 5) return 0;
      else return -10; // تقليل النقاط للمُعلنين المهيمنين

    } catch (e) {
      print('Error calculating diversity score: $e');
      return 0;
    }
  }

  /// تطبيق خوارزمية التوزيع الذكي
  List<Estate> _applySmartDistribution(
    List<EstateWithScore> scoredAds,
    PageType pageType,
    int limit) {
    final distributionRule = distributionRules[pageType] ??
        distributionRules[PageType.homePage]!;

    final List<Estate> result = [];
    final Map<PremiumAdType, int> typeCounters = {};

    // حساب العدد المطلوب لكل نوع
    final Map<PremiumAdType, int> targetCounts = {};
    distributionRule.forEach((type, percentage) {
      targetCounts[type] = (limit * percentage).round();
      typeCounters[type] = 0;
    });

    // توزيع الإعلانات حسب النوع والنقاط
    for (final scoredAd in scoredAds) {
      if (result.length >= limit) break;

      final adType = _getEstateAdType(scoredAd.estate);
      final currentCount = typeCounters[adType] ?? 0;
      final targetCount = targetCounts[adType] ?? 0;

      if (currentCount < targetCount) {
        result.add(scoredAd.estate);
        typeCounters[adType] = currentCount + 1;
      }
    }

    // ملء المساحات المتبقية بأفضل الإعلانات المتاحة
    for (final scoredAd in scoredAds) {
      if (result.length >= limit) break;
      if (!result.contains(scoredAd.estate)) {
        result.add(scoredAd.estate);
      }
    }

    return result;
  }

  /// تحديد نوع الإعلان
  PremiumAdType _getEstateAdType(Estate estate) {
    if (estate.vipBadge == true) return PremiumAdType.vip;
    if (estate.pinnedOnHome == true) return PremiumAdType.pinnedOnHome;
    if (estate.isFeatured == true) return PremiumAdType.featured;
    if (estate.kuwaitCornersPin == true) return PremiumAdType.kuwaitCornersPin;
    if (estate.movingAd == true) return PremiumAdType.movingAd;
    return PremiumAdType.normal;
  }

  /// تسجيل إحصائيات العرض
  Future<void> _logAdDisplayStats(List<Estate> displayedAds, PageType pageType) async {
    try {
      final batch = _firestore.batch();
      final timestamp = Timestamp.now();

      for (final ad in displayedAds) {
        // تسجيل عرض الإعلان
        final logRef = _firestore.collection('adDisplayLogs').doc();
        batch.set(logRef, {
          'adId': ad.id,
          'advertiserId': ad.userId,
          'pageType': pageType.name,
          'timestamp': timestamp,
          'adType': _getEstateAdType(ad).name,
        });

        // تحديث عداد المشاهدات
        final statsRef = _firestore.collection('adStats').doc(ad.id);
        batch.update(statsRef, {
          'views': FieldValue.increment(1),
          'lastViewed': timestamp,
        });
      }

      await batch.commit();
    } catch (e) {
      print('Error logging ad display stats: $e');
    }
  }

  /// تحديث إحصائيات النقر
  Future<void> logAdClick(String adId) async {
    try {
      await _firestore.collection('adStats').doc(adId).update({
        'clicks': FieldValue.increment(1),
        'lastClicked': Timestamp.now(),
      });
    } catch (e) {
      print('Error logging ad click: $e');
    }
  }

  /// تحديث إحصائيات الاستفسار
  Future<void> logAdInquiry(String adId) async {
    try {
      await _firestore.collection('adStats').doc(adId).update({
        'inquiries': FieldValue.increment(1),
        'lastInquiry': Timestamp.now(),
      });
    } catch (e) {
      print('Error logging ad inquiry: $e');
    }
  }

  /// تحديث إحصائيات المفضلة
  Future<void> logAdFavorite(String adId, bool isAdded) async {
    try {
      await _firestore.collection('adStats').doc(adId).update({
        'favorites': FieldValue.increment(isAdded ? 1 : -1),
        'lastFavorited': Timestamp.now(),
      });
    } catch (e) {
      print('Error logging ad favorite: $e');
    }
  }

  /// تحويل البيانات من Firestore إلى Estate object
  Estate _mapToEstate(Map<String, dynamic> data, String id) {
    return Estate(
      id: id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      location: data['location'] ?? '',
      photoUrls: List<String>.from(data['photoUrls'] ?? []),
      isFeatured: data['isFeatured'] ?? false,
      planType: data['planType'] ?? 'free',
      startDate: data['startDate'] != null
          ? DateTime.parse(data['startDate'])
          : null,
      endDate: data['endDate'] != null
          ? DateTime.parse(data['endDate'])
          : null,
      createdAt: data['createdAt'] != null
          ? DateTime.parse(data['createdAt'])
          : DateTime.now(),
      mainCategory: data['mainCategory'],
      subCategory: data['subCategory'],
      postedByUserType: data['postedByUserType'],
      hidePhone: data['hidePhone'] ?? false,
      extraPhones: List<String>.from(data['extraPhones'] ?? []),
      shareLocation: data['shareLocation'] ?? false,
      lat: data['lat']?.toDouble(),
      lng: data['lng']?.toDouble(),
      hasCentralAC: data['hasCentralAC'] ?? false,
      hasSecurity: data['hasSecurity'],
      allowPets: data['allowPets'],
      hasElevator: data['hasElevator'],
      hasSwimmingPool: data['hasSwimmingPool'],
      hasMaidRoom: data['hasMaidRoom'] ?? false,
      hasGarage: data['hasGarage'] ?? false,
      hasBalcony: data['hasBalcony'],
      isFullyFurnished: data['isFullyFurnished'],
      rebound: data['rebound'],
      numberOfRooms: data['numberOfRooms'],
      internalLocation: data['internalLocation'],
      salon: data['salon'],
      area: data['area']?.toDouble(),
      floorNumber: data['floorNumber'],
      numberOfBathrooms: data['numberOfBathrooms'],
      buildingAge: data['buildingAge'],
      numberOfFloors: data['numberOfFloors'],
      propertyType: data['propertyType'],
      autoRepublish: data['autoRepublish'] ?? false,
      kuwaitCornersPin: data['kuwaitCornersPin'] ?? false,
      movingAd: data['movingAd'] ?? false,
      vipBadge: data['vipBadge'] ?? false,
      pinnedOnHome: data['pinnedOnHome'] ?? false,
      discountCode: data['discountCode'],
      advertiserImage: data['advertiserImage'],
      advertiserName: data['advertiserName'],
      advertiserEmail: data['advertiserEmail'],
      advertiserRegistrationDate: data['advertiserRegistrationDate'] != null
          ? DateTime.parse(data['advertiserRegistrationDate'])
          : null,
      advertiserAdsCount: data['advertiserAdsCount'],
      ownerId: data['ownerId'],
      originalEstateId: data['originalEstateId'],
      isOriginal: data['isOriginal'] ?? true,
      copiedBy: List<String>.from(data['copiedBy'] ?? []),
      isPaymentVerified: data['isPaymentVerified'] ?? false,
      viewsCount: data['viewsCount'],
      inquiriesCount: data['inquiriesCount'],
      favoritesCount: data['favoritesCount'],
      latitude: data['latitude']?.toDouble(),
      longitude: data['longitude']?.toDouble(),
      rooms: data['rooms'],
      bathrooms: data['bathrooms'],
      floors: data['floors'],
      purpose: data['purpose'],
      hasGarden: data['hasGarden'],
      hasPool: data['hasPool'],
      hasDriverRoom: data['hasDriverRoom'],
      hasPrivateEntrance: data['hasPrivateEntrance'],
      hasEquippedKitchen: data['hasEquippedKitchen'],
      isAvailable: data['isAvailable'] ?? true);
  }
}

/// كلاس مساعد لربط العقار بنقاطه
class EstateWithScore {
  final Estate estate;
  final double score;

  EstateWithScore(this.estate, this.score);
}
