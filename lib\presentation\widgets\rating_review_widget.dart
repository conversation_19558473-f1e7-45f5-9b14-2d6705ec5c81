import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:kuwait_corners/core/services/rating_review_service.dart';
import 'package:kuwait_corners/presentation/widgets/enhanced_progress_indicator.dart';

/// مكون عرض التقييمات والمراجعات
class RatingReviewWidget extends StatefulWidget {
  /// معرف العنصر
  final String itemId;
  
  /// نوع العنصر
  final RatableItemType itemType;
  
  /// ما إذا كان يمكن للمستخدم إضافة تقييم
  final bool canAddRating;
  
  /// ما إذا كان يمكن للمستخدم إضافة رد
  final bool canAddReply;
  
  /// ما إذا كان المستخدم هو مالك العنصر
  final bool isOwner;
  
  /// دالة يتم استدعاؤها عند تغيير التقييم
  final Function(double)? onRatingChanged;

  const RatingReviewWidget({
    super.key,
    required this.itemId,
    required this.itemType,
    this.canAddRating = true,
    this.canAddReply = true,
    this.isOwner = false,
    this.onRatingChanged,
  });

  @override
  State<RatingReviewWidget> createState() => _RatingReviewWidgetState();
}

class _RatingReviewWidgetState extends State<RatingReviewWidget> with SingleTickerProviderStateMixin {
  final RatingReviewService _ratingService = RatingReviewService();
  
  // حالة المكون
  bool _isLoading = true;
  String? _errorMessage;
  RatingSummaryModel? _ratingSummary;
  List<RatingModel> _ratings = [];
  RatingModel? _userRating;
  
  // وحدة التحكم في علامات التبويب
  late TabController _tabController;
  
  // وحدات التحكم في النص
  final _reviewController = TextEditingController();
  final _replyController = TextEditingController();
  
  // قيمة التقييم الجديد
  double _newRating = 0;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _reviewController.dispose();
    _replyController.dispose();
    super.dispose();
  }
  
  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    
    try {
      // تحميل ملخص التقييمات
      final summary = await _ratingService.getItemRatingSummary(widget.itemId);

      // تحميل التقييمات
      final ratings = await _ratingService.getItemRatings(widget.itemId);
      
      // تحميل تقييم المستخدم الحالي
      final userRating = await _ratingService.getUserRatingForItem(widget.itemId);
      
      setState(() {
        _ratingSummary = summary;
        _ratings = ratings;
        _userRating = userRating;
        
        if (userRating != null) {
          _newRating = userRating.rating;
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل التقييمات';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// إضافة تقييم جديد
  Future<void> _addRating() async {
    if (_newRating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار تقييم')));
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final ratingId = await _ratingService.addRating(
        itemId: widget.itemId,
        itemType: widget.itemType,
        rating: _newRating,
        review: _reviewController.text.isNotEmpty ? _reviewController.text : null);
      
      if (ratingId != null) {
        // إعادة تحميل البيانات
        await _loadData();
        
        // استدعاء دالة التغيير
        if (widget.onRatingChanged != null) {
          widget.onRatingChanged!(_newRating);
        }
        
        // إعادة تعيين النص
        _reviewController.clear();
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إضافة التقييم بنجاح')));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء إضافة التقييم')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: ${e.toString()}')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// تحديث تقييم موجود
  Future<void> _updateRating() async {
    if (_userRating == null) {
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final success = await _ratingService.updateRating(
        ratingId: _userRating!.id,
        rating: _newRating,
        review: _reviewController.text.isNotEmpty ? _reviewController.text : null);
      
      if (success) {
        // إعادة تحميل البيانات
        await _loadData();
        
        // استدعاء دالة التغيير
        if (widget.onRatingChanged != null) {
          widget.onRatingChanged!(_newRating);
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديث التقييم بنجاح')));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء تحديث التقييم')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: ${e.toString()}')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// حذف تقييم
  Future<void> _deleteRating() async {
    if (_userRating == null) {
      return;
    }
    
    // عرض مربع حوار التأكيد
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التقييم'),
        content: const Text('هل أنت متأكد من حذف التقييم؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء')),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red),
            child: const Text('حذف')),
        ]));
    
    if (confirmed != true) {
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final success = await _ratingService.deleteRating(_userRating!.id);
      
      if (success) {
        // إعادة تحميل البيانات
        await _loadData();
        
        // استدعاء دالة التغيير
        if (widget.onRatingChanged != null) {
          widget.onRatingChanged!(0);
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف التقييم بنجاح')));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء حذف التقييم')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: ${e.toString()}')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// إضافة رد على مراجعة
  Future<void> _addReply(String ratingId) async {
    if (_replyController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى كتابة رد')));
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final success = await _ratingService.addReply(
        ratingId: ratingId,
        content: _replyController.text,
        isOwner: widget.isOwner);
      
      if (success) {
        // إعادة تحميل البيانات
        await _loadData();
        
        // إعادة تعيين النص
        _replyController.clear();
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إضافة الرد بنجاح')));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء إضافة الرد')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: ${e.toString()}')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : _errorMessage != null
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 48),
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red)),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadData,
                      child: const Text('إعادة المحاولة')),
                  ]))
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص التقييمات
                  _buildRatingSummary(),
                  
                  const SizedBox(height: 16),
                  
                  // علامات التبويب
                  TabBar(
                    controller: _tabController,
                    tabs: [
                      Tab(text: 'التقييمات (${_ratings.length})'),
                      const Tab(text: 'إضافة تقييم'),
                    ]),
                  
                  const SizedBox(height: 16),
                  
                  // محتوى علامات التبويب
                  SizedBox(
                    height: 400, // يمكن تعديل الارتفاع حسب الحاجة
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        // قائمة التقييمات
                        _buildRatingsList(),
                        
                        // نموذج إضافة تقييم
                        _buildAddRatingForm(),
                      ])),
                ]);
  }
  
  /// بناء ملخص التقييمات
  Widget _buildRatingSummary() {
    final summary = _ratingSummary;
    
    if (summary == null) {
      return const Center(
        child: Text(
          'لا توجد تقييمات بعد',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)));
    }
    
    final percentages = summary.getRatingPercentages();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // متوسط التقييم
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    summary.averageRating.toStringAsFixed(1),
                    style: const TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  _buildStarRating(summary.averageRating, size: 24),
                  const SizedBox(height: 8),
                  Text(
                    '${summary.ratingsCount} تقييم',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600)),
                ])),
            
            // توزيع التقييمات
            Expanded(
              flex: 3,
              child: Column(
                children: List.generate(5, (index) {
                  final starCount = 5 - index;
                  final percentage = percentages[starCount] ?? 0;
                  
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Text(
                          '$starCount',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold)),
                        const SizedBox(width: 4),
                        const Icon(Icons.star, size: 14, color: Colors.amber),
                        const SizedBox(width: 8),
                        Expanded(
                          child: EnhancedLinearProgressIndicator(
                            value: percentage,
                            title: '',
                            height: 8,
                            progressColor: Colors.amber,
                            backgroundColor: Colors.grey.shade200,
                            showPercentage: false)),
                        const SizedBox(width: 8),
                        Text(
                          '${(percentage * 100).toInt()}%',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600)),
                      ]));
                }))),
          ])));
  }
  
  /// بناء قائمة التقييمات
  Widget _buildRatingsList() {
    if (_ratings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.rate_review_outlined,
              size: 64,
              color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد تقييمات بعد',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600)),
            const SizedBox(height: 8),
            Text(
              'كن أول من يقيم هذا العنصر',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600)),
          ]));
    }
    
    return ListView.separated(
      itemCount: _ratings.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final rating = _ratings[index];
        
        return _buildRatingItem(rating);
      });
  }
  
  /// بناء عنصر تقييم
  Widget _buildRatingItem(RatingModel rating) {
    final formattedDate = DateFormat('yyyy/MM/dd').format(rating.createdAt);
    final isCurrentUserRating = _userRating != null && _userRating!.id == rating.id;
    
    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: isCurrentUserRating
            ? BorderSide(color: Colors.blue.shade200)
            : BorderSide.none),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المستخدم والتقييم
            Row(
              children: [
                // صورة المستخدم
                CircleAvatar(
                  radius: 20,
                  backgroundImage: rating.userPhotoUrl != null
                      ? NetworkImage(rating.userPhotoUrl!)
                      : null,
                  child: rating.userPhotoUrl == null
                      ? const Icon(Icons.person)
                      : null),
                
                const SizedBox(width: 12),
                
                // اسم المستخدم والتاريخ
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            rating.userName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold)),
                          if (isCurrentUserRating) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(4)),
                              child: const Text(
                                'تقييمك',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue))),
                          ],
                        ]),
                      Text(
                        formattedDate,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600)),
                    ])),
                
                // التقييم
                _buildStarRating(rating.rating),
              ]),
            
            // نص المراجعة
            if (rating.review != null && rating.review!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                rating.review!,
                style: const TextStyle(
                  fontSize: 14)),
            ],
            
            // أزرار التحكم (للمستخدم الحالي)
            if (isCurrentUserRating) ...[
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      // تعيين قيم النموذج
                      setState(() {
                        _newRating = rating.rating;
                        _reviewController.text = rating.review ?? '';
                      });
                      
                      // الانتقال إلى علامة تبويب التعديل
                      _tabController.animateTo(1);
                    },
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('تعديل')),
                  TextButton.icon(
                    onPressed: _deleteRating,
                    icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                    label: const Text('حذف', style: TextStyle(color: Colors.red))),
                ]),
            ],
            
            // الردود
            if (rating.replies.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),
              const Text(
                'الردود',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14)),
              const SizedBox(height: 8),
              ...rating.replies.map((reply) => _buildReplyItem(reply)),
            ],
            
            // نموذج إضافة رد
            if (widget.canAddReply) ...[
              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _replyController,
                      decoration: const InputDecoration(
                        hintText: 'أضف ردًا...',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8)),
                      maxLines: 2,
                      minLines: 1)),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () => _addReply(rating.id),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12)),
                    child: const Text('إرسال')),
                ]),
            ],
          ])));
  }
  
  /// بناء عنصر رد
  Widget _buildReplyItem(ReplyModel reply) {
    final formattedDate = DateFormat('yyyy/MM/dd').format(reply.createdAt);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8, right: 16),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: reply.isOwner ? Colors.blue.shade50 : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المستخدم
          Row(
            children: [
              // صورة المستخدم
              CircleAvatar(
                radius: 14,
                backgroundImage: reply.userPhotoUrl != null
                    ? NetworkImage(reply.userPhotoUrl!)
                    : null,
                child: reply.userPhotoUrl == null
                    ? const Icon(Icons.person, size: 14)
                    : null),
              
              const SizedBox(width: 8),
              
              // اسم المستخدم والتاريخ
              Expanded(
                child: Row(
                  children: [
                    Text(
                      reply.userName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12)),
                    if (reply.isOwner) ...[
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(2)),
                        child: const Text(
                          'المالك',
                          style: TextStyle(
                            fontSize: 8,
                            color: Colors.white))),
                    ],
                    const Spacer(),
                    Text(
                      formattedDate,
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey.shade600)),
                  ])),
            ]),
          
          const SizedBox(height: 4),
          
          // نص الرد
          Text(
            reply.content,
            style: const TextStyle(
              fontSize: 12)),
        ]));
  }
  
  /// بناء نموذج إضافة تقييم
  Widget _buildAddRatingForm() {
    if (!widget.canAddRating) {
      return const Center(
        child: Text(
          'لا يمكنك إضافة تقييم لهذا العنصر',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)));
    }
    
    final isUpdate = _userRating != null;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Text(
            isUpdate ? 'تعديل التقييم' : 'إضافة تقييم جديد',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold)),
          
          const SizedBox(height: 16),
          
          // اختيار التقييم
          const Text(
            'تقييمك',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold)),
          
          const SizedBox(height: 8),
          
          // نجوم التقييم
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              final starValue = index + 1;
              
              return IconButton(
                onPressed: () {
                  setState(() {
                    _newRating = starValue.toDouble();
                  });
                },
                icon: Icon(
                  starValue <= _newRating ? Icons.star : Icons.star_border,
                  color: Colors.amber,
                  size: 36),
                padding: const EdgeInsets.all(8));
            })),
          
          const SizedBox(height: 16),
          
          // حقل المراجعة
          const Text(
            'مراجعتك (اختياري)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold)),
          
          const SizedBox(height: 8),
          
          TextField(
            controller: _reviewController,
            decoration: const InputDecoration(
              hintText: 'اكتب مراجعتك هنا...',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.all(16)),
            maxLines: 5,
            minLines: 3),
          
          const SizedBox(height: 24),
          
          // زر الإرسال
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: isUpdate ? _updateRating : _addRating,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black87,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8))),
              child: Text(
                isUpdate ? 'تحديث التقييم' : 'إرسال التقييم',
                style: const TextStyle(fontSize: 16)))),
          
          if (isUpdate) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: _deleteRating,
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8))),
                child: const Text(
                  'حذف التقييم',
                  style: TextStyle(fontSize: 16)))),
          ],
        ]));
  }
  
  /// بناء تقييم النجوم
  Widget _buildStarRating(double rating, {double size = 16}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        final starValue = index + 1;
        
        if (starValue <= rating) {
          // نجمة كاملة
          return Icon(Icons.star, color: Colors.amber, size: size);
        } else if (starValue - 0.5 <= rating) {
          // نصف نجمة
          return Icon(Icons.star_half, color: Colors.amber, size: size);
        } else {
          // نجمة فارغة
          return Icon(Icons.star_border, color: Colors.amber, size: size);
        }
      }));
  }
}
