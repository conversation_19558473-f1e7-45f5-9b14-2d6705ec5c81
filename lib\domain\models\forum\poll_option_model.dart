import 'package:equatable/equatable.dart';

/// نموذج خيار استطلاع الرأي
class PollOptionModel extends Equatable {
  /// معرف الخيار
  final String id;

  /// نص الخيار
  final String text;

  /// قائمة معرفات المستخدمين الذين صوتوا لهذا الخيار
  final List<String> votedBy;

  /// عدد الأصوات لهذا الخيار
  final int votesCount;

  /// إنشاء نموذج خيار استطلاع الرأي
  const PollOptionModel({
    required this.id,
    required this.text,
    required this.votedBy,
    required this.votesCount,
  });

  /// إنشاء نسخة معدلة من خيار الاستطلاع
  PollOptionModel copyWith({
    String? id,
    String? text,
    List<String>? votedBy,
    int? votesCount,
  }) {
    return PollOptionModel(
      id: id ?? this.id,
      text: text ?? this.text,
      votedBy: votedBy ?? this.votedBy,
      votesCount: votesCount ?? this.votesCount);
  }

  /// تحويل خيار الاستطلاع إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'text': text,
      'votedBy': votedBy,
      'votesCount': votesCount,
    };
  }

  /// إنشاء خيار استطلاع من خريطة
  factory PollOptionModel.fromMap(Map<String, dynamic> map) {
    return PollOptionModel(
      id: map['id'] ?? '',
      text: map['text'] ?? '',
      votedBy: map['votedBy'] != null
          ? List<String>.from(map['votedBy'])
          : [],
      votesCount: map['votesCount'] ?? 0);
  }

  @override
  List<Object?> get props => [id, text, votedBy, votesCount];
}
