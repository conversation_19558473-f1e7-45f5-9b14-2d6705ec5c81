// lib/core/error/error_handler.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../theme/app_colors.dart';

/// معالج الأخطاء المحسن
/// يوفر واجهة موحدة للتعامل مع الأخطاء في التطبيق
class EnhancedErrorHandler {
  /// عرض رسالة خطأ في شريط سناك بار
  static void showErrorSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.cairo()),
        backgroundColor: Colors.red.shade700,
        duration: duration,
        action: action));
  }
  
  /// عرض رسالة نجاح في شريط سناك بار
  static void showSuccessSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.cairo()),
        backgroundColor: Colors.green.shade700,
        duration: duration,
        action: action));
  }
  
  /// عرض رسالة معلومات في شريط سناك بار
  static void showInfoSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.cairo()),
        backgroundColor: Colors.blue.shade700,
        duration: duration,
        action: action));
  }
  
  /// عرض حوار خطأ
  static Future<void> showErrorDialog(
    BuildContext context,
    String title,
    String message, {
    String? buttonText,
    VoidCallback? onRetry,
  }) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.red.shade700)),
        content: Text(
          message,
          style: GoogleFonts.cairo()),
        actions: [
          if (onRetry != null)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                onRetry();
              },
              child: Text(
                "إعادة المحاولة",
                style: GoogleFonts.cairo(
                  color: AppColors.primary))),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              buttonText ?? "حسناً",
              style: GoogleFonts.cairo(
                color: Colors.grey.shade800))),
        ]));
  }
  
  /// عرض حوار تأكيد
  static Future<bool> showConfirmationDialog(
    BuildContext context,
    String title,
    String message, {
    String confirmText = "تأكيد",
    String cancelText = "إلغاء",
    bool isDestructive = false,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: isDestructive ? Colors.red.shade700 : Colors.grey.shade800)),
        content: Text(
          message,
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              cancelText,
              style: GoogleFonts.cairo(
                color: Colors.grey.shade800))),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              confirmText,
              style: GoogleFonts.cairo(
                color: isDestructive ? Colors.red.shade700 : AppColors.primary,
                fontWeight: FontWeight.bold))),
        ]));
    
    return result ?? false;
  }
  
  /// عرض حوار إعادة المحاولة
  static Future<bool> showRetryDialog(
    BuildContext context,
    String title,
    String message, {
    String retryText = "إعادة المحاولة",
    String cancelText = "إلغاء",
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        content: Text(
          message,
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              cancelText,
              style: GoogleFonts.cairo(
                color: Colors.grey.shade800))),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              retryText,
              style: GoogleFonts.cairo(
                color: AppColors.primary,
                fontWeight: FontWeight.bold))),
        ]));
    
    return result ?? false;
  }
  
  /// عرض حوار تقدم
  static Future<void> showProgressDialog(
    BuildContext context,
    String message, {
    bool dismissible = false,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.cairo())),
          ])));
  }
  
  /// إغلاق حوار التقدم
  static void closeProgressDialog(BuildContext context) {
    Navigator.pop(context);
  }
}
