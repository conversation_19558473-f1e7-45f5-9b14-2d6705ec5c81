import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../domain/entities/recommendation.dart';
import '../../domain/entities/estate.dart';
import '../../domain/entities/user.dart' as app_user;
import '../../infrastructure/services/recommendation_service.dart';
import '../widgets/app_bar_widget.dart';
import '../widgets/loading_widget.dart';
import '../widgets/optimized_image.dart';

/// شاشة التوصيات الذكية
class RecommendationsScreen extends StatefulWidget {
  /// إنشاء شاشة التوصيات الذكية
  const RecommendationsScreen({super.key});

  @override
  _RecommendationsScreenState createState() => _RecommendationsScreenState();
}

class _RecommendationsScreenState extends State<RecommendationsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  String? _errorMessage;
  List<Recommendation> _recommendations = [];
  List<Estate> _recommendedEstates = [];
  List<app_user.User> _recommendedAgents = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadRecommendations();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل التوصيات
  Future<void> _loadRecommendations() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final recommendationService =
          Provider.of<RecommendationService>(context, listen: false);
      final userId =
          'user123'; // في التطبيق الحقيقي، يجب استخدام معرف المستخدم الحالي

      final recommendations =
          await recommendationService.getUserRecommendations(
        userId: userId,
        limit: 20);

      final recommendedEstates =
          await recommendationService.getRecommendedEstates(
        userId: userId,
        limit: 10);

      final recommendedAgents =
          await recommendationService.getRecommendedAgents(
        userId: userId,
        limit: 10);

      setState(() {
        _recommendations = recommendations;
        _recommendedEstates = recommendedEstates;
        _recommendedAgents = recommendedAgents;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل التوصيات: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: 'التوصيات الذكية'),
      body: _isLoading
          ? const LoadingWidget()
          : _errorMessage != null
              ? ErrorWidgetCustom(
                  message: _errorMessage!,
                  onRetry: _loadRecommendations)
              : Column(
                  children: [
                    _buildTabBar(),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          _buildAllRecommendationsTab(),
                          _buildEstatesRecommendationsTab(),
                          _buildAgentsRecommendationsTab(),
                        ])),
                  ]));
  }

  /// بناء شريط التبويب
  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).primaryColor,
      child: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        tabs: const [
          Tab(text: 'جميع التوصيات'),
          Tab(text: 'عقارات موصى بها'),
          Tab(text: 'وكلاء موصى بهم'),
        ]));
  }

  /// بناء تبويب جميع التوصيات
  Widget _buildAllRecommendationsTab() {
    if (_recommendations.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد توصيات متاحة حالياً',
          style: TextStyle(fontSize: 16)));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _recommendations.length,
      itemBuilder: (context, index) {
        final recommendation = _recommendations[index];
        return _buildRecommendationCard(recommendation);
      });
  }

  /// بناء تبويب العقارات الموصى بها
  Widget _buildEstatesRecommendationsTab() {
    if (_recommendedEstates.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد عقارات موصى بها حالياً',
          style: TextStyle(fontSize: 16)));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _recommendedEstates.length,
      itemBuilder: (context, index) {
        final estate = _recommendedEstates[index];
        return _buildEstateCard(estate);
      });
  }

  /// بناء تبويب الوكلاء الموصى بهم
  Widget _buildAgentsRecommendationsTab() {
    if (_recommendedAgents.isEmpty) {
      return const Center(
        child: Text(
          'لا يوجد وكلاء موصى بهم حالياً',
          style: TextStyle(fontSize: 16)));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _recommendedAgents.length,
      itemBuilder: (context, index) {
        final agent = _recommendedAgents[index];
        return _buildAgentCard(agent);
      });
  }

  /// بناء بطاقة التوصية
  Widget _buildRecommendationCard(Recommendation recommendation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _onRecommendationTap(recommendation),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (recommendation.itemImage != null)
              ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12)),
                child: OptimizedImage(
                  imageUrl: recommendation.itemImage!,
                  height: 150,
                  width: double.infinity,
                  fit: BoxFit.cover)),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          recommendation.itemTitle,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis)),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16)),
                        child: Text(
                          _getRecommendationTypeText(recommendation.type),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor))),
                    ]),
                  const SizedBox(height: 8),
                  if (recommendation.itemDescription != null)
                    Text(
                      recommendation.itemDescription!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600]),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.lightbulb_outline,
                        size: 16,
                        color: Colors.amber),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          recommendation.reason,
                          style: const TextStyle(
                            fontSize: 14,
                            fontStyle: FontStyle.italic),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis)),
                    ]),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'نسبة الملاءمة: ${(recommendation.relevanceScore * 100).toInt()}%',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold)),
                      Text(
                        _getRecommendationSourceText(recommendation.source),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600])),
                    ]),
                ])),
          ])));
  }

  /// بناء بطاقة العقار
  Widget _buildEstateCard(Estate estate) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _onEstateTap(estate),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (estate.images.isNotEmpty)
              ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12)),
                child: OptimizedImage(
                  imageUrl: estate.images.first,
                  height: 150,
                  width: double.infinity,
                  fit: BoxFit.cover)),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          estate.title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis)),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4),
                        decoration: BoxDecoration(
                          color: _getEstateTypeColor(estate.propertyType ?? "")
                              .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16)),
                        child: Text(
                          estate.propertyType ?? "",
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color:
                                _getEstateTypeColor(estate.propertyType ?? "")))),
                    ]),
                  const SizedBox(height: 8),
                  Text(
                    estate.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600]),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: Theme.of(context).primaryColor),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          '${estate.areaName} ${estate.city != null ? "- ${estate.city}" : ""}',
                          style: const TextStyle(
                            fontSize: 14),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis)),
                    ]),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${estate.price} ريال',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green)),
                      Text(
                        '${estate.size.toStringAsFixed(1)} متر مربع',
                        style: const TextStyle(
                          fontSize: 14)),
                    ]),
                ])),
          ])));
  }

  /// بناء بطاقة الوكيل
  Widget _buildAgentCard(app_user.User agent) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _onAgentTap(agent),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              CircleAvatar(
                radius: 40,
                backgroundImage: agent.profileImage != null
                    ? NetworkImage(agent.profileImage!)
                    : null,
                child: agent.profileImage == null
                    ? const Icon(
                        Icons.person,
                        size: 40,
                        color: Colors.white)
                    : null),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            agent.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis)),
                        if (agent.rating != null)
                          Row(
                            children: [
                              const Icon(
                                Icons.star,
                                size: 16,
                                color: Colors.amber),
                              const SizedBox(width: 4),
                              Text(
                                agent.rating!.toStringAsFixed(1),
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold)),
                            ]),
                      ]),
                    const SizedBox(height: 8),
                    Text(
                      agent.title ?? 'وكيل عقاري',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600])),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: Theme.of(context).primaryColor),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            agent.location ?? 'غير محدد',
                            style: const TextStyle(
                              fontSize: 14),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis)),
                      ]),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.business,
                          size: 16,
                          color: Theme.of(context).primaryColor),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            agent.company ?? 'مستقل',
                            style: const TextStyle(
                              fontSize: 14),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis)),
                      ]),
                  ])),
            ]))));
  }

  /// معالجة النقر على التوصية
  void _onRecommendationTap(Recommendation recommendation) async {
    // تسجيل النقر على التوصية
    final recommendationService =
        Provider.of<RecommendationService>(context, listen: false);
    await recommendationService.updateRecommendationStatus(
      recommendationId: recommendation.id,
      isClicked: true);

    // التنقل إلى الصفحة المناسبة بناءً على نوع التوصية
    switch (recommendation.type) {
      case RecommendationType.estate:
        // التنقل إلى صفحة العقار
        // Navigator.of(context).push(MaterialPageRoute(
        //   builder: (context) => EstateDetailsScreen(estateId: recommendation.itemId),
        // ));
        break;
      case RecommendationType.agent:
        // التنقل إلى صفحة الوكيل
        // Navigator.of(context).push(MaterialPageRoute(
        //   builder: (context) => AgentProfileScreen(agentId: recommendation.itemId),
        // ));
        break;
      case RecommendationType.area:
        // التنقل إلى صفحة المنطقة
        // Navigator.of(context).push(MaterialPageRoute(
        //   builder: (context) => AreaDetailsScreen(areaId: recommendation.itemId),
        // ));
        break;
      case RecommendationType.article:
        // التنقل إلى صفحة المقالة
        // Navigator.of(context).push(MaterialPageRoute(
        //   builder: (context) => ArticleDetailsScreen(articleId: recommendation.itemId),
        // ));
        break;
      case RecommendationType.forum:
        // التنقل إلى صفحة المنتدى
        // Navigator.of(context).push(MaterialPageRoute(
        //   builder: (context) => ForumTopicScreen(topicId: recommendation.itemId),
        // ));
        break;
      default:
        break;
    }
  }

  /// معالجة النقر على العقار
  void _onEstateTap(Estate estate) {
    // التنقل إلى صفحة العقار
    // Navigator.of(context).push(MaterialPageRoute(
    //   builder: (context) => EstateDetailsScreen(estateId: estate.id),
    // ));
  }

  /// معالجة النقر على الوكيل
  void _onAgentTap(app_user.User agent) {
    // التنقل إلى صفحة الوكيل
    // Navigator.of(context).push(MaterialPageRoute(
    //   builder: (context) => AgentProfileScreen(agentId: agent.id),
    // ));
  }

  /// الحصول على نص نوع التوصية
  String _getRecommendationTypeText(RecommendationType type) {
    switch (type) {
      case RecommendationType.estate:
        return 'عقار';
      case RecommendationType.area:
        return 'منطقة';
      case RecommendationType.agent:
        return 'وكيل';
      case RecommendationType.developer:
        return 'مطور';
      case RecommendationType.investment:
        return 'استثمار';
      case RecommendationType.article:
        return 'مقالة';
      case RecommendationType.forum:
        return 'منتدى';
      default:
        return '';
    }
  }

  /// الحصول على نص مصدر التوصية
  String _getRecommendationSourceText(RecommendationSource source) {
    switch (source) {
      case RecommendationSource.userPreferences:
        return 'بناءً على تفضيلاتك';
      case RecommendationSource.browsingHistory:
        return 'بناءً على سجل التصفح';
      case RecommendationSource.searchHistory:
        return 'بناءً على عمليات البحث';
      case RecommendationSource.favorites:
        return 'بناءً على المفضلة';
      case RecommendationSource.comparisons:
        return 'بناءً على المقارنات';
      case RecommendationSource.userInteractions:
        return 'بناءً على تفاعلاتك';
      case RecommendationSource.similarUsers:
        return 'مستخدمون مشابهون';
      case RecommendationSource.marketTrends:
        return 'اتجاهات السوق';
      case RecommendationSource.editorsPicks:
        return 'اختيارات المحررين';
      default:
        return '';
    }
  }

  /// الحصول على لون نوع العقار
  Color _getEstateTypeColor(String propertyType) {
    switch (propertyType) {
      case 'شقة':
        return Colors.blue;
      case 'فيلا':
        return Colors.green;
      case 'أرض':
        return Colors.brown;
      case 'عمارة':
        return Colors.purple;
      case 'مكتب':
        return Colors.orange;
      case 'محل تجاري':
        return Colors.red;
      case 'مستودع':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}

/// مكون خطأ مخصص
class ErrorWidgetCustom extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;

  const ErrorWidgetCustom({
    super.key,
    required this.message,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 60),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('إعادة المحاولة')),
          ])));
  }
}
