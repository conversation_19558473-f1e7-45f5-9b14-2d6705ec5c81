// lib/presentation/widgets/card_input_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

/// ويدجت إدخال بطاقة الائتمان مع تأثيرات حركية وتحقق تلقائي
class CardInputWidget extends StatefulWidget {
  /// دالة يتم استدعاؤها عند تغيير رقم البطاقة
  final Function(String) onCardNumberChanged;

  /// دالة يتم استدعاؤها عند تغيير تاريخ الانتهاء
  final Function(String) onExpiryDateChanged;

  /// دالة يتم استدعاؤها عند تغيير رمز الأمان
  final Function(String) onCvvChanged;

  /// دالة يتم استدعاؤها عند تغيير اسم حامل البطاقة
  final Function(String) onCardHolderChanged;

  /// ما إذا كان يجب عرض حقل اسم حامل البطاقة
  final bool showCardHolder;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون الحدود
  final Color? borderColor;

  /// لون النص
  final Color? textColor;

  /// لون التركيز
  final Color? focusColor;

  /// نصف قطر الحواف
  final double borderRadius;

  const CardInputWidget({
    super.key,
    required this.onCardNumberChanged,
    required this.onExpiryDateChanged,
    required this.onCvvChanged,
    required this.onCardHolderChanged,
    this.showCardHolder = true,
    this.backgroundColor,
    this.borderColor,
    this.textColor,
    this.focusColor,
    this.borderRadius = 12.0,
  });

  @override
  State<CardInputWidget> createState() => _CardInputWidgetState();
}

class _CardInputWidgetState extends State<CardInputWidget>
    with SingleTickerProviderStateMixin {
  final TextEditingController _cardNumberController = TextEditingController();
  final TextEditingController _expiryDateController = TextEditingController();
  final TextEditingController _cvvController = TextEditingController();
  final TextEditingController _cardHolderController = TextEditingController();

  final FocusNode _cardNumberFocus = FocusNode();
  final FocusNode _expiryDateFocus = FocusNode();
  final FocusNode _cvvFocus = FocusNode();
  final FocusNode _cardHolderFocus = FocusNode();

  late AnimationController _animationController;
  late Animation<double> _flipAnimation;

  bool _isCardFlipped = false;
  CardType _cardType = CardType.unknown;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400));

    _flipAnimation = Tween<double>(
      begin: 0,
      end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut));

    // إعداد مستمعي التركيز
    _cvvFocus.addListener(() {
      if (_cvvFocus.hasFocus && !_isCardFlipped) {
        setState(() {
          _isCardFlipped = true;
        });
        _animationController.forward();
      } else if (!_cvvFocus.hasFocus && _isCardFlipped) {
        setState(() {
          _isCardFlipped = false;
        });
        _animationController.reverse();
      }
    });

    // إعداد مستمعي التغيير
    _cardNumberController.addListener(_onCardNumberChanged);
  }

  @override
  void dispose() {
    _cardNumberController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();
    _cardHolderController.dispose();

    _cardNumberFocus.dispose();
    _expiryDateFocus.dispose();
    _cvvFocus.dispose();
    _cardHolderFocus.dispose();

    _animationController.dispose();
    super.dispose();
  }

  /// تحديد نوع البطاقة بناءً على الرقم
  void _onCardNumberChanged() {
    final cardNumber = _cardNumberController.text.replaceAll(' ', '');

    if (cardNumber.isEmpty) {
      setState(() {
        _cardType = CardType.unknown;
      });
      return;
    }

    // تحديد نوع البطاقة
    CardType type = CardType.unknown;

    if (cardNumber.startsWith('4')) {
      type = CardType.visa;
    } else if (cardNumber.startsWith('5')) {
      type = CardType.mastercard;
    } else if (cardNumber.startsWith('3')) {
      type = CardType.amex;
    } else if (cardNumber.startsWith('6')) {
      type = CardType.discover;
    }

    if (type != _cardType) {
      setState(() {
        _cardType = type;
      });
    }

    // تنسيق رقم البطاقة
    final formattedNumber = _formatCardNumber(cardNumber);
    if (formattedNumber != _cardNumberController.text) {
      _cardNumberController.value = TextEditingValue(
        text: formattedNumber,
        selection: TextSelection.collapsed(offset: formattedNumber.length));
    }

    widget.onCardNumberChanged(cardNumber);
  }

  /// تنسيق رقم البطاقة بإضافة مسافات
  String _formatCardNumber(String number) {
    if (number.isEmpty) return '';

    final buffer = StringBuffer();
    for (int i = 0; i < number.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write(' ');
      }
      buffer.write(number[i]);
    }
    return buffer.toString();
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = widget.backgroundColor ?? Colors.white;
    final borderColor = widget.borderColor ?? Colors.grey.shade300;
    final textColor = widget.textColor ?? Colors.black87;
    final focusColor = widget.focusColor ?? Theme.of(context).primaryColor;

    return AnimatedBuilder(
      animation: _flipAnimation,
      builder: (context, child) {
        final angle = _flipAnimation.value * 3.14159;
        return Transform(
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateY(angle),
          alignment: Alignment.center,
          child: angle < 1.57079
              ? _buildFrontCard(
                  backgroundColor, borderColor, textColor, focusColor)
              : Transform(
                  transform: Matrix4.identity()..rotateY(3.14159),
                  alignment: Alignment.center,
                  child: _buildBackCard(
                      backgroundColor, borderColor, textColor, focusColor)));
      });
  }

  /// بناء الجانب الأمامي للبطاقة
  Widget _buildFrontCard(
    Color backgroundColor,
    Color borderColor,
    Color textColor,
    Color focusColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: Border.all(
          color: borderColor,
          width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // أيقونة نوع البطاقة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'بطاقة الائتمان',
                style: GoogleFonts.cairo(
                  color: textColor.withOpacity(0.7),
                  fontSize: 14,
                  fontWeight: FontWeight.bold)),
              _buildCardTypeIcon(),
            ]),
          const SizedBox(height: 20),

          // حقل رقم البطاقة
          TextField(
            controller: _cardNumberController,
            focusNode: _cardNumberFocus,
            decoration: InputDecoration(
              labelText: 'رقم البطاقة',
              hintText: '0000 0000 0000 0000',
              labelStyle: GoogleFonts.cairo(color: textColor.withOpacity(0.7)),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: focusColor))),
            style: GoogleFonts.roboto(
              color: textColor,
              fontSize: 16,
              letterSpacing: 2),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(16),
            ],
            onChanged: (_) => widget.onCardNumberChanged(
                _cardNumberController.text.replaceAll(' ', ''))),
          const SizedBox(height: 16),

          // حقول تاريخ الانتهاء ورمز الأمان
          Row(
            children: [
              // حقل تاريخ الانتهاء
              Expanded(
                child: TextField(
                  controller: _expiryDateController,
                  focusNode: _expiryDateFocus,
                  decoration: InputDecoration(
                    labelText: 'تاريخ الانتهاء',
                    hintText: 'MM/YY',
                    labelStyle:
                        GoogleFonts.cairo(color: textColor.withOpacity(0.7)),
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: focusColor))),
                  style: GoogleFonts.roboto(
                    color: textColor,
                    fontSize: 16),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                    _ExpiryDateFormatter(),
                  ],
                  onChanged: (value) => widget.onExpiryDateChanged(value))),
              const SizedBox(width: 16),

              // حقل رمز الأمان
              Expanded(
                child: TextField(
                  controller: _cvvController,
                  focusNode: _cvvFocus,
                  decoration: InputDecoration(
                    labelText: 'CVV',
                    hintText: '000',
                    labelStyle:
                        GoogleFonts.cairo(color: textColor.withOpacity(0.7)),
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: focusColor))),
                  style: GoogleFonts.roboto(
                    color: textColor,
                    fontSize: 16),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(3),
                  ],
                  onChanged: (value) => widget.onCvvChanged(value))),
            ]),

          // حقل اسم حامل البطاقة
          if (widget.showCardHolder) ...[
            const SizedBox(height: 16),
            TextField(
              controller: _cardHolderController,
              focusNode: _cardHolderFocus,
              decoration: InputDecoration(
                labelText: 'اسم حامل البطاقة',
                hintText: 'الاسم كما يظهر على البطاقة',
                labelStyle:
                    GoogleFonts.cairo(color: textColor.withOpacity(0.7)),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: focusColor))),
              style: GoogleFonts.cairo(
                color: textColor,
                fontSize: 16),
              textCapitalization: TextCapitalization.characters,
              onChanged: (value) => widget.onCardHolderChanged(value)),
          ],
        ]));
  }

  /// بناء الجانب الخلفي للبطاقة
  Widget _buildBackCard(
    Color backgroundColor,
    Color borderColor,
    Color textColor,
    Color focusColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: Border.all(
          color: borderColor,
          width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الشريط المغناطيسي
          Container(
            height: 40,
            color: Colors.black87,
            margin: const EdgeInsets.symmetric(vertical: 16)),

          // شريط التوقيع
          Container(
            height: 40,
            color: Colors.grey.shade200,
            padding: const EdgeInsets.only(right: 10),
            alignment: Alignment.centerRight,
            child: Text(
              _cvvController.text.isEmpty ? '000' : _cvvController.text,
              style: GoogleFonts.roboto(
                color: Colors.black87,
                fontSize: 16))),

          const SizedBox(height: 16),

          // نص توضيحي
          Text(
            'رمز الأمان الموجود على ظهر البطاقة',
            style: GoogleFonts.cairo(
              color: textColor.withOpacity(0.7),
              fontSize: 12),
            textAlign: TextAlign.center),
        ]));
  }

  /// بناء أيقونة نوع البطاقة
  Widget _buildCardTypeIcon() {
    switch (_cardType) {
      case CardType.visa:
        return Image.asset(
          'assets/images/visa.png',
          height: 30,
          width: 40);
      case CardType.mastercard:
        return Image.asset(
          'assets/images/mastercard.png',
          height: 30,
          width: 40);
      case CardType.amex:
        return Image.asset(
          'assets/images/amex.png',
          height: 30,
          width: 40);
      case CardType.discover:
        return Image.asset(
          'assets/images/discover.png',
          height: 30,
          width: 40);
      case CardType.unknown:
      default:
        return Icon(
          Icons.credit_card,
          size: 30,
          color: Colors.grey.shade400);
    }
  }
}

/// أنواع بطاقات الائتمان
enum CardType {
  visa,
  mastercard,
  amex,
  discover,
  unknown,
}

/// منسق تاريخ انتهاء البطاقة
class _ExpiryDateFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue) {
    final newText = newValue.text;

    if (newText.isEmpty) {
      return newValue;
    }

    String formattedText = newText;

    if (newText.length == 3 &&
        !newText.contains('/') &&
        oldValue.text.length == 2) {
      formattedText = '${newText.substring(0, 2)}/${newText.substring(2)}';
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length));
  }
}
