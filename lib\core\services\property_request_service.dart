// lib/core/services/property_request_service.dart
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

import '../../domain/models/property_request/property_offer_model.dart';
import '../../domain/models/property_request/property_request_model.dart';
import '../../domain/repositories/property_request_repository.dart';
import 'messaging_service.dart';
import 'realtime_notification_service.dart';
import 'enhanced_cache_service.dart';

/// خدمة طلبات العقارات المحسنة مع الأمان والتخزين المؤقت
class PropertyRequestService {
  final PropertyRequestRepository _repository;
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;
  final MessagingService _messagingService;
  final RealtimeNotificationService _notificationService;
  final EnhancedCacheService _cacheService;

  // إعدادات الأمان
  static const int _maxRequestsPerHour = 50;
  static const int _maxOffersPerDay = 20;
  static const Duration _cacheExpiry = Duration(minutes: 15);

  /// إنشاء خدمة طلبات العقارات
  PropertyRequestService({
    required PropertyRequestRepository repository,
    FirebaseAuth? auth,
    FirebaseFirestore? firestore,
    MessagingService? messagingService,
    RealtimeNotificationService? notificationService,
    EnhancedCacheService? cacheService,
  })  : _repository = repository,
        _auth = auth ?? FirebaseAuth.instance,
        _firestore = firestore ?? FirebaseFirestore.instance,
        _messagingService = messagingService ?? MessagingService(
          messagingRepository: throw UnimplementedError('يجب توفير MessagingService')),
        _notificationService = notificationService ?? RealtimeNotificationService(),
        _cacheService = cacheService ?? EnhancedCacheService();

  /// الحصول على جميع طلبات العقارات مع التخزين المؤقت والأمان
  Future<List<PropertyRequestModel>> getAllPropertyRequests({
    int limit = 10,
    DocumentSnapshot? startAfter,
    String? sortBy,
    bool descending = true,
    Map<String, dynamic>? filters,
  }) async {
    try {
      // الحصول على البيانات من قاعدة البيانات مباشرة
      final requests = await _repository.getAllPropertyRequests(
        limit: limit,
        startAfter: startAfter,
        sortBy: sortBy,
        descending: descending,
        filters: filters);

      // تسجيل النشاط (اختياري)
      final user = _auth.currentUser;
      if (user != null) {
        try {
          await _logUserActivity(user.uid, 'get_property_requests', {
            'limit': limit,
            'sortBy': sortBy,
            'filters': filters,
            'result_count': requests.length,
          });
        } catch (e) {
          // تجاهل أخطاء تسجيل النشاط
        }
      }

      return requests;
    } catch (e) {
      // في حالة الخطأ، إرجاع قائمة فارغة بدلاً من رمي استثناء
      print('Error in getAllPropertyRequests: $e');
      return [];
    }
  }

  /// الحصول على طلب عقار محدد بواسطة المعرف
  Future<PropertyRequestModel?> getPropertyRequestById(String requestId) async {
    return await _repository.getPropertyRequestById(requestId);
  }

  /// إنشاء طلب عقار جديد
  Future<PropertyRequestModel> createPropertyRequest(
    PropertyRequestModel request, {
    List<File>? images,
  }) async {
    // التحقق من المستخدم الحالي
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لإنشاء طلب عقار');
    }

    // التحقق من نوع المستخدم
    final userDoc = await _firestore.collection('users').doc(user.uid).get();
    final userData = userDoc.data();

    if (userData == null) {
      throw Exception('بيانات المستخدم غير موجودة');
    }

    final userType = userData['type'];

    // التحقق من نوع المستخدم - يجب أن يكون باحث عن عقار
    bool isSeeker = userType == 0 || // UserType.seeker.index
                   userType == 1 || // للتوافق مع البيانات القديمة
                   userData['userType'] == 'seeker' ||
                   userData['userType'] == 'user' ||
                   userData['userType'] == 'property_seeker';

    if (!isSeeker) {
      throw Exception('يمكن فقط للباحثين عن عقارات إنشاء طلبات عقارية');
    }

    // إنشاء الطلب
    return await _repository.createPropertyRequest(request, images: images);
  }

  /// تحديث طلب عقار موجود
  Future<void> updatePropertyRequest(
    PropertyRequestModel request, {
    List<File>? newImages,
    List<String>? imagesToDelete,
  }) async {
    await _repository.updatePropertyRequest(
      request,
      newImages: newImages,
      imagesToDelete: imagesToDelete);
  }

  /// حذف طلب عقار
  Future<void> deletePropertyRequest(String requestId) async {
    await _repository.deletePropertyRequest(requestId);
  }

  /// تحديث حالة طلب عقار
  Future<void> updatePropertyRequestStatus(String requestId, RequestStatus status) async {
    await _repository.updatePropertyRequestStatus(requestId, status);
  }

  /// زيادة عدد مشاهدات طلب عقار
  Future<void> incrementPropertyRequestViews(String requestId) async {
    await _repository.incrementPropertyRequestViews(requestId);
  }

  /// الحصول على طلبات العقارات الخاصة بمستخدم محدد
  Future<List<PropertyRequestModel>> getUserPropertyRequests(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  }) async {
    return await _repository.getUserPropertyRequests(
      userId,
      limit: limit,
      startAfter: startAfter);
  }

  /// الحصول على طلبات العقارات الأكثر مشاهدة
  Future<List<PropertyRequestModel>> getMostViewedPropertyRequests({int limit = 10}) async {
    return await _repository.getMostViewedPropertyRequests(limit: limit);
  }

  /// الحصول على طلبات العقارات الأحدث
  Future<List<PropertyRequestModel>> getLatestPropertyRequests({int limit = 10}) async {
    return await _repository.getLatestPropertyRequests(limit: limit);
  }

  /// البحث في طلبات العقارات
  Future<List<PropertyRequestModel>> searchPropertyRequests(
    String query, {
    int limit = 20,
    Map<String, dynamic>? filters,
  }) async {
    return await _repository.searchPropertyRequests(
      query,
      limit: limit,
      filters: filters);
  }

  /// الحصول على عروض طلب عقار محدد
  Future<List<PropertyOfferModel>> getOffersByRequest(
    String requestId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    return await _repository.getOffersByRequest(
      requestId,
      limit: limit,
      startAfter: startAfter);
  }

  /// الحصول على عرض محدد بواسطة المعرف
  Future<PropertyOfferModel?> getOfferById(String offerId) async {
    return await _repository.getOfferById(offerId);
  }

  /// إنشاء عرض جديد
  Future<PropertyOfferModel> createOffer(
    PropertyOfferModel offer, {
    List<File>? images,
  }) async {
    // التحقق من المستخدم الحالي
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لإنشاء عرض');
    }

    // التحقق من نوع المستخدم
    final userDoc = await _firestore.collection('users').doc(user.uid).get();
    final userData = userDoc.data();

    if (userData == null) {
      throw Exception('بيانات المستخدم غير موجودة');
    }

    final userType = userData['type'];

    // التحقق من نوع المستخدم - لا يجب أن يكون باحث عن عقار
    bool isSeeker = userType == 0 || // UserType.seeker.index
                   userData['userType'] == 'seeker' ||
                   userData['userType'] == 'user' ||
                   userData['userType'] == 'property_seeker';

    if (isSeeker) {
      throw Exception('لا يمكن للباحثين عن عقارات إنشاء عروض');
    }

    // الحصول على طلب العقار
    final request = await _repository.getPropertyRequestById(offer.requestId);
    if (request == null) {
      throw Exception('طلب العقار غير موجود');
    }

    // إنشاء العرض
    final createdOffer = await _repository.createOffer(offer, images: images);

    // إرسال إشعار لصاحب الطلب
    await _notificationService.sendNotification(
      recipientId: request.userId,
      type: RealtimeNotificationType.newPropertyOffer,
      title: 'عرض جديد على طلبك',
      body: 'تم إضافة عرض جديد على طلبك: ${request.title}',
      data: {
        'requestId': request.id,
        'offerId': createdOffer.id,
        'offerTitle': createdOffer.title,
        'offerPrice': createdOffer.price.toString(),
        'offerUserId': createdOffer.userId,
        'offerUserName': createdOffer.userName,
      });

    return createdOffer;
  }

  /// تحديث عرض موجود
  Future<void> updateOffer(
    PropertyOfferModel offer, {
    List<File>? newImages,
    List<String>? imagesToDelete,
  }) async {
    await _repository.updateOffer(
      offer,
      newImages: newImages,
      imagesToDelete: imagesToDelete);
  }

  /// حذف عرض
  Future<void> deleteOffer(String offerId) async {
    await _repository.deleteOffer(offerId);
  }

  /// تحديث حالة عرض
  Future<void> updateOfferStatus(String offerId, OfferStatus status) async {
    await _repository.updateOfferStatus(offerId, status);
  }

  /// قبول عرض
  Future<void> acceptOffer(String offerId, String requestId) async {
    // التحقق من المستخدم الحالي
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لقبول العرض');
    }

    // الحصول على طلب العقار
    final request = await _repository.getPropertyRequestById(requestId);
    if (request == null) {
      throw Exception('طلب العقار غير موجود');
    }

    // التحقق من أن المستخدم هو صاحب الطلب
    if (user.uid != request.userId) {
      throw Exception('لا يمكنك قبول عرض على طلب عقار لا تملكه');
    }

    // الحصول على العرض
    final offer = await _repository.getOfferById(offerId);
    if (offer == null) {
      throw Exception('العرض غير موجود');
    }

    // تحديث حالة العرض
    await _repository.updateOfferStatus(offerId, OfferStatus.accepted);

    // تحديث حالة طلب العقار
    await _repository.updatePropertyRequestStatus(requestId, RequestStatus.resolved);

    // إنشاء محادثة بين صاحب الطلب وصاحب العرض
    final conversationId = await _messagingService.createConversationForPropertyOffer(
      requestId,
      offerId,
      offer.userId,
      request.userId,
      request.title,
      offer.title);

    // إرسال إشعار لصاحب العرض
    await _notificationService.sendNotification(
      recipientId: offer.userId,
      type: RealtimeNotificationType.propertyOfferAccepted,
      title: 'تم قبول عرضك',
      body: 'تم قبول عرضك على طلب: ${request.title}',
      data: {
        'requestId': requestId,
        'offerId': offerId,
        'requestTitle': request.title,
        'conversationId': conversationId,
        'requestUserId': request.userId,
        'requestUserName': request.userName,
      });
  }

  /// رفض عرض
  Future<void> rejectOffer(String offerId, String requestId) async {
    // التحقق من المستخدم الحالي
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لرفض العرض');
    }

    // الحصول على طلب العقار
    final request = await _repository.getPropertyRequestById(requestId);
    if (request == null) {
      throw Exception('طلب العقار غير موجود');
    }

    // التحقق من أن المستخدم هو صاحب الطلب
    if (user.uid != request.userId) {
      throw Exception('لا يمكنك رفض عرض على طلب عقار لا تملكه');
    }

    // الحصول على العرض
    final offer = await _repository.getOfferById(offerId);
    if (offer == null) {
      throw Exception('العرض غير موجود');
    }

    // تحديث حالة العرض
    await _repository.updateOfferStatus(offerId, OfferStatus.rejected);

    // إرسال إشعار لصاحب العرض
    await _notificationService.sendNotification(
      recipientId: offer.userId,
      type: RealtimeNotificationType.propertyOfferRejected,
      title: 'تم رفض عرضك',
      body: 'تم رفض عرضك على طلب: ${request.title}',
      data: {
        'requestId': requestId,
        'offerId': offerId,
        'requestTitle': request.title,
        'requestUserId': request.userId,
        'requestUserName': request.userName,
      });
  }

  /// الحصول على العروض الخاصة بمستخدم محدد
  Future<List<PropertyOfferModel>> getUserOffers(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  }) async {
    return await _repository.getUserOffers(
      userId,
      limit: limit,
      startAfter: startAfter);
  }

  /// الاستماع للتغييرات في طلبات العقارات
  Stream<List<PropertyRequestModel>> listenToPropertyRequests({
    int limit = 10,
    String? sortBy,
    bool descending = true,
    Map<String, dynamic>? filters,
  }) {
    return _repository.listenToPropertyRequests(
      limit: limit,
      sortBy: sortBy,
      descending: descending,
      filters: filters);
  }

  /// الاستماع للتغييرات في طلب عقار محدد
  Stream<PropertyRequestModel?> listenToPropertyRequest(String requestId) {
    return _repository.listenToPropertyRequest(requestId);
  }

  /// الاستماع للتغييرات في عروض طلب عقار محدد
  Stream<List<PropertyOfferModel>> listenToRequestOffers(
    String requestId, {
    int limit = 20,
  }) {
    return _repository.listenToRequestOffers(
      requestId,
      limit: limit);
  }

  /// الاستماع للتغييرات في عرض محدد
  Stream<PropertyOfferModel?> listenToOffer(String offerId) {
    return _repository.listenToOffer(offerId);
  }

  // ===== دوال الأمان والتخزين المؤقت المساعدة =====

  /// التحقق من معدل الطلبات
  Future<bool> _checkRateLimit(String userId, String action) async {
    try {
      final now = DateTime.now();
      final windowStart = now.subtract(const Duration(hours: 1));

      final query = await _firestore
          .collection('rate_limits')
          .where('userId', isEqualTo: userId)
          .where('action', isEqualTo: action)
          .where('timestamp', isGreaterThan: windowStart)
          .get();

      if (query.docs.length >= _maxRequestsPerHour) {
        return false;
      }

      // تسجيل الطلب الحالي
      await _firestore.collection('rate_limits').add({
        'userId': userId,
        'action': action,
        'timestamp': DateTime.now(),
      });

      return true;
    } catch (e) {
      // في حالة الخطأ، نسمح بالعملية
      return true;
    }
  }

  /// توليد مفتاح التخزين المؤقت
  String _generateCacheKey(String prefix, Map<String, dynamic> params) {
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key)));

    final paramString = sortedParams.entries
        .where((entry) => entry.value != null)
        .map((entry) => '${entry.key}:${entry.value}')
        .join('|');

    final combined = '$prefix|$paramString';

    // إنشاء hash للمفتاح الطويل
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);

    return 'pr_${digest.toString().substring(0, 16)}';
  }

  /// تسجيل نشاط المستخدم
  Future<void> _logUserActivity(String userId, String action, Map<String, dynamic> details) async {
    try {
      await _firestore.collection('user_activity_logs').add({
        'userId': userId,
        'action': action,
        'details': details,
        'timestamp': DateTime.now(),
        'service': 'PropertyRequestService',
      });
    } catch (e) {
      // تسجيل الخطأ دون إيقاف العملية الأساسية
      print('فشل في تسجيل نشاط المستخدم: $e');
    }
  }

  /// تنظيف سجلات معدل الطلبات القديمة
  Future<void> cleanupOldRateLimitRecords() async {
    try {
      final cutoffTime = DateTime.now().subtract(const Duration(hours: 24));
      final query = await _firestore
          .collection('rate_limits')
          .where('timestamp', isLessThan: cutoffTime)
          .get();

      final batch = _firestore.batch();
      for (final doc in query.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      print('فشل في تنظيف سجلات معدل الطلبات: $e');
    }
  }

  /// تنظيف التخزين المؤقت
  Future<void> clearCache() async {
    try {
      await _cacheService.clearAll();
    } catch (e) {
      print('فشل في تنظيف التخزين المؤقت: $e');
    }
  }

  /// الحصول على إحصائيات الأداء
  Future<Map<String, dynamic>> getPerformanceStats() async {
    try {
      final cacheSize = await _cacheService.getCacheSize();

      return {
        'cache_size_bytes': cacheSize,
        'cache_service_initialized': _cacheService != null,
        'max_requests_per_hour': _maxRequestsPerHour,
        'max_offers_per_day': _maxOffersPerDay,
        'cache_expiry_minutes': _cacheExpiry.inMinutes,
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }
}
