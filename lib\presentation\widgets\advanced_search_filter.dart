import 'package:flutter/material.dart';

/// نموذج بيانات فلتر البحث المتقدم
class SearchFilterModel {
  /// نوع العقار (شقة، منزل، أرض، إلخ)
  String? propertyType;

  /// نوع الإعلان (بيع، إيجار)
  String? adType;

  /// المحافظة
  String? governorate;

  /// المنطقة
  String? area;

  /// الحد الأدنى للسعر
  double? minPrice;

  /// الحد الأقصى للسعر
  double? maxPrice;

  /// الحد الأدنى للمساحة
  double? minArea;

  /// الحد الأقصى للمساحة
  double? maxArea;

  /// الحد الأدنى لعدد الغرف
  int? minRooms;

  /// الحد الأقصى لعدد الغرف
  int? maxRooms;

  /// الحد الأدنى لعدد الحمامات
  int? minBathrooms;

  /// الحد الأقصى لعدد الحمامات
  int? maxBathrooms;

  /// ميزات إضافية
  Map<String, bool> features = {};

  SearchFilterModel({
    this.propertyType,
    this.adType,
    this.governorate,
    this.area,
    this.minPrice,
    this.maxPrice,
    this.minArea,
    this.maxArea,
    this.minRooms,
    this.maxRooms,
    this.minBathrooms,
    this.maxBathrooms,
    Map<String, bool>? features,
  }) {
    if (features != null) {
      this.features = features;
    }
  }

  /// نسخ النموذج مع تعديل بعض القيم
  SearchFilterModel copyWith({
    String? propertyType,
    String? adType,
    String? governorate,
    String? area,
    double? minPrice,
    double? maxPrice,
    double? minArea,
    double? maxArea,
    int? minRooms,
    int? maxRooms,
    int? minBathrooms,
    int? maxBathrooms,
    Map<String, bool>? features,
  }) {
    return SearchFilterModel(
      propertyType: propertyType ?? this.propertyType,
      adType: adType ?? this.adType,
      governorate: governorate ?? this.governorate,
      area: area ?? this.area,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      minArea: minArea ?? this.minArea,
      maxArea: maxArea ?? this.maxArea,
      minRooms: minRooms ?? this.minRooms,
      maxRooms: maxRooms ?? this.maxRooms,
      minBathrooms: minBathrooms ?? this.minBathrooms,
      maxBathrooms: maxBathrooms ?? this.maxBathrooms,
      features: features ?? Map.from(this.features));
  }

  /// إعادة تعيين جميع القيم
  void reset() {
    propertyType = null;
    adType = null;
    governorate = null;
    area = null;
    minPrice = null;
    maxPrice = null;
    minArea = null;
    maxArea = null;
    minRooms = null;
    maxRooms = null;
    minBathrooms = null;
    maxBathrooms = null;
    features.clear();
  }
}

/// مكون فلتر البحث المتقدم
class AdvancedSearchFilter extends StatefulWidget {
  /// نموذج بيانات الفلتر الحالي
  final SearchFilterModel initialFilter;

  /// دالة يتم استدعاؤها عند تطبيق الفلتر
  final Function(SearchFilterModel) onApplyFilter;

  /// قائمة بأنواع العقارات المتاحة
  final List<String> propertyTypes;

  /// قائمة بأنواع الإعلانات المتاحة
  final List<String> adTypes;

  /// قائمة بالمحافظات المتاحة
  final List<String> governorates;

  /// قائمة بالمناطق المتاحة (يمكن أن تتغير بناءً على المحافظة المختارة)
  final List<String> areas;

  /// قائمة بالميزات الإضافية المتاحة
  final Map<String, String> availableFeatures;

  const AdvancedSearchFilter({
    super.key,
    required this.initialFilter,
    required this.onApplyFilter,
    required this.propertyTypes,
    required this.adTypes,
    required this.governorates,
    required this.areas,
    required this.availableFeatures,
  });

  @override
  State<AdvancedSearchFilter> createState() => _AdvancedSearchFilterState();
}

class _AdvancedSearchFilterState extends State<AdvancedSearchFilter> {
  late SearchFilterModel _filter;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _filter = widget.initialFilter.copyWith();

    // إضافة الميزات المتاحة إلى الفلتر إذا لم تكن موجودة
    for (final feature in widget.availableFeatures.keys) {
      if (!_filter.features.containsKey(feature)) {
        _filter.features[feature] = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("بحث متقدم"),
        actions: [
          // زر إعادة تعيين الفلتر
          TextButton(
            onPressed: () {
              setState(() {
                _filter.reset();
              });
            },
            child: const Text("إعادة تعيين")),
        ]),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // قسم نوع العقار ونوع الإعلان
            _buildSectionTitle("نوع العقار والإعلان"),
            _buildPropertyTypeSection(),
            const SizedBox(height: 16),

            // قسم الموقع
            _buildSectionTitle("الموقع"),
            _buildLocationSection(),
            const SizedBox(height: 16),

            // قسم السعر
            _buildSectionTitle("السعر"),
            _buildPriceSection(),
            const SizedBox(height: 16),

            // قسم المواصفات
            _buildSectionTitle("المواصفات"),
            _buildSpecificationsSection(),
            const SizedBox(height: 16),

            // قسم الميزات الإضافية
            _buildSectionTitle("الميزات الإضافية"),
            _buildFeaturesSection(),
            const SizedBox(height: 24),

            // زر تطبيق الفلتر
            ElevatedButton(
              onPressed: _applyFilter,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black87,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8))),
              child: const Text(
                "تطبيق الفلتر",
                style: TextStyle(fontSize: 16))),
          ])));
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black87)));
  }

  /// بناء قسم نوع العقار ونوع الإعلان
  Widget _buildPropertyTypeSection() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // نوع العقار
            const Text(
              "نوع العقار",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _filter.propertyType,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                hintText: "اختر نوع العقار"),
              items: widget.propertyTypes.map((type) {
                return DropdownMenuItem<String>(
                  value: type,
                  child: Text(type));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _filter.propertyType = value;
                });
              }),

            const SizedBox(height: 16),

            // نوع الإعلان
            const Text(
              "نوع الإعلان",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: widget.adTypes.map((type) {
                return Expanded(
                  child: RadioListTile<String>(
                    title: Text(type),
                    value: type,
                    groupValue: _filter.adType,
                    onChanged: (value) {
                      setState(() {
                        _filter.adType = value;
                      });
                    },
                    contentPadding: EdgeInsets.zero,
                    dense: true));
              }).toList()),
          ])));
  }

  /// بناء قسم الموقع
  Widget _buildLocationSection() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // المحافظة
            const Text(
              "المحافظة",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _filter.governorate,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                hintText: "اختر المحافظة"),
              items: widget.governorates.map((governorate) {
                return DropdownMenuItem<String>(
                  value: governorate,
                  child: Text(governorate));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _filter.governorate = value;
                  // إعادة تعيين المنطقة عند تغيير المحافظة
                  _filter.area = null;
                });
              }),

            const SizedBox(height: 16),

            // المنطقة
            const Text(
              "المنطقة",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _filter.area,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                hintText: "اختر المنطقة"),
              items: widget.areas.map((area) {
                return DropdownMenuItem<String>(
                  value: area,
                  child: Text(area));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _filter.area = value;
                });
              }),
          ])));
  }

  /// بناء قسم السعر
  Widget _buildPriceSection() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // نطاق السعر
            const Text(
              "نطاق السعر (د.ك)",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                // الحد الأدنى للسعر
                Expanded(
                  child: TextFormField(
                    initialValue: _filter.minPrice?.toString() ?? '',
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      hintText: "من"),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _filter.minPrice = value.isEmpty ? null : double.tryParse(value);
                      });
                    })),

                const SizedBox(width: 16),

                // الحد الأقصى للسعر
                Expanded(
                  child: TextFormField(
                    initialValue: _filter.maxPrice?.toString() ?? '',
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      hintText: "إلى"),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _filter.maxPrice = value.isEmpty ? null : double.tryParse(value);
                      });
                    })),
              ]),
          ])));
  }

  /// بناء قسم المواصفات
  Widget _buildSpecificationsSection() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // المساحة
            const Text(
              "المساحة (م²)",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                // الحد الأدنى للمساحة
                Expanded(
                  child: TextFormField(
                    initialValue: _filter.minArea?.toString() ?? '',
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      hintText: "من"),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _filter.minArea = value.isEmpty ? null : double.tryParse(value);
                      });
                    })),

                const SizedBox(width: 16),

                // الحد الأقصى للمساحة
                Expanded(
                  child: TextFormField(
                    initialValue: _filter.maxArea?.toString() ?? '',
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      hintText: "إلى"),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _filter.maxArea = value.isEmpty ? null : double.tryParse(value);
                      });
                    })),
              ]),

            const SizedBox(height: 16),

            // عدد الغرف
            const Text(
              "عدد الغرف",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                // الحد الأدنى لعدد الغرف
                Expanded(
                  child: TextFormField(
                    initialValue: _filter.minRooms?.toString() ?? '',
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      hintText: "من"),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _filter.minRooms = value.isEmpty ? null : int.tryParse(value);
                      });
                    })),

                const SizedBox(width: 16),

                // الحد الأقصى لعدد الغرف
                Expanded(
                  child: TextFormField(
                    initialValue: _filter.maxRooms?.toString() ?? '',
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      hintText: "إلى"),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _filter.maxRooms = value.isEmpty ? null : int.tryParse(value);
                      });
                    })),
              ]),

            const SizedBox(height: 16),

            // عدد الحمامات
            const Text(
              "عدد الحمامات",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                // الحد الأدنى لعدد الحمامات
                Expanded(
                  child: TextFormField(
                    initialValue: _filter.minBathrooms?.toString() ?? '',
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      hintText: "من"),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _filter.minBathrooms = value.isEmpty ? null : int.tryParse(value);
                      });
                    })),

                const SizedBox(width: 16),

                // الحد الأقصى لعدد الحمامات
                Expanded(
                  child: TextFormField(
                    initialValue: _filter.maxBathrooms?.toString() ?? '',
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      hintText: "إلى"),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        _filter.maxBathrooms = value.isEmpty ? null : int.tryParse(value);
                      });
                    })),
              ]),
          ])));
  }

  /// بناء قسم الميزات الإضافية
  Widget _buildFeaturesSection() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: widget.availableFeatures.entries.map((entry) {
            final featureKey = entry.key;
            final featureLabel = entry.value;

            return CheckboxListTile(
              title: Text(featureLabel),
              value: _filter.features[featureKey] ?? false,
              onChanged: (value) {
                setState(() {
                  _filter.features[featureKey] = value ?? false;
                });
              },
              contentPadding: EdgeInsets.zero,
              dense: true,
              controlAffinity: ListTileControlAffinity.leading);
          }).toList())));
  }

  /// تطبيق الفلتر
  void _applyFilter() {
    if (_formKey.currentState!.validate()) {
      widget.onApplyFilter(_filter);
      Navigator.pop(context);
    }
  }
}
