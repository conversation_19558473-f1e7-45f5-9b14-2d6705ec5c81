// lib/domain/models/rating/user_rating_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نموذج تقييم المستخدم
class UserRatingModel extends Equatable {
  /// معرف التقييم
  final String id;

  /// معرف المستخدم المُقيِّم
  final String reviewerId;

  /// اسم المستخدم المُقيِّم
  final String reviewerName;

  /// صورة المستخدم المُقيِّم
  final String? reviewerImage;

  /// معرف المستخدم المُقيَّم
  final String targetUserId;

  /// اسم المستخدم المُقيَّم
  final String targetUserName;

  /// التقييم العام (1-5)
  final double overallRating;

  /// تقييم التواصل (1-5)
  final double communicationRating;

  /// تقييم الموثوقية (1-5)
  final double reliabilityRating;

  /// تقييم الاحترافية (1-5)
  final double professionalismRating;

  /// تقييم سرعة الاستجابة (1-5)
  final double responsivenessRating;

  /// نص المراجعة
  final String reviewText;

  /// معرف الطلب أو العرض المرتبط
  final String? relatedRequestId;

  /// معرف العقار المرتبط
  final String? relatedPropertyId;

  /// نوع التفاعل
  final UserInteractionType interactionType;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// تاريخ التحديث
  final DateTime updatedAt;

  /// هل التقييم مفيد (عدد الإعجابات)
  final int helpfulCount;

  /// هل تم الإبلاغ عن التقييم
  final bool isReported;

  /// هل التقييم معتمد من الإدارة
  final bool isVerified;

  /// رد المستخدم المُقيَّم
  final String? ownerResponse;

  /// تاريخ رد المالك
  final DateTime? ownerResponseDate;

  /// علامات إضافية
  final List<String>? tags;

  const UserRatingModel({
    required this.id,
    required this.reviewerId,
    required this.reviewerName,
    this.reviewerImage,
    required this.targetUserId,
    required this.targetUserName,
    required this.overallRating,
    required this.communicationRating,
    required this.reliabilityRating,
    required this.professionalismRating,
    required this.responsivenessRating,
    required this.reviewText,
    this.relatedRequestId,
    this.relatedPropertyId,
    required this.interactionType,
    required this.createdAt,
    required this.updatedAt,
    this.helpfulCount = 0,
    this.isReported = false,
    this.isVerified = false,
    this.ownerResponse,
    this.ownerResponseDate,
    this.tags,
  });

  /// إنشاء من Firestore
  factory UserRatingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserRatingModel(
      id: doc.id,
      reviewerId: data['reviewerId'] ?? '',
      reviewerName: data['reviewerName'] ?? '',
      reviewerImage: data['reviewerImage'],
      targetUserId: data['targetUserId'] ?? '',
      targetUserName: data['targetUserName'] ?? '',
      overallRating: (data['overallRating'] ?? 0.0).toDouble(),
      communicationRating: (data['communicationRating'] ?? 0.0).toDouble(),
      reliabilityRating: (data['reliabilityRating'] ?? 0.0).toDouble(),
      professionalismRating: (data['professionalismRating'] ?? 0.0).toDouble(),
      responsivenessRating: (data['responsivenessRating'] ?? 0.0).toDouble(),
      reviewText: data['reviewText'] ?? '',
      relatedRequestId: data['relatedRequestId'],
      relatedPropertyId: data['relatedPropertyId'],
      interactionType: UserInteractionType.values[data['interactionType'] ?? 0],
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      helpfulCount: data['helpfulCount'] ?? 0,
      isReported: data['isReported'] ?? false,
      isVerified: data['isVerified'] ?? false,
      ownerResponse: data['ownerResponse'],
      ownerResponseDate: (data['ownerResponseDate'] as Timestamp?)?.toDate(),
      tags: data['tags'] != null ? List<String>.from(data['tags']) : null,
    );
  }

  /// تحويل إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'reviewerId': reviewerId,
      'reviewerName': reviewerName,
      'reviewerImage': reviewerImage,
      'targetUserId': targetUserId,
      'targetUserName': targetUserName,
      'overallRating': overallRating,
      'communicationRating': communicationRating,
      'reliabilityRating': reliabilityRating,
      'professionalismRating': professionalismRating,
      'responsivenessRating': responsivenessRating,
      'reviewText': reviewText,
      'relatedRequestId': relatedRequestId,
      'relatedPropertyId': relatedPropertyId,
      'interactionType': interactionType.index,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'helpfulCount': helpfulCount,
      'isReported': isReported,
      'isVerified': isVerified,
      'ownerResponse': ownerResponse,
      'ownerResponseDate': ownerResponseDate != null 
          ? Timestamp.fromDate(ownerResponseDate!)
          : null,
      'tags': tags,
    };
  }

  /// إنشاء نسخة محدثة
  UserRatingModel copyWith({
    String? id,
    String? reviewerId,
    String? reviewerName,
    String? reviewerImage,
    String? targetUserId,
    String? targetUserName,
    double? overallRating,
    double? communicationRating,
    double? reliabilityRating,
    double? professionalismRating,
    double? responsivenessRating,
    String? reviewText,
    String? relatedRequestId,
    String? relatedPropertyId,
    UserInteractionType? interactionType,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? helpfulCount,
    bool? isReported,
    bool? isVerified,
    String? ownerResponse,
    DateTime? ownerResponseDate,
    List<String>? tags,
  }) {
    return UserRatingModel(
      id: id ?? this.id,
      reviewerId: reviewerId ?? this.reviewerId,
      reviewerName: reviewerName ?? this.reviewerName,
      reviewerImage: reviewerImage ?? this.reviewerImage,
      targetUserId: targetUserId ?? this.targetUserId,
      targetUserName: targetUserName ?? this.targetUserName,
      overallRating: overallRating ?? this.overallRating,
      communicationRating: communicationRating ?? this.communicationRating,
      reliabilityRating: reliabilityRating ?? this.reliabilityRating,
      professionalismRating: professionalismRating ?? this.professionalismRating,
      responsivenessRating: responsivenessRating ?? this.responsivenessRating,
      reviewText: reviewText ?? this.reviewText,
      relatedRequestId: relatedRequestId ?? this.relatedRequestId,
      relatedPropertyId: relatedPropertyId ?? this.relatedPropertyId,
      interactionType: interactionType ?? this.interactionType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      isReported: isReported ?? this.isReported,
      isVerified: isVerified ?? this.isVerified,
      ownerResponse: ownerResponse ?? this.ownerResponse,
      ownerResponseDate: ownerResponseDate ?? this.ownerResponseDate,
      tags: tags ?? this.tags,
    );
  }

  /// حساب متوسط التقييمات الفرعية
  double get averageSubRating {
    return (communicationRating + reliabilityRating + 
            professionalismRating + responsivenessRating) / 4;
  }

  /// التحقق من صحة التقييم
  bool get isValid {
    return overallRating >= 1 && overallRating <= 5 &&
           communicationRating >= 1 && communicationRating <= 5 &&
           reliabilityRating >= 1 && reliabilityRating <= 5 &&
           professionalismRating >= 1 && professionalismRating <= 5 &&
           responsivenessRating >= 1 && responsivenessRating <= 5 &&
           reviewText.isNotEmpty &&
           reviewerId.isNotEmpty &&
           targetUserId.isNotEmpty;
  }

  @override
  List<Object?> get props => [
        id,
        reviewerId,
        reviewerName,
        reviewerImage,
        targetUserId,
        targetUserName,
        overallRating,
        communicationRating,
        reliabilityRating,
        professionalismRating,
        responsivenessRating,
        reviewText,
        relatedRequestId,
        relatedPropertyId,
        interactionType,
        createdAt,
        updatedAt,
        helpfulCount,
        isReported,
        isVerified,
        ownerResponse,
        ownerResponseDate,
        tags,
      ];
}

/// أنواع التفاعل بين المستخدمين
enum UserInteractionType {
  /// تفاعل حول طلب عقار
  propertyRequest,
  
  /// تفاعل حول عرض عقار
  propertyOffer,
  
  /// تفاعل حول عقار مباشر
  directProperty,
  
  /// تفاعل عام
  general,
}
