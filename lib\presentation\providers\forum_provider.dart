import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

import '../../core/services/forum_service.dart';
import '../../core/services/forum_activity_tracker.dart';
import '../../domain/models/forum/category_model.dart';
import '../../domain/models/forum/filter_options_model.dart';
import '../../domain/models/forum/notification_model.dart';
import '../../domain/models/forum/poll_model.dart';
import '../../domain/models/forum/post_model.dart';
import '../../domain/models/forum/reaction_model.dart';
import '../../domain/models/forum/topic_model.dart';
import '../../domain/models/forum/user_statistics_model.dart';
import '../../domain/models/forum/badge_model.dart';
import '../../domain/models/forum/achievement_model.dart';
import '../../domain/models/forum/report_model.dart';
import 'auth_provider.dart' as app_auth;

/// حالة تحميل البيانات
enum LoadingState {
  /// جاري التحميل
  loading,

  /// تم التحميل بنجاح
  loaded,

  /// حدث خطأ
  error,

  /// لا توجد بيانات
  empty,
}

/// مزود اللوبي
class ForumProvider extends ChangeNotifier {
  final ForumService _forumService;
  final ForumActivityTracker? _activityTracker;
  final app_auth.AuthProvider? _authProvider;

  ForumProvider({
    required ForumService forumService,
    ForumActivityTracker? activityTracker,
    app_auth.AuthProvider? authProvider,
  })  : _forumService = forumService,
        _activityTracker = activityTracker,
        _authProvider = authProvider;

  /// الحصول على خدمة المنتدى
  ForumService get forumService => _forumService;

  // حالة التحميل
  LoadingState _categoriesState = LoadingState.loading;
  LoadingState _topicsState = LoadingState.loading;
  LoadingState _topicState = LoadingState.loading;
  LoadingState _postsState = LoadingState.loading;
  LoadingState _userStatisticsState = LoadingState.loading;
  LoadingState _notificationsState = LoadingState.loading;

  // البيانات
  List<CategoryModel> _categories = [];
  List<TopicModel> _topics = [];
  TopicModel? _currentTopic;
  List<PostModel> _posts = [];
  UserStatisticsModel? _userStatistics;
  List<NotificationModel> _notifications = [];
  int _unreadNotificationsCount = 0;
  Map<String, dynamic> _forumStatistics = {};

  // حالة إحصائيات اللوبي
  LoadingState _forumStatisticsState = LoadingState.loading;

  // الصفحات
  DocumentSnapshot? _lastTopicDocument;
  DocumentSnapshot? _lastPostDocument;
  DocumentSnapshot? _lastNotificationDocument;
  bool _hasMoreTopics = true;
  bool _hasMorePosts = true;
  bool _hasMoreNotifications = true;

  // الفلاتر والترتيب
  String? _selectedCategoryId;
  String? _sortBy;
  bool _descending = true;
  String _searchQuery = '';
  ForumFilterOptionsModel? _filterOptions;
  String _viewMode = 'list'; // 'list' or 'grid'

  // الحصول على البيانات
  LoadingState get categoriesState => _categoriesState;
  LoadingState get topicsState => _topicsState;
  LoadingState get topicState => _topicState;
  LoadingState get postsState => _postsState;
  LoadingState get userStatisticsState => _userStatisticsState;
  LoadingState get notificationsState => _notificationsState;
  LoadingState get forumStatisticsState => _forumStatisticsState;

  List<CategoryModel> get categories => _categories;
  List<TopicModel> get topics => _topics;
  TopicModel? get currentTopic => _currentTopic;
  List<PostModel> get posts => _posts;
  UserStatisticsModel? get userStatistics => _userStatistics;
  List<NotificationModel> get notifications => _notifications;
  int get unreadNotificationsCount => _unreadNotificationsCount;
  Map<String, dynamic> get forumStatistics => _forumStatistics;

  bool get hasMoreTopics => _hasMoreTopics;
  bool get hasMorePosts => _hasMorePosts;
  bool get hasMoreNotifications => _hasMoreNotifications;

  String? get selectedCategoryId => _selectedCategoryId;
  String? get sortBy => _sortBy;
  bool get descending => _descending;
  String get searchQuery => _searchQuery;
  ForumFilterOptionsModel? get filterOptions => _filterOptions;
  String get viewMode => _viewMode;

  // تحديث الفلاتر والترتيب
  void setSelectedCategory(String? categoryId) {
    _selectedCategoryId = categoryId;
    _lastTopicDocument = null;
    _hasMoreTopics = true;
    _topics = [];
    _topicsState = LoadingState.loading;
    notifyListeners();
    fetchTopics();
  }

  void setSortBy(String? sortBy, {bool descending = true}) {
    _sortBy = sortBy;
    _descending = descending;
    _lastTopicDocument = null;
    _hasMoreTopics = true;
    _topics = [];
    _topicsState = LoadingState.loading;
    notifyListeners();
    fetchTopics();
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void setFilterOptions(ForumFilterOptionsModel options) {
    _filterOptions = options;
    _lastTopicDocument = null;
    _hasMoreTopics = true;
    _topics = [];
    _topicsState = LoadingState.loading;
    notifyListeners();
  }

  void setViewMode(String mode) {
    if (mode == 'list' || mode == 'grid') {
      _viewMode = mode;
      notifyListeners();
    }
  }

  /// مشاركة موضوع
  Future<bool> shareTopic(String topicId, String shareMethod) async {
    try {
      // تسجيل نشاط مشاركة موضوع
      _activityTracker?.trackActivity(
        ForumActivityType.shareTopic,
        itemId: topicId,
        additionalData: {
          'shareMethod': shareMethod,
        });
      return true;
    } catch (e) {
      return false;
    }
  }

  /// حفظ موضوع
  Future<bool> bookmarkTopic(String topicId, String userId) async {
    try {
      await _forumService.bookmarkTopic(topicId, userId);

      // تسجيل نشاط حفظ موضوع
      _activityTracker?.trackActivity(
        ForumActivityType.bookmarkTopic,
        itemId: topicId);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء حفظ موضوع
  Future<bool> unbookmarkTopic(String topicId, String userId) async {
    try {
      await _forumService.unbookmarkTopic(topicId, userId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// متابعة موضوع
  Future<bool> followTopic(String topicId, String userId) async {
    try {
      await _forumService.followTopic(topicId, userId);

      // تسجيل نشاط متابعة موضوع
      _activityTracker?.trackActivity(
        ForumActivityType.followTopic,
        itemId: topicId);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء متابعة موضوع
  Future<bool> unfollowTopic(String topicId, String userId) async {
    try {
      await _forumService.unfollowTopic(topicId, userId);
      return true;
    } catch (e) {
      return false;
    }
  }

  // فئات اللوبي

  /// جلب فئات اللوبي
  Future<void> fetchCategories() async {
    try {
      _categoriesState = LoadingState.loading;
      notifyListeners();

      final categories = await _forumService.getCategories();

      _categories = categories;
      _categoriesState =
          categories.isEmpty ? LoadingState.empty : LoadingState.loaded;
    } catch (e) {
      _categoriesState = LoadingState.error;
    } finally {
      notifyListeners();
    }
  }

  /// الاستماع لتغييرات فئات اللوبي
  void listenToCategories() {
    _forumService.listenToCategories().listen((categories) {
      _categories = categories;
      _categoriesState =
          categories.isEmpty ? LoadingState.empty : LoadingState.loaded;
      notifyListeners();
    }, onError: (e) {
      _categoriesState = LoadingState.error;
      notifyListeners();
    });
  }

  // مواضيع اللوبي

  /// جلب مواضيع اللوبي
  Future<void> fetchTopics({bool refresh = false}) async {
    if (refresh) {
      _lastTopicDocument = null;
      _hasMoreTopics = true;
      _topics = [];
    }

    if (!_hasMoreTopics) return;

    try {
      _topicsState = _topics.isEmpty ? LoadingState.loading : _topicsState;
      notifyListeners();

      List<TopicModel> newTopics = [];

      if (_selectedCategoryId != null) {
        // جلب المواضيع حسب الفئة المحددة
        final queryResult = await _forumService.getTopicsByCategory(
          _selectedCategoryId!,
          limit: 10,
          startAfter: _lastTopicDocument,
          sortBy: _sortBy,
          descending: _descending);

        // تحديث المواضيع الجديدة
        newTopics = queryResult;

        // تحديث آخر وثيقة للصفحة التالية إذا كانت هناك نتائج
        if (newTopics.isNotEmpty && newTopics.length >= 10) {
          // هنا يجب تحديث _lastTopicDocument بآخر وثيقة تم جلبها
          // لكن نظرًا لأن الوثيقة غير متاحة مباشرة، سنستخدم طريقة أخرى
          // في هذه الحالة، سنحدد أن هناك المزيد من المواضيع فقط إذا كان عدد النتائج يساوي الحد
          _hasMoreTopics = newTopics.length == 10;
        } else {
          _hasMoreTopics = false;
        }
      } else if (_searchQuery.isNotEmpty) {
        // البحث عن المواضيع
        newTopics = await _forumService.searchTopics(_searchQuery, limit: 20);
        // لا نقوم بالتمرير في نتائج البحث
        _hasMoreTopics = false;
      } else {
        // جلب أحدث المواضيع
        newTopics = await _forumService.getLatestTopics(limit: 10);
        // لا نقوم بالتمرير في أحدث المواضيع، نعرض فقط أحدث 10 مواضيع
        _hasMoreTopics = false;
      }

      if (newTopics.isNotEmpty) {
        if (refresh) {
          // استبدال المواضيع الحالية بالمواضيع الجديدة
          _topics = newTopics;
        } else {
          // إضافة المواضيع الجديدة إلى القائمة الحالية مع تجنب التكرار
          final existingIds = _topics.map((topic) => topic.id).toSet();
          final uniqueNewTopics = newTopics.where((topic) => !existingIds.contains(topic.id)).toList();
          _topics.addAll(uniqueNewTopics);
        }
        _topicsState = LoadingState.loaded;
      } else {
        _hasMoreTopics = false;
        _topicsState = _topics.isEmpty ? LoadingState.empty : LoadingState.loaded;
      }
    } catch (e) {
      debugPrint('Error fetching topics: $e');
      _topicsState = LoadingState.error;
    } finally {
      notifyListeners();
    }
  }

  /// الاستماع لتغييرات مواضيع فئة محددة
  void listenToCategoryTopics(String categoryId) {
    _forumService
        .listenToCategoryTopics(
      categoryId,
      limit: 20,
      sortBy: _sortBy,
      descending: _descending)
        .listen((topics) {
      _topics = topics;
      _topicsState = topics.isEmpty ? LoadingState.empty : LoadingState.loaded;
      notifyListeners();
    }, onError: (e) {
      _topicsState = LoadingState.error;
      notifyListeners();
    });
  }

  /// جلب موضوع محدد
  Future<void> fetchTopic(String topicId) async {
    try {
      _topicState = LoadingState.loading;
      notifyListeners();

      final topic = await _forumService.getTopicById(topicId);

      if (topic != null) {
        _currentTopic = topic;
        _topicState = LoadingState.loaded;

        // زيادة عدد المشاهدات
        await _forumService.incrementTopicViews(topicId);

        // تسجيل نشاط مشاهدة الموضوع
        _activityTracker?.trackActivity(
          ForumActivityType.viewTopic,
          itemId: topicId);
      } else {
        _topicState = LoadingState.empty;
      }
    } catch (e) {
      _topicState = LoadingState.error;
    } finally {
      notifyListeners();
    }
  }

  /// تحديث الموضوع الحالي
  void updateCurrentTopic(TopicModel topic) {
    _currentTopic = topic;
    notifyListeners();
  }

  /// تحديث موضوع محلياً في القائمة بدون إعادة تحميل جميع المواضيع
  void updateTopicLocally(String topicId, TopicModel Function(TopicModel) updateFunction) {
    final topicIndex = _topics.indexWhere((topic) => topic.id == topicId);
    if (topicIndex != -1) {
      _topics[topicIndex] = updateFunction(_topics[topicIndex]);

      // تحديث الموضوع الحالي إذا كان هو نفس الموضوع المحدث
      if (_currentTopic?.id == topicId) {
        _currentTopic = _topics[topicIndex];
      }

      notifyListeners();
    }
  }

  /// الاستماع لتغييرات موضوع محدد
  void listenToTopic(String topicId) {
    _forumService.listenToTopic(topicId).listen((topic) {
      _currentTopic = topic;
      _topicState = topic != null ? LoadingState.loaded : LoadingState.empty;
      notifyListeners();
    }, onError: (e) {
      _topicState = LoadingState.error;
      notifyListeners();
    });
  }

  /// إنشاء موضوع جديد
  Future<TopicModel?> createTopic(TopicModel topic,
      {List<File>? images}) async {
    try {
      final newTopic = await _forumService.createTopic(topic, images: images);

      // تسجيل نشاط إنشاء موضوع جديد
      _activityTracker?.trackActivity(
        ForumActivityType.createTopic,
        itemId: newTopic.id,
        additionalData: {
          'categoryId': newTopic.categoryId,
          'categoryName': newTopic.categoryName,
        });

      return newTopic;
    } catch (e) {
      return null;
    }
  }

  /// تحديث موضوع
  Future<bool> updateTopic(TopicModel topic,
      {List<File>? newImages, List<String>? imagesToDelete}) async {
    try {
      await _forumService.updateTopic(topic,
          newImages: newImages, imagesToDelete: imagesToDelete);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// حذف موضوع
  Future<bool> deleteTopic(String topicId) async {
    try {
      await _forumService.deleteTopic(topicId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// الإعجاب بموضوع
  Future<bool> likeTopic(String topicId, String userId) async {
    try {
      await _forumService.likeTopic(topicId, userId);

      // تسجيل نشاط الإعجاب بموضوع
      _activityTracker?.trackActivity(
        ForumActivityType.likeTopic,
        itemId: topicId);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء الإعجاب بموضوع
  Future<bool> unlikeTopic(String topicId, String userId) async {
    try {
      await _forumService.unlikeTopic(topicId, userId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// التحقق مما إذا كان المستخدم قد أعجب بالموضوع
  Future<bool> isTopicLiked(String topicId, String userId) async {
    try {
      final topic = await _forumService.getTopicById(topicId);
      if (topic != null && topic.likedBy != null) {
        return topic.likedBy!.contains(userId);
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// إضافة تفاعل إلى موضوع (الإصدار الجديد)
  Future<String?> addTopicReactionNew(String topicId, ReactionModel reaction) async {
    try {
      // استخدام الطريقة الحالية للتوافق مع الخدمة الحالية
      final success = await addTopicReaction(
        topicId,
        reaction.userId,
        reaction.reactionType
      );

      if (success) {
        // استرجاع معرف التفاعل (في الحالة الحقيقية يجب استرجاعه من الخدمة)
        return DateTime.now().millisecondsSinceEpoch.toString();
      }
      return null;
    } catch (e) {
      debugPrint('Error adding topic reaction: $e');
      return null;
    }
  }

  /// إزالة تفاعل من موضوع (الإصدار الجديد)
  Future<bool> removeTopicReactionNew(String topicId, String reactionId) async {
    try {
      // في الإصدار الحقيقي، يجب استخدام معرف التفاعل للحذف
      // هنا نستخدم الطريقة الحالية للتوافق
      final userId = _authProvider?.user?.uid;

      if (userId != null) {
        return await removeTopicReaction(topicId, userId, 'like');
      }
      return false;
    } catch (e) {
      debugPrint('Error removing topic reaction: $e');
      return false;
    }
  }

  /// إضافة تفاعل إلى مشاركة (الإصدار الجديد)
  Future<String?> addPostReactionNew(String postId, ReactionModel reaction) async {
    try {
      // استخدام الطريقة الحالية للتوافق مع الخدمة الحالية
      final success = await addPostReaction(
        postId,
        reaction.userId,
        reaction.reactionType
      );

      if (success) {
        // استرجاع معرف التفاعل (في الحالة الحقيقية يجب استرجاعه من الخدمة)
        return DateTime.now().millisecondsSinceEpoch.toString();
      }
      return null;
    } catch (e) {
      debugPrint('Error adding post reaction: $e');
      return null;
    }
  }

  /// إزالة تفاعل من مشاركة (الإصدار الجديد)
  Future<bool> removePostReactionNew(String postId, String reactionId) async {
    try {
      // في الإصدار الحقيقي، يجب استخدام معرف التفاعل للحذف
      // هنا نستخدم الطريقة الحالية للتوافق
      final userId = _authProvider?.user?.uid;

      if (userId != null) {
        return await removePostReaction(postId, userId, 'like');
      }
      return false;
    } catch (e) {
      debugPrint('Error removing post reaction: $e');
      return false;
    }
  }

  /// الحصول على تفاعلات موضوع
  Future<List<ReactionModel>> getTopicReactions(String topicId) async {
    try {
      // في الإصدار الحقيقي، يجب استرجاع التفاعلات من الخدمة
      // هنا نستخدم بيانات وهمية للتوافق
      final topic = await _forumService.getTopicById(topicId);
      if (topic != null && topic.likedBy != null && topic.likedBy!.isNotEmpty) {
        return topic.likedBy!.map((userId) => ReactionModel(
          id: '${userId}_${DateTime.now().millisecondsSinceEpoch}',
          itemType: ReactionItemType.topic,
          itemId: topicId,
          reactionType: 'like',
          userId: userId,
          userName: 'مستخدم',
          createdAt: DateTime.now())).toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error getting topic reactions: $e');
      return [];
    }
  }

  /// الحصول على تفاعلات مشاركة
  Future<List<ReactionModel>> getPostReactions(String postId) async {
    try {
      // في الإصدار الحقيقي، يجب استرجاع التفاعلات من الخدمة
      // هنا نستخدم بيانات وهمية للتوافق
      final post = await _forumService.getPostById(postId);
      if (post != null && post.likedBy != null && post.likedBy!.isNotEmpty) {
        return post.likedBy!.map((userId) => ReactionModel(
          id: '${userId}_${DateTime.now().millisecondsSinceEpoch}',
          itemType: ReactionItemType.post,
          itemId: postId,
          reactionType: 'like',
          userId: userId,
          userName: 'مستخدم',
          createdAt: DateTime.now())).toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error getting post reactions: $e');
      return [];
    }
  }

  /// إنشاء استطلاع رأي
  Future<PollModel?> createPoll(String topicId, PollModel poll) async {
    try {
      final pollData = poll.toMap();
      final pollId = await _forumService.createPoll(topicId, pollData);
      if (pollId != null) {
        return poll.copyWith(id: pollId);
      }
      return null;
    } catch (e) {
      debugPrint('Error creating poll: $e');
      return null;
    }
  }

  /// تحديث استطلاع رأي
  Future<bool> updatePoll(String topicId, PollModel poll) async {
    try {
      final pollData = poll.toMap();
      await _forumService.updatePoll(topicId, poll.id, pollData);
      return true;
    } catch (e) {
      debugPrint('Error updating poll: $e');
      return false;
    }
  }

  /// حذف استطلاع رأي
  Future<bool> deletePoll(String topicId, String pollId) async {
    try {
      await _forumService.deletePoll(topicId, pollId);
      return true;
    } catch (e) {
      debugPrint('Error deleting poll: $e');
      return false;
    }
  }

  /// الحصول على استطلاع رأي بواسطة المعرف
  Future<PollModel?> getPollById(String topicId, String pollId) async {
    try {
      final pollData = await _forumService.getPollById(topicId, pollId);
      if (pollData != null) {
        return PollModel.fromMap(pollData);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting poll: $e');
      return null;
    }
  }

  /// التصويت على استطلاع رأي
  Future<bool> voteOnPoll(String topicId, String pollId, List<String> optionIds, String userId) async {
    try {
      await _forumService.voteOnPoll(topicId, pollId, optionIds, userId);

      // تسجيل نشاط التصويت على استطلاع
      _activityTracker?.trackActivity(
        ForumActivityType.voteOnPoll,
        itemId: pollId,
        additionalData: {
          'topicId': topicId,
          'optionIds': optionIds,
        });

      return true;
    } catch (e) {
      debugPrint('Error voting on poll: $e');
      return false;
    }
  }

  /// إلغاء التصويت على استطلاع رأي
  Future<bool> unvoteOnPoll(String topicId, String pollId, String userId) async {
    try {
      await _forumService.unvoteOnPoll(topicId, pollId, userId);
      return true;
    } catch (e) {
      debugPrint('Error unvoting on poll: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات اللوبي
  Future<Map<String, dynamic>> getForumStatistics() async {
    try {
      final statistics = await _forumService.getForumStatistics();

      // إضافة بيانات وهمية للمخططات إذا لم تكن موجودة
      if (!statistics.containsKey('activityData')) {
        statistics['activityData'] = {
          'الأحد': 5,
          'الإثنين': 8,
          'الثلاثاء': 12,
          'الأربعاء': 7,
          'الخميس': 15,
          'الجمعة': 3,
          'السبت': 9,
        };
      }

      if (!statistics.containsKey('categoriesData')) {
        statistics['categoriesData'] = {
          'عقارات': 45,
          'استفسارات': 30,
          'نصائح': 15,
          'أخبار': 10,
        };
      }

      return statistics;
    } catch (e) {
      debugPrint('Error getting forum statistics: $e');

      // إرجاع بيانات وهمية في حالة الخطأ
      return {
        'categoriesCount': 4,
        'topicsCount': 120,
        'postsCount': 350,
        'usersCount': 85,
        'activityData': {
          'الأحد': 5,
          'الإثنين': 8,
          'الثلاثاء': 12,
          'الأربعاء': 7,
          'الخميس': 15,
          'الجمعة': 3,
          'السبت': 9,
        },
        'categoriesData': {
          'عقارات': 45,
          'استفسارات': 30,
          'نصائح': 15,
          'أخبار': 10,
        },
      };
    }
  }

  /// إضافة تفاعل إلى موضوع
  Future<bool> addTopicReaction(
      String topicId, String userId, String reactionType) async {
    try {
      await _forumService.addTopicReaction(topicId, userId, reactionType);

      // تسجيل نشاط إضافة تفاعل إلى موضوع
      _activityTracker?.trackActivity(
        ForumActivityType.addTopicReaction,
        itemId: topicId,
        additionalData: {
          'reactionType': reactionType,
        });

      return true;
    } catch (e) {
      return false;
    }
  }

  /// إزالة تفاعل من موضوع
  Future<bool> removeTopicReaction(
      String topicId, String userId, String reactionType) async {
    try {
      await _forumService.removeTopicReaction(topicId, userId, reactionType);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تثبيت موضوع
  Future<bool> pinTopic(String topicId) async {
    try {
      await _forumService.pinTopic(topicId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء تثبيت موضوع
  Future<bool> unpinTopic(String topicId) async {
    try {
      await _forumService.unpinTopic(topicId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تمييز موضوع
  Future<bool> featureTopic(String topicId) async {
    try {
      await _forumService.featureTopic(topicId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء تمييز موضوع
  Future<bool> unfeatureTopic(String topicId) async {
    try {
      await _forumService.unfeatureTopic(topicId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إغلاق موضوع
  Future<bool> closeTopic(String topicId) async {
    try {
      await _forumService.closeTopic(topicId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إعادة فتح موضوع
  Future<bool> reopenTopic(String topicId) async {
    try {
      await _forumService.reopenTopic(topicId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تعليم موضوع كمحلول
  Future<bool> markTopicAsSolved(String topicId, String postId) async {
    try {
      await _forumService.markTopicAsSolved(topicId, postId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء تعليم موضوع كمحلول
  Future<bool> unmarkTopicAsSolved(String topicId) async {
    try {
      await _forumService.unmarkTopicAsSolved(topicId);
      return true;
    } catch (e) {
      return false;
    }
  }

  // مشاركات المنتدى

  /// جلب مشاركات موضوع محدد
  Future<void> fetchPosts(String topicId, {bool refresh = false}) async {
    if (refresh) {
      _lastPostDocument = null;
      _hasMorePosts = true;
      _posts = [];
    }

    if (!_hasMorePosts) return;

    try {
      _postsState = _posts.isEmpty ? LoadingState.loading : _postsState;
      notifyListeners();

      final newPosts = await _forumService.getPostsByTopic(
        topicId,
        limit: 20,
        startAfter: _lastPostDocument);

      if (newPosts.isNotEmpty) {
        _lastPostDocument = null; // تحديث آخر وثيقة للصفحة التالية
        _posts.addAll(newPosts);
        _postsState = LoadingState.loaded;
      } else {
        _hasMorePosts = false;
        _postsState = _posts.isEmpty ? LoadingState.empty : LoadingState.loaded;
      }
    } catch (e) {
      _postsState = LoadingState.error;
    } finally {
      notifyListeners();
    }
  }

  /// الاستماع لتغييرات مشاركات موضوع محدد
  void listenToTopicPosts(String topicId) {
    _forumService
        .listenToTopicPosts(
      topicId,
      limit: 50)
        .listen((posts) {
      _posts = posts;
      _postsState = posts.isEmpty ? LoadingState.empty : LoadingState.loaded;
      notifyListeners();
    }, onError: (e) {
      _postsState = LoadingState.error;
      notifyListeners();
    });
  }

  /// إنشاء مشاركة جديدة
  Future<PostModel?> createPost(PostModel post, {List<File>? images}) async {
    try {
      final newPost = await _forumService.createPost(post, images: images);

      // تسجيل نشاط إنشاء مشاركة جديدة
      _activityTracker?.trackActivity(
        ForumActivityType.createPost,
        itemId: newPost.id,
        additionalData: {
          'topicId': newPost.topicId,
          'topicTitle': newPost.topicTitle,
          'categoryId': newPost.categoryId,
          'categoryName': newPost.categoryName,
        });

      return newPost;
    } catch (e) {
      return null;
    }
  }

  /// تحديث مشاركة
  Future<bool> updatePost(PostModel post,
      {List<File>? newImages, List<String>? imagesToDelete}) async {
    try {
      await _forumService.updatePost(post,
          newImages: newImages, imagesToDelete: imagesToDelete);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// حذف مشاركة
  Future<bool> deletePost(String postId) async {
    try {
      await _forumService.deletePost(postId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// الإعجاب بمشاركة
  Future<bool> likePost(String postId, String userId) async {
    try {
      await _forumService.likePost(postId, userId);

      // تسجيل نشاط الإعجاب بمشاركة
      _activityTracker?.trackActivity(
        ForumActivityType.likePost,
        itemId: postId);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء الإعجاب بمشاركة
  Future<bool> unlikePost(String postId, String userId) async {
    try {
      await _forumService.unlikePost(postId, userId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إضافة تفاعل إلى مشاركة
  Future<bool> addPostReaction(
      String postId, String userId, String reactionType) async {
    try {
      await _forumService.addPostReaction(postId, userId, reactionType);

      // تسجيل نشاط إضافة تفاعل إلى مشاركة
      _activityTracker?.trackActivity(
        ForumActivityType.addPostReaction,
        itemId: postId,
        additionalData: {
          'reactionType': reactionType,
        });

      return true;
    } catch (e) {
      return false;
    }
  }

  /// إزالة تفاعل من مشاركة
  Future<bool> removePostReaction(
      String postId, String userId, String reactionType) async {
    try {
      await _forumService.removePostReaction(postId, userId, reactionType);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تثبيت مشاركة
  Future<bool> pinPost(String postId) async {
    try {
      await _forumService.pinPost(postId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء تثبيت مشاركة
  Future<bool> unpinPost(String postId) async {
    try {
      await _forumService.unpinPost(postId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تعليم مشاركة كأفضل إجابة
  Future<bool> markPostAsBestAnswer(String postId, String topicId) async {
    try {
      await _forumService.markPostAsBestAnswer(postId, topicId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء تعليم مشاركة كأفضل إجابة
  Future<bool> unmarkPostAsBestAnswer(String postId, String topicId) async {
    try {
      await _forumService.unmarkPostAsBestAnswer(postId, topicId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// الإبلاغ عن مشاركة
  Future<bool> reportPost(String postId, String userId, String reason) async {
    try {
      await _forumService.reportPost(postId, userId, reason);
      return true;
    } catch (e) {
      return false;
    }
  }

  // البحث

  /// البحث في المواضيع
  Future<List<TopicModel>> searchTopics(String query,
      {String? categoryId}) async {
    try {
      return await _forumService.searchTopics(query, categoryId: categoryId);
    } catch (e) {
      return [];
    }
  }

  /// البحث في المشاركات
  Future<List<PostModel>> searchPosts(String query, {String? topicId}) async {
    try {
      return await _forumService.searchPosts(query, topicId: topicId);
    } catch (e) {
      return [];
    }
  }

  // إحصائيات المستخدم

  /// جلب إحصائيات مستخدم محدد
  Future<void> fetchUserStatistics(String userId) async {
    try {
      debugPrint('جاري جلب إحصائيات المستخدم: $userId');

      _userStatisticsState = LoadingState.loading;
      notifyListeners();

      final statistics = await _forumService.getUserStatistics(userId);

      if (statistics != null) {
        debugPrint('تم جلب إحصائيات المستخدم بنجاح: $userId');
        _userStatistics = statistics;
        _userStatisticsState = LoadingState.loaded;

        // طباعة محتويات الإحصائيات للتصحيح
        debugPrint('إحصائيات المستخدم: ${statistics.userName}');
        debugPrint('عدد المواضيع: ${statistics.topicsCount}');
        debugPrint('عدد المشاركات: ${statistics.postsCount}');
        debugPrint('النقاط: ${statistics.points}');
        debugPrint('المستوى: ${statistics.level}');
      } else {
        debugPrint('لا توجد إحصائيات للمستخدم: $userId');
        _userStatisticsState = LoadingState.empty;

        // محاولة إعادة حساب الإحصائيات
        debugPrint('جاري محاولة إعادة حساب إحصائيات المستخدم: $userId');
        await recalculateUserStatistics(userId);
      }
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المستخدم: $e');
      _userStatisticsState = LoadingState.error;
    } finally {
      notifyListeners();
    }
  }

  /// الاستماع لتغييرات إحصائيات مستخدم محدد
  void listenToUserStatistics(String userId) {
    _forumService.listenToUserStatistics(userId).listen((statistics) {
      _userStatistics = statistics;
      _userStatisticsState =
          statistics != null ? LoadingState.loaded : LoadingState.empty;
      notifyListeners();
    }, onError: (e) {
      _userStatisticsState = LoadingState.error;
      notifyListeners();
    });
  }

  /// إعادة حساب إحصائيات مستخدم
  Future<bool> recalculateUserStatistics(String userId) async {
    try {
      _userStatisticsState = LoadingState.loading;
      notifyListeners();

      final statistics = await _forumService.recalculateUserStatistics(userId);

      if (statistics != null) {
        _userStatistics = statistics;
        _userStatisticsState = LoadingState.loaded;
        return true;
      } else {
        _userStatisticsState = LoadingState.empty;
        return false;
      }
    } catch (e) {
      _userStatisticsState = LoadingState.error;
      return false;
    } finally {
      notifyListeners();
    }
  }

  // الإشعارات

  /// جلب إشعارات مستخدم محدد
  Future<void> fetchNotifications(String userId,
      {bool refresh = false, bool unreadOnly = false}) async {
    if (refresh) {
      _lastNotificationDocument = null;
      _hasMoreNotifications = true;
      _notifications = [];
    }

    if (!_hasMoreNotifications) return;

    try {
      _notificationsState =
          _notifications.isEmpty ? LoadingState.loading : _notificationsState;
      notifyListeners();

      final newNotifications = await _forumService.getUserNotifications(
        userId,
        limit: 20,
        startAfter: _lastNotificationDocument,
        unreadOnly: unreadOnly);

      if (newNotifications.isNotEmpty) {
        _lastNotificationDocument = null; // تحديث آخر وثيقة للصفحة التالية
        _notifications.addAll(newNotifications);
        _notificationsState = LoadingState.loaded;
      } else {
        _hasMoreNotifications = false;
        _notificationsState =
            _notifications.isEmpty ? LoadingState.empty : LoadingState.loaded;
      }

      // جلب عدد الإشعارات غير المقروءة
      _unreadNotificationsCount =
          await _forumService.getUnreadNotificationsCount(userId);
    } catch (e) {
      _notificationsState = LoadingState.error;
    } finally {
      notifyListeners();
    }
  }

  /// الاستماع لتغييرات إشعارات مستخدم محدد
  void listenToUserNotifications(String userId, {bool unreadOnly = false}) {
    _forumService
        .listenToUserNotifications(
      userId,
      limit: 20,
      unreadOnly: unreadOnly)
        .listen((notifications) {
      _notifications = notifications;
      _notificationsState =
          notifications.isEmpty ? LoadingState.empty : LoadingState.loaded;
      notifyListeners();
    }, onError: (e) {
      _notificationsState = LoadingState.error;
      notifyListeners();
    });

    // الاستماع لتغييرات عدد الإشعارات غير المقروءة
    _forumService.listenToUnreadNotificationsCount(userId).listen((countValue) {
      _unreadNotificationsCount = countValue;
      notifyListeners();
    });
  }

  /// تعليم إشعار كمقروء
  Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      await _forumService.markNotificationAsRead(notificationId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تعليم جميع الإشعارات كمقروءة
  Future<bool> markAllNotificationsAsRead(String userId) async {
    try {
      await _forumService.markAllNotificationsAsRead(userId);
      _unreadNotificationsCount = 0;
      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// حذف إشعار
  Future<bool> deleteNotification(String notificationId) async {
    try {
      await _forumService.deleteNotification(notificationId);
      _notifications
          .removeWhere((notification) => notification.id == notificationId);
      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  // التفاعلات

  /// الحصول على إحصائيات التفاعلات
  Future<ReactionStats?> getReactionStats(
      String itemId, ReactionItemType itemType) async {
    try {
      return await _forumService.getReactionStats(itemId, itemType);
    } catch (e) {
      return null;
    }
  }

  // متابعة المواضيع والإشارات المرجعية - تم نقلها إلى أعلى

  /// جلب المواضيع المتابعة
  Future<List<TopicModel>> getFollowedTopics(String userId) async {
    try {
      return await _forumService.getFollowedTopics(userId);
    } catch (e) {
      return [];
    }
  }

  /// التحقق مما إذا كان المستخدم يتابع موضوعاً
  Future<bool> isFollowingTopic(String topicId, String userId) async {
    return await _forumService.isFollowingTopic(topicId, userId);
  }

  /// إضافة إشارة مرجعية إلى موضوع - تم نقلها إلى أعلى

  /// جلب المواضيع المحفوظة
  Future<List<TopicModel>> getBookmarkedTopics(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      return await _forumService.getBookmarkedTopics(
        userId,
        limit: limit,
        startAfter: startAfter);
    } catch (e) {
      return [];
    }
  }

  /// جلب مواضيع المستخدم
  Future<List<TopicModel>> getUserTopics(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      return await _forumService.getUserTopics(
        userId,
        limit: limit,
        startAfter: startAfter);
    } catch (e) {
      return [];
    }
  }

  /// التحقق مما إذا كان الموضوع محفوظاً
  Future<bool> isTopicBookmarked(String topicId, String userId) async {
    return await _forumService.isTopicBookmarked(topicId, userId);
  }

  /// مشاركة موضوع - تم نقلها إلى أعلى

  // إحصائيات المنتدى

  /// جلب إحصائيات المنتدى
  Future<void> fetchForumStatistics() async {
    try {
      debugPrint('جاري جلب إحصائيات المنتدى...');

      // تحديث حالة التحميل
      _forumStatisticsState = LoadingState.loading;
      notifyListeners();

      // الحصول على الإحصائيات من الخدمة
      final statistics = await _forumService.getForumStatistics();

      debugPrint('تم جلب إحصائيات المنتدى بنجاح: ${statistics.length} عنصر');

      // تحديث الإحصائيات وإخطار المستمعين
      _forumStatistics = statistics;

      // تحديث حالة التحميل بناءً على البيانات
      if (_forumStatistics.isEmpty ||
          (_forumStatistics['categoriesCount'] == 0 &&
           _forumStatistics['topicsCount'] == 0 &&
           _forumStatistics['postsCount'] == 0)) {
        _forumStatisticsState = LoadingState.empty;
        debugPrint('لا توجد إحصائيات للمنتدى');
      } else {
        _forumStatisticsState = LoadingState.loaded;
        debugPrint('تم تحميل إحصائيات المنتدى بنجاح');
      }

      notifyListeners();

      debugPrint('تم تحديث إحصائيات المنتدى: ${_forumStatistics.length} عنصر');

      // طباعة محتويات الإحصائيات للتصحيح
      _forumStatistics.forEach((key, value) {
        if (value is Map || value is List) {
          debugPrint('$key: ${value.runtimeType} with ${value is Map ? value.length : (value as List).length} items');
        } else {
          debugPrint('$key: $value');
        }
      });
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المنتدى: $e');

      // في حالة الخطأ، نستخدم بيانات فارغة
      _forumStatistics = {
        'categoriesCount': 0,
        'topicsCount': 0,
        'postsCount': 0,
        'activeUsersCount': 0,
        'latestTopics': [],
        'mostViewedTopics': [],
        'mostActiveTopics': [],
        'updatedAt': Timestamp.now(),
        'activityData': {},
        'categoriesData': {},
      };

      _forumStatisticsState = LoadingState.error;
      notifyListeners();

      debugPrint('حدث خطأ أثناء جلب إحصائيات المنتدى');
    }
  }

  /// تسجيل الدخول اليومي للمستخدم
  Future<void> trackDailyLogin(String userId) async {
    try {
      // تسجيل نشاط تسجيل الدخول اليومي
      _activityTracker?.trackActivity(
        ForumActivityType.dailyLogin);

      // تحديث آخر نشاط للمستخدم
      await _forumService.updateUserStatistics(userId);
    } catch (e) {
      debugPrint('خطأ في تسجيل الدخول اليومي: $e');
    }
  }

  // ===== دوال المكافآت والإنجازات =====

  /// منح نقاط للمستخدم
  Future<void> awardPoints(String userId, int points, String reason) async {
    await _forumService.awardPoints(userId, points, reason);
  }

  /// منح نقاط للأنشطة
  Future<void> awardPointsForActivity(String userId, String activity) async {
    await _forumService.awardPointsForActivity(userId, activity);
  }

  /// الحصول على إنجازات المستخدم
  Future<List<AchievementModel>> getUserAchievements(String userId) async {
    return await _forumService.getUserAchievements(userId);
  }

  /// الحصول على شارات المستخدم
  Future<List<BadgeModel>> getUserBadges(String userId) async {
    return await _forumService.getUserBadges(userId);
  }

  /// الحصول على سجل النقاط
  Future<List<Map<String, dynamic>>> getPointsHistory(String userId, {int limit = 20}) async {
    return await _forumService.getPointsHistory(userId, limit: limit);
  }

  // ===== دوال الاستطلاعات المحسنة =====

  /// إنشاء استطلاع رأي محسن
  Future<PollModel?> createEnhancedPoll({
    required String topicId,
    required String question,
    String? description,
    required List<String> options,
    required bool allowMultipleChoices,
    required String createdBy,
    DateTime? endDate,
  }) async {
    return await _forumService.createEnhancedPoll(
      topicId: topicId,
      question: question,
      description: description,
      options: options,
      allowMultipleChoices: allowMultipleChoices,
      createdBy: createdBy,
      endDate: endDate,
    );
  }

  /// التصويت في استطلاع
  Future<bool> voteInPoll({
    required String pollId,
    required List<String> optionIds,
    required String userId,
  }) async {
    return await _forumService.voteInPoll(
      pollId: pollId,
      optionIds: optionIds,
      userId: userId,
    );
  }

  /// الحصول على نتائج الاستطلاع
  Future<Map<String, dynamic>> getPollResults(String pollId) async {
    return await _forumService.getPollResults(pollId);
  }

  /// فحص إذا كان المستخدم صوت
  Future<bool> hasUserVotedInPoll(String pollId, String userId) async {
    return await _forumService.hasUserVotedInPoll(pollId, userId);
  }

  /// الحصول على الاستطلاعات النشطة
  Future<List<PollModel>> getActivePolls({int limit = 10}) async {
    return await _forumService.getActivePolls(limit: limit);
  }

  // ===== دوال الإشراف المتقدمة =====

  /// الحصول على جميع التقارير
  Future<List<ReportModel>> getAllReports({
    ReportStatus? status,
    ReportContentType? contentType,
    int limit = 50,
  }) async {
    return await _forumService.getAllReports(
      status: status,
      contentType: contentType,
      limit: limit,
    );
  }

  /// معالجة تقرير
  Future<bool> handleReport({
    required String reportId,
    required String moderatorId,
    required ReportStatus newStatus,
    String? adminNotes,
    bool deleteContent = false,
    bool banUser = false,
    int banDurationDays = 0,
  }) async {
    return await _forumService.handleReport(
      reportId: reportId,
      moderatorId: moderatorId,
      newStatus: newStatus,
      adminNotes: adminNotes,
      deleteContent: deleteContent,
      banUser: banUser,
      banDurationDays: banDurationDays,
    );
  }

  /// فحص إذا كان المستخدم محظور
  Future<bool> isUserBanned(String userId) async {
    return await _forumService.isUserBanned(userId);
  }

  /// إلغاء حظر مستخدم
  Future<bool> unbanUser(String userId, String moderatorId) async {
    return await _forumService.unbanUser(userId, moderatorId);
  }

  /// الحصول على إحصائيات الإشراف
  Future<Map<String, dynamic>> getModerationStats() async {
    return await _forumService.getModerationStats();
  }





  /// الحصول على إحصائيات المستخدم (دالة مساعدة)
  Future<UserStatisticsModel?> getUserStatistics(String userId) async {
    try {
      return await _forumService.getUserStatistics(userId);
    } catch (e) {
      return null;
    }
  }
}
