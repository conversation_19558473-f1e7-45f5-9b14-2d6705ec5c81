import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج فئة المنتدى
class ForumCategory {
  final String id;
  final String name;
  final String? description;
  final String? iconName;
  final String? color;
  final int topicsCount;
  final int postsCount;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const ForumCategory({
    required this.id,
    required this.name,
    this.description,
    this.iconName,
    this.color,
    this.topicsCount = 0,
    this.postsCount = 0,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// إنشاء ForumCategory من Firestore Document
  factory ForumCategory.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ForumCategory(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'],
      iconName: data['iconName'],
      color: data['color'],
      topicsCount: data['topicsCount'] ?? 0,
      postsCount: data['postsCount'] ?? 0,
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  /// تحويل ForumCategory إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'iconName': iconName,
      'color': color,
      'topicsCount': topicsCount,
      'postsCount': postsCount,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'metadata': metadata,
    };
  }

  /// إنشاء نسخة محدثة من ForumCategory
  ForumCategory copyWith({
    String? id,
    String? name,
    String? description,
    String? iconName,
    String? color,
    int? topicsCount,
    int? postsCount,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ForumCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      topicsCount: topicsCount ?? this.topicsCount,
      postsCount: postsCount ?? this.postsCount,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ForumCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ForumCategory(id: $id, name: $name, topicsCount: $topicsCount, postsCount: $postsCount)';
  }
}
