import 'package:flutter/material.dart';

/// عرض فارغ
class EmptyView extends StatelessWidget {
  /// رسالة العرض الفارغ
  final String message;

  /// أيقونة العرض الفارغ
  final IconData icon;

  /// دالة الإجراء
  final VoidCallback? onAction;

  /// نص زر الإجراء
  final String? actionText;

  /// إنشاء عرض فارغ
  const EmptyView({
    super.key,
    required this.message,
    this.icon = Icons.inbox,
    this.onAction,
    this.actionText,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600])),
            if (onAction != null && actionText != null) ...[
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: onAction,
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
                child: Text(actionText!)),
            ],
          ])));
  }
}
