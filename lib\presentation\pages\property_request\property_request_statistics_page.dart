// lib/presentation/pages/property_request/property_request_statistics_page.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';

// import '../../../core/constants/app_colors.dart';
import '../../../core/services/property_request_statistics_service.dart';
import '../../../domain/models/statistics/property_request_statistics_model.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/property_request/statistics/property_request_statistics_card.dart';
import '../../widgets/property_request/statistics/property_request_pie_chart.dart';
import '../../widgets/property_request/statistics/property_request_bar_chart.dart';

/// صفحة إحصائيات طلبات العقارات
class PropertyRequestStatisticsPage extends StatefulWidget {
  const PropertyRequestStatisticsPage({super.key});

  @override
  State<PropertyRequestStatisticsPage> createState() =>
      _PropertyRequestStatisticsPageState();
}

class _PropertyRequestStatisticsPageState
    extends State<PropertyRequestStatisticsPage> with SingleTickerProviderStateMixin {
  // خدمة إحصائيات طلبات العقارات
  final PropertyRequestStatisticsService _statisticsService =
      PropertyRequestStatisticsService();

  // حالة التحميل
  bool _isLoading = true;

  // رسالة الخطأ
  String? _errorMessage;

  // إحصائيات المستخدم
  PropertyRequestStatisticsModel? _userStatistics;

  // إحصائيات عامة
  Map<String, dynamic>? _generalStatistics;

  // إحصائيات حسب نوع العقار
  Map<String, dynamic>? _statisticsByPropertyType;

  // متحكم علامات التبويب
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadStatistics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل الإحصائيات
  Future<void> _loadStatistics() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // التحقق من تسجيل الدخول
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (!authProvider.isLoggedIn) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'يجب تسجيل الدخول للحصول على الإحصائيات';
        });
        return;
      }

      // تحميل إحصائيات المستخدم
      final userStatistics = await _statisticsService.getUserStatistics();

      // تحميل الإحصائيات العامة
      final generalStatistics = await _statisticsService.getGeneralStatistics();

      // تحميل الإحصائيات حسب نوع العقار
      final statisticsByPropertyType =
          await _statisticsService.getStatisticsByPropertyType();

      setState(() {
        _userStatistics = userStatistics;
        _generalStatistics = generalStatistics;
        _statisticsByPropertyType = statisticsByPropertyType;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل الإحصائيات: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إحصائيات طلبات العقارات',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'إحصائياتي'),
            Tab(text: 'إحصائيات عامة'),
            Tab(text: 'حسب النوع'),
          ])),
      body: _buildBody());
  }

  /// بناء محتوى الصفحة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadStatistics,
              child: const Text('إعادة المحاولة')),
          ]));
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildUserStatistics(),
        _buildGeneralStatistics(),
        _buildStatisticsByPropertyType(),
      ]);
  }

  /// بناء إحصائيات المستخدم
  Widget _buildUserStatistics() {
    if (_userStatistics == null) {
      return const Center(child: Text('لا توجد إحصائيات متاحة'));
    }

    return RefreshIndicator(
      onRefresh: _loadStatistics,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص طلباتك',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'إجمالي الطلبات',
                    value: _userStatistics!.totalRequests.toString(),
                    icon: Icons.list_alt,
                    color: Colors.green)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'طلبات نشطة',
                    value: _userStatistics!.activeRequests.toString(),
                    icon: Icons.pending_actions,
                    color: Colors.blue)),
              ]),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'طلبات تم حلها',
                    value: _userStatistics!.resolvedRequests.toString(),
                    icon: Icons.check_circle,
                    color: Colors.green)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'طلبات مغلقة',
                    value: _userStatistics!.closedRequests.toString(),
                    icon: Icons.cancel,
                    color: Colors.red)),
              ]),
            const SizedBox(height: 24),
            Text(
              'توزيع حالة الطلبات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PropertyRequestPieChart(
                sections: [
                  PieChartSectionData(
                    value: _userStatistics!.activeRequests.toDouble(),
                    title: 'نشطة',
                    color: Colors.blue,
                    radius: 60),
                  PieChartSectionData(
                    value: _userStatistics!.resolvedRequests.toDouble(),
                    title: 'تم حلها',
                    color: Colors.green,
                    radius: 60),
                  PieChartSectionData(
                    value: _userStatistics!.closedRequests.toDouble(),
                    title: 'مغلقة',
                    color: Colors.red,
                    radius: 60),
                ])),
            const SizedBox(height: 24),
            Text(
              'ملخص العروض المقدمة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'إجمالي العروض',
                    value: _userStatistics!.totalOffers.toString(),
                    icon: Icons.local_offer,
                    color: Colors.green)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'عروض مقبولة',
                    value: _userStatistics!.acceptedOffers.toString(),
                    icon: Icons.check_circle,
                    color: Colors.green)),
              ]),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'عروض مرفوضة',
                    value: _userStatistics!.rejectedOffers.toString(),
                    icon: Icons.cancel,
                    color: Colors.red)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'عروض معلقة',
                    value: _userStatistics!.pendingOffers.toString(),
                    icon: Icons.pending,
                    color: Colors.orange)),
              ]),
            const SizedBox(height: 24),
            Text(
              'ملخص العروض المستلمة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'إجمالي العروض',
                    value: _userStatistics!.totalOffersReceived.toString(),
                    icon: Icons.local_offer,
                    color: Colors.green)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'عروض مقبولة',
                    value: _userStatistics!.acceptedOffersReceived.toString(),
                    icon: Icons.check_circle,
                    color: Colors.green)),
              ]),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'عروض مرفوضة',
                    value: _userStatistics!.rejectedOffersReceived.toString(),
                    icon: Icons.cancel,
                    color: Colors.red)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'عروض معلقة',
                    value: _userStatistics!.pendingOffersReceived.toString(),
                    icon: Icons.pending,
                    color: Colors.orange)),
              ]),
            const SizedBox(height: 24),
            Text(
              'مؤشرات الأداء',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'متوسط وقت الاستجابة',
                    value: '${_userStatistics!.averageResponseTime.toStringAsFixed(1)} ساعة',
                    icon: Icons.timer,
                    color: Colors.purple)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'متوسط العروض لكل طلب',
                    value: _userStatistics!.averageOffersPerRequest.toStringAsFixed(1),
                    icon: Icons.bar_chart,
                    color: Colors.teal)),
              ]),
            const SizedBox(height: 32),
          ])));
  }

  /// بناء الإحصائيات العامة
  Widget _buildGeneralStatistics() {
    if (_generalStatistics == null) {
      return const Center(child: Text('لا توجد إحصائيات متاحة'));
    }

    return RefreshIndicator(
      onRefresh: _loadStatistics,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص عام',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'إجمالي الطلبات',
                    value: _generalStatistics!['totalRequests'].toString(),
                    icon: Icons.list_alt,
                    color: Colors.green)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'طلبات نشطة',
                    value: _generalStatistics!['activeRequests'].toString(),
                    icon: Icons.pending_actions,
                    color: Colors.blue)),
              ]),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'طلبات تم حلها',
                    value: _generalStatistics!['resolvedRequests'].toString(),
                    icon: Icons.check_circle,
                    color: Colors.green)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'إجمالي العروض',
                    value: _generalStatistics!['totalOffers'].toString(),
                    icon: Icons.local_offer,
                    color: Colors.orange)),
              ]),
            const SizedBox(height: 24),
            Text(
              'مؤشرات الأداء',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'متوسط العروض لكل طلب',
                    value: (_generalStatistics!['averageOffersPerRequest'] as double).toStringAsFixed(1),
                    icon: Icons.bar_chart,
                    color: Colors.teal)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'نسبة الحل',
                    value: '${((_generalStatistics!['resolutionRate'] as double) * 100).toStringAsFixed(1)}%',
                    icon: Icons.check_circle_outline,
                    color: Colors.green)),
              ]),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'نسبة القبول',
                    value: '${((_generalStatistics!['acceptanceRate'] as double) * 100).toStringAsFixed(1)}%',
                    icon: Icons.thumb_up,
                    color: Colors.blue)),
                const SizedBox(width: 16),
                Expanded(
                  child: PropertyRequestStatisticsCard(
                    title: 'عروض مقبولة',
                    value: _generalStatistics!['acceptedOffers'].toString(),
                    icon: Icons.check_circle,
                    color: Colors.green)),
              ]),
            const SizedBox(height: 32),
          ])));
  }

  /// بناء الإحصائيات حسب نوع العقار
  Widget _buildStatisticsByPropertyType() {
    if (_statisticsByPropertyType == null) {
      return const Center(child: Text('لا توجد إحصائيات متاحة'));
    }

    final requestsByType = _statisticsByPropertyType!['requestsByType'] as Map<String, int>;
    final resolvedByType = _statisticsByPropertyType!['resolvedByType'] as Map<String, int>;
    final resolutionRateByType = _statisticsByPropertyType!['resolutionRateByType'] as Map<String, double>;

    final List<String> propertyTypes = requestsByType.keys.toList();
    final List<double> requestCounts = propertyTypes.map((type) => requestsByType[type]!.toDouble()).toList();
    final List<double> resolvedCounts = propertyTypes.map((type) => resolvedByType[type]?.toDouble() ?? 0).toList();
    final List<double> resolutionRates = propertyTypes.map((type) => resolutionRateByType[type]! * 100).toList();

    return RefreshIndicator(
      onRefresh: _loadStatistics,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع الطلبات حسب نوع العقار',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: PropertyRequestBarChart(
                titles: propertyTypes,
                values: requestCounts,
                color: Colors.green)),
            const SizedBox(height: 24),
            Text(
              'الطلبات التي تم حلها حسب نوع العقار',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: PropertyRequestBarChart(
                titles: propertyTypes,
                values: resolvedCounts,
                color: Colors.green)),
            const SizedBox(height: 24),
            Text(
              'نسبة الحل حسب نوع العقار',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: PropertyRequestBarChart(
                titles: propertyTypes,
                values: resolutionRates,
                color: Colors.blue,
                suffix: '%')),
            const SizedBox(height: 32),
          ])));
  }
}
