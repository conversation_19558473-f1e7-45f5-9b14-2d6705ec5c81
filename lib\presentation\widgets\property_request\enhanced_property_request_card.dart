import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:firebase_auth/firebase_auth.dart';

import '../../../core/services/property_request_favorites_service.dart';
import '../../../domain/models/property_request/property_request_model.dart';
import '../../../domain/services/user_interface_customization_service.dart';
import '../../../domain/entities/user.dart';
import 'property_request_comments_sheet.dart';

/// بطاقة طلب عقار محسنة مع أزرار التفاعل
class EnhancedPropertyRequestCard extends StatefulWidget {
  final PropertyRequestModel request;
  final VoidCallback onTap;
  final bool isCompact;

  const EnhancedPropertyRequestCard({
    super.key,
    required this.request,
    required this.onTap,
    this.isCompact = false,
  });

  @override
  State<EnhancedPropertyRequestCard> createState() => _EnhancedPropertyRequestCardState();
}

class _EnhancedPropertyRequestCardState extends State<EnhancedPropertyRequestCard> {
  bool _isLoading = false;
  final bool _isFavorite = false;
  final PropertyRequestFavoritesService _favoritesService = PropertyRequestFavoritesService();

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            _buildContent(),
            _buildInteractionButtons(),
            _buildFooter(),
          ])));
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.purple.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16))),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Colors.blue.shade100,
            backgroundImage: widget.request.userImage != null
                ? NetworkImage(widget.request.userImage!)
                : null,
            child: widget.request.userImage == null
                ? Icon(Icons.person, color: Colors.blue.shade700)
                : null),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.request.userName,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                    fontSize: 14)),
                Text(
                  timeago.format(widget.request.createdAt, locale: 'ar'),
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600)),
              ])),
          _buildStatusBadge(),
        ]));
  }

  Widget _buildStatusBadge() {
    Color color;
    String text;

    switch (widget.request.status) {
      case RequestStatus.open:
        color = Colors.green;
        text = 'مفتوح';
        break;
      case RequestStatus.closed:
        color = Colors.red;
        text = 'مغلق';
        break;
      case RequestStatus.resolved:
        color = Colors.blue;
        text = 'تم الحل';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3))),
      child: Text(
        text,
        style: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: color)));
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Text(
            widget.request.title,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800),
            maxLines: 2,
            overflow: TextOverflow.ellipsis),
          const SizedBox(height: 8),

          // الوصف
          Text(
            widget.request.description,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade600,
              height: 1.5),
            maxLines: 3,
            overflow: TextOverflow.ellipsis),
          const SizedBox(height: 12),

          // المعلومات الأساسية
          _buildBasicInfo(),

          // المميزات
          if (_hasFeatures()) ...[
            const SizedBox(height: 12),
            _buildFeatures(),
          ],
        ]));
  }

  Widget _buildBasicInfo() {
    return Column(
      children: [
        // المناطق المفضلة
        _buildInfoRow(
          Icons.location_on,
          'المناطق المفضلة',
          widget.request.preferredLocations.join(' • '),
          Colors.red),
        const SizedBox(height: 8),

        // نطاق السعر
        _buildInfoRow(
          Icons.attach_money,
          'نطاق السعر',
          _getPriceRange(),
          Colors.green),
        const SizedBox(height: 8),

        // نوع العقار
        _buildInfoRow(
          _getPropertyTypeIcon(),
          'نوع العقار',
          widget.request.propertyType,
          Colors.blue),
      ]);
  }

  Widget _buildInfoRow(IconData icon, String label, String value, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8)),
          child: Icon(icon, size: 16, color: color)),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey.shade500)),
              Text(
                value,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade800),
                maxLines: 1,
                overflow: TextOverflow.ellipsis),
            ])),
      ]);
  }

  Widget _buildFeatures() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المميزات المطلوبة',
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade700)),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _buildFeatureChips()),
      ]);
  }

  List<Widget> _buildFeatureChips() {
    List<Widget> chips = [];

    if (widget.request.minRooms != null) {
      chips.add(_buildFeatureChip('${widget.request.minRooms}+ غرف', Icons.bedroom_parent));
    }
    if (widget.request.minBathrooms != null) {
      chips.add(_buildFeatureChip('${widget.request.minBathrooms}+ حمامات', Icons.bathroom));
    }
    if (widget.request.minArea != null) {
      chips.add(_buildFeatureChip('${widget.request.minArea}+ م²', Icons.square_foot));
    }
    if (widget.request.hasCentralAC) {
      chips.add(_buildFeatureChip('تكييف مركزي', Icons.ac_unit));
    }
    if (widget.request.hasMaidRoom) {
      chips.add(_buildFeatureChip('غرفة خادمة', Icons.person));
    }
    if (widget.request.hasGarage) {
      chips.add(_buildFeatureChip('مرآب', Icons.garage));
    }
    if (widget.request.hasSwimmingPool) {
      chips.add(_buildFeatureChip('مسبح', Icons.pool));
    }
    if (widget.request.hasElevator) {
      chips.add(_buildFeatureChip('مصعد', Icons.elevator));
    }
    if (widget.request.isFullyFurnished) {
      chips.add(_buildFeatureChip('مفروش', Icons.chair));
    }

    return chips;
  }

  Widget _buildFeatureChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.blue.shade700),
          const SizedBox(width: 4),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.blue.shade700,
              fontWeight: FontWeight.w500)),
        ]));
  }

  Widget _buildInteractionButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
          bottom: BorderSide(color: Colors.grey.shade200))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildInteractionButton(
            icon: Icons.message_outlined,
            label: 'مراسلة',
            onPressed: _startConversation,
            color: Colors.blue),
          _buildInteractionButton(
            icon: Icons.comment_outlined,
            label: 'تعليق',
            onPressed: _showCommentsDialog,
            color: Colors.green),
          _buildInteractionButton(
            icon: Icons.local_offer_outlined,
            label: 'عرض',
            onPressed: _createOffer,
            color: Colors.orange),
        ]));
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3))),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 18, color: color),
            const SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: color)),
          ])));
  }

  Widget _buildFooter() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(Icons.visibility, size: 16, color: Colors.grey.shade500),
              const SizedBox(width: 4),
              Text(
                '${widget.request.viewsCount} مشاهدة',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey.shade600)),
            ]),
          Row(
            children: [
              Icon(Icons.local_offer, size: 16, color: Colors.grey.shade500),
              const SizedBox(width: 4),
              Text(
                '${widget.request.offersCount} عرض',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey.shade600)),
            ]),
        ]));
  }

  // Helper methods
  String _getPriceRange() {
    if (widget.request.minPrice != null && widget.request.maxPrice != null) {
      return '${widget.request.minPrice!.toStringAsFixed(0)} - ${widget.request.maxPrice!.toStringAsFixed(0)} د.ك';
    } else if (widget.request.minPrice != null) {
      return 'من ${widget.request.minPrice!.toStringAsFixed(0)} د.ك';
    } else if (widget.request.maxPrice != null) {
      return 'حتى ${widget.request.maxPrice!.toStringAsFixed(0)} د.ك';
    }
    return 'السعر غير محدد';
  }

  IconData _getPropertyTypeIcon() {
    switch (widget.request.propertyType) {
      case 'شقة':
        return Icons.apartment;
      case 'فيلا':
        return Icons.home;
      case 'أرض':
        return Icons.landscape;
      case 'مكتب':
        return Icons.business;
      case 'محل تجاري':
        return Icons.storefront;
      case 'مخزن':
        return Icons.warehouse;
      default:
        return Icons.home_work;
    }
  }

  bool _hasFeatures() {
    return widget.request.minRooms != null ||
        widget.request.minBathrooms != null ||
        widget.request.minArea != null ||
        widget.request.hasCentralAC ||
        widget.request.hasMaidRoom ||
        widget.request.hasGarage ||
        widget.request.hasSwimmingPool ||
        widget.request.hasElevator ||
        widget.request.isFullyFurnished;
  }

  // Action methods
  void _startConversation() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تسجيل الدخول أولاً'),
          backgroundColor: Colors.red));
      return;
    }

    if (currentUser.uid == widget.request.userId) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكنك مراسلة نفسك'),
          backgroundColor: Colors.orange));
      return;
    }

    try {
      setState(() => _isLoading = true);

      // TODO: إضافة خدمة المراسلة
      // final messagingService = MessagingService();

      // TODO: إنشاء محادثة جديدة أو الحصول على المحادثة الموجودة
      // final conversationId = await messagingService.createPropertyRequestConversation(
      //   requestId: widget.request.id,
      //   requestTitle: widget.request.title,
      //   ownerId: widget.request.userId,
      // );

      setState(() => _isLoading = false);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خدمة المراسلة غير متاحة حالياً'),
            backgroundColor: Colors.orange));
        // TODO: الانتقال إلى صفحة المحادثة
        // Navigator.pushNamed(
        //   context,
        //   '/conversation',
        //   arguments: {
        //     'conversationId': conversationId,
        //     'title': 'محادثة حول: ${widget.request.title}',
        //   },
        // );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في بدء المحادثة: $e'),
            backgroundColor: Colors.red));
      }
    }
  }

  void _showCommentsDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PropertyRequestCommentsSheet(
        requestId: widget.request.id,
        requestTitle: widget.request.title));
  }

  void _createOffer() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    if (currentUser.uid == widget.request.userId) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكنك تقديم عرض على طلبك الخاص'),
          backgroundColor: Colors.orange));
      return;
    }

    final uiService = UserInterfaceCustomizationService();
    final userType = await uiService.getCurrentUserType();

    if (userType == UserType.seeker) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يمكن فقط لمالكي العقارات والوكلاء تقديم العروض'),
            backgroundColor: Colors.orange));
      }
      return;
    }

    if (mounted) {
      Navigator.pushNamed(
        context,
        '/create-property-offer',
        arguments: {
          'requestId': widget.request.id,
          'requestTitle': widget.request.title,
        });
    }
  }
}
