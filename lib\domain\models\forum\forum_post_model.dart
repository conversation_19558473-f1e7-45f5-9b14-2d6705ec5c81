// ARCHIVO ADAPTADOR - PARA COMPATIBILIDAD
// Este archivo es un adaptador para mantener compatibilidad con código existente
// Utiliza PostModel internamente

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'post_model.dart';

/// Adaptador para mantener compatibilidad con código existente
/// Utiliza PostModel internamente
class ForumPost extends Equatable {
  final PostModel _model;

  /// Obtener el modelo interno
  PostModel get model => _model;

  /// معرف المشاركة
  String get id => _model.id;

  /// معرف الموضوع
  String get topicId => _model.topicId;

  /// عنوان الموضوع
  String get topicTitle => _model.topicTitle;

  /// معرف الفئة
  String get categoryId => _model.categoryId;

  /// اسم الفئة
  String get categoryName => _model.categoryName;

  /// معرف المشاركة الأب (إذا كانت رداً على مشاركة أخرى)
  String? get parentId => _model.parentId;

  /// محتوى المشاركة
  String get content => _model.content;

  /// معرف المستخدم
  String get userId => _model.userId;

  /// اسم المستخدم
  String get userName => _model.userName;

  /// صورة المستخدم
  String? get userImage => _model.userImage;

  /// عدد الإعجابات
  int get likesCount => _model.likesCount;

  /// قائمة المستخدمين الذين أعجبوا بالمشاركة
  List<String>? get likedBy => _model.likedBy;

  /// عدد الردود
  int get repliesCount => _model.repliesCount;

  /// ما إذا كانت المشاركة مثبتة
  bool get isPinned => _model.isPinned;

  /// ما إذا كانت المشاركة أفضل إجابة
  bool get isBestAnswer => _model.isBestAnswer;

  /// ما إذا كانت المشاركة تم الإبلاغ عنها
  bool get isReported => _model.isReported;

  /// سبب الإبلاغ
  String? get reportReason => _model.reportReason;

  /// ما إذا كانت المشاركة محذوفة
  bool get isDeleted => _model.isDeleted;

  /// تاريخ إنشاء المشاركة
  DateTime get createdAt => _model.createdAt;

  /// تاريخ آخر تحديث للمشاركة
  DateTime get updatedAt => _model.updatedAt;

  /// الصور المرفقة بالمشاركة
  List<String>? get images => _model.images;

  /// المرفقات الأخرى
  List<Map<String, dynamic>>? get attachments => _model.attachments;

  /// تفاعلات المستخدمين مع المشاركة
  Map<String, List<String>>? get reactions => _model.reactions;

  /// درجة التطابق في نتائج البحث
  double? get searchScore => _model.searchScore;

  /// Constructor
  const ForumPost(this._model);

  /// Factory constructor from PostModel
  factory ForumPost.fromModel(PostModel model) {
    return ForumPost(model);
  }

  /// Factory constructor from Map
  factory ForumPost.fromMap(Map<String, dynamic> map) {
    return ForumPost(PostModel.fromMap(map));
  }

  /// Factory constructor from Firestore
  factory ForumPost.fromFirestore(DocumentSnapshot doc) {
    return ForumPost(PostModel.fromFirestore(doc));
  }

  @override
  List<Object?> get props => [_model];
}
