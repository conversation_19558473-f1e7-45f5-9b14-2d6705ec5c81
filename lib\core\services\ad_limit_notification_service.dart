// lib/core/services/ad_limit_notification_service.dart
// import 'package:flutter_local_notifications/flutter_local_notifications.dart'; // Removed due to compilation issues
import 'package:shared_preferences/shared_preferences.dart';

import '../models/user_subscription_model.dart';
import 'enhanced_subscription_service.dart';

/// خدمة إشعارات حد الإعلانات
/// توفر وظائف لإرسال إشعارات للمستخدم عند اقترابه من حد الإعلانات
class AdLimitNotificationService {
    // /// مثيل الإشعارات المحلية
  // final FlutterLocalNotificationsPlugin _notifications;

  /// خدمة الاشتراكات
  final EnhancedSubscriptionService _subscriptionService;

  /// مفتاح آخر تحقق من الإعلانات
  static const String _lastCheckKey = 'last_ad_limit_check';

  /// مفتاح تفعيل الإشعارات
  static const String _notificationsEnabledKey = 'ad_limit_notifications_enabled';

  /// معرف إشعار حد الإعلانات
  static const int _adLimitNotificationId = 1001;

  /// معرف قناة الإشعارات
  static const String _channelId = 'ad_limit_channel';

  /// اسم قناة الإشعارات
  static const String _channelName = 'Ad Limit Notifications';

  /// وصف قناة الإشعارات
  static const String _channelDescription = 'Notifications for ad limit warnings';

  /// منشئ الخدمة
  AdLimitNotificationService({
    // FlutterLocalNotificationsPlugin? notifications,
    EnhancedSubscriptionService? subscriptionService,
  }) : // _notifications = notifications ?? FlutterLocalNotificationsPlugin(),
       _subscriptionService = subscriptionService ?? EnhancedSubscriptionService() {
    // _initNotifications();
  }

  /// تهيئة الإشعارات - معطلة مؤقتًا
  /*
  Future<void> _initNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true);

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings);

    await _notifications.initialize(initSettings);
  }
  */

  /// Check ad limit and send notification if needed - DISABLED
  Future<void> checkAdLimit() async {
    // Check if notifications are enabled
    final isEnabled = await areNotificationsEnabled();
    if (!isEnabled) {
      return;
    }

    // Check last check time
    final prefs = await SharedPreferences.getInstance();
    final lastCheck = prefs.getInt(_lastCheckKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;

    // Check once a day
    if (now - lastCheck < const Duration(days: 1).inMilliseconds) {
      return;
    }

    // Update last check time
    await prefs.setInt(_lastCheckKey, now);

    // Get current subscription
    final subscription = await _subscriptionService.getCurrentSubscription();
    if (subscription == null) {
      return;
    }

    // Check remaining ads
    // Notification functionality disabled due to package removal
    /*
    if (subscription.remainingAds <= 3 && subscription.remainingAds > 0) {
      await _sendAdLimitNotification(subscription);
    } else if (subscription.remainingAds <= 0) {
      await _sendNoAdsNotification(subscription);
    }
    */
  }

  /// Send ad limit notification - DISABLED
  Future<void> _sendAdLimitNotification(UserSubscriptionModel subscription) async {
    // Notification functionality disabled due to package removal
    /*
    const androidDetails = AndroidNotificationDetails(
      _channelId,
      _channelName,
      channelDescription: _channelDescription,
      importance: Importance.high,
      priority: Priority.high,
      ticker: 'Ad Limit Warning');

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true);

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails);

    await _notifications.show(
      _adLimitNotificationId,
      'Ad Limit Warning',
      'You have only ${subscription.remainingAds} ads left. Consider upgrading your plan.',
      details);
    */
  }

  /// Send no ads notification - DISABLED
  Future<void> _sendNoAdsNotification(UserSubscriptionModel subscription) async {
    // Notification functionality disabled due to package removal
    /*
    const androidDetails = AndroidNotificationDetails(
      _channelId,
      _channelName,
      channelDescription: _channelDescription,
      importance: Importance.high,
      priority: Priority.high,
      ticker: 'No Ads Warning');

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true);

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails);

    await _notifications.show(
      _adLimitNotificationId + 1,
      'No Ads Warning',
      'You have no ads left. Please upgrade your plan to post more ads.',
      details);
    */
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_notificationsEnabledKey) ?? true;
  }

  /// Enable notifications
  Future<void> enableNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsEnabledKey, true);
  }

  /// Disable notifications
  Future<void> disableNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsEnabledKey, false);
  }

  /// Toggle notifications state
  Future<bool> toggleNotifications() async {
    final isEnabled = await areNotificationsEnabled();
    if (isEnabled) {
      await disableNotifications();
      return false;
    } else {
      await enableNotifications();
      return true;
    }
  }
}
