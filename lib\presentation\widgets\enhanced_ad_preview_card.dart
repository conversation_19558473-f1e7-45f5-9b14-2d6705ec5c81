// lib/presentation/widgets/enhanced_ad_preview_card.dart
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../../core/theme/app_colors.dart';

/// بطاقة معاينة محسنة للإعلان
/// تعرض معاينة أكثر تفصيلاً للإعلان قبل النشر
class EnhancedAdPreviewCard extends StatefulWidget {
  /// عنوان الإعلان
  final String title;
  
  /// وصف الإعلان
  final String description;
  
  /// سعر العقار
  final double price;
  
  /// موقع العقار
  final String location;
  
  /// مسارات الصور
  final List<String> imagePaths;
  
  /// التصنيف الرئيسي
  final String? mainCategory;
  
  /// التصنيف الفرعي
  final String? subCategory;
  
  /// مساحة العقار
  final double? area;
  
  /// عدد الغرف
  final int? numberOfRooms;
  
  /// عدد الحمامات
  final int? numberOfBathrooms;
  
  /// عمر البناء
  final int? buildingAge;
  
  /// ما إذا كان يحتوي على كراج
  final bool? hasGarage;
  
  /// ما إذا كان يحتوي على تكييف مركزي
  final bool? hasCentralAC;
  
  /// ما إذا كان يحتوي على غرفة خادمة
  final bool? hasMaidRoom;
  
  /// ما إذا كان مفروش بالكامل
  final bool? isFullyFurnished;
  
  /// خطة الاشتراك
  final String? subscriptionPlan;
  
  /// الميزات الإضافية
  final List<String>? extraFeatures;

  const EnhancedAdPreviewCard({
    super.key,
    required this.title,
    required this.description,
    required this.price,
    required this.location,
    required this.imagePaths,
    this.mainCategory,
    this.subCategory,
    this.area,
    this.numberOfRooms,
    this.numberOfBathrooms,
    this.buildingAge,
    this.hasGarage,
    this.hasCentralAC,
    this.hasMaidRoom,
    this.isFullyFurnished,
    this.subscriptionPlan,
    this.extraFeatures,
  });

  @override
  State<EnhancedAdPreviewCard> createState() => _EnhancedAdPreviewCardState();
}

class _EnhancedAdPreviewCardState extends State<EnhancedAdPreviewCard> {
  /// مؤشر الصورة الحالية
  int _currentImageIndex = 0;
  
  /// متحكم عرض الصفحات
  late final PageController _pageController;
  
  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }
  
  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تنسيق السعر
    final priceFormat = NumberFormat("#,##0.000", "ar");
    final formattedPrice = "${priceFormat.format(widget.price)} د.ك";
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // عارض الصور
          SizedBox(
            height: 250,
            child: Stack(
              children: [
                // عارض الصور
                PageView.builder(
                  controller: _pageController,
                  itemCount: widget.imagePaths.length,
                  onPageChanged: (index) {
                    setState(() {
                      _currentImageIndex = index;
                    });
                  },
                  itemBuilder: (context, index) {
                    return ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(16)),
                      child: Image.file(
                        File(widget.imagePaths[index]),
                        fit: BoxFit.cover));
                  }),
                
                // مؤشر الصور
                Positioned(
                  bottom: 16,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      widget.imagePaths.length,
                      (index) => Container(
                        width: 8,
                        height: 8,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: index == _currentImageIndex
                              ? AppColors.primary
                              : Colors.white.withAlpha(150)))))),
                
                // شارة التصنيف
                if (widget.mainCategory != null)
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(150),
                        borderRadius: BorderRadius.circular(20)),
                      child: Text(
                        _getCategoryName(),
                        style: GoogleFonts.cairo(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12)))),
                
                // شارة الخطة
                if (widget.subscriptionPlan != null)
                  Positioned(
                    top: 16,
                    left: 16,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6),
                      decoration: BoxDecoration(
                        color: _getPlanColor(),
                        borderRadius: BorderRadius.circular(20)),
                      child: Text(
                        _getPlanName(),
                        style: GoogleFonts.cairo(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12)))),
              ])),
          
          // تفاصيل العقار
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العنوان والسعر
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        widget.title,
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis)),
                    const SizedBox(width: 8),
                    Text(
                      formattedPrice,
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary)),
                  ]),
                
                const SizedBox(height: 8),
                
                // الموقع
                Row(
                  children: [
                    const Icon(
                      Icons.location_on,
                      size: 16,
                      color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        widget.location,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade700),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis)),
                  ]),
                
                const SizedBox(height: 16),
                
                // المميزات الرئيسية
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    if (widget.area != null)
                      _buildFeatureItem(
                        Icons.square_foot,
                        "${widget.area} م²"),
                    if (widget.numberOfRooms != null)
                      _buildFeatureItem(
                        Icons.bed,
                        "${widget.numberOfRooms} غرف"),
                    if (widget.numberOfBathrooms != null)
                      _buildFeatureItem(
                        Icons.bathtub,
                        "${widget.numberOfBathrooms} حمام"),
                  ]),
                
                const SizedBox(height: 16),
                
                // الوصف
                Text(
                  "الوصف",
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Text(
                  widget.description,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade800),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis),
                
                const SizedBox(height: 16),
                
                // الميزات الإضافية
                if (widget.extraFeatures != null && widget.extraFeatures!.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "الميزات الإضافية",
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: widget.extraFeatures!.map((feature) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withAlpha(30),
                              borderRadius: BorderRadius.circular(20)),
                            child: Text(
                              feature,
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold)));
                        }).toList()),
                    ]),
              ])),
        ]));
  }
  
  /// بناء عنصر ميزة
  Widget _buildFeatureItem(IconData icon, String text) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.primary),
        const SizedBox(height: 4),
        Text(
          text,
          style: GoogleFonts.cairo(
            fontSize: 12,
            fontWeight: FontWeight.bold)),
      ]);
  }
  
  /// الحصول على اسم التصنيف
  String _getCategoryName() {
    if (widget.subCategory != null && widget.subCategory!.isNotEmpty) {
      return widget.subCategory!;
    } else if (widget.mainCategory != null && widget.mainCategory!.isNotEmpty) {
      return widget.mainCategory!;
    } else {
      return "سكني";
    }
  }
  
  /// الحصول على اسم الخطة
  String _getPlanName() {
    switch (widget.subscriptionPlan) {
      case 'gold':
        return "ذهبية";
      case 'silver':
        return "فضية";
      case 'bronze':
        return "برونزية";
      default:
        return "مجانية";
    }
  }
  
  /// الحصول على لون الخطة
  Color _getPlanColor() {
    switch (widget.subscriptionPlan) {
      case 'gold':
        return Colors.amber.shade700;
      case 'silver':
        return Colors.blueGrey.shade700;
      case 'bronze':
        return Colors.brown.shade700;
      default:
        return Colors.green.shade700;
    }
  }
}
