import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/topic_model.dart';
import '../../../domain/models/forum/filter_options_model.dart';

/// قائمة عرض مواضيع المنتدى
class TopicsList extends StatelessWidget {
  /// قائمة المواضيع
  final List<TopicModel> topics;

  /// دالة يتم استدعاؤها عند النقر على موضوع
  final Function(TopicModel) onTopicTap;

  /// دالة يتم استدعاؤها عند النقر على زر الإعجاب
  final Function(TopicModel)? onLikeTap;

  /// دالة يتم استدعاؤها عند النقر على زر الحفظ
  final Function(TopicModel)? onBookmarkTap;

  /// دالة يتم استدعاؤها عند النقر على زر المشاركة
  final Function(TopicModel)? onShareTap;

  /// دالة يتم استدعاؤها عند النقر على زر المتابعة
  final Function(TopicModel)? onFollowTap;

  /// معرف المستخدم الحالي
  final String? currentUserId;

  /// خيارات الفلترة الحالية
  final ForumFilterOptionsModel? filterOptions;

  /// ما إذا كان يتم عرض المواضيع في وضع القائمة
  final bool listMode;

  /// ما إذا كان يتم تحميل المزيد من المواضيع
  final bool isLoadingMore;

  /// دالة يتم استدعاؤها عند التمرير للأسفل لتحميل المزيد
  final VoidCallback? onLoadMore;

  /// متحكم التمرير
  final ScrollController? scrollController;

  const TopicsList({
    super.key,
    required this.topics,
    required this.onTopicTap,
    this.onLikeTap,
    this.onBookmarkTap,
    this.onShareTap,
    this.onFollowTap,
    this.currentUserId,
    this.filterOptions,
    this.listMode = true,
    this.isLoadingMore = false,
    this.onLoadMore,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: listMode ? _buildListView() : _buildGridView());
  }

  /// بناء عرض القائمة
  Widget _buildListView() {
    return ListView.builder(
      controller: scrollController,
      padding: EdgeInsets.all(16),
      itemCount: topics.length + (isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == topics.length) {
          return Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(
                color: AppColors.primary)));
        }

        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 375),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: _buildTopicCard(context, topics[index]))));
      });
  }

  /// بناء عرض الشبكة
  Widget _buildGridView() {
    return GridView.builder(
      controller: scrollController,
      padding: EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16),
      itemCount: topics.length + (isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == topics.length) {
          return Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(
                color: AppColors.primary)));
        }

        return AnimationConfiguration.staggeredGrid(
          position: index,
          duration: const Duration(milliseconds: 375),
          columnCount: 2,
          child: ScaleAnimation(
            child: FadeInAnimation(
              child: _buildTopicGridCard(context, topics[index]))));
      });
  }

  /// بناء بطاقة الموضوع (عرض القائمة)
  Widget _buildTopicCard(BuildContext context, TopicModel topic) {
    final hasImages = topic.images != null && topic.images!.isNotEmpty;
    final isLiked = topic.likedBy?.contains(currentUserId) ?? false;
    final isBookmarked = topic.bookmarkedBy?.contains(currentUserId) ?? false;
    final isFollowed = topic.followedBy?.contains(currentUserId) ?? false;

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.05),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Colors.grey.shade200,
          width: 1)),
      child: InkWell(
        onTap: () => onTopicTap(topic),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس الموضوع
            _buildTopicHeader(topic),

            // محتوى الموضوع
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الموضوع
                  Text(
                    topic.title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 17,
                      height: 1.3),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),
                  SizedBox(height: 8),

                  // محتوى الموضوع
                  Text(
                    topic.content,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                      height: 1.4),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis),
                ])),

            // صور الموضوع
            if (hasImages) _buildTopicImages(topic),

            // تذييل الموضوع
            _buildTopicFooter(topic, isLiked, isBookmarked, isFollowed),
          ])));
  }

  /// بناء بطاقة الموضوع (عرض الشبكة)
  Widget _buildTopicGridCard(BuildContext context, TopicModel topic) {
    final hasImages = topic.images != null && topic.images!.isNotEmpty;
    final isLiked = topic.likedBy?.contains(currentUserId) ?? false;
    final isBookmarked = topic.bookmarkedBy?.contains(currentUserId) ?? false;

    return Card(
      elevation: 2,
      shadowColor: Colors.black.withAlpha(13),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Colors.grey.shade200,
          width: 1)),
      child: InkWell(
        onTap: () => onTopicTap(topic),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الموضوع (إذا وجدت)
            if (hasImages)
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                    child: CachedNetworkImage(
                      imageUrl: topic.images!.first,
                      height: 130,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey.shade100,
                        child: Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: AppColors.primary))),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey.shade100,
                        child: Icon(Icons.error, color: Colors.red)))),
                  // فئة الموضوع على الصورة
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(120),
                        borderRadius: BorderRadius.circular(12)),
                      child: Text(
                        topic.categoryName,
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.bold),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis))),
                ]),

            // محتوى الموضوع
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فئة الموضوع (إذا لم تكن هناك صورة)
                    if (!hasImages)
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withAlpha(25),
                          borderRadius: BorderRadius.circular(8)),
                        child: Text(
                          topic.categoryName,
                          style: TextStyle(
                            fontSize: 10,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis)),
                    if (!hasImages) SizedBox(height: 8),

                    // عنوان الموضوع
                    Text(
                      topic.title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        height: 1.3),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis),
                    SizedBox(height: 6),

                    // محتوى الموضوع
                    Expanded(
                      child: Text(
                        topic.content,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.black87,
                          height: 1.4),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis)),
                  ]))),

            // تذييل الموضوع
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.shade200,
                    width: 1))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.remove_red_eye_outlined,
                        size: 14,
                        color: Colors.grey.shade600),
                      SizedBox(width: 4),
                      Text(
                        '${topic.viewsCount}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600)),
                    ]),
                  Row(
                    children: [
                      GestureDetector(
                        onTap: onLikeTap != null ? () => onLikeTap!(topic) : null,
                        child: Icon(
                          isLiked ? Icons.favorite : Icons.favorite_border,
                          size: 16,
                          color: isLiked ? Colors.red : Colors.grey.shade600)),
                      SizedBox(width: 8),
                      GestureDetector(
                        onTap: onBookmarkTap != null ? () => onBookmarkTap!(topic) : null,
                        child: Icon(
                          isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                          size: 16,
                          color: isBookmarked ? AppColors.primary : Colors.grey.shade600)),
                    ]),
                ])),
          ])));
  }

  /// بناء رأس الموضوع
  Widget _buildTopicHeader(TopicModel topic) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1))),
      child: Row(
        children: [
          // صورة المستخدم
          CircleAvatar(
            radius: 20,
            backgroundImage:
                topic.userImage != null ? NetworkImage(topic.userImage!) : null,
            backgroundColor: AppColors.primary.withAlpha(25),
            child: topic.userImage == null
                ? Icon(Icons.person, color: AppColors.primary)
                : null),
          SizedBox(width: 12),

          // معلومات المستخدم والموضوع
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  topic.userName,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 15)),
                SizedBox(height: 2),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 12,
                      color: Colors.grey.shade600),
                    SizedBox(width: 4),
                    Text(
                      timeago.format(topic.createdAt, locale: 'ar'),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600)),
                  ]),
              ])),

          // فئة الموضوع
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(25),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppColors.primary.withAlpha(50),
                width: 1)),
            child: Text(
              topic.categoryName,
              style: TextStyle(
                fontSize: 12,
                color: AppColors.primary,
                fontWeight: FontWeight.w500))),
        ]));
  }

  /// بناء صور الموضوع
  Widget _buildTopicImages(TopicModel topic) {
    if (topic.images == null || topic.images!.isEmpty) {
      return SizedBox.shrink();
    }

    return Container(
      height: 150,
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: topic.images!.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.only(right: 8),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CachedNetworkImage(
                imageUrl: topic.images![index],
                width: 200,
                height: 150,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey.shade200,
                  child: Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppColors.primary))),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey.shade200,
                  child: Icon(Icons.error, color: Colors.red)))));
        }));
  }

  /// بناء تذييل الموضوع
  Widget _buildTopicFooter(
    TopicModel topic,
    bool isLiked,
    bool isBookmarked,
    bool isFollowed) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade200,
            width: 1))),
      child: Column(
        children: [
          // إحصائيات الموضوع
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItemWithLabel(
                Icons.remove_red_eye_outlined,
                topic.viewsCount.toString(),
                'مشاهدة'),
              _buildStatItemWithLabel(
                Icons.forum_outlined,
                topic.repliesCount.toString(),
                'رد'),
              _buildStatItemWithLabel(
                Icons.favorite_outline,
                topic.likesCount.toString(),
                'إعجاب'),
            ]),
          SizedBox(height: 12),
          Divider(height: 1, color: Colors.grey.shade200),
          SizedBox(height: 12),

          // أزرار التفاعل
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              // زر الإعجاب
              if (onLikeTap != null)
                _buildActionButton(
                  icon: isLiked ? Icons.favorite : Icons.favorite_border,
                  label: isLiked ? 'إلغاء الإعجاب' : 'إعجاب',
                  color: isLiked ? Colors.red : Colors.grey.shade600,
                  onPressed: () => onLikeTap!(topic)),

              // زر الحفظ
              if (onBookmarkTap != null)
                _buildActionButton(
                  icon: isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                  label: isBookmarked ? 'إلغاء الحفظ' : 'حفظ',
                  color: isBookmarked ? AppColors.primary : Colors.grey.shade600,
                  onPressed: () => onBookmarkTap!(topic)),

              // زر المتابعة
              if (onFollowTap != null)
                _buildActionButton(
                  icon: isFollowed ? Icons.notifications : Icons.notifications_none,
                  label: isFollowed ? 'إلغاء المتابعة' : 'متابعة',
                  color: isFollowed ? AppColors.primary : Colors.grey.shade600,
                  onPressed: () => onFollowTap!(topic)),

              // زر المشاركة
              if (onShareTap != null)
                _buildActionButton(
                  icon: Icons.share_outlined,
                  label: 'مشاركة',
                  color: Colors.grey.shade600,
                  onPressed: () => onShareTap!(topic)),
            ]),
        ]));
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color)),
          ])));
  }

  /// بناء عنصر إحصائية مع تسمية
  Widget _buildStatItemWithLabel(IconData icon, String count, String label) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: Colors.grey.shade600),
            SizedBox(width: 4),
            Text(
              count,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800)),
          ]),
        SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600)),
      ]);
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(IconData icon, String count) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey.shade600),
        SizedBox(width: 4),
        Text(
          count,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600)),
      ]);
  }
}
