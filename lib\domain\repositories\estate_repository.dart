// file: lib/domain/repositories/estate_repository.dart

import 'dart:io';

import '../entities/estate.dart';
import '../entities/estate_base.dart';
import '../entities/estate_document.dart';
import '../entities/virtual_tour.dart';

/// واجهة مستودع العقارات
abstract class EstateRepository {
  /// جلب جميع العقارات دفعة واحدة
  Future<List<EstateBase>> getAllEstates();

  /// جلب جميع العقارات دفعة واحدة (للتوافق مع الكود القديم)
  @Deprecated('استخدم getAllEstates بدلاً من ذلك')
  Future<List<Estate>> getAllEstatesLegacy();

  /// جلب العقارات بالتحميل المتدرج
  /// [limit] عدد العقارات في كل صفحة
  /// [lastDocumentId] معرف آخر مستند تم تحميله (للصفحات التالية)
  /// [page] رقم الصفحة الحالية
  /// [pageSize] عدد العناصر في كل صفحة
  /// [sortBy] حقل الترتيب
  /// [sortAscending] ترتيب تصاعدي أم تنازلي
  /// [filters] فلاتر إضافية
  /// [searchQuery] نص البحث
  /// يعيد Map تحتوي على:
  /// - 'estates': قائمة العقارات
  /// - 'lastDocumentId': معرف آخر مستند (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من العقارات
  Future<Map<String, dynamic>> getPaginatedEstates({
    int limit = 10,
    String? lastDocumentId,
    int page = 1,
    int pageSize = 10,
    String sortBy = "createdAt",
    bool sortAscending = false,
    Map<String, dynamic>? filters,
    String? searchQuery,
  });

  /// إنشاء عقار جديد
  Future<String> createEstate(EstateBase estate);

  /// إنشاء عقار جديد (للتوافق مع الكود القديم)
  @Deprecated('استخدم createEstate بدلاً من ذلك')
  Future<void> createEstateLegacy(Estate estate);

  /// تحديث عقار موجود
  Future<void> updateEstate(dynamic estate);

  /// تحديث عقار موجود (للتوافق مع الكود القديم)
  @Deprecated('استخدم updateEstate بدلاً من ذلك')
  Future<void> updateEstateLegacy(Estate estate);

  /// حذف عقار
  Future<void> deleteEstate(String id);

  /// رفع صور العقار
  Future<List<String>> uploadImages(List<File> images, {String? estateId});

  /// حذف صورة
  Future<void> deleteImage(String imageUrl);

  /// الحصول على عقار بواسطة المعرف
  Future<EstateBase?> getEstateById(String id);

  /// الحصول على عدد العقارات النشطة للمستخدم
  Future<int> getUserActiveAdsCount(String userId);

  /// البحث عن العقارات
  Future<List<EstateBase>> searchEstates({
    String? query,
    String? mainCategory,
    String? subCategory,
    double? minPrice,
    double? maxPrice,
    String? location,
    Map<String, dynamic>? filters,
  });

  /// الحصول على العقارات المميزة
  Future<List<EstateBase>> getFeaturedEstates({int limit = 10});

  /// الحصول على العقارات الأكثر مشاهدة
  Future<List<EstateBase>> getMostViewedEstates({int limit = 10});

  /// الحصول على العقارات الأحدث
  Future<List<EstateBase>> getLatestEstates({int limit = 10});

  /// الحصول على عقارات المستخدم
  Future<List<EstateBase>> getUserEstates(String userId);

  /// زيادة عدد مشاهدات العقار
  Future<void> incrementEstateViews(String estateId);

  /// إضافة عقار إلى المفضلة
  Future<void> addEstateToFavorites(String estateId, String userId);

  /// إزالة عقار من المفضلة
  Future<void> removeEstateFromFavorites(String estateId, String userId);

  /// الحصول على العقارات المفضلة للمستخدم
  Future<List<EstateBase>> getUserFavoriteEstates(String userId);

  /// التحقق مما إذا كان العقار في المفضلة
  Future<bool> isEstateInFavorites(String estateId, String userId);

  /// إضافة مستند للعقار
  Future<String> addEstateDocument(EstateDocument document, File file);

  /// حذف مستند من العقار
  Future<void> deleteEstateDocument(String documentId);

  /// الحصول على مستندات العقار
  Future<List<EstateDocument>> getEstateDocuments(String estateId,
      {bool publicOnly = false});

  /// إضافة جولة افتراضية للعقار
  Future<String> addVirtualTour(VirtualTour tour);

  /// تحديث جولة افتراضية
  Future<void> updateVirtualTour(VirtualTour tour);

  /// حذف جولة افتراضية
  Future<void> deleteVirtualTour(String tourId);

  /// الحصول على جولات العقار الافتراضية
  Future<List<VirtualTour>> getEstateVirtualTours(String estateId);

  /// زيادة عدد مشاهدات الجولة الافتراضية
  Future<void> incrementVirtualTourViews(String tourId);

  /// نسخ عقار
  Future<String> copyEstate(String estateId, String userId);

  /// الحصول على إحصائيات العقار
  Future<Map<String, dynamic>> getEstateStatistics(String estateId);

  /// تحديث حالة العقار (متاح، مباع، مؤجر)
  Future<void> updateEstateStatus(String estateId, String status);

  /// تمييز العقار (VIP، مثبت، مميز)
  Future<void> promoteEstate(
    String estateId, {
    bool isVIP = false,
    bool isPinned = false,
    bool isPromoted = false,
  });
}
