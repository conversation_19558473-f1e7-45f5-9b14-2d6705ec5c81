import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/estate.dart';
import '../../domain/services/estate_copy_service.dart';
import '../../domain/services/paid_ads_service.dart';

/// صفحة نسخ العقار للمستثمرين
class EstateCopyPage extends StatefulWidget {
  final Estate originalEstate;

  const EstateCopyPage({
    super.key,
    required this.originalEstate,
  });

  @override
  State<EstateCopyPage> createState() => _EstateCopyPageState();
}

class _EstateCopyPageState extends State<EstateCopyPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  
  final EstateCopyService _copyService = EstateCopyService();
  final PaidAdsService _paidAdsService = PaidAdsService();
  
  String _selectedAdType = 'featured';
  int _selectedDuration = 30;
  bool _isLoading = false;
  
  List<Map<String, dynamic>> _adTypes = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    // تعبئة الحقول بالبيانات الأصلية
    _titleController.text = widget.originalEstate.title;
    _descriptionController.text = widget.originalEstate.description;
    _priceController.text = widget.originalEstate.price.toString();
    
    // تحميل أنواع الإعلانات المدفوعة
    _adTypes = _paidAdsService.getAvailableAdTypes();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          'نسخ العقار',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white)),
        backgroundColor: Colors.purple,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white)),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildOriginalEstateCard(),
                    const SizedBox(height: 24),
                    _buildEditableFields(),
                    const SizedBox(height: 24),
                    _buildAdTypeSelection(),
                    const SizedBox(height: 24),
                    _buildPriceCalculation(),
                  ]))),
            _buildBottomActions(),
          ])));
  }

  /// بناء بطاقة العقار الأصلي
  Widget _buildOriginalEstateCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue, size: 20),
                const SizedBox(width: 8),
                Text(
                  'العقار الأصلي',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue)),
              ]),
            const SizedBox(height: 12),
            Text(
              widget.originalEstate.title,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600)),
            const SizedBox(height: 8),
            Text(
              '${NumberFormat('#,###').format(widget.originalEstate.price)} د.ك',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.green,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    widget.originalEstate.location,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey[600]))),
              ]),
          ])));
  }

  /// بناء الحقول القابلة للتعديل
  Widget _buildEditableFields() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تعديل بيانات العقار',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.purple)),
            const SizedBox(height: 16),
            
            // عنوان العقار
            TextFormField(
              controller: _titleController,
              decoration: InputDecoration(
                labelText: 'عنوان العقار',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: Icon(Icons.title)),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال عنوان العقار';
                }
                return null;
              }),
            const SizedBox(height: 16),
            
            // السعر
            TextFormField(
              controller: _priceController,
              decoration: InputDecoration(
                labelText: 'السعر (د.ك)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: Icon(Icons.attach_money)),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال السعر';
                }
                if (double.tryParse(value) == null) {
                  return 'يرجى إدخال رقم صحيح';
                }
                return null;
              }),
            const SizedBox(height: 16),
            
            // الوصف
            TextFormField(
              controller: _descriptionController,
              decoration: InputDecoration(
                labelText: 'وصف العقار',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: Icon(Icons.description)),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال وصف العقار';
                }
                return null;
              }),
          ])));
  }

  /// بناء اختيار نوع الإعلان
  Widget _buildAdTypeSelection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نوع الإعلان المدفوع',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.orange)),
            const SizedBox(height: 8),
            Text(
              'يجب اختيار نوع إعلان مدفوع لنسخ العقار',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey[600])),
            const SizedBox(height: 16),
            
            // أنواع الإعلانات
            ...(_adTypes.map((adType) => _buildAdTypeOption(adType)).toList()),
            
            const SizedBox(height: 16),
            
            // مدة الإعلان
            Text(
              'مدة الإعلان',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600)),
            const SizedBox(height: 8),
            DropdownButtonFormField<int>(
              value: _selectedDuration,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: Icon(Icons.schedule)),
              items: [
                DropdownMenuItem(value: 7, child: Text('أسبوع واحد')),
                DropdownMenuItem(value: 15, child: Text('أسبوعين')),
                DropdownMenuItem(value: 30, child: Text('شهر واحد')),
                DropdownMenuItem(value: 60, child: Text('شهرين')),
                DropdownMenuItem(value: 90, child: Text('3 أشهر')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedDuration = value!;
                });
              }),
          ])));
  }

  /// بناء خيار نوع الإعلان
  Widget _buildAdTypeOption(Map<String, dynamic> adType) {
    final isSelected = _selectedAdType == adType['id'];
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedAdType = adType['id'];
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Colors.orange : Colors.grey[300]!,
            width: isSelected ? 2 : 1),
          borderRadius: BorderRadius.circular(8),
          color: isSelected ? Colors.orange.withValues(alpha: 0.1) : Colors.white),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? Colors.orange : Colors.grey),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    adType['name'],
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.w600,
                      color: isSelected ? Colors.orange : Colors.black)),
                  Text(
                    adType['description'],
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey[600])),
                ])),
            Text(
              '${adType['price']} د.ك',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
                color: Colors.green)),
          ])));
  }

  /// بناء حساب السعر
  Widget _buildPriceCalculation() {
    final price = _paidAdsService.calculateAdPrice(_selectedAdType, customDuration: _selectedDuration);
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص التكلفة',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.green)),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'نوع الإعلان:',
                  style: GoogleFonts.cairo(fontSize: 14)),
                Text(
                  _adTypes.firstWhere((ad) => ad['id'] == _selectedAdType)['name'],
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600)),
              ]),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المدة:',
                  style: GoogleFonts.cairo(fontSize: 14)),
                Text(
                  '$_selectedDuration يوم',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600)),
              ]),
            const Divider(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'إجمالي التكلفة:',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold)),
                Text(
                  '${price.toStringAsFixed(1)} د.ك',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green)),
              ]),
          ])));
  }

  /// بناء أزرار الإجراءات السفلية
  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2)),
        ]),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: BorderSide(color: Colors.grey)),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700])))),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _copyEstate,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8))),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
                  : Text(
                      'نسخ العقار والدفع',
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        color: Colors.white)))),
        ]));
  }

  /// نسخ العقار
  Future<void> _copyEstate() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء طلب دفع
      final paymentRequest = await _paidAdsService.createPaymentRequest(
        estateId: 'temp_id', // سيتم تحديثه بعد النسخ
        adType: _selectedAdType,
        duration: _selectedDuration);

      // نسخ العقار
      final copiedEstate = await _copyService.copyEstate(
        originalEstateId: widget.originalEstate.id,
        investorId: 'current_user_id', // سيتم الحصول عليه من Firebase Auth
        adType: _selectedAdType,
        adExpiryDate: DateTime.now().add(Duration(days: _selectedDuration)),
        newPrice: double.parse(_priceController.text),
        newDescription: _descriptionController.text,
        newTitle: _titleController.text);

      // الانتقال لصفحة الدفع
      Navigator.pushReplacementNamed(
        context,
        '/payment',
        arguments: {
          'paymentRequest': paymentRequest,
          'copiedEstate': copiedEstate,
        });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في نسخ العقار: $e'),
          backgroundColor: Colors.red));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
