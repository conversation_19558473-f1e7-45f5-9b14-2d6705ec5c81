import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/poll_model.dart';
import '../../../domain/models/forum/poll_option_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';

/// صفحة إنشاء وتعديل استطلاع رأي
class CreateEditPollPage extends StatefulWidget {
  /// معرف الموضوع
  final String topicId;

  /// استطلاع الرأي (للتعديل)
  final PollModel? poll;

  /// دالة يتم استدعاؤها عند الانتهاء
  final Function(PollModel)? onComplete;

  const CreateEditPollPage({
    super.key,
    required this.topicId,
    this.poll,
    this.onComplete,
  });

  @override
  State<CreateEditPollPage> createState() => _CreateEditPollPageState();
}

class _CreateEditPollPageState extends State<CreateEditPollPage> {
  final _formKey = GlobalKey<FormState>();
  final _questionController = TextEditingController();
  final _descriptionController = TextEditingController();
  final List<TextEditingController> _optionControllers = [];
  bool _allowMultipleChoices = false;
  bool _hasEndDate = false;
  DateTime _endDate = DateTime.now().add(const Duration(days: 7));
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  /// تهيئة النموذج
  void _initializeForm() {
    if (widget.poll != null) {
      _questionController.text = widget.poll!.question;
      _descriptionController.text = widget.poll!.description ?? '';
      _allowMultipleChoices = widget.poll!.allowMultipleChoices;
      _hasEndDate = widget.poll!.endDate != null;
      if (_hasEndDate) {
        _endDate = widget.poll!.endDate!;
      }

      // تهيئة خيارات الاستطلاع
      for (final option in widget.poll!.options) {
        final controller = TextEditingController(text: option.text);
        _optionControllers.add(controller);
      }
    } else {
      // إضافة خيارين افتراضيين
      _optionControllers.add(TextEditingController());
      _optionControllers.add(TextEditingController());
    }
  }

  @override
  void dispose() {
    _questionController.dispose();
    _descriptionController.dispose();
    for (final controller in _optionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: widget.poll == null ? 'إنشاء استطلاع رأي' : 'تعديل استطلاع رأي',
        showBackButton: true),
      body: _buildBody());
  }

  /// بناء محتوى الصفحة
  Widget _buildBody() {
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildQuestionField(),
          const SizedBox(height: 16),
          _buildDescriptionField(),
          const SizedBox(height: 24),
          _buildOptionsSection(),
          const SizedBox(height: 24),
          _buildSettingsSection(),
          const SizedBox(height: 32),
          _buildSubmitButton(),
        ]));
  }

  /// بناء حقل السؤال
  Widget _buildQuestionField() {
    return CustomTextField(
      controller: _questionController,
      labelText: 'السؤال',
      hintText: 'أدخل سؤال الاستطلاع',
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال سؤال الاستطلاع';
        }
        return null;
      });
  }

  /// بناء حقل الوصف
  Widget _buildDescriptionField() {
    return CustomTextField(
      controller: _descriptionController,
      labelText: 'الوصف (اختياري)',
      hintText: 'أدخل وصف الاستطلاع',
      maxLines: 3);
  }

  /// بناء قسم الخيارات
  Widget _buildOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'خيارات الاستطلاع',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        ..._buildOptionFields(),
        const SizedBox(height: 16),
        _buildAddOptionButton(),
      ]);
  }

  /// بناء حقول الخيارات
  List<Widget> _buildOptionFields() {
    return List.generate(
      _optionControllers.length,
      (index) => Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _optionControllers[index],
                labelText: 'الخيار ${index + 1}',
                hintText: 'أدخل نص الخيار',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال نص الخيار';
                  }
                  return null;
                })),
            if (_optionControllers.length > 2)
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () => _removeOption(index)),
          ])));
  }

  /// بناء زر إضافة خيار
  Widget _buildAddOptionButton() {
    return TextButton.icon(
      onPressed: _addOption,
      icon: const Icon(Icons.add),
      label: const Text('إضافة خيار'),
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primary));
  }

  /// بناء قسم الإعدادات
  Widget _buildSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إعدادات الاستطلاع',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        SwitchListTile(
          title: const Text('السماح باختيار متعدد'),
          subtitle: const Text('يمكن للمستخدمين اختيار أكثر من خيار واحد'),
          value: _allowMultipleChoices,
          onChanged: (value) {
            setState(() {
              _allowMultipleChoices = value;
            });
          },
          activeColor: AppColors.primary),
        SwitchListTile(
          title: const Text('تحديد تاريخ انتهاء'),
          subtitle: const Text('سينتهي الاستطلاع في التاريخ المحدد'),
          value: _hasEndDate,
          onChanged: (value) {
            setState(() {
              _hasEndDate = value;
            });
          },
          activeColor: AppColors.primary),
        if (_hasEndDate) ...[
          const SizedBox(height: 8),
          ListTile(
            title: const Text('تاريخ الانتهاء'),
            subtitle: Text(_formatDate(_endDate)),
            trailing: const Icon(Icons.calendar_today),
            onTap: _selectEndDate),
        ],
      ]);
  }

  /// بناء زر الإرسال
  Widget _buildSubmitButton() {
    return CustomButton(
      text: widget.poll == null ? 'إنشاء استطلاع' : 'تحديث استطلاع',
      isLoading: _isLoading,
      onPressed: _submitPoll);
  }

  /// إضافة خيار جديد
  void _addOption() {
    setState(() {
      _optionControllers.add(TextEditingController());
    });
  }

  /// إزالة خيار
  void _removeOption(int index) {
    setState(() {
      _optionControllers[index].dispose();
      _optionControllers.removeAt(index);
    });
  }

  /// اختيار تاريخ الانتهاء
  Future<void> _selectEndDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)));

    if (pickedDate != null) {
      setState(() {
        _endDate = pickedDate;
      });
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  /// إرسال الاستطلاع
  Future<void> _submitPoll() async {
    if (!_formKey.currentState!.validate()) return;

    if (_optionControllers.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إضافة خيارين على الأقل')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = Provider.of<AuthProvider>(context, listen: false).user?.uid;
      if (userId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يجب تسجيل الدخول لإنشاء استطلاع')));
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      final now = DateTime.now();

      // إنشاء خيارات الاستطلاع
      final options = <PollOptionModel>[];
      for (int i = 0; i < _optionControllers.length; i++) {
        final text = _optionControllers[i].text.trim();
        if (text.isNotEmpty) {
          final optionId = (widget.poll?.options.length ?? 0) > i
              ? widget.poll!.options[i].id
              : 'option_${DateTime.now().millisecondsSinceEpoch}_$i';

          final votedBy = (widget.poll?.options.length ?? 0) > i
              ? widget.poll!.options[i].votedBy
              : <String>[];

          final votesCount = (widget.poll?.options.length ?? 0) > i
              ? widget.poll!.options[i].votesCount
              : 0;

          options.add(PollOptionModel(
            id: optionId,
            text: text,
            votedBy: votedBy,
            votesCount: votesCount));
        }
      }

      // إنشاء نموذج الاستطلاع
      final poll = PollModel(
        id: widget.poll?.id ?? '',
        question: _questionController.text.trim(),
        description: _descriptionController.text.trim().isNotEmpty
            ? _descriptionController.text.trim()
            : null,
        options: options,
        allowMultipleChoices: _allowMultipleChoices,
        startDate: widget.poll?.startDate ?? now,
        endDate: _hasEndDate ? _endDate : null,
        createdBy: widget.poll?.createdBy ?? userId,
        createdAt: widget.poll?.createdAt ?? now,
        updatedAt: now);

      // إنشاء أو تحديث الاستطلاع
      if (widget.poll == null) {
        final createdPoll = await forumProvider.createPoll(widget.topicId, poll);
        if (createdPoll != null && widget.onComplete != null) {
          widget.onComplete!(createdPoll);
        }
      } else {
        final success = await forumProvider.updatePoll(widget.topicId, poll);
        if (success && widget.onComplete != null) {
          widget.onComplete!(poll);
        }
      }

      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: ${e.toString()}')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
