import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج لمقارنة العقارات
class EstateComparison extends Equatable {
  /// معرف المقارنة
  final String id;
  
  /// معرف المستخدم
  final String userId;
  
  /// عنوان المقارنة
  final String? title;
  
  /// قائمة معرفات العقارات المقارنة
  final List<String> estateIds;
  
  /// تاريخ إنشاء المقارنة
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث للمقارنة
  final DateTime? updatedAt;
  
  /// ما إذا كانت المقارنة محفوظة
  final bool isSaved;
  
  /// ملاحظات المقارنة
  final String? notes;
  
  /// الخصائص المخفية في المقارنة
  final List<String>? hiddenProperties;

  const EstateComparison({
    required this.id,
    required this.userId,
    this.title,
    required this.estateIds,
    required this.createdAt,
    this.updatedAt,
    this.isSaved = false,
    this.notes,
    this.hiddenProperties,
  });

  /// إنشاء نسخة معدلة من المقارنة
  EstateComparison copyWith({
    String? id,
    String? userId,
    String? title,
    List<String>? estateIds,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSaved,
    String? notes,
    List<String>? hiddenProperties,
  }) {
    return EstateComparison(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      estateIds: estateIds ?? this.estateIds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSaved: isSaved ?? this.isSaved,
      notes: notes ?? this.notes,
      hiddenProperties: hiddenProperties ?? this.hiddenProperties);
  }
  
  /// تحويل المقارنة إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'estateIds': estateIds,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'isSaved': isSaved,
      'notes': notes,
      'hiddenProperties': hiddenProperties,
    };
  }
  
  /// إنشاء مقارنة من Map
  factory EstateComparison.fromMap(Map<String, dynamic> map) {
    return EstateComparison(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      title: map['title'],
      estateIds: List<String>.from(map['estateIds'] ?? []),
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
      isSaved: map['isSaved'] ?? false,
      notes: map['notes'],
      hiddenProperties: map['hiddenProperties'] != null 
          ? List<String>.from(map['hiddenProperties']) 
          : null);
  }
  
  /// إنشاء مقارنة من DocumentSnapshot
  factory EstateComparison.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return EstateComparison.fromMap(data);
  }
  
  /// إضافة عقار إلى المقارنة
  EstateComparison addEstate(String estateId) {
    if (estateIds.contains(estateId)) {
      return this;
    }
    
    final newEstateIds = List<String>.from(estateIds);
    newEstateIds.add(estateId);
    
    return copyWith(
      estateIds: newEstateIds,
      updatedAt: DateTime.now());
  }
  
  /// إزالة عقار من المقارنة
  EstateComparison removeEstate(String estateId) {
    if (!estateIds.contains(estateId)) {
      return this;
    }
    
    final newEstateIds = List<String>.from(estateIds);
    newEstateIds.remove(estateId);
    
    return copyWith(
      estateIds: newEstateIds,
      updatedAt: DateTime.now());
  }
  
  /// إخفاء خاصية في المقارنة
  EstateComparison hideProperty(String property) {
    final newHiddenProperties = List<String>.from(hiddenProperties ?? []);
    
    if (!newHiddenProperties.contains(property)) {
      newHiddenProperties.add(property);
    }
    
    return copyWith(
      hiddenProperties: newHiddenProperties,
      updatedAt: DateTime.now());
  }
  
  /// إظهار خاصية في المقارنة
  EstateComparison showProperty(String property) {
    if (hiddenProperties == null || !hiddenProperties!.contains(property)) {
      return this;
    }
    
    final newHiddenProperties = List<String>.from(hiddenProperties!);
    newHiddenProperties.remove(property);
    
    return copyWith(
      hiddenProperties: newHiddenProperties,
      updatedAt: DateTime.now());
  }
  
  /// حفظ المقارنة
  EstateComparison save() {
    return copyWith(
      isSaved: true,
      updatedAt: DateTime.now());
  }
  
  /// إلغاء حفظ المقارنة
  EstateComparison unsave() {
    return copyWith(
      isSaved: false,
      updatedAt: DateTime.now());
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    title,
    estateIds,
    createdAt,
    updatedAt,
    isSaved,
    notes,
    hiddenProperties,
  ];
}
