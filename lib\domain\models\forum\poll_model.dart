import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

import 'poll_option_model.dart';

/// نموذج استطلاع الرأي
class PollModel extends Equatable {
  /// معرف الاستطلاع
  final String id;

  /// سؤال الاستطلاع
  final String question;

  /// وصف الاستطلاع (اختياري)
  final String? description;

  /// خيارات الاستطلاع
  final List<PollOptionModel> options;

  /// ما إذا كان يسمح باختيارات متعددة
  final bool allowMultipleChoices;

  /// تاريخ بدء الاستطلاع
  final DateTime startDate;

  /// تاريخ انتهاء الاستطلاع (اختياري)
  final DateTime? endDate;

  /// معرف منشئ الاستطلاع
  final String createdBy;

  /// تاريخ إنشاء الاستطلاع
  final DateTime createdAt;

  /// تاريخ آخر تحديث للاستطلاع
  final DateTime updatedAt;

  /// إنشاء نموذج استطلاع الرأي
  const PollModel({
    required this.id,
    required this.question,
    this.description,
    required this.options,
    this.allowMultipleChoices = false,
    required this.startDate,
    this.endDate,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء نسخة معدلة من الاستطلاع
  PollModel copyWith({
    String? id,
    String? question,
    String? description,
    List<PollOptionModel>? options,
    bool? allowMultipleChoices,
    DateTime? startDate,
    DateTime? endDate,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PollModel(
      id: id ?? this.id,
      question: question ?? this.question,
      description: description ?? this.description,
      options: options ?? this.options,
      allowMultipleChoices: allowMultipleChoices ?? this.allowMultipleChoices,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt);
  }

  /// تحويل الاستطلاع إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'question': question,
      'description': description,
      'options': options.map((option) => option.toMap()).toList(),
      'allowMultipleChoices': allowMultipleChoices,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': endDate != null ? Timestamp.fromDate(endDate!) : null,
      'createdBy': createdBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// إنشاء استطلاع من خريطة
  factory PollModel.fromMap(Map<String, dynamic> map) {
    return PollModel(
      id: map['id'] ?? '',
      question: map['question'] ?? '',
      description: map['description'],
      options: map['options'] != null
          ? List<PollOptionModel>.from(
              map['options'].map((option) => PollOptionModel.fromMap(option)))
          : [],
      allowMultipleChoices: map['allowMultipleChoices'] ?? false,
      startDate: map['startDate'] is Timestamp
          ? (map['startDate'] as Timestamp).toDate()
          : DateTime.now(),
      endDate: map['endDate'] is Timestamp
          ? (map['endDate'] as Timestamp).toDate()
          : null,
      createdBy: map['createdBy'] ?? '',
      createdAt: map['createdAt'] is Timestamp
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp
          ? (map['updatedAt'] as Timestamp).toDate()
          : DateTime.now());
  }

  /// إنشاء استطلاع من وثيقة فايربيز
  factory PollModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return PollModel(
        id: doc.id,
        question: '',
        options: [],
        allowMultipleChoices: false,
        startDate: DateTime.now(),
        createdBy: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());
    }
    return PollModel.fromMap({...data, 'id': doc.id});
  }

  /// إنشاء استطلاع فارغ
  factory PollModel.empty() {
    return PollModel(
      id: '',
      question: '',
      options: [],
      allowMultipleChoices: false,
      startDate: DateTime.now(),
      createdBy: '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now());
  }

  @override
  List<Object?> get props => [
        id,
        question,
        description,
        options,
        allowMultipleChoices,
        startDate,
        endDate,
        createdBy,
        createdAt,
        updatedAt,
      ];
}
