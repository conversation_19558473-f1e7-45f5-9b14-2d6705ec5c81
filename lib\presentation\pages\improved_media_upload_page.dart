// lib/presentation/pages/improved_media_upload_page.dart
import 'dart:io';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core/services/enhanced_ad_draft_service.dart';
import '../../core/services/image_processing_service.dart';
import '../../domain/entities/estate.dart';
import '../bloc/improved_ad_bloc.dart';
import '../widgets/ad_creation_navigation_buttons.dart';
import '../widgets/improved_ad_creation_progress.dart';
import '../widgets/error_message_widget.dart';
import 'improved_ad_details_page.dart';

/// صفحة رفع الصور والوسائط المحسنة
class ImprovedMediaUploadPage extends StatefulWidget {
  final Estate estate;
  final bool isEditing;

  const ImprovedMediaUploadPage(
      {super.key, required this.estate, this.isEditing = false});

  @override
  State<ImprovedMediaUploadPage> createState() =>
      _ImprovedMediaUploadPageState();
}

class _ImprovedMediaUploadPageState extends State<ImprovedMediaUploadPage>
    with TickerProviderStateMixin {
  // خدمات ومتغيرات
  final ImagePicker _picker = ImagePicker();
  final ImageProcessingService _imageService = ImageProcessingService();
  final EnhancedAdDraftService _draftService = EnhancedAdDraftService();

  // متغيرات الحالة
  List<File> _pickedFiles = [];
  bool _isProcessing = false;
  String? _errorMessage;
  bool _showTips = true;
  final bool _isFullScreenPreview = false;
  final int _currentPreviewIndex = 0;
  final bool _isDragging = false;
  File? _draggedFile;

  // متغيرات الرسوم المتحركة
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  // ثوابت
  final int _maxImages = 10;
  final double _maxSizeMB = 5.0;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300));

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOutBack));

    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));

    // تشغيل الرسوم المتحركة
    _controller.forward();
    _fadeController.forward();
    _slideController.forward();

    // تحميل المسودة
    _loadDraft();
  }

  @override
  void dispose() {
    _controller.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  /// تحميل المسودة
  Future<void> _loadDraft() async {
    final lastDraft = await _draftService.getLastDraft();
    if (lastDraft != null && mounted) {
      if (lastDraft.containsKey('imagePaths')) {
        final List<dynamic> paths = lastDraft['imagePaths'] as List<dynamic>;
        if (paths.isNotEmpty) {
          setState(() {
            _pickedFiles = paths.map((path) => File(path.toString())).toList();
          });
        }
      }
    }
  }

  /// اختيار صور من المعرض
  Future<void> _pickImages() async {
    try {
      setState(() {
        _isProcessing = true;
        _errorMessage = null;
      });

      final files = await _picker.pickMultiImage();
      if (files.isNotEmpty) {
        final availableSlots = _maxImages - _pickedFiles.length;
        if (availableSlots > 0) {
          // تحويل XFile إلى File
          final newFiles =
              files.take(availableSlots).map((f) => File(f.path)).toList();

          // معالجة الصور
          await _processImages(newFiles);
        } else {
          setState(() {
            _errorMessage =
                "لا يمكن إضافة المزيد من الصور. الحد الأقصى هو $_maxImages صور.";
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = "حدث خطأ أثناء اختيار الصور. يرجى المحاولة مرة أخرى.";
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    try {
      setState(() {
        _isProcessing = true;
        _errorMessage = null;
      });

      final file = await _picker.pickImage(source: ImageSource.camera);
      if (file != null) {
        if (_pickedFiles.length < _maxImages) {
          // تحويل XFile إلى File
          final newFile = File(file.path);

          // معالجة الصورة
          await _processImages([newFile]);
        } else {
          setState(() {
            _errorMessage =
                "لا يمكن إضافة المزيد من الصور. الحد الأقصى هو $_maxImages صور.";
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = "حدث خطأ أثناء التقاط الصورة. يرجى المحاولة مرة أخرى.";
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// معالجة الصور (التحقق من الصحة والضغط)
  Future<void> _processImages(List<File> files) async {
    for (final file in files) {
      // التحقق من حجم الصورة
      final isValidSize =
          await _imageService.isImageSizeValid(file, _maxSizeMB);
      if (!isValidSize) {
        setState(() {
          _isProcessing = false;
          _errorMessage =
              "حجم الصورة كبير جدًا. يجب أن لا يتجاوز $_maxSizeMB ميجابايت.";
        });
        return;
      }

      // التحقق من أبعاد الصورة
      final isValidDimensions =
          await _imageService.isImageDimensionsValid(file);
      if (!isValidDimensions) {
        setState(() {
          _isProcessing = false;
          _errorMessage =
              "أبعاد الصورة صغيرة جدًا. يجب أن لا تقل عن 300×300 بكسل.";
        });
        return;
      }

      // التحقق من نوع الصورة
      final isValidType = _imageService.isImageTypeValid(file);
      if (!isValidType) {
        setState(() {
          _isProcessing = false;
          _errorMessage =
              "نوع الصورة غير مدعوم. الأنواع المدعومة: JPG, JPEG, PNG.";
        });
        return;
      }
    }

    // ضغط الصور
    final compressedFiles =
        await _imageService.compressImages(imageFiles: files);

    setState(() {
      _pickedFiles.addAll(compressedFiles);
    });

    // حفظ المسودة
    _saveDraft();
  }

  /// تحرير صورة
  Future<void> _editImage(int index) async {
    // إظهار مؤشر التحميل
    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    // حفظ المرجع للألوان قبل العملية غير المتزامنة
    final primaryColor = Theme.of(context).primaryColor;

    try {
      final file = _pickedFiles[index];

      // التحقق من وجود الملف
      if (!await file.exists()) {
        throw Exception('الملف غير موجود');
      }

      final croppedFile = await ImageCropper().cropImage(
        sourcePath: file.path,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 85,
        maxWidth: 1920,
        maxHeight: 1920,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'تحرير الصورة',
            toolbarColor: primaryColor,
            toolbarWidgetColor: Colors.white,
            statusBarColor: primaryColor,
            backgroundColor: Colors.white,
            activeControlsWidgetColor: primaryColor,
            lockAspectRatio: false,
            hideBottomControls: false,
            initAspectRatio: CropAspectRatioPreset.original,
            aspectRatioPresets: [
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.square,
              CropAspectRatioPreset.ratio3x2,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio16x9
            ]),
          IOSUiSettings(
            title: 'تحرير الصورة',
            doneButtonTitle: 'تم',
            cancelButtonTitle: 'إلغاء',
            aspectRatioPresets: [
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.square,
              CropAspectRatioPreset.ratio3x2,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio16x9
            ]),
        ]);

      if (croppedFile != null) {
        setState(() {
          _pickedFiles[index] = File(croppedFile.path);
        });

        // حفظ المسودة
        await _saveDraft();

        // إظهار رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم تحرير الصورة بنجاح',
                style: GoogleFonts.cairo()),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8)),
            ));
        }
      }
    } catch (e) {
      // استخدام debugPrint بدلاً من print
      debugPrint('Error editing image: $e');
      if (mounted) {
        setState(() {
          _errorMessage = "حدث خطأ أثناء تحرير الصورة. يرجى المحاولة مرة أخرى.";
        });

        // إظهار رسالة خطأ مفصلة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تحرير الصورة: ${e.toString()}',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8)),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _editImage(index),
            )));
      }
    } finally {
      // إخفاء مؤشر التحميل
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// حذف صورة
  void _deleteImage(int index) {
    setState(() {
      _pickedFiles.removeAt(index);
    });

    // حفظ المسودة
    _saveDraft();
  }

  // تم إزالة دالة _onReorder لأننا لا نستخدم ReorderableGridView

  /// حفظ المسودة
  Future<void> _saveDraft() async {
    final paths = _pickedFiles.map((f) => f.path).toList();
    await _draftService.saveDraft({
      'imagePaths': paths,
      'step': 2,
    });
  }

  /// فتح فيديو المساعدة
  Future<void> _launchVideo() async {
    const url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  /// الانتقال إلى الخطوة التالية
  void _goToNextStep() {
    // حفظ الصور في BLoC
    final paths = _pickedFiles.map((f) => f.path).toList();
    context.read<ImprovedAdBloc>().add(AddImages(paths));

    // حفظ المسودة مع الصور
    _saveDraftWithImages(paths);

    // الانتقال إلى صفحة التفاصيل
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => ImprovedAdDetailsPage(
          estate: widget.estate,
          isEditing: widget.isEditing)));
  }

  /// حفظ المسودة مع الصور
  void _saveDraftWithImages(List<String> imagePaths) {
    final adState = context.read<ImprovedAdBloc>().state;

    final mainCategory = adState.mainCategory;
    final subCategory = adState.subCategory;

    _draftService.autoSaveDraft({
      'mainCategory': mainCategory,
      'subCategory': subCategory,
      'imagePaths': imagePaths,
      'step': 2,
      'title': 'مسودة $mainCategory - $subCategory',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  @override
  Widget build(BuildContext context) {
    final canProceed = _pickedFiles.isNotEmpty;

    return Scaffold(
      backgroundColor: Colors.white,
      // مؤشر التقدم وأزرار التنقل أسفل الصفحة
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أزرار التنقل
          AdCreationNavigationButtons(
            onNext: _goToNextStep,
            onBack: () => Navigator.pop(context),
            nextText: "متابعة",
            backText: "العودة",
            isNextDisabled: !canProceed),

          // مؤشر التقدم
          ImprovedAdCreationProgress(
            currentStep: 2,
            onStepTap: (step) {
              if (step == 1) {
                Navigator.pop(context);
              }
            }),
        ]),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // ترويسة مع زر رجوع وعنوان
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.black87),
                    onPressed: () => Navigator.of(context).pop()),
                  const SizedBox(width: 8),
                  Text(
                    "إضافة صور العقار",
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87)),
                  const Spacer(),
                  // زر إظهار/إخفاء النصائح
                  IconButton(
                    icon: Icon(
                      _showTips ? Icons.lightbulb : Icons.lightbulb_outline,
                      color: _showTips ? Colors.amber : Colors.grey),
                    onPressed: () {
                      setState(() {
                        _showTips = !_showTips;
                      });
                    }),
                ]),

              // نصائح لتحسين جودة الصور
              if (_showTips)
                AnimatedSize(
                  duration: const Duration(milliseconds: 300),
                  child: Card(
                    color: Colors.blue.shade50,
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.tips_and_updates,
                                  color: Colors.blue.shade700),
                              const SizedBox(width: 8),
                              Text(
                                "نصائح لصور أفضل",
                                style: GoogleFonts.cairo(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade700)),
                            ]),
                          const SizedBox(height: 8),
                          Text(
                            "• التقط صوراً بإضاءة جيدة وواضحة\n"
                            "• أضف صوراً لجميع غرف العقار\n"
                            "• تأكد من نظافة وترتيب المكان قبل التصوير\n"
                            "• استخدم الوضع الأفقي للحصول على صور أوسع",
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.blue.shade900,
                              height: 1.3)),
                          const SizedBox(height: 8),
                          // رابط لفيديو المساعدة
                          RichText(
                            text: TextSpan(
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.blue.shade900),
                              children: [
                                const TextSpan(text: "لمزيد من النصائح، "),
                                TextSpan(
                                  text: "شاهد الفيديو التوضيحي",
                                  style: TextStyle(
                                    color: Colors.blue.shade700,
                                    decoration: TextDecoration.underline),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = _launchVideo),
                              ])),
                        ])))),

              const SizedBox(height: 16),

              // أزرار إضافة الصور
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isProcessing ? null : _pickImages,
                      icon: const Icon(Icons.photo_library),
                      label: Text(
                        "اختيار من المعرض",
                        style: GoogleFonts.cairo()),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12)))),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isProcessing ? null : _takePhoto,
                      icon: const Icon(Icons.camera_alt),
                      label: Text(
                        "التقاط صورة",
                        style: GoogleFonts.cairo()),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12)))),
                ]),

              const SizedBox(height: 16),

              // عرض رسالة الخطأ إذا وجدت
              if (_errorMessage != null)
                ErrorMessageWidget(
                  message: _errorMessage!,
                  onRetry: () {
                    setState(() {
                      _errorMessage = null;
                    });
                  }),

              // عرض مؤشر التحميل أثناء معالجة الصور
              if (_isProcessing)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(10),
                            blurRadius: 10,
                            spreadRadius: 1),
                        ]),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 40,
                            height: 40,
                            child: CircularProgressIndicator(
                              strokeWidth: 3,
                              valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor))),
                          const SizedBox(height: 16),
                          Text(
                            "جاري معالجة الصور...",
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800)),
                          const SizedBox(height: 8),
                          Text(
                            "يتم التحقق من الحجم والأبعاد وضغط الصور",
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey.shade600),
                            textAlign: TextAlign.center),
                        ])))),

              // عنوان قسم الصور
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Row(
                  children: [
                    Text(
                      "الصور المختارة",
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(width: 8),
                    Text(
                      "(${_pickedFiles.length}/$_maxImages)",
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey.shade600)),
                    const Spacer(),
                    if (_pickedFiles.isNotEmpty)
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _pickedFiles.clear();
                          });
                          _saveDraft();
                        },
                        icon: const Icon(Icons.delete_sweep, size: 16),
                        label: Text(
                          "حذف الكل",
                          style: GoogleFonts.cairo(fontSize: 12)),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.red)),
                  ])),

              // عرض الصور المختارة
              Expanded(
                child: _pickedFiles.isEmpty
                    ? FadeTransition(
                        opacity: _fadeAnimation,
                        child: SlideTransition(
                          position: _slideAnimation,
                          child: Container(
                            margin: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                  color: Colors.grey.shade200, width: 1),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.shade200,
                                  blurRadius: 10,
                                  offset: const Offset(0, 4)),
                              ]),
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.shade50,
                                      shape: BoxShape.circle),
                                    child: Icon(
                                      Icons.add_photo_alternate,
                                      size: 64,
                                      color: Colors.blue.shade300)),
                                  const SizedBox(height: 24),
                                  Text(
                                    "لم يتم اختيار أي صورة بعد",
                                    style: GoogleFonts.cairo(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey.shade700)),
                                  const SizedBox(height: 12),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 32),
                                    child: Text(
                                      "يجب إضافة صورة واحدة على الأقل لإكمال إعلانك",
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.cairo(
                                        fontSize: 14,
                                        color: Colors.grey.shade600,
                                        height: 1.5))),
                                  const SizedBox(height: 24),
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      ElevatedButton.icon(
                                        onPressed: _pickImages,
                                        icon: const Icon(Icons.photo_library),
                                        label: Text(
                                          "اختيار من المعرض",
                                          style: GoogleFonts.cairo()),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.blue,
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16, vertical: 12),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12)))),
                                    ]),
                                ])))))
                    : ScaleTransition(
                        scale: _scaleAnimation,
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          child: GridView.builder(
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              childAspectRatio: 1,
                              crossAxisSpacing: 8,
                              mainAxisSpacing: 8),
                            itemCount: _pickedFiles.length,
                            itemBuilder: (context, index) {
                              return Stack(
                                key: ValueKey(_pickedFiles[index].path),
                                children: [
                                  // الصورة
                                  AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      border: index == 0
                                          ? Border.all(
                                              color: Colors.blue.shade600,
                                              width: 3)
                                          : null,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withAlpha(40),
                                          blurRadius: 6,
                                          offset: const Offset(0, 2)),
                                      ]),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: Stack(
                                        fit: StackFit.expand,
                                        children: [
                                          Image.file(
                                            _pickedFiles[index],
                                            fit: BoxFit.cover,
                                            width: double.infinity,
                                            height: double.infinity),
                                          // Overlay gradient for better visibility of icons
                                          Positioned.fill(
                                            child: DecoratedBox(
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  begin: Alignment.topCenter,
                                                  end: Alignment.bottomCenter,
                                                  colors: [
                                                    Colors.transparent,
                                                    Colors.black.withAlpha(70),
                                                  ],
                                                  stops: const [0.7, 1.0])))),
                                        ]))),

                                  // شارة الصورة الرئيسية
                                  if (index == 0)
                                    Positioned(
                                      top: 8,
                                      right: 8,
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4),
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              Colors.blue.shade700,
                                              Colors.blue.shade500
                                            ],
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withAlpha(50),
                                              blurRadius: 4,
                                              offset: const Offset(0, 2)),
                                          ]),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            const Icon(
                                              Icons.star,
                                              color: Colors.white,
                                              size: 12),
                                            const SizedBox(width: 4),
                                            Text(
                                              "رئيسية",
                                              style: GoogleFonts.cairo(
                                                fontSize: 10,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white)),
                                          ]))),

                                  // أزرار التحرير والحذف
                                  Positioned(
                                    bottom: 8,
                                    left: 8,
                                    right: 8,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        Material(
                                          color: Colors.transparent,
                                          child: InkWell(
                                            onTap: () => _editImage(index),
                                            borderRadius:
                                                BorderRadius.circular(20),
                                            child: Container(
                                              padding: const EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                color:
                                                    Colors.blue.withAlpha(200),
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withAlpha(30),
                                                    blurRadius: 4,
                                                    offset: const Offset(0, 2)),
                                                ]),
                                              child: const Icon(
                                                Icons.edit,
                                                color: Colors.white,
                                                size: 16)))),
                                        Material(
                                          color: Colors.transparent,
                                          child: InkWell(
                                            onTap: () => _deleteImage(index),
                                            borderRadius:
                                                BorderRadius.circular(20),
                                            child: Container(
                                              padding: const EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                color:
                                                    Colors.red.withAlpha(200),
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withAlpha(30),
                                                    blurRadius: 4,
                                                    offset: const Offset(0, 2)),
                                                ]),
                                              child: const Icon(
                                                Icons.delete,
                                                color: Colors.white,
                                                size: 16)))),
                                      ])),
                                ]);
                            })))),
            ]))));
  }
}
