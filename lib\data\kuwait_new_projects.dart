/// المشاريع التنموية الجديدة والمناطق قيد التطوير في دولة الكويت
/// تاريخ التحديث: ديسمبر 2024
library;

class KuwaitNewProjects {
  /// المدن والمشاريع الجديدة
  static const Map<String, Map<String, dynamic>> newCities = {
    'مدينة جابر الأحمد': {
      'المحافظة': 'محافظة العاصمة',
      'الحالة': 'قيد التطوير',
      'المساحة': '17000 هكتار',
      'السكان المتوقع': '400000 نسمة',
      'الوصف': 'أكبر مدينة سكنية في الكويت',
      'المراحل': [
        'المرحلة الأولى - مكتملة',
        'المرحلة الثانية - قيد التنفيذ',
        'المرحلة الثالثة - قيد التخطيط',
        'المرحلة الرابعة - قيد التخطيط',
      ],
    },
    'مدينة صباح الأحمد': {
      'المحافظة': 'محافظة الأحمدي',
      'الحالة': 'قيد التطوير',
      'المساحة': '750 كيلومتر مربع',
      'السكان المتوقع': '400000 نسمة',
      'الوصف': 'مدينة سكنية متكاملة جنوب الكويت',
      'المراحل': [
        'المرحلة الأولى - مكتملة جزئياً',
        'المرحلة الثانية - قيد التنفيذ',
        'المرحلة الثالثة - قيد التخطيط',
      ],
    },
    'مدينة صباح الأحمد البحرية': {
      'المحافظة': 'محافظة الأحمدي',
      'الحالة': 'قيد التطوير',
      'النوع': 'مدينة سياحية بحرية',
      'الوصف': 'مشروع سياحي وترفيهي على الساحل',
      'المميزات': [
        'شواطئ خاصة',
        'مرافق سياحية',
        'فنادق ومنتجعات',
        'مراكز تجارية',
      ],
    },
    'مدينة الحرير': {
      'المحافظة': 'محافظة الجهراء',
      'الحالة': 'قيد التخطيط',
      'المساحة': '250 كيلومتر مربع',
      'النوع': 'مدينة اقتصادية',
      'الوصف': 'مشروع اقتصادي ضخم شمال الكويت',
      'المكونات': [
        'المنطقة المالية',
        'المنطقة السكنية',
        'المنطقة التجارية',
        'المنطقة الصناعية',
        'المنطقة السياحية',
      ],
    },
    'مدينة سعد العبد الله': {
      'المحافظة': 'محافظة الجهراء',
      'الحالة': 'مكتملة جزئياً',
      'النوع': 'مدينة سكنية',
      'الوصف': 'مدينة سكنية للمواطنين',
      'المراحل': [
        'المرحلة الأولى - مكتملة',
        'المرحلة الثانية - مكتملة',
        'المرحلة الثالثة - قيد التطوير',
      ],
    },
    'مدينة الخيران': {
      'المحافظة': 'محافظة الأحمدي',
      'الحالة': 'قيد التطوير',
      'النوع': 'مدينة سياحية',
      'الوصف': 'مشروع سياحي متكامل',
      'المميزات': [
        'منتجعات سياحية',
        'مراكز تسوق',
        'مرافق ترفيهية',
        'شواطئ',
      ],
    },
  };

  /// المناطق الصناعية الجديدة
  static const Map<String, Map<String, dynamic>> newIndustrialAreas = {
    'المنطقة الصناعية الجديدة بالشعيبة': {
      'المحافظة': 'محافظة الأحمدي',
      'الحالة': 'قيد التطوير',
      'التخصص': 'صناعات بتروكيماوية',
    },
    'المنطقة الصناعية بالصبية': {
      'المحافظة': 'محافظة الجهراء',
      'الحالة': 'قيد التخطيط',
      'التخصص': 'صناعات متنوعة',
    },
    'المنطقة الحرة بالشويخ': {
      'المحافظة': 'محافظة العاصمة',
      'الحالة': 'قيد التوسع',
      'التخصص': 'تجارة وخدمات لوجستية',
    },
  };

  /// المشاريع الإسكانية الجديدة
  static const Map<String, Map<String, dynamic>> housingProjects = {
    'مشروع جنوب سعد العبد الله': {
      'المحافظة': 'محافظة الجهراء',
      'عدد الوحدات': '12000 وحدة',
      'الحالة': 'قيد التنفيذ',
    },
    'مشروع شرق تيماء': {
      'المحافظة': 'محافظة الفروانية',
      'عدد الوحدات': '8000 وحدة',
      'الحالة': 'قيد التخطيط',
    },
    'مشروع غرب عبد الله المبارك': {
      'المحافظة': 'محافظة الفروانية',
      'عدد الوحدات': '6000 وحدة',
      'الحالة': 'قيد التنفيذ',
    },
    'مشروع شمال الصليبيخات': {
      'المحافظة': 'محافظة العاصمة',
      'عدد الوحدات': '4000 وحدة',
      'الحالة': 'قيد التطوير',
    },
  };

  /// المشاريع السياحية والترفيهية
  static const Map<String, Map<String, dynamic>> tourismProjects = {
    'مشروع تطوير جزيرة فيلكا': {
      'المحافظة': 'محافظة العاصمة',
      'النوع': 'سياحي تراثي',
      'الحالة': 'قيد التخطيط',
      'المكونات': [
        'متحف تراثي',
        'فنادق بوتيك',
        'مرافق ترفيهية',
        'مراكز ثقافية',
      ],
    },
    'مشروع الواجهة البحرية للكويت': {
      'المحافظة': 'محافظة العاصمة',
      'النوع': 'ترفيهي تجاري',
      'الحالة': 'قيد التطوير',
      'المكونات': [
        'كورنيش بحري',
        'مراكز تجارية',
        'مطاعم ومقاهي',
        'مرافق ترفيهية',
      ],
    },
    'مشروع الخيران السياحي': {
      'المحافظة': 'محافظة الأحمدي',
      'النوع': 'سياحي بحري',
      'الحالة': 'قيد التنفيذ',
      'المكونات': [
        'منتجعات',
        'مارينا',
        'ملاعب جولف',
        'مراكز مؤتمرات',
      ],
    },
  };

  /// مشاريع النقل والمواصلات
  static const Map<String, Map<String, dynamic>> transportProjects = {
    'مترو الكويت': {
      'الحالة': 'قيد التخطيط',
      'الخطوط': [
        'الخط الأحمر - العاصمة إلى الأحمدي',
        'الخط الأزرق - الفروانية إلى حولي',
        'الخط الأخضر - الجهراء إلى مبارك الكبير',
      ],
      'الطول الإجمالي': '160 كيلومتر',
    },
    'مطار الكويت الجديد': {
      'المحافظة': 'محافظة الفروانية',
      'الحالة': 'قيد التنفيذ',
      'السعة': '25 مليون مسافر سنوياً',
    },
    'ميناء مبارك الكبير': {
      'المحافظة': 'محافظة الجهراء',
      'الحالة': 'مكتمل جزئياً',
      'النوع': 'ميناء تجاري',
    },
  };

  /// الحصول على معلومات المدينة الجديدة
  static Map<String, dynamic>? getNewCityInfo(String cityName) {
    return newCities[cityName];
  }

  /// الحصول على جميع المدن الجديدة
  static List<String> getAllNewCities() {
    return newCities.keys.toList();
  }

  /// الحصول على المدن الجديدة حسب المحافظة
  static List<String> getNewCitiesByGovernorate(String governorate) {
    return newCities.entries
        .where((entry) => entry.value['المحافظة'] == governorate)
        .map((entry) => entry.key)
        .toList();
  }

  /// الحصول على المشاريع حسب الحالة
  static List<String> getProjectsByStatus(String status) {
    List<String> projects = [];
    
    newCities.forEach((name, info) {
      if (info['الحالة'] == status) {
        projects.add(name);
      }
    });
    
    return projects;
  }

  /// البحث في المشاريع
  static List<String> searchProjects(String query) {
    List<String> allProjects = [];
    allProjects.addAll(newCities.keys);
    allProjects.addAll(newIndustrialAreas.keys);
    allProjects.addAll(housingProjects.keys);
    allProjects.addAll(tourismProjects.keys);
    allProjects.addAll(transportProjects.keys);
    
    if (query.isEmpty) return allProjects;
    
    return allProjects
        .where((project) => project.contains(query))
        .toList();
  }

  /// الحصول على إحصائيات المشاريع
  static Map<String, int> getProjectsStatistics() {
    return {
      'المدن الجديدة': newCities.length,
      'المناطق الصناعية': newIndustrialAreas.length,
      'المشاريع الإسكانية': housingProjects.length,
      'المشاريع السياحية': tourismProjects.length,
      'مشاريع النقل': transportProjects.length,
    };
  }
}
