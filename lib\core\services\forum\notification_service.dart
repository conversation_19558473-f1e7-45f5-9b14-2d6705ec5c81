import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';

import '../../../domain/models/forum/notification_model.dart';

/// خدمة إشعارات المنتدى
class ForumNotificationService {
  /// مرجع Firestore
  final FirebaseFirestore _firestore;

  /// مرجع Firebase Messaging
  final FirebaseMessaging _messaging;

  /// Flutter Local Notifications reference
  final FlutterLocalNotificationsPlugin _localNotifications;

  /// Notifications collection
  final CollectionReference _notificationsCollection;

  /// Constructor
  ForumNotificationService({
    FirebaseFirestore? firestore,
    FirebaseMessaging? messaging,
    FlutterLocalNotificationsPlugin? localNotifications,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _messaging = messaging ?? FirebaseMessaging.instance,
        _localNotifications = localNotifications ?? FlutterLocalNotificationsPlugin(),
        _notificationsCollection =
            (firestore ?? FirebaseFirestore.instance).collection('forum_notifications');

  /// Initialize service
  Future<void> initialize() async {
    try {
      // Request notification permissions
      final settings = await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false);

      if (settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional) {
        // Local notifications initialization
        const initializationSettingsAndroid =
            AndroidInitializationSettings('@mipmap/ic_launcher');
        const initializationSettingsIOS = DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true);
        const initializationSettings = InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS);

        await _localNotifications.initialize(
          initializationSettings,
          onDidReceiveNotificationResponse: _onNotificationTapped);

        // Listen for background notifications
        FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

        // Listen for foreground notifications
        FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      }
    } catch (e) {
      debugPrint('Failed to initialize notification service: $e');
    }
  }

  /// Handle background notifications
  static Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
    debugPrint('Received background notification: ${message.notification?.title}');
  }

  /// Handle foreground notifications
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    try {
      final notification = message.notification;

      if (notification != null) {
        // Show local notification
        await _localNotifications.show(
          notification.hashCode,
          notification.title,
          notification.body,
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'forum_channel',
              'Forum Notifications',
              channelDescription: 'Forum notification channel',
              importance: Importance.max,
              priority: Priority.high,
              icon: '@mipmap/ic_launcher'),
            iOS: DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: true)),
          payload: message.data['payload']);
      }
    } catch (e) {
      debugPrint('Failed to handle foreground notification: $e');
    }
  }

  /// Notification tap handler
  void _onNotificationTapped(NotificationResponse response) {
    try {
      final payload = response.payload;
      if (payload != null) {
        debugPrint('Notification tapped with payload: $payload');
        // يمكن إضافة منطق التنقل هنا
      }
    } catch (e) {
      debugPrint('Failed to handle notification tap: $e');
    }
  }

  /// تسجيل رمز الجهاز للمستخدم
  Future<void> registerDeviceToken(String userId) async {
    try {
      final token = await _messaging.getToken();
      if (token != null) {
        await _firestore.collection('user_devices').doc(userId).set({
          'tokens': FieldValue.arrayUnion([token]),
          'updatedAt': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      }
    } catch (e) {
      print('فشل في تسجيل رمز الجهاز: $e');
    }
  }

  /// إلغاء تسجيل رمز الجهاز للمستخدم
  Future<void> unregisterDeviceToken(String userId) async {
    try {
      final token = await _messaging.getToken();
      if (token != null) {
        await _firestore.collection('user_devices').doc(userId).update({
          'tokens': FieldValue.arrayRemove([token]),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      print('فشل في إلغاء تسجيل رمز الجهاز: $e');
    }
  }

  /// إرسال إشعار
  Future<void> sendNotification({
    required String userId,
    required String title,
    required String body,
    required NotificationType type,
    required String referenceId,
    String? imageUrl,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // إنشاء الإشعار
      final notification = NotificationModel(
        id: '',
        userId: userId,
        title: title,
        body: body,
        type: type,
        referenceId: referenceId,
        imageUrl: imageUrl,
        isRead: false,
        createdAt: DateTime.now(),
        additionalData: additionalData);

      // حفظ الإشعار في Firestore
      final docRef = await _notificationsCollection.add(notification.toMap());

      // تحديث معرف الإشعار
      await docRef.update({'id': docRef.id});

      // إرسال الإشعار إلى الجهاز
      await _sendPushNotification(userId, title, body, {
        'type': type.toString(),
        'referenceId': referenceId,
        'notificationId': docRef.id,
        ...?additionalData,
      });
    } catch (e) {
      print('فشل في إرسال الإشعار: $e');
    }
  }

  /// إرسال إشعار دفع
  Future<void> _sendPushNotification(
    String userId,
    String title,
    String body,
    Map<String, dynamic> data) async {
    try {
      // الحصول على رموز أجهزة المستخدم
      final userDevices = await _firestore.collection('user_devices').doc(userId).get();
      final tokens = userDevices.data()?['tokens'] as List<dynamic>? ?? [];

      if (tokens.isEmpty) {
        return;
      }

      // إرسال الإشعار إلى كل جهاز
      for (final token in tokens) {
        await _messaging.sendMessage(
          to: token,
          data: {
            'title': title,
            'body': body,
            ...data,
          });
      }
    } catch (e) {
      print('فشل في إرسال إشعار الدفع: $e');
    }
  }

  /// الحصول على إشعارات المستخدم
  Stream<List<NotificationModel>> getUserNotifications(String userId) {
    return _notificationsCollection
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => NotificationModel.fromFirestore(doc))
            .toList());
  }

  /// تعليم الإشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationsCollection.doc(notificationId).update({
        'isRead': true,
      });
    } catch (e) {
      print('فشل في تعليم الإشعار كمقروء: $e');
    }
  }

  /// تعليم جميع إشعارات المستخدم كمقروءة
  Future<void> markAllAsRead(String userId) async {
    try {
      final batch = _firestore.batch();
      final notifications = await _notificationsCollection
          .where('userId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      for (final doc in notifications.docs) {
        batch.update(doc.reference, {'isRead': true});
      }

      await batch.commit();
    } catch (e) {
      print('فشل في تعليم جميع الإشعارات كمقروءة: $e');
    }
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _notificationsCollection.doc(notificationId).delete();
    } catch (e) {
      print('فشل في حذف الإشعار: $e');
    }
  }

  /// حذف جميع إشعارات المستخدم
  Future<void> deleteAllNotifications(String userId) async {
    try {
      final batch = _firestore.batch();
      final notifications = await _notificationsCollection
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in notifications.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      print('فشل في حذف جميع الإشعارات: $e');
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  Stream<int> getUnreadCount(String userId) {
    return _notificationsCollection
        .where('userId', isEqualTo: userId)
        .where('isRead', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }
}
