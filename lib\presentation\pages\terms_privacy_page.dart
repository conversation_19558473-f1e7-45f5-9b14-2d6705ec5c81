// lib/presentation/pages/terms_privacy_page.dart
import 'package:flutter/material.dart';

class TermsPrivacyPage extends StatelessWidget {
  const TermsPrivacyPage({super.key});

  final String _termsText = """
شروط الاستخدام:
1. يجب استخدام التطبيق بما يتوافق مع القوانين المحلية والدولية.
2. لا يجوز إساءة استخدام التطبيق أو التلاعب بالبيانات.
3. تتحمل الشركة مسؤولية تقديم الخدمة وفقاً للشروط المحددة دون تحمل مسؤولية الاستخدام الخاطئ.
  """;

  final String _privacyText = """
سياسة الخصوصية:
1. نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية.
2. يتم جمع البيانات الشخصية لأغراض تحسين جودة الخدمة فقط.
3. لا تتم مشاركة بياناتك مع جهات خارجية دون موافقتك الصريحة.
  """;

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text("شروط الاستخدام والخصوصية"),
          bottom: const TabBar(
            tabs: [
              Tab(text: "الشروط"),
              Tab(text: "الخصوصية"),
            ])),
        body: TabBarView(
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: Text(
                  _termsText,
                  style: const TextStyle(fontSize: 16, height: 1.5)))),
            Padding(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: Text(
                  _privacyText,
                  style: const TextStyle(fontSize: 16, height: 1.5)))),
          ])));
  }
}
