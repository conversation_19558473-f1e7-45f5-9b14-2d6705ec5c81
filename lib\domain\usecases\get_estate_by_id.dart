import '../entities/estate_base.dart';
import '../repositories/estate_repository.dart';

/// حالة استخدام للحصول على عقار بواسطة المعرف
class GetEstateById {
  final EstateRepository repository;

  GetEstateById(this.repository);

  /// الحصول على عقار بواسطة المعرف
  /// [id] هو معرف العقار
  /// يعيد العقار إذا وجد، أو null إذا لم يجد
  Future<EstateBase?> call(String id) async {
    return await repository.getEstateById(id);
  }
}
