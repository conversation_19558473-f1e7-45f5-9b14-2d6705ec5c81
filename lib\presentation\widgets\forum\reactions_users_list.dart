import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/theme/app_colors.dart';
import 'reactions_button.dart';

/// نموذج معلومات المستخدم المتفاعل
class ReactingUserInfo {
  /// معرف المستخدم
  final String userId;

  /// اسم المستخدم
  final String userName;

  /// صورة المستخدم
  final String? userImage;

  /// نوع التفاعل
  final ReactionType reactionType;

  const ReactingUserInfo({
    required this.userId,
    required this.userName,
    this.userImage,
    required this.reactionType,
  });
}

/// قائمة المستخدمين المتفاعلين
class ReactionsUsersList extends StatefulWidget {
  /// التفاعلات
  final Map<String, List<String>> reactions;

  /// دالة للحصول على معلومات المستخدم
  final Future<ReactingUserInfo?> Function(String userId, ReactionType reactionType)
      getUserInfo;

  /// دالة يتم استدعاؤها عند النقر على مستخدم
  final Function(String userId)? onUserTap;

  const ReactionsUsersList({
    super.key,
    required this.reactions,
    required this.getUserInfo,
    this.onUserTap,
  });

  @override
  State<ReactionsUsersList> createState() => _ReactionsUsersListState();
}

class _ReactionsUsersListState extends State<ReactionsUsersList>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<String> _reactionTypes = [];
  bool _isLoading = true;
  final Map<String, List<ReactingUserInfo>> _usersMap = {};

  @override
  void initState() {
    super.initState();
    _reactionTypes = widget.reactions.keys.toList();
    _tabController = TabController(length: _reactionTypes.length + 1, vsync: this);
    _loadUsers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل معلومات المستخدمين
  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
    });

    // تحميل معلومات المستخدمين لكل نوع تفاعل
    for (final type in _reactionTypes) {
      final reactionType = _getReactionTypeFromString(type);
      if (reactionType == null) continue;

      final userIds = widget.reactions[type] ?? [];
      final users = <ReactingUserInfo>[];

      for (final userId in userIds) {
        final userInfo = await widget.getUserInfo(userId, reactionType);
        if (userInfo != null) {
          users.add(userInfo);
        }
      }

      _usersMap[type] = users;
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// الحصول على نوع التفاعل من السلسلة النصية
  ReactionType? _getReactionTypeFromString(String reactionString) {
    for (final reaction in ReactionType.values) {
      if (reaction.name == reactionString) {
        return reaction;
      }
    }
    return null;
  }

  /// الحصول على معلومات التفاعل من النوع
  ReactionInfo _getReactionInfo(ReactionType type) {
    return availableReactions.firstWhere(
      (reaction) => reaction.type == type,
      orElse: () => availableReactions.first);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط التبويب
        TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: AppColors.primary,
          unselectedLabelColor: Colors.grey.shade600,
          indicatorColor: AppColors.primary,
          tabs: [
            // تبويب الكل
            const Tab(text: 'الكل'),
            
            // تبويبات أنواع التفاعلات
            ..._reactionTypes.map((type) {
              final reactionType = _getReactionTypeFromString(type);
              if (reactionType == null) {
                return const Tab(text: 'غير معروف');
              }
              
              final reactionInfo = _getReactionInfo(reactionType);
              final count = widget.reactions[type]?.length ?? 0;
              
              return Tab(
                child: Row(
                  children: [
                    Text(reactionInfo.emoji),
                    const SizedBox(width: 4),
                    Text('${reactionInfo.name} ($count)'),
                  ]));
            }),
          ]),
        
        // محتوى التبويب
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // محتوى تبويب الكل
              _buildAllUsersTab(),
              
              // محتوى تبويبات أنواع التفاعلات
              ..._reactionTypes.map((type) {
                return _buildReactionTypeTab(type);
              }),
            ])),
      ]);
  }

  /// بناء تبويب كل المستخدمين
  Widget _buildAllUsersTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final allUsers = <ReactingUserInfo>[];
    for (final users in _usersMap.values) {
      allUsers.addAll(users);
    }

    if (allUsers.isEmpty) {
      return const Center(child: Text('لا يوجد متفاعلين'));
    }

    return ListView.builder(
      itemCount: allUsers.length,
      itemBuilder: (context, index) {
        final user = allUsers[index];
        return _buildUserListItem(user);
      });
  }

  /// بناء تبويب نوع تفاعل محدد
  Widget _buildReactionTypeTab(String type) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final users = _usersMap[type] ?? [];
    if (users.isEmpty) {
      return const Center(child: Text('لا يوجد متفاعلين'));
    }

    return ListView.builder(
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return _buildUserListItem(user);
      });
  }

  /// بناء عنصر قائمة المستخدم
  Widget _buildUserListItem(ReactingUserInfo user) {
    final reactionInfo = _getReactionInfo(user.reactionType);
    
    return ListTile(
      leading: CircleAvatar(
        backgroundImage: user.userImage != null
            ? CachedNetworkImageProvider(user.userImage!)
            : null,
        child: user.userImage == null
            ? Text(
                user.userName.isNotEmpty ? user.userName[0].toUpperCase() : '?',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold))
            : null),
      title: Text(user.userName),
      trailing: Text(
        reactionInfo.emoji,
        style: const TextStyle(fontSize: 20)),
      onTap: widget.onUserTap != null ? () => widget.onUserTap!(user.userId) : null);
  }
}
