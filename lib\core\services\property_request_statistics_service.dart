// lib/core/services/property_request_statistics_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

import '../../domain/models/property_request/property_request_model.dart';
import '../../domain/models/property_request/property_offer_model.dart';
import '../../domain/models/statistics/property_request_statistics_model.dart';

/// خدمة إحصائيات طلبات العقارات
class PropertyRequestStatisticsService {
  /// مثيل Firestore
  final FirebaseFirestore _firestore;

  /// مثيل Firebase Auth
  final FirebaseAuth _auth;

  /// إنشاء خدمة إحصائيات طلبات العقارات
  PropertyRequestStatisticsService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  /// الحصول على إحصائيات طلبات العقارات للمستخدم الحالي
  Future<PropertyRequestStatisticsModel> getUserStatistics() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول للحصول على الإحصائيات');
      }

      // الحصول على طلبات العقارات للمستخدم
      final requestsSnapshot = await _firestore
          .collection('propertyRequests')
          .where('userId', isEqualTo: user.uid)
          .get();

      // الحصول على عروض العقارات للمستخدم
      final offersSnapshot = await _firestore
          .collection('propertyOffers')
          .where('userId', isEqualTo: user.uid)
          .get();

      // الحصول على عروض العقارات على طلبات المستخدم
      final List<String> requestIds = requestsSnapshot.docs
          .map((doc) => doc.id)
          .toList();

      final List<QueryDocumentSnapshot> offersOnUserRequests = [];

      if (requestIds.isNotEmpty) {
        // تقسيم القائمة إلى مجموعات أصغر لتجنب تجاوز حد Firestore
        const int batchSize = 10;
        for (int i = 0; i < requestIds.length; i += batchSize) {
          final int end = (i + batchSize < requestIds.length) ? i + batchSize : requestIds.length;
          final batch = requestIds.sublist(i, end);

          final batchSnapshot = await _firestore
              .collection('propertyOffers')
              .where('requestId', whereIn: batch)
              .get();

          offersOnUserRequests.addAll(batchSnapshot.docs);
        }
      }

      // Calculate statistics
      final totalRequests = requestsSnapshot.docs.length;
      final activeRequests = requestsSnapshot.docs
          .where((doc) => doc.data()['status'] == RequestStatus.open.index)
          .length;
      final resolvedRequests = requestsSnapshot.docs
          .where((doc) => doc.data()['status'] == RequestStatus.resolved.index)
          .length;
      final closedRequests = requestsSnapshot.docs
          .where((doc) => doc.data()['status'] == RequestStatus.closed.index)
          .length;

      final totalOffers = offersSnapshot.docs.length;
      final acceptedOffers = offersSnapshot.docs
          .where((doc) => doc.data()['status'] == OfferStatus.accepted.name)
          .length;
      final rejectedOffers = offersSnapshot.docs
          .where((doc) => doc.data()['status'] == OfferStatus.rejected.name)
          .length;
      final pendingOffers = offersSnapshot.docs
          .where((doc) => doc.data()['status'] == OfferStatus.pending.name)
          .length;

      final totalOffersReceived = offersOnUserRequests.length;
      final acceptedOffersReceived = offersOnUserRequests
          .where((doc) {
            final data = doc.data() as Map<String, dynamic>?;
            return data != null && data['status'] == OfferStatus.accepted.index;
          })
          .length;
      final rejectedOffersReceived = offersOnUserRequests
          .where((doc) {
            final data = doc.data() as Map<String, dynamic>?;
            return data != null && data['status'] == OfferStatus.rejected.index;
          })
          .length;
      final pendingOffersReceived = offersOnUserRequests
          .where((doc) {
            final data = doc.data() as Map<String, dynamic>?;
            return data != null && data['status'] == OfferStatus.pending.index;
          })
          .length;

      // Calculate average response time
      final List<Duration> responseTimes = [];
      for (final doc in offersOnUserRequests) {
        final data = doc.data() as Map<String, dynamic>;
        if (data['status'] != OfferStatus.pending.name &&
            data['createdAt'] != null &&
            data['updatedAt'] != null) {
          final createdAt = (data['createdAt'] as Timestamp).toDate();
          final updatedAt = (data['updatedAt'] as Timestamp).toDate();
          responseTimes.add(updatedAt.difference(createdAt));
        }
      }

      final averageResponseTime = responseTimes.isNotEmpty
          ? (responseTimes.reduce((a, b) => a + b).inHours / responseTimes.length).toDouble()
          : 0.0;

      // Calculate average offers per request
      final averageOffersPerRequest = requestIds.isNotEmpty
          ? (totalOffersReceived / requestIds.length).toDouble()
          : 0.0;

      return PropertyRequestStatisticsModel(
        totalRequests: totalRequests,
        activeRequests: activeRequests,
        resolvedRequests: resolvedRequests,
        closedRequests: closedRequests,
        totalOffers: totalOffers,
        acceptedOffers: acceptedOffers,
        rejectedOffers: rejectedOffers,
        pendingOffers: pendingOffers,
        totalOffersReceived: totalOffersReceived,
        acceptedOffersReceived: acceptedOffersReceived,
        rejectedOffersReceived: rejectedOffersReceived,
        pendingOffersReceived: pendingOffersReceived,
        averageResponseTime: averageResponseTime,
        averageOffersPerRequest: averageOffersPerRequest);
    } catch (e) {
      debugPrint('Error getting property request statistics: $e');
      rethrow;
    }
  }

  /// Get general property request statistics
  Future<Map<String, dynamic>> getGeneralStatistics() async {
    try {
      // Get all property requests
      final requestsSnapshot = await _firestore
          .collection('propertyRequests')
          .get();

      // Get all property offers
      final offersSnapshot = await _firestore
          .collection('propertyOffers')
          .get();

      // Calculate statistics
      final totalRequests = requestsSnapshot.docs.length;
      final activeRequests = requestsSnapshot.docs
          .where((doc) {
            final data = doc.data() as Map<String, dynamic>?;
            return data != null && data['status'] == RequestStatus.open.index;
          })
          .length;
      final resolvedRequests = requestsSnapshot.docs
          .where((doc) {
            final data = doc.data() as Map<String, dynamic>?;
            return data != null && data['status'] == RequestStatus.resolved.index;
          })
          .length;

      final totalOffers = offersSnapshot.docs.length;
      final acceptedOffers = offersSnapshot.docs
          .where((doc) {
            final data = doc.data() as Map<String, dynamic>?;
            return data != null && data['status'] == OfferStatus.accepted.index;
          })
          .length;

      // Calculate average offers per request
      final averageOffersPerRequest = totalRequests > 0
          ? (totalOffers / totalRequests).toDouble()
          : 0.0;

      // Calculate resolution rate
      final resolutionRate = totalRequests > 0
          ? (resolvedRequests / totalRequests).toDouble()
          : 0.0;

      // Calculate acceptance rate
      final acceptanceRate = totalOffers > 0
          ? (acceptedOffers / totalOffers).toDouble()
          : 0.0;

      return {
        'totalRequests': totalRequests,
        'activeRequests': activeRequests,
        'resolvedRequests': resolvedRequests,
        'totalOffers': totalOffers,
        'acceptedOffers': acceptedOffers,
        'averageOffersPerRequest': averageOffersPerRequest,
        'resolutionRate': resolutionRate,
        'acceptanceRate': acceptanceRate,
      };
    } catch (e) {
      debugPrint('Error getting general statistics: $e');
      rethrow;
    }
  }

  /// Get statistics by property type
  Future<Map<String, dynamic>> getStatisticsByPropertyType() async {
    try {
      final requestsSnapshot = await _firestore
          .collection('propertyRequests')
          .get();

      final Map<String, int> requestsByType = {};
      final Map<String, int> resolvedByType = {};

      for (final doc in requestsSnapshot.docs) {
        final data = doc.data();
        final type = data['propertyType'] as String? ?? 'Undefined';
        final status = data['status'] as String?;

        requestsByType[type] = (requestsByType[type] ?? 0) + 1;

        if (status == RequestStatus.resolved.name) {
          resolvedByType[type] = (resolvedByType[type] ?? 0) + 1;
        }
      }

      final Map<String, double> resolutionRateByType = {};
      for (final type in requestsByType.keys) {
        resolutionRateByType[type] = requestsByType[type]! > 0
            ? (resolvedByType[type] ?? 0) / requestsByType[type]!
            : 0;
      }

      return {
        'requestsByType': requestsByType,
        'resolvedByType': resolvedByType,
        'resolutionRateByType': resolutionRateByType,
      };
    } catch (e) {
      debugPrint('Error getting statistics by property type: $e');
      rethrow;
    }
  }
}
