import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../../data/kuwait_locations.dart';
import '../../domain/entities/price_estimation.dart';
import '../../infrastructure/services/price_estimation_service.dart';
import '../widgets/app_bar_widget.dart';
import '../widgets/loading_widget.dart';

/// شاشة تقدير الأسعار
class PriceEstimationScreen extends StatefulWidget {
  /// إنشاء شاشة تقدير الأسعار
  const PriceEstimationScreen({super.key});

  @override
  _PriceEstimationScreenState createState() => _PriceEstimationScreenState();
}

class _PriceEstimationScreenState extends State<PriceEstimationScreen> {
  final _formKey = GlobalKey<FormState>();

  PriceEstimationType _estimationType = PriceEstimationType.sale;
  String _area = '';
  String _propertyType = '';
  double _size = 0;
  int? _rooms;
  int? _bathrooms;
  int? _age;
  int? _floor;
  bool? _isFurnished;
  bool? _isRenovated;
  final List<String> _features = [];

  bool _isLoading = false;
  String? _errorMessage;
  PriceEstimation? _estimation;

  // استخدام المناطق الكويتية من الملف الذكي
  List<String> get _availableAreas => KuwaitLocations.getAllAreas();

  final List<String> _availablePropertyTypes = [
    'شقة',
    'منزل',
    'أرض',
    'عمارة',
    'مكتب',
    'محل تجاري',
    'مخزن',
  ];

  final List<String> _availableFeatures = [
    'مسبح',
    'حديقة',
    'موقف سيارات',
    'مصعد',
    'تكييف مركزي',
    'أمن',
    'قريب من الخدمات',
    'إطلالة',
    'مدخل خاص',
    'مطبخ مجهز',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: 'تقدير سعر العقار'),
      body: _isLoading
          ? const LoadingWidget()
          : _errorMessage != null
              ? ErrorWidgetCustom(
                  message: _errorMessage!,
                  onRetry: () {
                    setState(() {
                      _errorMessage = null;
                    });
                  })
              : _estimation != null
                  ? _buildEstimationResult()
                  : _buildEstimationForm());
  }

  /// بناء نموذج تقدير السعر
  Widget _buildEstimationForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أدخل معلومات العقار للحصول على تقدير السعر',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 24),
            _buildEstimationTypeSelector(),
            const SizedBox(height: 16),
            _buildAreaDropdown(),
            const SizedBox(height: 16),
            _buildPropertyTypeDropdown(),
            const SizedBox(height: 16),
            _buildSizeField(),
            const SizedBox(height: 16),
            _buildRoomsField(),
            const SizedBox(height: 16),
            _buildBathroomsField(),
            const SizedBox(height: 16),
            _buildAgeField(),
            const SizedBox(height: 16),
            _buildFloorField(),
            const SizedBox(height: 16),
            _buildFurnishedSwitch(),
            const SizedBox(height: 16),
            _buildRenovatedSwitch(),
            const SizedBox(height: 16),
            _buildFeaturesSelector(),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _estimatePrice,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16)),
                child: const Text(
                  'تقدير السعر',
                  style: TextStyle(fontSize: 16)))),
          ])));
  }

  /// بناء محدد نوع التقدير
  Widget _buildEstimationTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نوع التقدير',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<PriceEstimationType>(
                title: const Text('سعر البيع'),
                value: PriceEstimationType.sale,
                groupValue: _estimationType,
                onChanged: (value) {
                  setState(() {
                    _estimationType = value!;
                  });
                },
                contentPadding: EdgeInsets.zero)),
            Expanded(
              child: RadioListTile<PriceEstimationType>(
                title: const Text('سعر الإيجار'),
                value: PriceEstimationType.rent,
                groupValue: _estimationType,
                onChanged: (value) {
                  setState(() {
                    _estimationType = value!;
                  });
                },
                contentPadding: EdgeInsets.zero)),
          ]),
      ]);
  }

  /// بناء قائمة منسدلة للمناطق
  Widget _buildAreaDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المنطقة',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8)),
          hint: const Text('اختر المنطقة'),
          isExpanded: true,
          value: _area.isEmpty ? null : _area,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'يرجى اختيار المنطقة';
            }
            return null;
          },
          onChanged: (value) {
            setState(() {
              _area = value!;
            });
          },
          items: _availableAreas
              .map((area) => DropdownMenuItem<String>(
                    value: area,
                    child: Text(area)))
              .toList()),
      ]);
  }

  /// بناء قائمة منسدلة لأنواع العقارات
  Widget _buildPropertyTypeDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نوع العقار',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8)),
          hint: const Text('اختر نوع العقار'),
          isExpanded: true,
          value: _propertyType.isEmpty ? null : _propertyType,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'يرجى اختيار نوع العقار';
            }
            return null;
          },
          onChanged: (value) {
            setState(() {
              _propertyType = value!;
            });
          },
          items: _availablePropertyTypes
              .map((type) => DropdownMenuItem<String>(
                    value: type,
                    child: Text(type)))
              .toList()),
      ]);
  }

  /// بناء حقل المساحة
  Widget _buildSizeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المساحة (متر مربع)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        TextFormField(
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            hintText: 'أدخل المساحة'),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'يرجى إدخال المساحة';
            }
            final size = double.tryParse(value);
            if (size == null || size <= 0) {
              return 'يرجى إدخال مساحة صحيحة';
            }
            return null;
          },
          onChanged: (value) {
            _size = double.tryParse(value) ?? 0;
          }),
      ]);
  }

  /// بناء حقل عدد الغرف
  Widget _buildRoomsField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'عدد الغرف',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        TextFormField(
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            hintText: 'أدخل عدد الغرف'),
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _rooms = int.tryParse(value);
          }),
      ]);
  }

  /// بناء حقل عدد الحمامات
  Widget _buildBathroomsField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'عدد الحمامات',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        TextFormField(
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            hintText: 'أدخل عدد الحمامات'),
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _bathrooms = int.tryParse(value);
          }),
      ]);
  }

  /// بناء حقل عمر العقار
  Widget _buildAgeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'عمر العقار (سنوات)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        TextFormField(
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            hintText: 'أدخل عمر العقار'),
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _age = int.tryParse(value);
          }),
      ]);
  }

  /// بناء حقل الطابق
  Widget _buildFloorField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الطابق',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        TextFormField(
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            hintText: 'أدخل الطابق'),
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _floor = int.tryParse(value);
          }),
      ]);
  }

  /// بناء مفتاح مفروش
  Widget _buildFurnishedSwitch() {
    return SwitchListTile(
      title: const Text(
        'مفروش',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold)),
      value: _isFurnished ?? false,
      onChanged: (value) {
        setState(() {
          _isFurnished = value;
        });
      },
      contentPadding: EdgeInsets.zero);
  }

  /// بناء مفتاح مجدد
  Widget _buildRenovatedSwitch() {
    return SwitchListTile(
      title: const Text(
        'مجدد',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold)),
      value: _isRenovated ?? false,
      onChanged: (value) {
        setState(() {
          _isRenovated = value;
        });
      },
      contentPadding: EdgeInsets.zero);
  }

  /// بناء محدد المميزات
  Widget _buildFeaturesSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المميزات',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableFeatures.map((feature) {
            final isSelected = _features.contains(feature);
            return FilterChip(
              label: Text(feature),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _features.add(feature);
                  } else {
                    _features.remove(feature);
                  }
                });
              },
              selectedColor: Theme.of(context).primaryColor.withOpacity(0.3),
              checkmarkColor: Theme.of(context).primaryColor);
          }).toList()),
      ]);
  }

  /// تقدير السعر
  Future<void> _estimatePrice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _estimation = null;
    });

    try {
      final estimationService =
          Provider.of<PriceEstimationService>(context, listen: false);
      final userId =
          'user123'; // في التطبيق الحقيقي، يجب استخدام معرف المستخدم الحالي

      final estimation = await estimationService.estimatePrice(
        userId: userId,
        type: _estimationType,
        area: _area,
        propertyType: _propertyType,
        size: _size,
        rooms: _rooms,
        bathrooms: _bathrooms,
        age: _age,
        floor: _floor,
        isFurnished: _isFurnished,
        isRenovated: _isRenovated,
        features: _features.isNotEmpty ? _features : null);

      setState(() {
        _estimation = estimation;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تقدير السعر: $e';
        _isLoading = false;
      });
    }
  }

  /// بناء نتيجة التقدير
  Widget _buildEstimationResult() {
    final estimation = _estimation!;
    final formatter = NumberFormat('#,###', 'ar');
    final estimatedValue = formatter.format(estimation.estimatedValue.round());
    final minValue = formatter.format(estimation.minValue.round());
    final maxValue = formatter.format(estimation.maxValue.round());

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        estimation.type == PriceEstimationType.sale
                            ? 'تقدير سعر البيع'
                            : 'تقدير سعر الإيجار',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold)),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6),
                        decoration: BoxDecoration(
                          color: _getAccuracyColor(estimation.accuracy),
                          borderRadius: BorderRadius.circular(16)),
                        child: Text(
                          _getAccuracyText(estimation.accuracy),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold))),
                    ]),
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 16),
                  Center(
                    child: Column(
                      children: [
                        const Text(
                          'القيمة المقدرة',
                          style: TextStyle(
                            fontSize: 16)),
                        const SizedBox(height: 8),
                        Text(
                          '$estimatedValue ريال',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.green)),
                        const SizedBox(height: 8),
                        Text(
                          'بين $minValue و $maxValue ريال',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600])),
                      ])),
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 16),
                  const Text(
                    'تفاصيل العقار',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  _buildPropertyDetail('المنطقة', estimation.area),
                  _buildPropertyDetail('نوع العقار', estimation.propertyType),
                  _buildPropertyDetail(
                      'المساحة', '${estimation.size} متر مربع'),
                  if (estimation.rooms != null)
                    _buildPropertyDetail('عدد الغرف', '${estimation.rooms}'),
                  if (estimation.bathrooms != null)
                    _buildPropertyDetail(
                        'عدد الحمامات', '${estimation.bathrooms}'),
                  if (estimation.age != null)
                    _buildPropertyDetail('عمر العقار', '${estimation.age} سنة'),
                  if (estimation.floor != null)
                    _buildPropertyDetail('الطابق', '${estimation.floor}'),
                  if (estimation.isFurnished != null)
                    _buildPropertyDetail(
                        'مفروش', estimation.isFurnished! ? 'نعم' : 'لا'),
                  if (estimation.isRenovated != null)
                    _buildPropertyDetail(
                        'مجدد', estimation.isRenovated! ? 'نعم' : 'لا'),
                  if (estimation.features != null &&
                      estimation.features!.isNotEmpty)
                    _buildPropertyDetail(
                        'المميزات', estimation.features!.join('، ')),
                ]))),
          const SizedBox(height: 24),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'العوامل المؤثرة في التقدير',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 16),
                  ...estimation.factors.entries.map((entry) => _buildFactorItem(
                        entry.key,
                        entry.value)),
                ]))),
          const SizedBox(height: 24),
          if (estimation.similarProperties != null &&
              estimation.similarProperties!.isNotEmpty)
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12)),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'عقارات مشابهة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 16),
                    ...estimation.similarProperties!
                        .map((property) => _buildSimilarPropertyItem(
                              property['title'] as String,
                              property['price'] as double,
                              property['area'] as String,
                              property['size'] as double,
                              property['image'] as String?)),
                  ]))),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  _estimation = null;
                });
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16)),
              child: const Text(
                'تقدير جديد',
                style: TextStyle(fontSize: 16)))),
        ]));
  }

  /// بناء تفاصيل العقار
  Widget _buildPropertyDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600])),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold)),
        ]));
  }

  /// بناء عنصر العامل المؤثر
  Widget _buildFactorItem(String factor, double value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                factor,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold)),
              Text(
                '${value.toStringAsFixed(1)}%',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: value / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              value > 0 ? Colors.green : Colors.red)),
        ]));
  }

  /// بناء عنصر العقار المشابه
  Widget _buildSimilarPropertyItem(
    String title,
    double price,
    String area,
    double size,
    String? image) {
    final formatter = NumberFormat('#,###', 'ar');
    final formattedPrice = formatter.format(price.round());

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: image != null
                  ? DecorationImage(
                      image: NetworkImage(image),
                      fit: BoxFit.cover)
                  : null,
              color: Colors.grey[300]),
            child: image == null
                ? const Icon(
                    Icons.home,
                    size: 40,
                    color: Colors.grey)
                : null),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
                const SizedBox(height: 4),
                Text(
                  '$area - $size متر مربع',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600])),
                const SizedBox(height: 4),
                Text(
                  '$formattedPrice ريال',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.green)),
              ])),
        ]));
  }

  /// الحصول على نص دقة التقدير
  String _getAccuracyText(EstimationAccuracy accuracy) {
    switch (accuracy) {
      case EstimationAccuracy.low:
        return 'دقة منخفضة';
      case EstimationAccuracy.medium:
        return 'دقة متوسطة';
      case EstimationAccuracy.high:
        return 'دقة عالية';
      case EstimationAccuracy.veryHigh:
        return 'دقة عالية جداً';
      default:
        return '';
    }
  }

  /// الحصول على لون دقة التقدير
  Color _getAccuracyColor(EstimationAccuracy accuracy) {
    switch (accuracy) {
      case EstimationAccuracy.low:
        return Colors.red;
      case EstimationAccuracy.medium:
        return Colors.orange;
      case EstimationAccuracy.high:
        return Colors.green;
      case EstimationAccuracy.veryHigh:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}

/// مكون خطأ مخصص
class ErrorWidgetCustom extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;

  const ErrorWidgetCustom({
    super.key,
    required this.message,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 60),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('إعادة المحاولة')),
          ])));
  }
}
