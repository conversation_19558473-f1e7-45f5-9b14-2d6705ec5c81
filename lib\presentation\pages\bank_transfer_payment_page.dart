// lib/presentation/pages/bank_transfer_payment_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../../core/theme/app_colors.dart';
import '../../domain/entities/estate.dart';
import '../bloc/improved_ad_bloc.dart';
import 'improved_ad_creation_success_page.dart';

/// صفحة الدفع عن طريق التحويل البنكي
class BankTransferPaymentPage extends StatefulWidget {
  /// العقار المراد الدفع له
  final Estate estate;

  /// المبلغ الإجمالي للدفع
  final double totalAmount;

  /// الميزات المختارة
  final List<String> selectedFeatures;

  /// ما إذا كان تحديثًا لإعلان موجود
  final bool isUpdate;

  const BankTransferPaymentPage({
    super.key,
    required this.estate,
    required this.selectedFeatures,
    required this.totalAmount,
    this.isUpdate = false,
  });

  @override
  State<BankTransferPaymentPage> createState() => _BankTransferPaymentPageState();
}

class _BankTransferPaymentPageState extends State<BankTransferPaymentPage> {
  bool _isProcessing = false;
  bool _isCompleted = false;
  String _estateId = '';

  // معلومات خدمة ومض
  final String _phoneNumber = "+965 9929 8821";

  // نموذج التحويل البنكي
  final _formKey = GlobalKey<FormState>();
  final _transferDateController = TextEditingController();
  final _transferAmountController = TextEditingController();
  final _transferReferenceController = TextEditingController();
  final _senderNameController = TextEditingController();
  final _senderBankController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // تعيين المبلغ تلقائيًا
    _transferAmountController.text = widget.totalAmount.toStringAsFixed(3);
    // تعيين تاريخ اليوم تلقائيًا
    _transferDateController.text = DateFormat('yyyy-MM-dd').format(DateTime.now());
  }

  @override
  void dispose() {
    _transferDateController.dispose();
    _transferAmountController.dispose();
    _transferReferenceController.dispose();
    _senderNameController.dispose();
    _senderBankController.dispose();
    super.dispose();
  }

  /// انتظار إنشاء الإعلان ومراقبة حالة الـ BLoC
  void _waitForAdCreation() {
    print('⏳ انتظار إنشاء الإعلان...');

    // الاستماع لتغييرات حالة الـ BLoC
    final subscription = context.read<ImprovedAdBloc>().stream.listen((state) {
      print('📊 حالة الـ BLoC: isSubmitting=${state.isSubmitting}, isSuccess=${state.isSuccess}, error=${state.errorMessage}');

      if (state.isSuccess && mounted) {
        print('✅ تم إنشاء الإعلان بنجاح!');

        // الحصول على معرف الإعلان من الحالة
        _estateId = state.createdEstateId ?? "estate_${DateTime.now().millisecondsSinceEpoch}";
        print('🆔 معرف الإعلان المُنشأ: $_estateId');

        // الانتقال إلى صفحة النجاح
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (_) => ImprovedAdCreationSuccessPage(
              adId: _estateId,
              isNewAd: true,
              isPaymentVerified: false)));
      } else if (state.errorMessage != null && mounted) {
        print('❌ خطأ في إنشاء الإعلان: ${state.errorMessage}');

        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              "حدث خطأ أثناء إنشاء الإعلان: ${state.errorMessage}",
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red.shade700,
            duration: const Duration(seconds: 5)));

        // إعادة تعيين الحالة
        setState(() {
          _isProcessing = false;
          _isCompleted = false;
        });
      }
    });

    // إلغاء الاشتراك بعد 30 ثانية كحد أقصى
    Future.delayed(const Duration(seconds: 30), () {
      subscription.cancel();
      if (mounted && _isCompleted && _estateId.isEmpty) {
        print('⏰ انتهت مهلة انتظار إنشاء الإعلان');

        // في حالة انتهاء المهلة، استخدم معرف مؤقت
        _estateId = "timeout_${DateTime.now().millisecondsSinceEpoch}";

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (_) => ImprovedAdCreationSuccessPage(
              adId: _estateId,
              isNewAd: true,
              isPaymentVerified: false)));
      }
    });
  }

  /// معالجة التحويل البنكي
  Future<void> _processTransfer() async {
    if (_isProcessing) return;

    // التحقق من صحة النموذج
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // محاكاة عملية التحقق من التحويل
      await Future.delayed(const Duration(seconds: 2));

      // في وضع الاختبار، نفترض أن التحويل تم تسجيله بنجاح (لكن لم يتم التحقق منه بعد)
      final result = {
        'status': 'success',
        'transactionId': 'bank_${DateTime.now().millisecondsSinceEpoch}',
        'reference': _transferReferenceController.text,
        'verified': false, // لم يتم التحقق من الدفع بعد
      };

      if (result['status'] == 'success') {
        print('💳 تم تسجيل معلومات التحويل البنكي بنجاح');

        // إرسال حدث إنشاء الإعلان مع تعيين حالة التحقق من الدفع إلى false
        if (widget.isUpdate && mounted) {
          print('🔄 تحديث إعلان موجود...');
          context.read<ImprovedAdBloc>().add(UpdateEstate(widget.estate.id));
          _estateId = widget.estate.id;

          // عرض رسالة نجاح للتحديث
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  "تم تسجيل معلومات التحويل بنجاح وجاري تحديث الإعلان...",
                  style: GoogleFonts.cairo()),
                backgroundColor: Colors.green.shade700,
                duration: const Duration(seconds: 3)));
          }

          // تحديث الحالة
          if (mounted) {
            setState(() {
              _isProcessing = false;
              _isCompleted = true;
            });
          }

          // الانتقال إلى صفحة النجاح بعد 3 ثوانٍ
          Future.delayed(const Duration(seconds: 3), () {
            if (!mounted) return;

            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (_) => ImprovedAdCreationSuccessPage(
                  adId: _estateId,
                  isNewAd: false,
                  isPaymentVerified: false)));
          });
        } else if (mounted) {
          print('🆕 إنشاء إعلان جديد...');

          // إرسال حدث إنشاء الإعلان
          context.read<ImprovedAdBloc>().add(SubmitAd());

          // عرض رسالة نجاح
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                "تم تسجيل معلومات التحويل بنجاح وجاري إنشاء الإعلان...",
                style: GoogleFonts.cairo()),
              backgroundColor: Colors.green.shade700,
              duration: const Duration(seconds: 3)));

          // تحديث الحالة
          setState(() {
            _isProcessing = false;
            _isCompleted = true;
          });

          // مراقبة حالة الـ BLoC لانتظار إنشاء الإعلان
          _waitForAdCreation();
        }
      } else {
        // عرض رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                "حدث خطأ أثناء معالجة التحويل",
                style: GoogleFonts.cairo()),
              backgroundColor: Colors.red.shade700,
              duration: const Duration(seconds: 3)));
        }

        if (mounted) {
          setState(() {
            _isProcessing = false;
          });
        }
      }
    } catch (e) {
      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              "حدث خطأ أثناء معالجة التحويل: $e",
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red.shade700,
            duration: const Duration(seconds: 3)));
      }

      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "الدفع عبر ومض",
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        centerTitle: true,
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 0),
      body: SafeArea(
        child: _isCompleted ? _buildSuccessView() : _buildPaymentView()));
  }

  /// بناء واجهة الدفع
  Widget _buildPaymentView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      physics: const ClampingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // بطاقة تفاصيل الدفع
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.receipt_long, color: AppColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        "تفاصيل الدفع",
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold)),
                    ]),
                  const Divider(height: 24),

                  // تفاصيل الخطة المختارة
                  Text(
                    "الخطة المختارة:",
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(20),
                      borderRadius: BorderRadius.circular(8)),
                    child: Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: AppColors.primary),
                        const SizedBox(width: 8),
                        Text(
                          _getPlanName(),
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary)),
                        const Spacer(),
                        Text(
                          _getPlanPrice(),
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold)),
                      ])),

                  const SizedBox(height: 16),

                  // الميزات المختارة
                  if (widget.selectedFeatures.isNotEmpty) ...[
                    Text(
                      "الميزات الإضافية:",
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    ...widget.selectedFeatures.map((feature) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 20),
                          const SizedBox(width: 8),
                          Text(
                            feature,
                            style: GoogleFonts.cairo(
                              fontSize: 14)),
                          const Spacer(),
                          Text(
                            _getFeaturePrice(feature),
                            style: GoogleFonts.cairo(
                              fontSize: 14)),
                        ]))),
                  ],

                  const Divider(height: 24),

                  // تفاصيل المبلغ الإجمالي
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "المبلغ الإجمالي:",
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold)),
                      Text(
                        "${widget.totalAmount.toStringAsFixed(3)} د.ك",
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary)),
                    ]),
                ]))),

          const SizedBox(height: 24),

          // بطاقة خدمة ومض للدفع الفوري
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16)),
            color: Colors.green.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.flash_on, color: Colors.green.shade700, size: 24),
                      const SizedBox(width: 8),
                      Text(
                        "خدمة ومض للدفع الفوري",
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700)),
                    ]),
                  const SizedBox(height: 16),

                  Text(
                    "للدفع السريع والفوري، يمكنك استخدام خدمة ومض المعتمدة من البنك المركزي الكويتي:",
                    style: GoogleFonts.cairo(
                      fontSize: 14)),
                  const SizedBox(height: 12),

                  // رقم الهاتف لومض
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.green.shade300)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.payment, color: Colors.green.shade700),
                            const SizedBox(width: 8),
                            Text(
                              "رقم الهاتف لومض:",
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold)),
                          ]),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                _phoneNumber,
                                style: GoogleFonts.cairo(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green.shade700))),
                          ]),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            ElevatedButton.icon(
                              onPressed: () {
                                Navigator.pushNamed(
                                  context,
                                  '/wamd-payment',
                                  arguments: {
                                    'estate': widget.estate,
                                    'selectedFeatures': widget.selectedFeatures,
                                    'totalAmount': widget.totalAmount,
                                    'isUpdate': widget.isUpdate,
                                  },
                                );
                              },
                              icon: const Icon(Icons.flash_on, size: 16),
                              label: Text(
                                "الدفع عبر ومض",
                                style: GoogleFonts.cairo(
                                  fontWeight: FontWeight.bold)),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green.shade700,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8))),
                          ]),
                      ])),

                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green.shade300)),
                    child: Row(
                      children: [
                        Icon(Icons.verified, color: Colors.green.shade700, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            "خدمة آمنة ومعتمدة من البنك المركزي الكويتي - دفع فوري",
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.green.shade800,
                              fontWeight: FontWeight.w500)),
                        ),
                      ])),
                ]))),

          const SizedBox(height: 24),

          // نموذج تأكيد التحويل
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "تأكيد التحويل البنكي",
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
                    const SizedBox(height: 16),

                    // تاريخ التحويل
                    TextFormField(
                      controller: _transferDateController,
                      decoration: InputDecoration(
                        labelText: "تاريخ التحويل",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12)),
                        prefixIcon: const Icon(Icons.calendar_today)),
                      readOnly: true,
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now().subtract(const Duration(days: 30)),
                          lastDate: DateTime.now());
                        if (date != null) {
                          _transferDateController.text = DateFormat('yyyy-MM-dd').format(date);
                        }
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "يرجى إدخال تاريخ التحويل";
                        }
                        return null;
                      }),
                    const SizedBox(height: 16),

                    // مبلغ التحويل
                    TextFormField(
                      controller: _transferAmountController,
                      decoration: InputDecoration(
                        labelText: "مبلغ التحويل (د.ك)",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12)),
                        prefixIcon: const Icon(Icons.attach_money)),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "يرجى إدخال مبلغ التحويل";
                        }
                        try {
                          final amount = double.parse(value);
                          if (amount < widget.totalAmount) {
                            return "المبلغ يجب أن يكون ${widget.totalAmount.toStringAsFixed(3)} د.ك على الأقل";
                          }
                        } catch (e) {
                          return "يرجى إدخال مبلغ صحيح";
                        }
                        return null;
                      }),
                    const SizedBox(height: 16),

                    // رقم مرجعي للتحويل
                    TextFormField(
                      controller: _transferReferenceController,
                      decoration: InputDecoration(
                        labelText: "الرقم المرجعي للتحويل",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12)),
                        prefixIcon: const Icon(Icons.numbers)),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "يرجى إدخال الرقم المرجعي للتحويل";
                        }
                        return null;
                      }),
                    const SizedBox(height: 16),

                    // اسم المرسل
                    TextFormField(
                      controller: _senderNameController,
                      decoration: InputDecoration(
                        labelText: "اسم المرسل",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12)),
                        prefixIcon: const Icon(Icons.person)),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "يرجى إدخال اسم المرسل";
                        }
                        return null;
                      }),
                    const SizedBox(height: 16),

                    // البنك المرسل منه
                    TextFormField(
                      controller: _senderBankController,
                      decoration: InputDecoration(
                        labelText: "البنك المرسل منه",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12)),
                        prefixIcon: const Icon(Icons.account_balance)),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "يرجى إدخال اسم البنك المرسل منه";
                        }
                        return null;
                      }),
                  ])))),

          const SizedBox(height: 24),

          // زر تأكيد التحويل
          ElevatedButton(
            onPressed: _isProcessing ? null : _processTransfer,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12)),
              disabledBackgroundColor: Colors.grey),
            child: _isProcessing
                ? const CircularProgressIndicator(color: Colors.white)
                : Text(
                    "تأكيد التحويل البنكي",
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold))),

          const SizedBox(height: 16),

          // ملاحظة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.amber.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.amber.shade300)),
            child: Text(
              "ملاحظة: سيتم التحقق من التحويل البنكي خلال يوم عمل واحد. سيتم تفعيل الإعلان بعد التأكد من التحويل.",
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.amber.shade900),
              textAlign: TextAlign.center)),

          const SizedBox(height: 24),
        ]));
  }



  /// الحصول على اسم الخطة المختارة
  String _getPlanName() {
    // تحديد الخطة بناءً على السعر
    if (widget.estate.planType == 'gold') {
      return "الخطة الذهبية";
    } else if (widget.estate.planType == 'silver') {
      return "الخطة الفضية";
    } else if (widget.estate.planType == 'bronze') {
      return "الخطة البرونزية";
    } else {
      return "الخطة المجانية";
    }
  }

  /// الحصول على سعر الخطة المختارة
  String _getPlanPrice() {
    // تحديد سعر الخطة
    if (widget.estate.planType == 'gold') {
      return "30.000 د.ك";
    } else if (widget.estate.planType == 'silver') {
      return "20.000 د.ك";
    } else if (widget.estate.planType == 'bronze') {
      return "10.000 د.ك";
    } else {
      return "0.000 د.ك";
    }
  }

  /// الحصول على سعر الميزة الإضافية
  String _getFeaturePrice(String feature) {
    // تحديد سعر الميزة
    switch (feature) {
      case "تثبيت الإعلان":
        return "5.000 د.ك";
      case "إعلان متحرك":
        return "3.000 د.ك";
      case "شارة VIP":
        return "7.000 د.ك";
      case "تثبيت في الصفحة الرئيسية":
        return "10.000 د.ك";
      default:
        return "0.000 د.ك";
    }
  }

  /// بناء واجهة النجاح
  Widget _buildSuccessView() {
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 40),
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 80),
              const SizedBox(height: 16),
              Text(
                "تم تسجيل معلومات التحويل بنجاح!",
                style: GoogleFonts.cairo(
                  fontSize: 24,
                  fontWeight: FontWeight.bold),
                textAlign: TextAlign.center),
              const SizedBox(height: 8),
              Text(
                "جاري إنشاء الإعلان...",
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.grey.shade600),
                textAlign: TextAlign.center),
              const SizedBox(height: 24),
              const CircularProgressIndicator(),
              const SizedBox(height: 40),
            ]))));
  }
}
