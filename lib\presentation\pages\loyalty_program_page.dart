import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:kuwait_corners/core/services/loyalty_program_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// صفحة برنامج الولاء
/// تعرض معلومات برنامج الولاء للمستخدم
class LoyaltyProgramPage extends StatefulWidget {
  const LoyaltyProgramPage({super.key});

  @override
  State<LoyaltyProgramPage> createState() => _LoyaltyProgramPageState();
}

class _LoyaltyProgramPageState extends State<LoyaltyProgramPage>
    with SingleTickerProviderStateMixin {
  final LoyaltyProgramService _loyaltyService = LoyaltyProgramService();

  // حالة الصفحة
  bool _isLoading = true;
  String? _errorMessage;
  LoyaltyProgramData? _loyaltyData;
  List<Map<String, dynamic>> _pointsHistory = [];

  // وحدة التحكم في علامات التبويب
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadLoyaltyData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل بيانات برنامج الولاء
  Future<void> _loadLoyaltyData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final loyaltyData = await _loyaltyService.getCurrentUserLoyaltyData();
      final pointsHistory = await _loyaltyService.getPointsHistory();

      setState(() {
        _loyaltyData = loyaltyData;
        _pointsHistory = pointsHistory;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل بيانات برنامج الولاء';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// الحصول على لون المستوى
  Color _getLevelColor(LoyaltyLevel level) {
    switch (level) {
      case LoyaltyLevel.bronze:
        return const Color(0xFFCD7F32); // لون برونزي
      case LoyaltyLevel.silver:
        return const Color(0xFFC0C0C0); // لون فضي
      case LoyaltyLevel.gold:
        return const Color(0xFFFFD700); // لون ذهبي
      case LoyaltyLevel.platinum:
        return const Color(0xFFE5E4E2); // لون بلاتيني
      case LoyaltyLevel.diamond:
        return const Color(0xFFB9F2FF); // لون ماسي
      case LoyaltyLevel.vip:
        return const Color(0xFF9C27B0); // لون VIP
    }
  }

  /// الحصول على اسم المستوى
  String _getLevelName(LoyaltyLevel level) {
    switch (level) {
      case LoyaltyLevel.bronze:
        return 'برونزي';
      case LoyaltyLevel.silver:
        return 'فضي';
      case LoyaltyLevel.gold:
        return 'ذهبي';
      case LoyaltyLevel.platinum:
        return 'بلاتيني';
      case LoyaltyLevel.diamond:
        return 'ماسي';
      case LoyaltyLevel.vip:
        return 'VIP';
    }
  }

  /// الحصول على أيقونة النشاط
  IconData _getActivityIcon(String icon) {
    switch (icon) {
      case 'person_add':
        return Icons.person_add;
      case 'email':
        return Icons.email;
      case 'person':
        return Icons.person;
      case 'post_add':
        return Icons.post_add;
      case 'update':
        return Icons.update;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'star':
        return Icons.star;
      case 'share':
        return Icons.share;
      case 'login':
        return Icons.login;
      case 'discount':
        return Icons.discount;
      case 'ad':
        return Icons.campaign;
      case 'pin':
        return Icons.push_pin;
      case 'vip':
        return Icons.workspace_premium;
      case 'support':
        return Icons.support_agent;
      case 'badge':
        return Icons.badge;
      default:
        return Icons.circle;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('برنامج الولاء'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الملخص'),
            Tab(text: 'المزايا'),
            Tab(text: 'السجل'),
          ])),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, color: Colors.red, size: 48),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red)),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadLoyaltyData,
                        child: const Text('إعادة المحاولة')),
                    ]))
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildSummaryTab(),
                    _buildBenefitsTab(),
                    _buildHistoryTab(),
                  ]));
  }

  /// بناء علامة تبويب الملخص
  Widget _buildSummaryTab() {
    if (_loyaltyData == null) {
      return const Center(child: Text('لا توجد بيانات متاحة'));
    }

    final levelColor = _getLevelColor(_loyaltyData!.level);
    final levelName = _getLevelName(_loyaltyData!.level);
    final nextLevelName = _loyaltyData!.getNextLevelName();
    final pointsToNextLevel = _loyaltyData!.getPointsToNextLevel();
    final progress = _loyaltyData!.getProgressToNextLevel();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة المستوى
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16)),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  colors: [
                    levelColor.withAlpha(179),
                    levelColor,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'برنامج الولاء',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16)),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(51),
                          borderRadius: BorderRadius.circular(16)),
                        child: Text(
                          'المستوى: $levelName',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold))),
                    ]),

                  const SizedBox(height: 24),

                  // عدد النقاط
                  Center(
                    child: Column(
                      children: [
                        const Text(
                          'نقاطك الحالية',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16)),
                        const SizedBox(height: 8),
                        Text(
                          '${_loyaltyData!.points}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 48,
                            fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        Text(
                          'تنتهي صلاحية النقاط في ${DateFormat('yyyy/MM/dd').format(_loyaltyData!.pointsExpiryDate)}',
                          style: TextStyle(
                            color: Colors.white.withAlpha(204),
                            fontSize: 12)),
                      ])),

                  const SizedBox(height: 24),

                  // التقدم نحو المستوى التالي
                  if (_loyaltyData!.level != LoyaltyLevel.platinum) ...[
                    Text(
                      'التقدم نحو المستوى $nextLevelName',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14)),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: progress,
                      backgroundColor: Colors.white.withAlpha(77),
                      valueColor:
                          const AlwaysStoppedAnimation<Color>(Colors.white)),
                    const SizedBox(height: 8),
                    Text(
                      'تحتاج إلى $pointsToNextLevel نقطة إضافية للوصول إلى المستوى $nextLevelName',
                      style: TextStyle(
                        color: Colors.white.withAlpha(204),
                        fontSize: 12)),
                  ] else ...[
                    const Center(
                      child: Text(
                        'تهانينا! لقد وصلت إلى أعلى مستوى',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold))),
                  ],
                ]))),

          const SizedBox(height: 24),

          // قسم الأنشطة
          const Text(
            'اكسب المزيد من النقاط',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold)),

          const SizedBox(height: 16),

          // قائمة الأنشطة
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12)),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _loyaltyService.getPointsActivities().length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final activity = _loyaltyService.getPointsActivities()[index];

                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue.shade50,
                    child: Icon(
                      _getActivityIcon(activity['icon']),
                      color: Colors.blue)),
                  title: Text(activity['title']),
                  trailing: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(16)),
                    child: Text(
                      '+${activity['points']} نقطة',
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.bold))));
              })),

          const SizedBox(height: 24),

          // قسم الإحصائيات
          const Text(
            'إحصائيات النقاط',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold)),

          const SizedBox(height: 16),

          // بطاقات الإحصائيات
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي النقاط المكتسبة',
                  _loyaltyData!.totalPointsEarned.toString(),
                  Icons.arrow_upward,
                  Colors.green)),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'إجمالي النقاط المستخدمة',
                  _loyaltyData!.totalPointsRedeemed.toString(),
                  Icons.arrow_downward,
                  Colors.red)),
            ]),
        ]));
  }

  /// بناء علامة تبويب المزايا
  Widget _buildBenefitsTab() {
    if (_loyaltyData == null) {
      return const Center(child: Text('لا توجد بيانات متاحة'));
    }

    final currentLevelBenefits =
        _loyaltyService.getCurrentLevelBenefits(_loyaltyData!.level);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المزايا الحالية
          Text(
            'مزايا المستوى ${_getLevelName(_loyaltyData!.level)}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold)),

          const SizedBox(height: 16),

          // قائمة المزايا الحالية
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12)),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: currentLevelBenefits.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final benefit = currentLevelBenefits[index];

                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor:
                        _getLevelColor(_loyaltyData!.level).withAlpha(51),
                    child: Icon(
                      _getActivityIcon(benefit['icon']),
                      color: _getLevelColor(_loyaltyData!.level))),
                  title: Text(benefit['title']));
              })),

          const SizedBox(height: 32),

          // عنوان مقارنة المستويات
          const Text(
            'مقارنة المستويات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold)),

          const SizedBox(height: 16),

          // جدول مقارنة المستويات
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // عناوين الجدول
                  Row(
                    children: [
                      const Expanded(
                        flex: 2,
                        child: Text(
                          'الميزة',
                          style: TextStyle(
                            fontWeight: FontWeight.bold))),
                      Expanded(
                        child: Center(
                          child: Text(
                            'برونزي',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _getLevelColor(LoyaltyLevel.bronze))))),
                      Expanded(
                        child: Center(
                          child: Text(
                            'فضي',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _getLevelColor(LoyaltyLevel.silver))))),
                      Expanded(
                        child: Center(
                          child: Text(
                            'ذهبي',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _getLevelColor(LoyaltyLevel.gold))))),
                      Expanded(
                        child: Center(
                          child: Text(
                            'بلاتيني',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _getLevelColor(LoyaltyLevel.platinum))))),
                    ]),

                  const Divider(height: 24),

                  // صف الخصم
                  _buildComparisonRow(
                    'الخصم على الباقات',
                    ['5%', '10%', '15%', '25%']),

                  const SizedBox(height: 12),

                  // صف الإعلانات المجانية
                  _buildComparisonRow(
                    'الإعلانات المجانية',
                    ['3', '5', '10', 'غير محدود']),

                  const SizedBox(height: 12),

                  // صف الإعلانات المثبتة
                  _buildComparisonRow(
                    'الإعلانات المثبتة',
                    ['0', '1', '3', '5']),

                  const SizedBox(height: 12),

                  // صف إعلانات VIP
                  _buildComparisonRow(
                    'إعلانات VIP',
                    ['0', '0', '1', '3']),

                  const SizedBox(height: 12),

                  // صف دعم العملاء
                  _buildComparisonRow(
                    'دعم العملاء',
                    ['عادي', 'أولوية', 'مميز', 'حصري']),

                  const SizedBox(height: 12),

                  // صف الشارة
                  _buildComparisonRow(
                    'شارة مميزة',
                    ['لا', 'لا', 'لا', 'نعم']),
                ]))),
        ]));
  }

  /// بناء علامة تبويب السجل
  Widget _buildHistoryTab() {
    if (_pointsHistory.isEmpty) {
      return const Center(child: Text('لا توجد سجلات متاحة'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _pointsHistory.length,
      itemBuilder: (context, index) {
        final entry = _pointsHistory[index];
        final date = (entry['date'] as Timestamp).toDate();
        final points = entry['points'] as int;
        final type = entry['type'] as String;
        final reason = entry['reason'] as String;
        final balance = entry['balance'] as int;

        final isEarn = type == 'earn';

        return Card(
          elevation: 1,
          margin: const EdgeInsets.only(bottom: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // أيقونة النوع
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isEarn ? Colors.green.shade50 : Colors.red.shade50,
                    shape: BoxShape.circle),
                  child: Icon(
                    isEarn ? Icons.arrow_upward : Icons.arrow_downward,
                    color: isEarn ? Colors.green : Colors.red)),

                const SizedBox(width: 12),

                // التفاصيل
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reason,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      Text(
                        DateFormat('yyyy/MM/dd - HH:mm').format(date),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600)),
                    ])),

                // النقاط
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${isEarn ? '+' : ''}$points نقطة',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isEarn ? Colors.green : Colors.red)),
                    const SizedBox(height: 4),
                    Text(
                      'الرصيد: $balance',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600)),
                  ]),
              ])));
      });
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withAlpha(25),
                    shape: BoxShape.circle),
                  child: Icon(
                    icon,
                    color: color,
                    size: 16)),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis)),
              ]),
            const SizedBox(height: 12),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold)),
          ])));
  }

  /// بناء صف في جدول المقارنة
  Widget _buildComparisonRow(String feature, List<String> values) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(feature)),
        Expanded(
          child: Center(
            child: Text(
              values[0],
              style: TextStyle(
                color: _loyaltyData!.level == LoyaltyLevel.bronze
                    ? _getLevelColor(LoyaltyLevel.bronze)
                    : null,
                fontWeight: _loyaltyData!.level == LoyaltyLevel.bronze
                    ? FontWeight.bold
                    : null)))),
        Expanded(
          child: Center(
            child: Text(
              values[1],
              style: TextStyle(
                color: _loyaltyData!.level == LoyaltyLevel.silver
                    ? _getLevelColor(LoyaltyLevel.silver)
                    : null,
                fontWeight: _loyaltyData!.level == LoyaltyLevel.silver
                    ? FontWeight.bold
                    : null)))),
        Expanded(
          child: Center(
            child: Text(
              values[2],
              style: TextStyle(
                color: _loyaltyData!.level == LoyaltyLevel.gold
                    ? _getLevelColor(LoyaltyLevel.gold)
                    : null,
                fontWeight: _loyaltyData!.level == LoyaltyLevel.gold
                    ? FontWeight.bold
                    : null)))),
        Expanded(
          child: Center(
            child: Text(
              values[3],
              style: TextStyle(
                color: _loyaltyData!.level == LoyaltyLevel.platinum
                    ? _getLevelColor(LoyaltyLevel.platinum)
                    : null,
                fontWeight: _loyaltyData!.level == LoyaltyLevel.platinum
                    ? FontWeight.bold
                    : null)))),
      ]);
  }
}
