import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_blurhash/flutter_blurhash.dart';
import 'package:shimmer/shimmer.dart';

import '../../infrastructure/services/app_manager.dart';
import '../../infrastructure/services/image_service.dart';
import '../../infrastructure/services/performance_service.dart';

/// صورة محسنة
class OptimizedImage extends StatelessWidget {
  /// رابط الصورة
  final String imageUrl;

  /// عرض الصورة
  final double? width;

  /// ارتفاع الصورة
  final double? height;

  /// طريقة ملء الصورة
  final BoxFit fit;

  /// حجم الصورة
  final String size;

  /// مكون العرض أثناء التحميل
  final Widget? placeholder;

  /// مكون العرض في حالة الخطأ
  final Widget? errorWidget;

  /// قيمة BlurHash للصورة
  final String? blurHash;

  /// ما إذا كان يجب استخدام التحميل التدريجي
  final bool useProgressiveLoading;

  /// ما إذا كان يجب استخدام التخزين المؤقت
  final bool useCache;

  /// مدة التخزين المؤقت
  final Duration? cacheDuration;

  const OptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.size = 'medium',
    this.placeholder,
    this.errorWidget,
    this.blurHash,
    this.useProgressiveLoading = true,
    this.useCache = true,
    this.cacheDuration,
  });

  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تحديد حجم الصورة المناسب بناءً على إعدادات الأداء
    final appropriateSize = performanceService.getAppropriateImageSize();
    final imageSize = size == 'auto' ? appropriateSize : size;

    // تحديد ما إذا كان يجب استخدام التحميل التدريجي
    final shouldUseProgressiveLoading =
        useProgressiveLoading && performanceService.areAnimationsEnabled();

    // تحديد مكون العرض أثناء التحميل
    final loadingPlaceholder = placeholder ?? _buildLoadingPlaceholder();

    // تحديد مكون العرض في حالة الخطأ
    final errorPlaceholderWidget = errorWidget ?? _buildErrorWidget();

    // إذا كان BlurHash متاحاً وتم تفعيل التحميل التدريجي
    if (blurHash != null && shouldUseProgressiveLoading) {
      return _buildProgressiveImage(
        context,
        imageUrl,
        imageSize,
        loadingPlaceholder,
        errorPlaceholderWidget);
    }

    // إذا تم تفعيل التخزين المؤقت
    if (useCache) {
      return _buildCachedImage(
        context,
        imageUrl,
        imageSize,
        loadingPlaceholder,
        errorPlaceholderWidget);
    }

    // استخدام التحميل العادي
    return _buildNetworkImage(
      context,
      imageUrl,
      imageSize,
      loadingPlaceholder,
      errorPlaceholderWidget);
  }

  /// بناء مكون التحميل التدريجي
  Widget _buildProgressiveImage(
    BuildContext context,
    String imageUrl,
    String imageSize,
    Widget loadingPlaceholder,
    Widget errorPlaceholderWidget) {
    // الحصول على خدمة الصور
    final imageService = context.imageService;

    // الحصول على رابط الصورة المصغرة
    final thumbnailUrl = imageService.getImageVariantUrl(imageUrl, 'thumbnail');

    // الحصول على رابط الصورة بالحجم المناسب
    final fullImageUrl = imageService.getImageVariantUrl(imageUrl, imageSize);

    return CachedNetworkImage(
      imageUrl: fullImageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) {
        // إذا كان BlurHash متاحاً، نستخدمه كخلفية أثناء التحميل
        if (blurHash != null) {
          return Stack(
            fit: StackFit.expand,
            children: [
              BlurHash(hash: blurHash!),
              CachedNetworkImage(
                imageUrl: thumbnailUrl,
                width: width,
                height: height,
                fit: fit,
                placeholder: (context, url) => loadingPlaceholder,
                errorWidget: (context, url, error) => loadingPlaceholder),
            ]);
        }

        return loadingPlaceholder;
      },
      errorWidget: (context, url, error) => errorPlaceholderWidget,
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 300),
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
      maxWidthDiskCache: width?.toInt(),
      maxHeightDiskCache: height?.toInt(),
      cacheKey: '$imageUrl-$imageSize',
      cacheManager: context.cacheManager.imageCacheManager);
  }

  /// بناء مكون الصورة المخزنة مؤقتاً
  Widget _buildCachedImage(
    BuildContext context,
    String imageUrl,
    String imageSize,
    Widget loadingPlaceholder,
    Widget errorPlaceholderWidget) {
    // الحصول على خدمة الصور
    final imageService = context.imageService;

    // الحصول على رابط الصورة بالحجم المناسب
    final optimizedUrl = imageService.getImageVariantUrl(imageUrl, imageSize);

    return CachedNetworkImage(
      imageUrl: optimizedUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) => loadingPlaceholder,
      errorWidget: (context, url, error) => errorPlaceholderWidget,
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 300),
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
      maxWidthDiskCache: width?.toInt(),
      maxHeightDiskCache: height?.toInt(),
      cacheKey: '$imageUrl-$imageSize',
      cacheManager: context.cacheManager.imageCacheManager);
  }

  /// بناء مكون الصورة من الإنترنت
  Widget _buildNetworkImage(
    BuildContext context,
    String imageUrl,
    String imageSize,
    Widget loadingPlaceholder,
    Widget errorPlaceholderWidget) {
    // الحصول على خدمة الصور
    final imageService = context.imageService;

    // الحصول على رابط الصورة بالحجم المناسب
    final optimizedUrl = imageService.getImageVariantUrl(imageUrl, imageSize);

    return Image.network(
      optimizedUrl,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          return child;
        }
        return loadingPlaceholder;
      },
      errorBuilder: (context, error, stackTrace) => errorPlaceholderWidget,
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt());
  }

  /// بناء مكون العرض أثناء التحميل
  Widget _buildLoadingPlaceholder() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        color: Colors.white));
  }

  /// بناء مكون العرض في حالة الخطأ
  Widget _buildErrorWidget() {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: const Center(
        child: Icon(
          Icons.error_outline,
          color: Colors.grey)));
  }
}

/// صورة محسنة للقوائم
class OptimizedListImage extends StatelessWidget {
  /// رابط الصورة
  final String imageUrl;

  /// عرض الصورة
  final double? width;

  /// ارتفاع الصورة
  final double? height;

  /// طريقة ملء الصورة
  final BoxFit fit;

  /// قيمة BlurHash للصورة
  final String? blurHash;

  const OptimizedListImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.blurHash,
  });

  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تحديد حجم الصورة المناسب للقوائم
    final imageSize = performanceService.getAppropriateListImageQuality();

    return OptimizedImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      size: imageSize,
      blurHash: blurHash,
      useProgressiveLoading:
          false, // تعطيل التحميل التدريجي في القوائم لتحسين الأداء
    );
  }
}

/// صورة محسنة لصفحة التفاصيل
class OptimizedDetailImage extends StatelessWidget {
  /// رابط الصورة
  final String imageUrl;

  /// عرض الصورة
  final double? width;

  /// ارتفاع الصورة
  final double? height;

  /// طريقة ملء الصورة
  final BoxFit fit;

  /// قيمة BlurHash للصورة
  final String? blurHash;

  const OptimizedDetailImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.blurHash,
  });

  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تحديد حجم الصورة المناسب لصفحة التفاصيل
    final imageSize = performanceService.getAppropriateDetailImageQuality();

    return OptimizedImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      size: imageSize,
      blurHash: blurHash,
      useProgressiveLoading: true, // تفعيل التحميل التدريجي في صفحة التفاصيل
    );
  }
}

/// عارض الصور 360 درجة
class Panorama360Viewer extends StatelessWidget {
  /// رابط الصورة 360 درجة
  final String panoramaUrl;

  /// عرض العارض
  final double? width;

  /// ارتفاع العارض
  final double? height;

  /// ما إذا كان العارض تفاعلي
  final bool interactive;

  const Panorama360Viewer({
    super.key,
    required this.panoramaUrl,
    this.width,
    this.height,
    this.interactive = true,
  });

  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // التحقق مما إذا كان يجب تحميل الصور 360 درجة
    final shouldLoad360 = performanceService.shouldLoad360Tours();

    if (!shouldLoad360) {
      return Container(
        width: width,
        height: height,
        color: Colors.grey[200],
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.panorama,
                color: Colors.grey,
                size: 48),
              SizedBox(height: 16),
              Text(
                'الجولة الافتراضية 360 درجة غير متاحة في وضع توفير البيانات',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14)),
            ])));
    }

    // الحصول على خدمة الصور
    final imageService = context.imageService;

    return imageService.panoramaView(
      panoramaUrl,
      width: width,
      height: height,
      interactive: interactive);
  }
}

/// عارض الجولة الافتراضية 360 درجة
class VirtualTour360Viewer extends StatelessWidget {
  /// قائمة روابط الصور 360 درجة
  final List<String> panoramaUrls;

  /// عرض العارض
  final double? width;

  /// ارتفاع العارض
  final double? height;

  /// ما إذا كان العارض تفاعلي
  final bool interactive;

  const VirtualTour360Viewer({
    super.key,
    required this.panoramaUrls,
    this.width,
    this.height,
    this.interactive = true,
  });

  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // التحقق مما إذا كان يجب تحميل الصور 360 درجة
    final shouldLoad360 = performanceService.shouldLoad360Tours();

    if (!shouldLoad360) {
      return Container(
        width: width,
        height: height,
        color: Colors.grey[200],
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.view_in_ar,
                color: Colors.grey,
                size: 48),
              SizedBox(height: 16),
              Text(
                'الجولة الافتراضية 360 درجة غير متاحة في وضع توفير البيانات',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14)),
            ])));
    }

    // الحصول على خدمة الصور
    final imageService = context.imageService;

    return imageService.virtualTourView(
      panoramaUrls,
      width: width,
      height: height,
      interactive: interactive);
  }
}
