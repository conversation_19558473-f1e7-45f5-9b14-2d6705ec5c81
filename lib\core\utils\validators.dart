/// A utility class that provides various input validation methods.
class Validators {
  /// Checks if the provided [email] is in a valid email format.
  ///
  /// This method uses a simple regular expression to verify that the email
  /// contains characters before and after the '@' symbol and at least one '.'
  /// in the domain part.
  static bool isValidEmail(String email) {
    final regex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    return regex.hasMatch(email);
  }

  /// Checks if the provided [price] is valid.
  ///
  /// A valid price must be greater than 0.
  static bool isValidPrice(double price) {
    return price > 0;
  }
}
