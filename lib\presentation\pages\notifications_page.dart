import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/services/notification_service.dart';

/// Notifications Page
class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage>
    with SingleTickerProviderStateMixin {
  // Notification service
  final NotificationService _notificationService = NotificationService();

  // Page state
  bool _isLoading = false;
  List<NotificationModel> _allNotifications = [];
  List<NotificationModel> _unreadNotifications = [];
  List<NotificationModel> _systemNotifications = [];

  // Tab controller
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Load notifications
  Future<void> _loadNotifications() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Load all notifications
      final allNotifications = await _notificationService.getNotifications();

      // Filter unread notifications
      final unreadNotifications = allNotifications
          .where((notification) => !notification.isRead)
          .toList();

      // Filter system notifications
      final systemNotifications = allNotifications
          .where((notification) =>
              notification.type == NotificationType.system)
          .toList();

      if (!mounted) return;

      setState(() {
        _allNotifications = allNotifications;
        _unreadNotifications = unreadNotifications;
        _systemNotifications = systemNotifications;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      _showErrorSnackBar('خطأ في تحميل الإشعارات');
    }
  }

  /// Mark notification as read
  Future<void> _markAsRead(String notificationId) async {
    try {
      await _notificationService.markNotificationAsRead(notificationId);

      if (!mounted) return;

      setState(() {
        // Update notification state in local lists
        for (final notification in _allNotifications) {
          if (notification.id == notificationId) {
            notification.isRead = true;
          }
        }

        // Update unread notifications list
        _unreadNotifications = _unreadNotifications
            .where((notification) => notification.id != notificationId)
            .toList();
      });
    } catch (e) {
      // Ignore error
    }
  }

  /// Mark all notifications as read
  Future<void> _markAllAsRead() async {
    try {
      await _notificationService.markAllNotificationsAsRead();

      if (!mounted) return;

      setState(() {
        // Update all notifications state
        for (final notification in _allNotifications) {
          notification.isRead = true;
        }

        // Clear unread notifications list
        _unreadNotifications = [];
      });

      _showSuccessSnackBar('All notifications marked as read');
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Error marking notifications as read');
      }
    }
  }

  /// Delete notification
  Future<void> _deleteNotification(String notificationId) async {
    try {
      await _notificationService.deleteNotification(notificationId);

      if (!mounted) return;

      setState(() {
        // Remove notification from local lists
        _allNotifications.removeWhere((n) => n.id == notificationId);
        _unreadNotifications.removeWhere((n) => n.id == notificationId);
        _systemNotifications.removeWhere((n) => n.id == notificationId);
      });
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Error deleting notification');
      }
    }
  }

  /// Delete all notifications
  Future<void> _deleteAllNotifications() async {
    try {
      await _notificationService.deleteAllNotifications();

      if (!mounted) return;

      setState(() {
        _allNotifications = [];
        _unreadNotifications = [];
        _systemNotifications = [];
      });

      _showSuccessSnackBar('All notifications deleted');
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Error deleting notifications');
      }
    }
  }

  /// Show delete all confirmation dialog
  void _showDeleteAllConfirmationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Notifications'),
        content: const Text(
          'Are you sure you want to delete all notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel')),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAllNotifications();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red),
            child: const Text('Delete')),
        ]));
  }

  /// Show success snackbar
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)));
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        actions: [
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNotifications,
            tooltip: 'Refresh'),

          // Options menu
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'mark_all_read') {
                _markAllAsRead();
              } else if (value == 'delete_all') {
                _showDeleteAllConfirmationDialog();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem<String>(
                value: 'mark_all_read',
                child: Row(
                  children: [
                    Icon(Icons.done_all),
                    SizedBox(width: 8),
                    Text('تعليم الكل كمقروء'),
                  ])),
              const PopupMenuItem<String>(
                value: 'delete_all',
                child: Row(
                  children: [
                    Icon(Icons.delete_sweep),
                    SizedBox(width: 8),
                    Text('حذف الكل'),
                  ])),
            ]),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الكل'),
            Tab(text: 'غير مقروءة'),
            Tab(text: 'النظام'),
          ])),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                // All notifications
                _buildNotificationsList(_allNotifications),

                // Unread notifications
                _buildNotificationsList(_unreadNotifications),

                // System notifications
                _buildNotificationsList(_systemNotifications),
              ]));
  }

  /// Build notifications list
  Widget _buildNotificationsList(List<NotificationModel> notifications) {
    if (notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_off,
              size: 64,
              color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد إشعارات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600)),
          ]));
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.separated(
        padding: const EdgeInsets.all(8),
        itemCount: notifications.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final notification = notifications[index];

          return Dismissible(
            key: Key(notification.id),
            direction: DismissDirection.endToStart,
            background: Container(
              alignment: Alignment.centerRight,
              padding: const EdgeInsets.only(right: 16),
              color: Colors.red,
              child: const Icon(
                Icons.delete,
                color: Colors.white)),
            onDismissed: (direction) {
              _deleteNotification(notification.id);
            },
            child: _buildNotificationItem(notification));
        }));
  }

  /// Build notification item
  Widget _buildNotificationItem(NotificationModel notification) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: notification.getNotificationColor().withOpacity(0.2),
        child: Icon(
          notification.getNotificationIcon(),
          color: notification.getNotificationColor(),
          size: 20)),
      title: Text(
        notification.title,
        style: TextStyle(
          fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold)),
      subtitle: Text(notification.body),
      trailing: !notification.isRead
          ? IconButton(
              icon: const Icon(Icons.done),
              onPressed: () => _markAsRead(notification.id),
              tooltip: 'تعليم كمقروء')
          : Text(
              _formatTimestamp(notification.timestamp),
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey)),
      onTap: () {
        if (!notification.isRead) {
          _markAsRead(notification.id);
        }
      });
  }

  /// Format timestamp
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
