import 'package:flutter/material.dart';

/// عرض الخطأ
class ErrorView extends StatelessWidget {
  /// رسالة الخطأ
  final String message;
  
  /// دالة يتم استدعاؤها عند الضغط على زر إعادة المحاولة
  final VoidCallback onRetry;
  
  /// حجم أيقونة الخطأ
  final double iconSize;
  
  /// لون أيقونة الخطأ
  final Color iconColor;

  const ErrorView({
    super.key,
    required this.message,
    required this.onRetry,
    this.iconSize = 60,
    this.iconColor = Colors.red,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: iconColor,
              size: iconSize),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12))),
          ])));
  }
}
