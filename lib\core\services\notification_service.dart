import 'dart:convert';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../presentation/widgets/in_app_notification_widget.dart';
import '../../domain/models/notification_model.dart' as domain;

/// أنواع الإشعارات
enum NotificationType {
  /// إشعار عام
  general,

  /// إشعار عقار جديد
  newEstate,

  /// إشعار تحديث عقار
  estateUpdate,

  /// إشعار رسالة جديدة
  newMessage,

  /// إشعار تحقق من الحساب
  accountVerification,

  /// إشعار عرض خاص
  specialOffer,

  /// إشعار تذكير
  reminder,

  /// إشعار نظام
  system
}

/// نموذج بيانات الإشعار
class NotificationModel {
  /// معرف الإشعار
  final String id;

  /// عنوان الإشعار
  final String title;

  /// محتوى الإشعار
  final String body;

  /// نوع الإشعار
  final NotificationType type;

  /// بيانات إضافية
  final Map<String, dynamic> data;

  /// تاريخ الإشعار
  final DateTime timestamp;

  /// ما إذا كان الإشعار مقروء
  bool isRead;

  /// معرف المستخدم
  final String userId;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    required this.timestamp,
    required this.isRead,
    required this.userId,
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return NotificationModel(
      id: doc.id,
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      type: _getNotificationTypeFromString(data['type'] ?? 'general'),
      data: Map<String, dynamic>.from(data['data'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      isRead: data['isRead'] ?? false,
      userId: data['userId'] ?? '');
  }

  /// تحويل النموذج إلى Map لتخزينه في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'body': body,
      'type': type.toString().split('.').last,
      'data': data,
      'timestamp': Timestamp.fromDate(timestamp),
      'isRead': isRead,
      'userId': userId,
    };
  }

  /// الحصول على نوع الإشعار من النص
  static NotificationType _getNotificationTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'newEstate':
        return NotificationType.newEstate;
      case 'estateUpdate':
        return NotificationType.estateUpdate;
      case 'newMessage':
        return NotificationType.newMessage;
      case 'accountVerification':
        return NotificationType.accountVerification;
      case 'specialOffer':
        return NotificationType.specialOffer;
      case 'reminder':
        return NotificationType.reminder;
      case 'system':
        return NotificationType.system;
      default:
        return NotificationType.general;
    }
  }

  /// الحصول على أيقونة الإشعار
  IconData getNotificationIcon() {
    switch (type) {
      case NotificationType.newEstate:
        return Icons.home;
      case NotificationType.estateUpdate:
        return Icons.update;
      case NotificationType.newMessage:
        return Icons.message;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      case NotificationType.specialOffer:
        return Icons.local_offer;
      case NotificationType.reminder:
        return Icons.alarm;
      case NotificationType.system:
        return Icons.system_update;
      default:
        return Icons.notifications;
    }
  }

  /// الحصول على لون الإشعار
  Color getNotificationColor() {
    switch (type) {
      case NotificationType.newEstate:
        return Colors.green;
      case NotificationType.estateUpdate:
        return Colors.blue;
      case NotificationType.newMessage:
        return Colors.orange;
      case NotificationType.accountVerification:
        return Colors.purple;
      case NotificationType.specialOffer:
        return Colors.red;
      case NotificationType.reminder:
        return Colors.amber;
      case NotificationType.system:
        return Colors.grey;
      default:
        return Colors.teal;
    }
  }
}

/// خدمة الإشعارات
class NotificationService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();

  // مفتاح تخزين إعدادات الإشعارات
  static const String _notificationSettingsKey = 'notification_settings';

  // قناة الإشعارات الافتراضية
  static const String _defaultChannelId = 'krea_notifications';
  static const String _defaultChannelName = 'إشعارات كريا';
  static const String _defaultChannelDescription = 'إشعارات تطبيق كريا العقاري';

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    // تهيئة الإشعارات المحلية
    await _initializeLocalNotifications();

    // طلب إذن الإشعارات
    await _requestPermission();

    // تسجيل معالجات الإشعارات
    _registerNotificationHandlers();

    // تحديث رمز الجهاز في Firestore
    await _updateDeviceToken();
  }

  /// تهيئة الإشعارات المحلية
  Future<void> _initializeLocalNotifications() async {
    try {
      // إعدادات Android
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

      // إعدادات iOS
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
        requestCriticalPermission: false,
        requestProvisionalPermission: false,
      );

      // إعدادات التهيئة
      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      // تهيئة المكتبة
      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // إنشاء قناة الإشعارات لـ Android
      if (Platform.isAndroid) {
        await _createNotificationChannel();
      }
    } catch (e) {
      debugPrint('فشل في تهيئة الإشعارات المحلية: $e');
    }
  }

  /// إنشاء قناة الإشعارات لـ Android
  Future<void> _createNotificationChannel() async {
    const channel = AndroidNotificationChannel(
      _defaultChannelId,
      _defaultChannelName,
      description: _defaultChannelDescription,
      importance: Importance.high,
      playSound: true,
      enableVibration: true,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  /// معالجة النقر على الإشعار المحلي
  void _onNotificationTapped(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null) {
      _handleNotificationTap(payload);
    }
  }

  /// عرض إشعار محلي
  Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      // إعدادات Android
      const androidDetails = AndroidNotificationDetails(
        _defaultChannelId,
        _defaultChannelName,
        channelDescription: _defaultChannelDescription,
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
      );

      // إعدادات iOS
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      // إعدادات الإشعار
      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // عرض الإشعار
      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        notificationDetails,
        payload: payload,
      );
    } catch (e) {
      debugPrint('فشل في عرض الإشعار المحلي: $e');
    }
  }

  /// طلب إذن الإشعارات
  Future<void> _requestPermission() async {
    // طلب إذن الإشعارات المحلية
    if (Platform.isAndroid) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
    }

    // طلب إذن Firebase Messaging
    await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      announcement: false,
      carPlay: false,
      criticalAlert: false,
      provisional: false);
  }

  /// تسجيل معالجات الإشعارات
  void _registerNotificationHandlers() {
    // معالجة الإشعارات عندما يكون التطبيق في الخلفية
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _handleNotificationTap(jsonEncode(message.data));
    });

    // معالجة الإشعارات عندما يكون التطبيق في المقدمة
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _handleForegroundMessage(message);
    });
  }

  /// معالجة النقر على الإشعار
  void _handleNotificationTap(String? payload) {
    if (payload == null) {
      return;
    }

    try {
      final data = jsonDecode(payload) as Map<String, dynamic>;

      // تحديد نوع الإشعار
      final typeStr = data['type'] as String?;
      if (typeStr == null) {
        return;
      }

      // تعليم الإشعار كمقروء
      final notificationId = data['notificationId'] as String?;
      if (notificationId != null) {
        _markNotificationAsRead(notificationId);
      }

      // التنقل إلى الصفحة المناسبة
      // يتم تنفيذ هذا في الصفحة التي تستخدم الخدمة
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// معالجة الإشعار عندما يكون التطبيق في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      // حفظ الإشعار في Firestore
      await _saveNotificationToFirestore(
        title: notification.title ?? 'إشعار جديد',
        body: notification.body ?? '',
        data: data,
        type: _getNotificationTypeFromData(data));

      // عرض الإشعار المحلي
      await _showLocalNotification(
        title: notification.title ?? 'إشعار جديد',
        body: notification.body ?? '',
        payload: jsonEncode(data),
      );
    }
  }

  /// الحصول على نوع الإشعار من البيانات
  NotificationType _getNotificationTypeFromData(Map<String, dynamic> data) {
    final typeStr = data['type'] as String?;
    if (typeStr == null) {
      return NotificationType.general;
    }

    switch (typeStr) {
      case 'newEstate':
        return NotificationType.newEstate;
      case 'estateUpdate':
        return NotificationType.estateUpdate;
      case 'newMessage':
        return NotificationType.newMessage;
      case 'accountVerification':
        return NotificationType.accountVerification;
      case 'specialOffer':
        return NotificationType.specialOffer;
      case 'reminder':
        return NotificationType.reminder;
      case 'system':
        return NotificationType.system;
      default:
        return NotificationType.general;
    }
  }

  /// تحديث رمز الجهاز في Firestore
  Future<void> _updateDeviceToken() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      final token = await _firebaseMessaging.getToken();
      if (token == null) {
        return;
      }

      // تحديث رمز الجهاز في وثيقة المستخدم
      await _firestore.collection('users').doc(user.uid).update({
        'deviceTokens': FieldValue.arrayUnion([token]),
        'lastTokenUpdate': FieldValue.serverTimestamp(),
      });

      // الاستماع لتغييرات رمز الجهاز
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _updateDeviceToken();
      });
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// حفظ الإشعار في Firestore
  Future<void> _saveNotificationToFirestore({
    required String title,
    required String body,
    required Map<String, dynamic> data,
    required NotificationType type,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      final notificationId = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc()
          .id;

      final notification = NotificationModel(
        id: notificationId,
        title: title,
        body: body,
        type: type,
        data: data,
        timestamp: DateTime.now(),
        isRead: false,
        userId: user.uid);

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc(notificationId)
          .set(notification.toFirestore());

      // تحديث عدد الإشعارات غير المقروءة
      await _updateUnreadNotificationsCount();
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// تعليم الإشعار كمقروء
  Future<void> _markNotificationAsRead(String notificationId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc(notificationId)
          .update({
        'isRead': true,
      });
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// تعليم الإشعار كمقروء (عام)
  Future<void> markNotificationAsRead(String notificationId) async {
    await _markNotificationAsRead(notificationId);
  }

  /// تعليم جميع الإشعارات كمقروءة
  Future<void> markAllNotificationsAsRead() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      final batch = _firestore.batch();

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .where('isRead', isEqualTo: false)
          .get();

      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {'isRead': true});
      }

      await batch.commit();

      // تحديث عدد الإشعارات غير المقروءة
      await _updateUnreadNotificationsCount();
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// تحديث عدد الإشعارات غير المقروءة
  Future<void> _updateUnreadNotificationsCount() async {
    try {
      final count = await getUnreadNotificationsCount();

      // تحديث شارة التطبيق
      if (Platform.isIOS) {
        // تحديث شارة التطبيق على iOS
        // (تم تعطيل هذه الميزة مؤقتًا)
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// الحصول على قائمة الإشعارات
  Future<List<NotificationModel>> getNotifications({int limit = 20}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return [];
      }

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => NotificationModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadNotificationsCount() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return 0;
      }

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .where('isRead', isEqualTo: false)
          .count()
          .get();

      return snapshot.count ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc(notificationId)
          .delete();

      // تحديث عدد الإشعارات غير المقروءة
      await _updateUnreadNotificationsCount();
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// إرسال إشعار بقرب انتهاء الاشتراك
  Future<void> sendSubscriptionExpiringNotification(
    String userId,
    DateTime endDate) async {
    try {
      final daysRemaining = endDate.difference(DateTime.now()).inDays;

      await _saveNotificationToFirestore(
        title: 'تنبيه انتهاء الاشتراك',
        body:
            'سينتهي اشتراكك بعد $daysRemaining أيام. يرجى تجديد الاشتراك للاستمرار في الاستفادة من الخدمات.',
        data: {
          'type': 'reminder',
          'action': 'subscription_expiring',
          'endDate': endDate.toIso8601String(),
        },
        type: NotificationType.reminder);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// إرسال إشعار بانخفاض عدد الإعلانات المتبقية
  Future<void> sendLowRemainingAdsNotification(
    String userId,
    int remainingAds) async {
    try {
      await _saveNotificationToFirestore(
        title: 'تنبيه الإعلانات المتبقية',
        body:
            'لديك $remainingAds إعلانات متبقية في اشتراكك الحالي. يرجى ترقية الاشتراك للحصول على المزيد من الإعلانات.',
        data: {
          'type': 'reminder',
          'action': 'low_remaining_ads',
          'remainingAds': remainingAds,
        },
        type: NotificationType.reminder);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// إرسال إشعار بنجاح عملية الشراء
  Future<void> sendPaymentSuccessNotification(
    String userId,
    String paymentId,
    double amount,
    String subscriptionType) async {
    try {
      await _saveNotificationToFirestore(
        title: 'تم الدفع بنجاح',
        body:
            'تم تأكيد عملية الدفع بمبلغ $amount د.ك لاشتراك $subscriptionType بنجاح.',
        data: {
          'type': 'system',
          'action': 'payment_success',
          'paymentId': paymentId,
          'amount': amount,
          'subscriptionType': subscriptionType,
        },
        type: NotificationType.system);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// إرسال إشعار بتجديد الاشتراك
  Future<void> sendSubscriptionRenewedNotification(
    String userId,
    DateTime newEndDate,
    String subscriptionType) async {
    try {
      await _saveNotificationToFirestore(
        title: 'تم تجديد الاشتراك',
        body:
            'تم تجديد اشتراكك من نوع $subscriptionType بنجاح حتى تاريخ ${_formatDate(newEndDate)}.',
        data: {
          'type': 'system',
          'action': 'subscription_renewed',
          'newEndDate': newEndDate.toIso8601String(),
          'subscriptionType': subscriptionType,
        },
        type: NotificationType.system);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// إرسال إشعار بانتهاء الترويج
  Future<void> sendPromotionEndedNotification(
    String userId,
    String adId,
    String adTitle) async {
    try {
      await _saveNotificationToFirestore(
        title: 'انتهاء الترويج',
        body:
            'انتهت فترة ترويج إعلانك "$adTitle". يمكنك تجديد الترويج للحصول على المزيد من المشاهدات.',
        data: {
          'type': 'reminder',
          'action': 'promotion_ended',
          'adId': adId,
          'adTitle': adTitle,
        },
        type: NotificationType.reminder);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  /// حذف جميع الإشعارات
  Future<void> deleteAllNotifications() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      final batch = _firestore.batch();

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .get();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();

      // تحديث عدد الإشعارات غير المقروءة
      await _updateUnreadNotificationsCount();
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// الحصول على إعدادات الإشعارات
  Future<Map<String, bool>> getNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_notificationSettingsKey);

      if (settingsJson == null) {
        // الإعدادات الافتراضية
        return {
          'enablePushNotifications': true,
          'enableEmailNotifications': true,
          'enableNewEstateNotifications': true,
          'enableEstateUpdateNotifications': true,
          'enableMessageNotifications': true,
          'enableAccountNotifications': true,
          'enableSpecialOfferNotifications': true,
          'enableReminderNotifications': true,
          'enableSystemNotifications': true,
        };
      }

      final settings = jsonDecode(settingsJson) as Map<String, dynamic>;

      return settings.map((key, value) => MapEntry(key, value as bool));
    } catch (e) {
      // الإعدادات الافتراضية في حالة الخطأ
      return {
        'enablePushNotifications': true,
        'enableEmailNotifications': true,
        'enableNewEstateNotifications': true,
        'enableEstateUpdateNotifications': true,
        'enableMessageNotifications': true,
        'enableAccountNotifications': true,
        'enableSpecialOfferNotifications': true,
        'enableReminderNotifications': true,
        'enableSystemNotifications': true,
      };
    }
  }

  /// تحديث إعدادات الإشعارات
  Future<void> updateNotificationSettings(Map<String, bool> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_notificationSettingsKey, jsonEncode(settings));

      // تحديث إعدادات الإشعارات في Firebase
      final user = _auth.currentUser;
      if (user != null) {
        await _firestore.collection('users').doc(user.uid).update({
          'notificationSettings': settings,
        });
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// عرض إشعار فوري داخل التطبيق
  static void showInAppNotification(
    BuildContext context, {
    required String title,
    required String body,
    required domain.NotificationType type,
    VoidCallback? onTap,
    Duration duration = const Duration(seconds: 4),
  }) {
    final notification = domain.NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      body: body,
      type: type,
      data: {},
      timestamp: DateTime.now(),
      userId: '',
    );

    InAppNotificationManager.show(
      context,
      notification,
      onTap: onTap,
      duration: duration,
    );
  }

  /// إرسال إشعار محلي فوري
  Future<void> showLocalNotification({
    required String title,
    required String body,
    NotificationType type = NotificationType.general,
    Map<String, dynamic>? data,
  }) async {
    final payload = data != null ? jsonEncode(data) : null;
    await _showLocalNotification(
      title: title,
      body: body,
      payload: payload,
    );
  }

  /// إرسال إشعار إلى مستخدم
  Future<void> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    required NotificationType type,
    required Map<String, dynamic> data,
  }) async {
    try {
      // إضافة نوع الإشعار إلى البيانات
      data['type'] = type.toString().split('.').last;

      // إنشاء معرف للإشعار
      final notificationId = _firestore
          .collection('users')
          .doc(userId)
          .collection('notifications')
          .doc()
          .id;

      // إضافة معرف الإشعار إلى البيانات
      data['notificationId'] = notificationId;

      // حفظ الإشعار في Firestore
      final notification = NotificationModel(
        id: notificationId,
        title: title,
        body: body,
        type: type,
        data: data,
        timestamp: DateTime.now(),
        isRead: false,
        userId: userId);

      await _firestore
          .collection('users')
          .doc(userId)
          .collection('notifications')
          .doc(notificationId)
          .set(notification.toFirestore());

      // إرسال الإشعار عبر Cloud Functions
      // (يتطلب تنفيذ Cloud Function لإرسال الإشعارات)
      await _firestore.collection('notifications').add({
        'userId': userId,
        'title': title,
        'body': body,
        'data': data,
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // تجاهل الخطأ
    }
  }
}
