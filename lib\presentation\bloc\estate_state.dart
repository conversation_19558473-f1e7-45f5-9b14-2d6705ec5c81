import '../../domain/entities/estate.dart';
import '../../domain/entities/estate_base.dart';
import '../../domain/entities/estate_converter.dart';

/// Base state class for estate operations.
abstract class EstateState {}

/// Initial state when no action has been performed yet.
class EstateInitial extends EstateState {}

/// State indicating that estate-related data is currently being loaded.
class EstateLoading extends EstateState {
  final bool isFirstLoad; // إذا كانت true، فهذا هو التحميل الأول
  final bool
      isLoadingMore; // إذا كانت true، فنحن نحمل المزيد من البيانات (صفحة جديدة)
  final bool isFirstFetch; // إذا كانت true، فهذا هو التحميل الأول للبيانات

  EstateLoading({
    this.isFirstLoad = true,
    this.isLoadingMore = false,
    this.isFirstFetch = true,
  });
}

/// State representing that the estates have been successfully loaded.
/// Contains a list of [EstateBase] entities.
class EstateLoaded extends EstateState {
  final List<EstateBase> estateBaseList;

  // للتوافق مع الكود القديم
  List<Estate> get estates {
    final result = <Estate>[];
    for (final estateBase in estateBaseList) {
      final estate = EstateConverter.toLegacyEstate(estateBase);
      if (estate != null) {
        result.add(estate);
      }
    }
    return result;
  }

  EstateLoaded(this.estateBaseList);
}

/// State representing that the estates have been successfully loaded with pagination.
/// Contains a list of [Estate] entities and pagination information.
class PaginatedEstatesLoaded extends EstateState {
  final List<Estate> estates;
  final String? lastDocumentId;
  final bool hasMore;
  final bool hasMoreData;
  final int currentPage;
  final int totalPages;
  final int totalItems;

  PaginatedEstatesLoaded({
    required this.estates,
    this.lastDocumentId,
    this.hasMore = false,
    this.hasMoreData = false,
    this.currentPage = 1,
    this.totalPages = 1,
    this.totalItems = 0,
  });
}

/// State representing an error during estate operations.
/// Contains an error [message] describing the issue.
class EstateError extends EstateState {
  final String message;
  EstateError(this.message);
}
