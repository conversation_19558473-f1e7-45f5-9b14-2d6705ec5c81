import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:math' as math;

import '../entities/estate.dart';

class AdvancedSearchService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// البحث في العقارات بناءً على المعايير المحددة
  Future<List<Estate>> searchProperties(Map<String, dynamic> criteria) async {
    try {
      print('🔍 بدء البحث بالمعايير: $criteria');

      // جلب جميع العقارات أولاً (نظراً لقيود Firebase في الفلترة المتعددة)
      Query query = _firestore.collection('estates');

      // تطبيق فلاتر أساسية على مستوى قاعدة البيانات
      if (criteria['mainCategory'] != null && criteria['mainCategory'].isNotEmpty) {
        query = query.where('mainCategory', isEqualTo: criteria['mainCategory']);
      }

      if (criteria['subCategory'] != null && criteria['subCategory'].isNotEmpty) {
        query = query.where('subCategory', isEqualTo: criteria['subCategory']);
      }

      if (criteria['governorate'] != null && criteria['governorate'].isNotEmpty) {
        query = query.where('governorate', isEqualTo: criteria['governorate']);
      }

      // فلترة العقارات المتاحة فقط
      if (criteria['availableOnly'] == true) {
        query = query.where('isAvailable', isEqualTo: true);
      }

      // فلترة الإعلانات المدفوعة فقط
      if (criteria['paidAdsOnly'] == true) {
        query = query.where('isPaymentVerified', isEqualTo: true);
      }

      // ترتيب حسب تاريخ الإنشاء
      query = query.orderBy('createdAt', descending: true);

      // تحديد عدد النتائج
      query = query.limit(1000); // حد أقصى للأداء

      final snapshot = await query.get();
      print('📊 تم جلب ${snapshot.docs.length} عقار من قاعدة البيانات');

      List<Estate> results = snapshot.docs
          .map((doc) => _mapToEstate(doc.data() as Map<String, dynamic>, doc.id))
          .toList();

      // تطبيق جميع الفلاتر في الذاكرة
      results = _applyAllFilters(results, criteria);
      print('✅ تم تطبيق الفلاتر، النتائج: ${results.length}');

      // ترتيب النتائج
      results = _sortResults(results, criteria);

      // حفظ البحث في التاريخ
      await _saveSearchHistory(criteria, results.length);

      return results;
    } catch (e) {
      print('❌ خطأ في البحث: $e');
      throw Exception('فشل في البحث عن العقارات');
    }
  }

  /// تطبيق جميع الفلاتر في الذاكرة
  List<Estate> _applyAllFilters(List<Estate> estates, Map<String, dynamic> criteria) {
    List<Estate> filtered = List.from(estates);

    // فلتر البحث النصي
    if (criteria['query'] != null && criteria['query'].isNotEmpty) {
      final query = criteria['query'].toString().toLowerCase();
      filtered = filtered.where((estate) {
        return estate.title.toLowerCase().contains(query) ||
               estate.description.toLowerCase().contains(query) ||
               estate.location.toLowerCase().contains(query);
      }).toList();
    }

    // فلتر نطاق السعر
    if (criteria['minPrice'] != null) {
      filtered = filtered.where((estate) => estate.price >= criteria['minPrice']).toList();
    }
    if (criteria['maxPrice'] != null) {
      filtered = filtered.where((estate) => estate.price <= criteria['maxPrice']).toList();
    }

    // فلتر نطاق المساحة
    if (criteria['minArea'] != null && criteria['minArea'] > 0) {
      filtered = filtered.where((estate) =>
        estate.area != null && estate.area! >= criteria['minArea']).toList();
    }
    if (criteria['maxArea'] != null && criteria['maxArea'] > 0) {
      filtered = filtered.where((estate) =>
        estate.area != null && estate.area! <= criteria['maxArea']).toList();
    }

    // فلتر عدد الغرف
    if (criteria['numberOfRooms'] != null && criteria['numberOfRooms'] > 0) {
      filtered = filtered.where((estate) =>
        estate.numberOfRooms == criteria['numberOfRooms']).toList();
    }

    // فلتر عدد الحمامات
    if (criteria['numberOfBathrooms'] != null && criteria['numberOfBathrooms'] > 0) {
      filtered = filtered.where((estate) =>
        estate.numberOfBathrooms == criteria['numberOfBathrooms']).toList();
    }

    // فلتر المميزات
    if (criteria['hasCentralAC'] == true) {
      filtered = filtered.where((estate) => estate.hasCentralAC == true).toList();
    }
    if (criteria['hasElevator'] == true) {
      filtered = filtered.where((estate) => estate.hasElevator == true).toList();
    }
    if (criteria['hasSwimmingPool'] == true) {
      filtered = filtered.where((estate) => estate.hasSwimmingPool == true).toList();
    }
    if (criteria['hasGarage'] == true) {
      filtered = filtered.where((estate) => estate.hasGarage == true).toList();
    }
    if (criteria['isFullyFurnished'] == true) {
      filtered = filtered.where((estate) => estate.isFullyFurnished == true).toList();
    }

    // فلتر الجولات الافتراضية والمحتوى الإضافي (سيتم تطبيقها لاحقاً)
    // TODO: إضافة فلاتر الجولات الافتراضية والفيديو والمخططات عند إضافة الخصائص للكلاس

    // فلتر البحث الجغرافي
    if (criteria['latitude'] != null && criteria['longitude'] != null && criteria['radius'] != null) {
      filtered = _applyGeographicFilter(filtered, criteria);
    }

    return filtered;
  }

  /// تطبيق فلتر البحث الجغرافي
  List<Estate> _applyGeographicFilter(List<Estate> estates, Map<String, dynamic> criteria) {
    final centerLat = criteria['latitude'] as double;
    final centerLng = criteria['longitude'] as double;
    final radiusKm = criteria['radius'] as double;

    return estates.where((estate) {
      if (estate.latitude == null || estate.longitude == null) return false;

      final distance = _calculateDistance(
        centerLat, centerLng,
        estate.latitude!, estate.longitude!
      );

      return distance <= radiusKm;
    }).toList();
  }

  /// حساب المسافة بين نقطتين جغرافيتين (بالكيلومتر)
  double _calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    const double earthRadius = 6371; // نصف قطر الأرض بالكيلومتر

    final dLat = _degreesToRadians(lat2 - lat1);
    final dLng = _degreesToRadians(lng2 - lng1);

    final a = math.sin(dLat / 2) * math.sin(dLat / 2) +
              math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
              math.sin(dLng / 2) * math.sin(dLng / 2);

    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  /// تحويل الدرجات إلى راديان
  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  /// ترتيب النتائج
  List<Estate> _sortResults(List<Estate> estates, Map<String, dynamic> criteria) {
    final sortBy = criteria['sortBy'] ?? 'createdAt';
    final descending = criteria['descending'] ?? true;

    estates.sort((a, b) {
      int comparison = 0;

      switch (sortBy) {
        case 'price':
          comparison = a.price.compareTo(b.price);
          break;
        case 'area':
          final aArea = a.area ?? 0;
          final bArea = b.area ?? 0;
          comparison = aArea.compareTo(bArea);
          break;
        case 'createdAt':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'viewsCount':
          final aViews = a.viewsCount ?? 0;
          final bViews = b.viewsCount ?? 0;
          comparison = aViews.compareTo(bViews);
          break;
        default:
          comparison = a.createdAt.compareTo(b.createdAt);
      }

      return descending ? -comparison : comparison;
    });

    return estates;
  }

  /// حفظ تاريخ البحث
  Future<void> _saveSearchHistory(Map<String, dynamic> criteria, int resultsCount) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      await _firestore.collection('searchHistory').add({
        'userId': user.uid,
        'criteria': criteria,
        'resultsCount': resultsCount,
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('خطأ في حفظ تاريخ البحث: $e');
    }
  }

  /// تطبيق فلاتر قاعدة البيانات
  Query _applyFilters(Query query, Map<String, dynamic> criteria) {
    print('🔍 تطبيق فلاتر قاعدة البيانات: $criteria'); // للتتبع

    // فلتر نوع الاستغلال (البحث في حقول متعددة للتوافق)
    if (criteria['usageType'] != null && criteria['usageType'].isNotEmpty) {
      // تحويل القيم الإنجليزية إلى العربية للبحث في mainCategory
      String searchValue = criteria['usageType'];
      switch (criteria['usageType']) {
        case 'sale':
          searchValue = 'عقار للبيع';
          break;
        case 'rent':
          searchValue = 'عقار للايجار';
          break;
        case 'swap':
          searchValue = 'عقار للبدل';
          break;
        case 'international':
          searchValue = 'عقار دولي';
          break;
      }

      // البحث في حقل mainCategory (حيث يتم حفظ أنواع الاستغلال باللغة العربية)
      query = query.where('mainCategory', isEqualTo: searchValue);
      print('✅ تطبيق فلتر نوع الاستغلال (mainCategory): $searchValue');
    }

    // تم إزالة فلتر نوع العقار من مستوى قاعدة البيانات
    // سيتم تطبيقه على مستوى التطبيق للحصول على مرونة أكبر في البحث

    // فلتر الفئة الرئيسية
    if (criteria['mainCategory'] != null && criteria['mainCategory'].isNotEmpty) {
      query = query.where('mainCategory', isEqualTo: criteria['mainCategory']);
      print('✅ تطبيق فلتر الفئة الرئيسية: ${criteria['mainCategory']}');
    }

    // تم إزالة فلتر الفئة الفرعية من مستوى قاعدة البيانات
    // سيتم تطبيقه على مستوى التطبيق للحصول على مرونة أكبر في البحث

    // فلتر السعر
    if (criteria['priceMin'] != null && criteria['priceMin'] > 0) {
      query = query.where('price', isGreaterThanOrEqualTo: criteria['priceMin']);
      print('✅ تطبيق فلتر السعر الأدنى: ${criteria['priceMin']}');
    }
    if (criteria['priceMax'] != null && criteria['priceMax'] < 1000000) {
      query = query.where('price', isLessThanOrEqualTo: criteria['priceMax']);
      print('✅ تطبيق فلتر السعر الأقصى: ${criteria['priceMax']}');
    }

    // فلتر المساحة
    if (criteria['areaMin'] != null && criteria['areaMin'] > 0) {
      query = query.where('area', isGreaterThanOrEqualTo: criteria['areaMin']);
      print('✅ تطبيق فلتر المساحة الدنيا: ${criteria['areaMin']}');
    }
    if (criteria['areaMax'] != null && criteria['areaMax'] < 1000) {
      query = query.where('area', isLessThanOrEqualTo: criteria['areaMax']);
      print('✅ تطبيق فلتر المساحة القصوى: ${criteria['areaMax']}');
    }

    // فلتر عدد الغرف (البحث في حقل numberOfRooms أو rooms)
    if (criteria['rooms'] != null) {
      query = query.where('numberOfRooms', isEqualTo: criteria['rooms']);
      print('✅ تطبيق فلتر عدد الغرف (numberOfRooms): ${criteria['rooms']}');
    }

    // فلتر عدد الحمامات (البحث في حقل numberOfBathrooms أو bathrooms)
    if (criteria['bathrooms'] != null) {
      query = query.where('numberOfBathrooms', isEqualTo: criteria['bathrooms']);
      print('✅ تطبيق فلتر عدد الحمامات (numberOfBathrooms): ${criteria['bathrooms']}');
    }

    // فلتر الفئة (للتوافق مع الإصدارات القديمة)
    if (criteria['category'] != null && criteria['category'].isNotEmpty) {
      query = query.where('mainCategory', isEqualTo: criteria['category']);
      print('✅ تطبيق فلتر الفئة: ${criteria['category']}');
    }

    return query;
  }



  /// فحص تطابق نوع العقار مع المعايير المحددة
  bool _matchesPropertyType(Estate property, String searchType) {
    print('🔍 فحص تطابق نوع العقار:');
    print('  - نوع البحث: $searchType');
    print('  - propertyType: ${property.propertyType}');
    print('  - subCategory: ${property.subCategory}');

    // البحث المباشر في propertyType
    if (property.propertyType == searchType) {
      print('  ✅ تطابق مباشر في propertyType');
      return true;
    }

    // البحث المباشر في subCategory
    if (property.subCategory == searchType) {
      print('  ✅ تطابق مباشر في subCategory');
      return true;
    }

    // البحث الذكي في subCategory
    final subCategory = property.subCategory?.toLowerCase() ?? '';
    final searchTypeLower = searchType.toLowerCase();

    // أنماط البحث المعتمدة في التطبيق
    final patterns = [
      '$searchTypeLower للبيع',
      '$searchTypeLower للإيجار',
      '$searchTypeLower للايجار', // تنويع في الكتابة
      '$searchTypeLower للبدل',
      '$searchTypeLower للاستثمار',
      'بيع $searchTypeLower',
      'إيجار $searchTypeLower',
      'ايجار $searchTypeLower', // تنويع في الكتابة
      'بدل $searchTypeLower',
      'استثمار $searchTypeLower',
    ];

    // فحص الأنماط
    for (final pattern in patterns) {
      if (subCategory.contains(pattern.toLowerCase())) {
        print('  ✅ تطابق نمط: $pattern');
        return true;
      }
    }

    // فحص إضافي: البحث عن الكلمة في بداية subCategory
    if (subCategory.startsWith(searchTypeLower)) {
      print('  ✅ تطابق في بداية subCategory');
      return true;
    }

    // فحص إضافي: البحث عن الكلمة مع مسافة
    if (subCategory.contains(' $searchTypeLower ') ||
        subCategory.contains(' $searchTypeLower')) {
      print('  ✅ تطابق مع مسافة في subCategory');
      return true;
    }

    print('  ❌ لا يوجد تطابق');
    return false;
  }

  /// تطبيق فلاتر إضافية
  List<Estate> _applyAdditionalFilters(List<Estate> properties, Map<String, dynamic> criteria) {
    List<Estate> filtered = properties;

    // البحث النصي
    if (criteria['keyword'] != null && criteria['keyword'].isNotEmpty) {
      final keyword = criteria['keyword'].toString().toLowerCase();
      filtered = filtered.where((property) {
        return property.title.toLowerCase().contains(keyword) ||
               property.description.toLowerCase().contains(keyword) ||
               property.location.toLowerCase().contains(keyword);
      }).toList();
    }

    // فلتر الموقع النصي
    if (criteria['location'] != null && criteria['location'].isNotEmpty) {
      final location = criteria['location'].toString().toLowerCase();
      filtered = filtered.where((property) {
        return property.location.toLowerCase().contains(location);
      }).toList();
    }

    // فلتر المرافق
    if (criteria['amenities'] != null && criteria['amenities'].isNotEmpty) {
      final amenities = criteria['amenities'] as List<String>;
      filtered = filtered.where((property) {
        return _hasAmenities(property, amenities);
      }).toList();
    }

    // فلتر البحث الجغرافي
    if (criteria['searchLocation'] != null && criteria['searchRadius'] != null) {
      final searchLocation = criteria['searchLocation'] as LatLng;
      final searchRadius = criteria['searchRadius'] as double;

      filtered = filtered.where((property) {
        if (property.latitude != null && property.longitude != null) {
          final distance = _calculateDistance(
            searchLocation.latitude,
            searchLocation.longitude,
            property.latitude!,
            property.longitude!);
          return distance <= searchRadius;
        }
        return false;
      }).toList();
    }

    return filtered;
  }

  /// التحقق من وجود المرافق في العقار
  bool _hasAmenities(Estate property, List<String> amenities) {
    for (final amenity in amenities) {
      switch (amenity) {
        case 'تكييف مركزي':
          if (!property.hasCentralAC) return false;
          break;
        case 'مصعد':
          if (!(property.hasElevator ?? false)) return false;
          break;
        case 'مرآب':
          if (!property.hasGarage) return false;
          break;
        case 'غرفة خادمة':
          if (!property.hasMaidRoom) return false;
          break;
        case 'مفروش':
          if (!(property.isFullyFurnished ?? false)) return false;
          break;
        // يمكن إضافة المزيد من المرافق هنا
      }
    }
    return true;
  }





  /// الحصول على تاريخ البحث
  Future<List<Map<String, dynamic>>> getSearchHistory() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchHistory')
          .orderBy('searchedAt', descending: true)
          .limit(20)
          .get();

      return snapshot.docs.map((doc) => doc.data()).toList();
    } catch (e) {
      print('Error getting search history: $e');
      return [];
    }
  }

  /// حفظ البحث المفضل
  Future<bool> saveFavoriteSearch(String name, Map<String, dynamic> criteria) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final favoriteSearch = {
        'name': name,
        'criteria': criteria,
        'savedAt': FieldValue.serverTimestamp(),
        'userId': user.uid,
      };

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('savedSearches')
          .add(favoriteSearch);

      return true;
    } catch (e) {
      print('Error saving favorite search: $e');
      return false;
    }
  }

  /// الحصول على البحثات المحفوظة
  Future<List<Map<String, dynamic>>> getSavedSearches() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('savedSearches')
          .orderBy('savedAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting saved searches: $e');
      return [];
    }
  }

  /// حذف بحث محفوظ
  Future<bool> deleteSavedSearch(String searchId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('savedSearches')
          .doc(searchId)
          .delete();

      return true;
    } catch (e) {
      print('Error deleting saved search: $e');
      return false;
    }
  }

  /// إنشاء تنبيه بحث
  Future<bool> createSearchAlert(String name, Map<String, dynamic> criteria) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final searchAlert = {
        'name': name,
        'criteria': criteria,
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'lastChecked': FieldValue.serverTimestamp(),
        'userId': user.uid,
      };

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchAlerts')
          .add(searchAlert);

      return true;
    } catch (e) {
      print('Error creating search alert: $e');
      return false;
    }
  }

  /// الحصول على تنبيهات البحث
  Future<List<Map<String, dynamic>>> getSearchAlerts() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchAlerts')
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting search alerts: $e');
      return [];
    }
  }

  /// تعطيل تنبيه البحث
  Future<bool> disableSearchAlert(String alertId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchAlerts')
          .doc(alertId)
          .update({'isActive': false});

      return true;
    } catch (e) {
      print('Error disabling search alert: $e');
      return false;
    }
  }

  /// البحث الصوتي (تحويل الصوت إلى نص)
  Future<String> speechToText() async {
    // TODO: Implement speech to text functionality
    // يمكن استخدام مكتبة speech_to_text
    throw UnimplementedError('Speech to text not implemented yet');
  }

  /// اقتراحات البحث الذكية
  Future<List<String>> getSearchSuggestions(String query) async {
    try {
      if (query.isEmpty) return [];

      // البحث في العقارات الموجودة للحصول على اقتراحات
      final snapshot = await _firestore
          .collection('estates')
          .where('title', isGreaterThanOrEqualTo: query)
          .where('title', isLessThan: '${query}z')
          .limit(10)
          .get();

      final suggestions = <String>{};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        suggestions.add(data['title'] ?? '');
        suggestions.add(data['location'] ?? '');
        suggestions.add(data['propertyType'] ?? '');
      }

      return suggestions.where((s) => s.isNotEmpty).take(5).toList();
    } catch (e) {
      print('Error getting search suggestions: $e');
      return [];
    }
  }

  /// البحث المشابه
  Future<List<Estate>> findSimilarProperties(Estate property) async {
    try {
      final criteria = {
        'propertyType': property.propertyType,
        'priceMin': property.price * 0.8,
        'priceMax': property.price * 1.2,
        'areaMin': (property.area ?? 0) * 0.8,
        'areaMax': (property.area ?? 0) * 1.2,
        'location': property.location,
      };

      final results = await searchProperties(criteria);

      // إزالة العقار الحالي من النتائج
      results.removeWhere((p) => p.id == property.id);

      return results.take(10).toList();
    } catch (e) {
      print('Error finding similar properties: $e');
      return [];
    }
  }

  /// تحليل اتجاهات البحث
  Future<Map<String, dynamic>> getSearchTrends() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return {};

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchHistory')
          .orderBy('searchedAt', descending: true)
          .limit(50)
          .get();

      final trends = <String, int>{};

      for (final doc in snapshot.docs) {
        final criteria = doc.data()['criteria'] as Map<String, dynamic>;

        // تحليل الكلمات المفتاحية
        if (criteria['keyword'] != null && criteria['keyword'].isNotEmpty) {
          final keyword = criteria['keyword'].toString().toLowerCase();
          trends[keyword] = (trends[keyword] ?? 0) + 1;
        }

        // تحليل المواقع
        if (criteria['location'] != null && criteria['location'].isNotEmpty) {
          final location = criteria['location'].toString();
          trends[location] = (trends[location] ?? 0) + 1;
        }

        // تحليل أنواع العقارات
        if (criteria['propertyType'] != null && criteria['propertyType'].isNotEmpty) {
          final type = criteria['propertyType'].toString();
          trends[type] = (trends[type] ?? 0) + 1;
        }
      }

      // ترتيب الاتجاهات
      final sortedTrends = trends.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return {
        'topKeywords': sortedTrends.take(5).map((e) => e.key).toList(),
        'searchCount': snapshot.docs.length,
        'trends': Map.fromEntries(sortedTrends.take(10)),
      };
    } catch (e) {
      print('Error getting search trends: $e');
      return {};
    }
  }

  /// تحويل البيانات من Firestore إلى Estate object
  Estate _mapToEstate(Map<String, dynamic> data, String id) {
    return Estate(
      id: id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      location: data['location'] ?? '',
      photoUrls: List<String>.from(data['photoUrls'] ?? []),
      isFeatured: data['isFeatured'] ?? false,
      planType: data['planType'] ?? 'free',
      startDate: data['startDate'] != null
          ? (data['startDate'] is String
              ? DateTime.parse(data['startDate'])
              : (data['startDate'] as Timestamp).toDate())
          : null,
      endDate: data['endDate'] != null
          ? (data['endDate'] is String
              ? DateTime.parse(data['endDate'])
              : (data['endDate'] as Timestamp).toDate())
          : null,
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] is String
              ? DateTime.parse(data['createdAt'])
              : (data['createdAt'] as Timestamp).toDate())
          : DateTime.now(),
      // الحقول الجديدة من النظام المحسن
      mainCategory: data['mainCategory'],
      subCategory: data['subCategory'],
      usageType: data['usageType'] ?? data['purpose'], // دعم كلا الحقلين
      postedByUserType: data['postedByUserType'],
      hidePhone: data['hidePhone'] ?? false,
      extraPhones: List<String>.from(data['extraPhones'] ?? []),
      shareLocation: data['shareLocation'] ?? false,
      lat: data['lat']?.toDouble(),
      lng: data['lng']?.toDouble(),
      // المميزات والتجهيزات
      hasCentralAC: data['hasCentralAC'] ?? false,
      hasSecurity: data['hasSecurity'],
      allowPets: data['allowPets'],
      hasElevator: data['hasElevator'],
      hasSwimmingPool: data['hasSwimmingPool'],
      hasMaidRoom: data['hasMaidRoom'] ?? false,
      hasGarage: data['hasGarage'] ?? false,
      hasBalcony: data['hasBalcony'],
      isFullyFurnished: data['isFullyFurnished'],
      hasGarden: data['hasGarden'],
      hasPool: data['hasPool'],
      hasDriverRoom: data['hasDriverRoom'],
      hasPrivateEntrance: data['hasPrivateEntrance'],
      hasEquippedKitchen: data['hasEquippedKitchen'],
      // تفاصيل العقار
      rebound: data['rebound'],
      numberOfRooms: data['numberOfRooms'],
      internalLocation: data['internalLocation'],
      salon: data['salon'],
      area: data['area']?.toDouble(),
      floorNumber: data['floorNumber'],
      numberOfBathrooms: data['numberOfBathrooms'],
      buildingAge: data['buildingAge'],
      numberOfFloors: data['numberOfFloors'],
      propertyType: data['propertyType'],
      // إعدادات الإعلان
      autoRepublish: data['autoRepublish'] ?? false,
      kuwaitCornersPin: data['kuwaitCornersPin'] ?? false,
      movingAd: data['movingAd'] ?? false,
      vipBadge: data['vipBadge'] ?? false,
      pinnedOnHome: data['pinnedOnHome'] ?? false,
      discountCode: data['discountCode'],
      // بيانات المُعلِن
      advertiserImage: data['advertiserImage'],
      advertiserName: data['advertiserName'],
      advertiserEmail: data['advertiserEmail'],
      advertiserRegistrationDate: data['advertiserRegistrationDate'] != null
          ? (data['advertiserRegistrationDate'] is String
              ? DateTime.parse(data['advertiserRegistrationDate'])
              : (data['advertiserRegistrationDate'] as Timestamp).toDate())
          : null,
      advertiserAdsCount: data['advertiserAdsCount'],
      // ملكية العقار
      ownerId: data['ownerId'],
      originalEstateId: data['originalEstateId'],
      isOriginal: data['isOriginal'] ?? true,
      copiedBy: List<String>.from(data['copiedBy'] ?? []),
      isCopied: data['isCopied'] ?? false,
      copiedAt: data['copiedAt'] != null
          ? (data['copiedAt'] is String
              ? DateTime.parse(data['copiedAt'])
              : (data['copiedAt'] as Timestamp).toDate())
          : null,
      copyCount: data['copyCount'] ?? 0,
      // الدفع والتحقق
      isPaymentVerified: data['isPaymentVerified'] ?? false,
      isPaidAd: data['isPaidAd'] ?? false,
      adType: data['adType'],
      adExpiryDate: data['adExpiryDate'] != null
          ? (data['adExpiryDate'] is String
              ? DateTime.parse(data['adExpiryDate'])
              : (data['adExpiryDate'] as Timestamp).toDate())
          : null,
      // الإحصائيات
      viewsCount: data['viewsCount'],
      inquiriesCount: data['inquiriesCount'],
      favoritesCount: data['favoritesCount'],
      // الموقع الجغرافي (دعم كلا النظامين)
      latitude: data['latitude']?.toDouble() ?? data['lat']?.toDouble(),
      longitude: data['longitude']?.toDouble() ?? data['lng']?.toDouble(),
      // الحقول للتوافق مع النظام القديم
      rooms: data['rooms'] ?? data['numberOfRooms'],
      bathrooms: data['bathrooms'] ?? data['numberOfBathrooms'],
      floors: data['floors'] ?? data['numberOfFloors'],
      purpose: data['purpose'] ?? data['usageType'],
      // الحالة
      isAvailable: data['isAvailable'] ?? true);
  }
}
