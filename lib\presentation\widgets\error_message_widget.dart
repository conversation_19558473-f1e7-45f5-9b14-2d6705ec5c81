import 'package:flutter/material.dart';

/// مكون لعرض رسائل الخطأ بشكل أفضل
class ErrorMessageWidget extends StatelessWidget {
  /// رسالة الخطأ
  final String message;
  
  /// دالة يتم استدعاؤها عند الضغط على زر إعادة المحاولة
  final VoidCallback? onRetry;
  
  /// ما إذا كان يجب عرض زر إعادة المحاولة
  final bool showRetryButton;

  const ErrorMessageWidget({
    super.key,
    required this.message,
    this.onRetry,
    this.showRetryButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(Icons.error_outline, color: Colors.red.shade700),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  message,
                  style: TextStyle(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold))),
            ]),
          if (showRetryButton && onRetry != null) ...[
            const SizedBox(height: 12),
            Align(
              alignment: Alignment.centerLeft,
              child: TextButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red.shade700))),
          ],
        ]));
  }
}
