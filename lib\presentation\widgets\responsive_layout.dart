import 'package:flutter/material.dart';
import '../../core/utils/responsive_utils.dart';

/// مكون التخطيط المتجاوب
class ResponsiveLayout extends StatelessWidget {
  /// المحتوى للهواتف الذكية
  final Widget mobile;
  
  /// المحتوى للتابلت (اختياري)
  final Widget? tablet;
  
  /// المحتوى لسطح المكتب (اختياري)
  final Widget? desktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
}

/// مكون الحاوية المتجاوبة
class ResponsiveContainer extends StatelessWidget {
  /// المحتوى
  final Widget child;
  
  /// الحد الأقصى للعرض
  final double? maxWidth;
  
  /// المسافات الداخلية
  final EdgeInsetsGeometry? padding;
  
  /// المسافات الخارجية
  final EdgeInsetsGeometry? margin;
  
  /// محاذاة المحتوى
  final AlignmentGeometry? alignment;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.margin,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = padding ?? EdgeInsets.all(ResponsiveUtils.getPadding(context));
    final responsiveMaxWidth = maxWidth ?? ResponsiveUtils.getMaxContentWidth(context);
    
    return Container(
      width: double.infinity,
      alignment: alignment ?? Alignment.center,
      margin: margin,
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: responsiveMaxWidth),
        child: Container(
          padding: responsivePadding,
          child: child)));
  }
}

/// مكون الشبكة المتجاوبة
class ResponsiveGrid extends StatelessWidget {
  /// قائمة العناصر
  final List<Widget> children;
  
  /// عدد الأعمدة (اختياري - سيتم حسابه تلقائياً)
  final int? crossAxisCount;
  
  /// نسبة العرض إلى الارتفاع
  final double childAspectRatio;
  
  /// المسافة بين العناصر
  final double? spacing;
  
  /// المسافة بين الصفوف
  final double? runSpacing;
  
  /// المسافات الداخلية
  final EdgeInsetsGeometry? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.crossAxisCount,
    this.childAspectRatio = 1.0,
    this.spacing,
    this.runSpacing,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveColumns = crossAxisCount ?? ResponsiveUtils.getGridColumns(context);
    final responsiveSpacing = spacing ?? ResponsiveUtils.getSpacing(context, SpacingType.medium);
    final responsivePadding = padding ?? EdgeInsets.all(ResponsiveUtils.getPadding(context));
    
    return Padding(
      padding: responsivePadding,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: responsiveColumns,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: responsiveSpacing,
          mainAxisSpacing: runSpacing ?? responsiveSpacing),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index]));
  }
}

/// مكون النص المتجاوب
class ResponsiveText extends StatelessWidget {
  /// النص المراد عرضه
  final String text;
  
  /// نوع حجم الخط
  final FontSizeType sizeType;
  
  /// وزن الخط
  final FontWeight? fontWeight;
  
  /// لون النص
  final Color? color;
  
  /// محاذاة النص
  final TextAlign? textAlign;
  
  /// عدد الأسطر الأقصى
  final int? maxLines;
  
  /// كيفية التعامل مع النص الزائد
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.sizeType = FontSizeType.medium,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final fontSize = ResponsiveUtils.getFontSize(context, sizeType);
    
    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow);
  }
}

/// مكون الأيقونة المتجاوبة
class ResponsiveIcon extends StatelessWidget {
  /// الأيقونة
  final IconData icon;
  
  /// نوع حجم الأيقونة
  final IconSizeType sizeType;
  
  /// لون الأيقونة
  final Color? color;

  const ResponsiveIcon(
    this.icon, {
    super.key,
    this.sizeType = IconSizeType.medium,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final iconSize = ResponsiveUtils.getIconSize(context, sizeType);
    
    return Icon(
      icon,
      size: iconSize,
      color: color);
  }
}

/// مكون الزر المتجاوب
class ResponsiveButton extends StatelessWidget {
  /// النص على الزر
  final String text;
  
  /// دالة الضغط
  final VoidCallback? onPressed;
  
  /// لون الزر
  final Color? backgroundColor;
  
  /// لون النص
  final Color? textColor;
  
  /// الأيقونة (اختياري)
  final IconData? icon;
  
  /// عرض الزر بالكامل
  final bool fullWidth;

  const ResponsiveButton({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.fullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    final buttonHeight = ResponsiveUtils.getButtonHeight(context);
    final borderRadius = ResponsiveUtils.getBorderRadius(context);
    final fontSize = ResponsiveUtils.getFontSize(context, FontSizeType.medium);
    
    Widget button = ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: textColor,
        minimumSize: Size(0, buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius))),
      child: Row(
        mainAxisSize: fullWidth ? MainAxisSize.max : MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (icon != null) ...[
            ResponsiveIcon(icon!, sizeType: IconSizeType.small, color: textColor),
            SizedBox(width: ResponsiveUtils.getSpacing(context, SpacingType.small)),
          ],
          Text(
            text,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold)),
        ]));
    
    if (fullWidth) {
      return SizedBox(
        width: double.infinity,
        child: button);
    }
    
    return button;
  }
}

/// مكون البطاقة المتجاوبة
class ResponsiveCard extends StatelessWidget {
  /// المحتوى
  final Widget child;
  
  /// المسافات الداخلية
  final EdgeInsetsGeometry? padding;
  
  /// المسافات الخارجية
  final EdgeInsetsGeometry? margin;
  
  /// لون الخلفية
  final Color? backgroundColor;
  
  /// الارتفاع (الظل)
  final double? elevation;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = padding ?? EdgeInsets.all(ResponsiveUtils.getPadding(context));
    final responsiveMargin = margin ?? EdgeInsets.all(ResponsiveUtils.getSpacing(context, SpacingType.small));
    final borderRadius = ResponsiveUtils.getBorderRadius(context);
    
    return Card(
      margin: responsiveMargin,
      elevation: elevation ?? 2,
      color: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius)),
      child: Padding(
        padding: responsivePadding,
        child: child));
  }
}
