import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج لمعايير البحث المتقدمة
class SearchCriteria extends Equatable {
  /// معرف معايير البحث
  final String id;

  /// اسم معايير البحث (للبحث المحفوظ)
  final String? name;

  /// معرف المستخدم
  final String userId;

  /// نص البحث
  final String? query;

  /// التصنيف الرئيسي (سكني، تجاري، أرض)
  final String? mainCategory;

  /// التصنيف الفرعي (شقة، منزل، مكتب، محل، الخ)
  final String? subCategory;

  /// الحد الأدنى للسعر
  final double? minPrice;

  /// الحد الأقصى للسعر
  final double? maxPrice;

  /// الموقع
  final String? location;

  /// المحافظة
  final String? governorate;

  /// المدينة
  final String? city;

  /// المنطقة
  final String? district;

  /// القطعة
  final String? block;

  /// الحد الأدنى للمساحة
  final double? minArea;

  /// الحد الأقصى للمساحة
  final double? maxArea;

  /// الحد الأدنى لعدد الغرف
  final int? minRooms;

  /// الحد الأقصى لعدد الغرف
  final int? maxRooms;

  /// الحد الأدنى لعدد الحمامات
  final int? minBathrooms;

  /// الحد الأقصى لعدد الحمامات
  final int? maxBathrooms;

  /// ما إذا كان يجب أن يكون العقار مفروش
  final bool? isFurnished;

  /// ما إذا كان يجب أن يكون العقار مع تكييف مركزي
  final bool? hasCentralAC;

  /// ما إذا كان يجب أن يكون العقار مع مصعد
  final bool? hasElevator;

  /// ما إذا كان يجب أن يكون العقار مع مسبح
  final bool? hasSwimmingPool;

  /// ما إذا كان يجب أن يكون العقار مع موقف سيارات
  final bool? hasParking;

  /// ما إذا كان يجب أن يكون العقار مع حديقة
  final bool? hasGarden;

  /// ما إذا كان يجب أن يكون العقار مع أمن
  final bool? hasSecurity;

  /// ما إذا كان يجب أن يكون العقار مميز
  final bool? isFeatured;

  /// ما إذا كان يجب أن يكون العقار متاح فقط
  final bool? isAvailableOnly;

  /// ما إذا كان يجب أن يكون العقار جديد فقط
  final bool? isNewOnly;

  /// ما إذا كان يجب أن يكون العقار مع جولة افتراضية
  final bool? hasVirtualTour;

  /// ما إذا كان يجب أن يكون العقار مع فيديو
  final bool? hasVideo;

  /// ما إذا كان يجب أن يكون العقار مع مخطط طابق
  final bool? hasFloorPlan;

  /// ترتيب النتائج (الأحدث، الأقدم، السعر من الأعلى، السعر من الأقل، الخ)
  final String? sortBy;

  /// ما إذا كان البحث محفوظ
  final bool isSaved;

  /// تاريخ إنشاء معايير البحث
  final DateTime createdAt;

  /// تاريخ آخر استخدام لمعايير البحث
  final DateTime? lastUsedAt;

  /// ما إذا كان البحث مفعل للإشعارات
  final bool notificationsEnabled;

  /// مرشحات إضافية
  final Map<String, dynamic>? additionalFilters;

  const SearchCriteria({
    required this.id,
    this.name,
    required this.userId,
    this.query,
    this.mainCategory,
    this.subCategory,
    this.minPrice,
    this.maxPrice,
    this.location,
    this.governorate,
    this.city,
    this.district,
    this.block,
    this.minArea,
    this.maxArea,
    this.minRooms,
    this.maxRooms,
    this.minBathrooms,
    this.maxBathrooms,
    this.isFurnished,
    this.hasCentralAC,
    this.hasElevator,
    this.hasSwimmingPool,
    this.hasParking,
    this.hasGarden,
    this.hasSecurity,
    this.isFeatured,
    this.isAvailableOnly,
    this.isNewOnly,
    this.hasVirtualTour,
    this.hasVideo,
    this.hasFloorPlan,
    this.sortBy,
    this.isSaved = false,
    required this.createdAt,
    this.lastUsedAt,
    this.notificationsEnabled = false,
    this.additionalFilters,
  });

  /// إنشاء نسخة معدلة من معايير البحث
  SearchCriteria copyWith({
    String? id,
    String? name,
    String? userId,
    String? query,
    String? mainCategory,
    String? subCategory,
    double? minPrice,
    double? maxPrice,
    String? location,
    String? governorate,
    String? city,
    String? district,
    String? block,
    double? minArea,
    double? maxArea,
    int? minRooms,
    int? maxRooms,
    int? minBathrooms,
    int? maxBathrooms,
    bool? isFurnished,
    bool? hasCentralAC,
    bool? hasElevator,
    bool? hasSwimmingPool,
    bool? hasParking,
    bool? hasGarden,
    bool? hasSecurity,
    bool? isFeatured,
    bool? isAvailableOnly,
    bool? isNewOnly,
    bool? hasVirtualTour,
    bool? hasVideo,
    bool? hasFloorPlan,
    String? sortBy,
    bool? isSaved,
    DateTime? createdAt,
    DateTime? lastUsedAt,
    bool? notificationsEnabled,
    Map<String, dynamic>? additionalFilters,
  }) {
    return SearchCriteria(
      id: id ?? this.id,
      name: name ?? this.name,
      userId: userId ?? this.userId,
      query: query ?? this.query,
      mainCategory: mainCategory ?? this.mainCategory,
      subCategory: subCategory ?? this.subCategory,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      location: location ?? this.location,
      governorate: governorate ?? this.governorate,
      city: city ?? this.city,
      district: district ?? this.district,
      block: block ?? this.block,
      minArea: minArea ?? this.minArea,
      maxArea: maxArea ?? this.maxArea,
      minRooms: minRooms ?? this.minRooms,
      maxRooms: maxRooms ?? this.maxRooms,
      minBathrooms: minBathrooms ?? this.minBathrooms,
      maxBathrooms: maxBathrooms ?? this.maxBathrooms,
      isFurnished: isFurnished ?? this.isFurnished,
      hasCentralAC: hasCentralAC ?? this.hasCentralAC,
      hasElevator: hasElevator ?? this.hasElevator,
      hasSwimmingPool: hasSwimmingPool ?? this.hasSwimmingPool,
      hasParking: hasParking ?? this.hasParking,
      hasGarden: hasGarden ?? this.hasGarden,
      hasSecurity: hasSecurity ?? this.hasSecurity,
      isFeatured: isFeatured ?? this.isFeatured,
      isAvailableOnly: isAvailableOnly ?? this.isAvailableOnly,
      isNewOnly: isNewOnly ?? this.isNewOnly,
      hasVirtualTour: hasVirtualTour ?? this.hasVirtualTour,
      hasVideo: hasVideo ?? this.hasVideo,
      hasFloorPlan: hasFloorPlan ?? this.hasFloorPlan,
      sortBy: sortBy ?? this.sortBy,
      isSaved: isSaved ?? this.isSaved,
      createdAt: createdAt ?? this.createdAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      additionalFilters: additionalFilters ?? this.additionalFilters);
  }

  /// تحويل معايير البحث إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'userId': userId,
      'query': query,
      'mainCategory': mainCategory,
      'subCategory': subCategory,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'location': location,
      'governorate': governorate,
      'city': city,
      'district': district,
      'block': block,
      'minArea': minArea,
      'maxArea': maxArea,
      'minRooms': minRooms,
      'maxRooms': maxRooms,
      'minBathrooms': minBathrooms,
      'maxBathrooms': maxBathrooms,
      'isFurnished': isFurnished,
      'hasCentralAC': hasCentralAC,
      'hasElevator': hasElevator,
      'hasSwimmingPool': hasSwimmingPool,
      'hasParking': hasParking,
      'hasGarden': hasGarden,
      'hasSecurity': hasSecurity,
      'isFeatured': isFeatured,
      'isAvailableOnly': isAvailableOnly,
      'isNewOnly': isNewOnly,
      'hasVirtualTour': hasVirtualTour,
      'hasVideo': hasVideo,
      'hasFloorPlan': hasFloorPlan,
      'sortBy': sortBy,
      'isSaved': isSaved,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastUsedAt': lastUsedAt != null ? Timestamp.fromDate(lastUsedAt!) : null,
      'notificationsEnabled': notificationsEnabled,
      'additionalFilters': additionalFilters,
    };
  }

  /// إنشاء معايير بحث من Map
  factory SearchCriteria.fromMap(Map<String, dynamic> map) {
    return SearchCriteria(
      id: map['id'] ?? '',
      name: map['name'],
      userId: map['userId'] ?? '',
      query: map['query'],
      mainCategory: map['mainCategory'],
      subCategory: map['subCategory'],
      minPrice: map['minPrice'],
      maxPrice: map['maxPrice'],
      location: map['location'],
      governorate: map['governorate'],
      city: map['city'],
      district: map['district'],
      block: map['block'],
      minArea: map['minArea'],
      maxArea: map['maxArea'],
      minRooms: map['minRooms'],
      maxRooms: map['maxRooms'],
      minBathrooms: map['minBathrooms'],
      maxBathrooms: map['maxBathrooms'],
      isFurnished: map['isFurnished'],
      hasCentralAC: map['hasCentralAC'],
      hasElevator: map['hasElevator'],
      hasSwimmingPool: map['hasSwimmingPool'],
      hasParking: map['hasParking'],
      hasGarden: map['hasGarden'],
      hasSecurity: map['hasSecurity'],
      isFeatured: map['isFeatured'],
      isAvailableOnly: map['isAvailableOnly'],
      isNewOnly: map['isNewOnly'],
      hasVirtualTour: map['hasVirtualTour'],
      hasVideo: map['hasVideo'],
      hasFloorPlan: map['hasFloorPlan'],
      sortBy: map['sortBy'],
      isSaved: map['isSaved'] ?? false,
      createdAt: map['createdAt'] is Timestamp
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      lastUsedAt: map['lastUsedAt'] is Timestamp
          ? (map['lastUsedAt'] as Timestamp).toDate()
          : null,
      notificationsEnabled: map['notificationsEnabled'] ?? false,
      additionalFilters: map['additionalFilters']);
  }

  /// إنشاء معايير بحث من DocumentSnapshot
  factory SearchCriteria.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return SearchCriteria.fromMap(data);
  }

  /// تحويل معايير البحث إلى Map لاستخدامها في البحث
  Map<String, dynamic> toSearchMap() {
    final Map<String, dynamic> searchMap = {};

    if (query != null && query!.isNotEmpty) {
      searchMap['query'] = query;
    }

    if (mainCategory != null && mainCategory!.isNotEmpty) {
      searchMap['mainCategory'] = mainCategory;
    }

    if (subCategory != null && subCategory!.isNotEmpty) {
      searchMap['subCategory'] = subCategory;
    }

    if (minPrice != null) {
      searchMap['minPrice'] = minPrice;
    }

    if (maxPrice != null) {
      searchMap['maxPrice'] = maxPrice;
    }

    if (location != null && location!.isNotEmpty) {
      searchMap['location'] = location;
    }

    if (governorate != null && governorate!.isNotEmpty) {
      searchMap['governorate'] = governorate;
    }

    if (city != null && city!.isNotEmpty) {
      searchMap['city'] = city;
    }

    if (district != null && district!.isNotEmpty) {
      searchMap['district'] = district;
    }

    if (block != null && block!.isNotEmpty) {
      searchMap['block'] = block;
    }

    final Map<String, dynamic> filters = {};

    if (minArea != null) {
      filters['minArea'] = minArea;
    }

    if (maxArea != null) {
      filters['maxArea'] = maxArea;
    }

    if (minRooms != null) {
      filters['minRooms'] = minRooms;
    }

    if (maxRooms != null) {
      filters['maxRooms'] = maxRooms;
    }

    if (minBathrooms != null) {
      filters['minBathrooms'] = minBathrooms;
    }

    if (maxBathrooms != null) {
      filters['maxBathrooms'] = maxBathrooms;
    }

    if (isFurnished != null) {
      filters['isFurnished'] = isFurnished;
    }

    if (hasCentralAC != null) {
      filters['hasCentralAC'] = hasCentralAC;
    }

    if (hasElevator != null) {
      filters['hasElevator'] = hasElevator;
    }

    if (hasSwimmingPool != null) {
      filters['hasSwimmingPool'] = hasSwimmingPool;
    }

    if (hasParking != null) {
      filters['hasParking'] = hasParking;
    }

    if (hasGarden != null) {
      filters['hasGarden'] = hasGarden;
    }

    if (hasSecurity != null) {
      filters['hasSecurity'] = hasSecurity;
    }

    if (isFeatured != null) {
      filters['isFeatured'] = isFeatured;
    }

    if (isAvailableOnly != null) {
      filters['isAvailableOnly'] = isAvailableOnly;
    }

    if (isNewOnly != null) {
      filters['isNewOnly'] = isNewOnly;
    }

    if (hasVirtualTour != null) {
      filters['hasVirtualTour'] = hasVirtualTour;
    }

    if (hasVideo != null) {
      filters['hasVideo'] = hasVideo;
    }

    if (hasFloorPlan != null) {
      filters['hasFloorPlan'] = hasFloorPlan;
    }

    if (additionalFilters != null) {
      filters.addAll(additionalFilters!);
    }

    if (filters.isNotEmpty) {
      searchMap['filters'] = filters;
    }

    if (sortBy != null && sortBy!.isNotEmpty) {
      searchMap['sortBy'] = sortBy;
    }

    return searchMap;
  }

  /// الحصول على وصف موجز لمعايير البحث
  String getSummary() {
    final List<String> parts = [];

    if (mainCategory != null && mainCategory!.isNotEmpty) {
      parts.add(mainCategory!);
    }

    if (subCategory != null && subCategory!.isNotEmpty) {
      parts.add(subCategory!);
    }

    if (location != null && location!.isNotEmpty) {
      parts.add('في $location');
    } else if (city != null && city!.isNotEmpty) {
      parts.add('في $city');
    }

    if (minPrice != null && maxPrice != null) {
      parts.add('بسعر من $minPrice إلى $maxPrice');
    } else if (minPrice != null) {
      parts.add('بسعر أكبر من $minPrice');
    } else if (maxPrice != null) {
      parts.add('بسعر أقل من $maxPrice');
    }

    if (minArea != null && maxArea != null) {
      parts.add('بمساحة من $minArea إلى $maxArea متر مربع');
    } else if (minArea != null) {
      parts.add('بمساحة أكبر من $minArea متر مربع');
    } else if (maxArea != null) {
      parts.add('بمساحة أقل من $maxArea متر مربع');
    }

    if (minRooms != null) {
      parts.add('بعدد غرف $minRooms+');
    }

    if (minBathrooms != null) {
      parts.add('بعدد حمامات $minBathrooms+');
    }

    if (parts.isEmpty && query != null && query!.isNotEmpty) {
      return 'بحث عن: $query';
    }

    return parts.join(' ');
  }

  @override
  List<Object?> get props => [
    id,
    name,
    userId,
    query,
    mainCategory,
    subCategory,
    minPrice,
    maxPrice,
    location,
    governorate,
    city,
    district,
    block,
    minArea,
    maxArea,
    minRooms,
    maxRooms,
    minBathrooms,
    maxBathrooms,
    isFurnished,
    hasCentralAC,
    hasElevator,
    hasSwimmingPool,
    hasParking,
    hasGarden,
    hasSecurity,
    isFeatured,
    isAvailableOnly,
    isNewOnly,
    hasVirtualTour,
    hasVideo,
    hasFloorPlan,
    sortBy,
    isSaved,
    createdAt,
    lastUsedAt,
    notificationsEnabled,
    additionalFilters,
  ];
}
