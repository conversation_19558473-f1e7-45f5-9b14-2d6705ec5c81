import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة لحفظ مسودات الإعلانات واستعادتها
class AdDraftService {
  static const String _draftKey = 'ad_draft';
  static const String _draftTimeKey = 'ad_draft_time';
  static const Duration _draftValidity = Duration(days: 7);

  /// حفظ مسودة الإعلان
  /// [draftData] بيانات المسودة
  Future<bool> saveDraft(Map<String, dynamic> draftData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = json.encode(draftData);
      
      await prefs.setString(_draftKey, jsonData);
      await prefs.setInt(_draftTimeKey, DateTime.now().millisecondsSinceEpoch);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// استعادة مسودة الإعلان
  /// يعيد null إذا لم تكن هناك مسودة أو إذا كانت المسودة قديمة
  Future<Map<String, dynamic>?> getDraft() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = prefs.getString(_draftKey);
      
      if (jsonData == null) {
        return null;
      }
      
      // التحقق من صلاحية المسودة
      final draftTime = prefs.getInt(_draftTimeKey);
      if (draftTime != null) {
        final savedAt = DateTime.fromMillisecondsSinceEpoch(draftTime);
        final now = DateTime.now();
        
        if (now.difference(savedAt) > _draftValidity) {
          // المسودة قديمة، حذفها
          await deleteDraft();
          return null;
        }
      }
      
      return json.decode(jsonData) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// حذف مسودة الإعلان
  Future<bool> deleteDraft() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.remove(_draftKey);
      await prefs.remove(_draftTimeKey);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من وجود مسودة
  Future<bool> hasDraft() async {
    try {
      final draft = await getDraft();
      return draft != null;
    } catch (e) {
      return false;
    }
  }
}
