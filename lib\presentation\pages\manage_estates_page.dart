import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../domain/entities/estate.dart';
import '../bloc/estate_bloc.dart';
import '../bloc/estate_event.dart';
import '../bloc/estate_state.dart';
import '../widgets/estate_card.dart';
import 'improved_ad_creation_entry.dart';
import '../../core/theme/cairo_text_styles.dart';
import '../../core/services/notification_service.dart';
import '../../domain/models/notification_model.dart' as domain;

/// Page for managing estates. This page allows users to view, delete,
/// and (optionally) edit existing estates.
/// It also provides a floating action button to add a new estate.
class ManageEstatesPage extends StatefulWidget {
  const ManageEstatesPage({super.key});

  @override
  State<ManageEstatesPage> createState() => _ManageEstatesPageState();
}

class _ManageEstatesPageState extends State<ManageEstatesPage> {
  @override
  void initState() {
    super.initState();
    // Fetch all estates when the page initializes.
    context.read<EstateBloc>().add(FetchEstates());
  }

  /// Deletes an estate by dispatching a [DeleteEstateEvent] with the given [id].
  void _deleteEstate(String id) {
    context.read<EstateBloc>().add(DeleteEstateEvent(id));

    // عرض إشعار فوري
    NotificationService.showInAppNotification(
      context,
      title: 'تم حذف الإعلان',
      body: 'تم حذف إعلان العقار بنجاح',
      type: domain.NotificationType.system,
      duration: const Duration(seconds: 3),
    );
  }

  /// Placeholder method for editing an estate.
  /// You can implement navigation to an edit page where the estate data can be updated.
  void _editEstate(Estate estate) {
    // TODO: Navigate to an estate edit page.
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "إدارة العقارات",
          style: CairoTextStyles.appBarTitle)),
      // Listen to the EstateBloc state changes using BlocBuilder.
      body: BlocBuilder<EstateBloc, EstateState>(
        builder: (context, state) {
          if (state is EstateLoading) {
            // Display a loading indicator while estates are being fetched.
            return const Center(child: CircularProgressIndicator());
          } else if (state is EstateLoaded) {
            final estates = state.estates;
            // Build a ListView of estate cards.
            return ListView.builder(
              itemCount: estates.length,
              itemBuilder: (context, index) {
                final estate = estates[index];
                // Wrap each estate card in a Dismissible to enable swipe-to-delete.
                return Dismissible(
                  key: Key(estate.id),
                  background: Container(color: Colors.red),
                  onDismissed: (_) => _deleteEstate(estate.id),
                  child: EstateCard(
                    estate: estate,
                    // Indicates that this card is being shown in the manage section.
                    isManageSection: true,
                    // Callback for when the edit action is triggered.
                    onEdit: () => _editEstate(estate)));
              });
          } else if (state is EstateError) {
            // Display an error message if fetching estates fails.
            return Center(
              child: Text(
                state.message,
                style: CairoTextStyles.error));
          }
          return const SizedBox();
        }),
      // Floating action button to add a new estate.
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to the ImprovedAdCreationEntry when pressed.
          Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => const ImprovedAdCreationEntry()));
        },
        child: const Icon(Icons.add)));
  }
}
