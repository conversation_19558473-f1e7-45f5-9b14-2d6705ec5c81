import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج أساسي للعقار يحتوي على الخصائص المشتركة بين جميع أنواع العقارات
abstract class EstateBase extends Equatable {
  // معلومات أساسية
  final String id;
  final String title;
  final String description;
  final double price;
  final String location;
  final List<String> photoUrls;
  final bool isFeatured;
  final String status; // متاح، مباع، مؤجر، قيد الإنشاء

  // معلومات الموقع
  final String? governorate; // المحافظة
  final String? city; // المدينة
  final String? district; // المنطقة
  final String? block; // القطعة
  final double? latitude;
  final double? longitude;
  final bool shareLocation;

  // معلومات التصنيف
  final String mainCategory; // سكني، تجاري، أرض
  final String? subCategory; // شقة، منزل، مكتب، محل، الخ

  // معلومات المعلن
  final String ownerId;
  final String? advertiserName;
  final String? advertiserPhone;
  final String? advertiserImage;
  final String? advertiserType; // مالك، وسيط، شركة
  final DateTime? advertiserJoinDate;
  final int? advertiserAdsCount;
  final bool hidePhone;
  final List<String> extraPhones;

  // معلومات الإعلان
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? startDate;
  final DateTime? endDate;
  final int viewsCount;
  final int favoritesCount;
  final int contactCount;

  // معلومات الاشتراك والترويج
  final String subscriptionPlan; // free, monthly, yearly
  final bool autoRepublish;
  final bool isPinned;
  final bool isPromoted;
  final bool isVIP;
  final bool isVerified;
  final bool isPaymentVerified; // هل تم التحقق من الدفع

  // معلومات النسخ
  final String? originalEstateId;
  final bool isOriginal;
  final List<String>? copiedBy;

  // معلومات متقدمة
  final String? floorPlanUrl; // رابط مخطط الطابق
  final String? virtualTourUrl; // رابط الجولة الافتراضية
  final String? videoUrl; // رابط فيديو للعقار
  final List<Map<String, dynamic>>? documents; // مستندات إضافية (صكوك، رخص، الخ)

  const EstateBase({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.location,
    required this.photoUrls,
    required this.isFeatured,
    required this.status,
    this.governorate,
    this.city,
    this.district,
    this.block,
    this.latitude,
    this.longitude,
    this.shareLocation = false,
    required this.mainCategory,
    this.subCategory,
    required this.ownerId,
    this.advertiserName,
    this.advertiserPhone,
    this.advertiserImage,
    this.advertiserType,
    this.advertiserJoinDate,
    this.advertiserAdsCount,
    this.hidePhone = false,
    this.extraPhones = const [],
    required this.createdAt,
    this.updatedAt,
    this.startDate,
    this.endDate,
    this.viewsCount = 0,
    this.favoritesCount = 0,
    this.contactCount = 0,
    this.subscriptionPlan = 'free',
    this.autoRepublish = false,
    this.isPinned = false,
    this.isPromoted = false,
    this.isVIP = false,
    this.isVerified = false,
    this.isPaymentVerified = false,
    this.originalEstateId,
    this.isOriginal = true,
    this.copiedBy,
    this.floorPlanUrl,
    this.virtualTourUrl,
    this.videoUrl,
    this.documents,
  });

  /// التحقق من صحة بيانات العقار الأساسية
  Map<String, String> validate() {
    final errors = <String, String>{};

    if (title.isEmpty) {
      errors['title'] = 'يجب إدخال عنوان الإعلان';
    } else if (title.length < 5) {
      errors['title'] = 'يجب أن يكون العنوان أكثر من 5 أحرف';
    } else if (title.length > 100) {
      errors['title'] = 'يجب أن يكون العنوان أقل من 100 حرف';
    }

    if (description.isEmpty) {
      errors['description'] = 'يجب إدخال وصف الإعلان';
    } else if (description.length < 20) {
      errors['description'] = 'يجب أن يكون الوصف أكثر من 20 حرف';
    } else if (description.length > 2000) {
      errors['description'] = 'يجب أن يكون الوصف أقل من 2000 حرف';
    }

    if (price <= 0) {
      errors['price'] = 'يجب إدخال سعر صحيح';
    } else if (price > 10000000) {
      errors['price'] = 'السعر مرتفع جداً، يرجى التحقق';
    }

    if (location.isEmpty) {
      errors['location'] = 'يجب إدخال الموقع';
    }

    if (photoUrls.isEmpty) {
      errors['photoUrls'] = 'يجب إضافة صورة واحدة على الأقل';
    }

    if (mainCategory.isEmpty) {
      errors['mainCategory'] = 'يجب تحديد التصنيف الرئيسي';
    }

    if (ownerId.isEmpty) {
      errors['ownerId'] = 'معرف المالك غير صالح';
    }

    return errors;
  }

  /// التحقق مما إذا كان الإعلان ضمن فترة النشر
  bool isWithinPublishPeriod() {
    final now = DateTime.now();
    if (startDate == null || endDate == null) {
      return false;
    }
    return now.isAfter(startDate!) && now.isBefore(endDate!);
  }

  /// التحقق مما إذا كان الإعلان على وشك الانتهاء (أقل من 3 أيام)
  bool isExpiringSoon() {
    final now = DateTime.now();
    if (endDate == null) {
      return false;
    }
    final difference = endDate!.difference(now).inDays;
    return difference >= 0 && difference <= 3;
  }

  /// حساب الأيام المتبقية للإعلان
  int getRemainingDays() {
    final now = DateTime.now();
    if (endDate == null) {
      return 0;
    }
    return endDate!.difference(now).inDays;
  }

  /// الحصول على نوع العقار (يجب تنفيذه في الفئات الفرعية)
  String getEstateType();

  /// تحويل العقار إلى Map (يجب تنفيذه في الفئات الفرعية)
  Map<String, dynamic> toMap();

  /// تحويل العقار إلى Map للعرض في واجهة المستخدم
  Map<String, dynamic> toDisplayMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'location': location,
      'mainPhoto': photoUrls.isNotEmpty ? photoUrls.first : null,
      'photoCount': photoUrls.length,
      'mainCategory': mainCategory,
      'subCategory': subCategory,
      'advertiserName': advertiserName,
      'advertiserType': advertiserType,
      'isVerified': isVerified,
      'isPaymentVerified': isPaymentVerified,
      'isVIP': isVIP,
      'isPromoted': isPromoted,
      'isPinned': isPinned,
      'createdAt': createdAt,
      'remainingDays': getRemainingDays(),
      'viewsCount': viewsCount,
      'favoritesCount': favoritesCount,
      'estateType': getEstateType(),
      'hasVirtualTour': virtualTourUrl != null,
      'hasVideo': videoUrl != null,
      'hasFloorPlan': floorPlanUrl != null,
    };
  }

  /// إنشاء نسخة معدلة من العقار (يجب تنفيذه في الفئات الفرعية)
  EstateBase copyWithBase({
    String? id,
    String? title,
    String? description,
    double? price,
    String? location,
    List<String>? photoUrls,
    bool? isFeatured,
    String? status,
    String? governorate,
    String? city,
    String? district,
    String? block,
    double? latitude,
    double? longitude,
    bool? shareLocation,
    String? mainCategory,
    String? subCategory,
    String? ownerId,
    String? advertiserName,
    String? advertiserPhone,
    String? advertiserImage,
    String? advertiserType,
    DateTime? advertiserJoinDate,
    int? advertiserAdsCount,
    bool? hidePhone,
    List<String>? extraPhones,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? startDate,
    DateTime? endDate,
    int? viewsCount,
    int? favoritesCount,
    int? contactCount,
    String? subscriptionPlan,
    bool? autoRepublish,
    bool? isPinned,
    bool? isPromoted,
    bool? isVIP,
    bool? isVerified,
    bool? isPaymentVerified,
    String? originalEstateId,
    bool? isOriginal,
    List<String>? copiedBy,
    String? floorPlanUrl,
    String? virtualTourUrl,
    String? videoUrl,
    List<Map<String, dynamic>>? documents,
  });

  /// تحويل Timestamp إلى DateTime
  static DateTime? timestampToDateTime(dynamic timestamp) {
    if (timestamp == null) return null;
    if (timestamp is Timestamp) {
      return timestamp.toDate();
    }
    return null;
  }

  /// تحويل DateTime إلى Timestamp
  static Timestamp? dateTimeToTimestamp(DateTime? dateTime) {
    if (dateTime == null) return null;
    return Timestamp.fromDate(dateTime);
  }
}
