import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kuwait_corners/infrastructure/services/image_service.dart';
import 'dart:io';

/// صفحة إضافة عميل جديد
class AddClientPage extends StatefulWidget {
  const AddClientPage({super.key});

  @override
  State<AddClientPage> createState() => _AddClientPageState();
}

class _AddClientPageState extends State<AddClientPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _notesController = TextEditingController();
  final _budgetController = TextEditingController();

  String _selectedType = 'مشتري';
  String _selectedStatus = 'جديد';
  String _selectedInterest = 'شراء';
  bool _isVip = false;
  bool _isLoading = false;
  bool _isImageUploading = false;

  // متغيرات رفع الصورة
  File? _selectedImage;
  final ImagePicker _imagePicker = ImagePicker();
  final ImageService _imageService = ImageService();

  final List<String> _clientTypes = ['مشتري', 'مستأجر', 'بائع', 'مؤجر'];
  final List<String> _clientStatuses = ['جديد', 'نشط', 'محتمل'];
  final List<String> _interests = ['شراء', 'إيجار', 'بيع', 'تأجير', 'استثمار'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'إضافة عميل جديد',
          style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white)),
      body: Stack(
        children: [
          // خلفية بأشكال هندسية
          _buildBackgroundShapes(),

          // المحتوى الرئيسي
          Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildImageSection(),
                  const SizedBox(height: 24),
                  _buildBasicInfoSection(),
                  const SizedBox(height: 24),
                  _buildContactInfoSection(),
                  const SizedBox(height: 24),
                  _buildPreferencesSection(),
                  const SizedBox(height: 24),
                  _buildAdditionalInfoSection(),
                  const SizedBox(height: 32),
                  _buildActionButtons(),
                ]))),
        ]));
  }

  Widget _buildBackgroundShapes() {
    return Positioned.fill(
      child: Stack(
        children: [
          // أيقونة إضافة - محسنة
          Positioned(
            top: 90,
            right: -30,
            child: Opacity(
              opacity: 0.025,
              child: Icon(
                Icons.person_add_outlined,
                size: 105,
                color: AppColors.primary))),

          // أيقونة عميل - أصغر
          Positioned(
            top: 270,
            left: -22,
            child: Opacity(
              opacity: 0.03,
              child: Icon(
                Icons.person_outline,
                size: 80,
                color: AppColors.primary))),

          // أيقونة إدارة - موضعة أفضل
          Positioned(
            bottom: 210,
            right: -18,
            child: Opacity(
              opacity: 0.025,
              child: Icon(
                Icons.manage_accounts_outlined,
                size: 68,
                color: AppColors.primary))),

          // أيقونة معلومات - أصغر
          Positioned(
            bottom: 360,
            left: -12,
            child: Opacity(
              opacity: 0.03,
              child: Icon(
                Icons.info_outline,
                size: 58,
                color: AppColors.primary))),

          // أيقونة كاميرا - جديدة
          Positioned(
            top: 460,
            right: -10,
            child: Opacity(
              opacity: 0.02,
              child: Icon(
                Icons.camera_alt_outlined,
                size: 52,
                color: AppColors.primary))),

          // دوائر هندسية - أصغر وأكثر توزيعاً
          Positioned(
            top: 170,
            left: 32,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.015)))),

          Positioned(
            top: 330,
            right: 52,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.02)))),

          Positioned(
            bottom: 170,
            right: 72,
            child: Container(
              width: 26,
              height: 26,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.018)))),

          Positioned(
            bottom: 290,
            left: 62,
            child: Container(
              width: 17,
              height: 17,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.022)))),

          // مربعات مدورة - أصغر
          Positioned(
            top: 390,
            right: 26,
            child: Container(
              width: 15,
              height: 15,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3),
                color: AppColors.primary.withValues(alpha: 0.015)))),

          Positioned(
            top: 230,
            left: 20,
            child: Container(
              width: 22,
              height: 22,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: AppColors.primary.withValues(alpha: 0.018)))),

          Positioned(
            bottom: 200,
            left: 30,
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(7),
                color: AppColors.primary.withValues(alpha: 0.02)))),
        ]));
  }

  Widget _buildImageSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.photo_camera, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text('صورة العميل', style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 24),
          Center(
            child: Stack(
              children: [
                // الصورة الرئيسية
                Container(
                  width: 140,
                  height: 140,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      width: 3),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 8))
                    ]),
                  child: ClipOval(
                    child: _selectedImage != null
                        ? Image.file(
                            _selectedImage!,
                            width: 140,
                            height: 140,
                            fit: BoxFit.cover)
                        : Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.primary.withValues(alpha: 0.1),
                                  AppColors.primary.withValues(alpha: 0.05)
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight)),
                            child: Icon(
                              Icons.person,
                              size: 70,
                              color: AppColors.primary.withValues(alpha: 0.6))))),

                // زر اختيار الصورة
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: AnimatedScale(
                    scale: _isImageUploading ? 0.9 : 1.0,
                    duration: const Duration(milliseconds: 150),
                    child: GestureDetector(
                      onTap: _isImageUploading ? null : _pickImage,
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        width: 44,
                        height: 44,
                        decoration: BoxDecoration(
                          color: _isImageUploading
                              ? Colors.grey[400]
                              : AppColors.primary,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 3),
                          boxShadow: [
                            BoxShadow(
                              color: (_isImageUploading
                                  ? Colors.grey
                                  : AppColors.primary).withValues(alpha: 0.3),
                              blurRadius: _isImageUploading ? 8 : 12,
                              offset: const Offset(0, 4))
                          ]),
                        child: _isImageUploading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2))
                            : const Icon(
                                Icons.camera_alt,
                                color: Colors.white,
                                size: 20))))),

              ])),
        ]));
  }

  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.person, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text('المعلومات الأساسية', style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 24),

          // اسم العميل
          TextFormField(
            controller: _nameController,
            style: CairoTextStyles.bodyMedium,
            decoration: InputDecoration(
              labelText: 'اسم العميل *',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.person_outline, color: AppColors.primary),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02)),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال اسم العميل';
              }
              return null;
            }),
          const SizedBox(height: 20),

          // نوع العميل وحالته
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedType,
                  style: CairoTextStyles.bodyMedium,
                  decoration: InputDecoration(
                    labelText: 'نوع العميل',
                    labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.primary, width: 2)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
                    prefixIcon: Icon(Icons.category, color: AppColors.primary),
                    filled: true,
                    fillColor: Colors.grey.withValues(alpha: 0.02)),
                  items: _clientTypes.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type, style: CairoTextStyles.bodyMedium));
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedType = value!;
                    });
                  })),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  style: CairoTextStyles.bodyMedium,
                  decoration: InputDecoration(
                    labelText: 'حالة العميل',
                    labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.primary, width: 2)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
                    prefixIcon: Icon(Icons.flag, color: AppColors.primary),
                    filled: true,
                    fillColor: Colors.grey.withValues(alpha: 0.02)),
                  items: _clientStatuses.map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(status, style: CairoTextStyles.bodyMedium));
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value!;
                    });
                  })),
            ]),
          const SizedBox(height: 20),

          // خيار VIP
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.amber.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.amber.withValues(alpha: 0.2))),
            child: Row(
              children: [
                Checkbox(
                  value: _isVip,
                  onChanged: (value) {
                    setState(() {
                      _isVip = value!;
                    });
                  },
                  activeColor: Colors.amber,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4))),
                const SizedBox(width: 12),
                Icon(Icons.star, color: Colors.amber, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'عميل VIP',
                    style: CairoTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.amber[800]))),
                if (_isVip)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(20)),
                    child: Text(
                      'مفعل',
                      style: CairoTextStyles.labelSmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold))),
              ])),
        ]));
  }

  Widget _buildContactInfoSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.contact_phone, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text('معلومات التواصل', style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 24),

          // رقم الهاتف
          TextFormField(
            controller: _phoneController,
            style: CairoTextStyles.bodyMedium,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              labelText: 'رقم الهاتف *',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.phone, color: AppColors.primary),
              hintText: '+965 XXXX XXXX',
              hintStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[400]),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02)),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال رقم الهاتف';
              }
              return null;
            }),
          const SizedBox(height: 20),

          // البريد الإلكتروني
          TextFormField(
            controller: _emailController,
            style: CairoTextStyles.bodyMedium,
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              labelText: 'البريد الإلكتروني',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.email, color: AppColors.primary),
              hintText: '<EMAIL>',
              hintStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[400]),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02)),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                  return 'يرجى إدخال بريد إلكتروني صحيح';
                }
              }
              return null;
            }),
        ]));
  }

  Widget _buildPreferencesSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.interests, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text('التفضيلات والاهتمامات', style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 24),

          // الاهتمام الرئيسي
          DropdownButtonFormField<String>(
            value: _selectedInterest,
            style: CairoTextStyles.bodyMedium,
            decoration: InputDecoration(
              labelText: 'الاهتمام الرئيسي',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.favorite, color: AppColors.primary),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02)),
            items: _interests.map((interest) {
              return DropdownMenuItem(
                value: interest,
                child: Text(interest, style: CairoTextStyles.bodyMedium));
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedInterest = value!;
              });
            }),
          const SizedBox(height: 20),

          // الميزانية المتوقعة
          TextFormField(
            controller: _budgetController,
            style: CairoTextStyles.bodyMedium,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'الميزانية المتوقعة (د.ك)',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.attach_money, color: AppColors.primary),
              hintText: '0',
              hintStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[400]),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02))),
        ]));
  }

  Widget _buildAdditionalInfoSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.note, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text('معلومات إضافية', style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 24),

          // ملاحظات
          TextFormField(
            controller: _notesController,
            style: CairoTextStyles.bodyMedium,
            maxLines: 4,
            decoration: InputDecoration(
              labelText: 'ملاحظات',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.note_alt, color: AppColors.primary),
              hintText: 'أضف أي ملاحظات مهمة عن العميل...',
              hintStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[400]),
              alignLabelWithHint: true,
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02))),
        ]));
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Row(
        children: [
          // زر الإلغاء
          Expanded(
            child: Container(
              height: 56,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.withValues(alpha: 0.3))),
              child: OutlinedButton(
                onPressed: _isLoading ? null : () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.grey[700],
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12))),
                child: Text(
                  'إلغاء',
                  style: CairoTextStyles.button.copyWith(
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w600))))),
          const SizedBox(width: 16),

          // زر الحفظ
          Expanded(
            flex: 2,
            child: Container(
              height: 56,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary,
                    AppColors.primary.withValues(alpha: 0.8)
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight)),
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveClient,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12))),
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2.5))
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.save, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'حفظ العميل',
                            style: CairoTextStyles.button.copyWith(
                              fontWeight: FontWeight.bold)),
                        ])))),
        ]));
  }

  void _saveClient() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // رفع الصورة إذا تم اختيارها
      String? imageUrl;
      if (_selectedImage != null) {
        imageUrl = await _imageService.uploadImage(_selectedImage!, 'clients');
      }

      final clientData = {
        'name': _nameController.text.trim(),
        'email': _emailController.text.trim(),
        'phone': _phoneController.text.trim(),
        'type': _selectedType,
        'status': _selectedStatus,
        'interest': _selectedInterest,
        'budget': double.tryParse(_budgetController.text) ?? 0.0,
        'notes': _notesController.text.trim(),
        'isVip': _isVip,
        'agentId': currentUser.uid,
        'createdAt': FieldValue.serverTimestamp(),
        'lastContact': FieldValue.serverTimestamp(),
        'totalDeals': 0,
        'totalValue': 0.0,
        'favoriteEstates': [],
        'visitedEstates': [],
        'appointments': [],
        'offers': [],
        'soldRentedEstates': [],
        'searchCriteria': {},
        'additionalInfo': {},
        if (imageUrl != null) 'imageUrl': imageUrl,
      };

      await FirebaseFirestore.instance
          .collection('clients')
          .add(clientData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'تم إضافة العميل بنجاح'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8))));
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ في إضافة العميل: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8))));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // دالة اختيار الصورة
  Future<void> _pickImage() async {
    try {
      setState(() {
        _isImageUploading = true;
      });

      final XFile? pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        setState(() {
          _selectedImage = File(pickedFile.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في اختيار الصورة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isImageUploading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    _budgetController.dispose();
    super.dispose();
  }
}