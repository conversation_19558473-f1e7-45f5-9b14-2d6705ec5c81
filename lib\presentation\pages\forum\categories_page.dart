import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/enums/view_modes.dart';
import '../../../domain/models/forum/category_model.dart';
import '../../providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/empty_view.dart';
import '../../widgets/forum/modern_forum_header.dart';

/// صفحة أقسام المنتدى
class CategoriesPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/modern-forum/categories';

  const CategoriesPage({super.key});

  @override
  State<CategoriesPage> createState() => _CategoriesPageState();
}

class _CategoriesPageState extends State<CategoriesPage> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String _searchQuery = '';
  GridViewMode _viewMode = GridViewMode.grid;

  @override
  void initState() {
    super.initState();

    // جلب البيانات عند تحميل الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      forumProvider.fetchCategories();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// بدء البحث
  void _startSearch() {
    setState(() {
      _isSearching = true;
    });
  }

  /// إلغاء البحث
  void _cancelSearch() {
    setState(() {
      _isSearching = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  /// تنفيذ البحث
  void _performSearch(String query) {
    setState(() {
      _searchQuery = query.trim().toLowerCase();
    });
  }

  /// تغيير نمط العرض
  void _toggleViewMode() {
    setState(() {
      _viewMode = _viewMode == GridViewMode.grid ? GridViewMode.list : GridViewMode.grid;
    });
  }

  /// تصفية الفئات حسب البحث
  List<CategoryModel> _filterCategories(List<CategoryModel> categories) {
    if (_searchQuery.isEmpty) {
      return categories;
    }

    return categories.where((category) {
      return category.name.toLowerCase().contains(_searchQuery) ||
          category.description.toLowerCase().contains(_searchQuery);
    }).toList();
  }

  /// الحصول على الفئات الرئيسية (غير الفرعية)
  List<CategoryModel> _getMainCategories(List<CategoryModel> categories) {
    return categories.where((category) => category.parentId == null).toList();
  }

  /// الحصول على الفئات الفرعية لفئة معينة
  List<CategoryModel> _getSubCategories(
      List<CategoryModel> categories, String parentId) {
    return categories.where((category) => category.parentId == parentId).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // رأس الصفحة
            ModernForumHeader(
              title: 'أقسام المنتدى',
              description: 'تصفح أقسام المنتدى واختر القسم المناسب لموضوعك',
              icon: Icons.category,
              backgroundColor: AppColors.primary,
              isSearchActive: _isSearching,
              searchController: _searchController,
              onSearchTap: _startSearch,
              onSearchCancel: _cancelSearch,
              onSearchSubmitted: _performSearch,
              onNotificationsTap: () {
                Navigator.pushNamed(context, '/forum/notifications');
              }),

            // شريط الأدوات
            _buildToolbar(),

            // محتوى الصفحة
            Expanded(
              child: Consumer<ForumProvider>(
                builder: (context, forumProvider, child) {
                  if (forumProvider.categoriesState == LoadingState.loading) {
                    return const Center(child: LoadingIndicator());
                  } else if (forumProvider.categoriesState == LoadingState.error) {
                    return ErrorView(
                      message: 'حدث خطأ في تحميل الأقسام',
                      onRetry: () => forumProvider.fetchCategories());
                  } else if (forumProvider.categoriesState == LoadingState.empty ||
                      forumProvider.categories.isEmpty) {
                    return const EmptyView(
                      message: 'لا توجد أقسام',
                      icon: Icons.category_outlined);
                  }

                  // تصفية الفئات حسب البحث
                  final filteredCategories = _filterCategories(forumProvider.categories);

                  // الحصول على الفئات الرئيسية
                  final mainCategories = _getMainCategories(filteredCategories);

                  if (mainCategories.isEmpty) {
                    return const EmptyView(
                      message: 'لا توجد أقسام مطابقة لبحثك',
                      icon: Icons.search_off_outlined);
                  }

                  return _viewMode == GridViewMode.grid
                      ? _buildGridView(mainCategories, filteredCategories)
                      : _buildListView(mainCategories, filteredCategories);
                })),
          ])));
  }

  /// بناء شريط الأدوات
  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2)),
        ]),
      child: Row(
        children: [
          // عدد الأقسام
          Consumer<ForumProvider>(
            builder: (context, forumProvider, _) {
              final categoriesCount = forumProvider.categories.length;
              return Text(
                '$categoriesCount قسم',
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.bold));
            }),

          const Spacer(),

          // زر تغيير نمط العرض
          IconButton(
            icon: Icon(
              _viewMode == GridViewMode.grid ? Icons.view_list : Icons.grid_view,
              color: AppColors.primary),
            onPressed: _toggleViewMode,
            tooltip: _viewMode == GridViewMode.grid ? 'عرض قائمة' : 'عرض شبكة'),
        ]));
  }

  /// بناء عرض الشبكة
  Widget _buildGridView(
      List<CategoryModel> mainCategories, List<CategoryModel> allCategories) {
    return AnimationLimiter(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: MasonryGridView.count(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          itemCount: mainCategories.length,
          itemBuilder: (context, index) {
            return AnimationConfiguration.staggeredGrid(
              position: index,
              duration: const Duration(milliseconds: 375),
              columnCount: 2,
              child: ScaleAnimation(
                child: FadeInAnimation(
                  child: _buildCategoryCard(
                    mainCategories[index],
                    _getSubCategories(allCategories, mainCategories[index].id)))));
          })));
  }

  /// بناء عرض القائمة
  Widget _buildListView(
      List<CategoryModel> mainCategories, List<CategoryModel> allCategories) {
    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: mainCategories.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildCategoryListItem(
                  mainCategories[index],
                  _getSubCategories(allCategories, mainCategories[index].id)))));
        }));
  }

  /// بناء بطاقة الفئة (للعرض الشبكي)
  Widget _buildCategoryCard(
      CategoryModel category, List<CategoryModel> subCategories) {
    // تحويل اللون من سلسلة نصية إلى كائن Color
    final Color categoryColor = category.getColorObject();

    return InkWell(
      onTap: () => _navigateToCategory(category),
      borderRadius: BorderRadius.circular(16),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 5)),
          ]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    categoryColor.withOpacity(0.7),
                    categoryColor,
                  ]),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16))),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // أيقونة الفئة
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12)),
                    child: Icon(
                      _getCategoryIcon(category.icon),
                      color: Colors.white,
                      size: 24)),
                  const SizedBox(height: 16),

                  // اسم الفئة
                  Text(
                    category.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18)),
                  const SizedBox(height: 8),

                  // إحصائيات الفئة
                  Row(
                    children: [
                      _buildStatItem(Icons.topic_outlined, '${category.topicsCount}', 'موضوع'),
                      const SizedBox(width: 16),
                      _buildStatItem(Icons.forum_outlined, '${category.postsCount}', 'مشاركة'),
                    ]),
                ])),

            // محتوى البطاقة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16))),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // وصف الفئة
                  Text(
                    category.description,
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14,
                      height: 1.4),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),

                  // الفئات الفرعية
                  if (subCategories.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    const Text(
                      'الأقسام الفرعية:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14)),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: subCategories
                          .map((subCategory) => _buildSubCategoryChip(subCategory))
                          .toList()),
                  ],
                ])),
          ])));
  }

  /// بناء عنصر قائمة الفئة (للعرض القائمي)
  Widget _buildCategoryListItem(
      CategoryModel category, List<CategoryModel> subCategories) {
    // تحويل اللون من سلسلة نصية إلى كائن Color
    final Color categoryColor = category.getColorObject();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5)),
        ]),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: () => _navigateToCategory(category),
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس العنصر
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.grey.shade100,
                      width: 1))),
                child: Row(
                  children: [
                    // أيقونة الفئة
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: categoryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12)),
                      child: Icon(
                        _getCategoryIcon(category.icon),
                        color: categoryColor,
                        size: 24)),
                    const SizedBox(width: 16),

                    // معلومات الفئة
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            category.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18)),
                          const SizedBox(height: 4),
                          Text(
                            category.description,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis),
                        ])),

                    // إحصائيات الفئة
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.topic_outlined,
                              size: 16,
                              color: Colors.grey.shade600),
                            const SizedBox(width: 4),
                            Text(
                              '${category.topicsCount}',
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.bold,
                                fontSize: 14)),
                          ]),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.forum_outlined,
                              size: 16,
                              color: Colors.grey.shade600),
                            const SizedBox(width: 4),
                            Text(
                              '${category.postsCount}',
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.bold,
                                fontSize: 14)),
                          ]),
                      ]),
                  ])),

              // الفئات الفرعية
              if (subCategories.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الأقسام الفرعية:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Colors.grey.shade700)),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: subCategories
                            .map((subCategory) => _buildSubCategoryChip(subCategory))
                            .toList()),
                    ])),
            ]))));
  }

  /// بناء رقاقة الفئة الفرعية
  Widget _buildSubCategoryChip(CategoryModel category) {
    final Color categoryColor = category.getColorObject();

    return InkWell(
      onTap: () => _navigateToCategory(category),
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: categoryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: categoryColor.withOpacity(0.3),
            width: 1)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getCategoryIcon(category.icon),
              size: 14,
              color: categoryColor),
            const SizedBox(width: 4),
            Text(
              category.name,
              style: TextStyle(
                color: categoryColor,
                fontSize: 12,
                fontWeight: FontWeight.bold)),
          ])));
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(IconData icon, String count, String label) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.white.withOpacity(0.8)),
        const SizedBox(width: 4),
        Text(
          '$count $label',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12)),
      ]);
  }

  /// الحصول على أيقونة الفئة
  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'home':
        return Icons.home;
      case 'business':
        return Icons.business;
      case 'apartment':
        return Icons.apartment;
      case 'house':
        return Icons.house;
      case 'villa':
        return Icons.villa;
      case 'location_city':
        return Icons.location_city;
      case 'store':
        return Icons.store;
      case 'local_offer':
        return Icons.local_offer;
      case 'attach_money':
        return Icons.attach_money;
      case 'question_answer':
        return Icons.question_answer;
      case 'forum':
        return Icons.forum;
      case 'chat':
        return Icons.chat;
      case 'people':
        return Icons.people;
      case 'person':
        return Icons.person;
      case 'work':
        return Icons.work;
      case 'car_rental':
        return Icons.car_rental;
      case 'directions_car':
        return Icons.directions_car;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'local_mall':
        return Icons.local_mall;
      case 'restaurant':
        return Icons.restaurant;
      case 'local_cafe':
        return Icons.local_cafe;
      case 'school':
        return Icons.school;
      case 'local_hospital':
        return Icons.local_hospital;
      case 'sports':
        return Icons.sports;
      case 'sports_soccer':
        return Icons.sports_soccer;
      case 'movie':
        return Icons.movie;
      case 'music_note':
        return Icons.music_note;
      case 'book':
        return Icons.book;
      case 'newspaper':
        return Icons.newspaper;
      case 'announcement':
        return Icons.announcement;
      case 'info':
        return Icons.info;
      case 'help':
        return Icons.help;
      case 'feedback':
        return Icons.feedback;
      case 'settings':
        return Icons.settings;
      default:
        return Icons.category;
    }
  }

  /// الانتقال إلى صفحة الفئة
  void _navigateToCategory(CategoryModel category) {
    // تحديث الفئة المحددة في مزود المنتدى
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    forumProvider.setSelectedCategory(category.id);

    // الانتقال إلى صفحة المنتدى مع تحديد الفئة
    Navigator.pushNamed(context, '/modern-forum');
  }
}
