import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// صفحة تتيح للمستخدم اختيار موقع من الخريطة.
/// عند اختيار الموقع، يمكن للمستخدم تأكيد الاختيار وإرجاع [LatLng] المحدد.
/// يمكن العودة للصفحة السابقة عن طريق السحب نحو اليمين.
class MapPickerPage extends StatefulWidget {
  const MapPickerPage({super.key});

  @override
  State<MapPickerPage> createState() => _MapPickerPageState();
}

class _MapPickerPageState extends State<MapPickerPage> {
  late GoogleMapController _mapController;
  LatLng? selectedLatLng; // الموقع الذي يتم اختياره من قبل المستخدم

  // عتبة السرعة للسحب (بالبكسل في الثانية)
  final double swipeVelocityThreshold = 300;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // عند انتهاء السحب الأفقي، إذا كانت السرعة إلى اليمين تتجاوز العتبة، يتم الرجوع.
      onHorizontalDragEnd: (details) {
        if (details.velocity.pixelsPerSecond.dx > swipeVelocityThreshold) {
          Navigator.pop(context);
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Stack(
          children: [
            // عرض الخريطة مع مستوى زووم مناسب لعرض دولة الكويت بشكل كامل
            GoogleMap(
              initialCameraPosition: const CameraPosition(
                target: LatLng(29.3759, 47.9774), // مركز الكويت
                zoom: 10, // مستوى زووم لعرض الكويت بشكل كامل
              ),
              onMapCreated: (controller) => _mapController = controller,
              onTap: (latLng) {
                // تحديث الموقع عند النقر على الخريطة
                setState(() {
                  selectedLatLng = latLng;
                });
              },
              markers: selectedLatLng == null
                  ? {}
                  : {
                      Marker(
                        markerId: const MarkerId('selectedLocation'),
                        position: selectedLatLng!,
                        icon: BitmapDescriptor.defaultMarkerWithHue(
                          BitmapDescriptor.hueRed)),
                    },
              // إضافة هوامش لأزرار التكبير والتصغير لتجنب التداخل
              padding: const EdgeInsets.only(
                top: 120, // مساحة للشريط العلوي
                bottom: 120, // مساحة للزر السفلي
                left: 16,
                right: 16),
              // تخصيص موضع أزرار التكبير والتصغير
              zoomControlsEnabled: true,
              myLocationButtonEnabled: false, // إخفاء زر الموقع الحالي لتجنب التداخل
            ),
            // الشريط الزجاجي (تلميح) في أعلى الخريطة
            Positioned(
              top: 50,
              left: 80, // إزاحة لليمين لتجنب التداخل مع زر العودة
              right: 20,
              child: SafeArea(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.95),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2)),
                    ]),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.location_on,
                        color: Colors.indigo,
                        size: 18),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'انقر لتحديد الموقع',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.black87,
                            fontWeight: FontWeight.w500))),
                    ])))),
            // تأثير التدرج في أسفل الشاشة لتحسين وضوح زر التأكيد
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 100,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.transparent, Colors.black12],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter)))),
            // زر العودة في أعلى اليسار
            Positioned(
              top: 50,
              left: 20,
              child: SafeArea(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.95),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2)),
                    ]),
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.indigo),
                    onPressed: () => Navigator.pop(context),
                    tooltip: 'العودة')))),
            // أزرار التحكم في أسفل الصفحة
            Positioned(
              bottom: 30,
              left: 20,
              right: 20,
              child: SafeArea(
                child: Row(
                  children: [
                    // زر إلغاء
                    Expanded(
                      child: Container(
                        height: 50,
                        margin: const EdgeInsets.only(right: 8),
                        child: OutlinedButton.icon(
                          icon: const Icon(Icons.close, size: 20),
                          label: const Text(
                            'إلغاء',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500)),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.indigo,
                            side: const BorderSide(color: Colors.indigo, width: 1.5),
                            backgroundColor: Colors.white.withOpacity(0.95),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                            elevation: 0),
                          onPressed: () => Navigator.pop(context)))),
                    // زر التأكيد
                    Expanded(
                      child: Container(
                        height: 50,
                        margin: const EdgeInsets.only(left: 8),
                        child: ElevatedButton.icon(
                          icon: const Icon(Icons.check, color: Colors.white, size: 20),
                          label: const Text(
                            'تأكيد الموقع',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.white)),
                          style: ElevatedButton.styleFrom(
                            foregroundColor: Colors.white,
                            backgroundColor:
                                selectedLatLng == null ? Colors.grey : Colors.indigo,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                            elevation: 0),
                          onPressed: selectedLatLng == null
                              ? null
                              : () {
                                  Navigator.pop(context, selectedLatLng);
                                }))),
                  ]))),
          ])));
  }
}
