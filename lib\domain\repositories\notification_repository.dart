import '../entities/notification.dart';
import '../entities/search_criteria.dart';
import '../entities/estate_base.dart';

/// واجهة مستودع الإشعارات
abstract class NotificationRepository {
  /// الحصول على إشعارات المستخدم
  Future<List<Notification>> getUserNotifications(String userId);
  
  /// الحصول على إشعارات المستخدم بالتحميل المتدرج
  /// [userId] معرف المستخدم
  /// [limit] عدد الإشعارات في كل صفحة
  /// [lastNotificationId] معرف آخر إشعار تم تحميله (للصفحات التالية)
  /// [unreadOnly] ما إذا كان يجب تحميل الإشعارات غير المقروءة فقط
  /// يعيد Map تحتوي على:
  /// - 'notifications': قائمة الإشعارات
  /// - 'lastNotificationId': معرف آخر إشعار (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من الإشعارات
  Future<Map<String, dynamic>> getUserNotificationsPaginated({
    required String userId,
    int limit = 20,
    String? lastNotificationId,
    bool unreadOnly = false,
  });
  
  /// الحصول على عدد الإشعارات غير المقروءة للمستخدم
  Future<int> getUnreadNotificationsCount(String userId);
  
  /// تحديد إشعار كمقروء
  Future<void> markNotificationAsRead(String notificationId);
  
  /// تحديد جميع إشعارات المستخدم كمقروءة
  Future<void> markAllNotificationsAsRead(String userId);
  
  /// حذف إشعار
  Future<void> deleteNotification(String notificationId);
  
  /// حذف جميع إشعارات المستخدم
  Future<void> deleteAllNotifications(String userId);
  
  /// إنشاء إشعار
  Future<String> createNotification(Notification notification);
  
  /// إنشاء إشعار عن عقار جديد يطابق معايير البحث
  Future<void> createMatchingEstateNotification(
    EstateBase estate,
    SearchCriteria criteria,
    String userId);
  
  /// إنشاء إشعار عن تغيير في سعر عقار
  Future<void> createPriceChangeNotification(
    EstateBase estate,
    double oldPrice,
    double newPrice,
    String userId);
  
  /// إنشاء إشعار عن انتهاء صلاحية إعلان
  Future<void> createEstateExpirationNotification(
    EstateBase estate,
    String userId);
  
  /// إنشاء إشعار عن انتهاء صلاحية اشتراك
  Future<void> createSubscriptionExpirationNotification(
    String subscriptionId,
    String userId);
  
  /// تسجيل المستخدم لتلقي إشعارات عن عقارات جديدة تطابق معايير البحث
  Future<void> subscribeToSearchCriteria(String criteriaId, String userId);
  
  /// إلغاء تسجيل المستخدم لتلقي إشعارات عن عقارات جديدة تطابق معايير البحث
  Future<void> unsubscribeFromSearchCriteria(String criteriaId, String userId);
  
  /// الحصول على إعدادات الإشعارات للمستخدم
  Future<Map<String, bool>> getNotificationSettings(String userId);
  
  /// تحديث إعدادات الإشعارات للمستخدم
  Future<void> updateNotificationSettings(String userId, Map<String, bool> settings);
}
