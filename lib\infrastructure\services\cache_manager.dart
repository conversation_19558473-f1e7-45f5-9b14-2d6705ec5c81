import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// مدير التخزين المؤقت للتطبيق
class AppCacheManager {
  static const String _cacheKey = 'app_cache';
  static const String _imagesCacheKey = 'images_cache';
  static const String _dataCacheKey = 'data_cache';

  static final AppCacheManager _instance = AppCacheManager._internal();

  factory AppCacheManager() {
    return _instance;
  }

  AppCacheManager._internal();

  late Box<dynamic> _dataCache;
  late SharedPreferences _prefs;

  /// تهيئة مدير التخزين المؤقت
  Future<void> init() async {
    await Hive.initFlutter();
    _dataCache = await Hive.openBox(_dataCacheKey);
    _prefs = await SharedPreferences.getInstance();
  }

  /// الحصول على مدير التخزين المؤقت للصور
  DefaultCacheManager get imageCacheManager => DefaultCacheManager();

  /// تخزين بيانات في التخزين المؤقت مع تحسينات الأداء
  Future<void> cacheData(String key, dynamic data, {Duration? expiry}) async {
    try {
      final expiryTime = expiry != null
          ? DateTime.now().add(expiry).millisecondsSinceEpoch
          : null;

      // تحسين: ضغط البيانات الكبيرة قبل التخزين
      final compressedData = _compressDataIfNeeded(data);

      await _dataCache.put('$key:data', compressedData);

      if (expiryTime != null) {
        await _dataCache.put('$key:expiry', expiryTime);
      }

      // تسجيل إحصائيات التخزين المؤقت
      _recordCacheStats(key, 'write');
    } catch (e) {
      debugPrint('❌ خطأ في تخزين البيانات: $e');
      rethrow;
    }
  }

  /// ضغط البيانات إذا كانت كبيرة
  dynamic _compressDataIfNeeded(dynamic data) {
    try {
      // إذا كانت البيانات نص وحجمها كبير، نقوم بضغطها
      if (data is String && data.length > 1000) {
        return gzip.encode(utf8.encode(data));
      }

      // إذا كانت البيانات قائمة كبيرة، نقوم بتحسينها
      if (data is List && data.length > 100) {
        return _optimizeListData(data);
      }

      return data;
    } catch (e) {
      // في حالة فشل الضغط، نعيد البيانات الأصلية
      return data;
    }
  }

  /// تحسين بيانات القائمة
  List<dynamic> _optimizeListData(List<dynamic> data) {
    // إزالة البيانات المكررة والفارغة
    final optimizedData = data.where((item) => item != null).toSet().toList();
    return optimizedData.length < data.length ? optimizedData : data;
  }

  /// الحصول على إحصائيات التخزين المؤقت
  Map<String, dynamic> _getCacheStats() {
    final stats = _dataCache.get('cache_stats');
    if (stats != null && stats is Map<String, dynamic>) {
      return Map<String, dynamic>.from(stats);
    }
    return <String, dynamic>{};
  }

  /// تسجيل إحصائيات التخزين المؤقت
  void _recordCacheStats(String key, String operation) {
    final stats = _getCacheStats();
    stats['${operation}_count'] = (stats['${operation}_count'] ?? 0) + 1;
    stats['last_${operation}_time'] = DateTime.now().millisecondsSinceEpoch;
    _dataCache.put('cache_stats', stats);
  }

  /// الحصول على إحصائيات الأداء
  Map<String, dynamic> getPerformanceStats() {
    final stats = _getCacheStats();
    return {
      'cache_hits': stats['read_count'] ?? 0,
      'cache_writes': stats['write_count'] ?? 0,
      'last_read': stats['last_read_time'],
      'last_write': stats['last_write_time'],
      'cache_size': _dataCache.length,
    };
  }

  /// الحصول على بيانات من التخزين المؤقت
  dynamic getCachedData(String key) {
    final expiryTime = _dataCache.get('$key:expiry');

    if (expiryTime != null) {
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now > expiryTime) {
        // البيانات منتهية الصلاحية، حذفها
        _dataCache.delete('$key:data');
        _dataCache.delete('$key:expiry');
        return null;
      }
    }

    final data = _dataCache.get('$key:data');

    // تسجيل إحصائيات القراءة
    if (data != null) {
      _recordCacheStats(key, 'read');
    }

    return data;
  }

  /// حذف بيانات من التخزين المؤقت
  Future<void> removeCachedData(String key) async {
    await _dataCache.delete('$key:data');
    await _dataCache.delete('$key:expiry');
  }

  /// تخزين صورة في التخزين المؤقت
  Future<void> cacheImage(String url, {Duration? expiry}) async {
    await imageCacheManager.downloadFile(url);

    if (expiry != null) {
      final expiryTime = DateTime.now().add(expiry).millisecondsSinceEpoch;
      await _prefs.setInt('$_imagesCacheKey:$url', expiryTime);
    }
  }

  /// الحصول على صورة من التخزين المؤقت
  Future<File?> getCachedImage(String url) async {
    final expiryTime = _prefs.getInt('$_imagesCacheKey:$url');

    if (expiryTime != null) {
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now > expiryTime) {
        // الصورة منتهية الصلاحية، حذفها
        await imageCacheManager.removeFile(url);
        await _prefs.remove('$_imagesCacheKey:$url');
        return null;
      }
    }

    final fileInfo = await imageCacheManager.getFileFromCache(url);
    return fileInfo?.file;
  }

  /// حذف صورة من التخزين المؤقت
  Future<void> removeCachedImage(String url) async {
    await imageCacheManager.removeFile(url);
    await _prefs.remove('$_imagesCacheKey:$url');
  }

  /// تخزين بيانات ثنائية في التخزين المؤقت
  Future<void> cacheBinaryData(String key, Uint8List data,
      {Duration? expiry}) async {
    final dir = await getTemporaryDirectory();
    final file = File('${dir.path}/$key');
    await file.writeAsBytes(data);

    if (expiry != null) {
      final expiryTime = DateTime.now().add(expiry).millisecondsSinceEpoch;
      await _prefs.setInt('$_cacheKey:$key', expiryTime);
    }
  }

  /// الحصول على بيانات ثنائية من التخزين المؤقت
  Future<Uint8List?> getCachedBinaryData(String key) async {
    final dir = await getTemporaryDirectory();
    final file = File('${dir.path}/$key');

    if (!file.existsSync()) {
      return null;
    }

    final expiryTime = _prefs.getInt('$_cacheKey:$key');

    if (expiryTime != null) {
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now > expiryTime) {
        // البيانات منتهية الصلاحية، حذفها
        await file.delete();
        await _prefs.remove('$_cacheKey:$key');
        return null;
      }
    }

    return await file.readAsBytes();
  }

  /// حذف بيانات ثنائية من التخزين المؤقت
  Future<void> removeCachedBinaryData(String key) async {
    final dir = await getTemporaryDirectory();
    final file = File('${dir.path}/$key');

    if (file.existsSync()) {
      await file.delete();
    }

    await _prefs.remove('$_cacheKey:$key');
  }

  /// مسح كل التخزين المؤقت
  Future<void> clearCache() async {
    await _dataCache.clear();
    await imageCacheManager.emptyCache();

    // حذف جميع مفاتيح التخزين المؤقت من SharedPreferences
    final keys = _prefs.getKeys();
    for (final key in keys) {
      if (key.startsWith('$_cacheKey:') ||
          key.startsWith('$_imagesCacheKey:')) {
        await _prefs.remove(key);
      }
    }

    // حذف الملفات المؤقتة
    final dir = await getTemporaryDirectory();
    if (dir.existsSync()) {
      dir.listSync().forEach((entity) {
        if (entity is File) {
          entity.deleteSync();
        }
      });
    }
  }

  /// الحصول على حجم التخزين المؤقت
  Future<int> getCacheSize() async {
    int size = 0;

    // حجم التخزين المؤقت للصور
    final cacheDir = await getTemporaryDirectory();
    if (cacheDir.existsSync()) {
      cacheDir.listSync(recursive: true, followLinks: false).forEach((entity) {
        if (entity is File) {
          size += entity.lengthSync();
        }
      });
    }

    return size;
  }

  /// الحصول على حجم التخزين المؤقت بصيغة مقروءة
  Future<String> getReadableCacheSize() async {
    final cacheSize = await getCacheSize();

    if (cacheSize < 1024) {
      return '$cacheSize B';
    } else if (cacheSize < 1024 * 1024) {
      final kbSize = cacheSize / 1024;
      return '${kbSize.toStringAsFixed(2)} KB';
    } else if (cacheSize < 1024 * 1024 * 1024) {
      final mbSize = cacheSize / (1024 * 1024);
      return '${mbSize.toStringAsFixed(2)} MB';
    } else {
      final gbSize = cacheSize / (1024 * 1024 * 1024);
      return '${gbSize.toStringAsFixed(2)} GB';
    }
  }
}

/// مدير التخزين المؤقت للبيانات عن بعد
class RemoteDataCacheManager {
  static final RemoteDataCacheManager _instance =
      RemoteDataCacheManager._internal();

  factory RemoteDataCacheManager() {
    return _instance;
  }

  RemoteDataCacheManager._internal();

  final AppCacheManager _cacheManager = AppCacheManager();

  /// تخزين بيانات API في التخزين المؤقت
  Future<void> cacheApiResponse(String endpoint, dynamic data,
      {Duration? expiry}) async {
    await _cacheManager.cacheData('api:$endpoint', data, expiry: expiry);
  }

  /// الحصول على بيانات API من التخزين المؤقت
  dynamic getCachedApiResponse(String endpoint) {
    return _cacheManager.getCachedData('api:$endpoint');
  }

  /// حذف بيانات API من التخزين المؤقت
  Future<void> removeCachedApiResponse(String endpoint) async {
    await _cacheManager.removeCachedData('api:$endpoint');
  }

  /// تخزين قائمة عقارات في التخزين المؤقت
  Future<void> cacheEstatesList(String key, List<dynamic> estates,
      {Duration? expiry}) async {
    await _cacheManager.cacheData('estates:$key', estates, expiry: expiry);
  }

  /// الحصول على قائمة عقارات من التخزين المؤقت
  List<dynamic>? getCachedEstatesList(String key) {
    final data = _cacheManager.getCachedData('estates:$key');
    if (data != null && data is List) {
      return data;
    }
    return null;
  }

  /// تخزين عقار في التخزين المؤقت
  Future<void> cacheEstate(String estateId, dynamic estate,
      {Duration? expiry}) async {
    await _cacheManager.cacheData('estate:$estateId', estate, expiry: expiry);
  }

  /// الحصول على عقار من التخزين المؤقت
  dynamic getCachedEstate(String estateId) {
    return _cacheManager.getCachedData('estate:$estateId');
  }

  /// تخزين بيانات المستخدم في التخزين المؤقت
  Future<void> cacheUserData(String userId, dynamic userData,
      {Duration? expiry}) async {
    await _cacheManager.cacheData('user:$userId', userData, expiry: expiry);
  }

  /// الحصول على بيانات المستخدم من التخزين المؤقت
  dynamic getCachedUserData(String userId) {
    return _cacheManager.getCachedData('user:$userId');
  }

  /// تخزين نتائج البحث في التخزين المؤقت
  Future<void> cacheSearchResults(String query, dynamic results,
      {Duration? expiry}) async {
    await _cacheManager.cacheData('search:$query', results, expiry: expiry);
  }

  /// الحصول على نتائج البحث من التخزين المؤقت
  dynamic getCachedSearchResults(String query) {
    return _cacheManager.getCachedData('search:$query');
  }
}

/// مدير التخزين المؤقت للبيانات المحلية
class LocalDataCacheManager {
  static final LocalDataCacheManager _instance =
      LocalDataCacheManager._internal();

  factory LocalDataCacheManager() {
    return _instance;
  }

  LocalDataCacheManager._internal();

  final AppCacheManager _cacheManager = AppCacheManager();

  /// تخزين إعدادات المستخدم في التخزين المؤقت
  Future<void> cacheUserSettings(
      String userId, Map<String, dynamic> settings) async {
    await _cacheManager.cacheData('settings:$userId', settings);
  }

  /// الحصول على إعدادات المستخدم من التخزين المؤقت
  Map<String, dynamic>? getCachedUserSettings(String userId) {
    final data = _cacheManager.getCachedData('settings:$userId');
    if (data != null && data is Map<String, dynamic>) {
      return data;
    }
    return null;
  }

  /// تخزين حالة التطبيق في التخزين المؤقت
  Future<void> cacheAppState(Map<String, dynamic> state) async {
    await _cacheManager.cacheData('app:state', state);
  }

  /// الحصول على حالة التطبيق من التخزين المؤقت
  Map<String, dynamic>? getCachedAppState() {
    final data = _cacheManager.getCachedData('app:state');
    if (data != null && data is Map<String, dynamic>) {
      return data;
    }
    return null;
  }

  /// تخزين بيانات التصفح في التخزين المؤقت
  Future<void> cacheBrowsingHistory(
      String userId, List<dynamic> history) async {
    await _cacheManager.cacheData('history:$userId', history);
  }

  /// الحصول على بيانات التصفح من التخزين المؤقت
  List<dynamic>? getCachedBrowsingHistory(String userId) {
    final data = _cacheManager.getCachedData('history:$userId');
    if (data != null && data is List) {
      return data;
    }
    return null;
  }

  /// تخزين بيانات المفضلة في التخزين المؤقت
  Future<void> cacheFavorites(String userId, List<dynamic> favorites) async {
    await _cacheManager.cacheData('favorites:$userId', favorites);
  }

  /// الحصول على بيانات المفضلة من التخزين المؤقت
  List<dynamic>? getCachedFavorites(String userId) {
    final data = _cacheManager.getCachedData('favorites:$userId');
    if (data != null && data is List) {
      return data;
    }
    return null;
  }

  /// تخزين بيانات المقارنة في التخزين المؤقت
  Future<void> cacheComparisons(
      String userId, List<dynamic> comparisons) async {
    await _cacheManager.cacheData('comparisons:$userId', comparisons);
  }

  /// الحصول على بيانات المقارنة من التخزين المؤقت
  List<dynamic>? getCachedComparisons(String userId) {
    final data = _cacheManager.getCachedData('comparisons:$userId');
    if (data != null && data is List) {
      return data;
    }
    return null;
  }
}

/// امتدادات لتسهيل استخدام مدير التخزين المؤقت
extension CacheManagerExtensions on AppCacheManager {
  /// تخزين بيانات مع مفتاح مركب
  Future<void> cacheDataWithCompoundKey(List<String> keyParts, dynamic data,
      {Duration? expiry}) async {
    final key = keyParts.join(':');
    await cacheData(key, data, expiry: expiry);
  }

  /// الحصول على بيانات بمفتاح مركب
  dynamic getCachedDataWithCompoundKey(List<String> keyParts) {
    final key = keyParts.join(':');
    return getCachedData(key);
  }

  /// حذف بيانات بمفتاح مركب
  Future<void> removeCachedDataWithCompoundKey(List<String> keyParts) async {
    final key = keyParts.join(':');
    await removeCachedData(key);
  }

  /// تخزين بيانات مع مفتاح مركب ونسخة
  Future<void> cacheDataWithVersion(String key, dynamic data, int version,
      {Duration? expiry}) async {
    await cacheData('$key:v$version', data, expiry: expiry);
    await cacheData('$key:version', version);
  }

  /// الحصول على بيانات بمفتاح مركب ونسخة
  dynamic getCachedDataWithVersion(String key) {
    final version = getCachedData('$key:version');
    if (version == null) {
      return null;
    }

    return getCachedData('$key:v$version');
  }

  /// الحصول على نسخة البيانات
  int? getCachedDataVersion(String key) {
    final version = getCachedData('$key:version');
    if (version != null && version is int) {
      return version;
    }
    return null;
  }
}
