import 'estate_base.dart';

/// نموذج للعقارات التجارية
class CommercialEstate extends EstateBase {
  // خصائص العقارات التجارية
  final double? area; // المساحة بالمتر المربع
  final int? numberOfRooms; // عدد الغرف
  final int? numberOfBathrooms; // عدد الحمامات
  final int? numberOfFloors; // عدد الطوابق
  final int? floorNumber; // رقم الطابق
  final int? buildingAge; // عمر البناء بالسنوات

  // المرافق والميزات
  final bool hasCentralAC; // تكييف مركزي
  final bool hasElevator; // مصعد
  final bool hasSecurity; // أمن
  final bool hasParking; // مواقف سيارات
  final bool hasStorage; // مخزن
  final bool hasFireSystem; // نظام إطفاء حريق
  final bool hasLoadingDock; // منصة تحميل
  final bool hasShowroom; // صالة عرض

  // معلومات إضافية
  final String? commercialType; // نوع العقار التجاري (مكتب، محل، مخزن، مصنع)
  final String? streetWidth; // عرض الشارع
  final bool? isFurnished; // مفروش
  final bool? hasWaterAndElectricity; // متوفر ماء وكهرباء
  final String? licenseType; // نوع الترخيص
  final String? commercialActivity; // النشاط التجاري المسموح
  final String? facingDirection; // الاتجاه (شمال، جنوب، شرق، غرب)
  final String? finishingType; // نوع التشطيب (سوبر لوكس، لوكس، عادي)

  // معلومات الإيجار (إذا كان للإيجار)
  final String? rentalPeriod; // فترة الإيجار (شهري، سنوي)
  final double? insuranceAmount; // مبلغ التأمين
  final double? commissionAmount; // مبلغ العمولة
  final String? paymentMethod; // طريقة الدفع (كاش، أقساط)
  final String? contractType; // نوع العقد (تمليك، إيجار)

  // معلومات تجارية إضافية
  final double? annualIncome; // الدخل السنوي
  final double? returnOnInvestment; // العائد على الاستثمار
  final String? tenantInfo; // معلومات المستأجر الحالي
  final DateTime? leaseEndDate; // تاريخ انتهاء عقد الإيجار الحالي
  final bool? isInvestmentProperty; // عقار استثماري

  const CommercialEstate({
    required super.id,
    required super.title,
    required super.description,
    required super.price,
    required super.location,
    required super.photoUrls,
    required super.isFeatured,
    required super.status,
    super.governorate,
    super.city,
    super.district,
    super.block,
    super.latitude,
    super.longitude,
    super.shareLocation,
    required super.mainCategory,
    super.subCategory,
    required super.ownerId,
    super.advertiserName,
    super.advertiserPhone,
    super.advertiserImage,
    super.advertiserType,
    super.advertiserJoinDate,
    super.advertiserAdsCount,
    super.hidePhone,
    super.extraPhones,
    required super.createdAt,
    super.updatedAt,
    super.startDate,
    super.endDate,
    super.viewsCount,
    super.favoritesCount,
    super.contactCount,
    super.subscriptionPlan,
    super.autoRepublish,
    super.isPinned,
    super.isPromoted,
    super.isVIP,
    super.isVerified,
    super.originalEstateId,
    super.isOriginal,
    super.copiedBy,
    super.floorPlanUrl,
    super.virtualTourUrl,
    super.videoUrl,
    super.documents,
    this.area,
    this.numberOfRooms,
    this.numberOfBathrooms,
    this.numberOfFloors,
    this.floorNumber,
    this.buildingAge,
    this.hasCentralAC = false,
    this.hasElevator = false,
    this.hasSecurity = false,
    this.hasParking = false,
    this.hasStorage = false,
    this.hasFireSystem = false,
    this.hasLoadingDock = false,
    this.hasShowroom = false,
    this.commercialType,
    this.streetWidth,
    this.isFurnished,
    this.hasWaterAndElectricity,
    this.licenseType,
    this.commercialActivity,
    this.facingDirection,
    this.finishingType,
    this.rentalPeriod,
    this.insuranceAmount,
    this.commissionAmount,
    this.paymentMethod,
    this.contractType,
    this.annualIncome,
    this.returnOnInvestment,
    this.tenantInfo,
    this.leaseEndDate,
    this.isInvestmentProperty,
  });

  @override
  String getEstateType() {
    return 'commercial';
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'location': location,
      'photoUrls': photoUrls,
      'isFeatured': isFeatured,
      'status': status,
      'governorate': governorate,
      'city': city,
      'district': district,
      'block': block,
      'latitude': latitude,
      'longitude': longitude,
      'shareLocation': shareLocation,
      'mainCategory': mainCategory,
      'subCategory': subCategory,
      'ownerId': ownerId,
      'advertiserName': advertiserName,
      'advertiserPhone': advertiserPhone,
      'advertiserImage': advertiserImage,
      'advertiserType': advertiserType,
      'advertiserJoinDate': EstateBase.dateTimeToTimestamp(advertiserJoinDate),
      'advertiserAdsCount': advertiserAdsCount,
      'hidePhone': hidePhone,
      'extraPhones': extraPhones,
      'createdAt': EstateBase.dateTimeToTimestamp(createdAt),
      'updatedAt': EstateBase.dateTimeToTimestamp(updatedAt),
      'startDate': EstateBase.dateTimeToTimestamp(startDate),
      'endDate': EstateBase.dateTimeToTimestamp(endDate),
      'viewsCount': viewsCount,
      'favoritesCount': favoritesCount,
      'contactCount': contactCount,
      'subscriptionPlan': subscriptionPlan,
      'autoRepublish': autoRepublish,
      'isPinned': isPinned,
      'isPromoted': isPromoted,
      'isVIP': isVIP,
      'isVerified': isVerified,
      'originalEstateId': originalEstateId,
      'isOriginal': isOriginal,
      'copiedBy': copiedBy,
      'floorPlanUrl': floorPlanUrl,
      'virtualTourUrl': virtualTourUrl,
      'videoUrl': videoUrl,
      'documents': documents,
      'estateType': 'commercial',
      'area': area,
      'numberOfRooms': numberOfRooms,
      'numberOfBathrooms': numberOfBathrooms,
      'numberOfFloors': numberOfFloors,
      'floorNumber': floorNumber,
      'buildingAge': buildingAge,
      'hasCentralAC': hasCentralAC,
      'hasElevator': hasElevator,
      'hasSecurity': hasSecurity,
      'hasParking': hasParking,
      'hasStorage': hasStorage,
      'hasFireSystem': hasFireSystem,
      'hasLoadingDock': hasLoadingDock,
      'hasShowroom': hasShowroom,
      'commercialType': commercialType,
      'streetWidth': streetWidth,
      'isFurnished': isFurnished,
      'hasWaterAndElectricity': hasWaterAndElectricity,
      'licenseType': licenseType,
      'commercialActivity': commercialActivity,
      'facingDirection': facingDirection,
      'finishingType': finishingType,
      'rentalPeriod': rentalPeriod,
      'insuranceAmount': insuranceAmount,
      'commissionAmount': commissionAmount,
      'paymentMethod': paymentMethod,
      'contractType': contractType,
      'annualIncome': annualIncome,
      'returnOnInvestment': returnOnInvestment,
      'tenantInfo': tenantInfo,
      'leaseEndDate': EstateBase.dateTimeToTimestamp(leaseEndDate),
      'isInvestmentProperty': isInvestmentProperty,
    };
  }

  @override
  CommercialEstate copyWithBase({
    String? id,
    String? title,
    String? description,
    double? price,
    String? location,
    List<String>? photoUrls,
    bool? isFeatured,
    String? status,
    String? governorate,
    String? city,
    String? district,
    String? block,
    double? latitude,
    double? longitude,
    bool? shareLocation,
    String? mainCategory,
    String? subCategory,
    String? ownerId,
    String? advertiserName,
    String? advertiserPhone,
    String? advertiserImage,
    String? advertiserType,
    DateTime? advertiserJoinDate,
    int? advertiserAdsCount,
    bool? hidePhone,
    List<String>? extraPhones,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? startDate,
    DateTime? endDate,
    int? viewsCount,
    int? favoritesCount,
    int? contactCount,
    String? subscriptionPlan,
    bool? autoRepublish,
    bool? isPinned,
    bool? isPromoted,
    bool? isVIP,
    bool? isVerified,
    bool? isPaymentVerified,
    String? originalEstateId,
    bool? isOriginal,
    List<String>? copiedBy,
    String? floorPlanUrl,
    String? virtualTourUrl,
    String? videoUrl,
    List<Map<String, dynamic>>? documents,
  }) {
    return copyWith(
      id: id,
      title: title,
      description: description,
      price: price,
      location: location,
      photoUrls: photoUrls,
      isFeatured: isFeatured,
      status: status,
      governorate: governorate,
      city: city,
      district: district,
      block: block,
      latitude: latitude,
      longitude: longitude,
      shareLocation: shareLocation,
      mainCategory: mainCategory,
      subCategory: subCategory,
      ownerId: ownerId,
      advertiserName: advertiserName,
      advertiserPhone: advertiserPhone,
      advertiserImage: advertiserImage,
      advertiserType: advertiserType,
      advertiserJoinDate: advertiserJoinDate,
      advertiserAdsCount: advertiserAdsCount,
      hidePhone: hidePhone,
      extraPhones: extraPhones,
      createdAt: createdAt,
      updatedAt: updatedAt,
      startDate: startDate,
      endDate: endDate,
      viewsCount: viewsCount,
      favoritesCount: favoritesCount,
      contactCount: contactCount,
      subscriptionPlan: subscriptionPlan,
      autoRepublish: autoRepublish,
      isPinned: isPinned,
      isPromoted: isPromoted,
      isVIP: isVIP,
      isVerified: isVerified,
      originalEstateId: originalEstateId,
      isOriginal: isOriginal,
      copiedBy: copiedBy,
      floorPlanUrl: floorPlanUrl,
      virtualTourUrl: virtualTourUrl,
      videoUrl: videoUrl,
      documents: documents);
  }

  /// إنشاء نسخة معدلة من العقار التجاري
  CommercialEstate copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    String? location,
    List<String>? photoUrls,
    bool? isFeatured,
    String? status,
    String? governorate,
    String? city,
    String? district,
    String? block,
    double? latitude,
    double? longitude,
    bool? shareLocation,
    String? mainCategory,
    String? subCategory,
    String? ownerId,
    String? advertiserName,
    String? advertiserPhone,
    String? advertiserImage,
    String? advertiserType,
    DateTime? advertiserJoinDate,
    int? advertiserAdsCount,
    bool? hidePhone,
    List<String>? extraPhones,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? startDate,
    DateTime? endDate,
    int? viewsCount,
    int? favoritesCount,
    int? contactCount,
    String? subscriptionPlan,
    bool? autoRepublish,
    bool? isPinned,
    bool? isPromoted,
    bool? isVIP,
    bool? isVerified,
    String? originalEstateId,
    bool? isOriginal,
    List<String>? copiedBy,
    String? floorPlanUrl,
    String? virtualTourUrl,
    String? videoUrl,
    List<Map<String, dynamic>>? documents,
    double? area,
    int? numberOfRooms,
    int? numberOfBathrooms,
    int? numberOfFloors,
    int? floorNumber,
    int? buildingAge,
    bool? hasCentralAC,
    bool? hasElevator,
    bool? hasSecurity,
    bool? hasParking,
    bool? hasStorage,
    bool? hasFireSystem,
    bool? hasLoadingDock,
    bool? hasShowroom,
    String? commercialType,
    String? streetWidth,
    bool? isFurnished,
    bool? hasWaterAndElectricity,
    String? licenseType,
    String? commercialActivity,
    String? facingDirection,
    String? finishingType,
    String? rentalPeriod,
    double? insuranceAmount,
    double? commissionAmount,
    String? paymentMethod,
    String? contractType,
    double? annualIncome,
    double? returnOnInvestment,
    String? tenantInfo,
    DateTime? leaseEndDate,
    bool? isInvestmentProperty,
  }) {
    return CommercialEstate(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      location: location ?? this.location,
      photoUrls: photoUrls ?? this.photoUrls,
      isFeatured: isFeatured ?? this.isFeatured,
      status: status ?? this.status,
      governorate: governorate ?? this.governorate,
      city: city ?? this.city,
      district: district ?? this.district,
      block: block ?? this.block,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      shareLocation: shareLocation ?? this.shareLocation,
      mainCategory: mainCategory ?? this.mainCategory,
      subCategory: subCategory ?? this.subCategory,
      ownerId: ownerId ?? this.ownerId,
      advertiserName: advertiserName ?? this.advertiserName,
      advertiserPhone: advertiserPhone ?? this.advertiserPhone,
      advertiserImage: advertiserImage ?? this.advertiserImage,
      advertiserType: advertiserType ?? this.advertiserType,
      advertiserJoinDate: advertiserJoinDate ?? this.advertiserJoinDate,
      advertiserAdsCount: advertiserAdsCount ?? this.advertiserAdsCount,
      hidePhone: hidePhone ?? this.hidePhone,
      extraPhones: extraPhones ?? this.extraPhones,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      viewsCount: viewsCount ?? this.viewsCount,
      favoritesCount: favoritesCount ?? this.favoritesCount,
      contactCount: contactCount ?? this.contactCount,
      subscriptionPlan: subscriptionPlan ?? this.subscriptionPlan,
      autoRepublish: autoRepublish ?? this.autoRepublish,
      isPinned: isPinned ?? this.isPinned,
      isPromoted: isPromoted ?? this.isPromoted,
      isVIP: isVIP ?? this.isVIP,
      isVerified: isVerified ?? this.isVerified,
      originalEstateId: originalEstateId ?? this.originalEstateId,
      isOriginal: isOriginal ?? this.isOriginal,
      copiedBy: copiedBy ?? this.copiedBy,
      floorPlanUrl: floorPlanUrl ?? this.floorPlanUrl,
      virtualTourUrl: virtualTourUrl ?? this.virtualTourUrl,
      videoUrl: videoUrl ?? this.videoUrl,
      documents: documents ?? this.documents,
      area: area ?? this.area,
      numberOfRooms: numberOfRooms ?? this.numberOfRooms,
      numberOfBathrooms: numberOfBathrooms ?? this.numberOfBathrooms,
      numberOfFloors: numberOfFloors ?? this.numberOfFloors,
      floorNumber: floorNumber ?? this.floorNumber,
      buildingAge: buildingAge ?? this.buildingAge,
      hasCentralAC: hasCentralAC ?? this.hasCentralAC,
      hasElevator: hasElevator ?? this.hasElevator,
      hasSecurity: hasSecurity ?? this.hasSecurity,
      hasParking: hasParking ?? this.hasParking,
      hasStorage: hasStorage ?? this.hasStorage,
      hasFireSystem: hasFireSystem ?? this.hasFireSystem,
      hasLoadingDock: hasLoadingDock ?? this.hasLoadingDock,
      hasShowroom: hasShowroom ?? this.hasShowroom,
      commercialType: commercialType ?? this.commercialType,
      streetWidth: streetWidth ?? this.streetWidth,
      isFurnished: isFurnished ?? this.isFurnished,
      hasWaterAndElectricity:
          hasWaterAndElectricity ?? this.hasWaterAndElectricity,
      licenseType: licenseType ?? this.licenseType,
      commercialActivity: commercialActivity ?? this.commercialActivity,
      facingDirection: facingDirection ?? this.facingDirection,
      finishingType: finishingType ?? this.finishingType,
      rentalPeriod: rentalPeriod ?? this.rentalPeriod,
      insuranceAmount: insuranceAmount ?? this.insuranceAmount,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      contractType: contractType ?? this.contractType,
      annualIncome: annualIncome ?? this.annualIncome,
      returnOnInvestment: returnOnInvestment ?? this.returnOnInvestment,
      tenantInfo: tenantInfo ?? this.tenantInfo,
      leaseEndDate: leaseEndDate ?? this.leaseEndDate,
      isInvestmentProperty: isInvestmentProperty ?? this.isInvestmentProperty);
  }

  /// التحقق من صحة بيانات العقار التجاري
  @override
  Map<String, String> validate() {
    final errors = super.validate();

    if (area != null && area! <= 0) {
      errors['area'] = 'يجب إدخال مساحة صحيحة';
    } else if (area != null && area! > 50000) {
      errors['area'] = 'المساحة كبيرة جداً، يرجى التحقق';
    }

    if (commercialType == null || commercialType!.isEmpty) {
      errors['commercialType'] = 'يجب تحديد نوع العقار التجاري';
    }

    if (contractType == null || contractType!.isEmpty) {
      errors['contractType'] = 'يجب تحديد نوع العقد';
    }

    return errors;
  }

  /// إنشاء عقار تجاري من Map
  factory CommercialEstate.fromMap(Map<String, dynamic> map) {
    return CommercialEstate(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      location: map['location'] ?? '',
      photoUrls: List<String>.from(map['photoUrls'] ?? []),
      isFeatured: map['isFeatured'] ?? false,
      status: map['status'] ?? 'متاح',
      governorate: map['governorate'],
      city: map['city'],
      district: map['district'],
      block: map['block'],
      latitude: map['latitude'],
      longitude: map['longitude'],
      shareLocation: map['shareLocation'] ?? false,
      mainCategory: map['mainCategory'] ?? 'commercial',
      subCategory: map['subCategory'],
      ownerId: map['ownerId'] ?? '',
      advertiserName: map['advertiserName'],
      advertiserPhone: map['advertiserPhone'],
      advertiserImage: map['advertiserImage'],
      advertiserType: map['advertiserType'],
      advertiserJoinDate:
          EstateBase.timestampToDateTime(map['advertiserJoinDate']),
      advertiserAdsCount: map['advertiserAdsCount'],
      hidePhone: map['hidePhone'] ?? false,
      extraPhones: List<String>.from(map['extraPhones'] ?? []),
      createdAt:
          EstateBase.timestampToDateTime(map['createdAt']) ?? DateTime.now(),
      updatedAt: EstateBase.timestampToDateTime(map['updatedAt']),
      startDate: EstateBase.timestampToDateTime(map['startDate']),
      endDate: EstateBase.timestampToDateTime(map['endDate']),
      viewsCount: map['viewsCount'] ?? 0,
      favoritesCount: map['favoritesCount'] ?? 0,
      contactCount: map['contactCount'] ?? 0,
      subscriptionPlan: map['subscriptionPlan'] ?? 'free',
      autoRepublish: map['autoRepublish'] ?? false,
      isPinned: map['isPinned'] ?? false,
      isPromoted: map['isPromoted'] ?? false,
      isVIP: map['isVIP'] ?? false,
      isVerified: map['isVerified'] ?? false,
      originalEstateId: map['originalEstateId'],
      isOriginal: map['isOriginal'] ?? true,
      copiedBy:
          map['copiedBy'] != null ? List<String>.from(map['copiedBy']) : null,
      floorPlanUrl: map['floorPlanUrl'],
      virtualTourUrl: map['virtualTourUrl'],
      videoUrl: map['videoUrl'],
      documents: map['documents'] != null
          ? List<Map<String, dynamic>>.from(map['documents'])
          : null,
      area: map['area'],
      numberOfRooms: map['numberOfRooms'],
      numberOfBathrooms: map['numberOfBathrooms'],
      numberOfFloors: map['numberOfFloors'],
      floorNumber: map['floorNumber'],
      buildingAge: map['buildingAge'],
      hasCentralAC: map['hasCentralAC'] ?? false,
      hasElevator: map['hasElevator'] ?? false,
      hasSecurity: map['hasSecurity'] ?? false,
      hasParking: map['hasParking'] ?? false,
      hasStorage: map['hasStorage'] ?? false,
      hasFireSystem: map['hasFireSystem'] ?? false,
      hasLoadingDock: map['hasLoadingDock'] ?? false,
      hasShowroom: map['hasShowroom'] ?? false,
      commercialType: map['commercialType'],
      streetWidth: map['streetWidth'],
      isFurnished: map['isFurnished'],
      hasWaterAndElectricity: map['hasWaterAndElectricity'],
      licenseType: map['licenseType'],
      commercialActivity: map['commercialActivity'],
      facingDirection: map['facingDirection'],
      finishingType: map['finishingType'],
      rentalPeriod: map['rentalPeriod'],
      insuranceAmount: map['insuranceAmount'],
      commissionAmount: map['commissionAmount'],
      paymentMethod: map['paymentMethod'],
      contractType: map['contractType'],
      annualIncome: map['annualIncome'],
      returnOnInvestment: map['returnOnInvestment'],
      tenantInfo: map['tenantInfo'],
      leaseEndDate: EstateBase.timestampToDateTime(map['leaseEndDate']),
      isInvestmentProperty: map['isInvestmentProperty']);
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        price,
        location,
        photoUrls,
        isFeatured,
        status,
        governorate,
        city,
        district,
        block,
        latitude,
        longitude,
        shareLocation,
        mainCategory,
        subCategory,
        ownerId,
        advertiserName,
        advertiserPhone,
        advertiserImage,
        advertiserType,
        advertiserJoinDate,
        advertiserAdsCount,
        hidePhone,
        extraPhones,
        createdAt,
        updatedAt,
        startDate,
        endDate,
        viewsCount,
        favoritesCount,
        contactCount,
        subscriptionPlan,
        autoRepublish,
        isPinned,
        isPromoted,
        isVIP,
        isVerified,
        originalEstateId,
        isOriginal,
        copiedBy,
        floorPlanUrl,
        virtualTourUrl,
        videoUrl,
        documents,
        area,
        numberOfRooms,
        numberOfBathrooms,
        numberOfFloors,
        floorNumber,
        buildingAge,
        hasCentralAC,
        hasElevator,
        hasSecurity,
        hasParking,
        hasStorage,
        hasFireSystem,
        hasLoadingDock,
        hasShowroom,
        commercialType,
        streetWidth,
        isFurnished,
        hasWaterAndElectricity,
        licenseType,
        commercialActivity,
        facingDirection,
        finishingType,
        rentalPeriod,
        insuranceAmount,
        commissionAmount,
        paymentMethod,
        contractType,
        annualIncome,
        returnOnInvestment,
        tenantInfo,
        leaseEndDate,
        isInvestmentProperty,
      ];
}
