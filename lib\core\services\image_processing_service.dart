import 'dart:io';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

/// خدمة لمعالجة الصور وضغطها قبل رفعها
class ImageProcessingService {
  /// ضغط صورة واحدة
  /// [imageFile] ملف الصورة
  /// [maxWidth] العرض الأقصى للصورة
  /// [quality] جودة الصورة (0-100)
  Future<File> compressImage({
    required File imageFile,
    int maxWidth = 1200,
    int quality = 80,
  }) async {
    try {
      // قراءة الصورة
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        return imageFile;
      }

      // تغيير حجم الصورة إذا كان عرضها أكبر من الحد الأقصى
      img.Image resizedImage;
      if (image.width > maxWidth) {
        resizedImage = img.copyResize(
          image,
          width: maxWidth,
          interpolation: img.Interpolation.linear);
      } else {
        resizedImage = image;
      }

      // ضغط الصورة
      final compressedBytes = img.encodeJpg(resizedImage, quality: quality);

      // حفظ الصورة المضغوطة في ملف مؤقت
      final tempDir = await getTemporaryDirectory();
      final uuid = const Uuid().v4();
      final tempFile = File('${tempDir.path}/$uuid.jpg');

      await tempFile.writeAsBytes(compressedBytes);

      return tempFile;
    } catch (e) {
      // في حالة حدوث خطأ، إرجاع الصورة الأصلية
      return imageFile;
    }
  }

  /// ضغط مجموعة من الصور
  /// [imageFiles] قائمة ملفات الصور
  /// [maxWidth] العرض الأقصى للصورة
  /// [quality] جودة الصورة (0-100)
  Future<List<File>> compressImages({
    required List<File> imageFiles,
    int maxWidth = 1200,
    int quality = 80,
  }) async {
    final compressedImages = <File>[];

    for (final imageFile in imageFiles) {
      final compressedImage = await compressImage(
        imageFile: imageFile,
        maxWidth: maxWidth,
        quality: quality);

      compressedImages.add(compressedImage);
    }

    return compressedImages;
  }

  /// التحقق من حجم الصورة
  /// [imageFile] ملف الصورة
  /// [maxSizeInMB] الحجم الأقصى بالميجابايت
  Future<bool> isImageSizeValid(File imageFile, double maxSizeInMB) async {
    final bytes = await imageFile.length();
    final sizeInMB = bytes / (1024 * 1024);

    return sizeInMB <= maxSizeInMB;
  }

  /// التحقق من أبعاد الصورة
  /// [imageFile] ملف الصورة
  /// [minWidth] العرض الأدنى للصورة
  /// [minHeight] الارتفاع الأدنى للصورة
  Future<bool> isImageDimensionsValid(
    File imageFile, {
    int minWidth = 300,
    int minHeight = 300,
  }) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        return false;
      }

      return image.width >= minWidth && image.height >= minHeight;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من نوع الصورة
  /// [imageFile] ملف الصورة
  /// [allowedExtensions] امتدادات الملفات المسموح بها
  bool isImageTypeValid(
    File imageFile, {
    List<String> allowedExtensions = const ['jpg', 'jpeg', 'png'],
  }) {
    final extension = imageFile.path.split('.').last.toLowerCase();
    return allowedExtensions.contains(extension);
  }
}
