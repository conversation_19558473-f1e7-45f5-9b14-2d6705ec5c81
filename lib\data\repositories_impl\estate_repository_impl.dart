// lib/data/repositories_impl/estate_repository_impl.dart
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/services/cache_service.dart';
import '../../core/services/connectivity_service.dart';
import '../../domain/entities/estate.dart';
import '../../domain/entities/estate_base.dart';
import '../../domain/entities/estate_document.dart';
import '../../domain/entities/virtual_tour.dart';
import '../../domain/repositories/estate_repository.dart';
import '../datasources/estate_remote_data_source.dart';
import '../models/estate_model.dart';
import '../../domain/entities/estate_converter.dart';

class EstateRepositoryImpl implements EstateRepository {
  final EstateRemoteDataSource remoteDataSource;
  final CacheService _cacheService = CacheService();
  final ConnectivityService _connectivityService = ConnectivityService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  EstateRepositoryImpl(this.remoteDataSource);

  @override
  Future<List<EstateBase>> getAllEstates() async {
    try {
      // التحقق من الاتصال بالإنترنت
      bool isConnected = await _connectivityService.isConnected();

      if (isConnected) {
        // إذا كان هناك اتصال بالإنترنت، نحصل على البيانات من المصدر البعيد
        final models = await remoteDataSource.getAllEstates();
        final estates = models.map((model) => model.toEntity()).toList();

        // تخزين البيانات في الكاش
        await _cacheEstates(estates);

        // تحويل Estate إلى EstateBase باستخدام EstateConverter
        return estates
            .map((estate) => EstateConverter.fromLegacyEstate(estate))
            .toList();
      } else {
        // إذا لم يكن هناك اتصال بالإنترنت، نحاول استرداد البيانات من الكاش
        final cachedEstates = await _getCachedEstates();
        return cachedEstates
            .map((estate) => EstateConverter.fromLegacyEstate(estate))
            .toList();
      }
    } catch (e) {
      // في حالة حدوث خطأ، نحاول استرداد البيانات من الكاش
      try {
        final cachedEstates = await _getCachedEstates();
        return cachedEstates
            .map((estate) => EstateConverter.fromLegacyEstate(estate))
            .toList();
      } catch (cacheError) {
        // إذا فشل استرداد البيانات من الكاش أيضًا، نعيد رمي الخطأ الأصلي
        throw e;
      }
    }
  }

  @override
  Future<List<Estate>> getAllEstatesLegacy() async {
    try {
      // التحقق من الاتصال بالإنترنت
      bool isConnected = await _connectivityService.isConnected();

      if (isConnected) {
        // إذا كان هناك اتصال بالإنترنت، نحصل على البيانات من المصدر البعيد
        final models = await remoteDataSource.getAllEstates();
        final estates = models.map((model) => model.toEntity()).toList();

        // تخزين البيانات في الكاش
        await _cacheEstates(estates);

        return estates;
      } else {
        // إذا لم يكن هناك اتصال بالإنترنت، نحاول استرداد البيانات من الكاش
        return await _getCachedEstates();
      }
    } catch (e) {
      // في حالة حدوث خطأ، نحاول استرداد البيانات من الكاش
      try {
        return await _getCachedEstates();
      } catch (cacheError) {
        // إذا فشل استرداد البيانات من الكاش أيضًا، نعيد رمي الخطأ الأصلي
        throw e;
      }
    }
  }

  @override
  Future<Map<String, dynamic>> getPaginatedEstates({
    int limit = 10,
    String? lastDocumentId,
    int page = 1,
    int pageSize = 10,
    String sortBy = "createdAt",
    bool sortAscending = false,
    Map<String, dynamic>? filters,
    String? searchQuery,
  }) async {
    try {
      // التحقق من الاتصال بالإنترنت
      bool isConnected = await _connectivityService.isConnected();

      if (isConnected) {
        // إذا كان هناك اتصال بالإنترنت، نحصل على البيانات من المصدر البعيد
        final result = await remoteDataSource.getPaginatedEstates(
          limit: limit,
          lastDocumentId: lastDocumentId,
          page: page,
          pageSize: pageSize,
          sortBy: sortBy,
          sortAscending: sortAscending,
          // تمرير الفلاتر إذا كانت متوفرة
          filters: filters,
          searchQuery: searchQuery);

        // تحويل نماذج EstateModel إلى كيانات Estate
        final estateModels = result['estates'] as List<EstateModel>;
        final estates = estateModels.map((model) => model.toEntity()).toList();

        // إذا كانت هذه هي الصفحة الأولى، نخزنها في الكاش
        if (lastDocumentId == null) {
          await _cacheEstates(estates);
        }

        // تحويل Estate إلى EstateBase باستخدام EstateConverter
        final estateBaseList = estates
            .map((estate) => EstateConverter.fromLegacyEstate(estate))
            .toList();

        // إرجاع النتائج بنفس الهيكل ولكن مع كيانات EstateBase بدلاً من نماذج EstateModel
        return {
          'estates': estateBaseList,
          'lastDocumentId': result['lastDocumentId'],
          'hasMore': result['hasMore'],
        };
      } else if (lastDocumentId == null) {
        // إذا لم يكن هناك اتصال بالإنترنت وهذه هي الصفحة الأولى، نحاول استرداد البيانات من الكاش
        final cachedEstates = await _getCachedEstates();
        final estateBaseList = cachedEstates
            .map((estate) => EstateConverter.fromLegacyEstate(estate))
            .toList();

        return {
          'estates': estateBaseList,
          'lastDocumentId': null,
          'hasMore': false,
        };
      } else {
        // إذا لم يكن هناك اتصال بالإنترنت وهذه ليست الصفحة الأولى، نعيد قائمة فارغة
        return {
          'estates': <EstateBase>[],
          'lastDocumentId': null,
          'hasMore': false,
        };
      }
    } catch (e) {
      // في حالة حدوث خطأ وهذه هي الصفحة الأولى، نحاول استرداد البيانات من الكاش
      if (lastDocumentId == null) {
        try {
          final cachedEstates = await _getCachedEstates();
          final estateBaseList = cachedEstates
              .map((estate) => EstateConverter.fromLegacyEstate(estate))
              .toList();

          return {
            'estates': estateBaseList,
            'lastDocumentId': null,
            'hasMore': false,
          };
        } catch (cacheError) {
          // إذا فشل استرداد البيانات من الكاش أيضًا، نعيد رمي الخطأ الأصلي
          throw e;
        }
      } else {
        // إذا حدث خطأ وهذه ليست الصفحة الأولى، نعيد رمي الخطأ
        rethrow;
      }
    }
  }

  @override
  Future<String> createEstate(EstateBase estate) async {
    print('🏗️ EstateRepositoryImpl: بدء إنشاء العقار...');

    // تحويل كيان EstateBase إلى Estate
    // نستخدم EstateConverter.toLegacyEstate لتحويل EstateBase إلى Estate
    print('🔄 تحويل EstateBase إلى Estate...');
    final legacyEstate = EstateConverter.toLegacyEstate(estate);
    if (legacyEstate == null) {
      print('❌ فشل تحويل العقار من EstateBase إلى Estate');
      throw Exception('فشل تحويل العقار');
    }
    print('✅ تم تحويل العقار بنجاح');

    // تحويل كيان Estate إلى نموذج EstateModel باستخدام fromEntity.
    print('🔄 تحويل Estate إلى EstateModel...');
    final model = EstateModel.fromEntity(legacyEstate);
    print('✅ تم تحويل Estate إلى EstateModel بنجاح');

    print('💾 إرسال البيانات إلى remoteDataSource...');
    await remoteDataSource.createEstate(model);
    print('✅ تم حفظ العقار في Firebase بنجاح');

    // بعد إنشاء العقار، نمسح الكاش لضمان تحديث البيانات في المرة القادمة
    print('🧹 مسح الكاش...');
    await _cacheService.clearCache();
    print('✅ تم مسح الكاش بنجاح');

    // إرجاع معرف العقار
    print('✅ تم إنشاء العقار بنجاح. معرف العقار: ${legacyEstate.id}');
    return legacyEstate.id;
  }

  @override
  Future<void> createEstateLegacy(Estate estate) async {
    // تحويل كيان Estate إلى نموذج EstateModel باستخدام fromEntity.
    final model = EstateModel.fromEntity(estate);
    await remoteDataSource.createEstate(model);

    // بعد إنشاء العقار، نمسح الكاش لضمان تحديث البيانات في المرة القادمة
    await _cacheService.clearCache();
  }

  @override
  Future<void> updateEstate(dynamic estate) async {
    // تحويل كيان EstateBase إلى Estate
    // نستخدم EstateConverter.toLegacyEstate لتحويل EstateBase إلى Estate
    final legacyEstate = EstateConverter.toLegacyEstate(estate);
    if (legacyEstate == null) {
      throw Exception('فشل تحويل العقار');
    }

    final model = EstateModel.fromEntity(legacyEstate);
    await remoteDataSource.updateEstate(model);

    // بعد تحديث العقار، نمسح الكاش لضمان تحديث البيانات في المرة القادمة
    await _cacheService.clearCache();
  }

  @override
  Future<void> updateEstateLegacy(Estate estate) async {
    final model = EstateModel.fromEntity(estate);
    await remoteDataSource.updateEstate(model);

    // بعد تحديث العقار، نمسح الكاش لضمان تحديث البيانات في المرة القادمة
    await _cacheService.clearCache();
  }

  @override
  Future<void> deleteEstate(String id) async {
    await remoteDataSource.deleteEstate(id);

    // بعد حذف العقار، نمسح الكاش لضمان تحديث البيانات في المرة القادمة
    await _cacheService.clearCache();
  }

  @override
  Future<List<String>> uploadImages(List<File> images,
      {String? estateId}) async {
    return await remoteDataSource.uploadImages(images);
  }

  @override
  Future<void> deleteImage(String imageUrl) async {
    // تنفيذ حذف الصورة
    // هذه الطريقة تحتاج إلى تنفيذ في المصدر البعيد
    throw UnimplementedError('deleteImage not implemented');
  }

  @override
  Future<EstateBase?> getEstateById(String id) async {
    try {
      // التحقق من الاتصال بالإنترنت
      bool isConnected = await _connectivityService.isConnected();

      if (isConnected) {
        // إذا كان هناك اتصال بالإنترنت، نحصل على البيانات من المصدر البعيد
        final model = await remoteDataSource.getEstateById(id);
        if (model == null) {
          return null;
        }

        // تحويل نموذج EstateModel إلى كيان Estate
        final estate = model.toEntity();

        // تحويل Estate إلى EstateBase باستخدام EstateConverter
        return EstateConverter.fromLegacyEstate(estate);
      } else {
        // إذا لم يكن هناك اتصال بالإنترنت، نحاول البحث في الكاش
        final cachedEstates = await _getCachedEstates();
        final foundEstate = cachedEstates.firstWhere(
          (estate) => estate.id == id,
          orElse: () => throw Exception('العقار غير موجود في الكاش'));

        // تحويل Estate إلى EstateBase باستخدام EstateConverter
        return EstateConverter.fromLegacyEstate(foundEstate);
      }
    } catch (e) {
      // تسجيل الخطأ (يمكن استخدام مكتبة تسجيل مناسبة في الإنتاج)
      // print('خطأ في الحصول على العقار: $e');
      return null;
    }
  }

  @override
  Future<int> getUserActiveAdsCount(String userId) async {
    // تنفيذ الحصول على عدد العقارات المدفوعة للمستخدم
    // هذه الطريقة تحتاج إلى تنفيذ في المصدر البعيد
    throw UnimplementedError('getUserActiveAdsCount not implemented');
  }

  @override
  Future<List<EstateBase>> searchEstates({
    String? query,
    String? mainCategory,
    String? subCategory,
    double? minPrice,
    double? maxPrice,
    String? location,
    Map<String, dynamic>? filters,
  }) async {
    // تنفيذ البحث عن العقارات
    throw UnimplementedError('searchEstates not implemented');
  }

  @override
  Future<List<EstateBase>> getFeaturedEstates({int limit = 10}) async {
    // تنفيذ الحصول على العقارات المميزة
    throw UnimplementedError('getFeaturedEstates not implemented');
  }

  @override
  Future<List<EstateBase>> getMostViewedEstates({int limit = 10}) async {
    // تنفيذ الحصول على العقارات الأكثر مشاهدة
    throw UnimplementedError('getMostViewedEstates not implemented');
  }

  @override
  Future<List<EstateBase>> getLatestEstates({int limit = 10}) async {
    // تنفيذ الحصول على العقارات الأحدث
    throw UnimplementedError('getLatestEstates not implemented');
  }

  @override
  Future<List<EstateBase>> getUserEstates(String userId) async {
    // تنفيذ الحصول على عقارات المستخدم
    throw UnimplementedError('getUserEstates not implemented');
  }

  @override
  Future<void> incrementEstateViews(String estateId) async {
    // تنفيذ زيادة عدد مشاهدات العقار
    throw UnimplementedError('incrementEstateViews not implemented');
  }

  @override
  Future<void> addEstateToFavorites(String estateId, String userId) async {
    // تنفيذ إضافة عقار إلى المفضلة
    throw UnimplementedError('addEstateToFavorites not implemented');
  }

  @override
  Future<void> removeEstateFromFavorites(String estateId, String userId) async {
    // تنفيذ إزالة عقار من المفضلة
    throw UnimplementedError('removeEstateFromFavorites not implemented');
  }

  @override
  Future<List<EstateBase>> getUserFavoriteEstates(String userId) async {
    // تنفيذ الحصول على العقارات المفضلة للمستخدم
    throw UnimplementedError('getUserFavoriteEstates not implemented');
  }

  @override
  Future<bool> isEstateInFavorites(String estateId, String userId) async {
    // تنفيذ التحقق مما إذا كان العقار في المفضلة
    throw UnimplementedError('isEstateInFavorites not implemented');
  }

  @override
  Future<String> addEstateDocument(EstateDocument document, File file) async {
    // تنفيذ إضافة مستند للعقار
    throw UnimplementedError('addEstateDocument not implemented');
  }

  @override
  Future<void> deleteEstateDocument(String documentId) async {
    // تنفيذ حذف مستند من العقار
    throw UnimplementedError('deleteEstateDocument not implemented');
  }

  @override
  Future<List<EstateDocument>> getEstateDocuments(String estateId,
      {bool publicOnly = false}) async {
    // تنفيذ الحصول على مستندات العقار
    throw UnimplementedError('getEstateDocuments not implemented');
  }

  @override
  Future<String> addVirtualTour(VirtualTour tour) async {
    try {
      final docRef = await _firestore.collection('virtualTours').add(tour.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('فشل في إضافة الجولة الافتراضية: $e');
    }
  }

  @override
  Future<void> updateVirtualTour(VirtualTour tour) async {
    try {
      await _firestore.collection('virtualTours').doc(tour.id).update(tour.toMap());
    } catch (e) {
      throw Exception('فشل في تحديث الجولة الافتراضية: $e');
    }
  }

  @override
  Future<void> deleteVirtualTour(String tourId) async {
    try {
      await _firestore.collection('virtualTours').doc(tourId).delete();
    } catch (e) {
      throw Exception('فشل في حذف الجولة الافتراضية: $e');
    }
  }

  @override
  Future<List<VirtualTour>> getEstateVirtualTours(String estateId) async {
    try {
      final snapshot = await _firestore
          .collection('virtualTours')
          .where('estateId', isEqualTo: estateId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => VirtualTour.fromSnapshot(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الجولات الافتراضية: $e');
    }
  }

  @override
  Future<void> incrementVirtualTourViews(String tourId) async {
    try {
      await _firestore.collection('virtualTours').doc(tourId).update({
        'viewsCount': FieldValue.increment(1),
      });
    } catch (e) {
      throw Exception('فشل في تحديث عدد المشاهدات: $e');
    }
  }

  @override
  Future<String> copyEstate(String estateId, String userId) async {
    // تنفيذ نسخ عقار
    throw UnimplementedError('copyEstate not implemented');
  }

  @override
  Future<Map<String, dynamic>> getEstateStatistics(String estateId) async {
    // تنفيذ الحصول على إحصائيات العقار
    throw UnimplementedError('getEstateStatistics not implemented');
  }

  @override
  Future<void> updateEstateStatus(String estateId, String status) async {
    // تنفيذ تحديث حالة العقار
    throw UnimplementedError('updateEstateStatus not implemented');
  }

  @override
  Future<void> promoteEstate(
    String estateId, {
    bool isVIP = false,
    bool isPinned = false,
    bool isPromoted = false,
  }) async {
    // تنفيذ تمييز العقار
    throw UnimplementedError('promoteEstate not implemented');
  }

  // تخزين العقارات في الكاش
  Future<void> _cacheEstates(List<Estate> estates) async {
    final estatesJson = estates.map((estate) => _estateToJson(estate)).toList();
    await _cacheService.cacheEstates(estatesJson);
  }

  // استرداد العقارات من الكاش
  Future<List<Estate>> _getCachedEstates() async {
    final cachedData = await _cacheService.getCachedEstates();
    if (cachedData == null || cachedData.isEmpty) {
      return [];
    }

    return cachedData.map((json) => _estateFromJson(json)).toList();
  }

  // تحويل Estate إلى Map<String, dynamic>
  Map<String, dynamic> _estateToJson(Estate estate) {
    return {
      'id': estate.id,
      'title': estate.title,
      'description': estate.description,
      'price': estate.price,
      'location': estate.location,
      'photoUrls': estate.photoUrls,
      'isFeatured': estate.isFeatured,
      'planType': estate.planType,
      'startDate': estate.startDate?.toIso8601String(),
      'endDate': estate.endDate?.toIso8601String(),
      'createdAt': estate.createdAt.toIso8601String(),
      'mainCategory': estate.mainCategory,
      'subCategory': estate.subCategory,
      'postedByUserType': estate.postedByUserType,
      'hidePhone': estate.hidePhone,
      'extraPhones': estate.extraPhones,
      'shareLocation': estate.shareLocation,
      'lat': estate.lat,
      'lng': estate.lng,
      'hasCentralAC': estate.hasCentralAC,
      'hasSecurity': estate.hasSecurity,
      'allowPets': estate.allowPets,
      'hasElevator': estate.hasElevator,
      'hasSwimmingPool': estate.hasSwimmingPool,
      'hasMaidRoom': estate.hasMaidRoom,
      'hasGarage': estate.hasGarage,
      'hasBalcony': estate.hasBalcony,
      'isFullyFurnished': estate.isFullyFurnished,
      'rebound': estate.rebound,
      'numberOfRooms': estate.numberOfRooms,
      'internalLocation': estate.internalLocation,
      'salon': estate.salon,
      'area': estate.area,
      'floorNumber': estate.floorNumber,
      'numberOfBathrooms': estate.numberOfBathrooms,
      'buildingAge': estate.buildingAge,
      'numberOfFloors': estate.numberOfFloors,
      'propertyType': estate.propertyType,
      'autoRepublish': estate.autoRepublish,
      'kuwaitCornersPin': estate.kuwaitCornersPin,
      'movingAd': estate.movingAd,
      'vipBadge': estate.vipBadge,
      'pinnedOnHome': estate.pinnedOnHome,
      'discountCode': estate.discountCode,
      'advertiserImage': estate.advertiserImage,
      'advertiserName': estate.advertiserName,
      'advertiserEmail': estate.advertiserEmail,
      'advertiserRegistrationDate':
          estate.advertiserRegistrationDate?.toIso8601String(),
      'advertiserAdsCount': estate.advertiserAdsCount,
      'ownerId': estate.ownerId,
      'originalEstateId': estate.originalEstateId,
      'isOriginal': estate.isOriginal,
      'copiedBy': estate.copiedBy,
    };
  }

  // تحويل Map<String, dynamic> إلى Estate
  Estate _estateFromJson(Map<String, dynamic> json) {
    return Estate(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      location: json['location'] as String,
      photoUrls:
          (json['photoUrls'] as List<dynamic>).map((e) => e as String).toList(),
      isFeatured: json['isFeatured'] as bool,
      planType: json['planType'] as String,
      startDate: json['startDate'] != null
          ? DateTime.parse(json['startDate'] as String)
          : null,
      endDate: json['endDate'] != null
          ? DateTime.parse(json['endDate'] as String)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      mainCategory: json['mainCategory'] as String?,
      subCategory: json['subCategory'] as String?,
      postedByUserType: json['postedByUserType'] as String?,
      hidePhone: json['hidePhone'] as bool? ?? false,
      extraPhones: json['extraPhones'] != null
          ? (json['extraPhones'] as List<dynamic>)
              .map((e) => e as String)
              .toList()
          : const [],
      shareLocation: json['shareLocation'] as bool? ?? false,
      lat: json['lat'] != null ? (json['lat'] as num).toDouble() : null,
      lng: json['lng'] != null ? (json['lng'] as num).toDouble() : null,
      hasCentralAC: json['hasCentralAC'] as bool? ?? false,
      hasSecurity: json['hasSecurity'] as bool?,
      allowPets: json['allowPets'] as bool?,
      hasElevator: json['hasElevator'] as bool?,
      hasSwimmingPool: json['hasSwimmingPool'] as bool?,
      hasMaidRoom: json['hasMaidRoom'] as bool? ?? false,
      hasGarage: json['hasGarage'] as bool? ?? false,
      hasBalcony: json['hasBalcony'] as bool?,
      isFullyFurnished: json['isFullyFurnished'] as bool?,
      rebound: json['rebound'] as String?,
      numberOfRooms: json['numberOfRooms'] as int?,
      internalLocation: json['internalLocation'] as String?,
      salon: json['salon'] as String?,
      area: json['area'] != null ? (json['area'] as num).toDouble() : null,
      floorNumber: json['floorNumber'] as int?,
      numberOfBathrooms: json['numberOfBathrooms'] as int?,
      buildingAge: json['buildingAge'] as int?,
      numberOfFloors: json['numberOfFloors'] as int?,
      propertyType: json['propertyType'] as String?,
      autoRepublish: json['autoRepublish'] as bool? ?? false,
      kuwaitCornersPin: json['kuwaitCornersPin'] as bool? ?? false,
      movingAd: json['movingAd'] as bool? ?? false,
      vipBadge: json['vipBadge'] as bool? ?? false,
      pinnedOnHome: json['pinnedOnHome'] as bool? ?? false,
      discountCode: json['discountCode'] as String?,
      advertiserImage: json['advertiserImage'] as String?,
      advertiserName: json['advertiserName'] as String?,
      advertiserEmail: json['advertiserEmail'] as String?,
      advertiserRegistrationDate: json['advertiserRegistrationDate'] != null
          ? DateTime.parse(json['advertiserRegistrationDate'] as String)
          : null,
      advertiserAdsCount: json['advertiserAdsCount'] as int?,
      ownerId: json['ownerId'] as String?,
      originalEstateId: json['originalEstateId'] as String?,
      isOriginal: json['isOriginal'] as bool? ?? true,
      copiedBy: json['copiedBy'] != null
          ? (json['copiedBy'] as List<dynamic>).map((e) => e as String).toList()
          : const []);
  }
}
