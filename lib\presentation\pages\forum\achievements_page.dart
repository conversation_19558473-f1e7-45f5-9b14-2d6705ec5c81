import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/user_statistics_model.dart';
import '../../../domain/models/forum/badge_model.dart';
import '../../../presentation/providers/auth_provider.dart' as app_auth;
import '../../../presentation/providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/empty_view.dart';
import '../../widgets/forum/achievement_card.dart';
import '../../widgets/forum/badge_card.dart';

/// صفحة الإنجازات
class AchievementsPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/achievements';

  /// معرف المستخدم
  final String? userId;

  const AchievementsPage({
    super.key,
    this.userId,
  });

  @override
  State<AchievementsPage> createState() => _AchievementsPageState();
}

class _AchievementsPageState extends State<AchievementsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  UserStatisticsModel? _statistics;
  String _error = '';
  AchievementFilterType _filterType = AchievementFilterType.all;
  AchievementSortType _sortType = AchievementSortType.completion;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
      
      final userId = widget.userId ?? authProvider.user?.uid;
      if (userId == null) {
        setState(() {
          _error = 'يجب تسجيل الدخول أولاً';
          _isLoading = false;
        });
        return;
      }
      
      // تحميل إحصائيات المستخدم
      final statistics = await forumProvider.getUserStatistics(userId);
      
      setState(() {
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ أثناء تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإنجازات والشارات'),
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: Colors.grey.shade600,
          indicatorColor: AppColors.primary,
          tabs: const [
            Tab(text: 'الإنجازات'),
            Tab(text: 'الشارات'),
          ])),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _error.isNotEmpty
              ? ErrorView(
                  message: _error,
                  onRetry: _loadData)
              : _statistics == null
                  ? const Center(child: Text('لا توجد بيانات'))
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildAchievementsTab(),
                        _buildBadgesTab(),
                      ]));
  }

  /// بناء تبويب الإنجازات
  Widget _buildAchievementsTab() {
    final achievements = _getFilteredAchievements();
    
    if (achievements.isEmpty) {
      return const EmptyView(
        message: 'لا توجد إنجازات بعد',
        icon: Icons.emoji_events_outlined);
    }
    
    return Column(
      children: [
        // شريط التصفية والترتيب
        _buildFilterBar(),
        
        // قائمة الإنجازات
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16),
            itemCount: achievements.length,
            itemBuilder: (context, index) {
              final achievement = achievements[index];
              return AchievementCard(
                achievement: achievement,
                size: AchievementCardSize.medium,
                onTap: () => _showAchievementDetails(achievement));
            })),
      ]);
  }

  /// بناء تبويب الشارات
  Widget _buildBadgesTab() {
    final badges = _getBadges();
    
    if (badges.isEmpty) {
      return const EmptyView(
        message: 'لا توجد شارات بعد',
        icon: Icons.shield_outlined);
    }
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16),
      itemCount: badges.length,
      itemBuilder: (context, index) {
        final badge = badges[index];
        final earnedAchievement = _getAchievementForBadge(badge.id);
        
        return BadgeCard(
          badge: badge,
          size: BadgeCardSize.medium,
          isLocked: earnedAchievement == null,
          earnedDate: earnedAchievement?.earnedAt,
          onTap: () => _showBadgeDetails(badge, earnedAchievement));
      });
  }

  /// بناء شريط التصفية والترتيب
  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.grey.shade50,
      child: Row(
        children: [
          // تصفية الإنجازات
          Expanded(
            child: DropdownButtonFormField<AchievementFilterType>(
              value: _filterType,
              decoration: const InputDecoration(
                isDense: true,
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                border: OutlineInputBorder(),
                labelText: 'تصفية'),
              items: [
                DropdownMenuItem(
                  value: AchievementFilterType.all,
                  child: Text(_getFilterTypeName(AchievementFilterType.all))),
                DropdownMenuItem(
                  value: AchievementFilterType.completed,
                  child: Text(_getFilterTypeName(AchievementFilterType.completed))),
                DropdownMenuItem(
                  value: AchievementFilterType.inProgress,
                  child: Text(_getFilterTypeName(AchievementFilterType.inProgress))),
                DropdownMenuItem(
                  value: AchievementFilterType.level,
                  child: Text(_getFilterTypeName(AchievementFilterType.level))),
                DropdownMenuItem(
                  value: AchievementFilterType.posts,
                  child: Text(_getFilterTypeName(AchievementFilterType.posts))),
                DropdownMenuItem(
                  value: AchievementFilterType.topics,
                  child: Text(_getFilterTypeName(AchievementFilterType.topics))),
                DropdownMenuItem(
                  value: AchievementFilterType.likes,
                  child: Text(_getFilterTypeName(AchievementFilterType.likes))),
                DropdownMenuItem(
                  value: AchievementFilterType.streak,
                  child: Text(_getFilterTypeName(AchievementFilterType.streak))),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _filterType = value;
                  });
                }
              })),
          const SizedBox(width: 16),
          
          // ترتيب الإنجازات
          Expanded(
            child: DropdownButtonFormField<AchievementSortType>(
              value: _sortType,
              decoration: const InputDecoration(
                isDense: true,
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                border: OutlineInputBorder(),
                labelText: 'ترتيب'),
              items: [
                DropdownMenuItem(
                  value: AchievementSortType.completion,
                  child: Text(_getSortTypeName(AchievementSortType.completion))),
                DropdownMenuItem(
                  value: AchievementSortType.progress,
                  child: Text(_getSortTypeName(AchievementSortType.progress))),
                DropdownMenuItem(
                  value: AchievementSortType.name,
                  child: Text(_getSortTypeName(AchievementSortType.name))),
                DropdownMenuItem(
                  value: AchievementSortType.date,
                  child: Text(_getSortTypeName(AchievementSortType.date))),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _sortType = value;
                  });
                }
              })),
        ]));
  }

  /// الحصول على الإنجازات المصفاة
  List<AchievementModel> _getFilteredAchievements() {
    if (_statistics == null) {
      return [];
    }
    
    // تصفية الإنجازات
    List<AchievementModel> filteredAchievements = List.from(_statistics!.achievements);
    
    switch (_filterType) {
      case AchievementFilterType.completed:
        filteredAchievements = filteredAchievements
            .where((achievement) => achievement.isCompleted)
            .toList();
        break;
      case AchievementFilterType.inProgress:
        filteredAchievements = filteredAchievements
            .where((achievement) => !achievement.isCompleted && achievement.value > 0)
            .toList();
        break;
      case AchievementFilterType.level:
        filteredAchievements = filteredAchievements
            .where((achievement) => achievement.type == 'level')
            .toList();
        break;
      case AchievementFilterType.posts:
        filteredAchievements = filteredAchievements
            .where((achievement) => achievement.type == 'posts')
            .toList();
        break;
      case AchievementFilterType.topics:
        filteredAchievements = filteredAchievements
            .where((achievement) => achievement.type == 'topics')
            .toList();
        break;
      case AchievementFilterType.likes:
        filteredAchievements = filteredAchievements
            .where((achievement) => achievement.type == 'likes')
            .toList();
        break;
      case AchievementFilterType.streak:
        filteredAchievements = filteredAchievements
            .where((achievement) => achievement.type == 'streak')
            .toList();
        break;
      case AchievementFilterType.all:
      default:
        // لا تصفية
        break;
    }
    
    // ترتيب الإنجازات
    switch (_sortType) {
      case AchievementSortType.completion:
        filteredAchievements.sort((a, b) {
          if (a.isCompleted && !b.isCompleted) return -1;
          if (!a.isCompleted && b.isCompleted) return 1;
          return b.earnedAt.compareTo(a.earnedAt);
        });
        break;
      case AchievementSortType.progress:
        filteredAchievements.sort((a, b) {
          final aProgress = a.value / a.maxValue;
          final bProgress = b.value / b.maxValue;
          return bProgress.compareTo(aProgress);
        });
        break;
      case AchievementSortType.name:
        filteredAchievements.sort((a, b) => a.name.compareTo(b.name));
        break;
      case AchievementSortType.date:
        filteredAchievements.sort((a, b) => b.earnedAt.compareTo(a.earnedAt));
        break;
    }
    
    return filteredAchievements;
  }

  /// الحصول على الشارات
  List<BadgeModel> _getBadges() {
    if (_statistics == null) {
      return [];
    }
    
    // الحصول على معرفات الإنجازات المكتملة
    final completedAchievementIds = _statistics!.achievements
        .where((achievement) => achievement.isCompleted)
        .map((achievement) => achievement.id)
        .toSet();
    
    // الحصول على الشارات المقابلة للإنجازات المكتملة
    final earnedBadges = BadgeModel.badges
        .where((badge) => completedAchievementIds.contains(badge.id))
        .toList();
    
    // إضافة بعض الشارات الأخرى للعرض
    final otherBadges = BadgeModel.badges
        .where((badge) => !completedAchievementIds.contains(badge.id))
        .take(10)
        .toList();
    
    return [...earnedBadges, ...otherBadges];
  }

  /// الحصول على الإنجاز المقابل للشارة
  AchievementModel? _getAchievementForBadge(String badgeId) {
    if (_statistics == null) {
      return null;
    }
    
    try {
      return _statistics!.achievements
          .firstWhere((achievement) => achievement.id == badgeId && achievement.isCompleted);
    } catch (e) {
      return null;
    }
  }

  /// عرض تفاصيل الإنجاز
  void _showAchievementDetails(AchievementModel achievement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(achievement.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AchievementCard(
              achievement: achievement,
              size: AchievementCardSize.large),
          ]),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق')),
        ]));
  }

  /// عرض تفاصيل الشارة
  void _showBadgeDetails(BadgeModel badge, AchievementModel? achievement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(badge.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BadgeCard(
              badge: badge,
              size: BadgeCardSize.large,
              isLocked: achievement == null,
              earnedDate: achievement?.earnedAt),
            if (achievement == null) ...[
              const SizedBox(height: 16),
              Text(
                'كيفية الحصول على هذه الشارة:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800)),
              const SizedBox(height: 8),
              Text(
                badge.description,
                style: TextStyle(
                  color: Colors.grey.shade700)),
            ],
          ]),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق')),
        ]));
  }

  /// الحصول على اسم نوع التصفية
  String _getFilterTypeName(AchievementFilterType type) {
    switch (type) {
      case AchievementFilterType.all:
        return 'الكل';
      case AchievementFilterType.completed:
        return 'المكتملة';
      case AchievementFilterType.inProgress:
        return 'قيد التقدم';
      case AchievementFilterType.level:
        return 'المستوى';
      case AchievementFilterType.posts:
        return 'المشاركات';
      case AchievementFilterType.topics:
        return 'المواضيع';
      case AchievementFilterType.likes:
        return 'الإعجابات';
      case AchievementFilterType.streak:
        return 'النشاط';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على اسم نوع الترتيب
  String _getSortTypeName(AchievementSortType type) {
    switch (type) {
      case AchievementSortType.completion:
        return 'الإكمال';
      case AchievementSortType.progress:
        return 'التقدم';
      case AchievementSortType.name:
        return 'الاسم';
      case AchievementSortType.date:
        return 'التاريخ';
      default:
        return 'غير معروف';
    }
  }
}

/// نوع تصفية الإنجازات
enum AchievementFilterType {
  /// الكل
  all,

  /// المكتملة
  completed,

  /// قيد التقدم
  inProgress,

  /// المستوى
  level,

  /// المشاركات
  posts,

  /// المواضيع
  topics,

  /// الإعجابات
  likes,

  /// النشاط
  streak,
}

/// نوع ترتيب الإنجازات
enum AchievementSortType {
  /// الإكمال
  completion,

  /// التقدم
  progress,

  /// الاسم
  name,

  /// التاريخ
  date,
}
