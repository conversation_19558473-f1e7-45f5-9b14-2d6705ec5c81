// lib/presentation/widgets/property_request/property_type_selector.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

// import '../../../core/constants/app_colors.dart';

/// محدد نوع العقار
class PropertyTypeSelector extends StatelessWidget {
  /// النوع المحدد
  final String selectedType;

  /// دالة عند اختيار نوع
  final ValueChanged<String> onTypeSelected;

  const PropertyTypeSelector({
    super.key,
    required this.selectedType,
    required this.onTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final propertyTypes = [
      {'type': 'شقة', 'icon': Icons.apartment},
      {'type': 'منزل', 'icon': Icons.home},
      {'type': 'بيت', 'icon': Icons.house},
      {'type': 'أرض', 'icon': Icons.landscape},
      {'type': 'مكتب', 'icon': Icons.business},
      {'type': 'محل تجاري', 'icon': Icons.storefront},
      {'type': 'مخزن', 'icon': Icons.warehouse},
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: propertyTypes.map((type) {
        final isSelected = selectedType == type['type'];

        return InkWell(
          onTap: () => onTypeSelected(type['type'] as String),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: 100,
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 12),
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue.withOpacity(0.1) : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? Colors.blue : Colors.grey.shade300,
                width: 1)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  type['icon'] as IconData,
                  color: isSelected ? Colors.blue : Colors.grey.shade600,
                  size: 24),
                const SizedBox(height: 8),
                Text(
                  type['type'] as String,
                  style: GoogleFonts.cairo(
                    color: isSelected ? Colors.blue : Colors.grey.shade800,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 14),
                  textAlign: TextAlign.center),
              ])));
      }).toList());
  }
}
