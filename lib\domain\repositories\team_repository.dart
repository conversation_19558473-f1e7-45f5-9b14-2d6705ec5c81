import 'dart:io';
import '../entities/team_member.dart';

/// واجهة مستودع إدارة الفريق
abstract class TeamRepository {
  /// إنشاء عضو فريق جديد
  Future<String> createTeamMember(TeamMember teamMember);
  
  /// تحديث عضو فريق موجود
  Future<void> updateTeamMember(TeamMember teamMember);
  
  /// حذف عضو فريق
  Future<void> deleteTeamMember(String teamMemberId);
  
  /// الحصول على عضو فريق بواسطة المعرف
  Future<TeamMember?> getTeamMemberById(String teamMemberId);
  
  /// الحصول على عضو فريق بواسطة معرف المستخدم
  Future<TeamMember?> getTeamMemberByUserId(String userId);
  
  /// الحصول على أعضاء فريق الشركة
  Future<List<TeamMember>> getCompanyTeamMembers(String companyId);
  
  /// الحصول على أعضاء فريق الشركة بالتحميل المتدرج
  /// [companyId] معرف الشركة
  /// [limit] عدد الأعضاء في كل صفحة
  /// [lastTeamMemberId] معرف آخر عضو تم تحميله (للصفحات التالية)
  /// [role] دور الأعضاء المطلوبين
  /// [status] حالة الأعضاء المطلوبين
  /// [query] نص البحث
  /// يعيد Map تحتوي على:
  /// - 'teamMembers': قائمة الأعضاء
  /// - 'lastTeamMemberId': معرف آخر عضو (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من الأعضاء
  Future<Map<String, dynamic>> getCompanyTeamMembersPaginated({
    required String companyId,
    int limit = 20,
    String? lastTeamMemberId,
    TeamMemberRole? role,
    TeamMemberStatus? status,
    String? query,
  });
  
  /// الحصول على أعضاء الفريق تحت إشراف مشرف معين
  Future<List<TeamMember>> getTeamMembersUnderSupervisor(String supervisorId);
  
  /// تحديث حالة عضو الفريق
  Future<void> updateTeamMemberStatus(String teamMemberId, TeamMemberStatus status);
  
  /// تحديث دور عضو الفريق
  Future<void> updateTeamMemberRole(String teamMemberId, TeamMemberRole role);
  
  /// تحديث المشرف المباشر لعضو الفريق
  Future<void> updateTeamMemberSupervisor(String teamMemberId, String supervisorId, String supervisorName);
  
  /// إضافة منطقة مسؤول عنها لعضو الفريق
  Future<void> addTeamMemberAssignedArea(String teamMemberId, String area);
  
  /// إزالة منطقة مسؤول عنها من عضو الفريق
  Future<void> removeTeamMemberAssignedArea(String teamMemberId, String area);
  
  /// إضافة نوع عقار مسؤول عنه لعضو الفريق
  Future<void> addTeamMemberAssignedPropertyType(String teamMemberId, String propertyType);
  
  /// إضافة عميل مسؤول عنه لعضو الفريق
  Future<void> addTeamMemberAssignedClient(String teamMemberId, String clientId);
  
  /// إضافة عقار مسؤول عنه لعضو الفريق
  Future<void> addTeamMemberAssignedEstate(String teamMemberId, String estateId);
  
  /// تحديث هدف المبيعات الشهري لعضو الفريق
  Future<void> updateTeamMemberMonthlySalesTarget(String teamMemberId, double target);
  
  /// تحديث نسبة العمولة لعضو الفريق
  Future<void> updateTeamMemberCommissionRate(String teamMemberId, double rate);
  
  /// إضافة مبيعات لعضو الفريق
  Future<void> addTeamMemberSale(String teamMemberId, double amount);
  
  /// تحميل صورة عضو الفريق
  Future<String> uploadTeamMemberImage(String teamMemberId, File image);
  
  /// الحصول على إحصائيات أعضاء الفريق
  Future<Map<String, dynamic>> getTeamMembersStatistics(String companyId);
  
  /// الحصول على أفضل أعضاء الفريق أداءً
  Future<List<TeamMember>> getTopPerformingTeamMembers(String companyId, {int limit = 5});
  
  /// الحصول على تقرير أداء عضو الفريق
  Future<Map<String, dynamic>> getTeamMemberPerformanceReport(String teamMemberId);
  
  /// الحصول على تقرير أداء الفريق
  Future<Map<String, dynamic>> getTeamPerformanceReport(String companyId);
  
  /// إرسال إشعار لعضو الفريق
  Future<void> sendTeamMemberNotification(String teamMemberId, String title, String body);
  
  /// إرسال إشعار لجميع أعضاء الفريق
  Future<void> sendTeamNotification(String companyId, String title, String body);
}
