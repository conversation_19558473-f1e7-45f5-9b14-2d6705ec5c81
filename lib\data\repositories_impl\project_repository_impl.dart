import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/project.dart';
import '../../domain/entities/project_task.dart';
import '../../domain/entities/project_milestone.dart';
import '../../domain/entities/project_document.dart';
import '../../domain/entities/team_member.dart';
import '../../domain/repositories/project_repository.dart';

/// تطبيق مستودع المشاريع باستخدام Firestore
class ProjectRepositoryImpl implements ProjectRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final FirebaseStorage _storage;
  final Uuid _uuid;

  ProjectRepositoryImpl({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
    FirebaseStorage? storage,
    Uuid? uuid,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance,
        _storage = storage ?? FirebaseStorage.instance,
        _uuid = uuid ?? const Uuid();

  @override
  Future<String> createProject(Project project) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لإنشاء مشروع');
      }

      final projectId = _uuid.v4();
      final newProject = project.copyWith(
        id: projectId,
        companyId: user.uid,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('projects')
          .doc(projectId)
          .set(newProject.toMap());

      return projectId;
    } catch (e) {
      throw Exception('فشل في إنشاء المشروع: $e');
    }
  }

  @override
  Future<void> updateProject(Project project) async {
    try {
      final updatedProject = project.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('projects')
          .doc(project.id)
          .update(updatedProject.toMap());
    } catch (e) {
      throw Exception('فشل في تحديث المشروع: $e');
    }
  }

  @override
  Future<void> deleteProject(String projectId) async {
    try {
      // حذف المشروع والمجموعات الفرعية
      final batch = _firestore.batch();
      
      // حذف المشروع الرئيسي
      batch.delete(_firestore.collection('projects').doc(projectId));
      
      // حذف المهام
      final tasks = await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('tasks')
          .get();
      for (final task in tasks.docs) {
        batch.delete(task.reference);
      }
      
      // حذف المعالم
      final milestones = await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('milestones')
          .get();
      for (final milestone in milestones.docs) {
        batch.delete(milestone.reference);
      }
      
      // حذف الوثائق
      final documents = await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('documents')
          .get();
      for (final document in documents.docs) {
        batch.delete(document.reference);
      }

      await batch.commit();
    } catch (e) {
      throw Exception('فشل في حذف المشروع: $e');
    }
  }

  @override
  Future<Project?> getProjectById(String projectId) async {
    try {
      final doc = await _firestore
          .collection('projects')
          .doc(projectId)
          .get();

      if (doc.exists) {
        return Project.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في الحصول على المشروع: $e');
    }
  }

  @override
  Future<List<Project>> getCompanyProjects(String companyId) async {
    try {
      final querySnapshot = await _firestore
          .collection('projects')
          .where('companyId', isEqualTo: companyId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Project.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على مشاريع الشركة: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getCompanyProjectsPaginated({
    required String companyId,
    int limit = 20,
    String? lastProjectId,
    ProjectStatus? status,
    String? query,
  }) async {
    try {
      Query projectsQuery = _firestore
          .collection('projects')
          .where('companyId', isEqualTo: companyId);

      // تطبيق فلتر الحالة
      if (status != null) {
        projectsQuery = projectsQuery.where('status', 
            isEqualTo: status.toString().split('.').last);
      }

      // تطبيق البحث النصي
      if (query != null && query.isNotEmpty) {
        projectsQuery = projectsQuery
            .where('name', isGreaterThanOrEqualTo: query)
            .where('name', isLessThan: '$query\uf8ff');
      }

      projectsQuery = projectsQuery.orderBy('createdAt', descending: true);

      // تطبيق التحميل المتدرج
      if (lastProjectId != null) {
        final lastDoc = await _firestore
            .collection('projects')
            .doc(lastProjectId)
            .get();
        if (lastDoc.exists) {
          projectsQuery = projectsQuery.startAfterDocument(lastDoc);
        }
      }

      projectsQuery = projectsQuery.limit(limit);

      final querySnapshot = await projectsQuery.get();
      final projects = querySnapshot.docs
          .map((doc) => Project.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      return {
        'projects': projects,
        'lastProjectId': projects.isNotEmpty ? projects.last.id : null,
        'hasMore': querySnapshot.docs.length == limit,
      };
    } catch (e) {
      throw Exception('فشل في الحصول على المشاريع: $e');
    }
  }

  @override
  Future<List<Project>> searchProjects({
    required String companyId,
    String? query,
    ProjectStatus? status,
    ProjectType? type,
    ProjectPriority? priority,
  }) async {
    try {
      Query projectsQuery = _firestore
          .collection('projects')
          .where('companyId', isEqualTo: companyId);

      if (status != null) {
        projectsQuery = projectsQuery.where('status', 
            isEqualTo: status.toString().split('.').last);
      }

      if (type != null) {
        projectsQuery = projectsQuery.where('type', 
            isEqualTo: type.toString().split('.').last);
      }

      if (priority != null) {
        projectsQuery = projectsQuery.where('priority', 
            isEqualTo: priority.toString().split('.').last);
      }

      if (query != null && query.isNotEmpty) {
        projectsQuery = projectsQuery
            .where('name', isGreaterThanOrEqualTo: query)
            .where('name', isLessThan: '$query\uf8ff');
      }

      final querySnapshot = await projectsQuery
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Project.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('فشل في البحث عن المشاريع: $e');
    }
  }

  @override
  Future<List<Project>> getUserProjects(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('projects')
          .where('memberIds', arrayContains: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Project.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على مشاريع المستخدم: $e');
    }
  }

  @override
  Future<String> addProjectTask(String projectId, ProjectTask task) async {
    try {
      final taskId = _uuid.v4();
      final newTask = task.copyWith(
        id: taskId,
        projectId: projectId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('tasks')
          .doc(taskId)
          .set(newTask.toMap());

      // تحديث عداد المهام في المشروع
      await _firestore
          .collection('projects')
          .doc(projectId)
          .update({
        'tasksCount': FieldValue.increment(1),
        'taskIds': FieldValue.arrayUnion([taskId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return taskId;
    } catch (e) {
      throw Exception('فشل في إضافة المهمة: $e');
    }
  }

  @override
  Future<void> updateProjectTask(ProjectTask task) async {
    try {
      final updatedTask = task.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('projects')
          .doc(task.projectId)
          .collection('tasks')
          .doc(task.id)
          .update(updatedTask.toMap());
    } catch (e) {
      throw Exception('فشل في تحديث المهمة: $e');
    }
  }

  @override
  Future<void> deleteProjectTask(String projectId, String taskId) async {
    try {
      await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('tasks')
          .doc(taskId)
          .delete();

      // تحديث عداد المهام في المشروع
      await _firestore
          .collection('projects')
          .doc(projectId)
          .update({
        'tasksCount': FieldValue.increment(-1),
        'taskIds': FieldValue.arrayRemove([taskId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في حذف المهمة: $e');
    }
  }

  @override
  Future<List<ProjectTask>> getProjectTasks(String projectId) async {
    try {
      final querySnapshot = await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('tasks')
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ProjectTask.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على مهام المشروع: $e');
    }
  }

  @override
  Future<ProjectTask?> getTaskById(String taskId) async {
    try {
      // البحث في جميع المشاريع عن المهمة
      final projectsSnapshot = await _firestore
          .collection('projects')
          .get();

      for (final projectDoc in projectsSnapshot.docs) {
        final taskDoc = await _firestore
            .collection('projects')
            .doc(projectDoc.id)
            .collection('tasks')
            .doc(taskId)
            .get();

        if (taskDoc.exists) {
          return ProjectTask.fromMap(taskDoc.data()!);
        }
      }

      return null;
    } catch (e) {
      throw Exception('فشل في الحصول على المهمة: $e');
    }
  }

  @override
  Future<void> updateTaskStatus(String taskId, TaskStatus status) async {
    try {
      // البحث عن المهمة وتحديث حالتها
      final projectsSnapshot = await _firestore
          .collection('projects')
          .get();

      for (final projectDoc in projectsSnapshot.docs) {
        final taskDoc = await _firestore
            .collection('projects')
            .doc(projectDoc.id)
            .collection('tasks')
            .doc(taskId)
            .get();

        if (taskDoc.exists) {
          await taskDoc.reference.update({
            'status': status.toString().split('.').last,
            'updatedAt': FieldValue.serverTimestamp(),
            if (status == TaskStatus.completed)
              'completedDate': FieldValue.serverTimestamp(),
          });

          // تحديث عداد المهام المكتملة في المشروع
          if (status == TaskStatus.completed) {
            await _firestore
                .collection('projects')
                .doc(projectDoc.id)
                .update({
              'completedTasks': FieldValue.increment(1),
              'updatedAt': FieldValue.serverTimestamp(),
            });
          }
          break;
        }
      }
    } catch (e) {
      throw Exception('فشل في تحديث حالة المهمة: $e');
    }
  }

  @override
  Future<void> updateTaskProgress(String taskId, double progress) async {
    try {
      final projectsSnapshot = await _firestore
          .collection('projects')
          .get();

      for (final projectDoc in projectsSnapshot.docs) {
        final taskDoc = await _firestore
            .collection('projects')
            .doc(projectDoc.id)
            .collection('tasks')
            .doc(taskId)
            .get();

        if (taskDoc.exists) {
          await taskDoc.reference.update({
            'progress': progress,
            'updatedAt': FieldValue.serverTimestamp(),
            if (progress >= 100)
              'status': TaskStatus.completed.toString().split('.').last,
            if (progress >= 100)
              'completedDate': FieldValue.serverTimestamp(),
          });
          break;
        }
      }
    } catch (e) {
      throw Exception('فشل في تحديث تقدم المهمة: $e');
    }
  }

  @override
  Future<void> assignTaskToMember(String taskId, String memberId, String memberName) async {
    try {
      final projectsSnapshot = await _firestore
          .collection('projects')
          .get();

      for (final projectDoc in projectsSnapshot.docs) {
        final taskDoc = await _firestore
            .collection('projects')
            .doc(projectDoc.id)
            .collection('tasks')
            .doc(taskId)
            .get();

        if (taskDoc.exists) {
          await taskDoc.reference.update({
            'assignedToId': memberId,
            'assignedToName': memberName,
            'updatedAt': FieldValue.serverTimestamp(),
          });
          break;
        }
      }
    } catch (e) {
      throw Exception('فشل في تعيين المهمة: $e');
    }
  }

  @override
  Future<String> addProjectMilestone(String projectId, ProjectMilestone milestone) async {
    try {
      final milestoneId = _uuid.v4();
      final newMilestone = milestone.copyWith(
        id: milestoneId,
        projectId: projectId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('milestones')
          .doc(milestoneId)
          .set(newMilestone.toMap());

      // تحديث قائمة المعالم في المشروع
      await _firestore
          .collection('projects')
          .doc(projectId)
          .update({
        'milestoneIds': FieldValue.arrayUnion([milestoneId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return milestoneId;
    } catch (e) {
      throw Exception('فشل في إضافة المعلم: $e');
    }
  }

  @override
  Future<void> updateProjectMilestone(ProjectMilestone milestone) async {
    try {
      final updatedMilestone = milestone.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('projects')
          .doc(milestone.projectId)
          .collection('milestones')
          .doc(milestone.id)
          .update(updatedMilestone.toMap());
    } catch (e) {
      throw Exception('فشل في تحديث المعلم: $e');
    }
  }

  @override
  Future<void> deleteProjectMilestone(String projectId, String milestoneId) async {
    try {
      await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('milestones')
          .doc(milestoneId)
          .delete();

      // تحديث قائمة المعالم في المشروع
      await _firestore
          .collection('projects')
          .doc(projectId)
          .update({
        'milestoneIds': FieldValue.arrayRemove([milestoneId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في حذف المعلم: $e');
    }
  }

  @override
  Future<List<ProjectMilestone>> getProjectMilestones(String projectId) async {
    try {
      final querySnapshot = await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('milestones')
          .orderBy('dueDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => ProjectMilestone.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على معالم المشروع: $e');
    }
  }

  @override
  Future<ProjectMilestone?> getMilestoneById(String milestoneId) async {
    try {
      final projectsSnapshot = await _firestore
          .collection('projects')
          .get();

      for (final projectDoc in projectsSnapshot.docs) {
        final milestoneDoc = await _firestore
            .collection('projects')
            .doc(projectDoc.id)
            .collection('milestones')
            .doc(milestoneId)
            .get();

        if (milestoneDoc.exists) {
          return ProjectMilestone.fromMap(milestoneDoc.data()!);
        }
      }

      return null;
    } catch (e) {
      throw Exception('فشل في الحصول على المعلم: $e');
    }
  }

  @override
  Future<void> updateMilestoneStatus(String milestoneId, MilestoneStatus status) async {
    try {
      final projectsSnapshot = await _firestore
          .collection('projects')
          .get();

      for (final projectDoc in projectsSnapshot.docs) {
        final milestoneDoc = await _firestore
            .collection('projects')
            .doc(projectDoc.id)
            .collection('milestones')
            .doc(milestoneId)
            .get();

        if (milestoneDoc.exists) {
          await milestoneDoc.reference.update({
            'status': status.toString().split('.').last,
            'updatedAt': FieldValue.serverTimestamp(),
            if (status == MilestoneStatus.completed)
              'completedDate': FieldValue.serverTimestamp(),
          });
          break;
        }
      }
    } catch (e) {
      throw Exception('فشل في تحديث حالة المعلم: $e');
    }
  }

  @override
  Future<String> addProjectDocument(String projectId, ProjectDocument document, File file) async {
    try {
      final documentId = _uuid.v4();

      // رفع الملف إلى Firebase Storage
      final fileUrl = await _uploadFile(file, 'projects/$projectId/documents/$documentId');

      final newDocument = document.copyWith(
        id: documentId,
        projectId: projectId,
        fileUrl: fileUrl,
        uploadedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('documents')
          .doc(documentId)
          .set(newDocument.toMap());

      // تحديث قائمة الوثائق في المشروع
      await _firestore
          .collection('projects')
          .doc(projectId)
          .update({
        'documentIds': FieldValue.arrayUnion([documentId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return documentId;
    } catch (e) {
      throw Exception('فشل في إضافة الوثيقة: $e');
    }
  }

  @override
  Future<void> updateProjectDocument(ProjectDocument document, {File? newFile}) async {
    try {
      String fileUrl = document.fileUrl;

      if (newFile != null) {
        fileUrl = await _uploadFile(newFile, 'projects/${document.projectId}/documents/${document.id}');
      }

      final updatedDocument = document.copyWith(
        fileUrl: fileUrl,
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('projects')
          .doc(document.projectId)
          .collection('documents')
          .doc(document.id)
          .update(updatedDocument.toMap());
    } catch (e) {
      throw Exception('فشل في تحديث الوثيقة: $e');
    }
  }

  @override
  Future<void> deleteProjectDocument(String projectId, String documentId) async {
    try {
      // حذف الوثيقة من Firestore
      await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('documents')
          .doc(documentId)
          .delete();

      // تحديث قائمة الوثائق في المشروع
      await _firestore
          .collection('projects')
          .doc(projectId)
          .update({
        'documentIds': FieldValue.arrayRemove([documentId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في حذف الوثيقة: $e');
    }
  }

  @override
  Future<List<ProjectDocument>> getProjectDocuments(String projectId) async {
    try {
      final querySnapshot = await _firestore
          .collection('projects')
          .doc(projectId)
          .collection('documents')
          .orderBy('uploadedAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ProjectDocument.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على وثائق المشروع: $e');
    }
  }

  @override
  Future<ProjectDocument?> getDocumentById(String documentId) async {
    try {
      final projectsSnapshot = await _firestore
          .collection('projects')
          .get();

      for (final projectDoc in projectsSnapshot.docs) {
        final documentDoc = await _firestore
            .collection('projects')
            .doc(projectDoc.id)
            .collection('documents')
            .doc(documentId)
            .get();

        if (documentDoc.exists) {
          return ProjectDocument.fromMap(documentDoc.data()!);
        }
      }

      return null;
    } catch (e) {
      throw Exception('فشل في الحصول على الوثيقة: $e');
    }
  }

  @override
  Future<String> uploadDocumentFile(File file, String projectId, String documentType) async {
    try {
      return await _uploadFile(file, 'projects/$projectId/documents/$documentType');
    } catch (e) {
      throw Exception('فشل في رفع الملف: $e');
    }
  }

  // وظائف مساعدة
  Future<String> _uploadFile(File file, String path) async {
    try {
      final ref = _storage.ref().child(path);
      final uploadTask = await ref.putFile(file);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      throw Exception('فشل في رفع الملف: $e');
    }
  }

  // تطبيق باقي الوظائف المطلوبة (مبسطة)
  @override
  Future<void> addProjectMember(String projectId, String memberId) async {
    try {
      await _firestore
          .collection('projects')
          .doc(projectId)
          .update({
        'memberIds': FieldValue.arrayUnion([memberId]),
        'teamSize': FieldValue.increment(1),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في إضافة عضو للمشروع: $e');
    }
  }

  @override
  Future<void> removeProjectMember(String projectId, String memberId) async {
    try {
      await _firestore
          .collection('projects')
          .doc(projectId)
          .update({
        'memberIds': FieldValue.arrayRemove([memberId]),
        'teamSize': FieldValue.increment(-1),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في إزالة عضو من المشروع: $e');
    }
  }

  @override
  Future<List<TeamMember>> getProjectMembers(String projectId) async {
    try {
      final project = await getProjectById(projectId);
      if (project == null) return [];

      final List<TeamMember> members = [];
      for (final memberId in project.memberIds) {
        final memberDoc = await _firestore
            .collection('team_members')
            .doc(memberId)
            .get();

        if (memberDoc.exists) {
          members.add(TeamMember.fromMap(memberDoc.data()!));
        }
      }

      return members;
    } catch (e) {
      throw Exception('فشل في الحصول على أعضاء المشروع: $e');
    }
  }

  @override
  Future<void> updateMemberRole(String projectId, String memberId, String role) async {
    try {
      await _firestore
          .collection('team_members')
          .doc(memberId)
          .update({
        'role': role,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في تحديث دور العضو: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getProjectStatistics(String projectId) async {
    try {
      final project = await getProjectById(projectId);
      if (project == null) return {};

      final tasks = await getProjectTasks(projectId);
      final milestones = await getProjectMilestones(projectId);
      final documents = await getProjectDocuments(projectId);

      return {
        'totalTasks': tasks.length,
        'completedTasks': tasks.where((t) => t.status == TaskStatus.completed).length,
        'overdueTasks': tasks.where((t) => t.isOverdue()).length,
        'totalMilestones': milestones.length,
        'completedMilestones': milestones.where((m) => m.status == MilestoneStatus.completed).length,
        'overdueMilestones': milestones.where((m) => m.isOverdue()).length,
        'totalDocuments': documents.length,
        'progress': project.progress,
        'budget': project.budget,
        'spentBudget': project.spentBudget,
        'teamSize': project.teamSize,
      };
    } catch (e) {
      throw Exception('فشل في الحصول على إحصائيات المشروع: $e');
    }
  }

  // تطبيق باقي الوظائف المطلوبة (مبسطة)
  @override
  Future<Map<String, dynamic>> getCompanyProjectsStatistics(String companyId) async {
    try {
      final projects = await getCompanyProjects(companyId);

      return {
        'totalProjects': projects.length,
        'activeProjects': projects.where((p) => p.status == ProjectStatus.active).length,
        'completedProjects': projects.where((p) => p.status == ProjectStatus.completed).length,
        'totalBudget': projects.fold(0.0, (total, p) => total + p.budget),
        'totalSpent': projects.fold(0.0, (total, p) => total + p.spentBudget),
        'averageProgress': projects.isEmpty ? 0.0 :
            projects.fold(0.0, (total, p) => total + p.progress) / projects.length,
      };
    } catch (e) {
      throw Exception('فشل في الحصول على إحصائيات مشاريع الشركة: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getProjectProgressReport(String projectId) async {
    try {
      final stats = await getProjectStatistics(projectId);
      return {
        'projectId': projectId,
        'reportType': 'progress',
        'generatedAt': DateTime.now().toIso8601String(),
        'data': stats,
      };
    } catch (e) {
      throw Exception('فشل في إنشاء تقرير التقدم: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getProjectBudgetReport(String projectId) async {
    try {
      final project = await getProjectById(projectId);
      if (project == null) return {};

      return {
        'projectId': projectId,
        'reportType': 'budget',
        'generatedAt': DateTime.now().toIso8601String(),
        'budget': project.budget,
        'spentBudget': project.spentBudget,
        'remainingBudget': project.budget - project.spentBudget,
        'budgetUtilization': project.budget > 0 ?
            (project.spentBudget / project.budget) * 100 : 0,
      };
    } catch (e) {
      throw Exception('فشل في إنشاء تقرير الميزانية: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getProjectPerformanceReport(String projectId) async {
    try {
      final stats = await getProjectStatistics(projectId);
      final project = await getProjectById(projectId);

      return {
        'projectId': projectId,
        'reportType': 'performance',
        'generatedAt': DateTime.now().toIso8601String(),
        'efficiency': stats['totalTasks'] > 0 ?
            (stats['completedTasks'] / stats['totalTasks']) * 100 : 0,
        'onTimeDelivery': stats['overdueTasks'] == 0,
        'budgetPerformance': project?.budget != null && project!.budget > 0 ?
            (project.spentBudget / project.budget) * 100 : 0,
        'overallScore': project?.progress ?? 0,
      };
    } catch (e) {
      throw Exception('فشل في إنشاء تقرير الأداء: $e');
    }
  }

  @override
  Future<List<ProjectTask>> getOverdueTasks(String projectId) async {
    try {
      final tasks = await getProjectTasks(projectId);
      return tasks.where((task) => task.isOverdue()).toList();
    } catch (e) {
      throw Exception('فشل في الحصول على المهام المتأخرة: $e');
    }
  }

  @override
  Future<List<ProjectTask>> getUpcomingTasks(String projectId, {int daysAhead = 7}) async {
    try {
      final tasks = await getProjectTasks(projectId);
      return tasks.where((task) => task.isDueSoon(daysThreshold: daysAhead)).toList();
    } catch (e) {
      throw Exception('فشل في الحصول على المهام القادمة: $e');
    }
  }

  @override
  Future<List<ProjectMilestone>> getOverdueMilestones(String projectId) async {
    try {
      final milestones = await getProjectMilestones(projectId);
      return milestones.where((milestone) => milestone.isOverdue()).toList();
    } catch (e) {
      throw Exception('فشل في الحصول على المعالم المتأخرة: $e');
    }
  }

  @override
  Future<List<ProjectMilestone>> getUpcomingMilestones(String projectId, {int daysAhead = 7}) async {
    try {
      final milestones = await getProjectMilestones(projectId);
      return milestones.where((milestone) => milestone.isDueSoon(daysThreshold: daysAhead)).toList();
    } catch (e) {
      throw Exception('فشل في الحصول على المعالم القادمة: $e');
    }
  }

  // تطبيق باقي الوظائف (مبسطة)
  @override
  Future<void> sendTaskReminders(String projectId) async {
    // تطبيق مبسط - يمكن تطويره لاحقاً
    try {
      final overdueTasks = await getOverdueTasks(projectId);
      // إرسال إشعارات للمهام المتأخرة
      for (final task in overdueTasks) {
        // منطق إرسال التذكيرات
      }
    } catch (e) {
      throw Exception('فشل في إرسال تذكيرات المهام: $e');
    }
  }

  @override
  Future<void> sendMilestoneReminders(String projectId) async {
    // تطبيق مبسط - يمكن تطويره لاحقاً
    try {
      final overdueMilestones = await getOverdueMilestones(projectId);
      // إرسال إشعارات للمعالم المتأخرة
      for (final milestone in overdueMilestones) {
        // منطق إرسال التذكيرات
      }
    } catch (e) {
      throw Exception('فشل في إرسال تذكيرات المعالم: $e');
    }
  }

  @override
  Future<String> exportProjectData(String projectId, String format) async {
    // تطبيق مبسط - يمكن تطويره لاحقاً
    try {
      final project = await getProjectById(projectId);
      final stats = await getProjectStatistics(projectId);

      // إنشاء ملف التصدير
      final exportData = {
        'project': project?.toMap(),
        'statistics': stats,
        'exportedAt': DateTime.now().toIso8601String(),
        'format': format,
      };

      // حفظ البيانات وإرجاع مسار الملف
      return 'exports/project_${projectId}_${DateTime.now().millisecondsSinceEpoch}.$format';
    } catch (e) {
      throw Exception('فشل في تصدير بيانات المشروع: $e');
    }
  }

  @override
  Future<int> importTasksFromFile(String projectId, File file) async {
    // تطبيق مبسط - يمكن تطويره لاحقاً
    try {
      // قراءة الملف وتحليل البيانات
      // إنشاء المهام من البيانات المستوردة
      return 0; // عدد المهام المستوردة
    } catch (e) {
      throw Exception('فشل في استيراد المهام: $e');
    }
  }

  @override
  Future<String> exportProjectReport(String projectId, String reportType) async {
    try {
      Map<String, dynamic> reportData;

      switch (reportType) {
        case 'progress':
          reportData = await getProjectProgressReport(projectId);
          break;
        case 'budget':
          reportData = await getProjectBudgetReport(projectId);
          break;
        case 'performance':
          reportData = await getProjectPerformanceReport(projectId);
          break;
        default:
          reportData = await getProjectStatistics(projectId);
      }

      // إنشاء ملف التقرير
      return 'reports/${reportType}_${projectId}_${DateTime.now().millisecondsSinceEpoch}.pdf';
    } catch (e) {
      throw Exception('فشل في تصدير التقرير: $e');
    }
  }

  // Streams للاستماع للتغييرات
  @override
  Stream<Project?> listenToProject(String projectId) {
    return _firestore
        .collection('projects')
        .doc(projectId)
        .snapshots()
        .map((snapshot) {
      if (snapshot.exists) {
        return Project.fromMap(snapshot.data()!);
      }
      return null;
    });
  }

  @override
  Stream<List<ProjectTask>> listenToProjectTasks(String projectId) {
    return _firestore
        .collection('projects')
        .doc(projectId)
        .collection('tasks')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ProjectTask.fromMap(doc.data()))
            .toList());
  }

  @override
  Stream<List<ProjectMilestone>> listenToProjectMilestones(String projectId) {
    return _firestore
        .collection('projects')
        .doc(projectId)
        .collection('milestones')
        .orderBy('dueDate', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ProjectMilestone.fromMap(doc.data()))
            .toList());
  }

  @override
  Stream<List<ProjectDocument>> listenToProjectDocuments(String projectId) {
    return _firestore
        .collection('projects')
        .doc(projectId)
        .collection('documents')
        .orderBy('uploadedAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ProjectDocument.fromMap(doc.data()))
            .toList());
  }
}
