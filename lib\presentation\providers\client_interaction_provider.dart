import 'package:flutter/foundation.dart';
import '../../domain/entities/client_interaction.dart';
import '../../domain/entities/appointment.dart';
import '../../domain/repositories/client_interaction_repository.dart';

/// مزود حالة التفاعلات مع العملاء
class ClientInteractionProvider extends ChangeNotifier {
  final ClientInteractionRepository _interactionRepository;

  ClientInteractionProvider({required ClientInteractionRepository interactionRepository})
      : _interactionRepository = interactionRepository;

  // الحالة
  bool _isLoading = false;
  String? _error;
  List<ClientInteraction> _interactions = [];
  List<Appointment> _appointments = [];
  List<ClientInteraction> _followUpReminders = [];
  Map<String, dynamic> _interactionStatistics = {};
  Map<String, dynamic> _performanceReport = {};

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<ClientInteraction> get interactions => _interactions;
  List<Appointment> get appointments => _appointments;
  List<ClientInteraction> get followUpReminders => _followUpReminders;
  Map<String, dynamic> get interactionStatistics => _interactionStatistics;
  Map<String, dynamic> get performanceReport => _performanceReport;

  /// تحميل تفاعلات الوكيل
  Future<void> loadAgentInteractions(String agentId) async {
    _setLoading(true);
    try {
      _interactions = await _interactionRepository.getAgentInteractions(agentId);
      _clearError();
    } catch (e) {
      _setError('فشل في تحميل التفاعلات: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل تفاعلات الوكيل بالتحميل المتدرج
  Future<Map<String, dynamic>> loadAgentInteractionsPaginated({
    required String agentId,
    int limit = 20,
    String? lastInteractionId,
    InteractionType? type,
    InteractionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      return await _interactionRepository.getAgentInteractionsPaginated(
        agentId: agentId,
        limit: limit,
        lastInteractionId: lastInteractionId,
        type: type,
        status: status,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      _setError('فشل في تحميل التفاعلات: $e');
      return {'interactions': [], 'hasMore': false};
    }
  }

  /// البحث عن التفاعلات
  Future<void> searchInteractions({
    required String agentId,
    String? query,
    InteractionType? type,
    InteractionStatus? status,
    InteractionOutcome? outcome,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    _setLoading(true);
    try {
      _interactions = await _interactionRepository.searchInteractions(
        agentId: agentId,
        query: query,
        type: type,
        status: status,
        outcome: outcome,
        startDate: startDate,
        endDate: endDate,
      );
      _clearError();
    } catch (e) {
      _setError('فشل في البحث عن التفاعلات: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء تفاعل جديد
  Future<String?> createInteraction(ClientInteraction interaction) async {
    _setLoading(true);
    try {
      final interactionId = await _interactionRepository.createInteraction(interaction);
      await loadAgentInteractions(interaction.agentId);
      _clearError();
      return interactionId;
    } catch (e) {
      _setError('فشل في إنشاء التفاعل: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث تفاعل
  Future<bool> updateInteraction(ClientInteraction interaction) async {
    _setLoading(true);
    try {
      await _interactionRepository.updateInteraction(interaction);
      await loadAgentInteractions(interaction.agentId);
      _clearError();
      return true;
    } catch (e) {
      _setError('فشل في تحديث التفاعل: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف تفاعل
  Future<bool> deleteInteraction(String interactionId, String agentId) async {
    _setLoading(true);
    try {
      await _interactionRepository.deleteInteraction(interactionId);
      await loadAgentInteractions(agentId);
      _clearError();
      return true;
    } catch (e) {
      _setError('فشل في حذف التفاعل: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل مواعيد الوكيل
  Future<void> loadAgentAppointments(String agentId) async {
    _setLoading(true);
    try {
      _appointments = await _interactionRepository.getAgentAppointments(agentId);
      _clearError();
    } catch (e) {
      _setError('فشل في تحميل المواعيد: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء موعد جديد
  Future<String?> createAppointment(Appointment appointment) async {
    try {
      final appointmentId = await _interactionRepository.createAppointment(appointment);
      await loadAgentAppointments(appointment.agentId);
      _clearError();
      return appointmentId;
    } catch (e) {
      _setError('فشل في إنشاء الموعد: $e');
      return null;
    }
  }

  /// تحديث موعد
  Future<bool> updateAppointment(Appointment appointment) async {
    try {
      await _interactionRepository.updateAppointment(appointment);
      await loadAgentAppointments(appointment.agentId);
      _clearError();
      return true;
    } catch (e) {
      _setError('فشل في تحديث الموعد: $e');
      return false;
    }
  }

  /// الحصول على مواعيد اليوم
  Future<List<Appointment>> getTodayAppointments(String agentId) async {
    try {
      return await _interactionRepository.getTodayAppointments(agentId);
    } catch (e) {
      _setError('فشل في الحصول على مواعيد اليوم: $e');
      return [];
    }
  }

  /// الحصول على المواعيد القادمة
  Future<List<Appointment>> getUpcomingAppointments(String agentId, {int daysAhead = 7}) async {
    try {
      return await _interactionRepository.getUpcomingAppointments(agentId, daysAhead: daysAhead);
    } catch (e) {
      _setError('فشل في الحصول على المواعيد القادمة: $e');
      return [];
    }
  }

  /// تحميل تذكيرات المتابعة
  Future<void> loadFollowUpReminders(String agentId) async {
    try {
      _followUpReminders = await _interactionRepository.getFollowUpReminders(agentId);
      _clearError();
    } catch (e) {
      _setError('فشل في تحميل تذكيرات المتابعة: $e');
    }
  }

  /// إضافة تذكير متابعة
  Future<bool> addFollowUpReminder(String clientId, DateTime followUpDate, String notes) async {
    try {
      await _interactionRepository.addFollowUpReminder(clientId, followUpDate, notes);
      _clearError();
      return true;
    } catch (e) {
      _setError('فشل في إضافة تذكير المتابعة: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات التفاعلات
  Future<void> loadInteractionStatistics(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _interactionStatistics = await _interactionRepository.getInteractionStatistics(
        agentId,
        startDate: startDate,
        endDate: endDate,
      );
      _clearError();
    } catch (e) {
      _setError('فشل في تحميل إحصائيات التفاعلات: $e');
    }
  }

  /// الحصول على تقرير أداء الوكيل
  Future<void> loadAgentPerformanceReport(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _performanceReport = await _interactionRepository.getAgentPerformanceReport(
        agentId,
        startDate: startDate,
        endDate: endDate,
      );
      _clearError();
    } catch (e) {
      _setError('فشل في تحميل تقرير الأداء: $e');
    }
  }

  /// الحصول على تقرير معدل التحويل
  Future<Map<String, dynamic>> getConversionReport(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      return await _interactionRepository.getConversionReport(
        agentId,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      _setError('فشل في إنشاء تقرير التحويل: $e');
      return {};
    }
  }

  /// تحليل أنماط التفاعل
  Future<Map<String, dynamic>> analyzeInteractionPatterns(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      return await _interactionRepository.analyzeInteractionPatterns(
        agentId,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      _setError('فشل في تحليل أنماط التفاعل: $e');
      return {};
    }
  }

  /// الاستماع لتغييرات تفاعلات الوكيل
  Stream<List<ClientInteraction>> listenToAgentInteractions(String agentId) {
    return _interactionRepository.listenToAgentInteractions(agentId);
  }

  /// الاستماع لتغييرات مواعيد الوكيل
  Stream<List<Appointment>> listenToAgentAppointments(String agentId) {
    return _interactionRepository.listenToAgentAppointments(agentId);
  }

  /// تنظيف البيانات
  void clearData() {
    _interactions.clear();
    _appointments.clear();
    _followUpReminders.clear();
    _interactionStatistics.clear();
    _performanceReport.clear();
    _clearError();
    notifyListeners();
  }

  // وظائف مساعدة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
