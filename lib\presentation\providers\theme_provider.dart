import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/services/settings_service.dart';
import 'package:kuwait_corners/core/theme/app_theme.dart';
import 'package:kuwait_corners/core/theme/theme_config.dart';

/// مزود إدارة الثيم والمظهر
class ThemeProvider extends ChangeNotifier {
  final SettingsService _settingsService = SettingsService();
  
  ThemeMode _themeMode = ThemeMode.light;
  bool _useSystemTheme = false;
  bool _isInitialized = false;

  /// الحصول على وضع الثيم الحالي
  ThemeMode get themeMode => _themeMode;

  /// هل يتم استخدام ثيم النظام
  bool get useSystemTheme => _useSystemTheme;

  /// هل تم تهيئة المزود
  bool get isInitialized => _isInitialized;

  /// هل الوضع الداكن مفعل
  bool get isDarkMode => _themeMode == ThemeMode.dark;

  /// الحصول على الثيم الفاتح
  ThemeData get lightTheme => AppTheme.lightTheme;

  /// الحصول على الثيم الداكن
  ThemeData get darkTheme => AppTheme.darkTheme;

  /// تهيئة المزود
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _settingsService.initialize();
      final settings = _settingsService.currentSettings;
      
      _themeMode = settings.themeSettings.themeMode;
      _useSystemTheme = settings.themeSettings.useSystemTheme;
      
      // تطبيق إعدادات النظام حسب الثيم
      _applySystemUISettings();
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing ThemeProvider: $e');
      // استخدام القيم الافتراضية في حالة الخطأ
      _themeMode = ThemeMode.light;
      _useSystemTheme = false;
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// تغيير وضع الثيم
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    
    // حفظ الإعدادات
    await _settingsService.updateThemeSettings(
      themeMode: mode,
      useSystemTheme: false, // عند التغيير اليدوي، نعطل ثيم النظام
    );
    
    _useSystemTheme = false;
    
    // تطبيق إعدادات النظام
    _applySystemUISettings();
    
    notifyListeners();
  }

  /// تبديل بين الوضع الفاتح والداكن
  Future<void> toggleTheme() async {
    final newMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    await setThemeMode(newMode);
  }

  /// تفعيل/تعطيل استخدام ثيم النظام
  Future<void> setUseSystemTheme(bool useSystem) async {
    if (_useSystemTheme == useSystem) return;

    _useSystemTheme = useSystem;
    
    if (useSystem) {
      _themeMode = ThemeMode.system;
    }
    
    // حفظ الإعدادات
    await _settingsService.updateThemeSettings(
      themeMode: _themeMode,
      useSystemTheme: useSystem,
    );
    
    // تطبيق إعدادات النظام
    _applySystemUISettings();
    
    notifyListeners();
  }

  /// تطبيق إعدادات شريطي النظام حسب الثيم
  void _applySystemUISettings() {
    if (_themeMode == ThemeMode.dark) {
      ThemeConfig.configureSystemUIDark();
    } else {
      ThemeConfig.configureSystemUI();
    }
  }

  /// الحصول على الثيم المناسب حسب السياق
  ThemeData getThemeForContext(BuildContext context) {
    if (_useSystemTheme) {
      final brightness = MediaQuery.of(context).platformBrightness;
      return brightness == Brightness.dark ? darkTheme : lightTheme;
    }
    
    return _themeMode == ThemeMode.dark ? darkTheme : lightTheme;
  }

  /// تحديد ما إذا كان الوضع الداكن مفعل حسب السياق
  bool isDarkModeForContext(BuildContext context) {
    if (_useSystemTheme) {
      final brightness = MediaQuery.of(context).platformBrightness;
      return brightness == Brightness.dark;
    }
    
    return _themeMode == ThemeMode.dark;
  }

  /// إعادة تحميل الإعدادات من الخدمة
  Future<void> reloadSettings() async {
    try {
      await _settingsService.initialize();
      final settings = _settingsService.currentSettings;
      
      final oldThemeMode = _themeMode;
      final oldUseSystemTheme = _useSystemTheme;
      
      _themeMode = settings.themeSettings.themeMode;
      _useSystemTheme = settings.themeSettings.useSystemTheme;
      
      // إشعار المستمعين فقط إذا تغيرت الإعدادات
      if (oldThemeMode != _themeMode || oldUseSystemTheme != _useSystemTheme) {
        _applySystemUISettings();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error reloading theme settings: $e');
    }
  }

  /// إعادة تعيين الثيم إلى الإعدادات الافتراضية
  Future<void> resetToDefault() async {
    _themeMode = ThemeMode.light;
    _useSystemTheme = false;
    
    await _settingsService.updateThemeSettings(
      themeMode: _themeMode,
      useSystemTheme: _useSystemTheme,
    );
    
    _applySystemUISettings();
    notifyListeners();
  }

  /// الحصول على معلومات الثيم الحالي
  Map<String, dynamic> getThemeInfo() {
    return {
      'themeMode': _themeMode.toString(),
      'useSystemTheme': _useSystemTheme,
      'isDarkMode': isDarkMode,
      'isInitialized': _isInitialized,
    };
  }
}
