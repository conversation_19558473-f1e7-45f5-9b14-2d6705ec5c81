import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نوع العنصر المتفاعل معه
enum ReactionItemType {
  /// موضوع
  topic,

  /// مشاركة
  post,
}

/// نموذج تفاعل المنتدى
class ReactionModel extends Equatable {
  /// معرف التفاعل
  final String id;

  /// نوع العنصر المتفاعل معه
  final ReactionItemType itemType;

  /// معرف العنصر المتفاعل معه
  final String itemId;

  /// نوع التفاعل
  final String reactionType;

  /// معرف المستخدم
  final String userId;

  /// اسم المستخدم
  final String userName;

  /// صورة المستخدم
  final String? userImage;

  /// تاريخ إنشاء التفاعل
  final DateTime createdAt;

  const ReactionModel({
    required this.id,
    required this.itemType,
    required this.itemId,
    required this.reactionType,
    required this.userId,
    required this.userName,
    this.userImage,
    required this.createdAt,
  });

  /// إنشاء نسخة معدلة من التفاعل
  ReactionModel copyWith({
    String? id,
    ReactionItemType? itemType,
    String? itemId,
    String? reactionType,
    String? userId,
    String? userName,
    String? userImage,
    DateTime? createdAt,
  }) {
    return ReactionModel(
      id: id ?? this.id,
      itemType: itemType ?? this.itemType,
      itemId: itemId ?? this.itemId,
      reactionType: reactionType ?? this.reactionType,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      createdAt: createdAt ?? this.createdAt);
  }

  /// تحويل التفاعل إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'itemType': itemType.index,
      'itemId': itemId,
      'reactionType': reactionType,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  /// إنشاء تفاعل من خريطة
  factory ReactionModel.fromMap(Map<String, dynamic> map) {
    return ReactionModel(
      id: map['id'] ?? '',
      itemType: map['itemType'] != null && map['itemType'] < ReactionItemType.values.length
          ? ReactionItemType.values[map['itemType']]
          : ReactionItemType.topic,
      itemId: map['itemId'] ?? '',
      reactionType: map['reactionType'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'],
      createdAt: map['createdAt'] is Timestamp
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now());
  }

  /// إنشاء تفاعل من وثيقة فايربيز
  factory ReactionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return ReactionModel(
        id: doc.id,
        itemType: ReactionItemType.topic,
        itemId: '',
        reactionType: '',
        userId: '',
        userName: '',
        createdAt: DateTime.now());
    }
    return ReactionModel.fromMap({...data, 'id': doc.id});
  }

  @override
  List<Object?> get props => [
        id,
        itemType,
        itemId,
        reactionType,
        userId,
        userName,
        userImage,
        createdAt,
      ];
}

/// نموذج إحصائيات التفاعلات
class ReactionStats extends Equatable {
  /// معرف العنصر
  final String itemId;

  /// نوع العنصر
  final ReactionItemType itemType;

  /// إجمالي عدد التفاعلات
  final int totalCount;

  /// عدد كل نوع من التفاعلات
  final Map<String, int> reactionCounts;

  /// قائمة المستخدمين لكل نوع من التفاعلات
  final Map<String, List<String>> usersByReaction;

  /// تاريخ آخر تحديث للإحصائيات
  final DateTime updatedAt;

  const ReactionStats({
    required this.itemId,
    required this.itemType,
    this.totalCount = 0,
    required this.reactionCounts,
    required this.usersByReaction,
    required this.updatedAt,
  });

  /// إنشاء نسخة معدلة من إحصائيات التفاعلات
  ReactionStats copyWith({
    String? itemId,
    ReactionItemType? itemType,
    int? totalCount,
    Map<String, int>? reactionCounts,
    Map<String, List<String>>? usersByReaction,
    DateTime? updatedAt,
  }) {
    return ReactionStats(
      itemId: itemId ?? this.itemId,
      itemType: itemType ?? this.itemType,
      totalCount: totalCount ?? this.totalCount,
      reactionCounts: reactionCounts ?? this.reactionCounts,
      usersByReaction: usersByReaction ?? this.usersByReaction,
      updatedAt: updatedAt ?? this.updatedAt);
  }

  /// تحويل إحصائيات التفاعلات إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'itemId': itemId,
      'itemType': itemType.index,
      'totalCount': totalCount,
      'reactionCounts': reactionCounts,
      'usersByReaction': usersByReaction,
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// إنشاء إحصائيات تفاعلات من خريطة
  factory ReactionStats.fromMap(Map<String, dynamic> map) {
    return ReactionStats(
      itemId: map['itemId'] ?? '',
      itemType: map['itemType'] != null && map['itemType'] < ReactionItemType.values.length
          ? ReactionItemType.values[map['itemType']]
          : ReactionItemType.topic,
      totalCount: map['totalCount'] ?? 0,
      reactionCounts: map['reactionCounts'] != null
          ? Map<String, int>.from(map['reactionCounts'])
          : {},
      usersByReaction: map['usersByReaction'] != null
          ? Map<String, List<String>>.from(map['usersByReaction'].map(
              (key, value) => MapEntry(key, List<String>.from(value))))
          : {},
      updatedAt: map['updatedAt'] is Timestamp
          ? (map['updatedAt'] as Timestamp).toDate()
          : DateTime.now());
  }

  /// إنشاء إحصائيات تفاعلات من وثيقة فايربيز
  factory ReactionStats.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return ReactionStats(
        itemId: doc.id,
        itemType: ReactionItemType.topic,
        reactionCounts: {},
        usersByReaction: {},
        updatedAt: DateTime.now());
    }
    return ReactionStats.fromMap({...data, 'itemId': doc.id});
  }

  @override
  List<Object?> get props => [
        itemId,
        itemType,
        totalCount,
        reactionCounts,
        usersByReaction,
        updatedAt,
      ];
}
