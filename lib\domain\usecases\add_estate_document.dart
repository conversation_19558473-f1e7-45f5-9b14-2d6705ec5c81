import 'dart:io';
import '../entities/estate_document.dart';
import '../repositories/document_repository.dart';

/// حالة استخدام لإضافة مستند للعقار
class AddEstateDocument {
  final DocumentRepository repository;

  AddEstateDocument(this.repository);

  /// إضافة مستند جديد للعقار
  /// [document] هو المستند المراد إضافته
  /// [file] هو ملف المستند
  /// يعيد معرف المستند الجديد
  Future<String> call(EstateDocument document, File file) async {
    // التحقق من صحة البيانات
    if (document.estateId.isEmpty) {
      throw Exception('معرف العقار مطلوب');
    }
    
    if (document.name.isEmpty) {
      throw Exception('اسم المستند مطلوب');
    }
    
    if (document.uploadedBy.isEmpty) {
      throw Exception('معرف المستخدم مطلوب');
    }
    
    // إضافة المستند
    return await repository.addDocument(document, file);
  }
}
