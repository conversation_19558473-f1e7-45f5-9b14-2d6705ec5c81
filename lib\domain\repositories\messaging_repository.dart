import '../entities/conversation.dart';
import '../entities/message.dart';

/// واجهة مستودع المراسلة
abstract class MessagingRepository {
  /// إنشاء محادثة جديدة
  Future<String> createConversation(Conversation conversation);
  
  /// الحصول على محادثة بواسطة المعرف
  Future<Conversation?> getConversationById(String conversationId);
  
  /// الحصول على محادثة بين مستخدمين
  Future<Conversation?> getConversationBetweenUsers(String userId1, String userId2);
  
  /// الحصول على محادثة متعلقة بعقار
  Future<Conversation?> getConversationForEstate(String estateId, String userId);
  
  /// الحصول على محادثة متعلقة بعرض
  Future<Conversation?> getConversationForOffer(String offerId);
  
  /// الحصول على محادثات المستخدم
  Future<List<Conversation>> getUserConversations(String userId);
  
  /// الحصول على محادثات المستخدم بالتحميل المتدرج
  /// [userId] معرف المستخدم
  /// [limit] عدد المحادثات في كل صفحة
  /// [lastConversationId] معرف آخر محادثة تم تحميلها (للصفحات التالية)
  /// [status] حالة المحادثات المطلوبة
  /// يعيد Map تحتوي على:
  /// - 'conversations': قائمة المحادثات
  /// - 'lastConversationId': معرف آخر محادثة (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من المحادثات
  Future<Map<String, dynamic>> getUserConversationsPaginated({
    required String userId,
    int limit = 20,
    String? lastConversationId,
    ConversationStatus? status,
  });
  
  /// تحديث محادثة
  Future<void> updateConversation(Conversation conversation);
  
  /// تحديث حالة المحادثة
  Future<void> updateConversationStatus(String conversationId, ConversationStatus status);
  
  /// تثبيت/إلغاء تثبيت محادثة
  Future<void> togglePinConversation(String conversationId, String userId, bool isPinned);
  
  /// كتم/إلغاء كتم محادثة
  Future<void> toggleMuteConversation(String conversationId, String userId, bool isMuted);
  
  /// تعيين إعدادات الإشعارات للمحادثة
  Future<void> setConversationNotificationSettings(String conversationId, String userId, Map<String, bool> settings);
  
  /// حذف محادثة
  Future<void> deleteConversation(String conversationId);
  
  /// أرشفة محادثة
  Future<void> archiveConversation(String conversationId);
  
  /// إضافة مشارك إلى محادثة
  Future<void> addParticipantToConversation(String conversationId, String userId, String userName, String? userImage);
  
  /// إزالة مشارك من محادثة
  Future<void> removeParticipantFromConversation(String conversationId, String userId);
  
  /// تحديث معلومات مشارك في محادثة
  Future<void> updateParticipantInConversation(String conversationId, String userId, String? userName, String? userImage);
  
  /// تعيين المشارك كيكتب حالياً
  Future<void> setParticipantTyping(String conversationId, String userId, bool isTyping);
  
  /// إرسال رسالة
  Future<String> sendMessage(Message message);
  
  /// الحصول على رسالة بواسطة المعرف
  Future<Message?> getMessageById(String messageId);
  
  /// الحصول على رسائل محادثة
  Future<List<Message>> getConversationMessages(String conversationId);
  
  /// الحصول على رسائل محادثة بالتحميل المتدرج
  /// [conversationId] معرف المحادثة
  /// [limit] عدد الرسائل في كل صفحة
  /// [lastMessageId] معرف آخر رسالة تم تحميلها (للصفحات التالية)
  /// يعيد Map تحتوي على:
  /// - 'messages': قائمة الرسائل
  /// - 'lastMessageId': معرف آخر رسالة (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من الرسائل
  Future<Map<String, dynamic>> getConversationMessagesPaginated({
    required String conversationId,
    int limit = 20,
    String? lastMessageId,
  });
  
  /// تعيين الرسائل كمقروءة
  Future<void> markMessagesAsRead(String conversationId, String userId);
  
  /// تعيين رسالة كمقروءة
  Future<void> markMessageAsRead(String messageId, String userId);
  
  /// تحديث حالة الرسالة
  Future<void> updateMessageStatus(String messageId, MessageStatus status);
  
  /// تعديل رسالة
  Future<void> editMessage(String messageId, String newContent);
  
  /// حذف رسالة
  Future<void> deleteMessage(String messageId);
  
  /// تثبيت/إلغاء تثبيت رسالة
  Future<void> togglePinMessage(String messageId, bool isPinned);
  
  /// تمييز/إلغاء تمييز رسالة بنجمة
  Future<void> toggleStarMessage(String messageId, String userId, bool isStarred);
  
  /// أرشفة رسالة
  Future<void> archiveMessage(String messageId);
  
  /// الإبلاغ عن رسالة
  Future<void> reportMessage(String messageId, String userId, String reason);
  
  /// إضافة رد فعل إلى رسالة
  Future<void> addReactionToMessage(String messageId, String userId, String reaction);
  
  /// إزالة رد فعل من رسالة
  Future<void> removeReactionFromMessage(String messageId, String userId, String reaction);
  
  /// الحصول على الرسائل المميزة بنجمة للمستخدم
  Future<List<Message>> getUserStarredMessages(String userId);
  
  /// الحصول على الرسائل المميزة بنجمة للمستخدم بالتحميل المتدرج
  /// [userId] معرف المستخدم
  /// [limit] عدد الرسائل في كل صفحة
  /// [lastMessageId] معرف آخر رسالة تم تحميلها (للصفحات التالية)
  /// يعيد Map تحتوي على:
  /// - 'messages': قائمة الرسائل
  /// - 'lastMessageId': معرف آخر رسالة (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من الرسائل
  Future<Map<String, dynamic>> getUserStarredMessagesPaginated({
    required String userId,
    int limit = 20,
    String? lastMessageId,
  });
  
  /// البحث في رسائل المستخدم
  Future<List<Message>> searchUserMessages(String userId, String query);
  
  /// البحث في محادثات المستخدم
  Future<List<Conversation>> searchUserConversations(String userId, String query);
  
  /// الاستماع للتغييرات في محادثات المستخدم
  Stream<List<Conversation>> listenToUserConversations(String userId);
  
  /// الاستماع للتغييرات في رسائل محادثة
  Stream<List<Message>> listenToConversationMessages(String conversationId);
  
  /// الاستماع للتغييرات في حالة الكتابة للمشاركين
  Stream<List<String>> listenToTypingParticipants(String conversationId);
  
  /// الاستماع للتغييرات في رسالة
  Stream<Message?> listenToMessage(String messageId);
  
  /// الاستماع للتغييرات في محادثة
  Stream<Conversation?> listenToConversation(String conversationId);
  
  /// الحصول على عدد المحادثات غير المقروءة للمستخدم
  Future<int> getUnreadConversationsCount(String userId);
  
  /// الاستماع لعدد المحادثات غير المقروءة للمستخدم
  Stream<int> listenToUnreadConversationsCount(String userId);
  
  /// الحصول على إحصائيات المراسلة للمستخدم
  Future<Map<String, dynamic>> getUserMessagingStatistics(String userId);
}
