import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../../core/theme/app_colors.dart';

/// مكون قسم المنتدى الحديث
class ModernForumSection extends StatelessWidget {
  /// عنوان القسم
  final String title;

  /// وصف القسم (اختياري)
  final String? description;

  /// أيقونة القسم (اختياري)
  final IconData? icon;

  /// لون القسم (اختياري)
  final Color? color;

  /// محتوى القسم
  final Widget content;

  /// دالة يتم استدعاؤها عند النقر على زر "عرض الكل" (اختياري)
  final VoidCallback? onViewAllTap;

  /// ما إذا كان القسم قابل للطي
  final bool isCollapsible;

  /// ما إذا كان القسم مطوياً افتراضياً
  final bool initiallyCollapsed;

  /// ما إذا كان القسم يحتوي على حدود
  final bool hasBorder;

  /// ما إذا كان القسم يحتوي على ظل
  final bool hasShadow;

  /// نصف قطر الزوايا
  final double borderRadius;

  /// التباعد الداخلي
  final EdgeInsetsGeometry padding;

  const ModernForumSection({
    super.key,
    required this.title,
    this.description,
    this.icon,
    this.color,
    required this.content,
    this.onViewAllTap,
    this.isCollapsible = false,
    this.initiallyCollapsed = false,
    this.hasBorder = true,
    this.hasShadow = true,
    this.borderRadius = 16,
    this.padding = const EdgeInsets.all(16),
  });

  @override
  Widget build(BuildContext context) {
    return AnimationConfiguration.staggeredList(
      position: 0,
      duration: const Duration(milliseconds: 500),
      child: SlideAnimation(
        verticalOffset: 50.0,
        child: FadeInAnimation(
          child: _buildSectionContent(context))));
  }

  /// بناء محتوى القسم
  Widget _buildSectionContent(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        border: hasBorder
            ? Border.all(
                color: Colors.grey.withOpacity(0.2),
                width: 1)
            : null,
        boxShadow: hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 5)),
              ]
            : null),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس القسم
          _buildSectionHeader(context),
          
          // محتوى القسم
          if (isCollapsible && initiallyCollapsed)
            const SizedBox.shrink()
          else
            Padding(
              padding: padding,
              child: content),
        ]));
  }

  /// بناء رأس القسم
  Widget _buildSectionHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: color?.withOpacity(0.05) ?? AppColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(borderRadius),
          topRight: Radius.circular(borderRadius),
          bottomLeft: Radius.circular(isCollapsible && initiallyCollapsed ? borderRadius : 0),
          bottomRight: Radius.circular(isCollapsible && initiallyCollapsed ? borderRadius : 0)),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withOpacity(0.1),
            width: 1))),
      child: Row(
        children: [
          // أيقونة القسم
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color ?? AppColors.primary,
                borderRadius: BorderRadius.circular(8)),
              child: Icon(
                icon,
                color: Colors.white,
                size: 16)),
            const SizedBox(width: 12),
          ],
          
          // عنوان ووصف القسم
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: color ?? AppColors.primary,
                    fontSize: 16,
                    fontWeight: FontWeight.bold)),
                if (description != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    description!,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12)),
                ],
              ])),
          
          // زر عرض الكل أو طي/توسيع القسم
          if (onViewAllTap != null && !isCollapsible)
            TextButton(
              onPressed: onViewAllTap,
              style: TextButton.styleFrom(
                foregroundColor: color ?? AppColors.primary,
                padding: EdgeInsets.zero,
                minimumSize: const Size(50, 30),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap),
              child: const Text('عرض الكل'))
          else if (isCollapsible)
            IconButton(
              onPressed: () {
                // يمكن تنفيذ منطق الطي/التوسيع هنا
              },
              icon: Icon(
                initiallyCollapsed ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_up,
                color: Colors.grey.shade600),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              splashRadius: 20),
        ]));
  }
}
