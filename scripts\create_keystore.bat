@echo off
echo ========================================
echo إنشاء مفتاح التوقيع الرقمي لتطبيق Krea
echo ========================================
echo.

echo هذا السكريبت سينشئ مفتاح التوقيع الرقمي المطلوب لنشر التطبيق
echo تأكد من حفظ كلمات المرور في مكان آمن
echo.

set /p STORE_PASSWORD="أدخل كلمة مرور keystore: "
set /p KEY_PASSWORD="أدخل كلمة مرور المفتاح: "
set /p DEVELOPER_NAME="أدخل اسم المطور: "
set /p ORGANIZATION="أدخل اسم المؤسسة: "

echo.
echo إنشاء keystore...

keytool -genkey -v -keystore android\upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload -dname "CN=%DEVELOPER_NAME%, OU=%ORGANIZATION%, O=%ORGANIZATION%, L=Kuwait, S=Kuwait, C=KW" -storepass %STORE_PASSWORD% -keypass %KEY_PASSWORD%

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم إنشاء keystore بنجاح!
    echo.
    echo إنشاء ملف key.properties...
    
    echo storeFile=../upload-keystore.jks > android\key.properties
    echo storePassword=%STORE_PASSWORD% >> android\key.properties
    echo keyAlias=upload >> android\key.properties
    echo keyPassword=%KEY_PASSWORD% >> android\key.properties
    
    echo ✅ تم إنشاء ملف key.properties
    echo.
    echo ⚠️  تحذير مهم:
    echo - احتفظ بنسخة آمنة من ملف upload-keystore.jks
    echo - احتفظ بكلمات المرور في مكان آمن
    echo - لا تشارك هذه الملفات مع أحد
    echo - أضف key.properties إلى .gitignore
    echo.
) else (
    echo ❌ فشل في إنشاء keystore
    echo تأكد من تثبيت Java JDK
)

pause
