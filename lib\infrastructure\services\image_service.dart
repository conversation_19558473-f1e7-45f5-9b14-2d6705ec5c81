import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:cached_network_image/cached_network_image.dart';
// import 'package:panorama/panorama.dart'; // Temporarily commented out to fix build issues
import 'package:http/http.dart' as http;

/// خدمة إدارة الصور
class ImageService {
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final ImagePicker _picker = ImagePicker();
  final Uuid _uuid = const Uuid();

  /// أحجام الصور المختلفة
  static const Map<String, int> imageSizes = {
    'thumbnail': 150, // صورة مصغرة
    'small': 300, // صورة صغيرة
    'medium': 600, // صورة متوسطة
    'large': 1200, // صورة كبيرة
    'original': 0, // الحجم الأصلي
  };

  /// جودة الصور المختلفة
  static const Map<String, int> imageQualities = {
    'low': 60, // جودة منخفضة
    'medium': 80, // جودة متوسطة
    'high': 90, // جودة عالية
    'original': 100, // الجودة الأصلية
  };

  /// التقاط صورة من الكاميرا
  Future<File?> captureImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image == null) return null;
    return File(image.path);
  }

  /// اختيار صورة من المعرض
  Future<File?> pickImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image == null) return null;
    return File(image.path);
  }

  /// اختيار عدة صور من المعرض
  Future<List<File>> pickMultipleImages() async {
    final List<XFile> images = await _picker.pickMultiImage();
    return images.map((image) => File(image.path)).toList();
  }

  /// قص الصورة
  Future<File?> cropImage(File imageFile) async {
    final croppedFile = await ImageCropper().cropImage(
      sourcePath: imageFile.path,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'قص الصورة',
          toolbarColor: Colors.blue,
          toolbarWidgetColor: Colors.white,
          lockAspectRatio: false),
        IOSUiSettings(
          title: 'قص الصورة'),
      ]);

    if (croppedFile == null) return null;
    return File(croppedFile.path);
  }

  /// ضغط الصورة مع تحسينات الأداء
  Future<File> compressImage(File imageFile,
      {int quality = 80, int? targetWidth, int? targetHeight}) async {
    try {
      final dir = await getTemporaryDirectory();
      final targetPath = path.join(dir.path, '${_uuid.v4()}.jpg');

      // فحص حجم الملف قبل الضغط
      final originalSize = await imageFile.length();

      // إذا كان الملف صغير بالفعل، لا نحتاج لضغطه
      if (originalSize < 500 * 1024) { // أقل من 500 كيلوبايت
        return imageFile;
      }

      // تحديد جودة الضغط بناءً على حجم الملف الأصلي
      int adaptiveQuality = quality;
      if (originalSize > 5 * 1024 * 1024) { // أكبر من 5 ميجابايت
        adaptiveQuality = 60;
      } else if (originalSize > 2 * 1024 * 1024) { // أكبر من 2 ميجابايت
        adaptiveQuality = 70;
      }

      var result = await FlutterImageCompress.compressAndGetFile(
        imageFile.path,
        targetPath,
        quality: adaptiveQuality,
        minWidth: targetWidth ?? 1080,
        minHeight: targetHeight ?? 1920,
        format: CompressFormat.jpeg,
      );

      if (result == null) {
        throw Exception('فشل ضغط الصورة');
      }

      // التحقق من أن الضغط فعال
      final compressedSize = await File(result.path).length();
      if (compressedSize >= originalSize) {
        // إذا لم يكن الضغط فعال، نعيد الملف الأصلي
        return imageFile;
      }

      return File(result.path);
    } catch (e) {
      debugPrint('خطأ في ضغط الصورة: $e');
      // في حالة فشل الضغط، نعيد الملف الأصلي
      return imageFile;
    }
  }

  /// تغيير حجم الصورة
  Future<File> resizeImage(File imageFile,
      {required int width, int? height, int quality = 80}) async {
    return await compressImage(
      imageFile,
      quality: quality,
      targetWidth: width,
      targetHeight: height);
  }

  /// إنشاء صور بأحجام مختلفة
  Future<Map<String, File>> createImageVariants(File originalImage) async {
    final Map<String, File> variants = {};

    // إضافة الصورة الأصلية
    variants['original'] = originalImage;

    // إنشاء الأحجام المختلفة
    for (var entry in imageSizes.entries) {
      if (entry.key == 'original') continue;

      final size = entry.value;
      final variant = await resizeImage(
        originalImage,
        width: size,
        quality: imageQualities['medium']!);

      variants[entry.key] = variant;
    }

    return variants;
  }

  /// رفع صورة إلى Firebase Storage
  Future<String> uploadImage(File imageFile, String folderPath,
      {String? fileName, bool generateVariants = false}) async {
    fileName ??= '${_uuid.v4()}${path.extension(imageFile.path)}';
    final storageRef = _storage.ref().child('$folderPath/$fileName');

    // ضغط الصورة قبل الرفع
    final compressedImage = await compressImage(imageFile);

    // رفع الصورة
    final uploadTask = storageRef.putFile(compressedImage);
    final snapshot = await uploadTask.whenComplete(() => null);
    final downloadUrl = await snapshot.ref.getDownloadURL();

    // إنشاء وتخزين الأحجام المختلفة إذا كان مطلوباً
    if (generateVariants) {
      final variants = await createImageVariants(imageFile);

      for (var entry in variants.entries) {
        if (entry.key == 'original') continue;

        final variantFileName =
            '${path.basenameWithoutExtension(fileName)}_${entry.key}${path.extension(fileName)}';
        final variantRef = _storage.ref().child('$folderPath/$variantFileName');

        await variantRef.putFile(entry.value);
      }
    }

    return downloadUrl;
  }

  /// رفع عدة صور إلى Firebase Storage
  Future<List<String>> uploadMultipleImages(
      List<File> imageFiles, String folderPath,
      {bool generateVariants = false}) async {
    final List<String> downloadUrls = [];

    for (var imageFile in imageFiles) {
      final url = await uploadImage(
        imageFile,
        folderPath,
        generateVariants: generateVariants);

      downloadUrls.add(url);
    }

    return downloadUrls;
  }

  /// الحصول على رابط صورة بحجم معين
  String getImageVariantUrl(String originalUrl, String size) {
    if (size == 'original') return originalUrl;

    final uri = Uri.parse(originalUrl);
    final pathSegments = uri.pathSegments;

    if (pathSegments.isEmpty) return originalUrl;

    final fileName = pathSegments.last;
    final fileNameWithoutExt = path.basenameWithoutExtension(fileName);
    final fileExt = path.extension(fileName);

    final variantFileName = '${fileNameWithoutExt}_$size$fileExt';
    final newPathSegments = List<String>.from(pathSegments);
    newPathSegments[newPathSegments.length - 1] = variantFileName;

    final newUri = uri.replace(pathSegments: newPathSegments);
    return newUri.toString();
  }

  /// حذف صورة من Firebase Storage
  Future<void> deleteImage(String imageUrl) async {
    try {
      // استخراج المسار من الرابط
      final uri = Uri.parse(imageUrl);
      final ref = _storage.refFromURL(imageUrl);

      // حذف الصورة الأصلية
      await ref.delete();

      // حذف الأحجام المختلفة
      for (var size in imageSizes.keys) {
        if (size == 'original') continue;

        try {
          final variantUrl = getImageVariantUrl(imageUrl, size);
          final variantRef = _storage.refFromURL(variantUrl);
          await variantRef.delete();
        } catch (e) {
          // تجاهل الأخطاء إذا لم يتم العثور على الصورة
        }
      }
    } catch (e) {
      print('خطأ في حذف الصورة: $e');
      rethrow;
    }
  }

  /// تحويل صورة إلى Base64
  Future<String> imageToBase64(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    return base64Encode(bytes);
  }

  /// تحويل Base64 إلى صورة
  Future<File> base64ToImage(String base64String) async {
    final bytes = base64Decode(base64String);
    final dir = await getTemporaryDirectory();
    final file = File('${dir.path}/${_uuid.v4()}.jpg');
    await file.writeAsBytes(bytes);
    return file;
  }

  /// تحميل صورة من الإنترنت
  Future<File> downloadImage(String imageUrl) async {
    final response = await http.get(Uri.parse(imageUrl));
    final bytes = response.bodyBytes;
    final dir = await getTemporaryDirectory();
    final file = File('${dir.path}/${_uuid.v4()}.jpg');
    await file.writeAsBytes(bytes);
    return file;
  }
}

/// امتدادات لتسهيل استخدام الخدمة
extension ImageServiceExtensions on ImageService {
  /// رفع صورة عقار
  Future<String> uploadEstateImage(File imageFile, String estateId) {
    return uploadImage(imageFile, 'estates/$estateId', generateVariants: true);
  }

  /// رفع صورة مستخدم
  Future<String> uploadUserImage(File imageFile, String userId) {
    return uploadImage(imageFile, 'users/$userId');
  }

  /// رفع صورة مستند
  Future<String> uploadDocumentImage(
      File imageFile, String estateId, String documentId) {
    return uploadImage(imageFile, 'estates/$estateId/documents/$documentId');
  }

  /// رفع صورة جولة افتراضية
  Future<String> uploadVirtualTourImage(
      File imageFile, String estateId, String tourId) {
    return uploadImage(imageFile, 'estates/$estateId/tours/$tourId');
  }
}

/// امتدادات لتسهيل التعامل مع الصور في واجهة المستخدم
extension ImageWidgetExtensions on ImageService {
  /// عرض صورة من الإنترنت مع التحميل الكسول
  Widget networkImageWithLoading(
    String imageUrl, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    String size = 'medium',
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    final variantUrl =
        size == 'original' ? imageUrl : getImageVariantUrl(imageUrl, size);

    return Image.network(
      variantUrl,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return placeholder ??
            Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null));
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ?? const Icon(Icons.error);
      });
  }

  /// عرض صورة من الإنترنت مع تخزين مؤقت
  Widget cachedNetworkImage(
    String imageUrl, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    String size = 'medium',
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    final variantUrl =
        size == 'original' ? imageUrl : getImageVariantUrl(imageUrl, size);

    return CachedNetworkImage(
      imageUrl: variantUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) =>
          placeholder ?? const Center(child: CircularProgressIndicator()),
      errorWidget: (context, url, error) =>
          errorWidget ?? const Icon(Icons.error));
  }

  /// عرض صورة مع تحميل تدريجي
  Widget progressiveImage(
    String imageUrl, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    final thumbnailUrl = getImageVariantUrl(imageUrl, 'thumbnail');
    final fullImageUrl = imageUrl;

    return ProgressiveImage(
      thumbnail: NetworkImage(thumbnailUrl),
      image: NetworkImage(fullImageUrl),
      width: width,
      height: height,
      fit: fit);
  }
}

/// امتدادات لتسهيل التعامل مع الصور 360 درجة
extension Panorama360Extensions on ImageService {
  /// عرض صورة 360 درجة
  Widget panoramaView(
    String panoramaUrl, {
    double? width,
    double? height,
    bool interactive = true,
  }) {
    // استخدام عرض بديل في حالة عدم توفر مكتبة Panorama
    return _buildFallbackPanoramaView(panoramaUrl, width, height);
  }

  /// عرض جولة افتراضية 360 درجة
  Widget virtualTourView(
    List<String> panoramaUrls, {
    double? width,
    double? height,
    bool interactive = true,
  }) {
    // استخدام عرض بديل في حالة عدم توفر مكتبة Panorama
    return _buildFallbackVirtualTourView(panoramaUrls, width, height);
  }

  /// عرض بديل للصور 360 درجة
  Widget _buildFallbackPanoramaView(
      String panoramaUrl, double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.network(
            panoramaUrl,
            width: width,
            height: height != null ? height * 0.7 : null,
            fit: BoxFit.contain),
          const SizedBox(height: 16),
          const Text(
            'عرض 360 درجة غير متاح حالياً',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14)),
        ]));
  }

  /// عرض بديل للجولات الافتراضية 360 درجة
  Widget _buildFallbackVirtualTourView(
      List<String> panoramaUrls, double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (panoramaUrls.isNotEmpty)
            Image.network(
              panoramaUrls.first,
              width: width,
              height: height != null ? height * 0.7 : null,
              fit: BoxFit.contain),
          const SizedBox(height: 16),
          const Text(
            'الجولة الافتراضية 360 درجة غير متاحة حالياً',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14)),
        ]));
  }
}

/// مكون لعرض الصور بشكل تدريجي
class ProgressiveImage extends StatelessWidget {
  final ImageProvider thumbnail;
  final ImageProvider image;
  final double? width;
  final double? height;
  final BoxFit fit;

  const ProgressiveImage({
    super.key,
    required this.thumbnail,
    required this.image,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
  });

  @override
  Widget build(BuildContext context) {
    return FadeInImage(
      placeholder: thumbnail,
      image: image,
      width: width,
      height: height,
      fit: fit,
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 300));
  }
}

/// مكون لعرض جولة افتراضية 360 درجة
class VirtualTourViewer extends StatefulWidget {
  final List<String> panoramaUrls;
  final double? width;
  final double? height;
  final bool interactive;

  const VirtualTourViewer({
    super.key,
    required this.panoramaUrls,
    this.width,
    this.height,
    this.interactive = true,
  });

  @override
  VirtualTourViewerState createState() => VirtualTourViewerState();
}

class VirtualTourViewerState extends State<VirtualTourViewer> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.grey[200],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widget.panoramaUrls.isNotEmpty)
            Expanded(
              child: Image.network(
                widget.panoramaUrls[_currentIndex],
                width: widget.width,
                fit: BoxFit.contain)),
          const SizedBox(height: 16),
          const Text(
            'الجولة الافتراضية 360 درجة غير متاحة حالياً',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14)),
          if (widget.panoramaUrls.length > 1)
            SizedBox(
              height: 60,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: widget.panoramaUrls.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _currentIndex = index;
                      });
                    },
                    child: Container(
                      width: 60,
                      height: 60,
                      margin: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _currentIndex == index
                              ? Colors.blue
                              : Colors.grey,
                          width: 2),
                        borderRadius: BorderRadius.circular(8)),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: Image.network(
                          widget.panoramaUrls[index],
                          fit: BoxFit.cover))));
                })),
        ]));
  }
}

/// تحويل من Base64 إلى Uint8List
Uint8List base64Decode(String base64String) {
  return base64.decode(base64String);
}

/// تحويل من Uint8List إلى Base64
String base64Encode(Uint8List bytes) {
  return base64.encode(bytes);
}
