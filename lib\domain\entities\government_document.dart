import 'package:equatable/equatable.dart';

/// كيان المستند الحكومي
class GovernmentDocument extends Equatable {
  final String id;
  final String userId;
  final String documentType;
  final String documentNumber;
  final String documentUrl;
  final String verificationCode;
  final DateTime issueDate;
  final DateTime? expiryDate;
  final bool isVerified;
  final Map<String, dynamic>? metadata;

  /// إنشاء كيان المستند الحكومي
  const GovernmentDocument({
    required this.id,
    required this.userId,
    required this.documentType,
    required this.documentNumber,
    required this.documentUrl,
    required this.verificationCode,
    required this.issueDate,
    this.expiryDate,
    required this.isVerified,
    this.metadata,
  });

  /// إنشاء كيان المستند الحكومي من JSON
  factory GovernmentDocument.fromJson(Map<String, dynamic> json) {
    return GovernmentDocument(
      id: json['id'] as String,
      userId: json['userId'] as String,
      documentType: json['documentType'] as String,
      documentNumber: json['documentNumber'] as String,
      documentUrl: json['documentUrl'] as String,
      verificationCode: json['verificationCode'] as String,
      issueDate: DateTime.parse(json['issueDate'] as String),
      expiryDate: json['expiryDate'] != null
          ? DateTime.parse(json['expiryDate'] as String)
          : null,
      isVerified: json['isVerified'] as bool,
      metadata: json['metadata'] as Map<String, dynamic>?);
  }

  /// تحويل كيان المستند الحكومي إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'documentType': documentType,
      'documentNumber': documentNumber,
      'documentUrl': documentUrl,
      'verificationCode': verificationCode,
      'issueDate': issueDate.toIso8601String(),
      'expiryDate': expiryDate?.toIso8601String(),
      'isVerified': isVerified,
      'metadata': metadata,
    };
  }

  /// نسخ كيان المستند الحكومي مع تعديل بعض الخصائص
  GovernmentDocument copyWith({
    String? id,
    String? userId,
    String? documentType,
    String? documentNumber,
    String? documentUrl,
    String? verificationCode,
    DateTime? issueDate,
    DateTime? expiryDate,
    bool? isVerified,
    Map<String, dynamic>? metadata,
  }) {
    return GovernmentDocument(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      documentType: documentType ?? this.documentType,
      documentNumber: documentNumber ?? this.documentNumber,
      documentUrl: documentUrl ?? this.documentUrl,
      verificationCode: verificationCode ?? this.verificationCode,
      issueDate: issueDate ?? this.issueDate,
      expiryDate: expiryDate ?? this.expiryDate,
      isVerified: isVerified ?? this.isVerified,
      metadata: metadata ?? this.metadata);
  }

  /// التحقق مما إذا كان المستند ساري المفعول
  bool isValid() {
    if (!isVerified) {
      return false;
    }

    if (expiryDate == null) {
      return true;
    }

    return DateTime.now().isBefore(expiryDate!);
  }

  /// الحصول على المدة المتبقية لصلاحية المستند بالأيام
  int? getRemainingDays() {
    if (expiryDate == null) {
      return null;
    }

    final now = DateTime.now();
    return expiryDate!.difference(now).inDays;
  }

  /// الحصول على وصف نوع المستند
  String getDocumentTypeDescription() {
    switch (documentType) {
      case 'ownership_deed':
        return 'صك ملكية';
      case 'property_license':
        return 'رخصة عقار';
      case 'building_permit':
        return 'رخصة بناء';
      case 'id_card':
        return 'بطاقة هوية';
      case 'commercial_register':
        return 'سجل تجاري';
      case 'tax_certificate':
        return 'شهادة ضريبية';
      default:
        return 'مستند حكومي';
    }
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        documentType,
        documentNumber,
        documentUrl,
        verificationCode,
        issueDate,
        expiryDate,
        isVerified,
        metadata,
      ];
}
