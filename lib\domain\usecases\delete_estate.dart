import '../repositories/estate_repository.dart';

/// Use case class for deleting an estate record.
/// This class delegates the deletion operation to the EstateRepository.
class DeleteEstate {
  /// The repository used to perform estate-related operations.
  final EstateRepository repository;

  /// Constructs a [DeleteEstate] instance with the provided [repository].
  DeleteEstate(this.repository);

  /// Calls the repository's [deleteEstate] method to remove an estate by its [id].
  Future<void> call(String id) async {
    return await repository.deleteEstate(id);
  }
}
