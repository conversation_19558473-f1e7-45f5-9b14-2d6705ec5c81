import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/user_statistics_model.dart';

/// بطاقة الإنجاز
class AchievementCard extends StatelessWidget {
  /// نموذج الإنجاز
  final AchievementModel achievement;

  /// حجم البطاقة
  final AchievementCardSize size;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;

  /// ما إذا كانت البطاقة مقفلة
  final bool isLocked;

  const AchievementCard({
    super.key,
    required this.achievement,
    this.size = AchievementCardSize.medium,
    this.onTap,
    this.isLocked = false,
  });

  @override
  Widget build(BuildContext context) {
    switch (size) {
      case AchievementCardSize.small:
        return _buildSmallCard(context);
      case AchievementCardSize.medium:
        return _buildMediumCard(context);
      case AchievementCardSize.large:
        return _buildLargeCard(context);
    }
  }

  /// بناء بطاقة صغيرة
  Widget _buildSmallCard(BuildContext context) {
    final progress = achievement.value / achievement.maxValue;
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 80,
        height: 100,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isLocked
                ? Colors.grey.shade300
                : achievement.isCompleted
                    ? AppColors.primary
                    : Colors.grey.shade300),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2)),
          ]),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الإنجاز
            Stack(
              alignment: Alignment.center,
              children: [
                CircularPercentIndicator(
                  radius: 25,
                  lineWidth: 3,
                  percent: isLocked ? 0 : progress,
                  center: _buildAchievementIcon(size: 30),
                  progressColor: isLocked
                      ? Colors.grey.shade400
                      : achievement.isCompleted
                          ? AppColors.primary
                          : Colors.amber,
                  backgroundColor: Colors.grey.shade200),
                if (isLocked)
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      shape: BoxShape.circle),
                    child: const Icon(
                      Icons.lock,
                      color: Colors.white,
                      size: 20)),
              ]),
            const SizedBox(height: 8),
            
            // اسم الإنجاز
            Text(
              achievement.name,
              style: TextStyle(
                fontSize: 10,
                fontWeight: achievement.isCompleted ? FontWeight.bold : FontWeight.normal,
                color: isLocked
                    ? Colors.grey.shade500
                    : achievement.isCompleted
                        ? AppColors.primary
                        : Colors.grey.shade700),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis),
          ])));
  }

  /// بناء بطاقة متوسطة
  Widget _buildMediumCard(BuildContext context) {
    final progress = achievement.value / achievement.maxValue;
    final percentage = (progress * 100).round();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isLocked
              ? Colors.grey.shade300
              : achievement.isCompleted
                  ? AppColors.primary
                  : Colors.transparent,
          width: achievement.isCompleted ? 1 : 0)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // أيقونة الإنجاز
              Stack(
                alignment: Alignment.center,
                children: [
                  CircularPercentIndicator(
                    radius: 30,
                    lineWidth: 5,
                    percent: isLocked ? 0 : progress,
                    center: _buildAchievementIcon(size: 35),
                    progressColor: isLocked
                        ? Colors.grey.shade400
                        : achievement.isCompleted
                            ? AppColors.primary
                            : Colors.amber,
                    backgroundColor: Colors.grey.shade200),
                  if (isLocked)
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle),
                      child: const Icon(
                        Icons.lock,
                        color: Colors.white,
                        size: 24)),
                ]),
              const SizedBox(width: 16),
              
              // معلومات الإنجاز
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // اسم الإنجاز
                        Expanded(
                          child: Text(
                            achievement.name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: isLocked
                                  ? Colors.grey.shade500
                                  : achievement.isCompleted
                                      ? AppColors.primary
                                      : Colors.black87,
                              fontSize: 16),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis)),
                        
                        // نسبة الإكمال
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4),
                          decoration: BoxDecoration(
                            color: isLocked
                                ? Colors.grey.shade200
                                : achievement.isCompleted
                                    ? AppColors.primary
                                    : Colors.amber,
                            borderRadius: BorderRadius.circular(12)),
                          child: Text(
                            isLocked
                                ? 'مقفل'
                                : achievement.isCompleted
                                    ? 'مكتمل'
                                    : '$percentage%',
                            style: TextStyle(
                              color: isLocked
                                  ? Colors.grey.shade700
                                  : Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold))),
                      ]),
                    const SizedBox(height: 4),
                    
                    // وصف الإنجاز
                    Text(
                      achievement.description,
                      style: TextStyle(
                        color: isLocked
                            ? Colors.grey.shade500
                            : Colors.grey.shade700,
                        fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis),
                    const SizedBox(height: 8),
                    
                    // شريط التقدم
                    LinearPercentIndicator(
                      lineHeight: 8,
                      percent: isLocked ? 0 : progress,
                      progressColor: isLocked
                          ? Colors.grey.shade400
                          : achievement.isCompleted
                              ? AppColors.primary
                              : Colors.amber,
                      backgroundColor: Colors.grey.shade200,
                      barRadius: const Radius.circular(4),
                      padding: EdgeInsets.zero,
                      trailing: Text(
                        '${achievement.value}/${achievement.maxValue}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12))),
                  ])),
            ]))));
  }

  /// بناء بطاقة كبيرة
  Widget _buildLargeCard(BuildContext context) {
    final progress = achievement.value / achievement.maxValue;
    final percentage = (progress * 100).round();
    
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isLocked
              ? Colors.grey.shade300
              : achievement.isCompleted
                  ? AppColors.primary
                  : Colors.transparent,
          width: achievement.isCompleted ? 2 : 0)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // أيقونة الإنجاز
              Stack(
                alignment: Alignment.center,
                children: [
                  CircularPercentIndicator(
                    radius: 50,
                    lineWidth: 8,
                    percent: isLocked ? 0 : progress,
                    center: _buildAchievementIcon(size: 60),
                    progressColor: isLocked
                        ? Colors.grey.shade400
                        : achievement.isCompleted
                            ? AppColors.primary
                            : Colors.amber,
                    backgroundColor: Colors.grey.shade200,
                    animation: true,
                    animationDuration: 1000),
                  if (isLocked)
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle),
                      child: const Icon(
                        Icons.lock,
                        color: Colors.white,
                        size: 40)),
                ]),
              const SizedBox(height: 16),
              
              // اسم الإنجاز
              Text(
                achievement.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isLocked
                      ? Colors.grey.shade500
                      : achievement.isCompleted
                          ? AppColors.primary
                          : Colors.black87,
                  fontSize: 18),
                textAlign: TextAlign.center),
              const SizedBox(height: 8),
              
              // وصف الإنجاز
              Text(
                achievement.description,
                style: TextStyle(
                  color: isLocked
                      ? Colors.grey.shade500
                      : Colors.grey.shade700,
                  fontSize: 14),
                textAlign: TextAlign.center),
              const SizedBox(height: 16),
              
              // شريط التقدم
              LinearPercentIndicator(
                lineHeight: 10,
                percent: isLocked ? 0 : progress,
                progressColor: isLocked
                    ? Colors.grey.shade400
                    : achievement.isCompleted
                        ? AppColors.primary
                        : Colors.amber,
                backgroundColor: Colors.grey.shade200,
                barRadius: const Radius.circular(5),
                padding: EdgeInsets.zero,
                animation: true,
                animationDuration: 1000,
                center: Text(
                  isLocked
                      ? 'مقفل'
                      : achievement.isCompleted
                          ? 'مكتمل'
                          : '$percentage%',
                  style: TextStyle(
                    color: isLocked ? Colors.grey.shade700 : Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold))),
              const SizedBox(height: 8),
              
              // التقدم
              Text(
                '${achievement.value}/${achievement.maxValue}',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14)),
              
              // تاريخ الإكمال
              if (achievement.isCompleted) ...[
                const SizedBox(height: 8),
                Text(
                  'تم الإكمال في ${_formatDate(achievement.earnedAt)}',
                  style: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 12,
                    fontStyle: FontStyle.italic)),
              ],
            ]))));
  }

  /// بناء أيقونة الإنجاز
  Widget _buildAchievementIcon({required double size}) {
    if (achievement.imageUrl != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(size / 2),
        child: CachedNetworkImage(
          imageUrl: achievement.imageUrl!,
          width: size,
          height: size,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Colors.grey.shade200,
            child: Center(
              child: SizedBox(
                width: size / 2,
                height: size / 2,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isLocked
                        ? Colors.grey.shade400
                        : achievement.isCompleted
                            ? AppColors.primary
                            : Colors.amber))))),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey.shade200,
            child: Icon(
              Icons.error,
              size: size / 2,
              color: Colors.grey.shade400))));
    } else {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: isLocked
              ? Colors.grey.shade200
              : achievement.isCompleted
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : Colors.amber.withValues(alpha: 0.1),
          shape: BoxShape.circle),
        child: Icon(
          Icons.emoji_events,
          size: size / 2,
          color: isLocked
              ? Colors.grey.shade400
              : achievement.isCompleted
                  ? AppColors.primary
                  : Colors.amber));
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }
}

/// حجم بطاقة الإنجاز
enum AchievementCardSize {
  /// صغير
  small,

  /// متوسط
  medium,

  /// كبير
  large,
}
