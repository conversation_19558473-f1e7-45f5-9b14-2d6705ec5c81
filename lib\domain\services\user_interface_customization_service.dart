import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../entities/user.dart';
import '../../core/constants/user_types.dart';

/// خدمة تخصيص الواجهات حسب نوع المستخدم
class UserInterfaceCustomizationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// الحصول على نوع المستخدم الحالي
  Future<UserType> getCurrentUserType() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return UserType.seeker;

      // أولاً: محاولة الحصول على النوع من التخزين المحلي (للمستخدمين الذين فعلوا "تذكرني")
      try {
        final prefs = await SharedPreferences.getInstance();
        final rememberMe = prefs.getBool('rememberMe') ?? false;

        if (rememberMe) {
          final savedUserType = prefs.getString('savedUserType');
          if (savedUserType != null && savedUserType.isNotEmpty) {
            print('Using saved user type from local storage: $savedUserType');
            return _parseUserType(savedUserType);
          }
        }
      } catch (e) {
        print('Error reading from local storage: $e');
      }

      // ثانياً: محاولة الحصول على النوع من Firestore
      try {
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        if (!userDoc.exists) {
          print('User document does not exist in Firestore');
          return UserType.seeker;
        }

        final userData = userDoc.data()!;

        // طباعة جميع بيانات المستخدم للتشخيص
        print('=== User Data Debug ===');
        print('User ID: ${user.uid}');
        print('All user data: $userData');
        print('Available keys: ${userData.keys.toList()}');

        // التحقق من النوع النصي أولاً (الطريقة الصحيحة)
        String? userType = userData['userType'] as String?;

        // إذا لم يوجد userType، تحقق من type
        userType ??= userData['type'] as String?;

        // إذا لم يوجد أي منهما، تحقق من displayName أو fullName للشركات
        if (userType == null) {
          final displayName = userData['displayName'] as String?;
          final fullName = userData['fullName'] as String?;

          // إذا كان الاسم يحتوي على كلمات تدل على نوع المستخدم
          if (displayName != null || fullName != null) {
            final name = (displayName ?? fullName ?? '').toLowerCase();
            if (name.contains('شركة') || name.contains('company')) {
              userType = 'company';
            } else if (name.contains('وكيل') || name.contains('agent') || name.contains('وسيط')) {
              userType = 'agent';
            } else if (name.contains('مالك') || name.contains('owner')) {
              userType = 'owner';
            }
          }
        }

        print('User type from database: $userType');

        // حفظ النوع في التخزين المحلي إذا كان "تذكرني" مفعل
        try {
          final prefs = await SharedPreferences.getInstance();
          final rememberMe = prefs.getBool('rememberMe') ?? false;
          if (rememberMe && userType != null) {
            await prefs.setString('savedUserType', userType);
            print('Saved user type to local storage: $userType');
          }
        } catch (e) {
          print('Error saving user type to local storage: $e');
        }

        return _parseUserType(userType ?? UserTypeConstants.seeker);
      } catch (firestoreError) {
        print('Error fetching from Firestore: $firestoreError');
        return UserType.seeker;
      }
    } catch (e) {
      print('Error getting user type: $e');
      return UserType.seeker;
    }
  }

  /// تحليل نوع المستخدم من النص
  UserType _parseUserType(String userType) {
    print('Parsing user type: "$userType"');

    // استخدام دالة التطبيع من UserTypeConstants
    final normalizedType = UserTypeConstants.normalizeUserType(userType);
    print('Normalized to: "$normalizedType"');

    switch (normalizedType) {
      case UserTypeConstants.seeker:
        print('Parsed as SEEKER');
        return UserType.seeker;
      case UserTypeConstants.agent:
        print('Parsed as AGENT');
        return UserType.agent;
      case UserTypeConstants.owner:
        print('Parsed as OWNER');
        return UserType.owner;
      case UserTypeConstants.company:
        print('Parsed as COMPANY');
        return UserType.company;
      default:
        print('Unknown user type: "$normalizedType", defaulting to seeker');
        return UserType.seeker;
    }
  }

  /// الحصول على الأقسام المخصصة حسب نوع المستخدم
  List<Map<String, dynamic>> getCustomizedSections(UserType userType) {
    switch (userType) {
      case UserType.seeker:
        return _getSeekerSections();
      case UserType.agent:
        return _getAgentSections();
      case UserType.owner:
        return _getOwnerSections();
      case UserType.company:
        return _getCompanySections();
    }
  }

  /// أقسام الباحثين عن عقارات
  List<Map<String, dynamic>> _getSeekerSections() {
    return [
      {
        "name": "الكل",
        "icon": Icons.home_work,
        "route": null,
        "color": Colors.blue,
        "category": "all"
      },
      {
        "name": "للبيع",
        "icon": Icons.sell,
        "route": null,
        "color": Colors.green,
        "category": "for_sale"
      },
      {
        "name": "للإيجار",
        "icon": Icons.apartment,
        "route": null,
        "color": Colors.orange,
        "category": "for_rent"
      },
      {
        "name": "شقق",
        "icon": Icons.apartment_outlined,
        "route": null,
        "color": Colors.amber,
        "category": "apartments"
      },
      {
        "name": "فلل",
        "icon": Icons.villa,
        "route": null,
        "color": Colors.teal,
        "category": "villas"
      },
      {
        "name": "أراضي",
        "icon": Icons.landscape,
        "route": null,
        "color": Colors.brown,
        "category": "lands"
      },
      {
        "name": "طلبات العقارات",
        "icon": Icons.request_page,
        "route": "/property-requests",
        "color": Colors.purple,
        "category": "requests"
      },
      {
        "name": "المفضلة",
        "icon": Icons.favorite,
        "route": "/favorites",
        "color": Colors.red,
        "category": "favorites"
      },
    ];
  }

  /// أقسام الوسطاء العقاريين
  List<Map<String, dynamic>> _getAgentSections() {
    return [
      {
        "name": "الكل",
        "icon": Icons.home_work,
        "route": null,
        "color": Colors.blue,
        "category": "all"
      },
      {
        "name": "للبيع",
        "icon": Icons.sell,
        "route": null,
        "color": Colors.green,
        "category": "for_sale"
      },
      {
        "name": "للإيجار",
        "icon": Icons.apartment,
        "route": null,
        "color": Colors.orange,
        "category": "for_rent"
      },
      {
        "name": "إعلاناتي",
        "icon": Icons.business_center,
        "route": "/my-properties",
        "color": Colors.indigo,
        "category": "my_ads"
      },
      {
        "name": "طلبات العقارات",
        "icon": Icons.request_page,
        "route": "/property-requests",
        "color": Colors.purple,
        "category": "requests"
      },
      {
        "name": "عملائي",
        "icon": Icons.people,
        "route": "/my-clients",
        "color": Colors.cyan,
        "category": "clients"
      },
      {
        "name": "تحليل السوق",
        "icon": Icons.analytics,
        "route": "/market-analysis",
        "color": Colors.deepOrange,
        "category": "analytics"
      },
      {
        "name": "المفضلة",
        "icon": Icons.favorite,
        "route": "/favorites",
        "color": Colors.red,
        "category": "favorites"
      },
    ];
  }

  /// أقسام مالكي العقارات
  List<Map<String, dynamic>> _getOwnerSections() {
    return [
      {
        "name": "الكل",
        "icon": Icons.home_work,
        "route": null,
        "color": Colors.blue,
        "category": "all"
      },
      {
        "name": "عقاراتي",
        "icon": Icons.home,
        "route": "/my-properties",
        "color": Colors.green,
        "category": "my_properties"
      },
      {
        "name": "إضافة عقار",
        "icon": Icons.add_home,
        "route": "/add-property",
        "color": Colors.teal,
        "category": "add_property"
      },
      {
        "name": "طلبات العقارات",
        "icon": Icons.request_page,
        "route": "/property-requests",
        "color": Colors.purple,
        "category": "requests"
      },
      {
        "name": "الاستفسارات",
        "icon": Icons.question_answer,
        "route": "/inquiries",
        "color": Colors.orange,
        "category": "inquiries"
      },
      {
        "name": "الإحصائيات",
        "icon": Icons.bar_chart,
        "route": "/property-stats",
        "color": Colors.indigo,
        "category": "stats"
      },
      {
        "name": "المفضلة",
        "icon": Icons.favorite,
        "route": "/favorites",
        "color": Colors.red,
        "category": "favorites"
      },
      {
        "name": "للبدل",
        "icon": Icons.swap_horiz,
        "route": null,
        "color": Colors.amber,
        "category": "for_exchange"
      },
    ];
  }

  /// أقسام الشركات العقارية
  List<Map<String, dynamic>> _getCompanySections() {
    return [
      {
        "name": "الكل",
        "icon": Icons.home_work,
        "route": null,
        "color": Colors.blue,
        "category": "all"
      },
      {
        "name": "مشاريعنا",
        "icon": Icons.business,
        "route": "/company-projects",
        "color": Colors.green,
        "category": "projects"
      },
      {
        "name": "إعلاناتنا",
        "icon": Icons.campaign,
        "route": "/my-properties",
        "color": Colors.indigo,
        "category": "company_ads"
      },
      {
        "name": "العملاء",
        "icon": Icons.people,
        "route": "/company-clients",
        "color": Colors.cyan,
        "category": "clients"
      },
      {
        "name": "طلبات العقارات",
        "icon": Icons.request_page,
        "route": "/property-requests",
        "color": Colors.purple,
        "category": "requests"
      },
      {
        "name": "تحليل السوق",
        "icon": Icons.analytics,
        "route": "/market-analysis",
        "color": Colors.deepOrange,
        "category": "analytics"
      },
      {
        "name": "التقارير",
        "icon": Icons.assessment,
        "route": "/reports",
        "color": Colors.brown,
        "category": "reports"
      },
      {
        "name": "الفرق",
        "icon": Icons.group,
        "route": "/teams",
        "color": Colors.teal,
        "category": "teams"
      },
    ];
  }

  /// الحصول على الأدوات المخصصة حسب نوع المستخدم
  List<Map<String, dynamic>> getCustomizedTools(UserType userType) {
    switch (userType) {
      case UserType.seeker:
        return [
          {
            "name": "المفضلة",
            "icon": Icons.favorite,
            "route": "/favorites",
            "color": Colors.red,
          },
          {
            "name": "المقارنة",
            "icon": Icons.compare_arrows,
            "route": "/comparison",
            "color": Colors.blue,
          },
          {
            "name": "بحث متقدم",
            "icon": Icons.tune,
            "route": "/advanced-search",
            "color": Colors.green,
          },
        ];
      case UserType.agent:
        return [
          {
            "name": "إضافة عقار",
            "icon": Icons.add_home,
            "route": "/add-property",
            "color": Colors.green,
          },
          {
            "name": "عملائي",
            "icon": Icons.people,
            "route": "/my-clients",
            "color": Colors.blue,
          },
          {
            "name": "الإحصائيات",
            "icon": Icons.analytics,
            "route": "/agent-stats",
            "color": Colors.orange,
          },
        ];
      case UserType.owner:
        return [
          {
            "name": "إضافة عقار",
            "icon": Icons.add_home,
            "route": "/add-property",
            "color": Colors.green,
          },
          {
            "name": "عقاراتي",
            "icon": Icons.home,
            "route": "/my-properties",
            "color": Colors.blue,
          },
          {
            "name": "الاستفسارات",
            "icon": Icons.question_answer,
            "route": "/inquiries",
            "color": Colors.orange,
          },
        ];
      case UserType.company:
        return [
          {
            "name": "إضافة مشروع",
            "icon": Icons.add_business,
            "route": "/add-project",
            "color": Colors.green,
          },
          {
            "name": "الفرق",
            "icon": Icons.group,
            "route": "/teams",
            "color": Colors.blue,
          },
          {
            "name": "التقارير",
            "icon": Icons.assessment,
            "route": "/reports",
            "color": Colors.orange,
          },
        ];
    }
  }

  /// التحقق من صلاحية المستخدم لرؤية قسم معين
  bool canUserAccessSection(UserType userType, String category) {
    final sections = getCustomizedSections(userType);
    return sections.any((section) => section['category'] == category);
  }

  /// الحصول على رسالة ترحيب مخصصة حسب نوع المستخدم
  String getWelcomeMessage(UserType userType, String userName) {
    switch (userType) {
      case UserType.seeker:
        return 'مرحباً $userName! ابحث عن عقارك المثالي';
      case UserType.agent:
        return 'مرحباً $userName! إدارة عملائك وعقاراتك';
      case UserType.owner:
        return 'مرحباً $userName! إدارة عقاراتك بسهولة';
      case UserType.company:
        return 'مرحباً $userName! إدارة مشاريعك وفريقك';
    }
  }

  /// الحصول على لون مخصص حسب نوع المستخدم
  Color getUserTypeColor(UserType userType) {
    switch (userType) {
      case UserType.seeker:
        return Colors.blue;
      case UserType.agent:
        return Colors.green;
      case UserType.owner:
        return Colors.orange;
      case UserType.company:
        return Colors.purple;
    }
  }
}
