import 'package:cloud_firestore/cloud_firestore.dart';
import 'estate_base.dart';
import 'residential_estate.dart';
import 'commercial_estate.dart';
import 'land_estate.dart';

/// مصنع لإنشاء العقارات المناسبة حسب النوع
class EstateFactory {
  /// إنشاء عقار من Map
  static EstateBase createFromMap(Map<String, dynamic> map) {
    final estateType = map['estateType'] as String? ?? 'residential';
    
    switch (estateType) {
      case 'commercial':
        return CommercialEstate.fromMap(map);
      case 'land':
        return LandEstate.fromMap(map);
      case 'residential':
      default:
        return ResidentialEstate.fromMap(map);
    }
  }
  
  /// إنشاء عقار من DocumentSnapshot
  static EstateBase createFromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return createFromMap(data);
  }
  
  /// إنشاء عقار جديد فارغ حسب النوع
  static EstateBase createEmpty(String estateType, String ownerId) {
    final now = DateTime.now();
    final endDate = now.add(const Duration(days: 30));
    
    switch (estateType) {
      case 'commercial':
        return CommercialEstate(
          id: '',
          title: '',
          description: '',
          price: 0,
          location: '',
          photoUrls: [],
          isFeatured: false,
          status: 'متاح',
          mainCategory: 'commercial',
          ownerId: ownerId,
          createdAt: now,
          startDate: now,
          endDate: endDate);
      case 'land':
        return LandEstate(
          id: '',
          title: '',
          description: '',
          price: 0,
          location: '',
          photoUrls: [],
          isFeatured: false,
          status: 'متاح',
          mainCategory: 'land',
          ownerId: ownerId,
          createdAt: now,
          startDate: now,
          endDate: endDate);
      case 'residential':
      default:
        return ResidentialEstate(
          id: '',
          title: '',
          description: '',
          price: 0,
          location: '',
          photoUrls: [],
          isFeatured: false,
          status: 'متاح',
          mainCategory: 'residential',
          ownerId: ownerId,
          createdAt: now,
          startDate: now,
          endDate: endDate);
    }
  }
  
  /// تحويل من النموذج القديم (Estate) إلى النموذج الجديد (EstateBase)
  static EstateBase convertFromLegacyEstate(Map<String, dynamic> legacyEstate) {
    final mainCategory = legacyEstate['mainCategory'] as String?;
    String estateType = 'residential';
    
    // تحديد نوع العقار بناءً على التصنيف الرئيسي
    if (mainCategory != null) {
      if (mainCategory.contains('تجاري') || mainCategory.contains('commercial')) {
        estateType = 'commercial';
      } else if (mainCategory.contains('أرض') || mainCategory.contains('land')) {
        estateType = 'land';
      }
    }
    
    // إضافة نوع العقار إلى البيانات
    final updatedMap = Map<String, dynamic>.from(legacyEstate);
    updatedMap['estateType'] = estateType;
    
    // تحويل الحقول القديمة إلى الحقول الجديدة
    if (updatedMap['creationDate'] != null && updatedMap['createdAt'] == null) {
      updatedMap['createdAt'] = updatedMap['creationDate'];
    }
    
    if (updatedMap['userId'] != null && updatedMap['ownerId'] == null) {
      updatedMap['ownerId'] = updatedMap['userId'];
    }
    
    if (updatedMap['images'] != null && updatedMap['photoUrls'] == null) {
      updatedMap['photoUrls'] = updatedMap['images'];
    }
    
    if (updatedMap['isFeatured'] == null) {
      updatedMap['isFeatured'] = false;
    }
    
    if (updatedMap['status'] == null) {
      updatedMap['status'] = 'متاح';
    }
    
    return createFromMap(updatedMap);
  }
  
  /// إنشاء نسخة من العقار مع إضافة معلومات المعلن
  static EstateBase addAdvertiserInfo(EstateBase estate, Map<String, dynamic> advertiserInfo) {
    return estate.copyWithBase(
      advertiserName: advertiserInfo['fullName'],
      advertiserPhone: advertiserInfo['phoneNumber'],
      advertiserImage: advertiserInfo['profileImage'],
      advertiserType: advertiserInfo['userType'],
      advertiserJoinDate: EstateBase.timestampToDateTime(advertiserInfo['createdAt']),
      advertiserAdsCount: advertiserInfo['adsCount']);
  }
}
