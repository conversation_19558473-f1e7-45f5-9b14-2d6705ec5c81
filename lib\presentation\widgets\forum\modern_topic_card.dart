import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/topic_model.dart';
import '../../../domain/models/forum/forum_topic_model.dart';

/// بطاقة موضوع المنتدى الحديثة
class ModernTopicCard extends StatelessWidget {
  /// موضوع المنتدى
  final dynamic topic;

  /// دالة يتم استدعاؤها عند النقر على الموضوع
  final VoidCallback onTap;

  /// دالة يتم استدعاؤها عند النقر على زر الإعجاب
  final VoidCallback? onLikeTap;

  /// دالة يتم استدعاؤها عند النقر على زر الحفظ
  final VoidCallback? onBookmarkTap;

  /// دالة يتم استدعاؤها عند النقر على زر المشاركة
  final VoidCallback? onShareTap;

  /// ما إذا كان المستخدم الحالي قد أعجب بالموضوع
  final bool isLiked;

  /// ما إذا كان المستخدم الحالي قد حفظ الموضوع
  final bool isBookmarked;

  /// ما إذا كان العرض مصغراً
  final bool isCompact;

  const ModernTopicCard({
    super.key,
    required this.topic,
    required this.onTap,
    this.onLikeTap,
    this.onBookmarkTap,
    this.onShareTap,
    this.isLiked = false,
    this.isBookmarked = false,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    // التعامل مع نوعي الموضوع (TopicModel و ForumTopic)
    final TopicModel topicModel = topic is TopicModel
        ? topic
        : (topic is ForumTopic ? topic.model : null);

    final hasImages = topicModel.images != null && topicModel.images!.isNotEmpty;
    final isPinned = topicModel.status == TopicStatus.pinned ||
                     topicModel.status == TopicStatus.pinnedAndFeatured ||
                     topicModel.status == TopicStatus.closedAndPinned;
    final isFeatured = topicModel.status == TopicStatus.featured ||
                       topicModel.status == TopicStatus.pinnedAndFeatured;
    final isClosed = topicModel.status == TopicStatus.closed ||
                     topicModel.status == TopicStatus.closedAndPinned;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0),
        ]),
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: onTap,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: isFeatured
                    ? AppColors.primary.withOpacity(0.3)
                    : Colors.grey.shade200,
                width: isFeatured ? 1.5 : 0.5),
              borderRadius: BorderRadius.circular(16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس الموضوع
                _buildTopicHeader(topicModel, isPinned, isFeatured, isClosed),

                // محتوى الموضوع
                if (!isCompact) _buildTopicContent(topicModel),

                // صور الموضوع
                if (hasImages && !isCompact) _buildTopicImages(topicModel),

                // تذييل الموضوع
                _buildTopicFooter(topicModel),
              ])))));
  }

  /// بناء رأس الموضوع
  Widget _buildTopicHeader(TopicModel topic, bool isPinned, bool isFeatured, bool isClosed) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 14, 16, 10),
      decoration: BoxDecoration(
        color: isFeatured ? AppColors.primary.withOpacity(0.05) : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16)),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade100,
            width: 1))),
      child: Row(
        children: [
          // صورة المستخدم
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.1),
                  blurRadius: 5,
                  offset: const Offset(0, 2)),
              ]),
            child: CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.primary.withOpacity(0.1),
              backgroundImage: topic.userImage != null
                  ? CachedNetworkImageProvider(topic.userImage!)
                  : null,
              child: topic.userImage == null
                  ? Text(
                      topic.userName.isNotEmpty ? topic.userName[0].toUpperCase() : '?',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold))
                  : null)),
          const SizedBox(width: 12),

          // معلومات المستخدم والوقت
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      topic.userName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14)),
                    const SizedBox(width: 8),
                    Text(
                      timeago.format(topic.createdAt, locale: 'ar'),
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12)),
                  ]),
                if (topic.categoryName.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    topic.categoryName,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12)),
                ],
              ])),

          // مؤشرات الحالة
          if (isPinned)
            Tooltip(
              message: 'مثبت',
              child: Icon(
                Icons.push_pin,
                color: AppColors.warning,
                size: 16)),
          if (isFeatured) ...[
            const SizedBox(width: 4),
            Tooltip(
              message: 'مميز',
              child: Icon(
                Icons.star,
                color: AppColors.warning,
                size: 16)),
          ],
          if (isClosed) ...[
            const SizedBox(width: 4),
            Tooltip(
              message: 'مغلق',
              child: Icon(
                Icons.lock,
                color: AppColors.error,
                size: 16)),
          ],
        ]));
  }

  /// بناء محتوى الموضوع
  Widget _buildTopicContent(TopicModel topic) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الموضوع
          Text(
            topic.title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              height: 1.3),
            maxLines: 2,
            overflow: TextOverflow.ellipsis),
          const SizedBox(height: 8),

          // محتوى الموضوع
          Text(
            topic.content,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade800,
              height: 1.4),
            maxLines: 3,
            overflow: TextOverflow.ellipsis),
        ]));
  }

  /// بناء صور الموضوع
  Widget _buildTopicImages(TopicModel topic) {
    if (topic.images == null || topic.images!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 180,
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: topic.images!.length == 1
            ? _buildSingleImage(topic.images!.first)
            : _buildMultipleImages(topic.images!)));
  }

  /// بناء صورة واحدة
  Widget _buildSingleImage(String imageUrl) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      width: double.infinity,
      placeholder: (context, url) => Container(
        color: Colors.grey.shade200,
        child: const Center(
          child: CircularProgressIndicator())),
      errorWidget: (context, url, error) => Container(
        color: Colors.grey.shade200,
        child: const Icon(Icons.error)));
  }

  /// بناء صور متعددة
  Widget _buildMultipleImages(List<String> images) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: _buildSingleImage(images.first)),
        if (images.length > 1) ...[
          const SizedBox(width: 4),
          Expanded(
            flex: 2,
            child: Column(
              children: [
                Expanded(
                  child: _buildSingleImage(images[1])),
                if (images.length > 2) ...[
                  const SizedBox(height: 4),
                  Expanded(
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        _buildSingleImage(images[2]),
                        if (images.length > 3)
                          Container(
                            color: Colors.black.withOpacity(0.5),
                            child: Center(
                              child: Text(
                                '+${images.length - 3}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18)))),
                      ])),
                ],
              ])),
        ],
      ]);
  }

  /// بناء تذييل الموضوع
  Widget _buildTopicFooter(TopicModel topic) {
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 10, 12, 14),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade100,
            width: 1))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // الإحصائيات (المشاهدات والردود)
          Row(
            children: [
              // عدد المشاهدات
              _buildStatItem(
                Icons.remove_red_eye_outlined,
                '${topic.viewsCount}',
                label: 'مشاهدة'),
              const SizedBox(width: 16),

              // عدد الردود
              _buildStatItem(
                Icons.forum_outlined,
                '${topic.repliesCount}',
                label: 'رد'),
            ]),

          // الأزرار (الإعجاب والحفظ والمشاركة)
          Row(
            children: [
              // زر الإعجاب
              _buildActionButton(
                icon: isLiked ? Icons.favorite : Icons.favorite_border,
                label: '${topic.likesCount}',
                color: isLiked ? Colors.red : null,
                onTap: onLikeTap),

              const SizedBox(width: 8),

              // زر الحفظ
              _buildActionButton(
                icon: isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                color: isBookmarked ? AppColors.primary : null,
                onTap: onBookmarkTap),

              const SizedBox(width: 8),

              // زر المشاركة
              _buildActionButton(
                icon: Icons.share_outlined,
                onTap: onShareTap),
            ]),
        ]));
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(IconData icon, String count, {String? label}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(
          count,
          style: TextStyle(
            color: Colors.grey.shade700,
            fontSize: 12,
            fontWeight: FontWeight.bold)),
        if (label != null) ...[
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 12)),
        ],
      ]);
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    String? label,
    Color? color,
    VoidCallback? onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          child: Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: color ?? Colors.grey.shade600),
              if (label != null) ...[
                const SizedBox(width: 4),
                Text(
                  label,
                  style: TextStyle(
                    color: color ?? Colors.grey.shade700,
                    fontSize: 12,
                    fontWeight: FontWeight.bold)),
              ],
            ]))));
  }
}
