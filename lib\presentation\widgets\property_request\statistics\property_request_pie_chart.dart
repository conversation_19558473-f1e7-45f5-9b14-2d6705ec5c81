// lib/presentation/widgets/property_request/statistics/property_request_pie_chart.dart
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:google_fonts/google_fonts.dart';

/// مخطط دائري لإحصائيات طلبات العقارات
class PropertyRequestPieChart extends StatefulWidget {
  /// أقسام المخطط
  final List<PieChartSectionData> sections;

  /// إنشاء مخطط دائري لإحصائيات طلبات العقارات
  const PropertyRequestPieChart({
    super.key,
    required this.sections,
  });

  @override
  State<PropertyRequestPieChart> createState() => _PropertyRequestPieChartState();
}

class _PropertyRequestPieChartState extends State<PropertyRequestPieChart> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Pie<PERSON><PERSON>(
            PieChartData(
              pieTouchData: PieTouchData(
                touchCallback: (FlTouchEvent event, pieTouchResponse) {
                  setState(() {
                    if (!event.isInterestedForInteractions ||
                        pieTouchResponse == null ||
                        pieTouchResponse.touchedSection == null) {
                      touchedIndex = -1;
                      return;
                    }
                    touchedIndex = pieTouchResponse.touchedSection!.touchedSectionIndex;
                  });
                }),
              borderData: FlBorderData(show: false),
              sectionsSpace: 2,
              centerSpaceRadius: 0,
              sections: _buildSections()))),
        const SizedBox(width: 16),
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: _buildIndicators()),
      ]);
  }

  /// بناء أقسام المخطط
  List<PieChartSectionData> _buildSections() {
    return List.generate(widget.sections.length, (i) {
      final isTouched = i == touchedIndex;
      final section = widget.sections[i];
      
      return PieChartSectionData(
        color: section.color,
        value: section.value,
        title: isTouched ? '${section.value.toInt()}' : '',
        radius: isTouched ? section.radius * 1.1 : section.radius,
        titleStyle: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.white),
        badgeWidget: isTouched
            ? Text(
                section.title,
                style: GoogleFonts.cairo(
                  color: section.color,
                  fontSize: 14,
                  fontWeight: FontWeight.bold))
            : null,
        badgePositionPercentageOffset: 1.1);
    });
  }

  /// بناء مؤشرات المخطط
  List<Widget> _buildIndicators() {
    return List.generate(widget.sections.length, (i) {
      final section = widget.sections[i];
      
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: section.color)),
            const SizedBox(width: 8),
            Text(
              '${section.title} (${section.value.toInt()})',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.bold)),
          ]));
    });
  }
}
