import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// نوع الوثيقة
enum DocumentType {
  blueprint,    // مخطط
  contract,     // عقد
  permit,       // تصريح
  report,       // تقرير
  image,        // صورة
  pdf,          // ملف PDF
  drawing,      // رسم
  specification, // مواصفات
  invoice,      // فاتورة
  other,        // أخرى
}

/// حالة الوثيقة
enum DocumentStatus {
  draft,        // مسودة
  pending,      // معلقة
  approved,     // معتمدة
  rejected,     // مرفوضة
  archived,     // مؤرشفة
}

/// نموذج وثيقة المشروع
class ProjectDocument extends Equatable {
  /// معرف الوثيقة
  final String id;
  
  /// معرف المشروع
  final String projectId;
  
  /// اسم الوثيقة
  final String name;
  
  /// وصف الوثيقة
  final String description;
  
  /// نوع الوثيقة
  final DocumentType type;
  
  /// حالة الوثيقة
  final DocumentStatus status;
  
  /// رابط الملف
  final String fileUrl;
  
  /// اسم الملف الأصلي
  final String fileName;
  
  /// حجم الملف (بالبايت)
  final int fileSize;
  
  /// نوع MIME للملف
  final String mimeType;
  
  /// معرف الشخص الذي رفع الوثيقة
  final String uploadedById;
  
  /// اسم الشخص الذي رفع الوثيقة
  final String uploadedByName;
  
  /// معرف الشخص الذي اعتمد الوثيقة
  final String? approvedById;
  
  /// اسم الشخص الذي اعتمد الوثيقة
  final String? approvedByName;
  
  /// تاريخ الاعتماد
  final DateTime? approvedDate;
  
  /// رقم الإصدار
  final int version;
  
  /// العلامات
  final List<String> tags;
  
  /// الفئة
  final String? category;
  
  /// ملاحظات
  final String? notes;
  
  /// تاريخ الرفع
  final DateTime uploadedAt;
  
  /// تاريخ آخر تحديث
  final DateTime updatedAt;
  
  /// معلومات إضافية
  final Map<String, dynamic>? additionalInfo;

  const ProjectDocument({
    required this.id,
    required this.projectId,
    required this.name,
    required this.description,
    required this.type,
    required this.status,
    required this.fileUrl,
    required this.fileName,
    required this.fileSize,
    required this.mimeType,
    required this.uploadedById,
    required this.uploadedByName,
    this.approvedById,
    this.approvedByName,
    this.approvedDate,
    this.version = 1,
    this.tags = const [],
    this.category,
    this.notes,
    required this.uploadedAt,
    required this.updatedAt,
    this.additionalInfo,
  });

  /// إنشاء نسخة معدلة من الوثيقة
  ProjectDocument copyWith({
    String? id,
    String? projectId,
    String? name,
    String? description,
    DocumentType? type,
    DocumentStatus? status,
    String? fileUrl,
    String? fileName,
    int? fileSize,
    String? mimeType,
    String? uploadedById,
    String? uploadedByName,
    String? approvedById,
    String? approvedByName,
    DateTime? approvedDate,
    int? version,
    List<String>? tags,
    String? category,
    String? notes,
    DateTime? uploadedAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalInfo,
  }) {
    return ProjectDocument(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      fileUrl: fileUrl ?? this.fileUrl,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      uploadedById: uploadedById ?? this.uploadedById,
      uploadedByName: uploadedByName ?? this.uploadedByName,
      approvedById: approvedById ?? this.approvedById,
      approvedByName: approvedByName ?? this.approvedByName,
      approvedDate: approvedDate ?? this.approvedDate,
      version: version ?? this.version,
      tags: tags ?? this.tags,
      category: category ?? this.category,
      notes: notes ?? this.notes,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  /// تحويل الوثيقة إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'projectId': projectId,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'fileUrl': fileUrl,
      'fileName': fileName,
      'fileSize': fileSize,
      'mimeType': mimeType,
      'uploadedById': uploadedById,
      'uploadedByName': uploadedByName,
      'approvedById': approvedById,
      'approvedByName': approvedByName,
      'approvedDate': approvedDate != null ? Timestamp.fromDate(approvedDate!) : null,
      'version': version,
      'tags': tags,
      'category': category,
      'notes': notes,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalInfo': additionalInfo,
    };
  }

  /// إنشاء وثيقة من Map
  factory ProjectDocument.fromMap(Map<String, dynamic> map) {
    return ProjectDocument(
      id: map['id'] ?? '',
      projectId: map['projectId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      type: _getDocumentTypeFromString(map['type'] ?? 'other'),
      status: _getDocumentStatusFromString(map['status'] ?? 'draft'),
      fileUrl: map['fileUrl'] ?? '',
      fileName: map['fileName'] ?? '',
      fileSize: map['fileSize'] ?? 0,
      mimeType: map['mimeType'] ?? '',
      uploadedById: map['uploadedById'] ?? '',
      uploadedByName: map['uploadedByName'] ?? '',
      approvedById: map['approvedById'],
      approvedByName: map['approvedByName'],
      approvedDate: map['approvedDate'] is Timestamp 
          ? (map['approvedDate'] as Timestamp).toDate() 
          : null,
      version: map['version'] ?? 1,
      tags: List<String>.from(map['tags'] ?? []),
      category: map['category'],
      notes: map['notes'],
      uploadedAt: map['uploadedAt'] is Timestamp 
          ? (map['uploadedAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : DateTime.now(),
      additionalInfo: map['additionalInfo'],
    );
  }

  /// إنشاء وثيقة من DocumentSnapshot
  factory ProjectDocument.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return ProjectDocument.fromMap(data);
  }

  /// التحقق من كون الوثيقة صورة
  bool get isImage {
    return type == DocumentType.image || 
           mimeType.startsWith('image/');
  }

  /// التحقق من كون الوثيقة PDF
  bool get isPdf {
    return type == DocumentType.pdf || 
           mimeType == 'application/pdf';
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  @override
  List<Object?> get props => [
    id,
    projectId,
    name,
    description,
    type,
    status,
    fileUrl,
    fileName,
    fileSize,
    mimeType,
    uploadedById,
    uploadedByName,
    approvedById,
    approvedByName,
    approvedDate,
    version,
    tags,
    category,
    notes,
    uploadedAt,
    updatedAt,
    additionalInfo,
  ];
}

// Helper functions
DocumentType _getDocumentTypeFromString(String type) {
  switch (type) {
    case 'blueprint':
      return DocumentType.blueprint;
    case 'contract':
      return DocumentType.contract;
    case 'permit':
      return DocumentType.permit;
    case 'report':
      return DocumentType.report;
    case 'image':
      return DocumentType.image;
    case 'pdf':
      return DocumentType.pdf;
    case 'drawing':
      return DocumentType.drawing;
    case 'specification':
      return DocumentType.specification;
    case 'invoice':
      return DocumentType.invoice;
    default:
      return DocumentType.other;
  }
}

DocumentStatus _getDocumentStatusFromString(String status) {
  switch (status) {
    case 'draft':
      return DocumentStatus.draft;
    case 'pending':
      return DocumentStatus.pending;
    case 'approved':
      return DocumentStatus.approved;
    case 'rejected':
      return DocumentStatus.rejected;
    case 'archived':
      return DocumentStatus.archived;
    default:
      return DocumentStatus.draft;
  }
}
