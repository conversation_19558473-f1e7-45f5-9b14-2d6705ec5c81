import 'estate.dart';
import 'estate_base.dart';
import 'residential_estate.dart';
import 'commercial_estate.dart';
import 'land_estate.dart';
import 'estate_factory.dart';

/// محول بين النماذج القديمة والجديدة
class EstateConverter {
  /// تحويل من النموذج القديم (Estate) إلى النموذج الجديد (EstateBase)
  static EstateBase fromLegacyEstate(Estate estate) {
    final mainCategory = estate.mainCategory ?? 'residential';
    String estateType = 'residential';

    // تحديد نوع العقار بناءً على التصنيف الرئيسي
    if (mainCategory.contains('تجاري') || mainCategory.contains('commercial')) {
      estateType = 'commercial';
    } else if (mainCategory.contains('أرض') || mainCategory.contains('land')) {
      estateType = 'land';
    }

    // إنشاء Map من البيانات
    final Map<String, dynamic> data = {
      'id': estate.id,
      'title': estate.title,
      'description': estate.description,
      'price': estate.price,
      'location': estate.location,
      'photoUrls': estate.images,
      'isFeatured': estate.isFeatured ?? false,
      'status': 'متاح',
      'governorate': estate.governorate,
      'city': estate.city,
      'district': estate.district,
      'block': estate.block,
      'latitude': estate.lat,
      'longitude': estate.lng,
      'shareLocation': estate.shareLocation ?? false,
      'mainCategory': mainCategory,
      'subCategory': estate.subCategory,
      'ownerId': estate.userId,
      'advertiserName': estate.advertiserName,
      'advertiserPhone': estate.advertiserPhone,
      'advertiserImage': estate.advertiserImage,
      'advertiserType': estate.postedByUserType,
      'advertiserJoinDate': estate.advertiserRegistrationDate,
      'advertiserAdsCount': estate.advertiserAdsCount,
      'hidePhone': estate.hidePhone ?? false,
      'extraPhones': estate.extraPhones ?? [],
      'createdAt': estate.creationDate,
      'updatedAt': estate.updatedAt,
      'startDate': estate.startDate,
      'endDate': estate.endDate,
      'viewsCount': 0,
      'favoritesCount': 0,
      'contactCount': 0,
      'subscriptionPlan': estate.planType ?? 'free',
      'autoRepublish': estate.autoRepublish ?? false,
      'isPinned': estate.kuwaitCornersPin ?? false,
      'isPromoted': estate.movingAd ?? false,
      'isVIP': estate.vipBadge ?? false,
      'isVerified': false,
      'originalEstateId': estate.originalEstateId,
      'isOriginal': estate.isOriginal ?? true,
      'copiedBy': estate.copiedBy,
      'estateType': estateType,
    };

    // إضافة الخصائص الخاصة بكل نوع
    switch (estateType) {
      case 'residential':
        data['area'] = estate.area;
        data['numberOfRooms'] = estate.numberOfRooms;
        data['numberOfBathrooms'] = estate.numberOfBathrooms;
        data['numberOfLivingRooms'] =
            estate.salon != null ? int.tryParse(estate.salon!) : null;
        data['floorNumber'] = estate.floorNumber;
        data['buildingAge'] = estate.buildingAge;
        data['hasCentralAC'] = estate.hasCentralAC ?? false;
        data['hasMaidRoom'] = estate.hasMaidRoom ?? false;
        data['hasGarage'] = estate.hasGarage ?? false;
        data['hasBalcony'] = estate.hasBalcony ?? false;
        data['isFullyFurnished'] = estate.isFullyFurnished ?? false;
        data['hasElevator'] = estate.hasElevator ?? false;
        data['hasSwimmingPool'] = estate.hasSwimmingPool ?? false;
        data['hasSecurity'] = estate.hasSecurity ?? false;
        data['propertyType'] = estate.subCategory;
        break;
      case 'commercial':
        data['area'] = estate.area;
        data['commercialType'] = estate.subCategory;
        data['hasCentralAC'] = estate.hasCentralAC ?? false;
        data['hasParking'] = estate.hasGarage ?? false;
        data['streetWidth'] = estate.streetWidth;
        data['isFurnished'] = estate.isFullyFurnished ?? false;
        data['hasWaterAndElectricity'] = true;
        data['licenseType'] = null;
        data['commercialActivity'] = null;
        break;
      case 'land':
        data['area'] = estate.area;
        data['landType'] = estate.subCategory;
        data['streetWidth'] = estate.streetWidth;
        data['hasWaterAndElectricity'] = true;
        data['landShape'] = null;
        data['numberOfStreets'] = null;
        data['landDirection'] = null;
        data['isCornerLand'] = null;
        data['landNumber'] = null;
        data['planNumber'] = null;
        data['landUseType'] = null;
        data['hasMunicipality'] = null;
        break;
    }

    return EstateFactory.createFromMap(data);
  }

  /// تحويل من النموذج الجديد (EstateBase) إلى النموذج القديم (Estate)
  static Estate? toLegacyEstate(dynamic estate) {
    // التحقق من نوع الكائن
    if (estate is Estate) {
      return estate;
    }

    if (estate is! EstateBase) {
      return null;
    }
    try {
      // تنفيذ تحويل EstateBase إلى Estate
      return Estate(
        id: estate.id,
        title: estate.title,
        description: estate.description,
        price: estate.price,
        location: estate.location,
        photoUrls: estate.photoUrls,
        isFeatured: estate.isFeatured,
        planType: estate.subscriptionPlan,
        startDate: estate.startDate,
        endDate: estate.endDate,
        createdAt: estate.createdAt,
        mainCategory: estate.mainCategory,
        subCategory: estate.subCategory,
        postedByUserType: estate.advertiserType,
        hidePhone: estate.hidePhone,
        extraPhones: estate.extraPhones,
        shareLocation: estate.shareLocation,
        lat: estate.latitude,
        lng: estate.longitude,
        hasCentralAC: _getHasCentralACFromEstate(estate) ?? false,
        hasSecurity: _getHasSecurityFromEstate(estate),
        hasElevator: _getHasElevatorFromEstate(estate),
        hasSwimmingPool: _getHasSwimmingPoolFromEstate(estate),
        hasMaidRoom: _getHasMaidRoomFromEstate(estate) ?? false,
        hasGarage: _getHasGarageFromEstate(estate) ?? false,
        hasBalcony: _getHasBalconyFromEstate(estate),
        isFullyFurnished: _getIsFullyFurnishedFromEstate(estate),
        area: _getAreaFromEstate(estate),
        numberOfRooms: _getNumberOfRoomsFromEstate(estate),
        numberOfBathrooms: _getNumberOfBathroomsFromEstate(estate),
        salon: _getSalonFromEstate(estate),
        floorNumber: _getFloorNumberFromEstate(estate),
        buildingAge: _getBuildingAgeFromEstate(estate),
        // streetWidth is a getter in Estate, not a constructor parameter
        autoRepublish: estate.autoRepublish,
        kuwaitCornersPin: estate.isPinned,
        movingAd: estate.isPromoted,
        vipBadge: estate.isVIP,
        advertiserImage: estate.advertiserImage,
        advertiserName: estate.advertiserName,
        advertiserRegistrationDate: estate.advertiserJoinDate,
        advertiserAdsCount: estate.advertiserAdsCount ?? 0,
        ownerId: estate.ownerId,
        originalEstateId: estate.originalEstateId,
        isOriginal: estate.isOriginal,
        copiedBy: estate.copiedBy ?? []);
    } catch (e) {
      // تسجيل الخطأ (يمكن استخدام مكتبة تسجيل مناسبة في الإنتاج)
      // print("خطأ في تحويل EstateBase إلى Estate: $e");
      return null;
    }
  }

  // Helper methods to extract properties based on estate type
  static double? _getAreaFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.area;
    } else if (estate is CommercialEstate) {
      return estate.area;
    } else if (estate is LandEstate) {
      return estate.area;
    }
    return null;
  }

  static int? _getNumberOfRoomsFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.numberOfRooms;
    } else if (estate is CommercialEstate) {
      return estate.numberOfRooms;
    }
    return null;
  }

  static int? _getNumberOfBathroomsFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.numberOfBathrooms;
    } else if (estate is CommercialEstate) {
      return estate.numberOfBathrooms;
    }
    return null;
  }

  static String? _getSalonFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate && estate.numberOfLivingRooms != null) {
      return estate.numberOfLivingRooms.toString();
    }
    return null;
  }

  static int? _getFloorNumberFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.floorNumber;
    } else if (estate is CommercialEstate) {
      return estate.floorNumber;
    }
    return null;
  }

  static int? _getBuildingAgeFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.buildingAge;
    } else if (estate is CommercialEstate) {
      return estate.buildingAge;
    }
    return null;
  }

  static bool? _getHasCentralACFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.hasCentralAC;
    } else if (estate is CommercialEstate) {
      return estate.hasCentralAC;
    }
    return null;
  }

  static bool? _getHasMaidRoomFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.hasMaidRoom;
    }
    return null;
  }

  static bool? _getHasGarageFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.hasGarage;
    } else if (estate is CommercialEstate) {
      return estate.hasParking;
    }
    return null;
  }

  static bool? _getHasBalconyFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.hasBalcony;
    }
    return null;
  }

  static bool? _getIsFullyFurnishedFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.isFullyFurnished;
    } else if (estate is CommercialEstate) {
      return estate.isFurnished;
    }
    return null;
  }

  static bool? _getHasElevatorFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.hasElevator;
    } else if (estate is CommercialEstate) {
      return estate.hasElevator;
    }
    return null;
  }

  static bool? _getHasSwimmingPoolFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.hasSwimmingPool;
    }
    return null;
  }

  static bool? _getHasSecurityFromEstate(EstateBase estate) {
    if (estate is ResidentialEstate) {
      return estate.hasSecurity;
    } else if (estate is CommercialEstate) {
      return estate.hasSecurity;
    }
    return null;
  }

  // Helper method to get street width (used in toLegacyEstate)
  static String? _getStreetWidthFromEstate(EstateBase estate) {
    if (estate is CommercialEstate) {
      return estate.streetWidth;
    } else if (estate is LandEstate) {
      return estate.streetWidth;
    }
    return null;
  }
}
