import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج للتعليقات
class Comment extends Equatable {
  /// معرف التعليق
  final String id;
  
  /// معرف العنصر المعلق عليه (عقار، تقييم، تعليق)
  final String itemId;
  
  /// نوع العنصر المعلق عليه
  final String itemType;
  
  /// معرف المستخدم الذي قام بالتعليق
  final String userId;
  
  /// اسم المستخدم الذي قام بالتعليق
  final String userName;
  
  /// صورة المستخدم الذي قام بالتعليق
  final String? userImage;
  
  /// نص التعليق
  final String content;
  
  /// تاريخ إنشاء التعليق
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث للتعليق
  final DateTime? updatedAt;
  
  /// ما إذا كان التعليق تم الموافقة عليه
  final bool isApproved;
  
  /// ما إذا كان التعليق تم الإبلاغ عنه
  final bool isReported;
  
  /// عدد الإعجابات
  final int likesCount;
  
  /// عدد الردود
  final int repliesCount;
  
  /// قائمة معرفات المستخدمين الذين أعجبوا بالتعليق
  final List<String>? likedBy;
  
  /// معرف التعليق الأصلي (إذا كان هذا التعليق رداً على تعليق آخر)
  final String? parentId;
  
  /// صور مرفقة بالتعليق
  final List<String>? images;

  const Comment({
    required this.id,
    required this.itemId,
    required this.itemType,
    required this.userId,
    required this.userName,
    this.userImage,
    required this.content,
    required this.createdAt,
    this.updatedAt,
    this.isApproved = true,
    this.isReported = false,
    this.likesCount = 0,
    this.repliesCount = 0,
    this.likedBy,
    this.parentId,
    this.images,
  });

  /// إنشاء نسخة معدلة من التعليق
  Comment copyWith({
    String? id,
    String? itemId,
    String? itemType,
    String? userId,
    String? userName,
    String? userImage,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isApproved,
    bool? isReported,
    int? likesCount,
    int? repliesCount,
    List<String>? likedBy,
    String? parentId,
    List<String>? images,
  }) {
    return Comment(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      itemType: itemType ?? this.itemType,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isApproved: isApproved ?? this.isApproved,
      isReported: isReported ?? this.isReported,
      likesCount: likesCount ?? this.likesCount,
      repliesCount: repliesCount ?? this.repliesCount,
      likedBy: likedBy ?? this.likedBy,
      parentId: parentId ?? this.parentId,
      images: images ?? this.images);
  }
  
  /// تحويل التعليق إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'itemId': itemId,
      'itemType': itemType,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'content': content,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'isApproved': isApproved,
      'isReported': isReported,
      'likesCount': likesCount,
      'repliesCount': repliesCount,
      'likedBy': likedBy,
      'parentId': parentId,
      'images': images,
    };
  }
  
  /// إنشاء تعليق من Map
  factory Comment.fromMap(Map<String, dynamic> map) {
    return Comment(
      id: map['id'] ?? '',
      itemId: map['itemId'] ?? '',
      itemType: map['itemType'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'],
      content: map['content'] ?? '',
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
      isApproved: map['isApproved'] ?? true,
      isReported: map['isReported'] ?? false,
      likesCount: map['likesCount'] ?? 0,
      repliesCount: map['repliesCount'] ?? 0,
      likedBy: map['likedBy'] != null ? List<String>.from(map['likedBy']) : null,
      parentId: map['parentId'],
      images: map['images'] != null ? List<String>.from(map['images']) : null);
  }
  
  /// إنشاء تعليق من DocumentSnapshot
  factory Comment.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return Comment.fromMap(data);
  }
  
  /// إضافة إعجاب
  Comment addLike(String userId) {
    final newLikedBy = List<String>.from(likedBy ?? []);
    
    if (!newLikedBy.contains(userId)) {
      newLikedBy.add(userId);
    }
    
    return copyWith(
      likedBy: newLikedBy,
      likesCount: newLikedBy.length);
  }
  
  /// إزالة إعجاب
  Comment removeLike(String userId) {
    if (likedBy == null || !likedBy!.contains(userId)) {
      return this;
    }
    
    final newLikedBy = List<String>.from(likedBy!);
    newLikedBy.remove(userId);
    
    return copyWith(
      likedBy: newLikedBy,
      likesCount: newLikedBy.length);
  }
  
  /// زيادة عدد الردود
  Comment incrementReplies() {
    return copyWith(
      repliesCount: repliesCount + 1);
  }
  
  /// نقصان عدد الردود
  Comment decrementReplies() {
    return copyWith(
      repliesCount: repliesCount > 0 ? repliesCount - 1 : 0);
  }
  
  /// الإبلاغ عن التعليق
  Comment report() {
    return copyWith(
      isReported: true,
      updatedAt: DateTime.now());
  }
  
  /// الموافقة على التعليق
  Comment approve() {
    return copyWith(
      isApproved: true,
      updatedAt: DateTime.now());
  }
  
  /// رفض التعليق
  Comment reject() {
    return copyWith(
      isApproved: false,
      updatedAt: DateTime.now());
  }
  
  /// التحقق مما إذا كان التعليق رداً على تعليق آخر
  bool isReply() {
    return parentId != null && parentId!.isNotEmpty;
  }

  @override
  List<Object?> get props => [
    id,
    itemId,
    itemType,
    userId,
    userName,
    userImage,
    content,
    createdAt,
    updatedAt,
    isApproved,
    isReported,
    likesCount,
    repliesCount,
    likedBy,
    parentId,
    images,
  ];
}
