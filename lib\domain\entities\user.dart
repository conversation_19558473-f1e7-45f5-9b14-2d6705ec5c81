import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// أنواع المستخدمين
enum UserType {
  /// باحث عن عقار
  seeker,

  /// وسيط عقاري
  agent,

  /// مالك عقار
  owner,

  /// شركة عقارية
  company,
}

/// نموذج بيانات المستخدم
class User extends Equatable {
  /// معرف المستخدم
  final String id;

  /// الاسم الكامل أو اسم الشركة
  final String fullName;

  /// البريد الإلكتروني
  final String email;

  /// رقم الهاتف
  final String phoneNumber;

  /// صورة الملف الشخصي
  final String? profileImage;

  /// نوع المستخدم
  final UserType type;

  /// تاريخ إنشاء الحساب
  final DateTime createdAt;

  /// تاريخ آخر تسجيل دخول
  final DateTime? lastLoginAt;

  /// ما إذا كان الحساب مفعل
  final bool isActive;

  /// ما إذا كان البريد الإلكتروني مؤكد
  final bool isEmailVerified;

  /// ما إذا كان رقم الهاتف مؤكد
  final bool isPhoneVerified;

  /// معرف الاشتراك الحالي
  final String? subscriptionId;

  /// نوع الاشتراك الحالي
  final String? subscriptionType;

  /// تاريخ انتهاء الاشتراك
  final DateTime? subscriptionEndDate;

  /// ما إذا كان لديه اشتراك نشط
  final bool hasActiveSubscription;

  /// عدد الإعلانات المنشورة
  final int adsCount;

  /// عدد الإعلانات المفضلة
  final int favoritesCount;

  /// عدد مشاهدات الإعلانات
  final int viewsCount;

  /// معايير البحث المحفوظة (للباحثين عن العقارات)
  final List<Map<String, dynamic>>? savedSearches;

  /// قائمة العقارات المفضلة
  final List<String>? favoriteEstates;

  /// قائمة العقارات المقارنة
  final List<String>? compareEstates;

  /// قائمة العملاء (للوسطاء وشركات العقارات)
  final List<String>? clients;

  /// قائمة المواعيد (للوسطاء وشركات العقارات)
  final List<String>? appointments;

  /// قائمة أعضاء الفريق (لشركات العقارات)
  final List<String>? teamMembers;

  /// قائمة الحملات التسويقية (لشركات العقارات)
  final List<String>? marketingCampaigns;

  /// معلومات إضافية للوسيط
  final Map<String, dynamic>? agentInfo;

  /// معلومات إضافية للشركة
  final Map<String, dynamic>? companyInfo;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  const User({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    this.profileImage,
    required this.type,
    required this.createdAt,
    this.lastLoginAt,
    required this.isActive,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    this.subscriptionId,
    this.subscriptionType,
    this.subscriptionEndDate,
    required this.hasActiveSubscription,
    required this.adsCount,
    required this.favoritesCount,
    required this.viewsCount,
    this.savedSearches,
    this.favoriteEstates,
    this.compareEstates,
    this.clients,
    this.appointments,
    this.teamMembers,
    this.marketingCampaigns,
    this.agentInfo,
    this.companyInfo,
    this.metadata,
  });

  /// إنشاء نسخة معدلة من المستخدم
  User copyWith({
    String? id,
    String? fullName,
    String? email,
    String? phoneNumber,
    String? profileImage,
    UserType? type,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isActive,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    String? subscriptionId,
    String? subscriptionType,
    DateTime? subscriptionEndDate,
    bool? hasActiveSubscription,
    int? adsCount,
    int? favoritesCount,
    int? viewsCount,
    List<Map<String, dynamic>>? savedSearches,
    List<String>? favoriteEstates,
    List<String>? compareEstates,
    List<String>? clients,
    List<String>? appointments,
    List<String>? teamMembers,
    List<String>? marketingCampaigns,
    Map<String, dynamic>? agentInfo,
    Map<String, dynamic>? companyInfo,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImage: profileImage ?? this.profileImage,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      subscriptionId: subscriptionId ?? this.subscriptionId,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      subscriptionEndDate: subscriptionEndDate ?? this.subscriptionEndDate,
      hasActiveSubscription:
          hasActiveSubscription ?? this.hasActiveSubscription,
      adsCount: adsCount ?? this.adsCount,
      favoritesCount: favoritesCount ?? this.favoritesCount,
      viewsCount: viewsCount ?? this.viewsCount,
      savedSearches: savedSearches ?? this.savedSearches,
      favoriteEstates: favoriteEstates ?? this.favoriteEstates,
      compareEstates: compareEstates ?? this.compareEstates,
      clients: clients ?? this.clients,
      appointments: appointments ?? this.appointments,
      teamMembers: teamMembers ?? this.teamMembers,
      marketingCampaigns: marketingCampaigns ?? this.marketingCampaigns,
      agentInfo: agentInfo ?? this.agentInfo,
      companyInfo: companyInfo ?? this.companyInfo,
      metadata: metadata ?? this.metadata);
  }

  /// تحويل المستخدم إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'fullName': fullName,
      'email': email,
      'phoneNumber': phoneNumber,
      'profileImage': profileImage,
      'type': type.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastLoginAt':
          lastLoginAt != null ? Timestamp.fromDate(lastLoginAt!) : null,
      'isActive': isActive,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'subscriptionId': subscriptionId,
      'subscriptionType': subscriptionType,
      'subscriptionEndDate': subscriptionEndDate != null
          ? Timestamp.fromDate(subscriptionEndDate!)
          : null,
      'hasActiveSubscription': hasActiveSubscription,
      'adsCount': adsCount,
      'favoritesCount': favoritesCount,
      'viewsCount': viewsCount,
      'savedSearches': savedSearches,
      'favoriteEstates': favoriteEstates,
      'compareEstates': compareEstates,
      'clients': clients,
      'appointments': appointments,
      'teamMembers': teamMembers,
      'marketingCampaigns': marketingCampaigns,
      'agentInfo': agentInfo,
      'companyInfo': companyInfo,
      'metadata': metadata,
    };
  }

  /// إنشاء مستخدم من Map
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'] ?? '',
      fullName: map['fullName'] ?? '',
      email: map['email'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      profileImage: map['profileImage'],
      type: _getUserTypeFromString(map['type'] ?? 'seeker'),
      createdAt: map['createdAt'] is Timestamp
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      lastLoginAt: map['lastLoginAt'] is Timestamp
          ? (map['lastLoginAt'] as Timestamp).toDate()
          : null,
      isActive: map['isActive'] ?? true,
      isEmailVerified: map['isEmailVerified'] ?? false,
      isPhoneVerified: map['isPhoneVerified'] ?? false,
      subscriptionId: map['subscriptionId'],
      subscriptionType: map['subscriptionType'],
      subscriptionEndDate: map['subscriptionEndDate'] is Timestamp
          ? (map['subscriptionEndDate'] as Timestamp).toDate()
          : null,
      hasActiveSubscription: map['hasActiveSubscription'] ?? false,
      adsCount: map['adsCount'] ?? 0,
      favoritesCount: map['favoritesCount'] ?? 0,
      viewsCount: map['viewsCount'] ?? 0,
      savedSearches: map['savedSearches'] != null
          ? List<Map<String, dynamic>>.from(map['savedSearches'])
          : null,
      favoriteEstates: map['favoriteEstates'] != null
          ? List<String>.from(map['favoriteEstates'])
          : null,
      compareEstates: map['compareEstates'] != null
          ? List<String>.from(map['compareEstates'])
          : null,
      clients:
          map['clients'] != null ? List<String>.from(map['clients']) : null,
      appointments: map['appointments'] != null
          ? List<String>.from(map['appointments'])
          : null,
      teamMembers: map['teamMembers'] != null
          ? List<String>.from(map['teamMembers'])
          : null,
      marketingCampaigns: map['marketingCampaigns'] != null
          ? List<String>.from(map['marketingCampaigns'])
          : null,
      agentInfo: map['agentInfo'],
      companyInfo: map['companyInfo'],
      metadata: map['metadata']);
  }

  /// إنشاء مستخدم من DocumentSnapshot
  factory User.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return User.fromMap(data);
  }

  /// الحصول على نوع المستخدم من النص
  static UserType _getUserTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'agent':
        return UserType.agent;
      case 'owner':
        return UserType.owner;
      case 'company':
        return UserType.company;
      default:
        return UserType.seeker;
    }
  }

  /// الحصول على اسم نوع المستخدم بالعربية
  String getUserTypeName() {
    switch (type) {
      case UserType.agent:
        return 'مستثمر';
      case UserType.owner:
        return 'مالك عقار';
      case UserType.company:
        return 'شركة عقارية';
      case UserType.seeker:
        return 'باحث عن عقار';
    }
  }

  /// التحقق مما إذا كان المستخدم باحث عن عقار
  bool isSeeker() => type == UserType.seeker;

  /// التحقق مما إذا كان المستخدم مستثمر
  bool isAgent() => type == UserType.agent;

  /// التحقق مما إذا كان المستخدم مالك عقار
  bool isOwner() => type == UserType.owner;

  /// التحقق مما إذا كان المستخدم شركة عقارية
  bool isCompany() => type == UserType.company;

  /// التحقق مما إذا كان المستخدم يمكنه نشر إعلانات
  bool canPostAds() => type != UserType.seeker;

  // Getters for compatibility with other parts of the code
  String get name => fullName;
  double? get rating =>
      agentInfo != null ? (agentInfo!['rating'] as double?) : null;
  String? get title =>
      agentInfo != null ? (agentInfo!['title'] as String?) : null;
  String? get location =>
      agentInfo != null ? (agentInfo!['location'] as String?) : null;
  String? get company =>
      agentInfo != null ? (agentInfo!['company'] as String?) : null;

  @override
  List<Object?> get props => [
        id,
        fullName,
        email,
        phoneNumber,
        profileImage,
        type,
        createdAt,
        lastLoginAt,
        isActive,
        isEmailVerified,
        isPhoneVerified,
        subscriptionId,
        subscriptionType,
        subscriptionEndDate,
        hasActiveSubscription,
        adsCount,
        favoritesCount,
        viewsCount,
        savedSearches,
        favoriteEstates,
        compareEstates,
        clients,
        appointments,
        teamMembers,
        marketingCampaigns,
        agentInfo,
        companyInfo,
        metadata,
      ];
}
