import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../api/api_client.dart';
import '../../../core/errors/exceptions.dart';
import '../../../domain/entities/valuation.dart';
import '../../../domain/entities/valuation_request.dart';
import '../../../domain/entities/valuation_provider.dart';

/// خدمة التكامل مع خدمات التقييم العقاري
class ValuationService {
  final ApiClient _apiClient;
  final FlutterSecureStorage _secureStorage;
  final String _baseUrl;

  /// إنشاء خدمة التكامل مع خدمات التقييم
  ValuationService({
    required ApiClient apiClient,
    required FlutterSecureStorage secureStorage,
    String? baseUrl,
  })  : _apiClient = apiClient,
        _secureStorage = secureStorage,
        _baseUrl = baseUrl ?? 'https://api.realestate.com/valuation';

  /// الحصول على قائمة مزودي خدمات التقييم المعتمدين
  Future<List<ValuationProvider>> getValuationProviders() async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/providers',
        cache: true,
        cacheDuration: const Duration(hours: 24));

      if (response is List) {
        return response
            .map((provider) => ValuationProvider.fromJson(provider))
            .toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException('Valuation Service',
          'فشل في الحصول على قائمة مزودي خدمات التقييم: $e');
    }
  }

  /// طلب تقييم عقاري
  Future<ValuationRequest> requestValuation({
    required String userId,
    required String providerId,
    required String estateId,
    required String propertyType,
    required String area,
    required double size,
    required String address,
    double? latitude,
    double? longitude,
    int? rooms,
    int? bathrooms,
    int? age,
    bool? isFurnished,
    bool? isRenovated,
    List<String>? features,
    List<String>? photoUrls,
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.post(
        '$_baseUrl/requests',
        headers: {
          'Authorization': 'Bearer $authToken',
        },
        body: {
          'userId': userId,
          'providerId': providerId,
          'estateId': estateId,
          'propertyType': propertyType,
          'area': area,
          'size': size,
          'address': address,
          'latitude': latitude,
          'longitude': longitude,
          'rooms': rooms,
          'bathrooms': bathrooms,
          'age': age,
          'isFurnished': isFurnished,
          'isRenovated': isRenovated,
          'features': features,
          'photoUrls': photoUrls,
          'additionalInfo': additionalInfo,
        });

      return ValuationRequest.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Valuation Service', 'فشل في طلب التقييم العقاري: $e');
    }
  }

  /// الحصول على حالة طلب التقييم
  Future<ValuationRequest> getValuationRequestStatus(String requestId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/requests/$requestId',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      return ValuationRequest.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Valuation Service', 'فشل في الحصول على حالة طلب التقييم: $e');
    }
  }

  /// الحصول على طلبات التقييم للمستخدم
  Future<List<ValuationRequest>> getUserValuationRequests(String userId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/requests/user/$userId',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      if (response is List) {
        return response.map((req) => ValuationRequest.fromJson(req)).toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException(
          'Valuation Service', 'فشل في الحصول على طلبات التقييم للمستخدم: $e');
    }
  }

  /// الحصول على تقييم عقاري
  Future<Valuation?> getValuation(String valuationId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/valuations/$valuationId',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      return Valuation.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Valuation Service', 'فشل في الحصول على التقييم العقاري: $e');
    }
  }

  /// تحميل المستندات المطلوبة للتقييم
  Future<Map<String, String>> uploadValuationDocuments({
    required String requestId,
    required Map<String, dynamic> documents,
  }) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.upload(
        '$_baseUrl/requests/$requestId/documents',
        headers: {
          'Authorization': 'Bearer $authToken',
        },
        files: documents);

      return Map<String, String>.from(response);
    } catch (e) {
      throw IntegrationException(
          'Valuation Service', 'فشل في تحميل المستندات: $e');
    }
  }

  /// إلغاء طلب التقييم
  Future<void> cancelValuationRequest(String requestId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      await _apiClient.post(
        '$_baseUrl/requests/$requestId/cancel',
        headers: {
          'Authorization': 'Bearer $authToken',
        });
    } catch (e) {
      throw IntegrationException(
          'Valuation Service', 'فشل في إلغاء طلب التقييم: $e');
    }
  }

  /// الحصول على تفاصيل مزود خدمة التقييم
  Future<ValuationProvider> getProviderDetails(String providerId) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/providers/$providerId',
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return ValuationProvider.fromJson(response);
    } catch (e) {
      throw IntegrationException('Valuation Service',
          'فشل في الحصول على تفاصيل مزود خدمة التقييم: $e');
    }
  }

  /// الحصول على أسعار خدمات التقييم
  Future<Map<String, dynamic>> getValuationPrices({
    required String providerId,
    required String propertyType,
    required double size,
    String? area,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/providers/$providerId/prices',
        queryParameters: {
          'propertyType': propertyType,
          'size': size.toString(),
          'area': area,
        },
        cache: true,
        cacheDuration: const Duration(hours: 6));

      return response;
    } catch (e) {
      throw IntegrationException(
          'Valuation Service', 'فشل في الحصول على أسعار خدمات التقييم: $e');
    }
  }

  /// مقارنة التقييم الرسمي مع التقدير الآلي
  Future<Map<String, dynamic>> compareValuationWithEstimation({
    required String valuationId,
    required String estimationId,
  }) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/compare',
        headers: {
          'Authorization': 'Bearer $authToken',
        },
        queryParameters: {
          'valuationId': valuationId,
          'estimationId': estimationId,
        });

      return response;
    } catch (e) {
      throw IntegrationException(
          'Valuation Service', 'فشل في مقارنة التقييم مع التقدير: $e');
    }
  }

  /// التحقق من صحة شهادة التقييم
  Future<bool> verifyValuationCertificate({
    required String certificateId,
    required String verificationCode,
  }) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/certificates/verify',
        queryParameters: {
          'certificateId': certificateId,
          'verificationCode': verificationCode,
        });

      return response['isValid'] as bool;
    } catch (e) {
      throw IntegrationException(
          'Valuation Service', 'فشل في التحقق من صحة شهادة التقييم: $e');
    }
  }

  /// تنزيل شهادة التقييم
  Future<String> downloadValuationCertificate(String valuationId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/valuations/$valuationId/certificate',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      return response['certificateUrl'] as String;
    } catch (e) {
      throw IntegrationException(
          'Valuation Service', 'فشل في تنزيل شهادة التقييم: $e');
    }
  }
}

/// امتدادات لتسهيل استخدام خدمة التكامل مع خدمات التقييم
extension ValuationServiceExtensions on ValuationService {
  /// الحصول على وصف حالة طلب التقييم
  String getRequestStatusDescription(String status) {
    switch (status) {
      case 'pending':
        return 'قيد المراجعة';
      case 'approved':
        return 'تمت الموافقة';
      case 'rejected':
        return 'مرفوض';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'awaiting_payment':
        return 'في انتظار الدفع';
      case 'awaiting_documents':
        return 'في انتظار المستندات';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على وصف نوع التقييم
  String getValuationTypeDescription(String type) {
    switch (type) {
      case 'standard':
        return 'تقييم قياسي';
      case 'detailed':
        return 'تقييم مفصل';
      case 'express':
        return 'تقييم سريع';
      case 'mortgage':
        return 'تقييم للتمويل العقاري';
      case 'insurance':
        return 'تقييم للتأمين';
      case 'legal':
        return 'تقييم قانوني';
      default:
        return 'غير معروف';
    }
  }
}
