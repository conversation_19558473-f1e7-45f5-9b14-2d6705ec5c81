import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// أنواع الإشعارات
enum NotificationType {
  /// إشعار عام
  general,

  /// إشعار عقار جديد
  newEstate,

  /// إشعار تحديث عقار
  estateUpdate,

  /// إشعار رسالة جديدة
  newMessage,

  /// إشعار تحقق من الحساب
  accountVerification,

  /// إشعار عرض خاص
  specialOffer,

  /// إشعار تذكير
  reminder,

  /// إشعار نظام
  system
}

/// نموذج بيانات الإشعار
class NotificationModel {
  /// معرف الإشعار
  final String id;

  /// عنوان الإشعار
  final String title;

  /// محتوى الإشعار
  final String body;

  /// نوع الإشعار
  final NotificationType type;

  /// بيانات إضافية
  final Map<String, dynamic> data;

  /// تاريخ الإشعار
  final DateTime timestamp;

  /// ما إذا كان الإشعار مقروء
  bool isRead;

  /// معرف المستخدم
  final String userId;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    required this.timestamp,
    required this.isRead,
    required this.userId,
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return NotificationModel(
      id: doc.id,
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      type: _getNotificationTypeFromString(data['type'] ?? 'general'),
      data: Map<String, dynamic>.from(data['data'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      isRead: data['isRead'] ?? false,
      userId: data['userId'] ?? '');
  }

  /// تحويل النموذج إلى Map لتخزينه في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'body': body,
      'type': type.toString().split('.').last,
      'data': data,
      'timestamp': Timestamp.fromDate(timestamp),
      'isRead': isRead,
      'userId': userId,
    };
  }

  /// الحصول على نوع الإشعار من النص
  static NotificationType _getNotificationTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'newEstate':
        return NotificationType.newEstate;
      case 'estateUpdate':
        return NotificationType.estateUpdate;
      case 'newMessage':
        return NotificationType.newMessage;
      case 'accountVerification':
        return NotificationType.accountVerification;
      case 'specialOffer':
        return NotificationType.specialOffer;
      case 'reminder':
        return NotificationType.reminder;
      case 'system':
        return NotificationType.system;
      default:
        return NotificationType.general;
    }
  }

  /// الحصول على أيقونة الإشعار
  IconData getNotificationIcon() {
    switch (type) {
      case NotificationType.newEstate:
        return Icons.home;
      case NotificationType.estateUpdate:
        return Icons.update;
      case NotificationType.newMessage:
        return Icons.message;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      case NotificationType.specialOffer:
        return Icons.local_offer;
      case NotificationType.reminder:
        return Icons.alarm;
      case NotificationType.system:
        return Icons.system_update;
      default:
        return Icons.notifications;
    }
  }

  /// الحصول على لون الإشعار
  Color getNotificationColor() {
    switch (type) {
      case NotificationType.newEstate:
        return Colors.green;
      case NotificationType.estateUpdate:
        return Colors.blue;
      case NotificationType.newMessage:
        return Colors.orange;
      case NotificationType.accountVerification:
        return Colors.purple;
      case NotificationType.specialOffer:
        return Colors.red;
      case NotificationType.reminder:
        return Colors.amber;
      case NotificationType.system:
        return Colors.grey;
      default:
        return Colors.teal;
    }
  }
}

/// خدمة الإشعارات المبسطة
class NotificationServiceSimple {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // مفتاح تخزين إعدادات الإشعارات
  static const String _notificationSettingsKey = 'notification_settings';

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    // تحديث رمز الجهاز في Firestore
    await _updateDeviceToken();
  }

  /// تحديث رمز الجهاز في Firestore
  Future<void> _updateDeviceToken() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      final token = await _firebaseMessaging.getToken();
      if (token == null) {
        return;
      }

      // تحديث رمز الجهاز في وثيقة المستخدم
      await _firestore.collection('users').doc(user.uid).update({
        'deviceTokens': FieldValue.arrayUnion([token]),
        'lastTokenUpdate': FieldValue.serverTimestamp(),
      });

      // الاستماع لتغييرات رمز الجهاز
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _updateDeviceToken();
      });
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// تعليم الإشعار كمقروء (خاص)
  Future<void> _markNotificationAsRead(String notificationId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc(notificationId)
          .update({
        'isRead': true,
      });
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// تعليم الإشعار كمقروء (عام)
  Future<void> markNotificationAsRead(String notificationId) async {
    await _markNotificationAsRead(notificationId);
  }

  /// الحصول على قائمة الإشعارات
  Future<List<NotificationModel>> getNotifications({int limit = 20}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return [];
      }

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => NotificationModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadNotificationsCount() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return 0;
      }

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .where('isRead', isEqualTo: false)
          .count()
          .get();

      return snapshot.count ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// تعليم جميع الإشعارات كمقروءة
  Future<void> markAllNotificationsAsRead() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      final batch = _firestore.batch();

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .where('isRead', isEqualTo: false)
          .get();

      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {'isRead': true});
      }

      await batch.commit();
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc(notificationId)
          .delete();
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// حذف جميع الإشعارات
  Future<void> deleteAllNotifications() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      final batch = _firestore.batch();

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .get();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      // تجاهل الخطأ
    }
  }
}
