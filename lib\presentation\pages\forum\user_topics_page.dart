import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../domain/models/forum/topic_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/empty_view.dart';

/// صفحة مواضيع المستخدم
class UserTopicsPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/user-topics';

  /// معرف المستخدم (اختياري، إذا كان فارغاً سيتم استخدام المستخدم الحالي)
  final String? userId;

  const UserTopicsPage({super.key, this.userId});

  @override
  State<UserTopicsPage> createState() => _UserTopicsPageState();
}

class _UserTopicsPageState extends State<UserTopicsPage> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;
  List<TopicModel> _topics = [];
  String? _userId;
  String _userName = 'المستخدم';
  bool _hasMoreTopics = true;
  DocumentSnapshot? _lastTopicDocument;

  @override
  void initState() {
    super.initState();

    // إضافة مستمع للتمرير لتحميل المزيد من المواضيع
    _scrollController.addListener(_scrollListener);

    // جلب البيانات عند تحميل الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData({bool refresh = false}) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // تحديد معرف المستخدم
    _userId = widget.userId ?? authProvider.user?.uid;

    if (_userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يجب تسجيل الدخول لعرض المواضيع')));
      Navigator.pop(context);
      return;
    }

    if (refresh) {
      setState(() {
        _topics = [];
        _hasMoreTopics = true;
        _lastTopicDocument = null;
      });
    }

    if (!_hasMoreTopics) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);

      // جلب مواضيع المستخدم
      final topics = await forumProvider.getUserTopics(
        _userId!,
        limit: 10,
        startAfter: _lastTopicDocument);

      if (topics.isNotEmpty) {
        setState(() {
          _topics.addAll(topics);
          _lastTopicDocument = null; // تحديث آخر وثيقة للصفحة التالية

          // تحديث اسم المستخدم من أول موضوع
          if (_topics.isNotEmpty) {
            _userName = _topics.first.userName;
          }
        });
      } else {
        setState(() {
          _hasMoreTopics = false;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تحميل المواضيع')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// مستمع التمرير لتحميل المزيد من المواضيع
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMoreTopics) {
        _loadData();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isCurrentUser = widget.userId == null ||
        (Provider.of<AuthProvider>(context).isLoggedIn &&
            Provider.of<AuthProvider>(context).user!.uid == widget.userId);

    return Scaffold(
      appBar: AppBar(
        title: Text(isCurrentUser ? 'مواضيعي' : 'مواضيع $_userName')),
      body: _isLoading && _topics.isEmpty
          ? Center(child: LoadingIndicator())
          : _topics.isEmpty
              ? EmptyView(
                  message: isCurrentUser
                      ? 'لم تقم بإنشاء أي مواضيع بعد'
                      : 'لا توجد مواضيع لهذا المستخدم',
                  icon: Icons.forum_outlined)
              : RefreshIndicator(
                  onRefresh: () => _loadData(refresh: true),
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.all(8),
                    itemCount: _topics.length + (_hasMoreTopics ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == _topics.length) {
                        return Center(
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: LoadingIndicator()));
                      }

                      final topic = _topics[index];
                      return _buildTopicItem(topic, isCurrentUser);
                    })),
      floatingActionButton: isCurrentUser
          ? FloatingActionButton(
              onPressed: () {
                // التنقل إلى صفحة إنشاء موضوع جديد
                Navigator.pushNamed(context, '/forum/create-topic')
                    .then((_) => _loadData(refresh: true));
              },
              tooltip: 'إنشاء موضوع جديد',
              child: Icon(Icons.add))
          : null);
  }

  /// بناء عنصر الموضوع
  Widget _buildTopicItem(TopicModel topic, bool isCurrentUser) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 4),
      child: InkWell(
        onTap: () {
          // التنقل إلى صفحة الموضوع
          Navigator.pushNamed(
            context,
            '/forum/topic',
            arguments: topic.id).then((_) => _loadData(refresh: true));
        },
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundImage: topic.userImage != null
                        ? NetworkImage(topic.userImage!)
                        : null,
                    radius: 16,
                    child: topic.userImage == null
                        ? Icon(Icons.person, size: 16)
                        : null),
                  SizedBox(width: 8),
                  Text(
                    topic.userName,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14)),
                  Spacer(),
                  Text(
                    _formatDate(topic.createdAt),
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 12)),
                ]),
              SizedBox(height: 8),
              Text(
                topic.title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16),
                maxLines: 2,
                overflow: TextOverflow.ellipsis),
              SizedBox(height: 4),
              Text(
                topic.content,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.black87),
                maxLines: 2,
                overflow: TextOverflow.ellipsis),
              SizedBox(height: 8),
              Row(
                children: [
                  Chip(
                    label: Text(topic.categoryName),
                    backgroundColor: Colors.grey[200],
                    labelStyle: TextStyle(fontSize: 12),
                    padding: EdgeInsets.zero,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap),
                  Spacer(),
                  _buildTopicStats(topic),
                ]),
              if (isCurrentUser) ...[
                SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      icon: Icon(Icons.edit),
                      label: Text('تعديل'),
                      onPressed: () {
                        // التنقل إلى صفحة تعديل الموضوع
                        Navigator.pushNamed(
                          context,
                          '/forum/create-topic',
                          arguments: topic.id).then((_) => _loadData(refresh: true));
                      }),
                    SizedBox(width: 8),
                    TextButton.icon(
                      icon: Icon(Icons.delete, color: Colors.red),
                      label: Text(
                        'حذف',
                        style: TextStyle(color: Colors.red)),
                      onPressed: () => _confirmDeleteTopic(topic)),
                  ]),
              ],
            ]))));
  }

  /// بناء إحصائيات الموضوع
  Widget _buildTopicStats(TopicModel topic) {
    return Row(
      children: [
        Icon(Icons.remove_red_eye, size: 16, color: Colors.grey),
        SizedBox(width: 4),
        Text(
          '${topic.viewsCount}',
          style: TextStyle(fontSize: 12, color: Colors.grey)),
        SizedBox(width: 8),
        Icon(Icons.forum, size: 16, color: Colors.grey),
        SizedBox(width: 4),
        Text(
          '${topic.repliesCount}',
          style: TextStyle(fontSize: 12, color: Colors.grey)),
        SizedBox(width: 8),
        Icon(Icons.favorite, size: 16, color: Colors.grey),
        SizedBox(width: 4),
        Text(
          '${topic.likesCount}',
          style: TextStyle(fontSize: 12, color: Colors.grey)),
      ]);
  }

  /// تأكيد حذف الموضوع
  Future<void> _confirmDeleteTopic(TopicModel topic) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف الموضوع'),
        content: Text('هل أنت متأكد من حذف الموضوع "${topic.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء')),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'حذف',
              style: TextStyle(color: Colors.red))),
        ]));

    if (confirmed == true) {
      _deleteTopic(topic.id);
    }
  }

  /// حذف الموضوع
  Future<void> _deleteTopic(String topicId) async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    try {
      final success = await forumProvider.deleteTopic(topicId);

      if (success) {
        setState(() {
          _topics.removeWhere((topic) => topic.id == topicId);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم حذف الموضوع بنجاح')));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء حذف الموضوع')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء حذف الموضوع')));
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} سنة';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} شهر';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
