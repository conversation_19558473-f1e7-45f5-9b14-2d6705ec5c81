import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/services/discount_code_service.dart';

import 'package:kuwait_corners/presentation/widgets/ad_creation_progress_indicator.dart';
import '../bloc/improved_ad_bloc.dart';

import 'home_page.dart'; // الصفحة الرئيسية أو الصفحة الأساسية.

/// صفحة مراجعة الفاتورة والدفع للإعلان.
/// تعرض ملخص تفاصيل الإعلان مع حساب التكلفة الإجمالية بما في ذلك الميزات الإضافية والخصومات،
/// وتتيح للمستخدم الانتقال إلى صفحة الدفع.
class AdInvoicePage extends StatefulWidget {
  const AdInvoicePage({super.key});

  @override
  State<AdInvoicePage> createState() => _AdInvoicePageState();
}

class _AdInvoicePageState extends State<AdInvoicePage> {
  // خدمة التحقق من كود الخصم
  final DiscountCodeService _discountService = DiscountCodeService();

  // حالة التحقق من كود الخصم
  bool _isValidatingCode = false;
  String? _discountErrorMessage;
  Map<String, dynamic>? _validDiscountInfo;

  // حقل إدخال كود الخصم
  final TextEditingController _discountCodeController = TextEditingController();

  @override
  void dispose() {
    _discountCodeController.dispose();
    super.dispose();
  }

  // دالة للتحقق من صلاحية كود الخصم
  Future<void> _validateDiscountCode() async {
    final code = _discountCodeController.text.trim();
    if (code.isEmpty) {
      setState(() {
        _discountErrorMessage = "يرجى إدخال كود الخصم";
      });
      return;
    }

    setState(() {
      _isValidatingCode = true;
      _discountErrorMessage = null;
      _validDiscountInfo = null;
    });

    try {
      final discountInfo = await _discountService.validateDiscountCode(code);

      setState(() {
        _isValidatingCode = false;
        if (discountInfo == null) {
          _discountErrorMessage = "كود الخصم غير صالح أو منتهي الصلاحية";
        } else {
          _validDiscountInfo = discountInfo;
        }
      });
    } catch (e) {
      setState(() {
        _isValidatingCode = false;
        _discountErrorMessage = "حدث خطأ أثناء التحقق من كود الخصم";
      });
    }
  }

  // دالة لاختيار أيقونة معبرة بناءً على عنوان البند.
  IconData getItemIcon(String title) {
    if (title.contains("الباقة")) {
      return Icons.credit_card;
    } else if (title.contains("تثبيت")) {
      return Icons.push_pin;
    } else if (title.contains("إعلان VIP")) {
      return Icons.star;
    } else if (title.contains("متحرك")) {
      return Icons.slideshow;
    } else if (title.contains("إعادة نشر")) {
      return Icons.autorenew;
    } else if (title.contains("خصم")) {
      return Icons.discount;
    }
    return Icons.receipt_long;
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على الحالة الحالية من Bloc.
    final state = context.watch<ImprovedAdBloc>().state;

    // حساب التكلفة النهائية.
    double total = state.planPrice; // بدايةً بسعر الباقة.
    final items = <String, double>{}; // خريطة لتفاصيل كل بند.

    items["سعر الباقة (${state.planId})"] = state.planPrice;

    // إضافة التكلفة الخاصة بكل ميزة إضافية.
    if (state.kuwaitCornersPin) {
      items["تثبيت في إدارة أملاك الغير"] = 2.0;
      total += 2.0;
    }
    if (state.movingAd) {
      items["إعلان متحرك"] = 1.0;
      total += 1.0;
    }
    if (state.vipBadge) {
      items["إعلان VIP"] = 5.0;
      total += 5.0;
    }
    if (state.pinnedOnHome) {
      items["تثبيت بالصفحة الرئيسية"] = 7.0;
      total += 7.0;
    }
    if (state.autoRepublish) {
      items["إعادة نشر تلقائي"] = 3.0;
      total += 3.0;
    }

    // تطبيق الخصم إذا تم تقديم كود خصم.
    if (state.discountCode != null && state.discountCode!.isNotEmpty) {
      if (state.discountCode!.toLowerCase() == "kuwait10") {
        final discountValue = total * 0.10;
        items["خصم (kuwait10)"] = -discountValue;
        total -= discountValue;
      }
    }

    // في حال نجاح إنشاء الإعلان، عرض رسالة نجاح وتحويل المستخدم للصفحة الرئيسية بعد 3 ثوانٍ.
    if (state.isSuccess) {
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (_) => const HomePage()),
            (route) => false);
        }
      });
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          "مراجعة الفاتورة والدفع",
          style: TextStyle(fontSize: 18, color: Colors.black)),
        iconTheme: const IconThemeData(color: Colors.black)),
      // إضافة مؤشر التقدم أسفل الصفحة
      bottomNavigationBar: AdCreationProgressIndicator(currentStep: 7),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // عرض رسالة الخطأ إن وجدت.
              if (state.errorMessage != null && state.errorMessage!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    "خطأ: ${state.errorMessage}",
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                        color: Colors.red, fontWeight: FontWeight.bold))),
              const SizedBox(height: 16),
              // قسم ملخص الإعلان مع أيقونات معبرة.
              Card(
                color: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                margin: const EdgeInsets.symmetric(vertical: 8),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading: const Icon(Icons.title, color: Colors.black54),
                        title: Text("العنوان: ${state.title}",
                            style: const TextStyle(fontSize: 16))),
                      const SizedBox(height: 8),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading: const Icon(Icons.attach_money,
                            color: Colors.black54),
                        title: Text("السعر الأساسي (د.ك): ${state.price}",
                            style: const TextStyle(fontSize: 16))),
                      const SizedBox(height: 8),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading: const Icon(Icons.credit_card,
                            color: Colors.black54),
                        title: Text("الباقة المختارة: ${state.planId}",
                            style: const TextStyle(fontSize: 16))),
                    ]))),
              const SizedBox(height: 16),
              // قسم تفاصيل الفاتورة مع أيقونات لكل بند.
              Card(
                color: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                margin: const EdgeInsets.symmetric(vertical: 8),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      ...items.entries.map(
                        (e) => ListTile(
                          contentPadding: EdgeInsets.zero,
                          leading:
                              Icon(getItemIcon(e.key), color: Colors.black54),
                          title: Text(
                            e.key,
                            style: const TextStyle(fontSize: 15)),
                          trailing: Text(
                            e.value >= 0 ? "+${e.value} د.ك" : "${e.value} د.ك",
                            style: const TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 15)))),
                      const Divider(),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading:
                            const Icon(Icons.calculate, color: Colors.black54),
                        title: const Text(
                          "الإجمالي",
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold)),
                        trailing: Text(
                          "${total.toStringAsFixed(2)} د.ك",
                          style: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold))),
                    ]))),
              const SizedBox(height: 24),

              // قسم إدخال كود الخصم
              if (!state.isSuccess)
                Card(
                  color: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const Text(
                          "هل لديك كود خصم؟",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold)),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _discountCodeController,
                                decoration: const InputDecoration(
                                  hintText: "أدخل كود الخصم",
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8)))),
                            const SizedBox(width: 8),
                            ElevatedButton(
                              onPressed: _isValidatingCode
                                  ? null
                                  : _validateDiscountCode,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.black87,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12)),
                              child: _isValidatingCode
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white))
                                  : const Text("تحقق")),
                          ]),
                        if (_discountErrorMessage != null) ...[
                          const SizedBox(height: 8),
                          Text(
                            _discountErrorMessage!,
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 12)),
                        ],
                        if (_validDiscountInfo != null) ...[
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.green.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.green.shade200)),
                            child: Row(
                              children: [
                                Icon(Icons.check_circle,
                                    color: Colors.green.shade700),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    "تم تطبيق كود الخصم: ${_validDiscountInfo!['code']}",
                                    style: TextStyle(
                                      color: Colors.green.shade700,
                                      fontWeight: FontWeight.bold))),
                              ])),
                        ],
                      ]))),

              const SizedBox(height: 24),
              // زر الدفع: يظهر فقط إذا لم يتم نجاح الإعلان بعد.
              if (!state.isSuccess)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      // تطبيق الخصم إذا كان صالحًا
                      double finalAmount = total;
                      if (_validDiscountInfo != null) {
                        finalAmount = _discountService.applyDiscount(
                            total, _validDiscountInfo!);
                      }

                      // توجيه إلى صفحة اختيار طريقة الدفع
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى إكمال إنشاء الإعلان أولاً'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black87,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                      padding: const EdgeInsets.symmetric(vertical: 16)),
                    child: const Text(
                      "ادفع الآن",
                      style: TextStyle(fontSize: 16)))),
              const SizedBox(height: 24),
              // مؤشرات الحالة: عرض شريط التحميل أو رسالة النجاح.
              if (state.isSubmitting)
                const Center(child: CircularProgressIndicator()),
              if (state.isSuccess)
                Column(
                  children: const [
                    Icon(Icons.check_circle, color: Colors.green, size: 40),
                    SizedBox(height: 8),
                    Text(
                      "تم إنشاء الإعلان بنجاح!",
                      style: TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                          fontSize: 16)),
                    SizedBox(height: 8),
                    Text(
                      "سيتم تحويلك إلى الصفحة الرئيسية خلال 3 ثوانٍ...",
                      style: TextStyle(fontSize: 14),
                      textAlign: TextAlign.center),
                  ]),
            ]))));
  }
}
