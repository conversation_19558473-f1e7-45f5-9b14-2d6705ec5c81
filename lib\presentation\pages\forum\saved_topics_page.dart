import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../domain/models/forum/topic_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/empty_view.dart';

/// صفحة المواضيع المحفوظة والمتابعة
class SavedTopicsPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/saved-topics';

  const SavedTopicsPage({super.key});

  @override
  State<SavedTopicsPage> createState() => _SavedTopicsPageState();
}

class _SavedTopicsPageState extends State<SavedTopicsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  List<TopicModel> _bookmarkedTopics = [];
  List<TopicModel> _followedTopics = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // جلب البيانات عند تحميل الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يجب تسجيل الدخول لعرض المواضيع المحفوظة')));
      Navigator.pop(context);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);

      // جلب المواضيع المحفوظة
      _bookmarkedTopics =
          await forumProvider.getBookmarkedTopics(authProvider.user!.uid);

      // جلب المواضيع المتابعة
      _followedTopics =
          await forumProvider.getFollowedTopics(authProvider.user!.uid);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تحميل المواضيع')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('المواضيع المحفوظة'),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'المحفوظة'),
            Tab(text: 'المتابعة'),
          ])),
      body: _isLoading
          ? Center(child: LoadingIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildBookmarkedTopicsTab(),
                _buildFollowedTopicsTab(),
              ]));
  }

  /// بناء علامة تبويب المواضيع المحفوظة
  Widget _buildBookmarkedTopicsTab() {
    if (_bookmarkedTopics.isEmpty) {
      return EmptyView(
        message: 'لا توجد مواضيع محفوظة',
        icon: Icons.bookmark_border);
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.all(8),
        itemCount: _bookmarkedTopics.length,
        itemBuilder: (context, index) {
          final topic = _bookmarkedTopics[index];
          return _buildTopicItem(topic, isBookmarked: true);
        }));
  }

  /// بناء علامة تبويب المواضيع المتابعة
  Widget _buildFollowedTopicsTab() {
    if (_followedTopics.isEmpty) {
      return EmptyView(
        message: 'لا توجد مواضيع متابعة',
        icon: Icons.notifications_none);
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.all(8),
        itemCount: _followedTopics.length,
        itemBuilder: (context, index) {
          final topic = _followedTopics[index];
          return _buildTopicItem(topic, isFollowed: true);
        }));
  }

  /// بناء عنصر الموضوع
  Widget _buildTopicItem(TopicModel topic,
      {bool isBookmarked = false, bool isFollowed = false}) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 4),
      child: InkWell(
        onTap: () {
          // التنقل إلى صفحة الموضوع
          Navigator.pushNamed(
            context,
            '/forum/topic',
            arguments: topic.id).then((_) => _loadData()); // إعادة تحميل البيانات عند العودة
        },
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundImage: topic.userImage != null
                        ? NetworkImage(topic.userImage!)
                        : null,
                    radius: 16,
                    child: topic.userImage == null
                        ? Icon(Icons.person, size: 16)
                        : null),
                  SizedBox(width: 8),
                  Text(
                    topic.userName,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14)),
                  Spacer(),
                  Text(
                    _formatDate(topic.createdAt),
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 12)),
                ]),
              SizedBox(height: 8),
              Text(
                topic.title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16),
                maxLines: 2,
                overflow: TextOverflow.ellipsis),
              SizedBox(height: 4),
              Text(
                topic.content,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.black87),
                maxLines: 2,
                overflow: TextOverflow.ellipsis),
              SizedBox(height: 8),
              Row(
                children: [
                  Chip(
                    label: Text(topic.categoryName),
                    backgroundColor: Colors.grey[200],
                    labelStyle: TextStyle(fontSize: 12),
                    padding: EdgeInsets.zero,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap),
                  Spacer(),
                  _buildTopicStats(topic),
                ]),
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (isBookmarked)
                    TextButton.icon(
                      icon: Icon(Icons.bookmark_remove),
                      label: Text('إزالة من المحفوظة'),
                      onPressed: () => _removeBookmark(topic.id)),
                  if (isFollowed)
                    TextButton.icon(
                      icon: Icon(Icons.notifications_off),
                      label: Text('إلغاء المتابعة'),
                      onPressed: () => _unfollowTopic(topic.id)),
                ]),
            ]))));
  }

  /// بناء إحصائيات الموضوع
  Widget _buildTopicStats(TopicModel topic) {
    return Row(
      children: [
        Icon(Icons.remove_red_eye, size: 16, color: Colors.grey),
        SizedBox(width: 4),
        Text(
          '${topic.viewsCount}',
          style: TextStyle(fontSize: 12, color: Colors.grey)),
        SizedBox(width: 8),
        Icon(Icons.forum, size: 16, color: Colors.grey),
        SizedBox(width: 4),
        Text(
          '${topic.repliesCount}',
          style: TextStyle(fontSize: 12, color: Colors.grey)),
        SizedBox(width: 8),
        Icon(Icons.favorite, size: 16, color: Colors.grey),
        SizedBox(width: 4),
        Text(
          '${topic.likesCount}',
          style: TextStyle(fontSize: 12, color: Colors.grey)),
      ]);
  }

  /// إزالة إشارة مرجعية من موضوع
  Future<void> _removeBookmark(String topicId) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    try {
      await forumProvider.unbookmarkTopic(topicId, authProvider.user!.uid);

      setState(() {
        _bookmarkedTopics.removeWhere((topic) => topic.id == topicId);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تمت إزالة الموضوع من المحفوظة')));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء إزالة الإشارة المرجعية')));
    }
  }

  /// إلغاء متابعة موضوع
  Future<void> _unfollowTopic(String topicId) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    try {
      await forumProvider.unfollowTopic(topicId, authProvider.user!.uid);

      setState(() {
        _followedTopics.removeWhere((topic) => topic.id == topicId);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم إلغاء متابعة الموضوع')));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء إلغاء المتابعة')));
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} سنة';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} شهر';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
