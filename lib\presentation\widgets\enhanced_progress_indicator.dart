import 'package:flutter/material.dart';

/// مؤشر تقدم محسن يعرض المراحل بشكل أكثر تفاعلية
/// يستخدم في عمليات متعددة الخطوات مثل إنشاء الإعلان والتسجيل
class EnhancedProgressIndicator extends StatelessWidget {
  /// الخطوة الحالية (تبدأ من 1)
  final int currentStep;

  /// إجمالي عدد الخطوات
  final int totalSteps;

  /// قائمة بعناوين الخطوات
  final List<String> stepTitles;

  /// لون الخطوات المكتملة
  final Color completedColor;

  /// لون الخطوة الحالية
  final Color currentColor;

  /// لون الخطوات المتبقية
  final Color remainingColor;

  /// ما إذا كان يجب عرض أرقام الخطوات
  final bool showStepNumbers;

  const EnhancedProgressIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.stepTitles,
    this.completedColor = Colors.green,
    this.currentColor = Colors.blue,
    this.remainingColor = Colors.grey,
    this.showStepNumbers = true,
  })  : assert(currentStep > 0 && currentStep <= totalSteps,
            'الخطوة الحالية يجب أن تكون بين 1 و $totalSteps'),
        assert(stepTitles.length == totalSteps,
            'عدد العناوين يجب أن يساوي عدد الخطوات');

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // معلومات الخطوة الحالية
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "الخطوة $currentStep من $totalSteps",
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold)),
              Text(
                stepTitles[currentStep - 1],
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: currentColor)),
            ]),

          const SizedBox(height: 12),

          // مؤشر التقدم
          Row(
            children: List.generate(totalSteps, (index) {
              // تحديد حالة الخطوة (مكتملة، حالية، متبقية)
              final isCompleted = index + 1 < currentStep;
              final isCurrent = index + 1 == currentStep;
              final stepColor = isCompleted
                  ? completedColor
                  : (isCurrent ? currentColor : remainingColor);

              return Expanded(
                child: Row(
                  children: [
                    // دائرة الخطوة
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: stepColor,
                        shape: BoxShape.circle),
                      child: Center(
                        child: isCompleted
                            ? const Icon(Icons.check,
                                color: Colors.white, size: 16)
                            : (showStepNumbers
                                ? Text(
                                    "${index + 1}",
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold))
                                : null))),

                    // خط الاتصال (لا يظهر بعد آخر خطوة)
                    if (index < totalSteps - 1)
                      Expanded(
                        child: Container(
                          height: 3,
                          color: isCompleted
                              ? completedColor
                              : remainingColor.withOpacity(0.5))),
                  ]));
            })),

          const SizedBox(height: 8),

          // عناوين الخطوات (اختياري - يظهر في الشاشات الكبيرة)
          if (MediaQuery.of(context).size.width > 600)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(totalSteps, (index) {
                final isCompleted = index + 1 < currentStep;
                final isCurrent = index + 1 == currentStep;
                final textColor = isCompleted
                    ? completedColor
                    : (isCurrent ? currentColor : remainingColor);

                return Expanded(
                  child: Center(
                    child: Text(
                      stepTitles[index],
                      style: TextStyle(
                        fontSize: 10,
                        color: textColor,
                        fontWeight:
                            isCurrent ? FontWeight.bold : FontWeight.normal),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis)));
              })),
        ]));
  }
}

/// مكون مؤشر التقدم الدائري المحسن
/// يعرض مؤشر تقدم دائري مع نسبة مئوية
class EnhancedCircularProgressIndicator extends StatelessWidget {
  /// قيمة التقدم (0.0 - 1.0)
  final double value;

  /// نص العنوان
  final String title;

  /// نص الوصف
  final String? subtitle;

  /// لون التقدم
  final Color progressColor;

  /// لون الخلفية
  final Color backgroundColor;

  /// حجم الدائرة
  final double size;

  /// سمك الدائرة
  final double strokeWidth;

  /// حجم خط النسبة المئوية
  final double percentageFontSize;

  /// حجم خط العنوان
  final double titleFontSize;

  /// حجم خط الوصف
  final double subtitleFontSize;

  /// ما إذا كان يعرض النسبة المئوية
  final bool showPercentage;

  const EnhancedCircularProgressIndicator({
    super.key,
    required this.value,
    required this.title,
    this.subtitle,
    this.progressColor = Colors.blue,
    this.backgroundColor = Colors.grey,
    this.size = 120,
    this.strokeWidth = 10,
    this.percentageFontSize = 24,
    this.titleFontSize = 16,
    this.subtitleFontSize = 12,
    this.showPercentage = true,
  }) : assert(
            value >= 0 && value <= 1, 'قيمة التقدم يجب أن تكون بين 0.0 و 1.0');

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // مؤشر التقدم الدائري
        SizedBox(
          width: size,
          height: size,
          child: Stack(
            children: [
              // الدائرة الخلفية
              SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  value: 1.0,
                  strokeWidth: strokeWidth,
                  valueColor: AlwaysStoppedAnimation<Color>(
                      backgroundColor.withOpacity(0.2)))),

              // دائرة التقدم
              SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  value: value,
                  strokeWidth: strokeWidth,
                  valueColor: AlwaysStoppedAnimation<Color>(progressColor))),

              // النسبة المئوية
              if (showPercentage)
                Center(
                  child: Text(
                    '${(value * 100).toInt()}%',
                    style: TextStyle(
                      fontSize: percentageFontSize,
                      fontWeight: FontWeight.bold,
                      color: progressColor))),
            ])),

        const SizedBox(height: 16),

        // العنوان
        Text(
          title,
          style: TextStyle(
            fontSize: titleFontSize,
            fontWeight: FontWeight.bold),
          textAlign: TextAlign.center),

        // الوصف
        if (subtitle != null) ...[
          const SizedBox(height: 4),
          Text(
            subtitle!,
            style: TextStyle(
              fontSize: subtitleFontSize,
              color: Colors.grey),
            textAlign: TextAlign.center),
        ],
      ]);
  }
}

/// مكون مؤشر التقدم الخطي المحسن
/// يعرض مؤشر تقدم خطي مع نسبة مئوية وعنوان
class EnhancedLinearProgressIndicator extends StatelessWidget {
  /// قيمة التقدم (0.0 - 1.0)
  final double value;

  /// نص العنوان
  final String title;

  /// نص الوصف
  final String? subtitle;

  /// لون التقدم
  final Color progressColor;

  /// لون الخلفية
  final Color backgroundColor;

  /// ارتفاع شريط التقدم
  final double height;

  /// نصف قطر الزاوية
  final double borderRadius;

  /// ما إذا كان يعرض النسبة المئوية
  final bool showPercentage;

  /// موضع النسبة المئوية
  final PercentagePosition percentagePosition;

  const EnhancedLinearProgressIndicator({
    super.key,
    required this.value,
    required this.title,
    this.subtitle,
    this.progressColor = Colors.blue,
    this.backgroundColor = Colors.grey,
    this.height = 10,
    this.borderRadius = 5,
    this.showPercentage = true,
    this.percentagePosition = PercentagePosition.right,
  }) : assert(
            value >= 0 && value <= 1, 'قيمة التقدم يجب أن تكون بين 0.0 و 1.0');

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // العنوان والنسبة المئوية
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold)),
            if (showPercentage &&
                percentagePosition == PercentagePosition.right)
              Text(
                '${(value * 100).toInt()}%',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: progressColor)),
          ]),

        // الوصف
        if (subtitle != null) ...[
          const SizedBox(height: 4),
          Text(
            subtitle!,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey)),
        ],

        const SizedBox(height: 8),

        // شريط التقدم
        Stack(
          children: [
            // الشريط الخلفي
            Container(
              height: height,
              decoration: BoxDecoration(
                color: backgroundColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(borderRadius))),

            // شريط التقدم
            LayoutBuilder(
              builder: (context, constraints) {
                return Container(
                  height: height,
                  width: constraints.maxWidth * value,
                  decoration: BoxDecoration(
                    color: progressColor,
                    borderRadius: BorderRadius.circular(borderRadius)));
              }),

            // النسبة المئوية داخل الشريط
            if (showPercentage &&
                percentagePosition == PercentagePosition.inside)
              Positioned.fill(
                child: Center(
                  child: Text(
                    '${(value * 100).toInt()}%',
                    style: TextStyle(
                      fontSize: height * 0.7,
                      fontWeight: FontWeight.bold,
                      color: Colors.white)))),
          ]),

        // النسبة المئوية أسفل الشريط
        if (showPercentage &&
            percentagePosition == PercentagePosition.bottom) ...[
          const SizedBox(height: 4),
          Align(
            alignment: Alignment.center,
            child: Text(
              '${(value * 100).toInt()}%',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: progressColor))),
        ],
      ]);
  }
}

/// موضع النسبة المئوية في مؤشر التقدم الخطي
enum PercentagePosition {
  /// يمين العنوان
  right,

  /// داخل شريط التقدم
  inside,

  /// أسفل شريط التقدم
  bottom,
}
