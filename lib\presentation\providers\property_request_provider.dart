// lib/presentation/providers/property_request_provider.dart
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../core/services/property_request_service.dart';
import '../../domain/models/property_request/property_offer_model.dart';
import '../../domain/models/property_request/property_request_model.dart';
import '../utils/loading_state.dart';

/// مزود طلبات العقارات
class PropertyRequestProvider extends ChangeNotifier {
  final PropertyRequestService _service;
  final FirebaseAuth _auth;

  /// حالة تحميل طلبات العقارات
  LoadingState _requestsState = LoadingState.initial;

  /// حالة تحميل طلب العقار الحالي
  LoadingState _currentRequestState = LoadingState.initial;

  /// حالة تحميل العروض
  LoadingState _offersState = LoadingState.initial;

  /// حالة تحميل العرض الحالي
  LoadingState _currentOfferState = LoadingState.initial;

  /// قائمة طلبات العقارات
  List<PropertyRequestModel> _requests = [];

  /// طلب العقار الحالي
  PropertyRequestModel? _currentRequest;

  /// قائمة العروض
  List<PropertyOfferModel> _offers = [];

  /// العرض الحالي
  PropertyOfferModel? _currentOffer;

  /// آخر وثيقة تم تحميلها (للصفحات)
  DocumentSnapshot? _lastDocument;

  /// هل هناك المزيد من الطلبات للتحميل
  bool _hasMoreRequests = true;

  /// هل هناك المزيد من العروض للتحميل
  bool _hasMoreOffers = true;

  /// هل يتم تحميل المزيد من الطلبات
  bool _isLoadingMoreRequests = false;

  /// هل يتم تحميل المزيد من العروض
  final bool _isLoadingMoreOffers = false;

  /// الفلاتر الحالية
  Map<String, dynamic> _filters = {};

  /// ترتيب الطلبات
  String? _sortBy;

  /// اتجاه الترتيب
  bool _descending = true;

  /// رسالة الخطأ الحالية
  String? _errorMessage;

  /// الحصول على حالة تحميل طلبات العقارات
  LoadingState get requestsState => _requestsState;

  /// الحصول على حالة تحميل طلب العقار الحالي
  LoadingState get currentRequestState => _currentRequestState;

  /// الحصول على حالة تحميل العروض
  LoadingState get offersState => _offersState;

  /// الحصول على حالة تحميل العرض الحالي
  LoadingState get currentOfferState => _currentOfferState;

  /// الحصول على قائمة طلبات العقارات
  List<PropertyRequestModel> get requests => _requests;

  /// الحصول على طلب العقار الحالي
  PropertyRequestModel? get currentRequest => _currentRequest;

  /// الحصول على قائمة العروض
  List<PropertyOfferModel> get offers => _offers;

  /// الحصول على العرض الحالي
  PropertyOfferModel? get currentOffer => _currentOffer;

  /// هل هناك المزيد من الطلبات للتحميل
  bool get hasMoreRequests => _hasMoreRequests;

  /// هل هناك المزيد من العروض للتحميل
  bool get hasMoreOffers => _hasMoreOffers;

  /// هل يتم تحميل المزيد من الطلبات
  bool get isLoadingMoreRequests => _isLoadingMoreRequests;

  /// هل يتم تحميل المزيد من العروض
  bool get isLoadingMoreOffers => _isLoadingMoreOffers;

  /// الحصول على الفلاتر الحالية
  Map<String, dynamic> get filters => _filters;

  /// الحصول على ترتيب الطلبات
  String? get sortBy => _sortBy;

  /// الحصول على اتجاه الترتيب
  bool get descending => _descending;

  /// الحصول على رسالة الخطأ الحالية
  String? get errorMessage => _errorMessage;

  /// إنشاء مزود طلبات العقارات
  PropertyRequestProvider({
    required PropertyRequestService service,
    FirebaseAuth? auth,
  })  : _service = service,
        _auth = auth ?? FirebaseAuth.instance;

  /// تحميل طلبات العقارات مع معالجة محسنة للأخطاء
  Future<void> loadPropertyRequests({
    int limit = 10,
    bool refresh = false,
    int retryCount = 0,
  }) async {
    if (refresh) {
      _requestsState = LoadingState.loading;
      _lastDocument = null;
      _hasMoreRequests = true;
      _errorMessage = null;
      notifyListeners();
    } else if (_requestsState == LoadingState.loading) {
      return;
    } else {
      _requestsState = LoadingState.loading;
      _errorMessage = null;
      notifyListeners();
    }

    try {
      final requests = await _service.getAllPropertyRequests(
        limit: limit,
        startAfter: refresh ? null : _lastDocument,
        sortBy: _sortBy,
        descending: _descending,
        filters: _filters);

      if (refresh) {
        _requests = requests;
      } else {
        _requests.addAll(requests);
      }

      _requestsState = _requests.isEmpty ? LoadingState.empty : LoadingState.loaded;
      _hasMoreRequests = requests.length >= limit;

      if (requests.isNotEmpty) {
        _lastDocument = await _getLastDocument(requests.last.id);
      }
    } catch (e) {
      print('Error loading property requests: $e');

      // تحديد نوع الخطأ ورسالة مناسبة
      if (e.toString().contains('network') || e.toString().contains('timeout')) {
        _errorMessage = 'مشكلة في الاتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.';
      } else if (e.toString().contains('permission')) {
        _errorMessage = 'ليس لديك صلاحية للوصول إلى هذه البيانات.';
      } else if (e.toString().contains('rate-limit')) {
        _errorMessage = 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً.';
      } else {
        _errorMessage = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
      }

      _requestsState = LoadingState.error;

      // إعادة المحاولة التلقائية للأخطاء المؤقتة
      if (retryCount < 2 && (e.toString().contains('network') || e.toString().contains('timeout'))) {
        await Future.delayed(Duration(seconds: (retryCount + 1) * 2));
        return loadPropertyRequests(limit: limit, refresh: refresh, retryCount: retryCount + 1);
      }
    } finally {
      notifyListeners();
    }
  }

  /// تحميل المزيد من طلبات العقارات
  Future<void> loadMorePropertyRequests({int limit = 10}) async {
    if (!_hasMoreRequests || _isLoadingMoreRequests) {
      return;
    }

    _isLoadingMoreRequests = true;
    notifyListeners();

    try {
      final requests = await _service.getAllPropertyRequests(
        limit: limit,
        startAfter: _lastDocument,
        sortBy: _sortBy,
        descending: _descending,
        filters: _filters);

      _requests.addAll(requests);
      _hasMoreRequests = requests.length >= limit;

      if (requests.isNotEmpty) {
        _lastDocument = await _getLastDocument(requests.last.id);
      }
    } catch (e) {
      // تجاهل الخطأ
    } finally {
      _isLoadingMoreRequests = false;
      notifyListeners();
    }
  }

  /// تحميل طلب عقار محدد
  Future<void> loadPropertyRequest(String requestId) async {
    _currentRequestState = LoadingState.loading;
    notifyListeners();

    try {
      final request = await _service.getPropertyRequestById(requestId);

      if (request != null) {
        _currentRequest = request;
        _currentRequestState = LoadingState.loaded;

        // زيادة عدد المشاهدات
        await _service.incrementPropertyRequestViews(requestId);
      } else {
        _currentRequestState = LoadingState.empty;
      }
    } catch (e) {
      _currentRequestState = LoadingState.error;
    } finally {
      notifyListeners();
    }
  }

  /// إنشاء طلب عقار جديد
  Future<PropertyRequestModel?> createPropertyRequest(
    PropertyRequestModel request, {
    List<File>? images,
  }) async {
    try {
      final createdRequest = await _service.createPropertyRequest(
        request,
        images: images);

      // تحديث القائمة
      _requests.insert(0, createdRequest);
      notifyListeners();

      return createdRequest;
    } catch (e) {
      return null;
    }
  }

  /// تحديث طلب عقار
  Future<bool> updatePropertyRequest(
    PropertyRequestModel request, {
    List<File>? newImages,
    List<String>? imagesToDelete,
  }) async {
    try {
      await _service.updatePropertyRequest(
        request,
        newImages: newImages,
        imagesToDelete: imagesToDelete);

      // تحديث القائمة
      final index = _requests.indexWhere((r) => r.id == request.id);
      if (index != -1) {
        _requests[index] = request;
      }

      // تحديث الطلب الحالي
      if (_currentRequest?.id == request.id) {
        _currentRequest = request;
      }

      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// حذف طلب عقار
  Future<bool> deletePropertyRequest(String requestId) async {
    try {
      await _service.deletePropertyRequest(requestId);

      // تحديث القائمة
      _requests.removeWhere((r) => r.id == requestId);

      // تحديث الطلب الحالي
      if (_currentRequest?.id == requestId) {
        _currentRequest = null;
        _currentRequestState = LoadingState.empty;
      }

      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تحميل عروض طلب عقار محدد
  Future<void> loadRequestOffers(
    String requestId, {
    int limit = 20,
    bool refresh = false,
  }) async {
    if (refresh) {
      _offersState = LoadingState.loading;
      _hasMoreOffers = true;
      notifyListeners();
    } else if (_offersState == LoadingState.loading) {
      return;
    } else {
      _offersState = LoadingState.loading;
      notifyListeners();
    }

    try {
      final offers = await _service.getOffersByRequest(
        requestId,
        limit: limit);

      _offers = offers;
      _offersState = _offers.isEmpty ? LoadingState.empty : LoadingState.loaded;
      _hasMoreOffers = offers.length >= limit;
    } catch (e) {
      _offersState = LoadingState.error;
    } finally {
      notifyListeners();
    }
  }

  /// تحميل عرض محدد
  Future<void> loadOffer(String offerId) async {
    _currentOfferState = LoadingState.loading;
    notifyListeners();

    try {
      final offer = await _service.getOfferById(offerId);

      if (offer != null) {
        _currentOffer = offer;
        _currentOfferState = LoadingState.loaded;
      } else {
        _currentOfferState = LoadingState.empty;
      }
    } catch (e) {
      _currentOfferState = LoadingState.error;
    } finally {
      notifyListeners();
    }
  }

  /// إنشاء عرض جديد
  Future<PropertyOfferModel?> createOffer(
    PropertyOfferModel offer, {
    List<File>? images,
  }) async {
    try {
      final createdOffer = await _service.createOffer(
        offer,
        images: images);

      // تحديث القائمة
      _offers.insert(0, createdOffer);
      
      // تحديث عدد العروض في الطلب الحالي
      if (_currentRequest?.id == offer.requestId) {
        _currentRequest = _currentRequest!.copyWith(
          offersCount: (_currentRequest!.offersCount + 1));
      }
      
      notifyListeners();

      return createdOffer;
    } catch (e) {
      return null;
    }
  }

  /// قبول عرض
  Future<bool> acceptOffer(String offerId, String requestId) async {
    try {
      await _service.acceptOffer(offerId, requestId);

      // تحديث حالة العرض
      final offerIndex = _offers.indexWhere((o) => o.id == offerId);
      if (offerIndex != -1) {
        _offers[offerIndex] = _offers[offerIndex].copyWith(
          status: OfferStatus.accepted);
      }

      // تحديث حالة الطلب
      final requestIndex = _requests.indexWhere((r) => r.id == requestId);
      if (requestIndex != -1) {
        _requests[requestIndex] = _requests[requestIndex].copyWith(
          status: RequestStatus.resolved);
      }

      // تحديث الطلب الحالي
      if (_currentRequest?.id == requestId) {
        _currentRequest = _currentRequest!.copyWith(
          status: RequestStatus.resolved);
      }

      // تحديث العرض الحالي
      if (_currentOffer?.id == offerId) {
        _currentOffer = _currentOffer!.copyWith(
          status: OfferStatus.accepted);
      }

      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// رفض عرض
  Future<bool> rejectOffer(String offerId, String requestId) async {
    try {
      await _service.rejectOffer(offerId, requestId);

      // تحديث حالة العرض
      final offerIndex = _offers.indexWhere((o) => o.id == offerId);
      if (offerIndex != -1) {
        _offers[offerIndex] = _offers[offerIndex].copyWith(
          status: OfferStatus.rejected);
      }

      // تحديث العرض الحالي
      if (_currentOffer?.id == offerId) {
        _currentOffer = _currentOffer!.copyWith(
          status: OfferStatus.rejected);
      }

      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تعيين الفلاتر
  void setFilters(Map<String, dynamic> filters) {
    _filters = filters;
    notifyListeners();
  }

  /// تعيين ترتيب الطلبات
  void setSorting(String? sortBy, bool descending) {
    _sortBy = sortBy;
    _descending = descending;
    notifyListeners();
  }

  /// الحصول على آخر وثيقة
  Future<DocumentSnapshot?> _getLastDocument(String id) async {
    try {
      return await FirebaseFirestore.instance
          .collection('property_requests')
          .doc(id)
          .get();
    } catch (e) {
      return null;
    }
  }
}
