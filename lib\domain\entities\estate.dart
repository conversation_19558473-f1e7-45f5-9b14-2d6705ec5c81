import 'package:equatable/equatable.dart';

class Estate extends Equatable {
  final String id;
  final String title;
  final String description;
  final double price;
  final String location;
  final List<String> photoUrls;
  final bool isFeatured;
  final String planType;
  final DateTime? startDate;
  final DateTime? endDate;
  final DateTime createdAt;
  // بيانات أساسية إضافية
  final String? mainCategory;
  final String? subCategory;
  final String? postedByUserType;
  final bool hidePhone;
  final List<String> extraPhones;
  final bool shareLocation;
  final double? lat;
  final double? lng;
  // التجهيزات الإضافية
  final bool hasCentralAC;
  final bool? hasSecurity;
  final bool? allowPets;
  final bool? hasElevator;
  final bool? hasSwimmingPool;
  final bool hasMaidRoom;
  final bool hasGarage;
  final bool? hasBalcony;
  final bool? isFullyFurnished;
  // تفاصيل العقار الداخلية
  final String? rebound;
  final int? numberOfRooms;
  final String? internalLocation;
  final String? salon;
  final double? area;
  final int? floorNumber;
  final int? numberOfBathrooms;
  final int? buildingAge;
  final int? numberOfFloors;
  final String? propertyType;
  // الحقول الجديدة المطلوبة (بيانات المُعلِن)
  final String? advertiserImage;
  final String? advertiserName;
  final String? advertiserEmail;
  final DateTime? advertiserRegistrationDate;
  final int? advertiserAdsCount;
  // الحقول الجديدة الأخرى
  final bool autoRepublish;
  final bool kuwaitCornersPin;
  final bool movingAd;
  final bool vipBadge;
  final bool pinnedOnHome;
  final String? discountCode;

  // حقول ملكية العقار والعلاقات
  final String? ownerId; // معرف مالك العقار الأصلي
  final String? originalEstateId; // معرف العقار الأصلي (في حالة النسخ)
  final bool isOriginal; // هل هذا عقار أصلي أم منسوخ
  final List<String>
      copiedBy; // قائمة بمعرفات المستخدمين الذين نسخوا هذا العقار

  // حقل التحقق من الدفع
  final bool isPaymentVerified; // هل تم التحقق من الدفع

  // حقول الإحصائيات
  final int? viewsCount; // عدد المشاهدات
  final int? inquiriesCount; // عدد الاستفسارات
  final int? favoritesCount; // عدد مرات الإضافة للمفضلة

  // حقول إضافية للتوافق
  final double? latitude; // خط العرض
  final double? longitude; // خط الطول
  final int? rooms; // عدد الغرف
  final int? bathrooms; // عدد الحمامات
  final int? floors; // عدد الأدوار
  final String? purpose; // الغرض (للبيع/للإيجار/للبدل)
  final String? usageType; // نوع الاستغلال (للبيع/للإيجار/للبدل/استثمار)
  final bool? hasGarden; // يوجد حديقة
  final bool? hasPool; // يوجد مسبح
  final bool? hasDriverRoom; // غرفة سائق
  final bool? hasPrivateEntrance; // مدخل خاص
  final bool? hasEquippedKitchen; // مطبخ مجهز
  final bool isAvailable; // متاح للعرض

  /// هل العقار منسوخ؟
  final bool isCopied;

  /// تاريخ نسخ العقار
  final DateTime? copiedAt;

  /// هل الإعلان مدفوع؟
  final bool isPaidAd;

  /// نوع الإعلان المدفوع (featured/pinned/both)
  final String? adType;

  /// تاريخ انتهاء الإعلان المدفوع
  final DateTime? adExpiryDate;

  /// عدد مرات النسخ (للعقار الأصلي)
  final int copyCount;

  const Estate({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.location,
    required this.photoUrls,
    required this.isFeatured,
    required this.planType,
    required this.startDate,
    required this.endDate,
    required this.createdAt,
    this.mainCategory,
    this.subCategory,
    this.postedByUserType,
    this.hidePhone = false,
    this.extraPhones = const [],
    this.shareLocation = false,
    this.lat,
    this.lng,
    this.hasCentralAC = false,
    this.hasSecurity,
    this.allowPets,
    this.hasElevator,
    this.hasSwimmingPool,
    this.hasMaidRoom = false,
    this.hasGarage = false,
    this.hasBalcony,
    this.isFullyFurnished,
    this.rebound,
    this.numberOfRooms,
    this.internalLocation,
    this.salon,
    this.area,
    this.floorNumber,
    this.numberOfBathrooms,
    this.buildingAge,
    this.numberOfFloors,
    this.propertyType,
    this.autoRepublish = false,
    this.kuwaitCornersPin = false,
    this.movingAd = false,
    this.vipBadge = false,
    this.pinnedOnHome = false,
    this.discountCode,
    this.advertiserImage,
    this.advertiserName,
    this.advertiserEmail,
    this.advertiserRegistrationDate,
    this.advertiserAdsCount,
    this.ownerId,
    this.originalEstateId,
    this.isOriginal = true,
    this.copiedBy = const [],
    this.isPaymentVerified = false,
    this.viewsCount,
    this.inquiriesCount,
    this.favoritesCount,
    this.latitude,
    this.longitude,
    this.rooms,
    this.bathrooms,
    this.floors,
    this.purpose,
    this.usageType,
    this.hasGarden,
    this.hasPool,
    this.hasDriverRoom,
    this.hasPrivateEntrance,
    this.hasEquippedKitchen,
    this.isAvailable = true,
    this.isCopied = false,
    this.copiedAt,
    this.isPaidAd = false,
    this.adType,
    this.adExpiryDate,
    this.copyCount = 0,
  });

  /// التحقق من صحة بيانات العقار
  Map<String, String> validate() {
    final errors = <String, String>{};

    if (title.isEmpty) {
      errors['title'] = 'يجب إدخال عنوان الإعلان';
    } else if (title.length < 5) {
      errors['title'] = 'يجب أن يكون العنوان أكثر من 5 أحرف';
    } else if (title.length > 100) {
      errors['title'] = 'يجب أن يكون العنوان أقل من 100 حرف';
    }

    if (description.isEmpty) {
      errors['description'] = 'يجب إدخال وصف الإعلان';
    } else if (description.length < 20) {
      errors['description'] = 'يجب أن يكون الوصف أكثر من 20 حرف';
    } else if (description.length > 2000) {
      errors['description'] = 'يجب أن يكون الوصف أقل من 2000 حرف';
    }

    if (price <= 0) {
      errors['price'] = 'يجب إدخال سعر صحيح';
    } else if (price > 10000000) {
      errors['price'] = 'السعر مرتفع جداً، يرجى التحقق';
    }

    if (location.isEmpty) {
      errors['location'] = 'يجب إدخال الموقع';
    }

    if (photoUrls.isEmpty) {
      errors['photoUrls'] = 'يجب إضافة صورة واحدة على الأقل';
    }

    if (mainCategory?.isEmpty ?? true) {
      errors['mainCategory'] = 'يجب تحديد التصنيف الرئيسي';
    }

    if (ownerId?.isEmpty ?? true) {
      errors['ownerId'] = 'معرف المالك غير صالح';
    }

    return errors;
  }

  /// دالة copyWithBase لتحديث الحقول الأساسية (للتوافق مع EstateBase)
  Estate copyWithBase({
    String? id,
    String? title,
    String? description,
    double? price,
    String? location,
    List<String>? photoUrls,
    bool? isFeatured,
    String? status,
    String? governorate,
    String? city,
    String? district,
    String? block,
    double? latitude,
    double? longitude,
    bool? shareLocation,
    String? mainCategory,
    String? subCategory,
    String? ownerId,
    String? advertiserName,
    String? advertiserPhone,
    String? advertiserImage,
    String? advertiserType,
    DateTime? advertiserJoinDate,
    int? advertiserAdsCount,
    bool? hidePhone,
    List<String>? extraPhones,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? startDate,
    DateTime? endDate,
    int? viewsCount,
    int? favoritesCount,
    int? contactCount,
    String? subscriptionPlan,
    bool? autoRepublish,
    bool? isPinned,
    bool? isPromoted,
    bool? isVIP,
    bool? isVerified,
    bool? isPaymentVerified,
    String? originalEstateId,
    bool? isOriginal,
    List<String>? copiedBy,
    String? floorPlanUrl,
    String? virtualTourUrl,
    String? videoUrl,
    List<Map<String, dynamic>>? documents,
  }) {
    return Estate(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      location: location ?? this.location,
      photoUrls: photoUrls ?? this.photoUrls,
      isFeatured: isFeatured ?? this.isFeatured,
      planType: planType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createdAt: createdAt ?? this.createdAt,
      mainCategory: mainCategory ?? this.mainCategory,
      subCategory: subCategory ?? this.subCategory,
      postedByUserType: postedByUserType,
      hidePhone: hidePhone ?? this.hidePhone,
      extraPhones: extraPhones ?? this.extraPhones,
      shareLocation: shareLocation ?? this.shareLocation,
      lat: latitude ?? lat,
      lng: longitude ?? lng,
      hasCentralAC: hasCentralAC,
      hasSecurity: hasSecurity,
      allowPets: allowPets,
      hasElevator: hasElevator,
      hasSwimmingPool: hasSwimmingPool,
      hasMaidRoom: hasMaidRoom,
      hasGarage: hasGarage,
      hasBalcony: hasBalcony,
      isFullyFurnished: isFullyFurnished,
      rebound: rebound,
      numberOfRooms: numberOfRooms,
      internalLocation: internalLocation,
      salon: salon,
      area: area,
      floorNumber: floorNumber,
      numberOfBathrooms: numberOfBathrooms,
      buildingAge: buildingAge,
      numberOfFloors: numberOfFloors,
      propertyType: propertyType,
      autoRepublish: autoRepublish ?? this.autoRepublish,
      kuwaitCornersPin: kuwaitCornersPin,
      movingAd: movingAd,
      vipBadge: isVIP ?? vipBadge,
      pinnedOnHome: isPinned ?? pinnedOnHome,
      discountCode: discountCode,
      advertiserImage: advertiserImage ?? this.advertiserImage,
      advertiserName: advertiserName ?? this.advertiserName,
      advertiserEmail: advertiserEmail,
      advertiserRegistrationDate: advertiserJoinDate ?? advertiserRegistrationDate,
      advertiserAdsCount: advertiserAdsCount ?? this.advertiserAdsCount,
      ownerId: ownerId ?? this.ownerId,
      originalEstateId: originalEstateId ?? this.originalEstateId,
      isOriginal: isOriginal ?? this.isOriginal,
      copiedBy: copiedBy ?? this.copiedBy,
      isPaymentVerified: isPaymentVerified ?? this.isPaymentVerified,
      viewsCount: viewsCount ?? this.viewsCount,
      inquiriesCount: inquiriesCount,
      favoritesCount: favoritesCount ?? this.favoritesCount,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      rooms: rooms,
      bathrooms: bathrooms,
      floors: floors,
      purpose: purpose,
      usageType: usageType,
      hasGarden: hasGarden,
      hasPool: hasPool,
      hasDriverRoom: hasDriverRoom,
      hasPrivateEntrance: hasPrivateEntrance,
      hasEquippedKitchen: hasEquippedKitchen,
      isAvailable: isAvailable,
      isCopied: isCopied,
      copiedAt: copiedAt);
  }

  /// دالة copyWith لتحديث الحقول بما في ذلك بيانات المُعلِن
  Estate copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    String? location,
    List<String>? photoUrls,
    bool? isFeatured,
    String? planType,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? createdAt,
    String? mainCategory,
    String? subCategory,
    String? postedByUserType,
    bool? hidePhone,
    List<String>? extraPhones,
    bool? shareLocation,
    double? lat,
    double? lng,
    bool? hasCentralAC,
    bool? hasSecurity,
    bool? allowPets,
    bool? hasElevator,
    bool? hasSwimmingPool,
    bool? hasMaidRoom,
    bool? hasGarage,
    bool? hasBalcony,
    bool? isFullyFurnished,
    String? rebound,
    int? numberOfRooms,
    String? internalLocation,
    String? salon,
    double? area,
    int? floorNumber,
    int? numberOfBathrooms,
    int? buildingAge,
    int? numberOfFloors,
    String? propertyType,
    bool? autoRepublish,
    bool? kuwaitCornersPin,
    bool? movingAd,
    bool? vipBadge,
    bool? pinnedOnHome,
    String? discountCode,
    String? advertiserImage,
    String? advertiserName,
    String? advertiserEmail,
    DateTime? advertiserRegistrationDate,
    int? advertiserAdsCount,
    String? ownerId,
    String? originalEstateId,
    bool? isOriginal,
    List<String>? copiedBy,
    bool? isPaymentVerified,
    int? viewsCount,
    int? inquiriesCount,
    int? favoritesCount,
    double? latitude,
    double? longitude,
    int? rooms,
    int? bathrooms,
    int? floors,
    String? purpose,
    String? usageType,
    bool? hasGarden,
    bool? hasPool,
    bool? hasDriverRoom,
    bool? hasPrivateEntrance,
    bool? hasEquippedKitchen,
    bool? isAvailable,
    bool? isCopied,
    DateTime? copiedAt,
    bool? isPaidAd,
    String? adType,
    DateTime? adExpiryDate,
    int? copyCount,
  }) {
    return Estate(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      location: location ?? this.location,
      photoUrls: photoUrls ?? this.photoUrls,
      isFeatured: isFeatured ?? this.isFeatured,
      planType: planType ?? this.planType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createdAt: createdAt ?? this.createdAt,
      mainCategory: mainCategory ?? this.mainCategory,
      subCategory: subCategory ?? this.subCategory,
      postedByUserType: postedByUserType ?? this.postedByUserType,
      hidePhone: hidePhone ?? this.hidePhone,
      extraPhones: extraPhones ?? this.extraPhones,
      shareLocation: shareLocation ?? this.shareLocation,
      lat: lat ?? this.lat,
      lng: lng ?? this.lng,
      hasCentralAC: hasCentralAC ?? this.hasCentralAC,
      hasSecurity: hasSecurity ?? this.hasSecurity,
      allowPets: allowPets ?? this.allowPets,
      hasElevator: hasElevator ?? this.hasElevator,
      hasSwimmingPool: hasSwimmingPool ?? this.hasSwimmingPool,
      hasMaidRoom: hasMaidRoom ?? this.hasMaidRoom,
      hasGarage: hasGarage ?? this.hasGarage,
      hasBalcony: hasBalcony ?? this.hasBalcony,
      isFullyFurnished: isFullyFurnished ?? this.isFullyFurnished,
      rebound: rebound ?? this.rebound,
      numberOfRooms: numberOfRooms ?? this.numberOfRooms,
      internalLocation: internalLocation ?? this.internalLocation,
      salon: salon ?? this.salon,
      area: area ?? this.area,
      floorNumber: floorNumber ?? this.floorNumber,
      numberOfBathrooms: numberOfBathrooms ?? this.numberOfBathrooms,
      buildingAge: buildingAge ?? this.buildingAge,
      numberOfFloors: numberOfFloors ?? this.numberOfFloors,
      propertyType: propertyType ?? this.propertyType,
      autoRepublish: autoRepublish ?? this.autoRepublish,
      kuwaitCornersPin: kuwaitCornersPin ?? this.kuwaitCornersPin,
      movingAd: movingAd ?? this.movingAd,
      vipBadge: vipBadge ?? this.vipBadge,
      pinnedOnHome: pinnedOnHome ?? this.pinnedOnHome,
      discountCode: discountCode ?? this.discountCode,
      advertiserImage: advertiserImage ?? this.advertiserImage,
      advertiserName: advertiserName ?? this.advertiserName,
      advertiserEmail: advertiserEmail ?? this.advertiserEmail,
      advertiserRegistrationDate:
          advertiserRegistrationDate ?? this.advertiserRegistrationDate,
      advertiserAdsCount: advertiserAdsCount ?? this.advertiserAdsCount,
      ownerId: ownerId ?? this.ownerId,
      originalEstateId: originalEstateId ?? this.originalEstateId,
      isOriginal: isOriginal ?? this.isOriginal,
      copiedBy: copiedBy ?? this.copiedBy,
      isPaymentVerified: isPaymentVerified ?? this.isPaymentVerified,
      viewsCount: viewsCount ?? this.viewsCount,
      inquiriesCount: inquiriesCount ?? this.inquiriesCount,
      favoritesCount: favoritesCount ?? this.favoritesCount,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      rooms: rooms ?? this.rooms,
      bathrooms: bathrooms ?? this.bathrooms,
      floors: floors ?? this.floors,
      purpose: purpose ?? this.purpose,
      usageType: usageType ?? this.usageType,
      hasGarden: hasGarden ?? this.hasGarden,
      hasPool: hasPool ?? this.hasPool,
      hasDriverRoom: hasDriverRoom ?? this.hasDriverRoom,
      hasPrivateEntrance: hasPrivateEntrance ?? this.hasPrivateEntrance,
      hasEquippedKitchen: hasEquippedKitchen ?? this.hasEquippedKitchen,
      isAvailable: isAvailable ?? this.isAvailable,
      isCopied: isCopied ?? this.isCopied,
      copiedAt: copiedAt ?? this.copiedAt,
      isPaidAd: isPaidAd ?? this.isPaidAd,
      adType: adType ?? this.adType,
      adExpiryDate: adExpiryDate ?? this.adExpiryDate,
      copyCount: copyCount ?? this.copyCount);
  }

  /// إنشاء كائن Estate فارغ للاستخدام في إنشاء إعلان جديد
  static Estate empty() {
    return Estate(
      id: '',
      title: '',
      description: '',
      price: 0.0,
      location: '',
      photoUrls: [],
      isFeatured: false,
      planType: 'free',
      startDate: DateTime.now(),
      endDate: DateTime.now().add(const Duration(days: 30)),
      createdAt: DateTime.now());
  }

  // Getters for compatibility with other parts of the code
  List<String> get images => photoUrls;
  String? get city => null; // Add proper implementation if available
  String get areaName => location; // Using location as area name for now
  double get size => area ?? 0.0; // Using area as size for now
  String? get governorate => null; // Add proper implementation if available
  String? get district => null; // Add proper implementation if available
  String? get block => null; // Add proper implementation if available
  String? get userId => ownerId; // Using ownerId as userId for now
  String? get advertiserPhone => null; // Add proper implementation if available
  DateTime get creationDate => createdAt; // Using createdAt as creationDate
  DateTime? get updatedAt => null; // Add proper implementation if available
  double? get streetWidth => null; // Add proper implementation if available

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        price,
        location,
        photoUrls,
        isFeatured,
        planType,
        startDate,
        endDate,
        createdAt,
        mainCategory,
        subCategory,
        postedByUserType,
        hidePhone,
        extraPhones,
        shareLocation,
        lat,
        lng,
        hasCentralAC,
        hasSecurity,
        allowPets,
        hasElevator,
        hasSwimmingPool,
        hasMaidRoom,
        hasGarage,
        hasBalcony,
        isFullyFurnished,
        rebound,
        numberOfRooms,
        internalLocation,
        salon,
        area,
        floorNumber,
        numberOfBathrooms,
        buildingAge,
        numberOfFloors,
        propertyType,
        autoRepublish,
        kuwaitCornersPin,
        movingAd,
        vipBadge,
        pinnedOnHome,
        discountCode,
        advertiserImage,
        advertiserName,
        advertiserEmail,
        advertiserRegistrationDate,
        advertiserAdsCount,
        ownerId,
        originalEstateId,
        isOriginal,
        copiedBy,
        isPaymentVerified,
        viewsCount,
        inquiriesCount,
        favoritesCount,
        latitude,
        longitude,
        rooms,
        bathrooms,
        floors,
        purpose,
        usageType,
        hasGarden,
        hasPool,
        hasDriverRoom,
        hasPrivateEntrance,
        hasEquippedKitchen,
        isAvailable,
        isCopied,
        copiedAt,
        isPaidAd,
        adType,
        adExpiryDate,
        copyCount,
      ];
}
