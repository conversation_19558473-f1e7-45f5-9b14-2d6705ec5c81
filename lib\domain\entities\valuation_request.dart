import 'package:equatable/equatable.dart';

/// حالة طلب التقييم العقاري
enum ValuationRequestStatus {
  pending,
  approved,
  rejected,
  inProgress,
  completed,
  cancelled,
  awaitingPayment,
  awaitingDocuments,
}

/// كيان طلب التقييم العقاري
class ValuationRequest extends Equatable {
  final String id;
  final String userId;
  final String providerId;
  final String providerName;
  final String? estateId;
  final ValuationRequestStatus status;
  final String valuationType;
  final double price;
  final String propertyType;
  final String area;
  final double size;
  final String address;
  final double? latitude;
  final double? longitude;
  final int? rooms;
  final int? bathrooms;
  final int? age;
  final bool? isFurnished;
  final bool? isRenovated;
  final List<String>? features;
  final List<String>? photoUrls;
  final Map<String, String>? documents;
  final List<ValuationRequestNote>? notes;
  final String? valuationId;
  final DateTime requestDate;
  final DateTime? approvalDate;
  final DateTime? rejectionDate;
  final String? rejectionReason;
  final DateTime? completionDate;
  final DateTime? estimatedCompletionDate;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان طلب التقييم العقاري
  const ValuationRequest({
    required this.id,
    required this.userId,
    required this.providerId,
    required this.providerName,
    this.estateId,
    required this.status,
    required this.valuationType,
    required this.price,
    required this.propertyType,
    required this.area,
    required this.size,
    required this.address,
    this.latitude,
    this.longitude,
    this.rooms,
    this.bathrooms,
    this.age,
    this.isFurnished,
    this.isRenovated,
    this.features,
    this.photoUrls,
    this.documents,
    this.notes,
    this.valuationId,
    required this.requestDate,
    this.approvalDate,
    this.rejectionDate,
    this.rejectionReason,
    this.completionDate,
    this.estimatedCompletionDate,
    this.additionalInfo,
  });

  /// إنشاء كيان طلب التقييم العقاري من JSON
  factory ValuationRequest.fromJson(Map<String, dynamic> json) {
    return ValuationRequest(
      id: json['id'] as String,
      userId: json['userId'] as String,
      providerId: json['providerId'] as String,
      providerName: json['providerName'] as String,
      estateId: json['estateId'] as String?,
      status: _parseStatus(json['status'] as String),
      valuationType: json['valuationType'] as String,
      price: json['price'] as double,
      propertyType: json['propertyType'] as String,
      area: json['area'] as String,
      size: json['size'] as double,
      address: json['address'] as String,
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      rooms: json['rooms'] as int?,
      bathrooms: json['bathrooms'] as int?,
      age: json['age'] as int?,
      isFurnished: json['isFurnished'] as bool?,
      isRenovated: json['isRenovated'] as bool?,
      features: json['features'] != null
          ? List<String>.from(json['features'] as List)
          : null,
      photoUrls: json['photoUrls'] != null
          ? List<String>.from(json['photoUrls'] as List)
          : null,
      documents: json['documents'] != null
          ? Map<String, String>.from(json['documents'] as Map)
          : null,
      notes: json['notes'] != null
          ? (json['notes'] as List)
              .map((note) => ValuationRequestNote.fromJson(note))
              .toList()
          : null,
      valuationId: json['valuationId'] as String?,
      requestDate: DateTime.parse(json['requestDate'] as String),
      approvalDate: json['approvalDate'] != null
          ? DateTime.parse(json['approvalDate'] as String)
          : null,
      rejectionDate: json['rejectionDate'] != null
          ? DateTime.parse(json['rejectionDate'] as String)
          : null,
      rejectionReason: json['rejectionReason'] as String?,
      completionDate: json['completionDate'] != null
          ? DateTime.parse(json['completionDate'] as String)
          : null,
      estimatedCompletionDate: json['estimatedCompletionDate'] != null
          ? DateTime.parse(json['estimatedCompletionDate'] as String)
          : null,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان طلب التقييم العقاري إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'providerId': providerId,
      'providerName': providerName,
      'estateId': estateId,
      'status': _statusToString(status),
      'valuationType': valuationType,
      'price': price,
      'propertyType': propertyType,
      'area': area,
      'size': size,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'rooms': rooms,
      'bathrooms': bathrooms,
      'age': age,
      'isFurnished': isFurnished,
      'isRenovated': isRenovated,
      'features': features,
      'photoUrls': photoUrls,
      'documents': documents,
      'notes': notes?.map((note) => note.toJson()).toList(),
      'valuationId': valuationId,
      'requestDate': requestDate.toIso8601String(),
      'approvalDate': approvalDate?.toIso8601String(),
      'rejectionDate': rejectionDate?.toIso8601String(),
      'rejectionReason': rejectionReason,
      'completionDate': completionDate?.toIso8601String(),
      'estimatedCompletionDate': estimatedCompletionDate?.toIso8601String(),
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان طلب التقييم العقاري مع تعديل بعض الخصائص
  ValuationRequest copyWith({
    String? id,
    String? userId,
    String? providerId,
    String? providerName,
    String? estateId,
    ValuationRequestStatus? status,
    String? valuationType,
    double? price,
    String? propertyType,
    String? area,
    double? size,
    String? address,
    double? latitude,
    double? longitude,
    int? rooms,
    int? bathrooms,
    int? age,
    bool? isFurnished,
    bool? isRenovated,
    List<String>? features,
    List<String>? photoUrls,
    Map<String, String>? documents,
    List<ValuationRequestNote>? notes,
    String? valuationId,
    DateTime? requestDate,
    DateTime? approvalDate,
    DateTime? rejectionDate,
    String? rejectionReason,
    DateTime? completionDate,
    DateTime? estimatedCompletionDate,
    Map<String, dynamic>? additionalInfo,
  }) {
    return ValuationRequest(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      providerId: providerId ?? this.providerId,
      providerName: providerName ?? this.providerName,
      estateId: estateId ?? this.estateId,
      status: status ?? this.status,
      valuationType: valuationType ?? this.valuationType,
      price: price ?? this.price,
      propertyType: propertyType ?? this.propertyType,
      area: area ?? this.area,
      size: size ?? this.size,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      rooms: rooms ?? this.rooms,
      bathrooms: bathrooms ?? this.bathrooms,
      age: age ?? this.age,
      isFurnished: isFurnished ?? this.isFurnished,
      isRenovated: isRenovated ?? this.isRenovated,
      features: features ?? this.features,
      photoUrls: photoUrls ?? this.photoUrls,
      documents: documents ?? this.documents,
      notes: notes ?? this.notes,
      valuationId: valuationId ?? this.valuationId,
      requestDate: requestDate ?? this.requestDate,
      approvalDate: approvalDate ?? this.approvalDate,
      rejectionDate: rejectionDate ?? this.rejectionDate,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      completionDate: completionDate ?? this.completionDate,
      estimatedCompletionDate: estimatedCompletionDate ?? this.estimatedCompletionDate,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  /// تحويل حالة الطلب من نص إلى قيمة تعداد
  static ValuationRequestStatus _parseStatus(String status) {
    switch (status) {
      case 'pending':
        return ValuationRequestStatus.pending;
      case 'approved':
        return ValuationRequestStatus.approved;
      case 'rejected':
        return ValuationRequestStatus.rejected;
      case 'in_progress':
        return ValuationRequestStatus.inProgress;
      case 'completed':
        return ValuationRequestStatus.completed;
      case 'cancelled':
        return ValuationRequestStatus.cancelled;
      case 'awaiting_payment':
        return ValuationRequestStatus.awaitingPayment;
      case 'awaiting_documents':
        return ValuationRequestStatus.awaitingDocuments;
      default:
        return ValuationRequestStatus.pending;
    }
  }

  /// تحويل حالة الطلب من قيمة تعداد إلى نص
  static String _statusToString(ValuationRequestStatus status) {
    switch (status) {
      case ValuationRequestStatus.pending:
        return 'pending';
      case ValuationRequestStatus.approved:
        return 'approved';
      case ValuationRequestStatus.rejected:
        return 'rejected';
      case ValuationRequestStatus.inProgress:
        return 'in_progress';
      case ValuationRequestStatus.completed:
        return 'completed';
      case ValuationRequestStatus.cancelled:
        return 'cancelled';
      case ValuationRequestStatus.awaitingPayment:
        return 'awaiting_payment';
      case ValuationRequestStatus.awaitingDocuments:
        return 'awaiting_documents';
    }
  }

  /// التحقق مما إذا كان الطلب مكتمل
  bool isComplete() {
    return status == ValuationRequestStatus.completed;
  }

  /// التحقق مما إذا كان الطلب تمت الموافقة عليه
  bool isApproved() {
    return status == ValuationRequestStatus.approved;
  }

  /// التحقق مما إذا كان الطلب مرفوض
  bool isRejected() {
    return status == ValuationRequestStatus.rejected;
  }

  /// التحقق مما إذا كان الطلب في انتظار المستندات
  bool isAwaitingDocuments() {
    return status == ValuationRequestStatus.awaitingDocuments;
  }

  /// التحقق مما إذا كان الطلب في انتظار الدفع
  bool isAwaitingPayment() {
    return status == ValuationRequestStatus.awaitingPayment;
  }

  /// الحصول على المدة المتبقية للإكمال بالأيام
  int? getRemainingDays() {
    if (estimatedCompletionDate == null) {
      return null;
    }

    final now = DateTime.now();
    return estimatedCompletionDate!.difference(now).inDays;
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        providerId,
        providerName,
        estateId,
        status,
        valuationType,
        price,
        propertyType,
        area,
        size,
        address,
        latitude,
        longitude,
        rooms,
        bathrooms,
        age,
        isFurnished,
        isRenovated,
        features,
        photoUrls,
        documents,
        notes,
        valuationId,
        requestDate,
        approvalDate,
        rejectionDate,
        rejectionReason,
        completionDate,
        estimatedCompletionDate,
        additionalInfo,
      ];
}

/// كيان ملاحظة طلب التقييم العقاري
class ValuationRequestNote extends Equatable {
  final String id;
  final String content;
  final String authorId;
  final String authorName;
  final DateTime createdAt;
  final bool isInternal;

  /// إنشاء كيان ملاحظة طلب التقييم العقاري
  const ValuationRequestNote({
    required this.id,
    required this.content,
    required this.authorId,
    required this.authorName,
    required this.createdAt,
    this.isInternal = false,
  });

  /// إنشاء كيان ملاحظة طلب التقييم العقاري من JSON
  factory ValuationRequestNote.fromJson(Map<String, dynamic> json) {
    return ValuationRequestNote(
      id: json['id'] as String,
      content: json['content'] as String,
      authorId: json['authorId'] as String,
      authorName: json['authorName'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      isInternal: json['isInternal'] as bool? ?? false);
  }

  /// تحويل كيان ملاحظة طلب التقييم العقاري إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'authorId': authorId,
      'authorName': authorName,
      'createdAt': createdAt.toIso8601String(),
      'isInternal': isInternal,
    };
  }

  /// نسخ كيان ملاحظة طلب التقييم العقاري مع تعديل بعض الخصائص
  ValuationRequestNote copyWith({
    String? id,
    String? content,
    String? authorId,
    String? authorName,
    DateTime? createdAt,
    bool? isInternal,
  }) {
    return ValuationRequestNote(
      id: id ?? this.id,
      content: content ?? this.content,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      createdAt: createdAt ?? this.createdAt,
      isInternal: isInternal ?? this.isInternal);
  }

  @override
  List<Object?> get props => [
        id,
        content,
        authorId,
        authorName,
        createdAt,
        isInternal,
      ];
}
