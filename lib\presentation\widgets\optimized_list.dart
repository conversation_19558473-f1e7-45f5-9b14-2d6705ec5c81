import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../infrastructure/services/app_manager.dart';
import '../../infrastructure/services/performance_service.dart';

/// قائمة محسنة
class OptimizedList extends StatefulWidget {
  /// عدد العناصر
  final int itemCount;

  /// بناء العنصر
  final Widget Function(BuildContext context, int index) itemBuilder;

  /// ارتفاع العنصر
  final double? itemExtent;

  /// مفتاح التمرير
  final Key? scrollKey;

  /// وحدة التحكم في التمرير
  final ScrollController? controller;

  /// اتجاه التمرير
  final Axis scrollDirection;

  /// سلوك التمرير
  final ScrollPhysics? physics;

  /// تباعد العناصر
  final EdgeInsetsGeometry? padding;

  /// ما إذا كان يجب تخزين موضع التمرير
  final bool cacheScrollPosition;

  /// معرف الشاشة
  final String? screenId;

  /// عدد العناصر التي يتم تحميلها مسبقاً
  final int preloadItemCount;

  /// ما إذا كان يجب استخدام التحميل الكسول
  final bool useLazyLoading;

  /// دالة التحميل المسبق
  final void Function(int index)? onPreload;

  const OptimizedList({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.itemExtent,
    this.scrollKey,
    this.controller,
    this.scrollDirection = Axis.vertical,
    this.physics,
    this.padding,
    this.cacheScrollPosition = false,
    this.screenId,
    this.preloadItemCount = 5,
    this.useLazyLoading = true,
    this.onPreload,
  });

  @override
  _OptimizedListState createState() => _OptimizedListState();
}

class _OptimizedListState extends State<OptimizedList> {
  late ScrollController _scrollController;
  final Map<int, bool> _visibleItems = {};

  @override
  void initState() {
    super.initState();

    // استخدام وحدة التحكم في التمرير المقدمة أو إنشاء واحدة جديدة
    _scrollController = widget.controller ?? ScrollController();

    // استرداد موضع التمرير المخزن
    if (widget.cacheScrollPosition && widget.screenId != null) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _restoreScrollPosition();
      });
    }
  }

  @override
  void dispose() {
    // تخزين موضع التمرير
    if (widget.cacheScrollPosition && widget.screenId != null) {
      _saveScrollPosition();
    }

    // التخلص من وحدة التحكم في التمرير إذا تم إنشاؤها داخلياً
    if (widget.controller == null) {
      _scrollController.dispose();
    }

    super.dispose();
  }

  /// تخزين موضع التمرير
  void _saveScrollPosition() {
    if (_scrollController.hasClients) {
      final position = _scrollController.offset;
      context.dataCacheService.cacheScrollPosition(widget.screenId!, position);
    }
  }

  /// استرداد موضع التمرير المخزن
  Future<void> _restoreScrollPosition() async {
    final position =
        context.dataCacheService.getCachedScrollPosition(widget.screenId!);
    if (position != null && _scrollController.hasClients) {
      _scrollController.jumpTo(position);
    }
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تحديد ما إذا كان يجب استخدام التحميل الكسول
    final shouldUseLazyLoading =
        widget.useLazyLoading && performanceService.isPreloadingEnabled();

    // تحديد عدد العناصر التي يتم تحميلها مسبقاً
    final preloadCount = performanceService.getAppropriatePreloadCount();

    return ListView.builder(
      key: widget.scrollKey,
      controller: _scrollController,
      itemCount: widget.itemCount,
      itemExtent: widget.itemExtent,
      scrollDirection: widget.scrollDirection,
      physics: widget.physics,
      padding: widget.padding,
      itemBuilder: (context, index) {
        // إذا كان التحميل الكسول مفعل، نستخدم VisibilityDetector
        if (shouldUseLazyLoading) {
          return VisibilityDetector(
            key: Key('item-$index'),
            onVisibilityChanged: (info) {
              final isVisible = info.visibleFraction > 0;

              // إذا أصبح العنصر مرئياً
              if (isVisible && !_visibleItems.containsKey(index)) {
                _visibleItems[index] = true;

                // التحميل المسبق للعناصر القادمة
                if (widget.onPreload != null) {
                  for (int i = 1; i <= preloadCount; i++) {
                    final preloadIndex = index + i;
                    if (preloadIndex < widget.itemCount &&
                        !_visibleItems.containsKey(preloadIndex)) {
                      widget.onPreload!(preloadIndex);
                      _visibleItems[preloadIndex] = true;
                    }
                  }
                }
              }
            },
            child: widget.itemBuilder(context, index));
        }

        // إذا كان التحميل الكسول معطل، نستخدم البناء العادي
        return widget.itemBuilder(context, index);
      });
  }
}

/// شبكة محسنة
class OptimizedGrid extends StatefulWidget {
  /// عدد العناصر
  final int itemCount;

  /// بناء العنصر
  final Widget Function(BuildContext context, int index) itemBuilder;

  /// عدد الأعمدة
  final int crossAxisCount;

  /// نسبة العرض إلى الارتفاع
  final double childAspectRatio;

  /// المسافة الرئيسية بين العناصر
  final double mainAxisSpacing;

  /// المسافة الثانوية بين العناصر
  final double crossAxisSpacing;

  /// مفتاح التمرير
  final Key? scrollKey;

  /// وحدة التحكم في التمرير
  final ScrollController? controller;

  /// سلوك التمرير
  final ScrollPhysics? physics;

  /// تباعد العناصر
  final EdgeInsetsGeometry? padding;

  /// ما إذا كان يجب تخزين موضع التمرير
  final bool cacheScrollPosition;

  /// معرف الشاشة
  final String? screenId;

  /// عدد العناصر التي يتم تحميلها مسبقاً
  final int preloadItemCount;

  /// ما إذا كان يجب استخدام التحميل الكسول
  final bool useLazyLoading;

  /// دالة التحميل المسبق
  final void Function(int index)? onPreload;

  const OptimizedGrid({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    required this.crossAxisCount,
    this.childAspectRatio = 1.0,
    this.mainAxisSpacing = 0.0,
    this.crossAxisSpacing = 0.0,
    this.scrollKey,
    this.controller,
    this.physics,
    this.padding,
    this.cacheScrollPosition = false,
    this.screenId,
    this.preloadItemCount = 10,
    this.useLazyLoading = true,
    this.onPreload,
  });

  @override
  _OptimizedGridState createState() => _OptimizedGridState();
}

class _OptimizedGridState extends State<OptimizedGrid> {
  late ScrollController _scrollController;
  final Map<int, bool> _visibleItems = {};

  @override
  void initState() {
    super.initState();

    // استخدام وحدة التحكم في التمرير المقدمة أو إنشاء واحدة جديدة
    _scrollController = widget.controller ?? ScrollController();

    // استرداد موضع التمرير المخزن
    if (widget.cacheScrollPosition && widget.screenId != null) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _restoreScrollPosition();
      });
    }
  }

  @override
  void dispose() {
    // تخزين موضع التمرير
    if (widget.cacheScrollPosition && widget.screenId != null) {
      _saveScrollPosition();
    }

    // التخلص من وحدة التحكم في التمرير إذا تم إنشاؤها داخلياً
    if (widget.controller == null) {
      _scrollController.dispose();
    }

    super.dispose();
  }

  /// تخزين موضع التمرير
  void _saveScrollPosition() {
    if (_scrollController.hasClients) {
      final position = _scrollController.offset;
      context.dataCacheService.cacheScrollPosition(widget.screenId!, position);
    }
  }

  /// استرداد موضع التمرير المخزن
  Future<void> _restoreScrollPosition() async {
    final position =
        context.dataCacheService.getCachedScrollPosition(widget.screenId!);
    if (position != null && _scrollController.hasClients) {
      _scrollController.jumpTo(position);
    }
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تحديد ما إذا كان يجب استخدام التحميل الكسول
    final shouldUseLazyLoading =
        widget.useLazyLoading && performanceService.isPreloadingEnabled();

    // تحديد عدد العناصر التي يتم تحميلها مسبقاً
    final preloadCount = performanceService.getAppropriatePreloadCount();

    return GridView.builder(
      key: widget.scrollKey,
      controller: _scrollController,
      itemCount: widget.itemCount,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        mainAxisSpacing: widget.mainAxisSpacing,
        crossAxisSpacing: widget.crossAxisSpacing),
      physics: widget.physics,
      padding: widget.padding,
      itemBuilder: (context, index) {
        // إذا كان التحميل الكسول مفعل، نستخدم VisibilityDetector
        if (shouldUseLazyLoading) {
          return VisibilityDetector(
            key: Key('item-$index'),
            onVisibilityChanged: (info) {
              final isVisible = info.visibleFraction > 0;

              // إذا أصبح العنصر مرئياً
              if (isVisible && !_visibleItems.containsKey(index)) {
                _visibleItems[index] = true;

                // التحميل المسبق للعناصر القادمة
                if (widget.onPreload != null) {
                  for (int i = 1; i <= preloadCount; i++) {
                    final preloadIndex = index + i;
                    if (preloadIndex < widget.itemCount &&
                        !_visibleItems.containsKey(preloadIndex)) {
                      widget.onPreload!(preloadIndex);
                      _visibleItems[preloadIndex] = true;
                    }
                  }
                }
              }
            },
            child: widget.itemBuilder(context, index));
        }

        // إذا كان التحميل الكسول معطل، نستخدم البناء العادي
        return widget.itemBuilder(context, index);
      });
  }
}

/// قائمة محسنة مع سحب للتحديث
class OptimizedRefreshableList extends StatefulWidget {
  /// عدد العناصر
  final int itemCount;

  /// بناء العنصر
  final Widget Function(BuildContext context, int index) itemBuilder;

  /// ارتفاع العنصر
  final double? itemExtent;

  /// مفتاح التمرير
  final Key? scrollKey;

  /// وحدة التحكم في التمرير
  final ScrollController? controller;

  /// اتجاه التمرير
  final Axis scrollDirection;

  /// سلوك التمرير
  final ScrollPhysics? physics;

  /// تباعد العناصر
  final EdgeInsetsGeometry? padding;

  /// ما إذا كان يجب تخزين موضع التمرير
  final bool cacheScrollPosition;

  /// معرف الشاشة
  final String? screenId;

  /// عدد العناصر التي يتم تحميلها مسبقاً
  final int preloadItemCount;

  /// ما إذا كان يجب استخدام التحميل الكسول
  final bool useLazyLoading;

  /// دالة التحميل المسبق
  final void Function(int index)? onPreload;

  /// دالة التحديث
  final Future<void> Function() onRefresh;

  const OptimizedRefreshableList({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.itemExtent,
    this.scrollKey,
    this.controller,
    this.scrollDirection = Axis.vertical,
    this.physics,
    this.padding,
    this.cacheScrollPosition = false,
    this.screenId,
    this.preloadItemCount = 5,
    this.useLazyLoading = true,
    this.onPreload,
    required this.onRefresh,
  });

  @override
  _OptimizedRefreshableListState createState() =>
      _OptimizedRefreshableListState();
}

class _OptimizedRefreshableListState extends State<OptimizedRefreshableList> {
  late ScrollController _scrollController;
  final Map<int, bool> _visibleItems = {};

  @override
  void initState() {
    super.initState();

    // استخدام وحدة التحكم في التمرير المقدمة أو إنشاء واحدة جديدة
    _scrollController = widget.controller ?? ScrollController();

    // استرداد موضع التمرير المخزن
    if (widget.cacheScrollPosition && widget.screenId != null) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _restoreScrollPosition();
      });
    }
  }

  @override
  void dispose() {
    // تخزين موضع التمرير
    if (widget.cacheScrollPosition && widget.screenId != null) {
      _saveScrollPosition();
    }

    // التخلص من وحدة التحكم في التمرير إذا تم إنشاؤها داخلياً
    if (widget.controller == null) {
      _scrollController.dispose();
    }

    super.dispose();
  }

  /// تخزين موضع التمرير
  void _saveScrollPosition() {
    if (_scrollController.hasClients) {
      final position = _scrollController.offset;
      context.dataCacheService.cacheScrollPosition(widget.screenId!, position);
    }
  }

  /// استرداد موضع التمرير المخزن
  Future<void> _restoreScrollPosition() async {
    final position =
        context.dataCacheService.getCachedScrollPosition(widget.screenId!);
    if (position != null && _scrollController.hasClients) {
      _scrollController.jumpTo(position);
    }
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على خدمة الأداء
    final performanceService = context.performanceService;

    // تحديد ما إذا كان يجب استخدام التحميل الكسول
    final shouldUseLazyLoading =
        widget.useLazyLoading && performanceService.isPreloadingEnabled();

    // تحديد عدد العناصر التي يتم تحميلها مسبقاً
    final preloadCount = performanceService.getAppropriatePreloadCount();

    return RefreshIndicator(
      onRefresh: widget.onRefresh,
      child: ListView.builder(
        key: widget.scrollKey,
        controller: _scrollController,
        itemCount: widget.itemCount,
        itemExtent: widget.itemExtent,
        scrollDirection: widget.scrollDirection,
        physics: widget.physics ?? const AlwaysScrollableScrollPhysics(),
        padding: widget.padding,
        itemBuilder: (context, index) {
          // إذا كان التحميل الكسول مفعل، نستخدم VisibilityDetector
          if (shouldUseLazyLoading) {
            return VisibilityDetector(
              key: Key('item-$index'),
              onVisibilityChanged: (info) {
                final isVisible = info.visibleFraction > 0;

                // إذا أصبح العنصر مرئياً
                if (isVisible && !_visibleItems.containsKey(index)) {
                  _visibleItems[index] = true;

                  // التحميل المسبق للعناصر القادمة
                  if (widget.onPreload != null) {
                    for (int i = 1; i <= preloadCount; i++) {
                      final preloadIndex = index + i;
                      if (preloadIndex < widget.itemCount &&
                          !_visibleItems.containsKey(preloadIndex)) {
                        widget.onPreload!(preloadIndex);
                        _visibleItems[preloadIndex] = true;
                      }
                    }
                  }
                }
              },
              child: widget.itemBuilder(context, index));
          }

          // إذا كان التحميل الكسول معطل، نستخدم البناء العادي
          return widget.itemBuilder(context, index);
        }));
  }
}
