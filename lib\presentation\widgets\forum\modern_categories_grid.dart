import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/category_model.dart';
import '../../../core/enums/view_modes.dart';

/// شبكة عرض فئات المنتدى الحديثة
class ModernCategoriesGrid extends StatelessWidget {
  /// قائمة الفئات
  final List<CategoryModel> categories;

  /// دالة يتم استدعاؤها عند النقر على فئة
  final Function(CategoryModel) onCategoryTap;

  /// نمط العرض
  final CategoryViewMode viewMode;

  const ModernCategoriesGrid({
    super.key,
    required this.categories,
    required this.onCategoryTap,
    this.viewMode = CategoryViewMode.grid,
  });

  @override
  Widget build(BuildContext context) {
    if (categories.isEmpty) {
      return _buildEmptyView();
    }

    return AnimationLimiter(
      child: viewMode == CategoryViewMode.list
          ? _buildListView()
          : viewMode == CategoryViewMode.carousel
              ? _buildCarouselView()
              : _buildGridView());
  }

  /// بناء عرض فارغ
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.category_outlined,
            size: 64,
            color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد فئات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600)),
        ]));
  }

  /// بناء عرض الشبكة
  Widget _buildGridView() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        return AnimationConfiguration.staggeredGrid(
          position: index,
          duration: const Duration(milliseconds: 375),
          columnCount: 2,
          child: ScaleAnimation(
            child: FadeInAnimation(
              child: _buildCategoryCard(categories[index]))));
      });
  }

  /// بناء عرض القائمة
  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 375),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: _buildCategoryListItem(categories[index]))));
      });
  }

  /// بناء عرض المعرض
  Widget _buildCarouselView() {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildCategoryCarouselItem(categories[index]))));
        }));
  }

  /// بناء بطاقة الفئة
  Widget _buildCategoryCard(CategoryModel category) {
    // تحويل اللون من سلسلة نصية إلى كائن Color
    final Color categoryColor = _getCategoryColor(category.color);

    return Card(
      elevation: 3,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () => onCategoryTap(category),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                categoryColor.withOpacity(0.7),
                categoryColor,
              ])),
          child: Stack(
            children: [
              // زخرفة الخلفية
              Positioned(
                right: -20,
                bottom: -20,
                child: Icon(
                  _getCategoryIcon(category.icon),
                  size: 100,
                  color: Colors.white.withOpacity(0.2))),

              // محتوى البطاقة
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // أيقونة الفئة
                    Icon(
                      _getCategoryIcon(category.icon),
                      color: Colors.white,
                      size: 24),

                    // معلومات الفئة
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category.name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis),
                        const SizedBox(height: 4),
                        Text(
                          '${category.topicsCount} موضوع • ${category.postsCount} مشاركة',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 12)),
                      ]),
                  ])),
            ]))));
  }

  /// بناء عنصر قائمة الفئة
  Widget _buildCategoryListItem(CategoryModel category) {
    // تحويل اللون من سلسلة نصية إلى كائن Color
    final Color categoryColor = _getCategoryColor(category.color);

    return Card(
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => onCategoryTap(category),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة الفئة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: categoryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12)),
                child: Icon(
                  _getCategoryIcon(category.icon),
                  color: categoryColor,
                  size: 24)),
              const SizedBox(width: 16),

              // معلومات الفئة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16)),
                    const SizedBox(height: 4),
                    Text(
                      category.description,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis),
                  ])),

              // عدد المواضيع والمشاركات
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${category.topicsCount} موضوع',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12)),
                  const SizedBox(height: 4),
                  Text(
                    '${category.postsCount} مشاركة',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12)),
                ]),
            ]))));
  }

  /// بناء عنصر معرض الفئة
  Widget _buildCategoryCarouselItem(CategoryModel category) {
    // تحويل اللون من سلسلة نصية إلى كائن Color
    final Color categoryColor = _getCategoryColor(category.color);

    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 16),
      child: Card(
        elevation: 3,
        shadowColor: Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16)),
        child: InkWell(
          onTap: () => onCategoryTap(category),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  categoryColor.withOpacity(0.7),
                  categoryColor,
                ])),
            child: Stack(
              children: [
                // زخرفة الخلفية
                Positioned(
                  right: -20,
                  bottom: -20,
                  child: Icon(
                    _getCategoryIcon(category.icon),
                    size: 80,
                    color: Colors.white.withOpacity(0.2))),

                // محتوى البطاقة
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // أيقونة الفئة
                      Icon(
                        _getCategoryIcon(category.icon),
                        color: Colors.white,
                        size: 24),
                      const SizedBox(height: 8),

                      // اسم الفئة
                      Text(
                        category.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis),
                      const SizedBox(height: 4),

                      // عدد المواضيع والمشاركات
                      Text(
                        '${category.topicsCount} موضوع',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 12)),
                    ])),
              ])))));
  }

  /// الحصول على لون الفئة
  Color _getCategoryColor(String colorString) {
    if (colorString.startsWith('#')) {
      try {
        return Color(int.parse('0xFF${colorString.substring(1)}'));
      } catch (e) {
        return AppColors.primary;
      }
    }

    // لون افتراضي
    return AppColors.primary;
  }

  /// الحصول على أيقونة الفئة
  IconData _getCategoryIcon(String iconString) {
    switch (iconString) {
      case 'home':
        return Icons.home;
      case 'business':
        return Icons.business;
      case 'apartment':
        return Icons.apartment;
      case 'house':
        return Icons.house;
      case 'villa':
        return Icons.villa;
      case 'location_city':
        return Icons.location_city;
      case 'store':
        return Icons.store;
      case 'local_offer':
        return Icons.local_offer;
      case 'help':
        return Icons.help;
      case 'question_answer':
        return Icons.question_answer;
      case 'forum':
        return Icons.forum;
      case 'chat':
        return Icons.chat;
      case 'announcement':
        return Icons.announcement;
      case 'campaign':
        return Icons.campaign;
      case 'category':
        return Icons.category;
      default:
        return Icons.forum;
    }
  }
}
