import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kuwait_corners/core/services/referral_tracking_service.dart';
import 'package:kuwait_corners/core/services/kuwait_address_validation_service.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/presentation/pages/login_page.dart';
import 'package:kuwait_corners/presentation/pages/map_picker_page.dart';
import 'package:kuwait_corners/presentation/pages/legal/terms_and_conditions_page.dart';
import 'package:kuwait_corners/presentation/pages/legal/privacy_policy_page.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// صفحة تسجيل التفاصيل الإضافية للمستخدمين.
/// يتم عرض الحقول بشكل مبسط مع تحسينات تصميمية، بخلفية بيضاء ناصعة.
class RegisterDetailsPage extends StatefulWidget {
  final String userType; // "user", "owner", or "company"

  const RegisterDetailsPage({super.key, required this.userType});

  @override
  State<RegisterDetailsPage> createState() => _RegisterDetailsPageState();
}

class _RegisterDetailsPageState extends State<RegisterDetailsPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();

  // متغيرات للتحكم بالرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // متغيرات للتحكم بخطوات التسجيل
  int _currentStep = 1;
  final int _totalSteps = 3;
  final PageController _pageController = PageController(initialPage: 0);

  // خدمة تتبع الإحالات
  final ReferralTrackingService _trackingService = ReferralTrackingService();

  // متغيرات للتحكم بحقل كلمة المرور
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // مؤشر قوة كلمة المرور
  double _passwordStrength = 0.0;
  String _passwordStrengthText = "ضعيفة";
  Color _passwordStrengthColor = Colors.red;

  // قيم الحقول
  String fullNameOrCompanyName = '';
  String email = '';
  String password = '';
  String confirmPassword = '';
  String? phone;
  String? address;
  String? postalCode;
  String? referralCode;
  File? docFile;
  double? lat;
  double? lng;

  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  // متغيرات الموافقة على الشروط والأحكام
  bool _acceptTermsAndConditions = false;
  bool _acceptPrivacyPolicy = false;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutQuad));

    // بدء الرسوم المتحركة
    _animationController.forward();

    // التحقق من وجود رمز إحالة محفوظ
    _checkForPendingReferralCode();
  }

  /// التحقق من وجود رمز إحالة محفوظ
  Future<void> _checkForPendingReferralCode() async {
    try {
      // استخدام خدمة تتبع الإحالات للتحقق من وجود معلومات إحالة معلقة
      final pendingReferral = await _trackingService.checkPendingReferral();
      final pendingReferralCode = pendingReferral['referralCode'];
      final pendingReferralId = pendingReferral['referralId'];

      if (pendingReferralCode != null && pendingReferralCode.isNotEmpty) {
        setState(() {
          referralCode = pendingReferralCode;
          // تخزين معرف الإحالة في متغير مؤقت لاستخدامه عند التسجيل
          _referralId = pendingReferralId;
        });
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // متغير مؤقت لتخزين معرف الإحالة
  String? _referralId;

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  // التحقق من قوة كلمة المرور
  void _checkPasswordStrength(String password) {
    double strength = 0;

    if (password.isEmpty) {
      setState(() {
        _passwordStrength = 0;
        _passwordStrengthText = "ضعيفة";
        _passwordStrengthColor = Colors.red;
      });
      return;
    }

    // التحقق من طول كلمة المرور
    if (password.length >= 8) {
      strength += 0.25;
    }

    // التحقق من وجود أحرف كبيرة وصغيرة
    if (password.contains(RegExp(r'[A-Z]')) &&
        password.contains(RegExp(r'[a-z]'))) {
      strength += 0.25;
    }

    // التحقق من وجود أرقام
    if (password.contains(RegExp(r'[0-9]'))) {
      strength += 0.25;
    }

    // التحقق من وجود رموز خاصة
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      strength += 0.25;
    }

    // تحديث مؤشر قوة كلمة المرور
    setState(() {
      _passwordStrength = strength;

      if (strength <= 0.25) {
        _passwordStrengthText = "ضعيفة";
        _passwordStrengthColor = Colors.red;
      } else if (strength <= 0.5) {
        _passwordStrengthText = "متوسطة";
        _passwordStrengthColor = Colors.orange;
      } else if (strength <= 0.75) {
        _passwordStrengthText = "جيدة";
        _passwordStrengthColor = Colors.yellow.shade700;
      } else {
        _passwordStrengthText = "قوية";
        _passwordStrengthColor = Colors.green;
      }
    });
  }

  // الانتقال إلى الخطوة التالية
  void _nextStep() {
    if (_currentStep < _totalSteps) {
      setState(() {
        _currentStep++;
      });
      _pageController.animateToPage(
        _currentStep - 1,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut);

      // إعادة تشغيل الرسوم المتحركة
      _animationController.reset();
      _animationController.forward();
    }
  }

  // الرجوع إلى الخطوة السابقة
  void _previousStep() {
    if (_currentStep > 1) {
      setState(() {
        _currentStep--;
      });
      _pageController.animateToPage(
        _currentStep - 1,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut);

      // إعادة تشغيل الرسوم المتحركة
      _animationController.reset();
      _animationController.forward();
    }
  }

  /// التحقق من صحة البريد الإلكتروني الكويتي
  bool _isValidKuwaitEmail(String? email) {
    if (email == null) return false;
    return KuwaitAddressValidationService.isValidKuwaitEmail(email);
  }

  /// التحقق من صحة رقم الهاتف الكويتي
  bool _isValidKuwaitPhone(String? phone) {
    if (phone == null || phone.isEmpty) return false;

    // إزالة الرموز غير الرقمية
    final cleanPhone = phone.replaceAll(RegExp(r'[^0-9]'), '');

    // التحقق من أرقام الهواتف الكويتية
    // أرقام الهواتف المحمولة: تبدأ بـ 5, 6, 9 وتتكون من 8 أرقام
    // أرقام الهواتف الثابتة: تبدأ بـ 2 وتتكون من 8 أرقام
    // مع رمز الدولة +965 (اختياري)

    if (cleanPhone.startsWith('965')) {
      // مع رمز الدولة
      final phoneWithoutCountryCode = cleanPhone.substring(3);
      return phoneWithoutCountryCode.length == 8 &&
             (phoneWithoutCountryCode.startsWith('5') ||
              phoneWithoutCountryCode.startsWith('6') ||
              phoneWithoutCountryCode.startsWith('9') ||
              phoneWithoutCountryCode.startsWith('2'));
    } else {
      // بدون رمز الدولة
      return cleanPhone.length == 8 &&
             (cleanPhone.startsWith('5') ||
              cleanPhone.startsWith('6') ||
              cleanPhone.startsWith('9') ||
              cleanPhone.startsWith('2'));
    }
  }

  /// اختيار صورة من المعرض لتحميلها
  Future<void> _pickDocument() async {
    try {
      final XFile? pickedFile =
          await _picker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        setState(() {
          docFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      debugPrint("Error picking image: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("حدث خطأ أثناء اختيار الصورة: $e")));
    }
  }

  /// الانتقال إلى صفحة اختيار الموقع
  Future<void> _pickLocation() async {
    final LatLng? result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const MapPickerPage()));
    if (result != null) {
      setState(() {
        lat = result.latitude;
        lng = result.longitude;
      });
    }
  }

  /// تقديم بيانات التسجيل بعد التحقق
  void _submit() {
    if (_formKey.currentState!.validate()) {
      // التحقق من الموافقة على الشروط والأحكام
      if (!_acceptTermsAndConditions) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يجب الموافقة على شروط وأحكام الاستخدام'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      if (!_acceptPrivacyPolicy) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يجب الموافقة على سياسة الخصوصية'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      _formKey.currentState!.save();

      // تسجيل إكمال عملية الإحالة إذا كان هناك رمز إحالة
      if (referralCode != null && referralCode!.isNotEmpty) {
        _trackingService.completeReferral();
      }

      context.read<AuthBloc>().add(
            SignupRequested(
              email: email,
              password: password,
              confirmPassword: confirmPassword,
              userType: widget.userType,
              fullNameOrCompanyName: fullNameOrCompanyName,
              phone: phone,
              address: address,
              postalCode: postalCode,
              docPath: docFile?.path,
              lat: lat,
              lng: lng,
              referralCode: referralCode,
              referralId: _referralId));
    }
  }

  /// Helper method لبناء حقل نص مع تنسيق عصري.
  Widget _buildTextField({
    required String label,
    required IconData icon,
    String? hintText,
    TextInputType keyboardType = TextInputType.text,
    bool obscureText = false,
    bool isPassword = false,
    bool isConfirmPassword = false,
    String? Function(String?)? validator,
    void Function(String?)? onSaved,
    void Function(String)? onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4)),
        ]),
      child: TextFormField(
        keyboardType: keyboardType,
        obscureText: isPassword
            ? _obscurePassword
            : (isConfirmPassword ? _obscureConfirmPassword : obscureText),
        validator: validator,
        onSaved: onSaved,
        onChanged: (value) {
          if (isPassword) {
            _checkPasswordStrength(value);
          }
          if (onChanged != null) {
            onChanged(value);
          }
        },
        style: const TextStyle(fontSize: 16),
        decoration: InputDecoration(
          labelText: label,
          hintText: hintText,
          labelStyle: const TextStyle(color: Colors.black87),
          prefixIcon: Icon(icon, color: AppColors.primary),
          suffixIcon: isPassword
              ? IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility_off : Icons.visibility,
                    color: Colors.grey),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  })
              : isConfirmPassword
                  ? IconButton(
                      icon: Icon(
                        _obscureConfirmPassword
                            ? Icons.visibility_off
                            : Icons.visibility,
                        color: Colors.grey),
                      onPressed: () {
                        setState(() {
                          _obscureConfirmPassword = !_obscureConfirmPassword;
                        });
                      })
                  : null,
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16.0),
            borderSide: BorderSide.none),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16.0),
            borderSide: BorderSide(color: Colors.grey.shade200)),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16.0),
            borderSide: const BorderSide(color: AppColors.primary, width: 2)),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16.0),
            borderSide: BorderSide(color: Colors.red.shade300)),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16.0),
            borderSide: const BorderSide(color: Colors.red, width: 2)),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16))));
  }

  /// بناء مؤشر قوة كلمة المرور
  Widget _buildPasswordStrengthIndicator() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: _passwordStrength,
                backgroundColor: Colors.grey.shade200,
                color: _passwordStrengthColor,
                minHeight: 5,
                borderRadius: BorderRadius.circular(5))),
            const SizedBox(width: 12),
            Text(
              _passwordStrengthText,
              style: TextStyle(
                color: _passwordStrengthColor,
                fontSize: 12,
                fontWeight: FontWeight.bold)),
          ]),
        const SizedBox(height: 4),
        Text(
          "كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام ورموز",
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600)),
      ]);
  }

  /// بناء قسم الموافقة على الشروط والأحكام
  Widget _buildTermsAndConditionsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Icon(
                Icons.gavel,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'الشروط والأحكام',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الموافقة على شروط الاستخدام
          Row(
            children: [
              Checkbox(
                value: _acceptTermsAndConditions,
                onChanged: (value) {
                  setState(() {
                    _acceptTermsAndConditions = value ?? false;
                  });
                },
                activeColor: AppColors.primary,
              ),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                    children: [
                      const TextSpan(text: 'أوافق على '),
                      TextSpan(
                        text: 'شروط وأحكام الاستخدام',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const TermsAndConditionsPage(),
                              ),
                            );
                          },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // الموافقة على سياسة الخصوصية
          Row(
            children: [
              Checkbox(
                value: _acceptPrivacyPolicy,
                onChanged: (value) {
                  setState(() {
                    _acceptPrivacyPolicy = value ?? false;
                  });
                },
                activeColor: AppColors.primary,
              ),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                    children: [
                      const TextSpan(text: 'أوافق على '),
                      TextSpan(
                        text: 'سياسة الخصوصية',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const PrivacyPolicyPage(),
                              ),
                            );
                          },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // رسالة تنبيهية
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.info.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.info,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'الموافقة على الشروط والأحكام وسياسة الخصوصية مطلوبة لإنشاء حساب جديد',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.info,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Helper method لبناء زر إجراء مع أيقونة ونص.
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 3)),
        ]),
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, color: AppColors.primary),
        label: Text(
          label,
          style: const TextStyle(
            color: AppColors.primary,
            fontSize: 16,
            fontWeight: FontWeight.w500)),
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: AppColors.primary, width: 1.5),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16)))));
  }

  /// زر تسجيل الحساب بتصميم عصري
  Widget _buildRegisterButton() {
    return Container(
      width: double.infinity,
      height: 55,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4)),
        ]),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _submit,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16)),
          elevation: 0),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // المحتوى الأساسي للزر مع الأيقونة والنص
            Opacity(
              opacity: _isLoading ? 0.0 : 1.0,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: const [
                  Icon(Icons.how_to_reg, size: 22, color: Colors.white),
                  SizedBox(width: 10),
                  Text(
                    'تسجيل الحساب',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white)),
                ])),
            // مؤشر دوران عند بدء عملية التسجيل
            if (_isLoading)
              const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 2)),
          ])));
  }

  /// بناء مؤشر التقدم في خطوات التسجيل
  Widget _buildStepProgressIndicator() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "الخطوة $_currentStep من $_totalSteps",
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppColors.primary)),
          ]),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Container(
                height: 6,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(3),
                  color: Colors.grey.shade200),
                child: Row(
                  children: [
                    Expanded(
                      flex: _currentStep,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(3),
                          gradient: LinearGradient(
                            colors: [
                              AppColors.primary,
                              AppColors.primary.withValues(alpha: 0.7),
                            ])))),
                    Expanded(
                      flex: _totalSteps - _currentStep,
                      child: Container()),
                  ]))),
          ]),
      ]);
  }

  // بناء الخطوة الأولى: المعلومات الأساسية
  Widget _buildStep1() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
            _buildTextField(
              label:
                  widget.userType == 'company' ? 'اسم الشركة' : 'الاسم الكامل',
              hintText: widget.userType == 'company'
                  ? 'أدخل اسم الشركة'
                  : 'أدخل الاسم الكامل',
              icon: Icons.person,
              validator: (value) => (value == null || value.isEmpty)
                  ? 'الرجاء إدخال الاسم'
                  : null,
              onSaved: (value) => fullNameOrCompanyName = value!.trim()),
            const SizedBox(height: 20),
            _buildTextField(
              label: 'البريد الإلكتروني',
              hintText: 'أدخل بريدك الإلكتروني',
              icon: Icons.email,
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال البريد الإلكتروني';
                }
                // التحقق من صحة تنسيق البريد الإلكتروني الكويتي
                if (!_isValidKuwaitEmail(value.trim())) {
                  return 'الرجاء إدخال بريد إلكتروني صحيح بنطاق مدعوم';
                }
                return null;
              },
              onSaved: (value) => email = value!.trim()),
            const SizedBox(height: 20),
            _buildTextField(
              label: 'كلمة المرور',
              hintText: 'أدخل كلمة المرور',
              icon: Icons.lock,
              isPassword: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال كلمة المرور';
                }
                if (value.length < 6) {
                  return 'كلمة المرور يجب ألا تقل عن 6 أحرف';
                }
                // تخزين كلمة المرور في المتغير للتحقق لاحقاً
                password = value.trim();
                return null;
              },
              onSaved: (value) => password = value!.trim(),
              onChanged: (value) {
                _checkPasswordStrength(value);
                // تحديث كلمة المرور عند التغيير
                setState(() {
                  password = value.trim();
                });
              }),
            _buildPasswordStrengthIndicator(),
            const SizedBox(height: 20),
            _buildTextField(
              label: 'تأكيد كلمة المرور',
              hintText: 'أعد إدخال كلمة المرور',
              icon: Icons.lock_outline,
              isConfirmPassword: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء تأكيد كلمة المرور';
                }
                // التحقق من تطابق كلمتي المرور
                if (value.trim() != password) {
                  return 'كلمتا المرور غير متطابقتين';
                }
                return null;
              },
              onSaved: (value) => confirmPassword = value!.trim()),
            const SizedBox(height: 32),
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 55,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withValues(alpha: 0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 4)),
                      ]),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          _formKey.currentState!.save();
                          _nextStep();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16)),
                        padding: const EdgeInsets.symmetric(vertical: 12)),
                      icon: const Icon(Icons.arrow_forward, size: 20),
                      label: const Text(
                        "التالي",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold))))),
              ]),
          ]))));
  }

  // بناء الخطوة الثانية: معلومات الاتصال
  Widget _buildStep2() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
            if (widget.userType == 'user' || widget.userType == 'owner') ...[
              _buildTextField(
                label: 'رقم الهاتف',
                hintText: 'مثال: 50123456 أو 96550123456',
                icon: Icons.phone,
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال رقم الهاتف';
                  }
                  if (!_isValidKuwaitPhone(value.trim())) {
                    return 'الرجاء إدخال رقم هاتف كويتي صحيح (يبدأ بـ 2, 5, 6, أو 9)';
                  }
                  return null;
                },
                onSaved: (value) => phone = value),
              const SizedBox(height: 20),
            ],
            _buildTextField(
              label: 'العنوان',
              hintText: 'أدخل عنوانك',
              icon: Icons.location_on,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال العنوان';
                }
                return null;
              },
              onSaved: (value) => address = value),
            const SizedBox(height: 20),
            _buildTextField(
              label: 'الرمز البريدي',
              hintText: 'أدخل الرمز البريدي',
              icon: Icons.markunread_mailbox_outlined,
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال الرمز البريدي';
                }
                return null;
              },
              onSaved: (value) => postalCode = value),
            const SizedBox(height: 32),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _previousStep,
                    icon: const Icon(Icons.arrow_back, size: 20),
                    label: const Text(
                      "السابق",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500)),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: const BorderSide(
                          color: AppColors.primary, width: 1.5),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16))))),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    height: 55,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withValues(alpha: 0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 4)),
                      ]),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          _formKey.currentState!.save();
                          _nextStep();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16)),
                        padding: const EdgeInsets.symmetric(vertical: 12)),
                      icon: const Icon(Icons.arrow_forward, size: 20),
                      label: const Text(
                        "التالي",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold))))),
              ]),
          ]))));
  }

  // بناء الخطوة الثالثة: الوثائق والموقع
  Widget _buildStep3() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
            // حقل رمز الإحالة (اختياري)
            _buildTextField(
              label: 'رمز الإحالة (اختياري)',
              hintText: 'أدخل رمز الإحالة إذا كان لديك',
              icon: Icons.group_add,
              validator: (value) => null, // اختياري
              onSaved: (value) => referralCode = value?.trim()),
            const SizedBox(height: 16),

            if (widget.userType == 'owner' || widget.userType == 'company') ...[
              _buildActionButton(
                icon: Icons.map,
                label: 'تحديد الموقع على الخريطة',
                onPressed: _pickLocation),
              if (lat != null && lng != null)
                Container(
                  margin: const EdgeInsets.only(top: 12.0),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green.shade300)),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle,
                          color: Colors.green, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          "تم اختيار الموقع: ${lat!.toStringAsFixed(4)}, ${lng!.toStringAsFixed(4)}",
                          style: const TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.bold))),
                    ])),
              const SizedBox(height: 20),
              _buildActionButton(
                icon: Icons.upload_file,
                label: widget.userType == 'owner'
                    ? 'تحميل وثيقة الملكية'
                    : 'تحميل الوثيقة التجارية',
                onPressed: _pickDocument),
              if (docFile != null)
                Container(
                  margin: const EdgeInsets.only(top: 12.0),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green.shade300)),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle,
                          color: Colors.green, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          "تم اختيار الوثيقة: ${docFile!.path.split('/').last}",
                          style: const TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.bold))),
                    ])),
              const SizedBox(height: 20),
            ],

            // قسم الموافقة على الشروط والأحكام
            _buildTermsAndConditionsSection(),

            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _previousStep,
                    icon: const Icon(Icons.arrow_back, size: 20),
                    label: const Text(
                      "السابق",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500)),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: const BorderSide(
                          color: AppColors.primary, width: 1.5),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16))))),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildRegisterButton()),
              ]),
          ]))));
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // إذا كان المستخدم في الخطوة الأولى، العودة لصفحة اختيار نوع المستخدم
        if (_currentStep == 1) {
          return true; // السماح بالعودة الطبيعية
        } else {
          // إذا كان في خطوة أخرى، العودة للخطوة السابقة
          _previousStep();
          return false; // منع العودة الطبيعية
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.userType == 'company'
                ? "تسجيل شركة جديدة"
                : widget.userType == 'owner'
                    ? "تسجيل مالك عقار"
                    : widget.userType == 'agent'
                        ? "تسجيل مستثمر"
                        : "تسجيل مستخدم جديد"),
          centerTitle: true,
          elevation: 0,
          backgroundColor: Colors.transparent,
          foregroundColor: AppColors.primary),
      // خلفية متدرجة للصفحة
      body: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Colors.grey.shade50,
              Colors.grey.shade100,
            ])),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16),
              child: BlocListener<AuthBloc, AuthState>(
                listener: (context, state) {
                  // معالجة حالة التحميل
                  if (state is AuthLoading) {
                    setState(() {
                      _isLoading = true;
                    });
                    return;
                  }

                  // إيقاف التحميل للحالات الأخرى
                  setState(() {
                    _isLoading = false;
                  });

                  // معالجة نجاح التسجيل
                  if (state is SignupSuccess) {
                    // إذا تم استخدام رمز إحالة، نقوم بمسحه من التخزين المحلي
                    if (referralCode != null && referralCode!.isNotEmpty) {
                      SharedPreferences.getInstance().then((prefs) {
                        prefs.remove('pending_referral_code');
                      });
                    }

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(state.message),
                        backgroundColor: AppColors.success,
                        duration: const Duration(seconds: 3)));

                    // تأخير قصير قبل الانتقال للسماح بعرض الرسالة
                    Future.delayed(const Duration(milliseconds: 500), () {
                      if (mounted) {
                        Navigator.pushAndRemoveUntil(
                          context,
                          MaterialPageRoute(builder: (_) => const LoginPage()),
                          (route) => false);
                      }
                    });
                  }
                  // معالجة أخطاء التسجيل فقط
                  else if (state is AuthError) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(state.message),
                        backgroundColor: AppColors.error,
                        duration: const Duration(seconds: 3)));
                  }
                },
                child: Column(
                  children: [
                    // شعار التطبيق
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(20),
                            blurRadius: 15,
                            offset: const Offset(0, 5)),
                        ]),
                      child: Padding(
                        padding: const EdgeInsets.all(15),
                        child: Image.asset(
                          'assets/images/logo.png',
                          fit: BoxFit.contain))),
                    const SizedBox(height: 20),
                    // مؤشر التقدم في خطوات التسجيل
                    _buildStepProgressIndicator(),
                    const SizedBox(height: 24),
                    // نموذج التسجيل
                    Form(
                      key: _formKey,
                      child: SizedBox(
                        height: MediaQuery.of(context).size.height * 0.6, // ارتفاع ديناميكي للنموذج
                        child: PageView(
                          controller: _pageController,
                          physics: const NeverScrollableScrollPhysics(),
                          children: [
                            _buildStep1(),
                            _buildStep2(),
                            _buildStep3(),
                          ]))),
                    const SizedBox(height: 24),
                    // زر العودة إلى صفحة تسجيل الدخول
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text(
                          "هل لديك حساب بالفعل؟",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey)),
                        TextButton(
                          onPressed: () {
                            Navigator.pushReplacement(
                              context,
                              MaterialPageRoute(
                                  builder: (_) => const LoginPage()));
                          },
                          child: const Text(
                            "تسجيل الدخول",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary))),
                      ]),
                  ]))))))));
  }
}
