import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../data/kuwait_locations.dart';
import '../../domain/entities/market_prediction.dart';
import '../../infrastructure/services/market_prediction_service.dart';
import '../widgets/app_bar_widget.dart';
import '../widgets/loading_widget.dart';

/// شاشة تنبؤات السوق
class MarketPredictionScreen extends StatefulWidget {
  /// إنشاء شاشة تنبؤات السوق
  const MarketPredictionScreen({super.key});

  @override
  _MarketPredictionScreenState createState() => _MarketPredictionScreenState();
}

class _MarketPredictionScreenState extends State<MarketPredictionScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  PredictionType _selectedType = PredictionType.salePrices;
  PredictionPeriod _selectedPeriod = PredictionPeriod.year;
  String? _selectedArea;
  String? _selectedPropertyType;
  bool _isLoading = false;
  String? _errorMessage;
  List<MarketPrediction> _predictions = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadPredictions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل تنبؤات السوق
  Future<void> _loadPredictions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final predictionService = Provider.of<MarketPredictionService>(
        context,
        listen: false);
      final predictions = await predictionService.getMarketPredictions(
        type: _selectedType,
        period: _selectedPeriod,
        area: _selectedArea,
        propertyType: _selectedPropertyType);

      setState(() {
        _predictions = predictions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل تنبؤات السوق: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: 'تنبؤات سوق العقارات',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog),
        ]),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget()
                : _errorMessage != null
                    ? ErrorWidgetCustom(message: _errorMessage!)
                    : _predictions.isEmpty
                        ? const Center(
                            child: Text(
                              'لا توجد تنبؤات متاحة للمعايير المحددة',
                              style: TextStyle(fontSize: 16)))
                        : _buildTabBarView()),
        ]));
  }

  /// بناء شريط التبويب
  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).primaryColor,
      child: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        tabs: const [
          Tab(text: 'أسعار البيع'),
          Tab(text: 'أسعار الإيجار'),
          Tab(text: 'العرض والطلب'),
          Tab(text: 'العائد الاستثماري'),
        ],
        onTap: (index) {
          setState(() {
            switch (index) {
              case 0:
                _selectedType = PredictionType.salePrices;
                break;
              case 1:
                _selectedType = PredictionType.rentalPrices;
                break;
              case 2:
                _selectedType = PredictionType.supply;
                break;
              case 3:
                _selectedType = PredictionType.investmentReturn;
                break;
            }
          });
          _loadPredictions();
        }));
  }

  /// بناء عرض التبويب
  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildPredictionTab(PredictionType.salePrices),
        _buildPredictionTab(PredictionType.rentalPrices),
        _buildPredictionTab(PredictionType.supply),
        _buildPredictionTab(PredictionType.investmentReturn),
      ]);
  }

  /// بناء تبويب التنبؤ
  Widget _buildPredictionTab(PredictionType type) {
    final prediction = _getPredictionForType(type);
    if (prediction == null) {
      return Center(
        child: Text(
          'لا توجد تنبؤات ${_getPredictionTypeText(type)} متاحة للمعايير المحددة',
          style: const TextStyle(fontSize: 16)));
    }

    final predictionService = Provider.of<MarketPredictionService>(
      context,
      listen: false);
    final predictionChart = predictionService.createPredictionLineChart(
      prediction);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            prediction.title,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Text(prediction.description, style: const TextStyle(fontSize: 16)),
          const SizedBox(height: 16),
          _buildPeriodSelector(),
          const SizedBox(height: 16),
          _buildPredictionSummary(prediction),
          const SizedBox(height: 16),
          Container(
            height: 300,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, 3)),
              ]),
            child: LineChart(predictionChart)),
          const SizedBox(height: 24),
          const Text(
            'العوامل المؤثرة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          _buildFactorsChart(prediction),
          const SizedBox(height: 24),
          const Text(
            'الاستنتاجات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          ...prediction.insights.map(
            (insight) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('• ', style: TextStyle(fontSize: 16)),
                  Expanded(
                    child: Text(insight, style: const TextStyle(fontSize: 16))),
                ]))),
          const SizedBox(height: 24),
          const Text(
            'التوصيات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          ...prediction.recommendations.map(
            (recommendation) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('• ', style: TextStyle(fontSize: 16)),
                  Expanded(
                    child: Text(
                      recommendation,
                      style: const TextStyle(fontSize: 16))),
                ]))),
        ]));
  }

  /// بناء ملخص التنبؤ
  Widget _buildPredictionSummary(MarketPrediction prediction) {
    final changeColor =
        prediction.changePercentage >= 0 ? Colors.green : Colors.red;
    final changeIcon = prediction.changePercentage >= 0
        ? Icons.arrow_upward
        : Icons.arrow_downward;
    final changeText = prediction.changePercentage >= 0 ? 'ارتفاع' : 'انخفاض';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3)),
        ]),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'القيمة الحالية:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              Text(
                prediction.currentValue.toStringAsFixed(2),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'القيمة المتوقعة:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              Text(
                prediction.predictedValue.toStringAsFixed(2),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'نسبة التغيير:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              Row(
                children: [
                  Icon(changeIcon, color: changeColor, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    '${prediction.changePercentage.abs().toStringAsFixed(2)}%',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: changeColor)),
                ]),
            ]),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'الاتجاه:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              Row(
                children: [
                  Icon(changeIcon, color: changeColor, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    changeText,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: changeColor)),
                ]),
            ]),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'مستوى الثقة:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              Text(
                _getConfidenceLevelText(prediction.confidenceLevel),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _getConfidenceLevelColor(prediction.confidenceLevel))),
            ]),
        ]));
  }

  /// بناء مخطط العوامل المؤثرة
  Widget _buildFactorsChart(MarketPrediction prediction) {
    final factors = prediction.factors.entries.toList();
    factors.sort((a, b) => b.value.compareTo(a.value));

    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3)),
        ]),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: factors.first.value * 1.2,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              tooltipBgColor: Colors.blueGrey,
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final factor = factors[groupIndex];
                return BarTooltipItem(
                  '${factor.key}\n',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14),
                  children: <TextSpan>[
                    TextSpan(
                      text: '${factor.value.toStringAsFixed(2)}%',
                      style: const TextStyle(
                        color: Colors.yellow,
                        fontSize: 16,
                        fontWeight: FontWeight.w500)),
                  ]);
              })),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget: (double value, TitleMeta meta) {
                  final index = value.toInt();
                  String text = '';
                  if (index >= 0 && index < factors.length) {
                    text = factors[index].key;
                  }

                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    space: 16,
                    angle: 45,
                    child: Text(
                      text,
                      style: const TextStyle(
                        color: Color(0xff68737d),
                        fontWeight: FontWeight.bold,
                        fontSize: 12)));
                })),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 28,
                getTitlesWidget: (value, meta) {
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    space: 8,
                    child: Text(
                      '${value.toInt()}%',
                      style: const TextStyle(
                        color: Color(0xff67727d),
                        fontWeight: FontWeight.bold,
                        fontSize: 12)));
                }))),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: const Color(0xff37434d), width: 1)),
          barGroups: List.generate(factors.length, (index) {
            final factor = factors[index];
            return BarChartGroupData(
              x: index,
              barRods: [
                BarChartRodData(
                  toY: factor.value,
                  color: Colors.orange,
                  width: 22,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(6),
                    topRight: Radius.circular(6))),
              ]);
          }))));
  }

  /// بناء محدد الفترة
  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildPeriodButton(PredictionPeriod.threeMonths, '3 أشهر'),
          _buildPeriodButton(PredictionPeriod.sixMonths, '6 أشهر'),
          _buildPeriodButton(PredictionPeriod.year, 'سنة'),
          _buildPeriodButton(PredictionPeriod.twoYears, 'سنتين'),
          _buildPeriodButton(PredictionPeriod.fiveYears, '5 سنوات'),
        ]));
  }

  /// بناء زر الفترة
  Widget _buildPeriodButton(PredictionPeriod period, String label) {
    final isSelected = _selectedPeriod == period;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPeriod = period;
        });
        _loadPredictions();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color:
              isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(16)),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal))));
  }

  /// عرض مربع حوار التصفية
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية التنبؤات'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('المنطقة'),
              const SizedBox(height: 8),
              _buildAreaDropdown(),
              const SizedBox(height: 16),
              const Text('نوع العقار'),
              const SizedBox(height: 8),
              _buildPropertyTypeDropdown(),
            ])),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _loadPredictions();
            },
            child: const Text('تطبيق')),
        ]));
  }

  /// بناء قائمة منسدلة للمناطق الكويتية
  Widget _buildAreaDropdown() {
    // استخدام المحافظات من الملف الذكي
    final areas = KuwaitLocations.governorates;

    return DropdownButtonFormField<String>(
      value: _selectedArea,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8)),
      hint: const Text('اختر المنطقة'),
      isExpanded: true,
      onChanged: (value) {
        setState(() {
          _selectedArea = value;
        });
      },
      items: [
        const DropdownMenuItem<String>(
          value: null,
          child: Text('جميع المناطق')),
        ...areas.map(
          (area) => DropdownMenuItem<String>(value: area, child: Text(area))),
      ]);
  }

  /// بناء قائمة منسدلة لأنواع العقارات
  Widget _buildPropertyTypeDropdown() {
    final propertyTypes = [
      'شقة',
      'منزل',
      'بيت',
      'أرض',
      'عمارة',
      'مكتب',
      'محل تجاري',
      'مخزن',
    ];

    return DropdownButtonFormField<String>(
      value: _selectedPropertyType,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8)),
      hint: const Text('اختر نوع العقار'),
      isExpanded: true,
      onChanged: (value) {
        setState(() {
          _selectedPropertyType = value;
        });
      },
      items: [
        const DropdownMenuItem<String>(
          value: null,
          child: Text('جميع أنواع العقارات')),
        ...propertyTypes.map(
          (type) => DropdownMenuItem<String>(value: type, child: Text(type))),
      ]);
  }

  /// الحصول على تنبؤ لنوع معين
  MarketPrediction? _getPredictionForType(PredictionType type) {
    try {
      return _predictions.firstWhere((prediction) => prediction.type == type);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على نص نوع التنبؤ
  String _getPredictionTypeText(PredictionType type) {
    switch (type) {
      case PredictionType.salePrices:
        return 'أسعار البيع';
      case PredictionType.rentalPrices:
        return 'أسعار الإيجار';
      case PredictionType.supply:
        return 'العرض';
      case PredictionType.demand:
        return 'الطلب';
      case PredictionType.transactions:
        return 'الصفقات';
      case PredictionType.investmentReturn:
        return 'العائد الاستثماري';
      case PredictionType.growthRate:
        return 'معدل النمو';
      case PredictionType.occupancyRate:
        return 'معدل الإشغال';
    }
  }

  /// الحصول على نص مستوى الثقة
  String _getConfidenceLevelText(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.low:
        return 'منخفض';
      case ConfidenceLevel.medium:
        return 'متوسط';
      case ConfidenceLevel.high:
        return 'عالي';
      case ConfidenceLevel.veryHigh:
        return 'عالي جداً';
    }
  }

  /// الحصول على لون مستوى الثقة
  Color _getConfidenceLevelColor(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.low:
        return Colors.red;
      case ConfidenceLevel.medium:
        return Colors.orange;
      case ConfidenceLevel.high:
        return Colors.green;
      case ConfidenceLevel.veryHigh:
        return Colors.teal;
    }
  }
}

/// مكون خطأ مخصص
class ErrorWidgetCustom extends StatelessWidget {
  final String message;

  const ErrorWidgetCustom({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 60),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // Find the nearest MarketPredictionScreen state and call _loadPredictions
                final state = context
                    .findAncestorStateOfType<_MarketPredictionScreenState>();
                if (state != null) {
                  state._loadPredictions();
                }
              },
              child: const Text('إعادة المحاولة')),
          ])));
  }
}
