import 'dart:io';
import '../entities/marketing_campaign.dart';

/// واجهة مستودع الحملات التسويقية
abstract class MarketingRepository {
  /// إنشاء حملة تسويقية جديدة
  Future<String> createMarketingCampaign(MarketingCampaign campaign);
  
  /// تحديث حملة تسويقية موجودة
  Future<void> updateMarketingCampaign(MarketingCampaign campaign);
  
  /// حذف حملة تسويقية
  Future<void> deleteMarketingCampaign(String campaignId);
  
  /// الحصول على حملة تسويقية بواسطة المعرف
  Future<MarketingCampaign?> getMarketingCampaignById(String campaignId);
  
  /// الحصول على حملات الشركة التسويقية
  Future<List<MarketingCampaign>> getCompanyMarketingCampaigns(String companyId);
  
  /// الحصول على حملات الشركة التسويقية بالتحميل المتدرج
  /// [companyId] معرف الشركة
  /// [limit] عدد الحملات في كل صفحة
  /// [lastCampaignId] معرف آخر حملة تم تحميلها (للصفحات التالية)
  /// [type] نوع الحملات المطلوبة
  /// [status] حالة الحملات المطلوبة
  /// يعيد Map تحتوي على:
  /// - 'campaigns': قائمة الحملات
  /// - 'lastCampaignId': معرف آخر حملة (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من الحملات
  Future<Map<String, dynamic>> getCompanyMarketingCampaignsPaginated({
    required String companyId,
    int limit = 20,
    String? lastCampaignId,
    CampaignType? type,
    CampaignStatus? status,
  });
  
  /// الحصول على الحملات التسويقية النشطة
  Future<List<MarketingCampaign>> getActiveMarketingCampaigns(String companyId);
  
  /// تنشيط حملة تسويقية
  Future<void> activateMarketingCampaign(String campaignId, String updatedBy);
  
  /// إيقاف حملة تسويقية مؤقتاً
  Future<void> pauseMarketingCampaign(String campaignId, String updatedBy);
  
  /// إكمال حملة تسويقية
  Future<void> completeMarketingCampaign(String campaignId, String updatedBy);
  
  /// إلغاء حملة تسويقية
  Future<void> cancelMarketingCampaign(String campaignId, String updatedBy);
  
  /// إضافة مشاهدات للحملة
  Future<void> addCampaignViews(String campaignId, int count);
  
  /// إضافة نقرات للحملة
  Future<void> addCampaignClicks(String campaignId, int count);
  
  /// إضافة اتصالات للحملة
  Future<void> addCampaignContacts(String campaignId, int count);
  
  /// إضافة عملاء مكتسبين للحملة
  Future<void> addCampaignLeads(String campaignId, int count);
  
  /// إضافة مبيعات للحملة
  Future<void> addCampaignSales(String campaignId, int count, double amount);
  
  /// إضافة مبلغ مصروف للحملة
  Future<void> addCampaignSpentAmount(String campaignId, double amount);
  
  /// إضافة عضو فريق مسؤول عن الحملة
  Future<void> addCampaignTeamMember(String campaignId, String teamMemberId);
  
  /// إزالة عضو فريق مسؤول عن الحملة
  Future<void> removeCampaignTeamMember(String campaignId, String teamMemberId);
  
  /// تحميل صورة إعلان للحملة
  Future<String> uploadCampaignAdImage(String campaignId, File image);
  
  /// الحصول على إحصائيات الحملات التسويقية
  Future<Map<String, dynamic>> getMarketingCampaignsStatistics(String companyId);
  
  /// الحصول على أفضل الحملات التسويقية أداءً
  Future<List<MarketingCampaign>> getTopPerformingCampaigns(String companyId, {int limit = 5});
  
  /// الحصول على تقرير أداء الحملة التسويقية
  Future<Map<String, dynamic>> getCampaignPerformanceReport(String campaignId);
  
  /// الحصول على تقرير أداء الحملات التسويقية
  Future<Map<String, dynamic>> getMarketingPerformanceReport(String companyId);
  
  /// إنشاء حملة تسويقية تلقائية
  Future<String> createAutomaticCampaign({
    required String companyId,
    required String name,
    required CampaignType type,
    required double budget,
    required List<String> targetAreas,
    required List<String> targetPropertyTypes,
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// الحصول على اقتراحات للحملات التسويقية
  Future<List<Map<String, dynamic>>> getMarketingCampaignSuggestions(String companyId);
  
  /// الحصول على تحليل الجمهور المستهدف
  Future<Map<String, dynamic>> getTargetAudienceAnalysis(String companyId);
  
  /// الحصول على تحليل أداء القنوات التسويقية
  Future<Map<String, dynamic>> getMarketingChannelsAnalysis(String companyId);
}
