import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dart:io';

/// خدمة تتبع الإحالات
/// تستخدم لتتبع عمليات الإحالة والتحقق من إكمال عملية الإحالة
class ReferralTrackingService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// التحقق من وجود معلومات إحالة معلقة
  Future<Map<String, String?>> checkPendingReferral() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final referralCode = prefs.getString('pending_referral_code');
      final referralId = prefs.getString('pending_referral_id');

      return {
        'referralCode': referralCode,
        'referralId': referralId,
      };
    } catch (e) {
      return {
        'referralCode': null,
        'referralId': null,
      };
    }
  }

  /// تسجيل تثبيت التطبيق
  Future<void> logInstallation() async {
    try {
      final pendingReferral = await checkPendingReferral();
      final referralCode = pendingReferral['referralCode'];
      final referralId = pendingReferral['referralId'];

      if (referralCode == null) {
        // لا توجد إحالة معلقة
        return;
      }

      // جمع معلومات الجهاز
      final deviceInfo = await _getDeviceInfo();

      // جمع معلومات التطبيق
      final packageInfo = await PackageInfo.fromPlatform();

      // إنشاء سجل التثبيت
      final installationData = {
        'referralCode': referralCode,
        'referralId': referralId,
        'installationTime': FieldValue.serverTimestamp(),
        'deviceInfo': deviceInfo,
        'appInfo': {
          'appName': packageInfo.appName,
          'packageName': packageInfo.packageName,
          'version': packageInfo.version,
          'buildNumber': packageInfo.buildNumber,
        },
        'status': 'installed',
        'verified': false,
      };

      // حفظ سجل التثبيت في Firestore
      await _firestore
          .collection('referral_installations')
          .add(installationData);

      // تحديث إحصائيات الإحالة
      await _updateReferralStats(referralCode, 'installations');
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// تسجيل إكمال عملية الإحالة
  Future<void> completeReferral() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      final pendingReferral = await checkPendingReferral();
      final referralCode = pendingReferral['referralCode'];
      final referralId = pendingReferral['referralId'];

      if (referralCode == null) {
        // لا توجد إحالة معلقة
        return;
      }

      // البحث عن رمز الإحالة
      final query = await _firestore
          .collection('referrals')
          .where('referralCode', isEqualTo: referralCode)
          .limit(1)
          .get();

      if (query.docs.isEmpty) {
        return;
      }

      final referrerDoc = query.docs.first;
      final referrerId = referrerDoc.id;

      // التحقق من أن المستخدم لا يحاول استخدام رمز الإحالة الخاص به
      if (referrerId == user.uid) {
        return;
      }

      // البحث عن سجل التثبيت
      final installationQuery = await _firestore
          .collection('referral_installations')
          .where('referralCode', isEqualTo: referralCode)
          .where('referralId', isEqualTo: referralId)
          .limit(1)
          .get();

      if (installationQuery.docs.isNotEmpty) {
        // تحديث سجل التثبيت
        await installationQuery.docs.first.reference.update({
          'status': 'completed',
          'verified': true,
          'completionTime': FieldValue.serverTimestamp(),
          'userId': user.uid,
        });
      }

      // تحديث بيانات المستخدم المحال
      await _firestore.collection('users').doc(user.uid).update({
        'referredBy': referrerId,
        'referralCode': referralCode,
        'referralId': referralId,
        'referralCompleted': true,
        'referralCompletionTime': FieldValue.serverTimestamp(),
      });

      // تحديث إحصائيات الإحالة
      await _updateReferralStats(referralCode, 'completions');

      // مسح معلومات الإحالة المعلقة
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('pending_referral_code');
      await prefs.remove('pending_referral_id');
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// التحقق من صحة الإحالة
  Future<bool> verifyReferral(String referralCode, String? referralId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // البحث عن رمز الإحالة
      final query = await _firestore
          .collection('referrals')
          .where('referralCode', isEqualTo: referralCode)
          .limit(1)
          .get();

      if (query.docs.isEmpty) {
        return false;
      }

      final referrerDoc = query.docs.first;
      final referrerId = referrerDoc.id;

      // التحقق من أن المستخدم لا يحاول استخدام رمز الإحالة الخاص به
      if (referrerId == user.uid) {
        return false;
      }

      // التحقق من أن المستخدم لم يستخدم رمز إحالة من قبل
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data();
        if (userData != null && userData['referralCompleted'] == true) {
          return false;
        }
      }

      // التحقق من معرف الإحالة إذا كان متاحًا
      if (referralId != null) {
        final installationQuery = await _firestore
            .collection('referral_installations')
            .where('referralCode', isEqualTo: referralCode)
            .where('referralId', isEqualTo: referralId)
            .limit(1)
            .get();

        if (installationQuery.docs.isEmpty) {
          // لا يوجد سجل تثبيت مطابق
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على معلومات الجهاز
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return {
          'type': 'android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'androidId': androidInfo.id,
          'androidVersion': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return {
          'type': 'ios',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'identifierForVendor': iosInfo.identifierForVendor,
        };
      } else {
        return {
          'type': 'unknown',
        };
      }
    } catch (e) {
      return {
        'type': 'error',
        'error': e.toString(),
      };
    }
  }

  /// تحديث إحصائيات الإحالة
  Future<void> _updateReferralStats(
      String referralCode, String statType) async {
    try {
      // البحث عن رمز الإحالة
      final query = await _firestore
          .collection('referrals')
          .where('referralCode', isEqualTo: referralCode)
          .limit(1)
          .get();

      if (query.docs.isEmpty) {
        return;
      }

      final referrerDoc = query.docs.first;

      // تحديث الإحصائيات
      await referrerDoc.reference.update({
        'stats.$statType': FieldValue.increment(1),
        'stats.lastUpdated': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // تجاهل الخطأ
    }
  }
}
