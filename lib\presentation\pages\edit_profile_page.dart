import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kuwait_corners/core/services/loyalty_program_service.dart';
import 'package:kuwait_corners/domain/entities/user_profile.dart';
import 'package:kuwait_corners/domain/repositories/user_repository.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// صفحة تعديل الملف الشخصي
class EditProfilePage extends StatefulWidget {
  /// الملف الشخصي الحالي
  final UserProfile userProfile;

  const EditProfilePage({
    super.key,
    required this.userProfile,
  });

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _bioController = TextEditingController();
  final _emailController = TextEditingController();
  final _websiteController = TextEditingController();
  final _licenseNumberController = TextEditingController();
  final _experienceYearsController = TextEditingController();
  final _specialtyController = TextEditingController();
  final _companyNameController = TextEditingController();
  final _commercialRegisterController = TextEditingController();

  File? _profileImage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _bioController.dispose();
    _emailController.dispose();
    _websiteController.dispose();
    _licenseNumberController.dispose();
    _experienceYearsController.dispose();
    _specialtyController.dispose();
    _companyNameController.dispose();
    _commercialRegisterController.dispose();
    super.dispose();
  }

  /// تهيئة حقول النموذج بالبيانات الحالية
  void _initializeControllers() {
    _nameController.text = widget.userProfile.fullNameOrCompanyName;
    _phoneController.text = widget.userProfile.phone;
    _addressController.text = widget.userProfile.address;
    _emailController.text = widget.userProfile.email;

    // استرجاع البيانات الإضافية من Firestore
    _loadAdditionalData();
  }

  /// تحميل البيانات الإضافية من Firestore
  Future<void> _loadAdditionalData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(widget.userProfile.userId)
          .get();

      if (doc.exists) {
        final data = doc.data()!;

        setState(() {
          _bioController.text = data['bio'] ?? '';
          _websiteController.text = data['website'] ?? '';
          _licenseNumberController.text = data['licenseNumber'] ?? '';
          _experienceYearsController.text =
              data['experienceYears']?.toString() ?? '';
          _specialtyController.text = data['specialty'] ?? '';
          _companyNameController.text = data['companyName'] ?? '';
          _commercialRegisterController.text = data['commercialRegister'] ?? '';
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحميل البيانات: $e')));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _profileImage = File(pickedFile.path);
      });
    }
  }

  /// حفظ التغييرات
  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userRepository = RepositoryProvider.of<UserRepository>(context);
      final loyaltyService = LoyaltyProgramService();

      // تحميل الصورة الجديدة إذا تم اختيارها
      String profileImageUrl = widget.userProfile.profileImageUrl;
      if (_profileImage != null) {
        profileImageUrl = await userRepository.uploadProfileImage(
          widget.userProfile.userId,
          _profileImage!);
      }

      // تحديث الملف الشخصي الأساسي
      final updatedProfile = UserProfile(
        userId: widget.userProfile.userId,
        email: _emailController.text,
        fullNameOrCompanyName: _nameController.text,
        phone: _phoneController.text,
        address: _addressController.text,
        profileImageUrl: profileImageUrl,
        userType: widget.userProfile.userType);

      await userRepository.updateUserProfile(updatedProfile);

      // تحديث البيانات الإضافية حسب نوع المستخدم
      final additionalData = <String, dynamic>{
        'bio': _bioController.text,
        'profileCompleted': true, // تعليم الملف الشخصي كمكتمل
      };

      switch (widget.userProfile.userType) {
        case 'agent':
          additionalData.addAll({
            'licenseNumber': _licenseNumberController.text,
            'experienceYears':
                int.tryParse(_experienceYearsController.text) ?? 0,
            'specialty': _specialtyController.text,
            'website': _websiteController.text,
          });
          break;
        case 'company':
          additionalData.addAll({
            'companyName': _companyNameController.text,
            'commercialRegister': _commercialRegisterController.text,
            'website': _websiteController.text,
          });
          break;
        case 'owner':
          additionalData.addAll({
            'specialty': _specialtyController.text,
          });
          break;
      }

      // الحصول على الوثيقة الحالية للتحقق مما إذا كان الملف الشخصي مكتملاً من قبل
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(widget.userProfile.userId)
          .get();

      final bool wasProfileCompleted =
          userDoc.data()?['profileCompleted'] ?? false;

      // تحديث البيانات في Firestore
      await FirebaseFirestore.instance
          .collection('users')
          .doc(widget.userProfile.userId)
          .update(additionalData);

      // إضافة نقاط إذا كان هذا هو الإكمال الأول للملف الشخصي
      if (!wasProfileCompleted && _isProfileComplete()) {
        await loyaltyService.addPointsForCompletingProfile();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة 30 نقطة لإكمال الملف الشخصي!'),
              backgroundColor: Colors.green));
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ التغييرات بنجاح')));

        Navigator.pop(
            context, true); // العودة مع إشارة إلى أن التغييرات تمت بنجاح
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء حفظ التغييرات: $e')));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// التحقق مما إذا كان الملف الشخصي مكتملاً
  bool _isProfileComplete() {
    // التحقق من البيانات الأساسية
    if (_nameController.text.isEmpty ||
        _phoneController.text.isEmpty ||
        _addressController.text.isEmpty ||
        _bioController.text.isEmpty) {
      return false;
    }

    // التحقق من البيانات الإضافية حسب نوع المستخدم
    switch (widget.userProfile.userType) {
      case 'agent':
        return _licenseNumberController.text.isNotEmpty &&
            _specialtyController.text.isNotEmpty;
      case 'company':
        return _companyNameController.text.isNotEmpty &&
            _commercialRegisterController.text.isNotEmpty;
      case 'owner':
        return _specialtyController.text.isNotEmpty;
      default:
        return true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل الملف الشخصي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isLoading ? null : _saveChanges,
            tooltip: 'حفظ التغييرات'),
        ]),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // صورة الملف الشخصي
                    _buildProfileImageSection(),

                    const SizedBox(height: 24),

                    // البيانات الأساسية
                    _buildBasicInfoSection(),

                    const SizedBox(height: 24),

                    // البيانات الإضافية حسب نوع المستخدم
                    _buildAdditionalInfoSection(),

                    const SizedBox(height: 32),

                    // زر الحفظ
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveChanges,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12)),
                        child: const Text('حفظ التغييرات'))),
                  ]))));
  }

  /// بناء قسم صورة الملف الشخصي
  Widget _buildProfileImageSection() {
    return Column(
      children: [
        Stack(
          children: [
            CircleAvatar(
              radius: 60,
              backgroundColor: Colors.grey.shade200,
              backgroundImage: _profileImage != null
                  ? FileImage(_profileImage!)
                  : (widget.userProfile.profileImageUrl.isNotEmpty
                      ? NetworkImage(widget.userProfile.profileImageUrl)
                      : null) as ImageProvider?,
              child: widget.userProfile.profileImageUrl.isEmpty &&
                      _profileImage == null
                  ? const Icon(Icons.person, size: 60, color: Colors.grey)
                  : null),
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle),
                child: IconButton(
                  icon: const Icon(Icons.camera_alt, color: Colors.white),
                  onPressed: _pickImage,
                  tooltip: 'تغيير الصورة'))),
          ]),
        const SizedBox(height: 8),
        Text(
          'تغيير الصورة الشخصية',
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 14)),
      ]);
  }

  /// بناء قسم البيانات الأساسية
  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'البيانات الأساسية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),

            // الاسم
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'الاسم الكامل',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder()),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال الاسم';
                }
                return null;
              }),
            const SizedBox(height: 16),

            // البريد الإلكتروني
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                prefixIcon: Icon(Icons.email),
                border: OutlineInputBorder()),
              readOnly: true, // لا يمكن تغيير البريد الإلكتروني
              enabled: false),
            const SizedBox(height: 16),

            // رقم الهاتف
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                prefixIcon: Icon(Icons.phone),
                border: OutlineInputBorder()),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال رقم الهاتف';
                }
                return null;
              }),
            const SizedBox(height: 16),

            // العنوان
            TextFormField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'العنوان',
                prefixIcon: Icon(Icons.location_on),
                border: OutlineInputBorder()),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال العنوان';
                }
                return null;
              }),
            const SizedBox(height: 16),

            // نبذة شخصية
            TextFormField(
              controller: _bioController,
              decoration: const InputDecoration(
                labelText: 'نبذة شخصية',
                prefixIcon: Icon(Icons.info),
                border: OutlineInputBorder()),
              maxLines: 3),
          ])));
  }

  /// بناء قسم البيانات الإضافية حسب نوع المستخدم
  Widget _buildAdditionalInfoSection() {
    switch (widget.userProfile.userType) {
      case 'agent':
        return _buildAgentInfoSection();
      case 'company':
        return _buildCompanyInfoSection();
      case 'owner':
        return _buildOwnerInfoSection();
      default:
        return const SizedBox.shrink();
    }
  }

  /// بناء قسم بيانات الوكيل العقاري
  Widget _buildAgentInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'بيانات الوكيل العقاري',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),

            // رقم الترخيص
            TextFormField(
              controller: _licenseNumberController,
              decoration: const InputDecoration(
                labelText: 'رقم الترخيص',
                prefixIcon: Icon(Icons.badge),
                border: OutlineInputBorder()),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال رقم الترخيص';
                }
                return null;
              }),
            const SizedBox(height: 16),

            // سنوات الخبرة
            TextFormField(
              controller: _experienceYearsController,
              decoration: const InputDecoration(
                labelText: 'سنوات الخبرة',
                prefixIcon: Icon(Icons.work_history),
                border: OutlineInputBorder()),
              keyboardType: TextInputType.number),
            const SizedBox(height: 16),

            // التخصص
            TextFormField(
              controller: _specialtyController,
              decoration: const InputDecoration(
                labelText: 'التخصص',
                prefixIcon: Icon(Icons.category),
                border: OutlineInputBorder())),
            const SizedBox(height: 16),

            // الموقع الإلكتروني
            TextFormField(
              controller: _websiteController,
              decoration: const InputDecoration(
                labelText: 'الموقع الإلكتروني',
                prefixIcon: Icon(Icons.language),
                border: OutlineInputBorder()),
              keyboardType: TextInputType.url),
          ])));
  }

  /// بناء قسم بيانات الشركة العقارية
  Widget _buildCompanyInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'بيانات الشركة العقارية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),

            // اسم الشركة
            TextFormField(
              controller: _companyNameController,
              decoration: const InputDecoration(
                labelText: 'اسم الشركة',
                prefixIcon: Icon(Icons.business),
                border: OutlineInputBorder()),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال اسم الشركة';
                }
                return null;
              }),
            const SizedBox(height: 16),

            // رقم السجل التجاري
            TextFormField(
              controller: _commercialRegisterController,
              decoration: const InputDecoration(
                labelText: 'رقم السجل التجاري',
                prefixIcon: Icon(Icons.numbers),
                border: OutlineInputBorder()),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال رقم السجل التجاري';
                }
                return null;
              }),
            const SizedBox(height: 16),

            // الموقع الإلكتروني
            TextFormField(
              controller: _websiteController,
              decoration: const InputDecoration(
                labelText: 'الموقع الإلكتروني',
                prefixIcon: Icon(Icons.language),
                border: OutlineInputBorder()),
              keyboardType: TextInputType.url),
          ])));
  }

  /// بناء قسم بيانات مالك العقار
  Widget _buildOwnerInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'بيانات مالك العقار',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),

            // نوع العقارات
            TextFormField(
              controller: _specialtyController,
              decoration: const InputDecoration(
                labelText: 'نوع العقارات',
                prefixIcon: Icon(Icons.home),
                border: OutlineInputBorder(),
                hintText: 'مثال: سكني، تجاري، استثماري')),
          ])));
  }
}
