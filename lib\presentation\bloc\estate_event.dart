import '../../domain/entities/estate.dart';

/// Base class for all estate-related events.
abstract class EstateEvent {}

/// Event to fetch all estates (legacy method).
class FetchEstates extends EstateEvent {}

/// Event to fetch estates with pagination.
class FetchPaginatedEstates extends EstateEvent {
  final int limit;
  final String? lastDocumentId;
  final bool refresh; // إذا كانت true، سيتم تحميل الصفحة الأولى من جديد
  final int page;
  final int pageSize;
  final String sortBy;
  final bool sortAscending;
  final Map<String, dynamic> filters;
  final String? searchQuery; // إضافة معلمة للبحث النصي

  FetchPaginatedEstates({
    this.limit = 10,
    this.lastDocumentId,
    this.refresh = false,
    this.page = 1,
    this.pageSize = 10,
    this.sortBy = "createdAt",
    this.sortAscending = false,
    this.filters = const {},
    this.searchQuery, // إضافة معلمة للبحث النصي
  });

  List<Object?> get props => [
        limit,
        lastDocumentId,
        refresh,
        page,
        pageSize,
        sortBy,
        sortAscending,
        filters,
        searchQuery,
      ];
}

/// Event to create a new estate.
/// يحمل الحدث كائن [Estate] ومعرّف المستخدم (userId) الخاص بالمُعلِن.
class CreateEstateEvent extends EstateEvent {
  final Estate estate;
  final String userId;

  CreateEstateEvent(this.estate, this.userId);

  List<Object?> get props => [estate, userId];
}

/// Event to update an existing estate.
class UpdateEstateEvent extends EstateEvent {
  final Estate estate;
  UpdateEstateEvent(this.estate);

  List<Object?> get props => [estate];
}

/// Event to delete an estate.
class DeleteEstateEvent extends EstateEvent {
  final String id;
  DeleteEstateEvent(this.id);

  List<Object?> get props => [id];
}

/// Event to load cached estates data.
class LoadCachedEstatesEvent extends EstateEvent {
  final List<Estate> estates;

  LoadCachedEstatesEvent(this.estates);

  List<Object?> get props => [estates];
}

/// Event to copy an existing estate.
/// Used when an investor wants to copy a property from an owner.
class CopyEstateEvent extends EstateEvent {
  final String originalEstateId;
  final String investorId;

  CopyEstateEvent(this.originalEstateId, this.investorId);

  List<Object?> get props => [originalEstateId, investorId];
}
