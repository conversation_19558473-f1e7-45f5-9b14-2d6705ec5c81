{"indexes": [{"collectionGroup": "estates", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPaymentVerified", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "estates", "queryScope": "COLLECTION", "fields": [{"fieldPath": "mainCategory", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "estates", "queryScope": "COLLECTION", "fields": [{"fieldPath": "area", "order": "ASCENDING"}, {"fieldPath": "propertyType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "estates", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "estates", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isFeatured", "order": "ASCENDING"}, {"fieldPath": "isPinned", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "property_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "property_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "property_offers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "requestId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "conversations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participantIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "conversationId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "forum_topics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "categoryId", "order": "ASCENDING"}, {"fieldPath": "isPinned", "order": "DESCENDING"}, {"fieldPath": "lastActivityAt", "order": "DESCENDING"}]}, {"collectionGroup": "forum_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "topicId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "isVerified", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "projects", "queryScope": "COLLECTION", "fields": [{"fieldPath": "managerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "projects", "queryScope": "COLLECTION", "fields": [{"fieldPath": "memberIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "projectId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "assignedTo", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "priority", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isRead", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "analytics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "estates", "fieldPath": "searchKeywords", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}, {"collectionGroup": "users", "fieldPath": "searchableFields", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}]}