import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/user_statistics_model.dart';

/// أنواع بطاقات الإحصائيات
enum StatisticsCardType {
  /// بطاقة المستخدم
  user,

  /// بطاقة المستوى
  level,

  /// بطاقة النشاط
  activity,

  /// بطاقة الإنجازات
  achievements,
}

/// بطاقة إحصائيات المنتدى الحديثة
class ModernStatisticsCard extends StatelessWidget {
  /// إحصائيات المستخدم
  final UserStatisticsModel? statistics;

  /// ما إذا كان يتم تحميل البيانات
  final bool isLoading;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;

  /// نوع البطاقة
  final StatisticsCardType cardType;

  const ModernStatisticsCard({
    super.key,
    this.statistics,
    this.isLoading = false,
    this.onTap,
    this.cardType = StatisticsCardType.user,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: isLoading
            ? _buildLoadingState()
            : statistics == null
                ? _buildEmptyState()
                : _buildContent()));
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Container(
      height: 150,
      padding: const EdgeInsets.all(16),
      child: Center(
        child: CircularProgressIndicator(
          color: AppColors.primary)));
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Container(
      height: 150,
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getCardIcon(),
              size: 48,
              color: Colors.grey.shade400),
            const SizedBox(height: 8),
            Text(
              'لا توجد إحصائيات',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600)),
          ])));
  }

  /// بناء محتوى البطاقة
  Widget _buildContent() {
    switch (cardType) {
      case StatisticsCardType.user:
        return _buildUserCard();
      case StatisticsCardType.level:
        return _buildLevelCard();
      case StatisticsCardType.activity:
        return _buildActivityCard();
      case StatisticsCardType.achievements:
        return _buildAchievementsCard();
    }
  }

  /// بناء بطاقة المستخدم
  Widget _buildUserCard() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // صورة المستخدم
              CircleAvatar(
                radius: 24,
                backgroundColor: AppColors.primary.withOpacity(0.1),
                backgroundImage: statistics?.userImage != null
                    ? CachedNetworkImageProvider(statistics!.userImage!)
                    : null,
                child: statistics?.userImage == null
                    ? Text(
                        statistics?.userName.isNotEmpty == true
                            ? statistics!.userName[0].toUpperCase()
                            : '?',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 18))
                    : null),
              const SizedBox(width: 16),

              // معلومات المستخدم
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      statistics?.userName ?? 'مستخدم',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16)),
                    const SizedBox(height: 4),
                    Text(
                      'عضو منذ ${_formatDate(statistics?.joinDate)}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12)),
                  ])),

              // المستوى
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12)),
                child: Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: AppColors.primary,
                      size: 16),
                    const SizedBox(width: 4),
                    Text(
                      'المستوى ${statistics?.level ?? 0}',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 12)),
                  ])),
            ]),
          const SizedBox(height: 16),

          // إحصائيات المستخدم
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                'المواضيع',
                '${statistics?.topicsCount ?? 0}',
                Icons.topic),
              _buildStatItem(
                'المشاركات',
                '${statistics?.postsCount ?? 0}',
                Icons.forum),
              _buildStatItem(
                'الإعجابات',
                '${statistics?.likesCount ?? 0}',
                Icons.favorite),
              _buildStatItem(
                'النقاط',
                '${statistics?.points ?? 0}',
                Icons.emoji_events),
            ]),
        ]));
  }

  /// بناء بطاقة المستوى
  Widget _buildLevelCard() {
    final int level = statistics?.level ?? 0;
    final int points = statistics?.points ?? 0;
    final int nextLevelPoints = (level + 1) * 100; // مثال بسيط لحساب النقاط المطلوبة للمستوى التالي
    final double progress = points / nextLevelPoints;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.emoji_events,
                color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'المستوى والنقاط',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16)),
            ]),
          const SizedBox(height: 16),

          // المستوى الحالي
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المستوى الحالي',
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14)),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12)),
                child: Text(
                  '$level',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 14))),
            ]),
          const SizedBox(height: 8),

          // النقاط
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'النقاط',
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14)),
              Text(
                '$points / $nextLevelPoints',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14)),
            ]),
          const SizedBox(height: 8),

          // شريط التقدم
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: LinearProgressIndicator(
              value: progress.clamp(0.0, 1.0),
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              minHeight: 8)),
          const SizedBox(height: 16),

          // معلومات المستوى التالي
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12)),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.grey.shade600,
                  size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'تحتاج إلى ${nextLevelPoints - points} نقطة للوصول إلى المستوى ${level + 1}',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 12))),
              ])),
        ]));
  }

  /// بناء بطاقة النشاط
  Widget _buildActivityCard() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.timeline,
                color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'النشاط',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16)),
            ]),
          const SizedBox(height: 16),

          // معدلات النشاط
          _buildActivityRateItem(
            'النشاط اليومي',
            statistics?.dailyActivityRate ?? 0,
            Icons.today),
          const SizedBox(height: 8),
          _buildActivityRateItem(
            'النشاط الأسبوعي',
            statistics?.weeklyActivityRate ?? 0,
            Icons.view_week),
          const SizedBox(height: 8),
          _buildActivityRateItem(
            'النشاط الشهري',
            statistics?.monthlyActivityRate ?? 0,
            Icons.calendar_month),
          const SizedBox(height: 16),

          // آخر نشاط
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12)),
            child: Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: Colors.grey.shade600,
                  size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'آخر نشاط: ${_formatDate(statistics?.lastActivityDate)}',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 12))),
              ])),
        ]));
  }

  /// بناء بطاقة الإنجازات
  Widget _buildAchievementsCard() {
    final achievements = statistics?.achievements ?? [];

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.military_tech,
                color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'الإنجازات',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16)),
              const Spacer(),
              Text(
                '${achievements.length} / 10',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12)),
            ]),
          const SizedBox(height: 16),

          // قائمة الإنجازات
          if (achievements.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.emoji_events_outlined,
                    size: 48,
                    color: Colors.grey.shade400),
                  const SizedBox(height: 8),
                  Text(
                    'لا توجد إنجازات بعد',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600)),
                ]))
          else
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: achievements.map((achievement) {
                return Tooltip(
                  message: achievement.description,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getAchievementColor(achievement.type).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8)),
                    child: Column(
                      children: [
                        Icon(
                          _getAchievementIcon(achievement.type),
                          color: _getAchievementColor(achievement.type),
                          size: 24),
                        const SizedBox(height: 4),
                        Text(
                          achievement.name,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: _getAchievementColor(achievement.type))),
                      ])));
              }).toList()),
        ]));
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            shape: BoxShape.circle),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 20)),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16)),
        const SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 12)),
      ]);
  }

  /// بناء عنصر معدل النشاط
  Widget _buildActivityRateItem(String label, double rate, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.grey.shade600,
          size: 16),
        const SizedBox(width: 8),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey.shade700,
            fontSize: 14)),
        const Spacer(),
        _buildActivityRateIndicator(rate),
      ]);
  }

  /// بناء مؤشر معدل النشاط
  Widget _buildActivityRateIndicator(double rate) {
    final int filledStars = rate.floor().clamp(0, 5);
    final bool hasHalfStar = (rate - filledStars) >= 0.5;

    return Row(
      children: List.generate(5, (index) {
        if (index < filledStars) {
          return Icon(
            Icons.star,
            color: AppColors.warning,
            size: 16);
        } else if (index == filledStars && hasHalfStar) {
          return Icon(
            Icons.star_half,
            color: AppColors.warning,
            size: 16);
        } else {
          return Icon(
            Icons.star_border,
            color: Colors.grey.shade400,
            size: 16);
        }
      }));
  }

  /// الحصول على أيقونة البطاقة
  IconData _getCardIcon() {
    switch (cardType) {
      case StatisticsCardType.user:
        return Icons.person;
      case StatisticsCardType.level:
        return Icons.emoji_events;
      case StatisticsCardType.activity:
        return Icons.timeline;
      case StatisticsCardType.achievements:
        return Icons.military_tech;
    }
  }

  /// الحصول على أيقونة الإنجاز
  IconData _getAchievementIcon(String type) {
    switch (type) {
      case 'topic':
        return Icons.topic;
      case 'post':
        return Icons.forum;
      case 'like':
        return Icons.favorite;
      case 'bookmark':
        return Icons.bookmark;
      case 'share':
        return Icons.share;
      case 'login':
        return Icons.login;
      case 'streak':
        return Icons.local_fire_department;
      case 'solved':
        return Icons.check_circle;
      case 'featured':
        return Icons.star;
      case 'level':
        return Icons.emoji_events;
      default:
        return Icons.emoji_events;
    }
  }

  /// الحصول على لون الإنجاز
  Color _getAchievementColor(String type) {
    switch (type) {
      case 'topic':
        return AppColors.primary; // تغيير من الأزرق إلى الأخضر
      case 'post':
        return AppColors.success; // استخدام لون النجاح الأخضر
      case 'like':
        return Colors.red;
      case 'bookmark':
        return AppColors.primaryDark; // تغيير إلى درجة أغمق من الأخضر
      case 'share':
        return Colors.orange;
      case 'login':
        return AppColors.primaryLight; // تغيير إلى درجة أفتح من الأخضر
      case 'streak':
        return Colors.deepOrange;
      case 'solved':
        return AppColors.success; // استخدام لون النجاح الأخضر
      case 'featured':
        return Colors.amber;
      case 'level':
        return AppColors.primary;
      default:
        return AppColors.primary;
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime? date) {
    if (date == null) {
      return 'غير معروف';
    }

    return '${date.day}/${date.month}/${date.year}';
  }
}
