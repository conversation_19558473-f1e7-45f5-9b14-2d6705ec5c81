import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Base event class for authentication events.
/// Extends [Equatable] to facilitate value comparisons between events.
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Event triggered when a user requests to log in.
/// It contains the user's email, password, and an optional flag for "remember me".
class LoginRequested extends AuthEvent {
  final String email;
  final String password;
  final bool rememberMe;

  const LoginRequested({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  @override
  List<Object> get props => [email, password, rememberMe];
}

/// Event triggered when a user requests to sign up.
/// Expanded to include all fields relevant for different user types such as 'user', 'owner', or 'company'.
class SignupRequested extends AuthEvent {
  final String email;
  final String password;
  final String confirmPassword;

  /// User type can be 'user' (seeker), 'owner' (property owner), or 'company' (real estate company).
  final String userType;

  /// Full name or company name.
  final String fullNameOrCompanyName;

  /// Phone number (typically for a property seeker).
  final String? phone;

  /// Address (applicable for both residential and company users).
  final String? address;

  /// Postal code (should be specific to the region, e.g., Kuwait).
  final String? postalCode;

  /// Path or URL to an uploaded document (e.g., ownership or business document).
  final String? docPath;

  /// Latitude coordinate.
  final double? lat;

  /// Longitude coordinate.
  final double? lng;

  /// Referral code (optional) used during signup.
  final String? referralCode;

  /// Referral ID (optional) used for tracking referrals.
  final String? referralId;

  const SignupRequested({
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.userType,
    required this.fullNameOrCompanyName,
    this.phone,
    this.address,
    this.postalCode,
    this.docPath,
    this.lat,
    this.lng,
    this.referralCode,
    this.referralId,
  });

  @override
  List<Object?> get props => [
        email,
        password,
        confirmPassword,
        userType,
        fullNameOrCompanyName,
        phone,
        address,
        postalCode,
        docPath,
        lat,
        lng,
        referralCode,
        referralId,
      ];
}

/// Event triggered when a user requests a password reset.
/// Contains the user's email to send the reset instructions.
class PasswordResetRequested extends AuthEvent {
  final String email;

  const PasswordResetRequested({required this.email});

  @override
  List<Object> get props => [email];
}

/// Event triggered when a user requests to resend email verification.
/// Contains the user's email to resend verification instructions.
class ResendVerificationRequested extends AuthEvent {
  final String email;

  const ResendVerificationRequested({required this.email});

  @override
  List<Object> get props => [email];
}

/// Event triggered when a user requests to log out.
class LogoutRequested extends AuthEvent {}

/// Event triggered when a user is successfully authenticated.
/// Contains the authenticated user and their user type.
class LoginSuccessEvent extends AuthEvent {
  final User user;
  final String userType;

  const LoginSuccessEvent(this.user, this.userType);

  @override
  List<Object> get props => [user, userType];
}
