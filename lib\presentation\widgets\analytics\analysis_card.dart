import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../domain/entities/market_analysis.dart';
import '../../../domain/enums/analysis_type.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/cairo_text_styles.dart';

/// بطاقة تحليل
class AnalysisCard extends StatelessWidget {
  final MarketAnalysis analysis;
  final VoidCallback? onTap;

  /// إنشاء بطاقة تحليل
  const AnalysisCard({
    super.key,
    required this.analysis,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان التحليل ونوعه
              Row(
                children: [
                  _buildTypeIcon(),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      analysis.title,
                      style: CairoTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold))),
                ]),
              const SizedBox(height: 8),

              // وصف التحليل
              Text(
                analysis.description,
                style: CairoTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600]),
                maxLines: 2,
                overflow: TextOverflow.ellipsis),
              const SizedBox(height: 12),

              // معلومات إضافية
              Row(
                children: [
                  // المنطقة
                  // المنطقة
                  _buildInfoChip(
                    Icons.location_on_outlined,
                    analysis.location),

                  // نوع العقار
                  _buildInfoChip(
                    Icons.home_outlined,
                    analysis.propertyType),

                  // الفترة
                  _buildInfoChip(
                    Icons.date_range_outlined,
                    _getFormattedDate(analysis.analysisDate)),
                ]),
              const SizedBox(height: 12),

              // الإحصائيات
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // نسبة التغير في الأسعار
                  _buildStatItem(
                    'التغير',
                    '${analysis.priceChangePercentage.toStringAsFixed(1)}%',
                    analysis.priceChangePercentage >= 0
                        ? Colors.green
                        : Colors.red),

                  // متوسط السعر
                  _buildStatItem(
                    'متوسط السعر',
                    '${NumberFormat('#,###').format(analysis.averagePricePerSquareMeter.round())} ريال/م²',
                    AppColors.primary),

                  // تاريخ التحليل
                  _buildStatItem(
                    'التاريخ',
                    _getFormattedDate(analysis.analysisDate),
                    Colors.grey),
                ]),
            ]))));
  }

  /// بناء أيقونة نوع التحليل
  Widget _buildTypeIcon() {
    IconData icon;
    Color color;

    switch (analysis.type) {
      case AnalysisType.price:
        icon = Icons.monetization_on_outlined;
        color = Colors.amber;
        break;
      case AnalysisType.supplyDemand:
        icon = Icons.compare_arrows;
        color = AppColors.primary;
        break;
      case AnalysisType.areas:
        icon = Icons.location_city_outlined;
        color = AppColors.success;
        break;
      case AnalysisType.investmentReturn:
        icon = Icons.trending_up;
        color = AppColors.primaryDark;
        break;
      case AnalysisType.investors:
        icon = Icons.people;
        color = AppColors.primaryLight;
        break;
      case AnalysisType.developers:
        icon = Icons.business;
        color = AppColors.secondary;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(8)),
      child: Icon(
        icon,
        color: color,
        size: 20));
  }

  /// بناء شريحة معلومات
  Widget _buildInfoChip(IconData icon, String label) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Chip(
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
        backgroundColor: Colors.grey[200],
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: Colors.grey[600]),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[800])),
          ])));
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: CairoTextStyles.bodySmall.copyWith(
            color: Colors.grey[600])),
        const SizedBox(height: 4),
        Text(
          value,
          style: CairoTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color)),
      ]);
  }

  // تم حذف الدالة _getPeriodText لأنها غير مستخدمة

  /// الحصول على التاريخ المنسق
  String _getFormattedDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 1) {
      if (difference.inHours < 1) {
        return 'منذ ${difference.inMinutes} دقيقة';
      }
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 30) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return DateFormat('yyyy/MM/dd').format(date);
    }
  }
}
