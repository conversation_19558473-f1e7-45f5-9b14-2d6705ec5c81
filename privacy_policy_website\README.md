# سياسة الخصوصية - تطبيق Krea

## 📋 نظرة عامة
هذا المجلد يحتوي على صفحة ويب لسياسة الخصوصية الخاصة بتطبيق Krea، مصممة للنشر على Firebase Hosting.

## 🚀 خطوات النشر على Firebase Hosting

### 1. تثبيت Firebase CLI
```bash
npm install -g firebase-tools
```

### 2. تسجيل الدخول إلى Firebase
```bash
firebase login
```

### 3. إنشاء مشروع Firebase جديد (إذا لم يكن موجوداً)
- اذهب إلى [Firebase Console](https://console.firebase.google.com/)
- انقر على "Add project"
- اختر اسم المشروع (مثل: krea-privacy-policy)
- اتبع الخطوات لإنشاء المشروع

### 4. ربط المجلد بمشروع Firebase
```bash
cd privacy_policy_website
firebase init hosting
```

اختر:
- Use an existing project
- اختر المشروع الذي أنشأته
- Public directory: اتركه كما هو (.)
- Configure as single-page app: No
- Set up automatic builds: No

### 5. نشر الموقع
```bash
firebase deploy --only hosting
```

### 6. الحصول على الرابط
بعد النشر الناجح، ستحصل على رابط مثل:
```
https://krea-privacy-policy.web.app
```

## 🔗 استخدام الرابط في Google Play Console

استخدم الرابط الذي حصلت عليه في خانة "Privacy Policy URL" في Google Play Console.

## 📱 المعاينة المحلية

لمعاينة الصفحة محلياً قبل النشر:
```bash
firebase serve --only hosting
```

ثم افتح: http://localhost:5000

## 🎨 المميزات

- ✅ تصميم عصري ومتجاوب
- ✅ دعم كامل للغة العربية (RTL)
- ✅ خط Cairo من Google Fonts
- ✅ ألوان متناسقة مع تطبيق Krea
- ✅ محتوى شامل ومفصل
- ✅ متوافق مع متطلبات Google Play
- ✅ سرعة تحميل عالية
- ✅ SEO محسن

## 📞 معلومات الاتصال

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +965 9929 8821
- **العنوان**: الكويت

## 🔄 التحديثات

لتحديث سياسة الخصوصية:
1. عدّل ملف `index.html`
2. نفذ الأمر: `firebase deploy --only hosting`

---

© 2025 Krea. جميع الحقوق محفوظة.
