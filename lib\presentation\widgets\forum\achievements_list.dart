import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/user_statistics_model.dart';

/// قائمة الإنجازات
class AchievementsList extends StatelessWidget {
  /// قائمة الإنجازات
  final List<AchievementModel> achievements;

  /// ما إذا كانت القائمة مصغرة
  final bool isCompact;

  /// دالة يتم استدعاؤها عند النقر على إنجاز
  final Function(AchievementModel)? onAchievementTap;

  const AchievementsList({
    super.key,
    required this.achievements,
    this.isCompact = false,
    this.onAchievementTap,
  });

  @override
  Widget build(BuildContext context) {
    if (achievements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: 48,
              color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد إنجازات بعد',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16)),
            const SizedBox(height: 8),
            Text(
              'شارك في المنتدى للحصول على إنجازات',
              style: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 14),
              textAlign: TextAlign.center),
          ]));
    }

    // فرز الإنجازات: المكتملة أولاً، ثم حسب النسبة المئوية للإكمال
    final sortedAchievements = List<AchievementModel>.from(achievements)
      ..sort((a, b) {
        if (a.isCompleted && !b.isCompleted) return -1;
        if (!a.isCompleted && b.isCompleted) return 1;
        
        final aPercentage = a.value / a.maxValue;
        final bPercentage = b.value / b.maxValue;
        return bPercentage.compareTo(aPercentage);
      });

    return isCompact
        ? _buildCompactList(sortedAchievements)
        : _buildFullList(sortedAchievements);
  }

  /// بناء القائمة المصغرة
  Widget _buildCompactList(List<AchievementModel> sortedAchievements) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.8,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8),
      itemCount: sortedAchievements.length,
      itemBuilder: (context, index) {
        final achievement = sortedAchievements[index];
        return _buildCompactAchievementCard(achievement);
      });
  }

  /// بناء القائمة الكاملة
  Widget _buildFullList(List<AchievementModel> sortedAchievements) {
    return ListView.builder(
      itemCount: sortedAchievements.length,
      itemBuilder: (context, index) {
        final achievement = sortedAchievements[index];
        return _buildFullAchievementCard(achievement);
      });
  }

  /// بناء بطاقة إنجاز مصغرة
  Widget _buildCompactAchievementCard(AchievementModel achievement) {
    final progress = achievement.value / achievement.maxValue;
    
    return InkWell(
      onTap: onAchievementTap != null ? () => onAchievementTap!(achievement) : null,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: achievement.isCompleted
                ? AppColors.primary
                : Colors.grey.shade300)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الإنجاز
            CircularPercentIndicator(
              radius: 30,
              lineWidth: 5,
              percent: progress,
              center: achievement.imageUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(25),
                      child: Image.network(
                        achievement.imageUrl!,
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover))
                  : Icon(
                      Icons.emoji_events,
                      color: achievement.isCompleted
                          ? Colors.amber
                          : Colors.grey.shade400,
                      size: 30),
              progressColor: achievement.isCompleted
                  ? AppColors.primary
                  : Colors.grey.shade400,
              backgroundColor: Colors.grey.shade200),
            const SizedBox(height: 8),
            
            // اسم الإنجاز
            Text(
              achievement.name,
              style: TextStyle(
                fontWeight: achievement.isCompleted
                    ? FontWeight.bold
                    : FontWeight.normal,
                color: achievement.isCompleted
                    ? AppColors.primary
                    : Colors.grey.shade700,
                fontSize: 12),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis),
            
            // التقدم
            Text(
              '${achievement.value}/${achievement.maxValue}',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 10)),
          ])));
  }

  /// بناء بطاقة إنجاز كاملة
  Widget _buildFullAchievementCard(AchievementModel achievement) {
    final progress = achievement.value / achievement.maxValue;
    final percentage = (progress * 100).round();
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: achievement.isCompleted
              ? AppColors.primary
              : Colors.transparent)),
      child: InkWell(
        onTap: onAchievementTap != null ? () => onAchievementTap!(achievement) : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة الإنجاز
              CircularPercentIndicator(
                radius: 30,
                lineWidth: 5,
                percent: progress,
                center: achievement.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(25),
                        child: Image.network(
                          achievement.imageUrl!,
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover))
                    : Icon(
                        Icons.emoji_events,
                        color: achievement.isCompleted
                            ? Colors.amber
                            : Colors.grey.shade400,
                        size: 30),
                progressColor: achievement.isCompleted
                    ? AppColors.primary
                    : Colors.grey.shade400,
                backgroundColor: Colors.grey.shade200),
              const SizedBox(width: 16),
              
              // معلومات الإنجاز
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // اسم الإنجاز
                        Text(
                          achievement.name,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: achievement.isCompleted
                                ? AppColors.primary
                                : Colors.black87,
                            fontSize: 16)),
                        
                        // نسبة الإكمال
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4),
                          decoration: BoxDecoration(
                            color: achievement.isCompleted
                                ? AppColors.primary
                                : Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(12)),
                          child: Text(
                            achievement.isCompleted
                                ? 'مكتمل'
                                : '$percentage%',
                            style: TextStyle(
                              color: achievement.isCompleted
                                  ? Colors.white
                                  : Colors.grey.shade700,
                              fontSize: 12,
                              fontWeight: FontWeight.bold))),
                      ]),
                    const SizedBox(height: 4),
                    
                    // وصف الإنجاز
                    Text(
                      achievement.description,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis),
                    const SizedBox(height: 8),
                    
                    // شريط التقدم
                    LinearPercentIndicator(
                      lineHeight: 8,
                      percent: progress,
                      progressColor: achievement.isCompleted
                          ? AppColors.primary
                          : Colors.grey.shade400,
                      backgroundColor: Colors.grey.shade200,
                      barRadius: const Radius.circular(4),
                      padding: EdgeInsets.zero,
                      trailing: Text(
                        '${achievement.value}/${achievement.maxValue}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12)),
                      animation: true,
                      animationDuration: 1000),
                    
                    // تاريخ الإكمال
                    if (achievement.isCompleted) ...[
                      const SizedBox(height: 4),
                      Text(
                        'تم الإكمال في ${_formatDate(achievement.earnedAt)}',
                        style: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 12,
                          fontStyle: FontStyle.italic)),
                    ],
                  ])),
            ]))));
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }
}
