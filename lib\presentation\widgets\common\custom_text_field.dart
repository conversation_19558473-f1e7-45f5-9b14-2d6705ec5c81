import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/theme/app_colors.dart';

/// حقل نص مخصص للتطبيق
class CustomTextField extends StatefulWidget {
  /// متحكم النص
  final TextEditingController? controller;

  /// نوع لوحة المفاتيح
  final TextInputType? keyboardType;

  /// نص التسمية
  final String? labelText;

  /// نص التلميح
  final String? hintText;

  /// نص المساعدة
  final String? helperText;

  /// نص الخطأ
  final String? errorText;

  /// أيقونة البداية
  final Widget? prefixIcon;

  /// أيقونة النهاية
  final Widget? suffixIcon;

  /// دالة التحقق من الصحة
  final String? Function(String?)? validator;

  /// دالة يتم استدعاؤها عند تغيير النص
  final void Function(String)? onChanged;

  /// دالة يتم استدعاؤها عند الإرسال
  final void Function(String)? onSubmitted;

  /// دالة يتم استدعاؤها عند النقر
  final VoidCallback? onTap;

  /// ما إذا كان الحقل للقراءة فقط
  final bool readOnly;

  /// ما إذا كان الحقل معطل
  final bool enabled;

  /// ما إذا كان الحقل مطلوبًا
  final bool required;

  /// ما إذا كان الحقل لكلمة المرور
  final bool isPassword;

  /// الحد الأقصى لعدد الأحرف
  final int? maxLength;

  /// الحد الأقصى لعدد الأسطر
  final int? maxLines;

  /// الحد الأدنى لعدد الأسطر
  final int? minLines;

  /// قائمة منسقات الإدخال
  final List<TextInputFormatter>? inputFormatters;

  /// لون الخلفية
  final Color? fillColor;

  /// ما إذا كان يجب ملء الخلفية
  final bool filled;

  /// نمط الزخرفة
  final InputDecoration? decoration;

  /// نمط النص
  final TextStyle? style;

  /// محاذاة النص
  final TextAlign textAlign;

  /// إنشاء حقل نص مخصص
  const CustomTextField({
    super.key,
    this.controller,
    this.keyboardType,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.readOnly = false,
    this.enabled = true,
    this.required = false,
    this.isPassword = false,
    this.maxLength,
    this.maxLines = 1,
    this.minLines,
    this.inputFormatters,
    this.fillColor,
    this.filled = true,
    this.decoration,
    this.style,
    this.textAlign = TextAlign.start,
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      keyboardType: widget.keyboardType,
      obscureText: widget.isPassword ? _obscureText : false,
      maxLength: widget.maxLength,
      maxLines: widget.isPassword ? 1 : widget.maxLines,
      minLines: widget.minLines,
      readOnly: widget.readOnly,
      enabled: widget.enabled,
      textAlign: widget.textAlign,
      style: widget.style ?? const TextStyle(fontSize: 16),
      decoration: widget.decoration ??
          _buildInputDecoration(
            labelText: widget.labelText,
            hintText: widget.hintText,
            helperText: widget.helperText,
            errorText: widget.errorText,
            prefixIcon: widget.prefixIcon,
            suffixIcon: _buildSuffixIcon(),
            fillColor: widget.fillColor,
            filled: widget.filled),
      validator: widget.validator ??
          (widget.required
              ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'هذا الحقل مطلوب';
                  }
                  return null;
                }
              : null),
      onChanged: widget.onChanged,
      onFieldSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      inputFormatters: widget.inputFormatters);
  }

  /// بناء أيقونة النهاية
  Widget? _buildSuffixIcon() {
    if (widget.isPassword) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility_off : Icons.visibility,
          color: Colors.grey),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        });
    }
    return widget.suffixIcon;
  }

  /// بناء زخرفة حقل الإدخال
  InputDecoration _buildInputDecoration({
    String? labelText,
    String? hintText,
    String? helperText,
    String? errorText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    Color? fillColor,
    bool filled = true,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: filled,
      fillColor: fillColor ?? Colors.grey.shade100,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 16),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
          color: Colors.grey.shade300,
          width: 1)),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
          color: Colors.grey.shade300,
          width: 1)),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: AppColors.primary,
          width: 2)),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
          color: Colors.red.shade300,
          width: 1)),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
          color: Colors.red.shade300,
          width: 2)),
      errorStyle: TextStyle(
        color: Colors.red.shade300,
        fontSize: 12),
      helperStyle: TextStyle(
        color: Colors.grey.shade600,
        fontSize: 12),
      hintStyle: TextStyle(
        color: Colors.grey.shade400,
        fontSize: 14),
      labelStyle: TextStyle(
        color: Colors.grey.shade700,
        fontSize: 14));
  }
}
