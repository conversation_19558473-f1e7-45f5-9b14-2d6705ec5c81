import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

import '../../domain/models/forum/achievement_model.dart';
import '../../domain/models/forum/user_level_model.dart';
import '../../domain/models/forum/user_statistics_model.dart';

/// خدمة إدارة إنجازات المستخدم
class UserAchievementsService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  UserAchievementsService({
    required FirebaseFirestore firestore,
    required FirebaseAuth auth,
  })  : _firestore = firestore,
        _auth = auth;

  /// تحديث إنجازات المستخدم
  Future<void> updateUserAchievements(String userId) async {
    try {
      // جلب إحصائيات المستخدم
      final userStatsDoc = await _firestore
          .collection('forum_user_statistics')
          .doc(userId)
          .get();

      if (!userStatsDoc.exists) return;

      final userStats = UserStatisticsModel.fromFirestore(userStatsDoc);

      // جلب الإنجازات الحالية للمستخدم
      final userAchievementsDoc =
          await _firestore.collection('user_achievements').doc(userId).get();

      List<AchievementModel> currentAchievements = [];

      if (userAchievementsDoc.exists) {
        final data = userAchievementsDoc.data() as Map<String, dynamic>;
        if (data['achievements'] != null) {
          currentAchievements = (data['achievements'] as List)
              .map((e) => AchievementModel.fromMap(e as Map<String, dynamic>))
              .toList();
        }
      }

      // الحصول على جميع الإنجازات المتاحة
      final availableAchievements = AchievementModel.getAvailableAchievements();

      // تحديث قيم الإنجازات الحالية
      List<AchievementModel> updatedAchievements = [];

      for (final achievement in availableAchievements) {
        // البحث عن الإنجاز في القائمة الحالية
        final existingAchievement = currentAchievements.firstWhere(
          (a) => a.id == achievement.id,
          orElse: () => achievement);

        // تحديث قيمة الإنجاز بناءً على نوعه
        int newValue = _calculateAchievementValue(achievement, userStats);

        // إنشاء نسخة محدثة من الإنجاز
        AchievementModel updatedAchievement = existingAchievement.copyWith(
          currentValue: newValue,
          completedAt: newValue >= achievement.targetValue &&
                  existingAchievement.completedAt == null
              ? DateTime.now()
              : existingAchievement.completedAt);

        updatedAchievements.add(updatedAchievement);
      }

      // حساب النقاط الإضافية من الإنجازات المكتملة حديثًا
      int additionalPoints = 0;

      for (final achievement in updatedAchievements) {
        // التحقق مما إذا كان الإنجاز قد اكتمل حديثًا
        final existingAchievement = currentAchievements.firstWhere(
          (a) => a.id == achievement.id,
          orElse: () => achievement.copyWith(currentValue: 0));

        if (achievement.isCompleted && !existingAchievement.isCompleted) {
          additionalPoints += achievement.rewardPoints.toInt();
        }
      }

      // تحديث الإنجازات في قاعدة البيانات
      await _firestore.collection('user_achievements').doc(userId).set({
        'userId': userId,
        'achievements': updatedAchievements.map((a) => a.toMap()).toList(),
        'lastUpdated': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      // تحديث النقاط في إحصائيات المستخدم إذا كانت هناك نقاط إضافية
      if (additionalPoints > 0) {
        await _firestore
            .collection('forum_user_statistics')
            .doc(userId)
            .update({
          'points': FieldValue.increment(additionalPoints),
        });

        // تحديث المستوى والشارات
        await _updateUserLevelAndBadges(userId);
      }
    } catch (e) {
      debugPrint('خطأ في تحديث إنجازات المستخدم: $e');
    }
  }

  /// حساب قيمة الإنجاز بناءً على نوعه
  int _calculateAchievementValue(
      AchievementModel achievement, UserStatisticsModel userStats) {
    switch (achievement.id) {
      // إنجازات المواضيع
      case 'topics_creator_1':
      case 'topics_creator_2':
        return userStats.topicsCount;

      // إنجازات المشاركات
      case 'posts_contributor_1':
      case 'posts_contributor_2':
        return userStats.postsCount;

      // إنجازات التفاعل
      case 'interaction_liker_1':
        return userStats.totalLikesGiven;

      case 'interaction_popular_1':
        return userStats.totalTopicLikes + userStats.totalPostLikes;

      // إنجازات النشاط
      // ملاحظة: هذه تحتاج إلى منطق إضافي لتتبع الأيام المتتالية
      case 'activity_daily_1':
      case 'activity_daily_2':
        // يمكن تنفيذ هذا لاحقًا بمنطق أكثر تعقيدًا
        return 0;

      default:
        return 0;
    }
  }

  /// تحديث مستوى المستخدم والشارات
  Future<void> _updateUserLevelAndBadges(String userId) async {
    try {
      // جلب إحصائيات المستخدم
      final userStatsDoc = await _firestore
          .collection('forum_user_statistics')
          .doc(userId)
          .get();

      if (!userStatsDoc.exists) return;

      final userStats = UserStatisticsModel.fromFirestore(userStatsDoc);

      // تحديد المستوى الحالي
      final currentLevel = UserLevelModel.getCurrentLevel(userStats.points);

      // تحديث المستوى إذا تغير
      if (currentLevel.name != userStats.level) {
        await _firestore
            .collection('forum_user_statistics')
            .doc(userId)
            .update({
          'level': currentLevel.name,
        });

        // إضافة شارة المستوى الجديد
        await _addLevelBadge(userId, currentLevel.id);
      }
    } catch (e) {
      debugPrint('خطأ في تحديث مستوى المستخدم والشارات: $e');
    }
  }

  /// إضافة شارة مستوى جديد
  Future<void> _addLevelBadge(String userId, String levelId) async {
    try {
      // جلب الشارات الحالية للمستخدم
      final userBadgesDoc =
          await _firestore.collection('user_badges').doc(userId).get();

      List<String> currentBadges = [];

      if (userBadgesDoc.exists) {
        final data = userBadgesDoc.data() as Map<String, dynamic>;
        if (data['badges'] != null) {
          currentBadges = List<String>.from(data['badges']);
        }
      }

      // إضافة شارة المستوى الجديد إذا لم تكن موجودة
      final levelBadgeId = 'level_$levelId';

      if (!currentBadges.contains(levelBadgeId)) {
        currentBadges.add(levelBadgeId);

        // تحديث الشارات في قاعدة البيانات
        await _firestore.collection('user_badges').doc(userId).set({
          'userId': userId,
          'badges': currentBadges,
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        // تحديث الشارات في إحصائيات المستخدم
        await _firestore
            .collection('forum_user_statistics')
            .doc(userId)
            .update({
          'badges': currentBadges,
        });
      }
    } catch (e) {
      debugPrint('خطأ في إضافة شارة المستوى: $e');
    }
  }
}
