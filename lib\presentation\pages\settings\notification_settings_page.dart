import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/core/services/settings_service.dart';
import 'package:kuwait_corners/core/models/app_settings.dart';

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  final SettingsService _settingsService = SettingsService();
  late NotificationSettings _settings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    await _settingsService.initialize();
    setState(() {
      _settings = _settingsService.currentSettings.notificationSettings;
      _isLoading = false;
    });
  }

  Future<void> _updateSetting(String key, bool value) async {
    setState(() {
      switch (key) {
        case 'pushNotifications':
          _settings = _settings.copyWith(pushNotifications: value);
          break;
        case 'emailNotifications':
          _settings = _settings.copyWith(emailNotifications: value);
          break;
        case 'newEstateNotifications':
          _settings = _settings.copyWith(newEstateNotifications: value);
          break;
        case 'estateUpdateNotifications':
          _settings = _settings.copyWith(estateUpdateNotifications: value);
          break;
        case 'messageNotifications':
          _settings = _settings.copyWith(messageNotifications: value);
          break;
        case 'accountNotifications':
          _settings = _settings.copyWith(accountNotifications: value);
          break;
        case 'specialOfferNotifications':
          _settings = _settings.copyWith(specialOfferNotifications: value);
          break;
        case 'reminderNotifications':
          _settings = _settings.copyWith(reminderNotifications: value);
          break;
        case 'systemNotifications':
          _settings = _settings.copyWith(systemNotifications: value);
          break;
        case 'vibration':
          _settings = _settings.copyWith(vibration: value);
          break;
        // تم إزالة quietHoursEnabled لأنه غير موجود في النموذج
      }
    });

    await _settingsService.updateNotificationSettings(
      pushNotifications: _settings.pushNotifications,
      emailNotifications: _settings.emailNotifications,
      newEstateNotifications: _settings.newEstateNotifications,
      estateUpdateNotifications: _settings.estateUpdateNotifications,
      messageNotifications: _settings.messageNotifications,
      accountNotifications: _settings.accountNotifications,
      specialOfferNotifications: _settings.specialOfferNotifications,
      reminderNotifications: _settings.reminderNotifications,
      systemNotifications: _settings.systemNotifications,
      vibration: _settings.vibration,
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: CairoTextStyles.headlineSmall.copyWith(color: AppColors.primary),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required String key,
    required IconData icon,
    Color? iconColor,
  }) {
    return SwitchListTile(
      title: Text(title, style: CairoTextStyles.titleMedium),
      subtitle: Text(
        subtitle,
        style: CairoTextStyles.bodySmall.copyWith(color: Colors.grey.shade600),
      ),
      value: value,
      onChanged: (newValue) => _updateSetting(key, newValue),
      secondary: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (iconColor ?? AppColors.primary).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: iconColor ?? AppColors.primary),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إعدادات الإشعارات', style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              children: [
                _buildSectionTitle('الإشعارات العامة'),
                _buildSwitchTile(
                  title: 'الإشعارات الفورية',
                  subtitle: 'تلقي الإشعارات الفورية على الجهاز',
                  value: _settings.pushNotifications,
                  key: 'pushNotifications',
                  icon: Icons.notifications,
                ),
                _buildSwitchTile(
                  title: 'الإشعارات عبر البريد الإلكتروني',
                  subtitle: 'تلقي الإشعارات عبر البريد الإلكتروني',
                  value: _settings.emailNotifications,
                  key: 'emailNotifications',
                  icon: Icons.email,
                  iconColor: AppColors.info,
                ),
                _buildSwitchTile(
                  title: 'الاهتزاز',
                  subtitle: 'تفعيل الاهتزاز مع الإشعارات',
                  value: _settings.vibration,
                  key: 'vibration',
                  icon: Icons.vibration,
                  iconColor: AppColors.secondary,
                ),

                const Divider(),

                _buildSectionTitle('إشعارات العقارات'),
                _buildSwitchTile(
                  title: 'العقارات الجديدة',
                  subtitle: 'إشعارات عند إضافة عقارات جديدة',
                  value: _settings.newEstateNotifications,
                  key: 'newEstateNotifications',
                  icon: Icons.home_work,
                  iconColor: AppColors.success,
                ),
                _buildSwitchTile(
                  title: 'تحديثات العقارات',
                  subtitle: 'إشعارات عند تحديث العقارات المفضلة',
                  value: _settings.estateUpdateNotifications,
                  key: 'estateUpdateNotifications',
                  icon: Icons.update,
                  iconColor: AppColors.warning,
                ),
                _buildSwitchTile(
                  title: 'العروض الخاصة',
                  subtitle: 'إشعارات العروض والخصومات',
                  value: _settings.specialOfferNotifications,
                  key: 'specialOfferNotifications',
                  icon: Icons.local_offer,
                  iconColor: AppColors.error,
                ),

                const Divider(),

                _buildSectionTitle('إشعارات التفاعل'),
                _buildSwitchTile(
                  title: 'الرسائل',
                  subtitle: 'إشعارات الرسائل الجديدة',
                  value: _settings.messageNotifications,
                  key: 'messageNotifications',
                  icon: Icons.message,
                  iconColor: AppColors.info,
                ),
                _buildSwitchTile(
                  title: 'الحساب',
                  subtitle: 'إشعارات تحديثات الحساب والأمان',
                  value: _settings.accountNotifications,
                  key: 'accountNotifications',
                  icon: Icons.account_circle,
                  iconColor: AppColors.secondary,
                ),
                _buildSwitchTile(
                  title: 'التذكيرات',
                  subtitle: 'تذكيرات المواعيد والمهام',
                  value: _settings.reminderNotifications,
                  key: 'reminderNotifications',
                  icon: Icons.alarm,
                  iconColor: AppColors.warning,
                ),

                const Divider(),

                _buildSectionTitle('إعدادات النظام'),
                _buildSwitchTile(
                  title: 'إشعارات النظام',
                  subtitle: 'إشعارات التحديثات والصيانة',
                  value: _settings.systemNotifications,
                  key: 'systemNotifications',
                  icon: Icons.system_update,
                  iconColor: AppColors.primary,
                ),
                // تم إزالة الساعات الهادئة مؤقتاً

                const SizedBox(height: 20),
              ],
            ),
    );
  }
}
