import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/filter_options_model.dart';
import '../../../domain/models/forum/category_model.dart';

/// لوحة تصفية المنتدى الحديثة
class ModernFilterPanel extends StatefulWidget {
  /// خيارات التصفية الحالية
  final ForumFilterOptionsModel filterOptions;

  /// دالة يتم استدعاؤها عند تغيير خيارات التصفية
  final Function(ForumFilterOptionsModel) onFilterChanged;

  /// قائمة الفئات المتاحة
  final List<CategoryModel> categories;

  /// ما إذا كان يتم عرض خيارات التصفية المتقدمة
  final bool showAdvancedOptions;

  const ModernFilterPanel({
    super.key,
    required this.filterOptions,
    required this.onFilterChanged,
    this.categories = const [],
    this.showAdvancedOptions = false,
  });

  @override
  State<ModernFilterPanel> createState() => _ModernFilterPanelState();
}

class _ModernFilterPanelState extends State<ModernFilterPanel> {
  late ForumFilterOptionsModel _currentFilterOptions;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _currentFilterOptions = widget.filterOptions;
  }

  @override
  void didUpdateWidget(ModernFilterPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.filterOptions != widget.filterOptions) {
      _currentFilterOptions = widget.filterOptions;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.all(8),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس لوحة التصفية
            _buildFilterHeader(),
            
            // محتوى لوحة التصفية
            if (_isExpanded) _buildFilterContent(),
          ])));
  }

  /// بناء رأس لوحة التصفية
  Widget _buildFilterHeader() {
    return InkWell(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(16),
        topRight: Radius.circular(16),
        bottomLeft: Radius.circular(16),
        bottomRight: Radius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.filter_list,
              color: AppColors.primary),
            const SizedBox(width: 8),
            const Text(
              'تصفية وترتيب',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16)),
            const Spacer(),
            _buildActiveFiltersChip(),
            const SizedBox(width: 8),
            Icon(
              _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              color: Colors.grey.shade600),
          ])));
  }

  /// بناء شريحة الفلاتر النشطة
  Widget _buildActiveFiltersChip() {
    // حساب عدد الفلاتر النشطة
    int activeFiltersCount = 0;
    
    if (_currentFilterOptions.categoryId != null) activeFiltersCount++;
    if (_currentFilterOptions.sortType != ForumSortType.dateCreated) activeFiltersCount++;
    if (_currentFilterOptions.featuredOnly) activeFiltersCount++;
    if (_currentFilterOptions.pinnedOnly) activeFiltersCount++;
    if (_currentFilterOptions.solvedOnly) activeFiltersCount++;
    if (_currentFilterOptions.withImagesOnly) activeFiltersCount++;
    if (_currentFilterOptions.likedByUser) activeFiltersCount++;
    if (_currentFilterOptions.bookmarkedByUser) activeFiltersCount++;
    if (_currentFilterOptions.followedByUser) activeFiltersCount++;
    
    if (activeFiltersCount == 0) {
      return const SizedBox.shrink();
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.circular(12)),
      child: Text(
        '$activeFiltersCount',
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 12)));
  }

  /// بناء محتوى لوحة التصفية
  Widget _buildFilterContent() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          
          // الفئات
          if (widget.categories.isNotEmpty) ...[
            const Text(
              'الفئة',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14)),
            const SizedBox(height: 8),
            _buildCategoriesFilter(),
            const SizedBox(height: 16),
          ],
          
          // الترتيب
          const Text(
            'ترتيب حسب',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14)),
          const SizedBox(height: 8),
          _buildSortingFilter(),
          const SizedBox(height: 16),
          
          // الفلاتر الأساسية
          const Text(
            'عرض فقط',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14)),
          const SizedBox(height: 8),
          _buildBasicFilters(),
          
          // الفلاتر المتقدمة
          if (widget.showAdvancedOptions) ...[
            const SizedBox(height: 16),
            const Text(
              'خيارات متقدمة',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14)),
            const SizedBox(height: 8),
            _buildAdvancedFilters(),
          ],
          
          // أزرار التطبيق وإعادة الضبط
          const SizedBox(height: 16),
          _buildActionButtons(),
        ]));
  }

  /// بناء فلتر الفئات
  Widget _buildCategoriesFilter() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        // خيار "الكل"
        FilterChip(
          label: const Text('الكل'),
          selected: _currentFilterOptions.categoryId == null,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _currentFilterOptions = _currentFilterOptions.copyWith(
                  categoryId: null);
              });
              widget.onFilterChanged(_currentFilterOptions);
            }
          },
          backgroundColor: Colors.grey.shade200,
          selectedColor: AppColors.primary.withOpacity(0.2),
          checkmarkColor: AppColors.primary),
        
        // خيارات الفئات
        ...widget.categories.map((category) {
          return FilterChip(
            label: Text(category.name),
            selected: _currentFilterOptions.categoryId == category.id,
            onSelected: (selected) {
              setState(() {
                _currentFilterOptions = _currentFilterOptions.copyWith(
                  categoryId: selected ? category.id : null);
              });
              widget.onFilterChanged(_currentFilterOptions);
            },
            backgroundColor: Colors.grey.shade200,
            selectedColor: AppColors.primary.withOpacity(0.2),
            checkmarkColor: AppColors.primary);
        }),
      ]);
  }

  /// بناء فلتر الترتيب
  Widget _buildSortingFilter() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildSortOption(
          'الأحدث',
          ForumSortType.dateCreated,
          ForumSortDirection.descending),
        _buildSortOption(
          'الأقدم',
          ForumSortType.dateCreated,
          ForumSortDirection.ascending),
        _buildSortOption(
          'آخر تحديث',
          ForumSortType.lastUpdated,
          ForumSortDirection.descending),
        _buildSortOption(
          'الأكثر مشاهدة',
          ForumSortType.views,
          ForumSortDirection.descending),
        _buildSortOption(
          'الأكثر ردوداً',
          ForumSortType.replies,
          ForumSortDirection.descending),
        _buildSortOption(
          'الأكثر إعجاباً',
          ForumSortType.likes,
          ForumSortDirection.descending),
        _buildSortOption(
          'الأكثر نشاطاً',
          ForumSortType.activity,
          ForumSortDirection.descending),
        _buildSortOption(
          'أبجدياً',
          ForumSortType.alphabetical,
          ForumSortDirection.ascending),
      ]);
  }

  /// بناء خيار ترتيب
  Widget _buildSortOption(
    String label,
    ForumSortType sortType,
    ForumSortDirection sortDirection) {
    final isSelected = _currentFilterOptions.sortType == sortType &&
        _currentFilterOptions.sortDirection == sortDirection;
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _currentFilterOptions = _currentFilterOptions.copyWith(
              sortType: sortType,
              sortDirection: sortDirection);
          });
          widget.onFilterChanged(_currentFilterOptions);
        }
      },
      backgroundColor: Colors.grey.shade200,
      selectedColor: AppColors.primary.withOpacity(0.2),
      checkmarkColor: AppColors.primary);
  }

  /// بناء الفلاتر الأساسية
  Widget _buildBasicFilters() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        FilterChip(
          label: const Text('المميزة'),
          selected: _currentFilterOptions.featuredOnly,
          onSelected: (selected) {
            setState(() {
              _currentFilterOptions = _currentFilterOptions.copyWith(
                featuredOnly: selected);
            });
            widget.onFilterChanged(_currentFilterOptions);
          },
          backgroundColor: Colors.grey.shade200,
          selectedColor: AppColors.primary.withOpacity(0.2),
          checkmarkColor: AppColors.primary),
        FilterChip(
          label: const Text('المثبتة'),
          selected: _currentFilterOptions.pinnedOnly,
          onSelected: (selected) {
            setState(() {
              _currentFilterOptions = _currentFilterOptions.copyWith(
                pinnedOnly: selected);
            });
            widget.onFilterChanged(_currentFilterOptions);
          },
          backgroundColor: Colors.grey.shade200,
          selectedColor: AppColors.primary.withOpacity(0.2),
          checkmarkColor: AppColors.primary),
        FilterChip(
          label: const Text('المحلولة'),
          selected: _currentFilterOptions.solvedOnly,
          onSelected: (selected) {
            setState(() {
              _currentFilterOptions = _currentFilterOptions.copyWith(
                solvedOnly: selected);
            });
            widget.onFilterChanged(_currentFilterOptions);
          },
          backgroundColor: Colors.grey.shade200,
          selectedColor: AppColors.primary.withOpacity(0.2),
          checkmarkColor: AppColors.primary),
        FilterChip(
          label: const Text('مع صور'),
          selected: _currentFilterOptions.withImagesOnly,
          onSelected: (selected) {
            setState(() {
              _currentFilterOptions = _currentFilterOptions.copyWith(
                withImagesOnly: selected);
            });
            widget.onFilterChanged(_currentFilterOptions);
          },
          backgroundColor: Colors.grey.shade200,
          selectedColor: AppColors.primary.withOpacity(0.2),
          checkmarkColor: AppColors.primary),
      ]);
  }

  /// بناء الفلاتر المتقدمة
  Widget _buildAdvancedFilters() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        FilterChip(
          label: const Text('أعجبتني'),
          selected: _currentFilterOptions.likedByUser,
          onSelected: (selected) {
            setState(() {
              _currentFilterOptions = _currentFilterOptions.copyWith(
                likedByUser: selected);
            });
            widget.onFilterChanged(_currentFilterOptions);
          },
          backgroundColor: Colors.grey.shade200,
          selectedColor: AppColors.primary.withOpacity(0.2),
          checkmarkColor: AppColors.primary),
        FilterChip(
          label: const Text('محفوظة'),
          selected: _currentFilterOptions.bookmarkedByUser,
          onSelected: (selected) {
            setState(() {
              _currentFilterOptions = _currentFilterOptions.copyWith(
                bookmarkedByUser: selected);
            });
            widget.onFilterChanged(_currentFilterOptions);
          },
          backgroundColor: Colors.grey.shade200,
          selectedColor: AppColors.primary.withOpacity(0.2),
          checkmarkColor: AppColors.primary),
        FilterChip(
          label: const Text('أتابعها'),
          selected: _currentFilterOptions.followedByUser,
          onSelected: (selected) {
            setState(() {
              _currentFilterOptions = _currentFilterOptions.copyWith(
                followedByUser: selected);
            });
            widget.onFilterChanged(_currentFilterOptions);
          },
          backgroundColor: Colors.grey.shade200,
          selectedColor: AppColors.primary.withOpacity(0.2),
          checkmarkColor: AppColors.primary),
      ]);
  }

  /// بناء أزرار التطبيق وإعادة الضبط
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () {
            setState(() {
              _currentFilterOptions = ForumFilterOptionsModel.defaultOptions;
            });
            widget.onFilterChanged(_currentFilterOptions);
          },
          child: const Text('إعادة ضبط')),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: () {
            widget.onFilterChanged(_currentFilterOptions);
            setState(() {
              _isExpanded = false;
            });
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white),
          child: const Text('تطبيق')),
      ]);
  }
}
