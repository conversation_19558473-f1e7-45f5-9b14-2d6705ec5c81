import 'package:flutter/material.dart';

/// مؤشر تقدم عملية إنشاء الإعلان
/// يعرض الخطوات المختلفة لعملية إنشاء الإعلان ويوضح الخطوة الحالية
class AdCreationProgressIndicator extends StatelessWidget {
  /// الخطوة الحالية (1-7)
  final int currentStep;
  
  /// قائمة بأسماء الخطوات
  final List<String> stepNames = [
    "التصنيف",
    "الصور",
    "التفاصيل",
    "الإعدادات",
    "الخطة",
    "الميزات",
    "الدفع"
  ];

  AdCreationProgressIndicator({
    super.key,
    required this.currentStep,
  }) : assert(currentStep >= 1 && currentStep <= 7, "الخطوة يجب أن تكون بين 1 و 7");

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2)),
        ]),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مؤشر التقدم
          Row(
            children: List.generate(7, (index) {
              final isCompleted = index + 1 < currentStep;
              final isCurrent = index + 1 == currentStep;
              
              return Expanded(
                child: Container(
                  height: 4,
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  decoration: BoxDecoration(
                    color: isCompleted || isCurrent 
                        ? Colors.black87 
                        : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2))));
            })),
          
          const SizedBox(height: 8),
          
          // أسماء الخطوات
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "الخطوة $currentStep من 7",
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87)),
              Text(
                stepNames[currentStep - 1],
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87)),
            ]),
        ]));
  }
}
