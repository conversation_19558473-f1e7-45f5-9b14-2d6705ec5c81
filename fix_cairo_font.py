#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to remove explicit fontFamily: 'Cairo' declarations from Flutter files
since the app already applies Cairo font globally in main.dart
"""

import os
import re
import glob

def fix_cairo_font_in_file(file_path):
    """Remove explicit fontFamily: 'Cairo' from a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern 1: Remove fontFamily: 'Cairo' from TextStyle
        content = re.sub(r',?\s*fontFamily:\s*[\'"]Cairo[\'"]', '', content)
        
        # Pattern 2: Remove entire TextStyle if it only contains fontFamily: 'Cairo'
        content = re.sub(r'style:\s*const\s+TextStyle\(\s*fontFamily:\s*[\'"]Cairo[\'"]\s*\),?', '', content)
        content = re.sub(r'style:\s*TextStyle\(\s*fontFamily:\s*[\'"]Cairo[\'"]\s*\),?', '', content)
        
        # Pattern 3: Clean up empty TextStyle()
        content = re.sub(r'style:\s*const\s+TextStyle\(\s*\),?', '', content)
        content = re.sub(r'style:\s*TextStyle\(\s*\),?', '', content)
        
        # Pattern 4: Clean up labelStyle with only fontFamily
        content = re.sub(r'labelStyle:\s*const\s+TextStyle\(\s*fontFamily:\s*[\'"]Cairo[\'"]\s*\),?', '', content)
        content = re.sub(r'hintStyle:\s*const\s+TextStyle\(\s*fontFamily:\s*[\'"]Cairo[\'"]\s*\),?', '', content)
        
        # Clean up extra commas and whitespace
        content = re.sub(r',\s*,', ',', content)
        content = re.sub(r',\s*\)', ')', content)
        content = re.sub(r'\(\s*,', '(', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed: {file_path}")
            return True
        else:
            print(f"⏭️  No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def main():
    """Main function to process all Dart files"""
    print("🔧 Starting Cairo font cleanup...")
    
    # Find all Dart files in lib directory
    dart_files = glob.glob('lib/**/*.dart', recursive=True)
    
    fixed_count = 0
    total_count = len(dart_files)
    
    for file_path in dart_files:
        if fix_cairo_font_in_file(file_path):
            fixed_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Total files processed: {total_count}")
    print(f"   Files modified: {fixed_count}")
    print(f"   Files unchanged: {total_count - fixed_count}")
    print(f"\n✨ Cairo font cleanup completed!")

if __name__ == "__main__":
    main()
