// lib/core/services/ad_status_notification_service.dart
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Ad status notification service
/// Sends notifications to users when ad status changes
class AdStatusNotificationService {
  /// Local notifications instance
  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();

  /// Notifications enabled storage key
  static const String _notificationsEnabledKey = 'ad_status_notifications_enabled';

  /// Whether notifications are enabled
  bool _areNotificationsEnabled = true;

  /// Initialize service
  Future<void> initialize() async {
    // Initialize local notifications
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true);
    const initSettings = InitializationSettings(android: androidSettings, iOS: iosSettings);

    await _notifications.initialize(initSettings);

    // Load notification settings from local storage
    final prefs = await SharedPreferences.getInstance();
    _areNotificationsEnabled = prefs.getBool(_notificationsEnabledKey) ?? true;
  }

  /// Send ad status notification - DISABLED
  Future<void> sendAdStatusNotification({
    required String adId,
    required String adTitle,
    required String status,
    String? message,
  }) async {
    if (!_areNotificationsEnabled) return;

    // Determine notification title and body based on status
    String title;
    String body;

    switch (status) {
      case 'approved':
        title = 'Ad Approved';
        body = 'Your ad "$adTitle" has been approved and published.';
        break;
      case 'rejected':
        title = 'Ad Rejected';
        body = 'Your ad "$adTitle" has been rejected. ${message ?? 'Please review the ad details.'}';
        break;
      case 'payment_verified':
        title = 'Payment Verified';
        body = 'Payment for your ad "$adTitle" has been verified and activated.';
        break;
      case 'payment_rejected':
        title = 'Payment Rejected';
        body = 'Payment for your ad "$adTitle" has been rejected. ${message ?? 'Please contact support.'}';
        break;
      case 'expired':
        title = 'Ad Expired';
        body = 'Your ad "$adTitle" has expired. You can renew it from "My Properties" page.';
        break;
      default:
        title = 'Ad Status Update';
        body = 'Your ad "$adTitle" status has been updated to $status.';
    }

    // Show local notification
    const androidDetails = AndroidNotificationDetails(
      'ad_status_channel',
      'Ad Status',
      channelDescription: 'Notifications for ad status changes',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher');
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true);
    const details = NotificationDetails(android: androidDetails, iOS: iosDetails);

    await _notifications.show(
      adId.hashCode,
      title,
      body,
      details,
      payload: 'ad_$adId');
  }

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    _areNotificationsEnabled = enabled;

    // Save setting to local storage
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsEnabledKey, enabled);
  }

  /// Get notifications enabled state
  bool get areNotificationsEnabled => _areNotificationsEnabled;
}
