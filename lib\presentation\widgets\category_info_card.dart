// lib/presentation/widgets/category_info_card.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/theme/app_colors.dart';

/// بطاقة معلومات التصنيف
/// تعرض معلومات تفصيلية عن كل تصنيف
class CategoryInfoCard extends StatelessWidget {
  /// عنوان التصنيف
  final String title;
  
  /// وصف التصنيف
  final String description;
  
  /// أيقونة التصنيف
  final IconData icon;
  
  /// ما إذا كانت البطاقة محددة
  final bool isSelected;
  
  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback onTap;
  
  /// أمثلة على العقارات في هذا التصنيف
  final List<String> examples;

  const CategoryInfoCard({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.isSelected,
    required this.onTap,
    this.examples = const [],
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isSelected ? AppColors.primary : Colors.transparent,
          width: 2)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // أيقونة وعنوان التصنيف
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.primary
                          : AppColors.primary.withAlpha(30),
                      shape: BoxShape.circle),
                    child: Icon(
                      icon,
                      color: isSelected ? Colors.white : AppColors.primary,
                      size: 24)),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isSelected
                                ? AppColors.primary
                                : Colors.grey.shade800)),
                        if (isSelected)
                          Container(
                            margin: const EdgeInsets.only(top: 4),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withAlpha(30),
                              borderRadius: BorderRadius.circular(12)),
                            child: Text(
                              "محدد",
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary))),
                      ])),
                ]),
              
              const SizedBox(height: 16),
              
              // وصف التصنيف
              Text(
                description,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade700)),
              
              if (examples.isNotEmpty) ...[
                const SizedBox(height: 16),
                
                // أمثلة على العقارات
                Text(
                  "أمثلة:",
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800)),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: examples.map((example) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.grey.shade300)),
                      child: Text(
                        example,
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade800)));
                  }).toList()),
              ],
            ]))));
  }
}
