import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/constants/auth_messages.dart';
import 'package:kuwait_corners/core/services/loyalty_program_service.dart';
import 'package:kuwait_corners/core/services/referral_program_service.dart';
import 'package:kuwait_corners/core/services/referral_tracking_service.dart';

import 'package:shared_preferences/shared_preferences.dart';

import 'auth_event.dart';
import 'auth_state.dart';
import '../../core/constants/user_types.dart';

/// BLoC for handling authentication-related events and state changes.
/// It manages user login, signup, password reset, and logout operations.
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  /// Firebase Authentication instance.
  final FirebaseAuth _firebaseAuth;

  /// Firebase Firestore instance.
  final FirebaseFirestore _firestore;

  /// خدمة برنامج الولاء
  final LoyaltyProgramService _loyaltyService;

  /// خدمة تتبع الإحالات
  final ReferralTrackingService _trackingService = ReferralTrackingService();

  /// Constructs an [AuthBloc] with optional [firebaseAuth], [firestore], and [loyaltyService] parameters.
  /// If not provided, it defaults to [FirebaseAuth.instance], [FirebaseFirestore.instance], and a new [LoyaltyProgramService].
  AuthBloc({
    FirebaseAuth? firebaseAuth,
    FirebaseFirestore? firestore,
    LoyaltyProgramService? loyaltyService,
  })  : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _firestore = firestore ?? FirebaseFirestore.instance,
        _loyaltyService = loyaltyService ?? LoyaltyProgramService(),
        super(AuthInitial()) {
    on<LoginRequested>(_onLoginRequested);
    on<SignupRequested>(_onSignupRequested);
    on<PasswordResetRequested>(_onPasswordResetRequested);
    on<ResendVerificationRequested>(_onResendVerificationRequested);
    on<LogoutRequested>(_onLogoutRequested);
    on<LoginSuccessEvent>(_onLoginSuccess);
  }

  /// Handles the [LoginRequested] event by attempting to sign in the user.
  /// Emits [AuthLoading] state, then processes the login:
  /// - If email is not verified, sends a verification email and emits an [AuthError] state.
  /// - If login succeeds, saves the "remember me" preference and fetches the user's type from Firestore.
  /// - Finally, emits the [Authenticated] state with the user and their type.
  Future<void> _onLoginRequested(
      LoginRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      // Attempt to sign in using Firebase Authentication.
      UserCredential userCredential =
          await _firebaseAuth.signInWithEmailAndPassword(
        email: event.email,
        password: event.password);

      // Check if the user's email is verified.
      if (!userCredential.user!.emailVerified) {
        // Send a new verification email if not verified.
        await userCredential.user!.sendEmailVerification();
        emit(AuthError(AuthMessages.emailNotVerified));
        await _firebaseAuth.signOut();
      } else {
        // Save "remember me" preference using SharedPreferences.
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('rememberMe', event.rememberMe);

        // Retrieve the user document from Firestore to get the user type.
        final doc = await _firestore
            .collection('users')
            .doc(userCredential.user!.uid)
            .get();
        String userType = UserTypeConstants.seeker; // Default user type.
        if (doc.exists) {
          final rawUserType = doc.data()?['userType'] ??
                             doc.data()?['type'] ??
                             UserTypeConstants.seeker;
          // تطبيع نوع المستخدم
          userType = UserTypeConstants.normalizeUserType(rawUserType);

          // حفظ نوع المستخدم في التخزين المحلي إذا كان "تذكرني" مفعل
          if (event.rememberMe) {
            await prefs.setString('savedUserType', userType);
            print('AuthBloc: Saved user type to local storage: $userType');
          }

          // التحقق مما إذا كان البريد الإلكتروني قد تم التحقق منه
          final isEmailVerifiedInFirestore =
              doc.data()?['isEmailVerified'] ?? false;

          // إذا كان البريد الإلكتروني مؤكدًا في Firebase Auth ولكن ليس في Firestore
          if (userCredential.user!.emailVerified &&
              !isEmailVerifiedInFirestore) {
            // إضافة نقاط للتحقق من البريد الإلكتروني مباشرة في Firestore
            final loyaltyDoc = await _firestore
                .collection('loyaltyProgram')
                .doc(userCredential.user!.uid)
                .get();

            if (loyaltyDoc.exists) {
              // الحصول على النقاط الحالية
              final currentPoints = loyaltyDoc.data()?['points'] ?? 0;

              // إنشاء سجل جديد في تاريخ النقاط
              final historyEntry = {
                'date': Timestamp.now(),
                'points': 20,
                'type': 'earn',
                'reason': 'تأكيد البريد الإلكتروني',
                'balance': currentPoints + 20,
              };

              // تحديث بيانات برنامج الولاء
              await _firestore
                  .collection('loyaltyProgram')
                  .doc(userCredential.user!.uid)
                  .update({
                'points': FieldValue.increment(20),
                'totalPointsEarned': FieldValue.increment(20),
                'lastUpdated': Timestamp.now(),
                'pointsHistory': FieldValue.arrayUnion([historyEntry]),
              });
            }

            // تحديث حالة التحقق من البريد الإلكتروني في Firestore
            await _firestore
                .collection('users')
                .doc(userCredential.user!.uid)
                .update({'isEmailVerified': true});

            // لا نحتاج لعرض رسالة هنا، سيتم عرضها في واجهة المستخدم
          }
        }

        // التحقق من وجود بيانات برنامج الولاء للمستخدم
        final loyaltyData = await _loyaltyService.getCurrentUserLoyaltyData();

        // إضافة نقاط لتسجيل الدخول اليومي
        final pointsAdded = await _loyaltyService.addPointsForDailyLogin();

        // التحقق مما إذا كان هذا مستخدم قديم تم إنشاء بيانات برنامج الولاء له للتو
        bool isLegacyUser = false;
        int totalPoints = 0;

        if (loyaltyData != null) {
          // التحقق من تاريخ آخر تحديث - إذا كان حديثًا جدًا (أقل من 10 ثوانٍ)، فهذا يعني أنه تم إنشاؤه للتو
          final timeSinceUpdate =
              DateTime.now().difference(loyaltyData.lastUpdated);
          isLegacyUser = timeSinceUpdate.inSeconds < 10 &&
              loyaltyData.points > 55; // أكثر من النقاط الافتراضية
          totalPoints = loyaltyData.points;
        }

        // Emit the authenticated state with the user and their user type.
        if (isLegacyUser) {
          // رسالة خاصة للمستخدمين القدامى
          emit(Authenticated(userCredential.user!, userType,
              welcomeMessage: AuthMessages.welcomeBackLegacyUser(totalPoints)));
        } else {
          // رسالة عادية
          emit(Authenticated(userCredential.user!, userType,
              // إضافة رسالة ترحيب مع معلومات النقاط إذا تم إضافتها
              welcomeMessage: pointsAdded
                  ? AuthMessages.welcomeBackWithPoints
                  : AuthMessages.welcomeBack));
        }
      }
    } on FirebaseAuthException catch (e) {
      emit(AuthError(_getAuthErrorMessage(e)));
    } catch (e) {
      emit(AuthError("حدث خطأ غير متوقع"));
    }
  }

  /// Get localized error message for Firebase Auth exceptions
  String _getAuthErrorMessage(FirebaseAuthException error) {
    switch (error.code) {
      case 'user-not-found':
        return AuthMessages.userNotFound;
      case 'wrong-password':
        return AuthMessages.wrongPassword;
      case 'email-already-in-use':
        return AuthMessages.emailAlreadyInUse;
      case 'weak-password':
        return AuthMessages.weakPassword;
      case 'invalid-email':
        return AuthMessages.emailInvalid;
      case 'user-disabled':
        return AuthMessages.userDisabled;
      case 'too-many-requests':
        return AuthMessages.tooManyRequests;
      case 'operation-not-allowed':
        return AuthMessages.operationNotAllowed;
      case 'account-exists-with-different-credential':
        return AuthMessages.accountExistsWithDifferentCredential;
      case 'requires-recent-login':
        return AuthMessages.requiresRecentLogin;
      case 'network-request-failed':
        return AuthMessages.networkError;
      case 'invalid-credential':
        return AuthMessages.invalidCredential;
      default:
        return error.message ?? AuthMessages.loginError;
    }
  }

  /// Handles the [SignupRequested] event by creating a new user account.
  /// It validates that the password and confirmPassword match, creates the user in Firebase Auth,
  /// sends an email verification, creates a user document in Firestore with additional details,
  /// emits a [SignupSuccess] state, and signs out the newly created user.
  Future<void> _onSignupRequested(
      SignupRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      print('بدء عملية تسجيل الحساب للمستخدم: ${event.email}');

      // Validate that password and confirmPassword match.
      if (event.password != event.confirmPassword) {
        print('خطأ: كلمات المرور غير متطابقة');
        emit(const AuthError(AuthMessages.passwordsNotMatch));
        return;
      }

      print('كلمات المرور متطابقة، متابعة إنشاء الحساب...');

      // Create the user in Firebase Auth.
      print('إنشاء المستخدم في Firebase Auth...');
      UserCredential userCredential =
          await _firebaseAuth.createUserWithEmailAndPassword(
        email: event.email,
        password: event.password);
      print('تم إنشاء المستخدم بنجاح في Firebase Auth: ${userCredential.user?.uid}');

      // Send an email verification.
      print('إرسال رسالة تأكيد البريد الإلكتروني...');
      await userCredential.user!.sendEmailVerification();
      print('تم إرسال رسالة تأكيد البريد الإلكتروني بنجاح');

      // Create a Firestore document for the new user with required and additional fields.
      print('إنشاء وثيقة المستخدم في Firestore...');
      // تطبيع نوع المستخدم قبل التخزين
      final normalizedUserType = UserTypeConstants.normalizeUserType(event.userType);
      print('نوع المستخدم المطبع: $normalizedUserType');

      await _firestore.collection('users').doc(userCredential.user!.uid).set({
        'email': event.email,
        'userType': normalizedUserType, // استخدام النوع المطبع
        'isEmailVerified': false,
        'createdAt': FieldValue.serverTimestamp(),
        // Additional fields.
        'fullNameOrCompanyName': event.fullNameOrCompanyName,
        'phone': event.phone ?? '',
        'address': event.address ?? '',
        'postalCode': event.postalCode ?? '',
        'docPath': event.docPath ?? '',
        'lat': event.lat ?? 0.0,
        'lng': event.lng ?? 0.0,
      });
      print('تم إنشاء وثيقة المستخدم في Firestore بنجاح');

      // إنشاء وثيقة برنامج الولاء للمستخدم الجديد مباشرة
      print('إنشاء وثيقة برنامج الولاء...');
      try {
        await _firestore
            .collection('loyaltyProgram')
            .doc(userCredential.user!.uid)
            .set({
          'points': 50,
          'totalPointsEarned': 50,
          'totalPointsRedeemed': 0,
          'level': 'LoyaltyLevel.bronze',
          'lastUpdated': Timestamp.now(),
          'pointsExpiryDate':
              Timestamp.fromDate(DateTime.now().add(const Duration(days: 365))),
          'pointsHistory': [
            {
              'date': Timestamp.now(),
              'points': 50,
              'type': 'earn',
              'reason': 'إنشاء حساب جديد',
              'balance': 50,
            }
          ],
        });
        print('تم إنشاء وثيقة برنامج الولاء بنجاح');
      } catch (loyaltyError) {
        print('خطأ في إنشاء بيانات برنامج الولاء: $loyaltyError');
        // تجاهل خطأ برنامج الولاء ومتابعة إنشاء الحساب
      }

      // التحقق من وجود رمز إحالة واستخدامه إذا كان موجودًا
      String successMessage =
          "تم إنشاء الحساب بنجاح وإضافة 50 نقطة لبرنامج الولاء. يرجى التحقق من بريدك الإلكتروني.";

      if (event.referralCode != null && event.referralCode!.isNotEmpty) {
        try {
          // إنشاء خدمة برنامج الإحالة
          final referralService = ReferralProgramService();

          // التحقق من صلاحية رمز الإحالة
          final isValidReferralCode =
              await referralService.validateReferralCode(event.referralCode!);

          if (isValidReferralCode) {
            // استخدام رمز الإحالة مع معرف الإحالة إذا كان متاحًا
            final referralSuccess = await referralService.useReferralCode(
              event.referralCode!,
              referralId: event.referralId);

            // تسجيل إكمال عملية الإحالة
            await _trackingService.completeReferral();

            if (referralSuccess) {
              successMessage =
                  "تم إنشاء الحساب بنجاح وإضافة 50 نقطة لبرنامج الولاء. تم استخدام رمز الإحالة وإضافة 20 نقطة إضافية. يرجى التحقق من بريدك الإلكتروني.";
            }
          }
        } catch (referralError) {
          print('خطأ في معالجة رمز الإحالة: $referralError');
          // تجاهل خطأ الإحالة ومتابعة إنشاء الحساب
        }
      }

      // Emit success message and sign out the new user.
      print('إصدار رسالة النجاح وتسجيل الخروج...');
      emit(SignupSuccess(successMessage));
      await _firebaseAuth.signOut();
      print('تم إكمال عملية تسجيل الحساب بنجاح');
    } on FirebaseAuthException catch (e) {
      emit(AuthError(_getAuthErrorMessage(e)));
    } catch (e) {
      emit(AuthError("حدث خطأ غير متوقع أثناء إنشاء الحساب"));
    }
  }

  /// Handles the [PasswordResetRequested] event by sending a password reset email.
  /// Emits [AuthLoading] state, then emits [PasswordResetEmailSent] on success,
  /// or [PasswordResetFailed] on error.
  Future<void> _onPasswordResetRequested(
      PasswordResetRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: event.email);
      emit(PasswordResetEmailSent());
    } on FirebaseAuthException catch (e) {
      emit(PasswordResetFailed(_getPasswordResetErrorMessage(e)));
    } catch (e) {
      emit(PasswordResetFailed("حدث خطأ غير متوقع"));
    }
  }

  /// Get localized error message for password reset Firebase Auth exceptions
  String _getPasswordResetErrorMessage(FirebaseAuthException error) {
    switch (error.code) {
      case 'user-not-found':
        return AuthMessages.userNotFound;
      case 'invalid-email':
        return AuthMessages.emailInvalid;
      case 'too-many-requests':
        return AuthMessages.passwordResetTooManyRequests;
      case 'network-request-failed':
        return AuthMessages.networkError;
      default:
        return AuthMessages.passwordResetFailed;
    }
  }

  /// Handles the [ResendVerificationRequested] event by resending email verification.
  /// Emits [AuthLoading] state, then emits [VerificationEmailSent] on success,
  /// or [VerificationEmailFailed] on error.
  Future<void> _onResendVerificationRequested(
      ResendVerificationRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      // Try to sign in first to get the user
      final user = _firebaseAuth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
        emit(VerificationEmailSent());
      } else {
        emit(VerificationEmailFailed(AuthMessages.noUserNeedsVerification));
      }
    } on FirebaseAuthException catch (e) {
      emit(VerificationEmailFailed(_getVerificationErrorMessage(e)));
    } catch (e) {
      emit(VerificationEmailFailed(AuthMessages.unexpectedError));
    }
  }

  /// Get localized error message for verification email Firebase Auth exceptions
  String _getVerificationErrorMessage(FirebaseAuthException error) {
    switch (error.code) {
      case 'too-many-requests':
        return AuthMessages.tooManyRequests;
      case 'network-request-failed':
        return AuthMessages.networkError;
      case 'user-disabled':
        return AuthMessages.userDisabled;
      default:
        return AuthMessages.verificationEmailFailed;
    }
  }

  /// Handles the [LogoutRequested] event by signing the user out.
  /// Clears the "remember me" preference and emits the [Unauthenticated] state.
  Future<void> _onLogoutRequested(
      LogoutRequested event, Emitter<AuthState> emit) async {
    try {
      // تسجيل الخروج من Firebase
      await _firebaseAuth.signOut();

      // مسح حالة "تذكرني" ونوع المستخدم من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('rememberMe', false);
      await prefs.remove('savedUserType');
      print('AuthBloc: Cleared saved user type from local storage');

      // إصدار حالة عدم المصادقة
      emit(Unauthenticated());
    } catch (e) {
      // في حالة حدوث خطأ، نحاول مرة أخرى مسح حالة "تذكرني" ونوع المستخدم وإصدار حالة عدم المصادقة
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('rememberMe', false);
        await prefs.remove('savedUserType');
      } catch (_) {
        // تجاهل الخطأ
      }
      emit(Unauthenticated());
    }
  }

  /// معالجة حدث نجاح تسجيل الدخول
  /// يتم استدعاء هذا الحدث عندما يكون المستخدم مسجل الدخول بالفعل (مثلاً عند بدء التطبيق)
  Future<void> _onLoginSuccess(
      LoginSuccessEvent event, Emitter<AuthState> emit) async {
    try {
      // التحقق من وجود بيانات برنامج الولاء للمستخدم
      final loyaltyData = await _loyaltyService.getCurrentUserLoyaltyData();

      // إضافة نقاط لتسجيل الدخول اليومي
      final pointsAdded = await _loyaltyService.addPointsForDailyLogin();

      // ملاحظة: يمكن إضافة تسجيل نشاط تسجيل الدخول اليومي في المنتدى هنا
      // عن طريق استخدام ForumActivityTracker

      // التحقق مما إذا كان هذا مستخدم قديم تم إنشاء بيانات برنامج الولاء له للتو
      bool isLegacyUser = false;
      int totalPoints = 0;

      if (loyaltyData != null) {
        // التحقق من تاريخ آخر تحديث - إذا كان حديثًا جدًا (أقل من 10 ثوانٍ)، فهذا يعني أنه تم إنشاؤه للتو
        final timeSinceUpdate =
            DateTime.now().difference(loyaltyData.lastUpdated);
        isLegacyUser = timeSinceUpdate.inSeconds < 10 &&
            loyaltyData.points > 55; // أكثر من النقاط الافتراضية
        totalPoints = loyaltyData.points;
      }

      // إصدار حالة المصادقة مع المستخدم ونوع المستخدم
      if (isLegacyUser) {
        // رسالة خاصة للمستخدمين القدامى
        emit(Authenticated(event.user, event.userType,
            welcomeMessage:
                "مرحبًا بعودتك! تم إضافة $totalPoints نقطة إلى رصيدك كمستخدم قديم في برنامج الولاء الجديد!"));
      } else {
        // رسالة عادية
        emit(Authenticated(event.user, event.userType,
            welcomeMessage: pointsAdded
                ? "مرحبًا بعودتك! تم إضافة 5 نقاط لتسجيل الدخول اليومي."
                : "مرحبًا بعودتك!"));
      }
    } catch (e) {
      // في حالة حدوث خطأ، نصدر حالة المصادقة بدون رسالة ترحيب
      emit(Authenticated(event.user, event.userType));
    }
  }
}
