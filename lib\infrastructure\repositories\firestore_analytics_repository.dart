import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

import '../../domain/entities/market_analysis.dart';
import '../../domain/entities/market_prediction.dart';
import '../../domain/entities/price_estimation.dart';
import '../../domain/entities/recommendation.dart';
import '../../domain/enums/analysis_type.dart';
import '../../domain/enums/analysis_period.dart';
import '../../domain/repositories/analytics_repository.dart';

/// مستودع التحليلات باستخدام Firestore
class FirestoreAnalyticsRepository implements AnalyticsRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  /// إنشاء مستودع التحليلات باستخدام Firestore
  FirestoreAnalyticsRepository({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  @override
  Future<MarketAnalysis?> getMarketAnalysisById(String analysisId) async {
    try {
      final doc =
          await _firestore.collection('marketAnalyses').doc(analysisId).get();
      if (doc.exists) {
        return MarketAnalysis.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting market analysis: $e');
      return null;
    }
  }

  @override
  Future<List<MarketAnalysis>> getMarketAnalyses({
    AnalysisType? type,
    AnalysisPeriod? period,
    String? area,
    String? propertyType,
  }) async {
    try {
      Query query = _firestore.collection('marketAnalyses');

      if (type != null) {
        query = query.where('type', isEqualTo: type.toString());
      }

      if (period != null) {
        query = query.where('period', isEqualTo: period.toString());
      }

      if (area != null) {
        query = query.where('area', isEqualTo: area);
      }

      if (propertyType != null) {
        query = query.where('propertyType', isEqualTo: propertyType);
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) =>
              MarketAnalysis.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error getting market analyses: $e');
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> getMarketAnalysesPaginated({
    AnalysisType? type,
    AnalysisPeriod? period,
    String? area,
    String? propertyType,
    int limit = 20,
    String? lastAnalysisId,
  }) async {
    try {
      Query query = _firestore.collection('marketAnalyses');

      if (type != null) {
        query = query.where('type', isEqualTo: type.toString());
      }

      if (period != null) {
        query = query.where('period', isEqualTo: period.toString());
      }

      if (area != null) {
        query = query.where('area', isEqualTo: area);
      }

      if (propertyType != null) {
        query = query.where('propertyType', isEqualTo: propertyType);
      }

      query = query.orderBy('createdAt', descending: true);

      if (lastAnalysisId != null) {
        final lastDoc = await _firestore
            .collection('marketAnalyses')
            .doc(lastAnalysisId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      final analyses = snapshot.docs
          .map((doc) =>
              MarketAnalysis.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      return {
        'analyses': analyses,
        'lastAnalysisId': analyses.isNotEmpty ? analyses.last.id : null,
        'hasMore': analyses.length >= limit,
      };
    } catch (e) {
      debugPrint('Error getting paginated market analyses: $e');
      return {
        'analyses': <MarketAnalysis>[],
        'lastAnalysisId': null,
        'hasMore': false,
      };
    }
  }

  @override
  Future<List<MarketAnalysis>> getLatestMarketAnalyses({int limit = 5}) async {
    try {
      final snapshot = await _firestore
          .collection('marketAnalyses')
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => MarketAnalysis.fromMap(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('Error getting latest market analyses: $e');
      return [];
    }
  }

  @override
  Future<MarketPrediction?> getMarketPredictionById(String predictionId) async {
    try {
      final doc = await _firestore
          .collection('marketPredictions')
          .doc(predictionId)
          .get();
      if (doc.exists) {
        return MarketPrediction.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting market prediction: $e');
      return null;
    }
  }

  @override
  Future<List<MarketPrediction>> getMarketPredictions({
    PredictionType? type,
    PredictionPeriod? period,
    String? area,
    String? propertyType,
  }) async {
    try {
      Query query = _firestore.collection('marketPredictions');

      if (type != null) {
        query = query.where('type', isEqualTo: type.toString());
      }

      if (period != null) {
        query = query.where('period', isEqualTo: period.toString());
      }

      if (area != null) {
        query = query.where('area', isEqualTo: area);
      }

      if (propertyType != null) {
        query = query.where('propertyType', isEqualTo: propertyType);
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) =>
              MarketPrediction.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error getting market predictions: $e');
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> getMarketPredictionsPaginated({
    PredictionType? type,
    PredictionPeriod? period,
    String? area,
    String? propertyType,
    int limit = 20,
    String? lastPredictionId,
  }) async {
    try {
      Query query = _firestore.collection('marketPredictions');

      if (type != null) {
        query = query.where('type', isEqualTo: type.toString());
      }

      if (period != null) {
        query = query.where('period', isEqualTo: period.toString());
      }

      if (area != null) {
        query = query.where('area', isEqualTo: area);
      }

      if (propertyType != null) {
        query = query.where('propertyType', isEqualTo: propertyType);
      }

      query = query.orderBy('createdAt', descending: true);

      if (lastPredictionId != null) {
        final lastDoc = await _firestore
            .collection('marketPredictions')
            .doc(lastPredictionId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      final predictions = snapshot.docs
          .map((doc) =>
              MarketPrediction.fromJson(doc.data() as Map<String, dynamic>))
          .toList();

      return {
        'predictions': predictions,
        'lastPredictionId': predictions.isNotEmpty ? predictions.last.id : null,
        'hasMore': predictions.length >= limit,
      };
    } catch (e) {
      debugPrint('Error getting paginated market predictions: $e');
      return {
        'predictions': <MarketPrediction>[],
        'lastPredictionId': null,
        'hasMore': false,
      };
    }
  }

  @override
  Future<List<MarketPrediction>> getLatestMarketPredictions(
      {int limit = 5}) async {
    try {
      final snapshot = await _firestore
          .collection('marketPredictions')
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => MarketPrediction.fromJson(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('Error getting latest market predictions: $e');
      return [];
    }
  }

  @override
  Future<PriceEstimation> estimatePrice({
    required String userId,
    required PriceEstimationType type,
    String? estateId,
    required String area,
    required String propertyType,
    required double size,
    int? rooms,
    int? bathrooms,
    int? age,
    int? floor,
    bool? isFurnished,
    bool? isRenovated,
    List<String>? features,
  }) async {
    try {
      // إنشاء بيانات التقدير
      final estimationData = {
        'userId': userId,
        'type': type.toString(),
        'area': area,
        'propertyType': propertyType,
        'size': size,
        'createdAt': FieldValue.serverTimestamp(),
      };

      if (estateId != null) {
        estimationData['estateId'] = estateId;
      }

      if (rooms != null) {
        estimationData['rooms'] = rooms;
      }

      if (bathrooms != null) {
        estimationData['bathrooms'] = bathrooms;
      }

      if (age != null) {
        estimationData['age'] = age;
      }

      if (floor != null) {
        estimationData['floor'] = floor;
      }

      if (isFurnished != null) {
        estimationData['isFurnished'] = isFurnished;
      }

      if (isRenovated != null) {
        estimationData['isRenovated'] = isRenovated;
      }

      if (features != null) {
        estimationData['features'] = features;
      }

      // إرسال طلب التقدير إلى الخادم
      final docRef =
          await _firestore.collection('priceEstimations').add(estimationData);

      // الحصول على نتيجة التقدير
      final doc = await docRef.get();
      return PriceEstimation.fromJson(doc.data()!);
    } catch (e) {
      debugPrint('Error estimating price: $e');
      throw Exception('فشل في تقدير السعر: $e');
    }
  }

  @override
  Future<PriceEstimation?> getPriceEstimationById(String estimationId) async {
    try {
      final doc = await _firestore
          .collection('priceEstimations')
          .doc(estimationId)
          .get();
      if (doc.exists) {
        return PriceEstimation.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting price estimation: $e');
      return null;
    }
  }

  @override
  Future<List<PriceEstimation>> getUserPriceEstimations(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('priceEstimations')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => PriceEstimation.fromJson(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('Error getting user price estimations: $e');
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> getUserPriceEstimationsPaginated({
    required String userId,
    int limit = 20,
    String? lastEstimationId,
  }) async {
    try {
      Query query = _firestore
          .collection('priceEstimations')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true);

      if (lastEstimationId != null) {
        final lastDoc = await _firestore
            .collection('priceEstimations')
            .doc(lastEstimationId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      final estimations = snapshot.docs
          .map((doc) =>
              PriceEstimation.fromJson(doc.data() as Map<String, dynamic>))
          .toList();

      return {
        'estimations': estimations,
        'lastEstimationId': estimations.isNotEmpty ? estimations.last.id : null,
        'hasMore': estimations.length >= limit,
      };
    } catch (e) {
      debugPrint('Error getting paginated user price estimations: $e');
      return {
        'estimations': <PriceEstimation>[],
        'lastEstimationId': null,
        'hasMore': false,
      };
    }
  }

  @override
  Future<List<Recommendation>> getUserRecommendations({
    required String userId,
    RecommendationType? type,
    int limit = 10,
  }) async {
    try {
      Query query = _firestore
          .collection('recommendations')
          .where('userId', isEqualTo: userId);

      if (type != null) {
        query = query.where('type', isEqualTo: type.toString().split('.').last);
      }

      query = query.orderBy('createdAt', descending: true).limit(limit);

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) =>
              Recommendation.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error getting user recommendations: $e');
      return [];
    }
  }

  @override
  Future<void> updateRecommendationStatus({
    required String recommendationId,
    bool? isViewed,
    bool? isClicked,
    bool? isDismissed,
  }) async {
    try {
      final updates = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (isViewed != null) {
        updates['isViewed'] = isViewed;
      }

      if (isClicked != null) {
        updates['isClicked'] = isClicked;
      }

      if (isDismissed != null) {
        updates['isDismissed'] = isDismissed;
      }

      await _firestore
          .collection('recommendations')
          .doc(recommendationId)
          .update(updates);
    } catch (e) {
      debugPrint('Error updating recommendation status: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getAreasStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
  }) async {
    try {
      final doc = await _firestore
          .collection('statistics')
          .doc('areas')
          .collection(period.toString().split('.').last)
          .doc('data')
          .get();

      if (doc.exists) {
        return doc.data()!;
      }

      return {
        'areas': [],
        'period': period.toString(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting areas statistics: $e');
      return {
        'areas': [],
        'period': period.toString(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> getPropertyTypesStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
  }) async {
    try {
      final doc = await _firestore
          .collection('statistics')
          .doc('propertyTypes')
          .collection(period.toString().split('.').last)
          .doc('data')
          .get();

      if (doc.exists) {
        return doc.data()!;
      }

      return {
        'propertyTypes': [],
        'period': period.toString(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting property types statistics: $e');
      return {
        'propertyTypes': [],
        'period': period.toString(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> getPriceStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) async {
    try {
      String docPath = 'prices';
      if (area != null) {
        docPath += '_$area';
      }
      if (propertyType != null) {
        docPath += '_$propertyType';
      }

      final doc = await _firestore
          .collection('statistics')
          .doc(docPath)
          .collection(period.toString().split('.').last)
          .doc('data')
          .get();

      if (doc.exists) {
        return doc.data()!;
      }

      return {
        'prices': [],
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting price statistics: $e');
      return {
        'prices': [],
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> getSupplyDemandStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) async {
    try {
      String docPath = 'supplyDemand';
      if (area != null) {
        docPath += '_$area';
      }
      if (propertyType != null) {
        docPath += '_$propertyType';
      }

      final doc = await _firestore
          .collection('statistics')
          .doc(docPath)
          .collection(period.toString().split('.').last)
          .doc('data')
          .get();

      if (doc.exists) {
        return doc.data()!;
      }

      return {
        'supply': [],
        'demand': [],
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting supply demand statistics: $e');
      return {
        'supply': [],
        'demand': [],
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> getSalesStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) async {
    try {
      String docPath = 'sales';
      if (area != null) {
        docPath += '_$area';
      }
      if (propertyType != null) {
        docPath += '_$propertyType';
      }

      final doc = await _firestore
          .collection('statistics')
          .doc(docPath)
          .collection(period.toString().split('.').last)
          .doc('data')
          .get();

      if (doc.exists) {
        return doc.data()!;
      }

      return {
        'volume': [],
        'value': [],
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting sales statistics: $e');
      return {
        'volume': [],
        'value': [],
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> getRentalsStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) async {
    try {
      String docPath = 'rentals';
      if (area != null) {
        docPath += '_$area';
      }
      if (propertyType != null) {
        docPath += '_$propertyType';
      }

      final doc = await _firestore
          .collection('statistics')
          .doc(docPath)
          .collection(period.toString().split('.').last)
          .doc('data')
          .get();

      if (doc.exists) {
        return doc.data()!;
      }

      return {
        'rentals': [],
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting rentals statistics: $e');
      return {
        'rentals': [],
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> getInvestmentReturnStatistics({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) async {
    try {
      String docPath = 'investmentReturn';
      if (area != null) {
        docPath += '_$area';
      }
      if (propertyType != null) {
        docPath += '_$propertyType';
      }

      final doc = await _firestore
          .collection('statistics')
          .doc(docPath)
          .collection(period.toString().split('.').last)
          .doc('data')
          .get();

      if (doc.exists) {
        return doc.data()!;
      }

      return {
        'returns': [],
        'investments': [],
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting investment return statistics: $e');
      return {
        'returns': [],
        'investments': [],
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> getMarketIndicators({
    AnalysisPeriod period = AnalysisPeriod.year,
  }) async {
    try {
      final doc = await _firestore
          .collection('statistics')
          .doc('marketIndicators')
          .collection(period.toString().split('.').last)
          .doc('data')
          .get();

      if (doc.exists) {
        return doc.data()!;
      }

      return {
        'indicators': [],
        'period': period.toString(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting market indicators: $e');
      return {
        'indicators': [],
        'period': period.toString(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getTopInvestmentAreas(
      {int limit = 5}) async {
    try {
      final doc = await _firestore
          .collection('statistics')
          .doc('topInvestmentAreas')
          .get();

      if (doc.exists && doc.data() != null) {
        final data = doc.data()!;
        if (data['areas'] != null) {
          final areas = List<Map<String, dynamic>>.from(data['areas']);
          return areas.take(limit).toList();
        }
      }

      // بيانات مثال إذا لم تكن هناك بيانات حقيقية
      return List.generate(
          limit,
          (index) => {
                'name': 'منطقة ${index + 1}',
                'roi': 5.0 + index * 0.5,
                'growth': 3.0 + index * 0.3,
                'score': 80.0 - index * 2.0,
              });
    } catch (e) {
      debugPrint('Error getting top investment areas: $e');
      return [];
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getTopInvestmentPropertyTypes(
      {int limit = 5}) async {
    try {
      final doc = await _firestore
          .collection('statistics')
          .doc('topInvestmentPropertyTypes')
          .get();

      if (doc.exists && doc.data() != null) {
        final data = doc.data()!;
        if (data['propertyTypes'] != null) {
          final propertyTypes =
              List<Map<String, dynamic>>.from(data['propertyTypes']);
          return propertyTypes.take(limit).toList();
        }
      }

      // بيانات مثال إذا لم تكن هناك بيانات حقيقية
      return List.generate(
          limit,
          (index) => {
                'name': 'نوع العقار ${index + 1}',
                'roi': 6.0 + index * 0.4,
                'growth': 4.0 + index * 0.2,
                'score': 85.0 - index * 3.0,
              });
    } catch (e) {
      debugPrint('Error getting top investment property types: $e');
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> getMarketReport({
    AnalysisPeriod period = AnalysisPeriod.year,
    String? area,
    String? propertyType,
  }) async {
    try {
      String docPath = 'marketReport';
      if (area != null) {
        docPath += '_$area';
      }
      if (propertyType != null) {
        docPath += '_$propertyType';
      }

      final doc = await _firestore
          .collection('reports')
          .doc(docPath)
          .collection(period.toString().split('.').last)
          .doc('data')
          .get();

      if (doc.exists) {
        return doc.data()!;
      }

      // إذا لم يكن التقرير موجودًا، نقوم بتجميع البيانات من المصادر المختلفة
      final priceData = await getPriceStatistics(
        period: period,
        area: area,
        propertyType: propertyType);

      final supplyDemandData = await getSupplyDemandStatistics(
        period: period,
        area: area,
        propertyType: propertyType);

      final salesData = await getSalesStatistics(
        period: period,
        area: area,
        propertyType: propertyType);

      final rentalsData = await getRentalsStatistics(
        period: period,
        area: area,
        propertyType: propertyType);

      final investmentData = await getInvestmentReturnStatistics(
        period: period,
        area: area,
        propertyType: propertyType);

      return {
        'title': 'تقرير سوق العقارات ${area ?? ''} ${propertyType ?? ''}',
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'priceData': priceData,
        'supplyDemandData': supplyDemandData,
        'salesData': salesData,
        'rentalsData': rentalsData,
        'investmentData': investmentData,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting market report: $e');
      return {
        'title': 'تقرير سوق العقارات',
        'period': period.toString(),
        'area': area,
        'propertyType': propertyType,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> getUserReport(String userId) async {
    try {
      final doc = await _firestore.collection('userReports').doc(userId).get();

      if (doc.exists) {
        return doc.data()!;
      }

      // إذا لم يكن التقرير موجودًا، نقوم بتجميع البيانات من المصادر المختلفة
      final userEstimations = await getUserPriceEstimations(userId);

      final userRecommendations = await getUserRecommendations(
        userId: userId,
        limit: 10);

      // الحصول على عدد العقارات التي شاهدها المستخدم
      final viewedEstatesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('viewedEstates')
          .get();

      // الحصول على عدد العقارات المحفوظة
      final savedEstatesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('savedEstates')
          .get();

      // الحصول على عدد الاستفسارات
      final inquiriesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('inquiries')
          .get();

      return {
        'userId': userId,
        'viewedProperties': viewedEstatesSnapshot.docs.length,
        'savedProperties': savedEstatesSnapshot.docs.length,
        'inquiries': inquiriesSnapshot.docs.length,
        'estimations': userEstimations.length,
        'recommendations': userRecommendations.length,
        'lastActivity': DateTime.now().toIso8601String(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting user report: $e');
      return {
        'userId': userId,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }
}
