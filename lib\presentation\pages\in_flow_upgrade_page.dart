// lib/presentation/pages/in_flow_upgrade_page.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/models/plan_model.dart';
import '../../core/models/user_subscription_model.dart';
import '../../core/services/enhanced_subscription_service.dart';
import '../../core/theme/app_colors.dart';
import '../widgets/plan_comparison_table.dart';
import '../widgets/plan_status_card.dart';
import '../widgets/upgrade_benefits_card.dart';

/// صفحة الترقية أثناء تدفق إنشاء الإعلان
/// تسمح للمستخدم بترقية باقته أثناء عملية إنشاء الإعلان
class InFlowUpgradePage extends StatefulWidget {
  /// دالة يتم استدعاؤها عند الانتهاء من الترقية
  final VoidCallback? onUpgradeComplete;
  
  /// دالة يتم استدعاؤها عند إلغاء الترقية
  final VoidCallback? onCancel;
  
  /// ما إذا كان يجب عرض زر الإلغاء
  final bool showCancelButton;
  
  /// ما إذا كان يجب عرض زر التخطي
  final bool showSkipButton;
  
  /// نص زر التخطي
  final String skipButtonText;

  const InFlowUpgradePage({
    super.key,
    this.onUpgradeComplete,
    this.onCancel,
    this.showCancelButton = true,
    this.showSkipButton = true,
    this.skipButtonText = "تخطي الترقية",
  });

  @override
  State<InFlowUpgradePage> createState() => _InFlowUpgradePageState();
}

class _InFlowUpgradePageState extends State<InFlowUpgradePage> {
  /// خدمة الاشتراكات
  final _subscriptionService = EnhancedSubscriptionService();
  
  /// قائمة الباقات
  List<PlanModel> _plans = [];
  
  /// الاشتراك الحالي
  UserSubscriptionModel? _currentSubscription;
  
  /// الباقة الحالية
  PlanModel? _currentPlan;
  
  /// الباقة المختارة
  PlanModel? _selectedPlan;
  
  /// ما إذا كان جاري التحميل
  bool _isLoading = true;
  
  /// ما إذا كان جاري الترقية
  bool _isUpgrading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }
  
  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // تحميل الباقات
      final plans = await _subscriptionService.getAllPlans();
      
      // تحميل الاشتراك الحالي
      final subscription = await _subscriptionService.getCurrentSubscription();
      
      if (subscription != null) {
        // تحميل الباقة الحالية
        final currentPlan = plans.firstWhere(
          (plan) => plan.id == subscription.planId,
          orElse: () => plans.first);
        
        setState(() {
          _plans = plans;
          _currentSubscription = subscription;
          _currentPlan = currentPlan;
          _selectedPlan = currentPlan;
          _isLoading = false;
        });
      } else {
        setState(() {
          _plans = plans;
          _currentPlan = plans.first;
          _selectedPlan = plans.first;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// ترقية الباقة
  Future<void> _upgradePlan() async {
    if (_selectedPlan == null || _currentPlan == null || _selectedPlan!.id == _currentPlan!.id) {
      return;
    }
    
    setState(() {
      _isUpgrading = true;
    });
    
    try {
      // هنا يتم تنفيذ عملية الترقية
      // في الإصدار الحالي، سنفترض أن الترقية تمت بنجاح
      
      // استدعاء دالة الانتهاء من الترقية
      if (widget.onUpgradeComplete != null) {
        widget.onUpgradeComplete!();
      }
    } catch (e) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            "حدث خطأ أثناء الترقية: $e",
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
    } finally {
      setState(() {
        _isUpgrading = false;
      });
    }
  }
  
  /// إلغاء الترقية
  void _cancelUpgrade() {
    if (widget.onCancel != null) {
      widget.onCancel!();
    } else {
      Navigator.pop(context);
    }
  }
  
  /// تخطي الترقية
  void _skipUpgrade() {
    if (widget.onUpgradeComplete != null) {
      widget.onUpgradeComplete!();
    } else {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "ترقية الباقة",
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        actions: [
          if (widget.showSkipButton)
            TextButton(
              onPressed: _skipUpgrade,
              child: Text(
                widget.skipButtonText,
                style: GoogleFonts.cairo(
                  color: Colors.grey.shade700))),
        ]),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الصفحة
                  Text(
                    "اختر الباقة المناسبة لك",
                    style: GoogleFonts.cairo(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary)),
                  
                  const SizedBox(height: 8),
                  
                  // وصف الصفحة
                  Text(
                    "ترقية باقتك تمنحك المزيد من المميزات والإمكانيات لعرض عقاراتك بشكل أفضل",
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: AppColors.textSecondary)),
                  
                  const SizedBox(height: 24),
                  
                  // بطاقة الباقة الحالية
                  if (_currentPlan != null && _currentSubscription != null)
                    PlanStatusCard(
                      plan: _currentPlan!,
                      subscription: _currentSubscription!,
                      showUpgradeButton: false),
                  
                  const SizedBox(height: 24),
                  
                  // جدول مقارنة الباقات
                  PlanComparisonTable(
                    plans: _plans,
                    selectedPlanId: _selectedPlan?.id,
                    onPlanSelected: (plan) {
                      setState(() {
                        _selectedPlan = plan;
                      });
                    }),
                  
                  const SizedBox(height: 24),
                  
                  // بطاقة فوائد الترقية
                  if (_currentPlan != null && _selectedPlan != null && _selectedPlan!.id != _currentPlan!.id)
                    UpgradeBenefitsCard(
                      currentPlan: _currentPlan!,
                      targetPlan: _selectedPlan!,
                      onUpgrade: _upgradePlan,
                      showUpgradeButton: true,
                      upgradeButtonText: _isUpgrading ? "جاري الترقية..." : "ترقية الآن"),
                  
                  const SizedBox(height: 24),
                  
                  // أزرار التحكم
                  Row(
                    children: [
                      if (widget.showCancelButton)
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _isUpgrading ? null : _cancelUpgrade,
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                              side: BorderSide(color: Colors.grey.shade300)),
                            child: Text(
                              "إلغاء",
                              style: GoogleFonts.cairo(
                                fontWeight: FontWeight.bold)))),
                      
                      if (widget.showCancelButton)
                        const SizedBox(width: 16),
                      
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: _isUpgrading || _selectedPlan == null || _currentPlan == null || _selectedPlan!.id == _currentPlan!.id
                              ? null
                              : _upgradePlan,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12))),
                          child: _isUpgrading
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2))
                              : Text(
                                  "تأكيد الترقية",
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.bold)))),
                    ]),
                ])));
  }
}
