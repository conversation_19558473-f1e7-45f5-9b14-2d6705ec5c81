<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Kuwait Corners - دعوة للانضمام</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f8f9fa;
        color: #333;
        text-align: center;
      }
      .container {
        max-width: 500px;
        margin: 0 auto;
        background-color: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .logo {
        width: 100px;
        height: 100px;
        margin: 0 auto 20px;
        display: block;
      }
      h1 {
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 24px;
      }
      p {
        font-size: 16px;
        line-height: 1.5;
        margin-bottom: 20px;
      }
      .btn {
        display: block;
        background-color: #3498db;
        color: white;
        padding: 15px;
        text-decoration: none;
        border-radius: 5px;
        font-weight: bold;
        margin: 15px 0;
        text-align: center;
      }
      .btn-primary {
        background-color: #2ecc71;
      }
      .btn-secondary {
        background-color: #e74c3c;
      }
      .store-badges {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        flex-wrap: wrap;
      }
      .store-badge {
        margin: 10px;
        max-width: 160px;
      }
      .code-box {
        background-color: #f1f1f1;
        padding: 10px;
        border-radius: 5px;
        font-size: 18px;
        font-weight: bold;
        margin: 15px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <img
        src="https://firebasestorage.googleapis.com/v0/b/kuwait-corners.appspot.com/o/app_assets%2Flogo.png?alt=media"
        alt="Kuwait Corners Logo"
        class="logo"
      />
      <h1>تمت دعوتك للانضمام إلى Kuwait Corners!</h1>
      <p>
        تطبيق Kuwait Corners هو الوجهة الأولى للعقارات في الكويت. انضم الآن
        واستفد من مزايا حصرية!
      </p>

      <div id="referral-info">
        <p>استخدم رمز الإحالة التالي عند التسجيل:</p>
        <div class="code-box" id="referral-code">LOADING...</div>
        <button onclick="copyCode()" class="btn">نسخ الرمز</button>
      </div>

      <p>لتنزيل التطبيق، اختر متجر التطبيقات المناسب:</p>

      <div class="store-badges">
        <a
          href="https://play.google.com/store/apps/details?id=com.example.kuwait_corners"
          id="play-store-link"
          class="store-badge"
        >
          <img
            src="https://play.google.com/intl/en_us/badges/static/images/badges/ar_badge_web_generic.png"
            alt="Google Play"
            width="160"
          />
        </a>
        <a
          href="https://apps.apple.com/app/id123456789"
          id="app-store-link"
          class="store-badge"
        >
          <img
            src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg"
            alt="App Store"
            width="140"
          />
        </a>
      </div>

      <p style="margin-top: 20px; font-size: 14px; color: #666">
        بعد تنزيل التطبيق، قم بالتسجيل واستخدم رمز الإحالة للحصول على مزايا
        حصرية!
      </p>
    </div>

    <script>
      // استخراج رمز الإحالة من URL
      const urlParams = new URLSearchParams(window.location.search);
      const referralCode = urlParams.get("code") || "";
      const referralId = urlParams.get("id") || "";

      // عرض رمز الإحالة
      if (referralCode) {
        document.getElementById("referral-code").textContent = referralCode;
      } else {
        document.getElementById("referral-info").style.display = "none";
      }

      // تكوين روابط المتاجر مع معلمات التتبع
      const PLAY_STORE_BASE =
        "https://play.google.com/store/apps/details?id=com.example.kuwait_corners";
      const APP_STORE_BASE = "https://apps.apple.com/app/id123456789";

      const playStoreLink = `${PLAY_STORE_BASE}&referrer=utm_source%3Dreferral%26utm_medium%3Dapp%26utm_campaign%3Dreferral%26utm_content%3D${referralCode}%26referral_id%3D${referralId}`;
      const appStoreLink = `${APP_STORE_BASE}?pt=123456&ct=${referralCode}&mt=${referralId}`;

      document.getElementById("play-store-link").href = playStoreLink;
      document.getElementById("app-store-link").href = appStoreLink;

      // حفظ رمز الإحالة في localStorage
      if (referralCode) {
        try {
          localStorage.setItem("pending_referral_code", referralCode);
          if (referralId) {
            localStorage.setItem("pending_referral_id", referralId);
          }
        } catch (e) {
          // تجاهل الخطأ
        }
      }

      // نسخ الرمز إلى الحافظة
      function copyCode() {
        const codeElement = document.getElementById("referral-code");
        const code = codeElement.textContent;

        navigator.clipboard
          .writeText(code)
          .then(() => {
            alert("تم نسخ الرمز بنجاح!");
          })
          .catch((err) => {
            console.error("فشل في نسخ النص: ", err);

            // طريقة بديلة للنسخ
            const textArea = document.createElement("textarea");
            textArea.value = code;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand("copy");
            document.body.removeChild(textArea);
            alert("تم نسخ الرمز بنجاح!");
          });
      }

      // تسجيل بيانات الإحالة
      function logReferral() {
        // يمكن إضافة كود هنا لإرسال بيانات الإحالة إلى خادم
        console.log("Referral viewed", {
          referralCode,
          referralId,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
        });
      }

      // تسجيل مشاهدة الإحالة
      logReferral();
    </script>
  </body>
</html>
