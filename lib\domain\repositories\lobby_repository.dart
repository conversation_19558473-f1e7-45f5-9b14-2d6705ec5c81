import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/forum/category_model.dart';
import '../models/forum/topic_model.dart';
import '../models/forum/post_model.dart';
import '../models/forum/user_statistics_model.dart';
import '../models/forum/notification_model.dart';
import '../models/forum/reaction_model.dart';
import '../models/forum/poll_model.dart';

/// واجهة مستودع اللوبي
abstract class LobbyRepository {
  // فئات اللوبي

  /// الحصول على جميع فئات اللوبي
  Future<List<CategoryModel>> getCategories();

  /// الحصول على فئة محددة بواسطة المعرف
  Future<CategoryModel?> getCategoryById(String categoryId);

  /// إنشاء فئة جديدة
  Future<CategoryModel> createCategory(CategoryModel category);

  /// تحديث فئة موجودة
  Future<void> updateCategory(CategoryModel category);

  /// حذف فئة
  Future<void> deleteCategory(String categoryId);

  // مواضيع اللوبي

  /// الحصول على مواضيع فئة محددة
  Future<List<TopicModel>> getTopicsByCategory(
    String categoryId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
    String? sortBy,
    bool descending = true,
  });

  /// الحصول على المواضيع المميزة
  Future<List<TopicModel>> getFeaturedTopics({int limit = 5});

  /// الحصول على المواضيع المثبتة
  Future<List<TopicModel>> getPinnedTopics({int limit = 5});

  /// الحصول على أحدث المواضيع
  Future<List<TopicModel>> getLatestTopics({int limit = 10});

  /// الحصول على المواضيع الأكثر مشاهدة
  Future<List<TopicModel>> getMostViewedTopics({int limit = 10});

  /// الحصول على المواضيع الأكثر إعجاباً
  Future<List<TopicModel>> getMostLikedTopics({int limit = 10});

  /// الحصول على المواضيع الأكثر نشاطاً
  Future<List<TopicModel>> getMostActiveTopics({int limit = 10});

  /// الحصول على مواضيع مستخدم محدد
  Future<List<TopicModel>> getUserTopics(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
    String? sortBy,
    bool descending = true,
  });

  /// الحصول على موضوع محدد بواسطة المعرف
  Future<TopicModel?> getTopicById(String topicId);

  /// إنشاء موضوع جديد
  Future<TopicModel> createTopic(TopicModel topic, {List<File>? images});

  /// تحديث موضوع موجود
  Future<void> updateTopic(TopicModel topic,
      {List<File>? newImages, List<String>? imagesToDelete});

  /// حذف موضوع
  Future<void> deleteTopic(String topicId);

  /// زيادة عدد المشاهدات للموضوع
  Future<void> incrementTopicViews(String topicId);

  /// إعجاب بموضوع
  Future<void> likeTopic(String topicId, String userId);

  /// إلغاء الإعجاب بموضوع
  Future<void> unlikeTopic(String topicId, String userId);

  /// إضافة تفاعل لموضوع
  Future<void> addTopicReaction(
      String topicId, String userId, String reactionType);

  /// إزالة تفاعل من موضوع
  Future<void> removeTopicReaction(
      String topicId, String userId, String reactionType);

  /// تثبيت موضوع
  Future<void> pinTopic(String topicId);

  /// إلغاء تثبيت موضوع
  Future<void> unpinTopic(String topicId);

  /// تمييز موضوع
  Future<void> featureTopic(String topicId);

  /// إلغاء تمييز موضوع
  Future<void> unfeatureTopic(String topicId);

  /// إغلاق موضوع
  Future<void> closeTopic(String topicId);

  /// إعادة فتح موضوع
  Future<void> reopenTopic(String topicId);

  /// تعليم موضوع كمحلول
  Future<void> markTopicAsSolved(String topicId, String postId);

  /// إلغاء تعليم موضوع كمحلول
  Future<void> unmarkTopicAsSolved(String topicId);

  // مشاركات اللوبي

  /// الحصول على مشاركات موضوع محدد
  Future<List<PostModel>> getPostsByTopic(
    String topicId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
    bool includeReplies = true,
  });

  /// الحصول على ردود مشاركة محددة
  Future<List<PostModel>> getRepliesByPost(
    String postId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  });

  /// الحصول على مشاركات مستخدم محدد
  Future<List<PostModel>> getUserPosts(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? sortBy,
    bool descending = true,
  });

  /// الحصول على مشاركة محددة بواسطة المعرف
  Future<PostModel?> getPostById(String postId);

  /// إنشاء مشاركة جديدة
  Future<PostModel> createPost(PostModel post, {List<File>? images});

  /// تحديث مشاركة موجودة
  Future<void> updatePost(PostModel post,
      {List<File>? newImages, List<String>? imagesToDelete});

  /// حذف مشاركة
  Future<void> deletePost(String postId);

  /// إعجاب بمشاركة
  Future<void> likePost(String postId, String userId);

  /// إلغاء الإعجاب بمشاركة
  Future<void> unlikePost(String postId, String userId);

  /// إضافة تفاعل لمشاركة
  Future<void> addPostReaction(
      String postId, String userId, String reactionType);

  /// إزالة تفاعل من مشاركة
  Future<void> removePostReaction(
      String postId, String userId, String reactionType);

  /// تثبيت مشاركة
  Future<void> pinPost(String postId);

  /// إلغاء تثبيت مشاركة
  Future<void> unpinPost(String postId);

  /// تعليم مشاركة كأفضل إجابة
  Future<void> markPostAsBestAnswer(String postId, String topicId);

  /// إلغاء تعليم مشاركة كأفضل إجابة
  Future<void> unmarkPostAsBestAnswer(String postId, String topicId);

  /// الإبلاغ عن مشاركة
  Future<void> reportPost(String postId, String userId, String reason);

  // البحث

  /// البحث في المواضيع
  Future<List<TopicModel>> searchTopics(
    String query, {
    int limit = 20,
    String? categoryId,
  });

  /// البحث في المشاركات
  Future<List<PostModel>> searchPosts(
    String query, {
    int limit = 20,
    String? topicId,
  });

  // الإشعارات

  /// إنشاء إشعار جديد
  Future<NotificationModel> createNotification(NotificationModel notification);

  /// الحصول على إشعارات مستخدم محدد
  Future<List<NotificationModel>> getUserNotifications(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
    bool unreadOnly = false,
  });

  /// تعليم إشعار كمقروء
  Future<void> markNotificationAsRead(String notificationId);

  /// تعليم جميع الإشعارات كمقروءة
  Future<void> markAllNotificationsAsRead(String userId);

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId);

  /// الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadNotificationsCount(String userId);

  // إحصائيات المستخدم

  /// الحصول على إحصائيات مستخدم محدد
  Future<UserStatisticsModel?> getUserStatistics(String userId);

  /// تحديث إحصائيات مستخدم
  Future<void> updateUserStatistics(String userId);

  /// إعادة حساب إحصائيات مستخدم
  Future<UserStatisticsModel?> recalculateUserStatistics(String userId);

  // التفاعلات

  /// الحصول على إحصائيات التفاعلات
  Future<ReactionStats?> getReactionStats(
      String itemId, ReactionItemType itemType);

  // متابعة المواضيع

  /// متابعة موضوع
  Future<void> followTopic(String topicId, String userId);

  /// إلغاء متابعة موضوع
  Future<void> unfollowTopic(String topicId, String userId);

  /// الحصول على المواضيع المتابعة
  Future<List<TopicModel>> getFollowedTopics(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  });

  /// التحقق من متابعة موضوع
  Future<bool> isFollowingTopic(String topicId, String userId);

  // الإشارات المرجعية

  /// إضافة إشارة مرجعية لموضوع
  Future<void> bookmarkTopic(String topicId, String userId);

  /// إزالة إشارة مرجعية من موضوع
  Future<void> unbookmarkTopic(String topicId, String userId);

  /// الحصول على المواضيع المحفوظة
  Future<List<TopicModel>> getBookmarkedTopics(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  });

  /// التحقق من حفظ موضوع
  Future<bool> isTopicBookmarked(String topicId, String userId);

  // مشاركة الموضوع

  /// مشاركة موضوع
  Future<void> shareTopic(String topicId, String userId);

  // إحصائيات اللوبي

  /// الحصول على إحصائيات اللوبي
  Future<Map<String, dynamic>> getForumStatistics();

  // استطلاعات الرأي

  /// إنشاء استطلاع رأي
  Future<String?> createPoll(String topicId, Map<String, dynamic> pollData);

  /// تحديث استطلاع رأي
  Future<void> updatePoll(String topicId, String pollId, Map<String, dynamic> pollData);

  /// حذف استطلاع رأي
  Future<void> deletePoll(String topicId, String pollId);

  /// الحصول على استطلاع رأي
  Future<Map<String, dynamic>?> getPollById(String topicId, String pollId);

  /// التصويت في استطلاع رأي
  Future<void> voteOnPoll(String topicId, String pollId, List<String> optionIds, String userId);

  /// إلغاء التصويت في استطلاع رأي
  Future<void> unvoteOnPoll(String topicId, String pollId, String userId);

  /// الحصول على استطلاعات موضوع
  Future<List<PollModel>> getTopicPolls(String topicId);

  // الاستماع للتغييرات

  /// الاستماع لتغييرات الفئات
  Stream<List<CategoryModel>> listenToCategories();

  /// الاستماع لتغييرات مواضيع فئة
  Stream<List<TopicModel>> listenToCategoryTopics(
    String categoryId, {
    int limit = 20,
    String? sortBy,
    bool descending = true,
  });

  /// الاستماع لتغييرات موضوع
  Stream<TopicModel?> listenToTopic(String topicId);

  /// الاستماع لتغييرات مشاركات موضوع
  Stream<List<PostModel>> listenToTopicPosts(
    String topicId, {
    int limit = 20,
    bool includeReplies = true,
  });

  /// الاستماع لتغييرات مشاركة
  Stream<PostModel?> listenToPost(String postId);

  /// الاستماع لتغييرات إشعارات المستخدم
  Stream<List<NotificationModel>> listenToUserNotifications(
    String userId, {
    int limit = 20,
    bool unreadOnly = false,
  });

  /// الاستماع لتغييرات عدد الإشعارات غير المقروءة
  Stream<int> listenToUnreadNotificationsCount(String userId);

  /// الاستماع لتغييرات إحصائيات المستخدم
  Stream<UserStatisticsModel?> listenToUserStatistics(String userId);
}
