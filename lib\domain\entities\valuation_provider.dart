import 'package:equatable/equatable.dart';

/// كيان مزود خدمة التقييم العقاري
class ValuationProvider extends Equatable {
  final String id;
  final String name;
  final String logoUrl;
  final String description;
  final List<String> supportedValuationTypes;
  final double rating;
  final int reviewsCount;
  final int completedValuationsCount;
  final int averageCompletionTimeHours;
  final double minPrice;
  final double maxPrice;
  final String website;
  final String phoneNumber;
  final String email;
  final bool isOfficial;
  final bool isAvailable;
  final Map<String, dynamic>? additionalInfo;

  /// إنشاء كيان مزود خدمة التقييم العقاري
  const ValuationProvider({
    required this.id,
    required this.name,
    required this.logoUrl,
    required this.description,
    required this.supportedValuationTypes,
    required this.rating,
    required this.reviewsCount,
    required this.completedValuationsCount,
    required this.averageCompletionTimeHours,
    required this.minPrice,
    required this.maxPrice,
    required this.website,
    required this.phoneNumber,
    required this.email,
    required this.isOfficial,
    required this.isAvailable,
    this.additionalInfo,
  });

  /// إنشاء كيان مزود خدمة التقييم العقاري من JSON
  factory ValuationProvider.fromJson(Map<String, dynamic> json) {
    return ValuationProvider(
      id: json['id'] as String,
      name: json['name'] as String,
      logoUrl: json['logoUrl'] as String,
      description: json['description'] as String,
      supportedValuationTypes: List<String>.from(json['supportedValuationTypes']),
      rating: json['rating'] as double,
      reviewsCount: json['reviewsCount'] as int,
      completedValuationsCount: json['completedValuationsCount'] as int,
      averageCompletionTimeHours: json['averageCompletionTimeHours'] as int,
      minPrice: json['minPrice'] as double,
      maxPrice: json['maxPrice'] as double,
      website: json['website'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String,
      isOfficial: json['isOfficial'] as bool,
      isAvailable: json['isAvailable'] as bool,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?);
  }

  /// تحويل كيان مزود خدمة التقييم العقاري إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'logoUrl': logoUrl,
      'description': description,
      'supportedValuationTypes': supportedValuationTypes,
      'rating': rating,
      'reviewsCount': reviewsCount,
      'completedValuationsCount': completedValuationsCount,
      'averageCompletionTimeHours': averageCompletionTimeHours,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'website': website,
      'phoneNumber': phoneNumber,
      'email': email,
      'isOfficial': isOfficial,
      'isAvailable': isAvailable,
      'additionalInfo': additionalInfo,
    };
  }

  /// نسخ كيان مزود خدمة التقييم العقاري مع تعديل بعض الخصائص
  ValuationProvider copyWith({
    String? id,
    String? name,
    String? logoUrl,
    String? description,
    List<String>? supportedValuationTypes,
    double? rating,
    int? reviewsCount,
    int? completedValuationsCount,
    int? averageCompletionTimeHours,
    double? minPrice,
    double? maxPrice,
    String? website,
    String? phoneNumber,
    String? email,
    bool? isOfficial,
    bool? isAvailable,
    Map<String, dynamic>? additionalInfo,
  }) {
    return ValuationProvider(
      id: id ?? this.id,
      name: name ?? this.name,
      logoUrl: logoUrl ?? this.logoUrl,
      description: description ?? this.description,
      supportedValuationTypes: supportedValuationTypes ?? this.supportedValuationTypes,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      completedValuationsCount: completedValuationsCount ?? this.completedValuationsCount,
      averageCompletionTimeHours: averageCompletionTimeHours ?? this.averageCompletionTimeHours,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      website: website ?? this.website,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      isOfficial: isOfficial ?? this.isOfficial,
      isAvailable: isAvailable ?? this.isAvailable,
      additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  @override
  List<Object?> get props => [
        id,
        name,
        logoUrl,
        description,
        supportedValuationTypes,
        rating,
        reviewsCount,
        completedValuationsCount,
        averageCompletionTimeHours,
        minPrice,
        maxPrice,
        website,
        phoneNumber,
        email,
        isOfficial,
        isAvailable,
        additionalInfo,
      ];
}
