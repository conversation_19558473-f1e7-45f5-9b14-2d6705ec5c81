import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// أنواع الإشعارات المختلفة في التطبيق
enum NotificationType {
  /// إشعار عقار جديد
  newProperty,

  /// إشعار رسالة جديدة
  newMessage,

  /// إشعار تعليق جديد
  newComment,

  /// إشعار عرض جديد على طلب عقار
  newPropertyRequestOffer,

  /// إشعار قبول عرض على طلب عقار
  propertyRequestOfferAccepted,

  /// إشعار رفض عرض على طلب عقار
  propertyRequestOfferRejected,

  /// إشعار تحديث حالة طلب عقار
  propertyRequestStatusUpdate,

  /// إشعار تحقق من الحساب
  accountVerification,

  /// إشعار تحديث في المنتدى
  forumUpdate,

  /// إشعار نظام
  system,

  /// إشعار آخر
  other,
}

/// نموذج الإشعار
class NotificationModel {
  /// معرف الإشعار
  final String id;

  /// عنوان الإشعار
  final String title;

  /// نص الإشعار
  final String body;

  /// نوع الإشعار
  final NotificationType type;

  /// بيانات إضافية للإشعار
  final Map<String, dynamic> data;

  /// تاريخ الإشعار
  final DateTime timestamp;

  /// ما إذا كان الإشعار مقروء
  bool isRead;

  /// معرف المستخدم المرسل إليه الإشعار
  final String userId;

  /// إنشاء نموذج إشعار
  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    required this.timestamp,
    required this.userId,
    this.isRead = false,
  });

  /// إنشاء نموذج إشعار من Firestore
  factory NotificationModel.fromFirestore(Map<String, dynamic> data, String id) {
    return NotificationModel(
      id: id,
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      type: _getNotificationTypeFromString(data['type'] ?? 'other'),
      data: data['data'] ?? {},
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isRead: data['isRead'] ?? false,
      userId: data['userId'] ?? '');
  }

  /// تحويل نموذج الإشعار إلى Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'body': body,
      'type': type.toString().split('.').last,
      'data': data,
      'timestamp': Timestamp.fromDate(timestamp),
      'isRead': isRead,
      'userId': userId,
    };
  }

  /// الحصول على نوع الإشعار من النص
  static NotificationType _getNotificationTypeFromString(String typeString) {
    switch (typeString) {
      case 'newProperty':
        return NotificationType.newProperty;
      case 'newMessage':
        return NotificationType.newMessage;
      case 'newComment':
        return NotificationType.newComment;
      case 'newPropertyRequestOffer':
        return NotificationType.newPropertyRequestOffer;
      case 'propertyRequestOfferAccepted':
        return NotificationType.propertyRequestOfferAccepted;
      case 'propertyRequestOfferRejected':
        return NotificationType.propertyRequestOfferRejected;
      case 'propertyRequestStatusUpdate':
        return NotificationType.propertyRequestStatusUpdate;
      case 'accountVerification':
        return NotificationType.accountVerification;
      case 'forumUpdate':
        return NotificationType.forumUpdate;
      case 'system':
        return NotificationType.system;
      default:
        return NotificationType.other;
    }
  }

  /// الحصول على أيقونة الإشعار
  IconData getNotificationIcon() {
    switch (type) {
      case NotificationType.newProperty:
        return Icons.home;
      case NotificationType.newMessage:
        return Icons.message;
      case NotificationType.newComment:
        return Icons.comment;
      case NotificationType.newPropertyRequestOffer:
        return Icons.local_offer;
      case NotificationType.propertyRequestOfferAccepted:
        return Icons.check_circle;
      case NotificationType.propertyRequestOfferRejected:
        return Icons.cancel;
      case NotificationType.propertyRequestStatusUpdate:
        return Icons.update;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      case NotificationType.forumUpdate:
        return Icons.forum;
      case NotificationType.system:
        return Icons.notifications_active;
      case NotificationType.other:
        return Icons.notifications;
    }
  }

  /// الحصول على لون الإشعار
  Color getNotificationColor() {
    switch (type) {
      case NotificationType.newProperty:
        return Colors.green; // تغيير من الأزرق إلى الأخضر
      case NotificationType.newMessage:
        return Colors.green;
      case NotificationType.newComment:
        return Colors.amber;
      case NotificationType.newPropertyRequestOffer:
        return Colors.orange;
      case NotificationType.propertyRequestOfferAccepted:
        return Colors.green;
      case NotificationType.propertyRequestOfferRejected:
        return Colors.red;
      case NotificationType.propertyRequestStatusUpdate:
        return Colors.green; // تغيير من الأزرق إلى الأخضر
      case NotificationType.accountVerification:
        return Colors.purple;
      case NotificationType.forumUpdate:
        return Colors.indigo;
      case NotificationType.system:
        return Colors.red;
      case NotificationType.other:
        return Colors.grey;
    }
  }

  /// نسخة من الإشعار مع تحديث حالة القراءة
  NotificationModel copyWith({bool? isRead}) {
    return NotificationModel(
      id: id,
      title: title,
      body: body,
      type: type,
      data: data,
      timestamp: timestamp,
      userId: userId,
      isRead: isRead ?? this.isRead);
  }
}
