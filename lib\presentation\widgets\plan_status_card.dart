// lib/presentation/widgets/plan_status_card.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/models/plan_model.dart';
import '../../core/models/user_subscription_model.dart';
import '../../core/theme/app_colors.dart';

/// بطاقة حالة الباقة
/// تعرض معلومات عن الباقة الحالية للمستخدم
class PlanStatusCard extends StatelessWidget {
  /// نموذج الباقة
  final PlanModel plan;
  
  /// نموذج الاشتراك
  final UserSubscriptionModel subscription;
  
  /// دالة يتم استدعاؤها عند النقر على زر الترقية
  final VoidCallback? onUpgrade;
  
  /// ما إذا كان يجب عرض زر الترقية
  final bool showUpgradeButton;

  const PlanStatusCard({
    super.key,
    required this.plan,
    required this.subscription,
    this.onUpgrade,
    this.showUpgradeButton = true,
  });

  @override
  Widget build(BuildContext context) {
    // حساب عدد الأيام المتبقية
    final daysRemaining = subscription.endDate.difference(DateTime.now()).inDays;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: plan.color.withAlpha(100),
          width: 1)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            Row(
              children: [
                Icon(
                  Icons.workspace_premium,
                  color: plan.color,
                  size: 24),
                const SizedBox(width: 8),
                Text(
                  "باقتك الحالية",
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800)),
                const Spacer(),
                // شارة الباقة
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4),
                  decoration: BoxDecoration(
                    color: plan.color.withAlpha(30),
                    borderRadius: BorderRadius.circular(16)),
                  child: Text(
                    plan.nameAr,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: plan.color))),
              ]),
            
            const Divider(height: 24),
            
            // معلومات الباقة
            _buildInfoRow(
              context,
              "الإعلانات المتبقية:",
              "${subscription.remainingAds}/${subscription.allowedAds}",
              Icons.real_estate_agent),
            
            _buildInfoRow(
              context,
              "الصور لكل إعلان:",
              "${subscription.allowedImagesPerAd}",
              Icons.image),
            
            _buildInfoRow(
              context,
              "مدة عرض الإعلان:",
              "${subscription.adDurationDays} يوم",
              Icons.calendar_today),
            
            _buildInfoRow(
              context,
              "تاريخ الانتهاء:",
              "${subscription.endDate.day}/${subscription.endDate.month}/${subscription.endDate.year}",
              Icons.event),
            
            _buildInfoRow(
              context,
              "الأيام المتبقية:",
              "$daysRemaining يوم",
              Icons.timer,
              valueColor: daysRemaining < 7 ? Colors.red : null),
            
            if (showUpgradeButton && onUpgrade != null) ...[
              const SizedBox(height: 16),
              
              // زر الترقية
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: onUpgrade,
                  icon: const Icon(Icons.upgrade),
                  label: Text(
                    "ترقية الباقة",
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12))))),
            ],
          ])));
  }
  
  /// بناء صف معلومات
  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: Colors.grey.shade600),
          const SizedBox(width: 8),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade700)),
          const Spacer(),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: valueColor ?? Colors.grey.shade900)),
        ]));
  }
}
