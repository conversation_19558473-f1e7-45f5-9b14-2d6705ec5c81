import '../entities/estate_base.dart';
import '../repositories/estate_repository.dart';

/// Use case class for retrieving all estate records.
/// This class delegates the fetching operation to the EstateRepository.
class GetAllEstates {
  /// The repository used to perform estate-related operations.
  final EstateRepository repository;

  /// Constructs a [GetAllEstates] instance with the provided [repository].
  GetAllEstates(this.repository);

  /// Calls the repository's [getAllEstates] method to fetch a list of all estates.
  Future<List<EstateBase>> call() async {
    // استدعاء طريقة getAllEstates من المستودع
    return await repository.getAllEstates();
  }
}
