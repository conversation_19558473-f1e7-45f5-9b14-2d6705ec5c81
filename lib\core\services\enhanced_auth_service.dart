import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة المصادقة المحسنة
/// توفر وظائف متقدمة للمصادقة والتحقق من المستخدمين
class EnhancedAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  // تدفق حالة المستخدم
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // الحصول على المستخدم الحالي
  User? get currentUser => _auth.currentUser;

  /// تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
  Future<UserCredential> signInWithEmailAndPassword(String email, String password) async {
    try {
      final result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password);

      // تحديث آخر تسجيل دخول في Firestore
      if (result.user != null) {
        await _updateUserLastLogin(result.user!.uid);
      }

      return result;
    } catch (e) {
      rethrow;
    }
  }

  /// إنشاء حساب جديد باستخدام البريد الإلكتروني وكلمة المرور
  Future<UserCredential> createUserWithEmailAndPassword(
    String email,
    String password,
    String name,
    String phone
  ) async {
    try {
      final result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password);

      // إنشاء وثيقة المستخدم في Firestore
      if (result.user != null) {
        await _createUserDocument(
          result.user!.uid,
          email,
          name,
          phone);

        // تحديث اسم العرض للمستخدم
        await result.user!.updateDisplayName(name);
      }

      return result;
    } catch (e) {
      rethrow;
    }
  }

  /// تسجيل الدخول باستخدام حساب Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // بدء عملية تسجيل الدخول بـ Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        return null;
      }

      // الحصول على تفاصيل المصادقة من طلب Google
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // إنشاء بيانات اعتماد Firebase
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken);

      // تسجيل الدخول باستخدام بيانات الاعتماد
      final result = await _auth.signInWithCredential(credential);

      // التحقق مما إذا كان المستخدم جديدًا
      if (result.additionalUserInfo?.isNewUser ?? false) {
        // إنشاء وثيقة المستخدم في Firestore
        await _createUserDocument(
          result.user!.uid,
          result.user!.email ?? '',
          result.user!.displayName ?? '',
          result.user!.phoneNumber ?? '');
      } else {
        // تحديث آخر تسجيل دخول
        await _updateUserLastLogin(result.user!.uid);
      }

      return result;
    } catch (e) {
      rethrow;
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _auth.signOut();

      // مسح بيانات "تذكرني" من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('rememberMe', false);
    } catch (e) {
      rethrow;
    }
  }

  /// إرسال رابط إعادة تعيين كلمة المرور
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      rethrow;
    }
  }

  /// تحديث كلمة المرور
  Future<void> updatePassword(String newPassword) async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        await user.updatePassword(newPassword);
      } else {
        throw Exception('لا يوجد مستخدم مسجل الدخول');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// تحديث معلومات الملف الشخصي
  Future<void> updateProfile({
    String? displayName,
    String? photoURL,
    String? phone,
    String? bio,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        // تحديث معلومات المستخدم في Firebase Auth
        if (displayName != null || photoURL != null) {
          await user.updateDisplayName(displayName);
          await user.updatePhotoURL(photoURL);
        }

        // تحديث معلومات المستخدم في Firestore
        final updates = <String, dynamic>{};
        if (displayName != null) updates['name'] = displayName;
        if (photoURL != null) updates['photoURL'] = photoURL;
        if (phone != null) updates['phone'] = phone;
        if (bio != null) updates['bio'] = bio;

        if (updates.isNotEmpty) {
          await _firestore.collection('users').doc(user.uid).update(updates);
        }
      } else {
        throw Exception('لا يوجد مستخدم مسجل الدخول');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// إرسال بريد إلكتروني للتحقق
  Future<void> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      } else if (user == null) {
        throw Exception('لا يوجد مستخدم مسجل الدخول');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// التحقق من حالة التحقق من البريد الإلكتروني
  Future<bool> checkEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        // إعادة تحميل بيانات المستخدم للحصول على أحدث حالة
        await user.reload();
        return user.emailVerified;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// حفظ حالة "تذكرني" مع نوع المستخدم
  Future<void> saveRememberMeStatus(bool rememberMe, {String? userType}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('rememberMe', rememberMe);

      // حفظ نوع المستخدم إذا تم تفعيل "تذكرني"
      if (rememberMe && userType != null) {
        await prefs.setString('savedUserType', userType);
        print('Saved user type to local storage: $userType');
      } else if (!rememberMe) {
        // حذف نوع المستخدم المحفوظ إذا تم إلغاء "تذكرني"
        await prefs.remove('savedUserType');
        print('Removed saved user type from local storage');
      }
    } catch (e) {
      print('Error saving remember me status: $e');
    }
  }

  /// التحقق من حالة "تذكرني"
  Future<bool> getRememberMeStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('rememberMe') ?? false;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على نوع المستخدم المحفوظ محلياً
  Future<String?> getSavedUserType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedType = prefs.getString('savedUserType');
      print('Retrieved saved user type: $savedType');
      return savedType;
    } catch (e) {
      print('Error getting saved user type: $e');
      return null;
    }
  }

  /// الحصول على معلومات المستخدم من Firestore
  Future<Map<String, dynamic>?> getUserData() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        final doc = await _firestore.collection('users').doc(user.uid).get();
        if (doc.exists) {
          return doc.data();
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// إنشاء وثيقة المستخدم في Firestore
  Future<void> _createUserDocument(
    String uid,
    String email,
    String name,
    String phone) async {
    try {
      await _firestore.collection('users').doc(uid).set({
        'email': email,
        'name': name,
        'phone': phone,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLogin': FieldValue.serverTimestamp(),
        'userType': 'regular',
        'verificationStatus': 'unverified',
        'isActive': true,
        'adsCount': 0,
        'favoriteAds': [],
        'searchHistory': [],
      });
    } catch (e) {
      // تسجيل الخطأ ولكن لا نرميه لتجنب فشل عملية التسجيل
      print('Error creating user document: $e');
    }
  }

  /// تحديث آخر تسجيل دخول للمستخدم
  Future<void> _updateUserLastLogin(String uid) async {
    try {
      await _firestore.collection('users').doc(uid).update({
        'lastLogin': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // تجاهل الخطأ
    }
  }
}
