import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/services/messaging_service.dart';
import 'package:kuwait_corners/domain/entities/conversation.dart';
import 'package:kuwait_corners/data/repositories_impl/messaging_repository_impl.dart';
import 'package:kuwait_corners/presentation/pages/messaging/conversation_details_page.dart';
import 'package:kuwait_corners/presentation/widgets/loading_indicator.dart';
import 'package:firebase_auth/firebase_auth.dart';

// تم إزالة MockMessagingRepository واستبداله بالتنفيذ الحقيقي

/// صفحة المحادثات
class ConversationsPage extends StatefulWidget {
  const ConversationsPage({super.key});

  @override
  State<ConversationsPage> createState() => _ConversationsPageState();
}

class _ConversationsPageState extends State<ConversationsPage> {
  late MessagingService _messagingService;

  List<Conversation> _conversations = [];
  bool _isLoading = true;
  String? _errorMessage;

  final FirebaseAuth _auth = FirebaseAuth.instance;

  @override
  void initState() {
    super.initState();
    // Create a default messaging service with a real repository
    final messagingRepository = MessagingRepositoryImpl();
    _messagingService = MessagingService(
      messagingRepository: messagingRepository);
    _loadConversations();
  }

  /// تحميل المحادثات
  Future<void> _loadConversations() async {
    final user = _auth.currentUser;
    if (user == null) {
      setState(() {
        _errorMessage = 'يجب تسجيل الدخول لعرض المحادثات';
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final conversations = await _messagingService.getUserConversations();

      setState(() {
        _conversations = conversations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل المحادثات';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المحادثات')),
      body: _isLoading
          ? const LoadingIndicator()
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(_errorMessage!),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadConversations,
                        child: const Text('إعادة المحاولة')),
                    ]))
              : RefreshIndicator(
                  onRefresh: _loadConversations,
                  child: _conversations.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _conversations.length,
                          itemBuilder: (context, index) {
                            final conversation = _conversations[index];
                            return _buildConversationCard(conversation);
                          })));
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد محادثات',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600)),
          const SizedBox(height: 8),
          Text(
            'ابدأ محادثة جديدة من صفحة العقار أو الوكيل',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500),
            textAlign: TextAlign.center),
        ]));
  }

  /// بناء بطاقة محادثة
  Widget _buildConversationCard(Conversation conversation) {
    final user = _auth.currentUser;
    if (user == null) return const SizedBox();

    // تحديد معلومات المستخدم الآخر
    final otherUserIndex =
        conversation.participantIds.indexWhere((id) => id != user.uid);
    if (otherUserIndex == -1) return const SizedBox();

    final otherUserId = conversation.participantIds[otherUserIndex];
    final otherUserName = conversation.participantNames[otherUserIndex];
    final otherUserImage = conversation.participantImages[otherUserIndex];

    // تحديد ما إذا كانت هناك رسائل غير مقروءة
    final unreadCount = conversation.unreadCount?[user.uid] ?? 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ConversationDetailsPage(
                conversationId: conversation.id))).then((_) => _loadConversations());
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Stack(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundImage: otherUserImage != null
                        ? NetworkImage(otherUserImage)
                        : null,
                    child: otherUserImage == null
                        ? const Icon(Icons.person)
                        : null),
                  if (conversation.typingParticipants?.contains(otherUserId) ??
                      false)
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 2)),
                        child: const Text(
                          '...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold)))),
                ]),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            conversation.type == ConversationType.estate
                                ? conversation.title
                                : otherUserName,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: unreadCount > 0
                                  ? FontWeight.bold
                                  : FontWeight.normal),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis)),
                        if (conversation.lastMessageTimestamp != null)
                          Text(
                            _formatDate(conversation.lastMessageTimestamp!),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600)),
                      ]),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        if (conversation.lastMessageSenderName != null &&
                            conversation.lastMessageSenderId != user.uid)
                          Text(
                            '${conversation.lastMessageSenderName}: ',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade700,
                              fontWeight: unreadCount > 0
                                  ? FontWeight.bold
                                  : FontWeight.normal),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis),
                        Expanded(
                          child: Text(
                            conversation.lastMessageContent ?? 'لا توجد رسائل',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade700,
                              fontWeight: unreadCount > 0
                                  ? FontWeight.bold
                                  : FontWeight.normal),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis)),
                        if (unreadCount > 0)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(12)),
                            child: Text(
                              '$unreadCount',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold))),
                      ]),
                    if (conversation.type == ConversationType.estate &&
                        conversation.estateTitle != null) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(12)),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.home,
                                  size: 12,
                                  color: Colors.blue.shade700),
                                const SizedBox(width: 4),
                                Text(
                                  conversation.estateTitle!,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.blue.shade700),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis),
                              ])),
                        ]),
                    ],
                  ])),
            ]))));
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} سنة';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} شهر';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
