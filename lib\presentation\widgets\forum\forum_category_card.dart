import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../domain/models/forum/forum_data_model.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';

/// بطاقة فئة المنتدى المحسنة
class ForumCategoryCard extends StatelessWidget {
  /// فئة المنتدى
  final ForumCategory category;

  /// دالة عند النقر على البطاقة
  final VoidCallback onTap;

  /// ما إذا كانت البطاقة مميزة
  final bool isFeatured;

  const ForumCategoryCard({
    super.key,
    required this.category,
    required this.onTap,
    this.isFeatured = false,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد لون الفئة
    final Color categoryColor = _getCategoryColor(category.color);

    // تحديد لون النص
    final Color textColor = isFeatured ? Colors.white : Colors.black87;

    // تحديد لون الخلفية
    final Color backgroundColor =
        isFeatured ? categoryColor.withOpacity(0.9) : Colors.white;

    // تحديد لون الظل
    final Color shadowColor =
        isFeatured ? categoryColor.withOpacity(0.5) : Colors.black12;

    return Card(
      elevation: isFeatured ? 4 : 2,
      shadowColor: shadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: categoryColor.withOpacity(0.3),
          width: 1)),
      color: backgroundColor,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  // أيقونة الفئة
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: categoryColor.withOpacity(isFeatured ? 0.3 : 0.1),
                      borderRadius: BorderRadius.circular(8)),
                    child: Icon(
                      _getCategoryIcon(category.icon),
                      color: isFeatured ? Colors.white : categoryColor,
                      size: 24)),
                  const SizedBox(width: 12),
                  // اسم الفئة وعدد المواضيع
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category.name,
                          style: AppTextStyles.subtitle1.copyWith(
                            color: textColor,
                            fontWeight: FontWeight.bold),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis),
                        const SizedBox(height: 4),
                        Text(
                          '${category.topicsCount} موضوع • ${category.postsCount} مشاركة',
                          style: AppTextStyles.caption.copyWith(
                            color: textColor.withOpacity(0.7))),
                      ])),
                  // سهم للانتقال
                  Icon(
                    Icons.arrow_forward_ios,
                    color: textColor.withOpacity(0.5),
                    size: 16),
                ]),

              // وصف الفئة
              if (category.description.isNotEmpty) ...[
                const SizedBox(height: 12),
                Text(
                  category.description,
                  style: AppTextStyles.body2.copyWith(
                    color: textColor.withOpacity(0.8)),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis),
              ],

              // آخر موضوع
              if (category.lastPost != null) ...[
                const SizedBox(height: 12),
                const Divider(height: 1),
                const SizedBox(height: 12),
                Row(
                  children: [
                    // صورة المستخدم
                    if (category.lastPost!.userImage != null)
                      CircleAvatar(
                        radius: 12,
                        backgroundImage:
                            NetworkImage(category.lastPost!.userImage!))
                    else
                      CircleAvatar(
                        radius: 12,
                        backgroundColor: categoryColor.withOpacity(0.2),
                        child: Icon(
                          Icons.person,
                          size: 16,
                          color: categoryColor)),
                    const SizedBox(width: 8),
                    // معلومات آخر موضوع
                    Expanded(
                      child: RichText(
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        text: TextSpan(
                          style: AppTextStyles.caption.copyWith(
                            color: textColor.withOpacity(0.7)),
                          children: [
                            TextSpan(
                              text: 'آخر مشاركة: ',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: textColor.withOpacity(0.8))),
                            TextSpan(
                              text: category.lastPost!.topicTitle,
                              style: TextStyle(
                                color: textColor.withOpacity(0.9))),
                            TextSpan(
                              text: ' بواسطة '),
                            TextSpan(
                              text: category.lastPost!.userName,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: textColor.withOpacity(0.9))),
                          ]))),
                    const SizedBox(width: 8),
                    // وقت آخر مشاركة
                    Text(
                      timeago.format(category.lastPost!.timestamp,
                          locale: 'ar'),
                      style: AppTextStyles.caption.copyWith(
                        color: textColor.withOpacity(0.6),
                        fontSize: 10)),
                  ]),
              ],
            ]))));
  }

  /// الحصول على لون الفئة
  Color _getCategoryColor(String colorCode) {
    try {
      if (colorCode.startsWith('#')) {
        return Color(int.parse('0xFF${colorCode.substring(1)}'));
      } else {
        // استخدام اللون الافتراضي إذا كان التنسيق غير صحيح
        return AppColors.primary;
      }
    } catch (e) {
      return AppColors.primary;
    }
  }

  /// الحصول على أيقونة الفئة
  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'home':
        return Icons.home;
      case 'business':
        return Icons.business;
      case 'apartment':
        return Icons.apartment;
      case 'house':
        return Icons.house;
      case 'villa':
        return Icons.villa;
      case 'location_city':
        return Icons.location_city;
      case 'store':
        return Icons.store;
      case 'local_offer':
        return Icons.local_offer;
      case 'attach_money':
        return Icons.attach_money;
      case 'question_answer':
        return Icons.question_answer;
      case 'forum':
        return Icons.forum;
      case 'chat':
        return Icons.chat;
      case 'info':
        return Icons.info;
      case 'help':
        return Icons.help;
      case 'announcement':
        return Icons.announcement;
      default:
        return Icons.category;
    }
  }
}
