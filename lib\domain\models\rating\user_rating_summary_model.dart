// lib/domain/models/rating/user_rating_summary_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نموذج ملخص تقييمات المستخدم
class UserRatingSummaryModel extends Equatable {
  /// معرف المستخدم
  final String userId;

  /// إجمالي عدد التقييمات
  final int totalRatings;

  /// متوسط التقييم العام
  final double averageOverallRating;

  /// متوسط تقييم التواصل
  final double averageCommunicationRating;

  /// متوسط تقييم الموثوقية
  final double averageReliabilityRating;

  /// متوسط تقييم الاحترافية
  final double averageProfessionalismRating;

  /// متوسط تقييم سرعة الاستجابة
  final double averageResponsivenessRating;

  /// توزيع النجوم (1-5 نجوم وعدد التقييمات لكل منها)
  final Map<int, int> starDistribution;

  /// تاريخ آخر تحديث
  final DateTime lastUpdated;

  /// عدد التقييمات الإيجابية (4-5 نجوم)
  final int positiveRatings;

  /// عدد التقييمات السلبية (1-2 نجوم)
  final int negativeRatings;

  /// عدد التقييمات المتوسطة (3 نجوم)
  final int neutralRatings;

  /// نسبة التقييمات الإيجابية
  final double positivePercentage;

  const UserRatingSummaryModel({
    required this.userId,
    required this.totalRatings,
    required this.averageOverallRating,
    required this.averageCommunicationRating,
    required this.averageReliabilityRating,
    required this.averageProfessionalismRating,
    required this.averageResponsivenessRating,
    required this.starDistribution,
    required this.lastUpdated,
    this.positiveRatings = 0,
    this.negativeRatings = 0,
    this.neutralRatings = 0,
    this.positivePercentage = 0.0,
  });

  /// إنشاء من Firestore
  factory UserRatingSummaryModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    final starDistribution = <int, int>{};
    if (data['starDistribution'] != null) {
      final starData = data['starDistribution'] as Map<String, dynamic>;
      for (final entry in starData.entries) {
        starDistribution[int.parse(entry.key)] = entry.value as int;
      }
    }

    return UserRatingSummaryModel(
      userId: doc.id,
      totalRatings: data['totalRatings'] ?? 0,
      averageOverallRating: (data['averageOverallRating'] ?? 0.0).toDouble(),
      averageCommunicationRating: (data['averageCommunicationRating'] ?? 0.0).toDouble(),
      averageReliabilityRating: (data['averageReliabilityRating'] ?? 0.0).toDouble(),
      averageProfessionalismRating: (data['averageProfessionalismRating'] ?? 0.0).toDouble(),
      averageResponsivenessRating: (data['averageResponsivenessRating'] ?? 0.0).toDouble(),
      starDistribution: starDistribution,
      lastUpdated: (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
      positiveRatings: data['positiveRatings'] ?? 0,
      negativeRatings: data['negativeRatings'] ?? 0,
      neutralRatings: data['neutralRatings'] ?? 0,
      positivePercentage: (data['positivePercentage'] ?? 0.0).toDouble(),
    );
  }

  /// تحويل إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    final starData = <String, int>{};
    for (final entry in starDistribution.entries) {
      starData[entry.key.toString()] = entry.value;
    }

    return {
      'totalRatings': totalRatings,
      'averageOverallRating': averageOverallRating,
      'averageCommunicationRating': averageCommunicationRating,
      'averageReliabilityRating': averageReliabilityRating,
      'averageProfessionalismRating': averageProfessionalismRating,
      'averageResponsivenessRating': averageResponsivenessRating,
      'starDistribution': starData,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'positiveRatings': positiveRatings,
      'negativeRatings': negativeRatings,
      'neutralRatings': neutralRatings,
      'positivePercentage': positivePercentage,
    };
  }

  /// إنشاء نسخة محدثة
  UserRatingSummaryModel copyWith({
    String? userId,
    int? totalRatings,
    double? averageOverallRating,
    double? averageCommunicationRating,
    double? averageReliabilityRating,
    double? averageProfessionalismRating,
    double? averageResponsivenessRating,
    Map<int, int>? starDistribution,
    DateTime? lastUpdated,
    int? positiveRatings,
    int? negativeRatings,
    int? neutralRatings,
    double? positivePercentage,
  }) {
    return UserRatingSummaryModel(
      userId: userId ?? this.userId,
      totalRatings: totalRatings ?? this.totalRatings,
      averageOverallRating: averageOverallRating ?? this.averageOverallRating,
      averageCommunicationRating: averageCommunicationRating ?? this.averageCommunicationRating,
      averageReliabilityRating: averageReliabilityRating ?? this.averageReliabilityRating,
      averageProfessionalismRating: averageProfessionalismRating ?? this.averageProfessionalismRating,
      averageResponsivenessRating: averageResponsivenessRating ?? this.averageResponsivenessRating,
      starDistribution: starDistribution ?? this.starDistribution,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      positiveRatings: positiveRatings ?? this.positiveRatings,
      negativeRatings: negativeRatings ?? this.negativeRatings,
      neutralRatings: neutralRatings ?? this.neutralRatings,
      positivePercentage: positivePercentage ?? this.positivePercentage,
    );
  }

  /// حساب الإحصائيات المشتقة
  UserRatingSummaryModel calculateDerivedStats() {
    final positive = (starDistribution[4] ?? 0) + (starDistribution[5] ?? 0);
    final negative = (starDistribution[1] ?? 0) + (starDistribution[2] ?? 0);
    final neutral = starDistribution[3] ?? 0;
    final positivePercent = totalRatings > 0 ? (positive / totalRatings) * 100 : 0.0;

    return copyWith(
      positiveRatings: positive,
      negativeRatings: negative,
      neutralRatings: neutral,
      positivePercentage: positivePercent,
    );
  }

  /// الحصول على مستوى التقييم
  RatingLevel get ratingLevel {
    if (averageOverallRating >= 4.5) {
      return RatingLevel.excellent;
    } else if (averageOverallRating >= 4.0) {
      return RatingLevel.veryGood;
    } else if (averageOverallRating >= 3.5) {
      return RatingLevel.good;
    } else if (averageOverallRating >= 3.0) {
      return RatingLevel.average;
    } else if (averageOverallRating >= 2.0) {
      return RatingLevel.poor;
    } else {
      return RatingLevel.veryPoor;
    }
  }

  /// الحصول على نص مستوى التقييم
  String get ratingLevelText {
    switch (ratingLevel) {
      case RatingLevel.excellent:
        return 'ممتاز';
      case RatingLevel.veryGood:
        return 'جيد جداً';
      case RatingLevel.good:
        return 'جيد';
      case RatingLevel.average:
        return 'متوسط';
      case RatingLevel.poor:
        return 'ضعيف';
      case RatingLevel.veryPoor:
        return 'ضعيف جداً';
    }
  }

  /// الحصول على لون مستوى التقييم
  String get ratingLevelColor {
    switch (ratingLevel) {
      case RatingLevel.excellent:
        return '#4CAF50'; // أخضر
      case RatingLevel.veryGood:
        return '#8BC34A'; // أخضر فاتح
      case RatingLevel.good:
        return '#CDDC39'; // أخضر مصفر
      case RatingLevel.average:
        return '#FFC107'; // أصفر
      case RatingLevel.poor:
        return '#FF9800'; // برتقالي
      case RatingLevel.veryPoor:
        return '#F44336'; // أحمر
    }
  }

  /// التحقق من وجود تقييمات كافية
  bool get hasEnoughRatings {
    return totalRatings >= 3;
  }

  /// الحصول على نسبة النجوم لكل مستوى
  Map<int, double> get starPercentages {
    final percentages = <int, double>{};
    for (int i = 1; i <= 5; i++) {
      final count = starDistribution[i] ?? 0;
      percentages[i] = totalRatings > 0 ? (count / totalRatings) * 100 : 0.0;
    }
    return percentages;
  }

  /// الحصول على أعلى نقاط القوة
  List<String> get topStrengths {
    final strengths = <String>[];
    
    if (averageCommunicationRating >= 4.0) {
      strengths.add('تواصل ممتاز');
    }
    if (averageReliabilityRating >= 4.0) {
      strengths.add('موثوق');
    }
    if (averageProfessionalismRating >= 4.0) {
      strengths.add('احترافي');
    }
    if (averageResponsivenessRating >= 4.0) {
      strengths.add('سريع الاستجابة');
    }
    
    return strengths;
  }

  @override
  List<Object?> get props => [
        userId,
        totalRatings,
        averageOverallRating,
        averageCommunicationRating,
        averageReliabilityRating,
        averageProfessionalismRating,
        averageResponsivenessRating,
        starDistribution,
        lastUpdated,
        positiveRatings,
        negativeRatings,
        neutralRatings,
        positivePercentage,
      ];
}

/// مستويات التقييم
enum RatingLevel {
  excellent,
  veryGood,
  good,
  average,
  poor,
  veryPoor,
}
