import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';
import 'package:kuwait_corners/domain/entities/estate_factory.dart';
import 'package:kuwait_corners/domain/entities/estate_converter.dart';
import 'package:kuwait_corners/presentation/widgets/estate_card.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:kuwait_corners/presentation/pages/debug_estates_page.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;

/// صفحة نسخ العقارات للوكلاء
class CopyPropertyPage extends StatefulWidget {
  const CopyPropertyPage({super.key});

  @override
  State<CopyPropertyPage> createState() => _CopyPropertyPageState();
}

class _CopyPropertyPageState extends State<CopyPropertyPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'الكل';

  // نوع زر المعاينة: 'modern', 'animated', 'iconic'
  String _previewButtonType = 'modern';

  // التصنيفات الخاصة بالإيجار فقط
  final List<String> _categories = [
    'الكل',
    'منزل للايجار',
    'شقة للايجار',
    'مخزن للايجار',
    'مكتب للايجار',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'نسخ عقار للإيجار',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white)),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.visibility, color: Colors.white),
            tooltip: 'نوع زر المعاينة',
            onSelected: (String value) {
              setState(() {
                _previewButtonType = value;
              });
            },
            itemBuilder: (BuildContext context) => [
              PopupMenuItem<String>(
                value: 'modern',
                child: Row(
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      color: _previewButtonType == 'modern' ? AppColors.primary : AppColors.textSecondary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'عصري',
                      style: GoogleFonts.cairo(
                        color: _previewButtonType == 'modern' ? AppColors.primary : AppColors.textSecondary,
                        fontWeight: _previewButtonType == 'modern' ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuItem<String>(
                value: 'animated',
                child: Row(
                  children: [
                    Icon(
                      Icons.animation,
                      color: _previewButtonType == 'animated' ? AppColors.primary : AppColors.textSecondary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'متحرك',
                      style: GoogleFonts.cairo(
                        color: _previewButtonType == 'animated' ? AppColors.primary : AppColors.textSecondary,
                        fontWeight: _previewButtonType == 'animated' ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuItem<String>(
                value: 'iconic',
                child: Row(
                  children: [
                    Icon(
                      Icons.circle,
                      color: _previewButtonType == 'iconic' ? AppColors.primary : AppColors.textSecondary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'أيقوني',
                      style: GoogleFonts.cairo(
                        color: _previewButtonType == 'iconic' ? AppColors.primary : AppColors.textSecondary,
                        fontWeight: _previewButtonType == 'iconic' ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DebugEstatesPage(),
                ),
              );
            },
            icon: const Icon(Icons.bug_report, color: Colors.white),
            tooltip: 'تشخيص العقارات'),
        ]),
      body: Stack(
        children: [
          // الأشكال الهندسية في الخلفية
          Positioned.fill(
            child: CustomPaint(
              painter: RentalPropertyShapesPainter(),
            ),
          ),
          // المحتوى الرئيسي
          Column(
            children: [
              _buildSearchAndFilters(),
              Expanded(
                child: _buildPropertiesList()),
            ]),
        ]));
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: AppColors.border, width: 1),
        )),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            style: GoogleFonts.cairo(),
            decoration: InputDecoration(
              hintText: 'البحث عن عقار للإيجار...',
              hintStyle: GoogleFonts.cairo(color: AppColors.textLight),
              prefixIcon: Icon(Icons.search, color: AppColors.primary),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      })
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.border)),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              filled: true,
              fillColor: AppColors.background),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            }),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _categories.map((category) {
                final isSelected = _selectedCategory == category;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(
                      category,
                      style: GoogleFonts.cairo(
                        color: isSelected ? Colors.white : AppColors.textSecondary,
                        fontSize: 13,
                        fontWeight: FontWeight.w500)),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                    backgroundColor: AppColors.background,
                    selectedColor: AppColors.primary,
                    elevation: 0,
                    pressElevation: 0));
              }).toList())),
        ]));
  }

  Widget _buildPropertiesList() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getPropertiesStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل العقارات');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState();
        }

        final properties = snapshot.data!.docs;
        final filteredProperties = _filterProperties(properties);

        if (filteredProperties.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredProperties.length,
            itemBuilder: (context, index) {
              final propertyDoc = filteredProperties[index];
              print('🏗️ بناء بطاقة العقار رقم $index');

              try {
                final estateBase = EstateFactory.createFromSnapshot(propertyDoc);
                print('✅ تم إنشاء EstateBase بنجاح');

                final estate = EstateConverter.toLegacyEstate(estateBase);
                if (estate != null) {
                  print('✅ تم تحويل العقار إلى Legacy Estate: ${estate.title}');
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _buildCopyablePropertyCard(estate, propertyDoc.id));
                } else {
                  print('❌ فشل في تحويل العقار إلى Legacy Estate');
                }
                return const SizedBox.shrink();
              } catch (e) {
                print('❌ خطأ في بناء بطاقة العقار: $e');
                return const SizedBox.shrink();
              }
            }));
      });
  }

  Widget _buildCopyablePropertyCard(Estate estate, String propertyId) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border, width: 1),
      ),
      child: Column(
        children: [
          Stack(
            children: [
              EstateCard(
                estate: estate,
                onTap: () => _showPropertyDetails(estate)),
              // عرض نوع العقار
              Positioned(
                top: 12,
                left: 12,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    estate.subCategory ?? 'غير محدد',
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      color: Colors.white,
                      fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12))),
            child: Column(
              children: [
                // معلومات إضافية
                Row(
                  children: [
                    Icon(Icons.category_outlined, size: 14, color: AppColors.textSecondary),
                    const SizedBox(width: 6),
                    Text(
                      'نوع العقار: ${estate.subCategory ?? 'غير محدد'}',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // الأزرار
                Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: ElevatedButton.icon(
                        onPressed: () => _copyProperty(estate),
                        icon: const Icon(Icons.copy, size: 18),
                        label: Text('نسخ العقار', style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12))))),
                    const SizedBox(width: 12),
                    _buildPreviewButton(estate),
                  ]),
              ])),
        ]));
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.home_work_outlined,
            size: 80,
            color: AppColors.primary.withValues(alpha: 0.5)),
          const SizedBox(height: 16),
          Text(
            'لا توجد عقارات للإيجار متاحة للنسخ',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary)),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير فلاتر البحث أو المحاولة لاحقاً',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: AppColors.textLight)),
        ]));
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[600])),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {});
            },
            child: const Text('إعادة المحاولة')),
        ]));
  }

  Stream<QuerySnapshot> _getPropertiesStream() {
    final currentUser = FirebaseAuth.instance.currentUser;

    print('🔍 البحث عن عقارات للإيجار...');
    print('👤 المستخدم الحالي: ${currentUser?.uid}');

    // أولاً: جلب جميع العقارات للإيجار بدون استبعاد المستخدم الحالي للتشخيص
    FirebaseFirestore.instance
        .collection('estates')
        .where('mainCategory', isEqualTo: 'عقار للايجار')
        .get()
        .then((allSnapshot) {
          print('🔍 إجمالي عقارات الإيجار في قاعدة البيانات: ${allSnapshot.docs.length}');
          for (var doc in allSnapshot.docs) {
            final data = doc.data();
            print('📋 عقار: ${data['title']} - isPaymentVerified: ${data['isPaymentVerified']} - ownerId: ${data['ownerId']} - mainCategory: ${data['mainCategory']}');
          }
        });

    return FirebaseFirestore.instance
        .collection('estates')
        .where('mainCategory', isEqualTo: 'عقار للايجار') // فقط عقارات الإيجار
        .where('ownerId', isNotEqualTo: currentUser?.uid) // استبعاد عقارات المستخدم الحالي
        .orderBy('createdAt', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) {
          print('📊 تم العثور على ${snapshot.docs.length} عقار بعد الفلترة');
          for (var doc in snapshot.docs) {
            final data = doc.data();
            final isPaymentVerified = data['isPaymentVerified'];
            print('🏠 عقار: ${data['title']} - mainCategory: ${data['mainCategory']} - isPaymentVerified: $isPaymentVerified - ownerId: ${data['ownerId']}');
          }
          return snapshot;
        });
  }

  List<QueryDocumentSnapshot> _filterProperties(List<QueryDocumentSnapshot> properties) {
    print('🔍 بدء فلترة العقارات...');
    print('📊 عدد العقارات قبل الفلترة: ${properties.length}');
    print('🏷️ الفئة المختارة: $_selectedCategory');
    print('🔍 نص البحث: "$_searchQuery"');

    List<QueryDocumentSnapshot> filtered = properties;

    // أولاً: فلترة العقارات المدفوعة (معالجة حالة isPaymentVerified = null)
    filtered = filtered.where((property) {
      final data = property.data() as Map<String, dynamic>;
      final isPaymentVerified = data['isPaymentVerified'];
      final isPaymentVerifiedValue = isPaymentVerified == true; // فقط المدفوعة

      if (!isPaymentVerifiedValue) {
        print('❌ العقار "${data['title']}" غير مدفوع. isPaymentVerified: $isPaymentVerified');
      } else {
        print('✅ العقار "${data['title']}" مدفوع. isPaymentVerified: $isPaymentVerified');
      }

      return isPaymentVerifiedValue;
    }).toList();

    print('📊 عدد العقارات المدفوعة: ${filtered.length}');

    // تطبيق فلتر الفئة
    if (_selectedCategory != 'الكل') {
      print('🏷️ تطبيق فلتر الفئة: $_selectedCategory');
      filtered = filtered.where((property) {
        final data = property.data() as Map<String, dynamic>;
        final category = '${data['subCategory'] ?? ''}';
        final matches = category == _selectedCategory;
        if (!matches) {
          print('❌ العقار "${data['title']}" لا يطابق الفئة. subCategory: "$category"');
        } else {
          print('✅ العقار "${data['title']}" يطابق الفئة. subCategory: "$category"');
        }
        return matches;
      }).toList();
      print('📊 عدد العقارات بعد فلتر الفئة: ${filtered.length}');
    }

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      print('🔍 تطبيق فلتر البحث: "$_searchQuery"');
      final searchTerm = _searchQuery.toLowerCase();
      filtered = filtered.where((property) {
        final data = property.data() as Map<String, dynamic>;
        final title = (data['title'] ?? '').toString().toLowerCase();
        final description = (data['description'] ?? '').toString().toLowerCase();
        final location = (data['location'] ?? '').toString().toLowerCase();
        final subCategory = (data['subCategory'] ?? '').toString().toLowerCase();

        final matches = title.contains(searchTerm) ||
               description.contains(searchTerm) ||
               location.contains(searchTerm) ||
               subCategory.contains(searchTerm);

        if (!matches) {
          print('❌ العقار "${data['title']}" لا يطابق البحث');
        } else {
          print('✅ العقار "${data['title']}" يطابق البحث');
        }
        return matches;
      }).toList();
      print('📊 عدد العقارات بعد فلتر البحث: ${filtered.length}');
    }

    print('✅ انتهاء الفلترة. العدد النهائي: ${filtered.length}');
    return filtered;
  }

  void _showPropertyDetails(Estate estate) {
    Navigator.pushNamed(
      context,
      '/estate-details',
      arguments: estate);
  }

  /// اختيار نوع زر المعاينة
  Widget _buildPreviewButton(Estate estate) {
    switch (_previewButtonType) {
      case 'animated':
        return _buildAnimatedPreviewButton(estate);
      case 'iconic':
        return _buildIconicPreviewButton(estate);
      case 'modern':
      default:
        return _buildModernPreviewButton(estate);
    }
  }

  /// زر المعاينة العصري
  Widget _buildModernPreviewButton(Estate estate) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.primary.withValues(alpha: 0.05),
          ],
        ),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _previewProperty(estate),
          borderRadius: BorderRadius.circular(12),
          splashColor: AppColors.primary.withValues(alpha: 0.1),
          highlightColor: AppColors.primary.withValues(alpha: 0.05),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.visibility_outlined,
                    size: 16,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'معاينة',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// زر المعاينة المتحرك (بديل)
  Widget _buildAnimatedPreviewButton(Estate estate) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _previewProperty(estate),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white,
                  AppColors.primary.withValues(alpha: 0.02),
                ],
              ),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.4),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primary.withValues(alpha: 0.1),
                  ),
                  child: Icon(
                    Icons.remove_red_eye,
                    size: 14,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  'معاينة',
                  style: GoogleFonts.cairo(
                    fontSize: 13,
                    fontWeight: FontWeight.w700,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// زر المعاينة الأيقوني (بديل مدمج)
  Widget _buildIconicPreviewButton(Estate estate) {
    return GestureDetector(
      onTap: () => _previewProperty(estate),
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primary.withValues(alpha: 0.15),
              AppColors.primary.withValues(alpha: 0.08),
            ],
          ),
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.15),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          Icons.visibility,
          size: 20,
          color: AppColors.primary,
        ),
      ),
    );
  }

  void _previewProperty(Estate estate) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(16),
        child: Container(
          constraints: const BoxConstraints(maxHeight: 600),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header مع صورة العقار
              _buildPreviewHeader(estate),
              // محتوى التفاصيل
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: _buildPreviewContent(estate),
                ),
              ),
              // الأزرار السفلية
              _buildPreviewActions(estate),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPreviewHeader(Estate estate) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // صورة العقار
          if (estate.images.isNotEmpty)
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              child: Image.network(
                estate.images.first,
                width: double.infinity,
                height: 200,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultImage(),
              ),
            )
          else
            _buildDefaultImage(),
          // تدرج للنص
          Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.7),
                ],
              ),
            ),
          ),
          // زر الإغلاق
          Positioned(
            top: 16,
            right: 16,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.close,
                  color: AppColors.textPrimary,
                  size: 20,
                ),
              ),
            ),
          ),
          // نوع العقار
          Positioned(
            top: 16,
            left: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                estate.subCategory ?? 'عقار للإيجار',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          // عنوان العقار
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  estate.title,
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: Colors.white.withValues(alpha: 0.9),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        estate.location,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultImage() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.8),
            AppColors.primary,
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.home_work,
            size: 60,
            color: Colors.white.withValues(alpha: 0.8),
          ),
          const SizedBox(height: 8),
          Text(
            'لا توجد صورة',
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewContent(Estate estate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // السعر
        _buildInfoCard(
          icon: Icons.payments,
          title: 'السعر',
          value: '${estate.price} د.ك',
          color: Colors.green,
        ),
        const SizedBox(height: 16),
        // المساحة
        if (estate.area != null) ...[
          _buildInfoCard(
            icon: Icons.square_foot,
            title: 'المساحة',
            value: '${estate.area} م²',
            color: Colors.blue,
          ),
          const SizedBox(height: 16),
        ],
        // نوع العقار
        _buildInfoCard(
          icon: Icons.category,
          title: 'نوع العقار',
          value: estate.subCategory ?? 'غير محدد',
          color: AppColors.primary,
        ),
        const SizedBox(height: 16),
        // الوصف
        if (estate.description.isNotEmpty) ...[
          Text(
            'الوصف',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.border),
            ),
            child: Text(
              estate.description,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
        // معلومات إضافية
        _buildAdditionalInfo(estate),
      ],
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo(Estate estate) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'معلومات إضافية',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInfoRow('التصنيف الرئيسي', estate.mainCategory ?? 'غير محدد'),
          _buildInfoRow('معرف العقار', estate.id),
          if (estate.images.isNotEmpty)
            _buildInfoRow('عدد الصور', '${estate.images.length} صورة'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewActions(Estate estate) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        border: Border(
          top: BorderSide(color: AppColors.border, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.close, size: 18),
              label: Text(
                'إغلاق',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.textSecondary,
                side: BorderSide(color: AppColors.border),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
                _copyProperty(estate);
              },
              icon: const Icon(Icons.copy, size: 18),
              label: Text(
                'نسخ العقار',
                style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _copyProperty(Estate estate) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'تأكيد النسخ'),
        content: const Text(
          'هل تريد نسخ هذا العقار؟ سيتم إنشاء إعلان مدفوع جديد.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performCopy(estate);
            },
            child: const Text('تأكيد النسخ')),
        ]));
  }

  void _performCopy(Estate estate) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        _showErrorMessage('يجب تسجيل الدخول أولاً');
        return;
      }

      // التحقق من عدم نسخ العقار مسبقاً
      final existingCopy = await FirebaseFirestore.instance
          .collection('estates')
          .where('originalEstateId', isEqualTo: estate.id)
          .where('ownerId', isEqualTo: currentUser.uid)
          .get();

      if (existingCopy.docs.isNotEmpty) {
        _showErrorMessage('لقد قمت بنسخ هذا العقار مسبقاً. لا يمكن نسخ نفس العقار أكثر من مرة.');
        return;
      }

      // إظهار مؤشر التحميل
      _showLoadingDialog();

      // إنشاء نسخة كاملة من العقار مع جميع البيانات
      final copiedEstate = {
        // البيانات الأساسية
        'title': '${estate.title} (نسخة)',
        'description': estate.description ?? '',
        'price': estate.price ?? 0,
        'location': estate.location ?? '',
        'area': estate.area,
        'mainCategory': estate.mainCategory ?? 'عقار للايجار',
        'subCategory': estate.subCategory ?? '',

        // الصور والملفات
        'images': List<String>.from(estate.images), // نسخ كاملة للصور
        'photoUrls': List<String>.from(estate.images), // للتوافق مع النظام القديم

        // تفاصيل العقار
        'rooms': estate.rooms ?? estate.numberOfRooms,
        'bathrooms': estate.bathrooms ?? estate.numberOfBathrooms,
        'buildingAge': estate.buildingAge,
        'floor': estate.floorNumber,
        'totalFloors': estate.numberOfFloors,

        // المرافق والخدمات
        'furnished': estate.isFullyFurnished ?? false,
        'parking': estate.hasGarage ?? false,
        'elevator': estate.hasElevator ?? false,
        'balcony': estate.hasBalcony ?? false,
        'garden': estate.hasGarden ?? false,
        'swimmingPool': estate.hasSwimmingPool ?? false,
        'gym': false, // لا يوجد في Estate
        'security': estate.hasSecurity ?? false,
        'airConditioning': estate.hasCentralAC,
        'heating': false, // لا يوجد في Estate
        'internet': false, // لا يوجد في Estate
        'satelliteDish': false, // لا يوجد في Estate
        'laundry': false, // لا يوجد في Estate
        'storage': false, // لا يوجد في Estate
        'maidRoom': estate.hasMaidRoom,
        'driverRoom': estate.hasDriverRoom ?? false,

        // معلومات الاتصال (من العقار الأصلي)
        'contactPhone': '', // لا يوجد في Estate
        'contactWhatsApp': '', // لا يوجد في Estate
        'contactEmail': estate.advertiserEmail ?? '',

        // معلومات إضافية
        'propertyType': estate.propertyType ?? '',
        'usageType': estate.usageType ?? '',
        'ownershipType': '', // لا يوجد في Estate
        'availableFrom': null, // لا يوجد في Estate
        'leaseDuration': null, // لا يوجد في Estate
        'deposit': null, // لا يوجد في Estate
        'commission': null, // لا يوجد في Estate

        // معلومات النسخ
        'ownerId': currentUser.uid,
        'originalEstateId': estate.id,
        'originalOwnerId': estate.ownerId,
        'originalTitle': estate.title,
        'originalPrice': estate.price,
        'originalLocation': estate.location,

        // حالة الإعلان المنسوخ
        'isCopied': true,
        'isPaid': false, // يتطلب دفع
        'isActive': false, // غير نشط حتى يتم الدفع
        'isVerified': false,
        'isFeatured': false,
        'isPinned': false,
        'isPaymentVerified': false,

        // إحصائيات
        'views': 0,
        'inquiries': 0,
        'likes': 0,
        'shares': 0,

        // تواريخ
        'createdAt': FieldValue.serverTimestamp(),
        'copiedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'lastModified': FieldValue.serverTimestamp(),

        // معلومات إضافية للنسخة
        'copyVersion': '1.0',
        'copySource': 'mobile_app',
        'copyReason': 'investor_copy',
      };

      final docRef = await FirebaseFirestore.instance
          .collection('estates')
          .add(copiedEstate);

      // إخفاء مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
        _showSuccessDialog(docRef.id);
      }
    } catch (e) {
      // إخفاء مؤشر التحميل
      if (mounted) {
        if (Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }
        _showErrorMessage('خطأ في نسخ العقار: ${e.toString()}');
      }
    }
  }

  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: AppColors.primary),
              const SizedBox(height: 16),
              Text(
                'جاري نسخ العقار...',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSuccessDialog(String copiedEstateId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            const SizedBox(width: 8),
            Text(
              'تم النسخ بنجاح',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Text(
          'تم نسخ العقار بنجاح. يرجى إكمال عملية الدفع لتفعيل الإعلان.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushNamed(context, '/my-properties');
            },
            child: Text(
              'عقاراتي',
              style: GoogleFonts.cairo(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToPayment(copiedEstateId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            child: Text(
              'دفع الآن',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.cairo(fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _navigateToPayment(String estateId) {
    Navigator.pushNamed(
      context,
      '/payment',
      arguments: {
        'estateId': estateId,
        'paymentType': 'copy_activation',
        'amount': 50.0, // سعر تفعيل النسخة
        'description': 'تفعيل إعلان منسوخ',
      },
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

/// رسام الأشكال الهندسية لعقارات الإيجار
class RentalPropertyShapesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary.withValues(alpha: 0.08)
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = AppColors.primary.withValues(alpha: 0.12)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // إنشاء أشكال متنوعة معبرة عن العقارات
    _drawRentalShapes(canvas, size, paint, strokePaint);
  }

  void _drawRentalShapes(Canvas canvas, Size size, Paint fillPaint, Paint strokePaint) {
    final random = math.Random(42); // seed ثابت للحصول على نفس النمط

    // رسم أشكال مختلفة
    for (int i = 0; i < 20; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final shapeType = random.nextInt(6);
      final scale = 0.4 + random.nextDouble() * 0.6; // حجم متغير

      canvas.save();
      canvas.translate(x, y);
      canvas.scale(scale);

      switch (shapeType) {
        case 0:
          _drawHouse(canvas, fillPaint, strokePaint);
          break;
        case 1:
          _drawBuilding(canvas, fillPaint, strokePaint);
          break;
        case 2:
          _drawKey(canvas, fillPaint, strokePaint);
          break;
        case 3:
          _drawDoor(canvas, strokePaint);
          break;
        case 4:
          _drawWindow(canvas, strokePaint);
          break;
        case 5:
          _drawRentSign(canvas, strokePaint);
          break;
      }

      canvas.restore();
    }
  }

  // رسم منزل
  void _drawHouse(Canvas canvas, Paint fillPaint, Paint strokePaint) {
    final path = Path();
    // قاعدة المنزل
    path.addRect(const Rect.fromLTWH(-15, -10, 30, 20));
    // سقف المنزل
    path.moveTo(-20, -10);
    path.lineTo(0, -25);
    path.lineTo(20, -10);
    path.close();

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, strokePaint);
  }

  // رسم مبنى
  void _drawBuilding(Canvas canvas, Paint fillPaint, Paint strokePaint) {
    final rect = const Rect.fromLTWH(-12, -20, 24, 40);
    canvas.drawRect(rect, fillPaint);
    canvas.drawRect(rect, strokePaint);

    // نوافذ
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 2; j++) {
        final windowRect = Rect.fromLTWH(-8 + j * 8, -15 + i * 10, 4, 4);
        canvas.drawRect(windowRect, strokePaint);
      }
    }
  }

  // رسم مفتاح
  void _drawKey(Canvas canvas, Paint fillPaint, Paint strokePaint) {
    final path = Path();
    // رأس المفتاح
    path.addOval(const Rect.fromLTWH(-8, -8, 16, 16));
    // جسم المفتاح
    path.addRect(const Rect.fromLTWH(8, -2, 15, 4));
    // أسنان المفتاح
    path.addRect(const Rect.fromLTWH(20, -4, 3, 2));
    path.addRect(const Rect.fromLTWH(20, 2, 3, 2));

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, strokePaint);
  }

  // رسم باب
  void _drawDoor(Canvas canvas, Paint strokePaint) {
    final rect = const Rect.fromLTWH(-8, -15, 16, 30);
    canvas.drawRect(rect, strokePaint);

    // مقبض الباب
    canvas.drawCircle(const Offset(5, 0), 2, strokePaint);
  }

  // رسم نافذة
  void _drawWindow(Canvas canvas, Paint strokePaint) {
    final rect = const Rect.fromLTWH(-10, -8, 20, 16);
    canvas.drawRect(rect, strokePaint);

    // إطار النافذة
    canvas.drawLine(const Offset(-10, 0), const Offset(10, 0), strokePaint);
    canvas.drawLine(const Offset(0, -8), const Offset(0, 8), strokePaint);
  }

  // رسم علامة للإيجار
  void _drawRentSign(Canvas canvas, Paint strokePaint) {
    final rect = const Rect.fromLTWH(-12, -8, 24, 16);
    canvas.drawRect(rect, strokePaint);

    // نص للإيجار (رمزي)
    final path = Path();
    path.moveTo(-8, -4);
    path.lineTo(-4, 0);
    path.lineTo(-8, 4);
    path.moveTo(0, -4);
    path.lineTo(0, 4);
    path.moveTo(4, -4);
    path.lineTo(8, 4);

    canvas.drawPath(path, strokePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
