import 'package:flutter/material.dart';
import 'dart:math' as math;

/// خلفية متحركة للمنتدى مع أشكال هندسية وعلامات ترقيم
class AnimatedForumBackground extends StatefulWidget {
  /// لون الخلفية الأساسي
  final Color primaryColor;

  /// لون الخلفية الثانوي
  final Color secondaryColor;

  const AnimatedForumBackground({
    super.key,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  State<AnimatedForumBackground> createState() => _AnimatedForumBackgroundState();
}

class _AnimatedForumBackgroundState extends State<AnimatedForumBackground>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;
  late List<FloatingElement> _elements;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = [];
    _animations = [];
    _elements = [];

    // إنشاء العناصر المتحركة
    final elementTypes = [
      ElementType.circle,
      ElementType.triangle,
      ElementType.square,
      ElementType.diamond,
      ElementType.exclamation,
      ElementType.question,
      ElementType.comma,
      ElementType.semicolon,
    ];

    for (int i = 0; i < 8; i++) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 6000 + (i * 500)),
        vsync: this);

      final animation = Tween<double>(
        begin: 0.0,
        end: 1.0).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut));

      final element = FloatingElement(
        type: elementTypes[i % elementTypes.length],
        startX: 0.1 + math.Random().nextDouble() * 0.8,
        startY: 0.1 + math.Random().nextDouble() * 0.8,
        endX: 0.1 + math.Random().nextDouble() * 0.8,
        endY: 0.1 + math.Random().nextDouble() * 0.8,
        size: 16 + math.Random().nextDouble() * 20,
        opacity: 0.1 + math.Random().nextDouble() * 0.2,
        rotationSpeed: (math.Random().nextDouble() - 0.5) * 0.5);

      _controllers.add(controller);
      _animations.add(animation);
      _elements.add(element);

      // تأخير بدء كل عنصر لإنشاء تأثير متدرج
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          controller.repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            widget.primaryColor,
            widget.secondaryColor,
          ])),
      child: Stack(
        children: [
          // تأثير التدرج الإضافي
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.topRight,
                radius: 1.5,
                colors: [
                  Colors.white.withOpacity(0.05),
                  Colors.transparent,
                ]))),

          // الأشكال الهندسية المتحركة
          ...List.generate(_elements.length, (index) {
            return AnimatedBuilder(
              animation: _animations[index],
              builder: (context, child) {
                final element = _elements[index];
                final progress = _animations[index].value;

                // حركة بسيطة للعناصر
                final x = element.startX + (element.endX - element.startX) * progress;
                final y = element.startY + (element.endY - element.startY) * progress;

                final screenWidth = MediaQuery.of(context).size.width;
                final containerHeight = 200.0; // ارتفاع الحاوية العليا

                // التأكد من أن العنصر داخل حدود الشاشة
                if (screenWidth <= 0 || containerHeight <= 0) {
                  return const SizedBox.shrink();
                }

                final leftPosition = (x * screenWidth - element.size / 2).clamp(0.0, screenWidth - element.size);
                final topPosition = (y * containerHeight - element.size / 2).clamp(0.0, containerHeight - element.size);

                return Positioned(
                  left: leftPosition,
                  top: topPosition,
                  child: Opacity(
                    opacity: element.opacity,
                    child: _buildElement(element)));
              });
          }),


        ]));
  }

  Widget _buildElement(FloatingElement element) {
    switch (element.type) {
      case ElementType.circle:
        return Container(
          width: element.size,
          height: element.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white.withValues(alpha: 0.3),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.5),
              width: 1)));

      case ElementType.triangle:
        return CustomPaint(
          size: Size(element.size, element.size),
          painter: TrianglePainter(
            color: Colors.white.withValues(alpha: 0.3),
            borderColor: Colors.white.withValues(alpha: 0.5)));

      case ElementType.square:
        return Container(
          width: element.size,
          height: element.size,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.3),
            border: Border.all(
              color: Colors.white.withOpacity(0.5),
              width: 1),
            borderRadius: BorderRadius.circular(4)));

      case ElementType.diamond:
        return Transform.rotate(
          angle: math.pi / 4,
          child: Container(
            width: element.size,
            height: element.size,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              border: Border.all(
                color: Colors.white.withOpacity(0.5),
                width: 1))));

      case ElementType.exclamation:
        return Text(
          '!',
          style: TextStyle(
            fontSize: element.size,
            color: Colors.white.withOpacity(0.4),
            fontWeight: FontWeight.bold));

      case ElementType.question:
        return Text(
          '؟',
          style: TextStyle(
            fontSize: element.size,
            color: Colors.white.withOpacity(0.4),
            fontWeight: FontWeight.bold));

      case ElementType.comma:
        return Text(
          '،',
          style: TextStyle(
            fontSize: element.size,
            color: Colors.white.withOpacity(0.4),
            fontWeight: FontWeight.bold));

      case ElementType.semicolon:
        return Text(
          '؛',
          style: TextStyle(
            fontSize: element.size,
            color: Colors.white.withOpacity(0.4),
            fontWeight: FontWeight.bold));
    }
  }
}

/// أنواع العناصر المتحركة
enum ElementType {
  circle,
  triangle,
  square,
  diamond,
  exclamation,
  question,
  comma,
  semicolon,
}

/// عنصر متحرك
class FloatingElement {
  final ElementType type;
  final double startX;
  final double startY;
  final double endX;
  final double endY;
  final double size;
  final double opacity;
  final double rotationSpeed;

  FloatingElement({
    required this.type,
    required this.startX,
    required this.startY,
    required this.endX,
    required this.endY,
    required this.size,
    required this.opacity,
    required this.rotationSpeed,
  });
}

/// رسام المثلث
class TrianglePainter extends CustomPainter {
  final Color color;
  final Color borderColor;

  TrianglePainter({
    required this.color,
    required this.borderColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
