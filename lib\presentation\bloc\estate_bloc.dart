import 'package:flutter_bloc/flutter_bloc.dart';

import '../../domain/entities/estate.dart';
import '../../domain/entities/estate_base.dart';
import '../../domain/entities/estate_converter.dart';
import '../../domain/usecases/copy_estate.dart';
import '../../domain/usecases/create_estate_new.dart';
import '../../domain/usecases/delete_estate.dart';
import '../../domain/usecases/get_all_estates.dart';
import '../../domain/usecases/get_paginated_estates.dart';
import '../../domain/usecases/update_estate.dart';
import 'estate_event.dart';
import 'estate_state.dart';

/// BLoC for managing estate operations such as fetching, creating,
/// updating, and deleting estates.
class EstateBloc extends Bloc<EstateEvent, EstateState> {
  final GetAllEstates getAllEstates;
  final GetPaginatedEstates getPaginatedEstates;
  final CreateEstateNew createEstate;
  final UpdateEstate updateEstate;
  final DeleteEstate deleteEstate;
  final CopyEstate copyEstate;

  // حفظ حالة التحميل المتدرج
  List<Estate> _currentEstates = [];
  String? _lastDocumentId;
  bool _hasMore = true;

  EstateBloc({
    required this.getAllEstates,
    required this.getPaginatedEstates,
    required this.createEstate,
    required this.updateEstate,
    required this.deleteEstate,
    required this.copyEstate,
  }) : super(EstateInitial()) {
    // Handler for fetching estates (legacy method).
    on<FetchEstates>((event, emit) async {
      emit(EstateLoading());
      try {
        final estates = await getAllEstates();
        emit(EstateLoaded(estates));
      } catch (e) {
        emit(EstateError("حدث خطأ في جلب البيانات"));
      }
    });

    // Handler for fetching estates with pagination.
    on<FetchPaginatedEstates>((event, emit) async {
      // إذا كان هذا تحميل جديد (refresh)، نعيد تعيين الحالة
      if (event.refresh) {
        _currentEstates = [];
        _lastDocumentId = null;
        _hasMore = true;
        emit(EstateLoading(isFirstLoad: true, isLoadingMore: false));
      } else if (_currentEstates.isEmpty) {
        // إذا كانت هذه هي المرة الأولى، نعرض حالة التحميل
        emit(EstateLoading(isFirstLoad: true, isLoadingMore: false));
      } else if (event.lastDocumentId != null) {
        // إذا كنا نحمل المزيد من البيانات، نعرض حالة تحميل المزيد
        emit(EstateLoading(isFirstLoad: false, isLoadingMore: true));
      }

      // إذا لم يكن هناك المزيد من البيانات، نعود
      if (!_hasMore && !event.refresh) {
        emit(PaginatedEstatesLoaded(
          estates: _currentEstates,
          lastDocumentId: _lastDocumentId,
          hasMore: _hasMore));
        return;
      }

      try {
        final result = await getPaginatedEstates(
          limit: event.limit,
          lastDocumentId: event.refresh ? null : event.lastDocumentId,
          page: event.page,
          pageSize: event.pageSize,
          sortBy: event.sortBy,
          sortAscending: event.sortAscending,
          filters: event.filters,
          searchQuery: event.searchQuery);

        // تحويل List<EstateBase> إلى List<Estate>
        final estateBaseList = result['estates'] as List<EstateBase>;
        final estates = <Estate>[];

        // تحويل كل عنصر من EstateBase إلى Estate
        for (final estateBase in estateBaseList) {
          final estate = EstateConverter.toLegacyEstate(estateBase);
          if (estate != null) {
            estates.add(estate);
          }
        }

        final lastDocumentId = result['lastDocumentId'] as String?;
        final hasMore = result['hasMore'] as bool;

        // تحديث الحالة
        if (event.refresh) {
          _currentEstates = estates;
        } else {
          _currentEstates = [..._currentEstates, ...estates];
        }
        _lastDocumentId = lastDocumentId;
        _hasMore = hasMore;

        emit(PaginatedEstatesLoaded(
          estates: _currentEstates,
          lastDocumentId: _lastDocumentId,
          hasMore: _hasMore));
      } catch (e) {
        emit(EstateError("حدث خطأ في جلب البيانات: ${e.toString()}"));
      }
    });

    // Handler for creating a new estate.
    on<CreateEstateEvent>((event, emit) async {
      try {
        await createEstate(event.estate, event.userId);
        // تحديث القائمة بعد الإنشاء
        add(FetchPaginatedEstates(refresh: true));
      } catch (e) {
        emit(EstateError("فشل في إنشاء العقار: ${e.toString()}"));
      }
    });

    // Handler for updating an existing estate.
    on<UpdateEstateEvent>((event, emit) async {
      try {
        await updateEstate(event.estate);
        // تحديث القائمة بعد التعديل
        add(FetchPaginatedEstates(refresh: true));
      } catch (e) {
        emit(EstateError("فشل في تحديث العقار: ${e.toString()}"));
      }
    });

    // Handler for deleting an estate.
    on<DeleteEstateEvent>((event, emit) async {
      try {
        await deleteEstate(event.id);
        // تحديث القائمة بعد الحذف
        add(FetchPaginatedEstates(refresh: true));
      } catch (e) {
        emit(EstateError("فشل في حذف العقار: ${e.toString()}"));
      }
    });

    // Handler for copying an estate.
    on<CopyEstateEvent>((event, emit) async {
      try {
        await copyEstate(event.originalEstateId, event.investorId);
        // Refresh the estate list after copying.
        add(FetchEstates());
      } catch (e) {
        emit(EstateError("فشل في نسخ العقار: ${e.toString()}"));
      }
    });

    // Handler for loading cached estates data.
    on<LoadCachedEstatesEvent>((event, emit) async {
      try {
        // تحديث الحالة الحالية مع البيانات المخزنة
        _currentEstates = event.estates;

        emit(PaginatedEstatesLoaded(
          estates: _currentEstates,
          lastDocumentId: _lastDocumentId,
          hasMore: _hasMore));
      } catch (e) {
        emit(EstateError("فشل في تحميل البيانات المخزنة: ${e.toString()}"));
      }
    });
  }
}
