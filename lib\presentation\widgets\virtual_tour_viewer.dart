import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../core/theme/app_colors.dart';
import '../../domain/entities/virtual_tour.dart';
import '../../data/repositories_impl/virtual_tour_repository_impl.dart';

/// ويدجت لعرض الجولات الافتراضية
class VirtualTourViewer extends StatefulWidget {
  final String estateId;
  final bool showHeader;
  final double? height;

  const VirtualTourViewer({
    super.key,
    required this.estateId,
    this.showHeader = true,
    this.height,
  });

  @override
  State<VirtualTourViewer> createState() => _VirtualTourViewerState();
}

class _VirtualTourViewerState extends State<VirtualTourViewer> {
  final VirtualTourRepositoryImpl _repository = VirtualTourRepositoryImpl();
  List<VirtualTour> _tours = [];
  bool _isLoading = true;
  int _currentTourIndex = 0;
  late WebViewController _webViewController;

  @override
  void initState() {
    super.initState();
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted);
    _loadVirtualTours();
  }

  Future<void> _loadVirtualTours() async {
    try {
      final tours = await _repository.getEstateVirtualTours(widget.estateId);
      setState(() {
        _tours = tours;
        _isLoading = false;
      });
      
      if (_tours.isNotEmpty) {
        _loadTour(_tours[0]);
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الجولات الافتراضية: $e')),
        );
      }
    }
  }

  void _loadTour(VirtualTour tour) {
    // زيادة عدد المشاهدات
    _repository.incrementVirtualTourViews(tour.id);
    
    // تحميل الجولة في WebView
    _webViewController.loadRequest(Uri.parse(tour.tourUrl));
  }

  void _switchTour(int index) {
    if (index >= 0 && index < _tours.length) {
      setState(() {
        _currentTourIndex = index;
      });
      _loadTour(_tours[index]);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return SizedBox(
        height: widget.height ?? 300,
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_tours.isEmpty) {
      return Container(
        height: widget.height ?? 300,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.threed_rotation,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 8),
              Text(
                'لا توجد جولات افتراضية متاحة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      height: widget.height ?? 400,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // رأس الجولة
          if (widget.showHeader) _buildTourHeader(),
          
          // عارض الجولة
          Expanded(child: _buildTourViewer()),
          
          // قائمة الجولات (إذا كان هناك أكثر من جولة)
          if (_tours.length > 1) _buildTourSelector(),
        ],
      ),
    );
  }

  Widget _buildTourHeader() {
    final currentTour = _tours[_currentTourIndex];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getTourIcon(currentTour.tourType),
            color: AppColors.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  currentTour.title,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                if (currentTour.description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    currentTour.description!,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              currentTour.getTourTypeName(),
              style: GoogleFonts.cairo(
                fontSize: 10,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTourViewer() {
    return ClipRRect(
      borderRadius: widget.showHeader 
          ? const BorderRadius.only(
              bottomLeft: Radius.circular(12),
              bottomRight: Radius.circular(12),
            )
          : BorderRadius.circular(12),
      child: WebViewWidget(
        controller: _webViewController,
      ),
    );
  }

  Widget _buildTourSelector() {
    return Container(
      height: 80,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _tours.length,
        itemBuilder: (context, index) {
          final tour = _tours[index];
          final isSelected = index == _currentTourIndex;
          
          return GestureDetector(
            onTap: () => _switchTour(index),
            child: Container(
              width: 120,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primary : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? AppColors.primary : Colors.grey.shade300,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _getTourIcon(tour.tourType),
                    color: isSelected ? Colors.white : AppColors.primary,
                    size: 20,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    tour.title,
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      color: isSelected ? Colors.white : Colors.black87,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  IconData _getTourIcon(String tourType) {
    switch (tourType) {
      case '360':
        return Icons.threesixty;
      case 'video':
        return Icons.play_circle_filled;
      case 'interactive':
        return Icons.touch_app;
      default:
        return Icons.view_in_ar;
    }
  }
}

/// ويدجت مبسط لعرض معاينة الجولة الافتراضية
class VirtualTourPreview extends StatelessWidget {
  final VirtualTour tour;
  final VoidCallback? onTap;

  const VirtualTourPreview({
    super.key,
    required this.tour,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 120,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Stack(
          children: [
            // صورة مصغرة أو خلفية افتراضية
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: tour.thumbnailUrl != null
                  ? Image.network(
                      tour.thumbnailUrl!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildDefaultBackground();
                      },
                    )
                  : _buildDefaultBackground(),
            ),
            
            // طبقة شفافة مع المعلومات
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
            ),
            
            // أيقونة التشغيل
            Center(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getTourIcon(tour.tourType),
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
            
            // معلومات الجولة
            Positioned(
              bottom: 8,
              left: 8,
              right: 8,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tour.title,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          tour.getTourTypeName(),
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const Spacer(),
                      if (tour.viewsCount > 0) ...[
                        Icon(
                          Icons.visibility,
                          size: 12,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${tour.viewsCount}',
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultBackground() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.primary.withValues(alpha: 0.3),
          ],
        ),
      ),
    );
  }

  IconData _getTourIcon(String tourType) {
    switch (tourType) {
      case '360':
        return Icons.threesixty;
      case 'video':
        return Icons.play_circle_filled;
      case 'interactive':
        return Icons.touch_app;
      default:
        return Icons.view_in_ar;
    }
  }
}
