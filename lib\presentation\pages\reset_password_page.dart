import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';

import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// A page that allows users to reset their password by entering their email address.
/// On submission, a password reset link is sent to the provided email.
class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key});

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage>
    with SingleTickerProviderStateMixin {
  // Key for form validation and saving form state.
  final _formKey = GlobalKey<FormState>();

  // متغيرات للتحكم بالرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Email input from the user.
  String email = '';
  // Flag to control display of the loading indicator.
  bool isLoading = false;
  // حالة إرسال البريد الإلكتروني
  bool _emailSent = false;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutQuad));

    // بدء الرسوم المتحركة
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Validates the form and dispatches a PasswordResetRequested event.
  void _resetPassword() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      // Dispatch the event to request a password reset.
      context.read<AuthBloc>().add(
            PasswordResetRequested(email: email));
    }
  }

  /// عرض رسالة تأكيد إرسال البريد الإلكتروني
  Widget _buildEmailSentConfirmation() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(20),
                blurRadius: 15,
                offset: const Offset(0, 5)),
            ]),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.success.withValues(alpha: 0.1)),
                child: const Icon(
                  Icons.mark_email_read,
                  size: 40,
                  color: AppColors.success)),
              const SizedBox(height: 24),
              const Text(
                "تم إرسال رابط إعادة تعيين كلمة المرور",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary),
                textAlign: TextAlign.center),
              const SizedBox(height: 16),
              Text(
                "تم إرسال رابط إعادة تعيين كلمة المرور إلى البريد الإلكتروني: $email",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade700),
                textAlign: TextAlign.center),
              const SizedBox(height: 8),
              const Text(
                "يرجى التحقق من بريدك الإلكتروني واتباع التعليمات لإعادة تعيين كلمة المرور",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey),
                textAlign: TextAlign.center),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12))),
                child: const Text(
                  "العودة إلى تسجيل الدخول",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold))),
            ]))));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("استعادة كلمة المرور"),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.primary),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Colors.grey.shade50,
              Colors.grey.shade100,
            ])),
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: BlocListener<AuthBloc, AuthState>(
              listener: (context, state) {
                if (state is AuthLoading) {
                  setState(() {
                    isLoading = true;
                  });
                } else {
                  setState(() {
                    isLoading = false;
                  });
                }

                if (state is PasswordResetEmailSent) {
                  setState(() {
                    _emailSent = true;

                    // إعادة تشغيل الرسوم المتحركة
                    _animationController.reset();
                    _animationController.forward();
                  });

                  // عرض رسالة نجاح
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                          "تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني"),
                      backgroundColor: AppColors.success));
                } else if (state is PasswordResetFailed) {
                  // عرض رسالة الخطأ
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: AppColors.error));
                }
              },
              child: _emailSent
                  ? _buildEmailSentConfirmation()
                  : FadeTransition(
                      opacity: _fadeAnimation,
                      child: SlideTransition(
                        position: _slideAnimation,
                        child: Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(20),
                                blurRadius: 15,
                                offset: const Offset(0, 5)),
                            ]),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Container(
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: AppColors.primary.withValues(alpha: 0.1)),
                                  child: const Icon(
                                    Icons.lock_reset,
                                    size: 50,
                                    color: AppColors.primary)),
                                const SizedBox(height: 24),
                                const Text(
                                  "نسيت كلمة المرور؟",
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primary),
                                  textAlign: TextAlign.center),
                                const SizedBox(height: 12),
                                const Text(
                                  "أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور",
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                    height: 1.4),
                                  textAlign: TextAlign.center),
                                const SizedBox(height: 32),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16.0),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.05),
                                        blurRadius: 10,
                                        offset: const Offset(0, 4)),
                                    ]),
                                  child: TextFormField(
                                    decoration: InputDecoration(
                                      labelText: "البريد الإلكتروني",
                                      hintText: "أدخل بريدك الإلكتروني",
                                      prefixIcon: const Icon(Icons.email,
                                          color: AppColors.primary),
                                      filled: true,
                                      fillColor: Colors.white,
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: BorderSide.none),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade200)),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: const BorderSide(
                                            color: AppColors.primary, width: 2))),
                                    keyboardType: TextInputType.emailAddress,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return "الرجاء إدخال بريد إلكتروني";
                                      }
                                      // التحقق من صحة تنسيق البريد الإلكتروني
                                      final emailRegex = RegExp(
                                          r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                                      if (!emailRegex.hasMatch(value)) {
                                        return "الرجاء إدخال بريد إلكتروني صحيح";
                                      }
                                      return null;
                                    },
                                    onSaved: (value) => email = value!.trim())),
                                const SizedBox(height: 32),
                                Container(
                                  width: double.infinity,
                                  height: 55,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color:
                                            AppColors.primary.withValues(alpha: 0.3),
                                        blurRadius: 10,
                                        offset: const Offset(0, 4)),
                                    ]),
                                  child: ElevatedButton(
                                    onPressed:
                                        isLoading ? null : _resetPassword,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.primary,
                                      foregroundColor: Colors.white,
                                      elevation: 0,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(16)),
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12)),
                                    child: isLoading
                                        ? const SizedBox(
                                            width: 24,
                                            height: 24,
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                              strokeWidth: 2))
                                        : Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: const [
                                              Icon(Icons.send_outlined,
                                                  size: 20),
                                              SizedBox(width: 8),
                                              Text(
                                                "إرسال رابط إعادة التعيين",
                                                style: TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold)),
                                            ]))),
                                const SizedBox(height: 24),
                                TextButton.icon(
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                  icon: const Icon(Icons.arrow_back, size: 18),
                                  label: const Text(
                                    "العودة إلى تسجيل الدخول",
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500)),
                                  style: TextButton.styleFrom(
                                    foregroundColor: AppColors.primary)),
                              ]))))))))));
  }
}
