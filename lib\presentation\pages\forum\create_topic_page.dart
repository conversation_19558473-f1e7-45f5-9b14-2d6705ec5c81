import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/category_model.dart';
import '../../../domain/models/forum/topic_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';
import '../../widgets/common/loading_indicator.dart';

/// صفحة إنشاء موضوع جديد
class CreateTopicPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/create-topic';

  /// معرف الموضوع للتعديل (اختياري)
  final String? topicId;

  const CreateTopicPage({super.key, this.topicId});

  @override
  State<CreateTopicPage> createState() => _CreateTopicPageState();
}

class _CreateTopicPageState extends State<CreateTopicPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _tagsController = TextEditingController();

  String? _selectedCategoryId;
  final List<File> _selectedImages = [];
  List<String> _existingImages = [];
  final List<String> _imagesToDelete = [];

  bool _isLoading = false;
  bool _isEditing = false;
  TopicModel? _existingTopic;

  @override
  void initState() {
    super.initState();

    _isEditing = widget.topicId != null;

    // جلب البيانات عند تحميل الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    // جلب الفئات
    await forumProvider.fetchCategories();

    // إذا كان تعديل موضوع موجود
    if (_isEditing && widget.topicId != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        // جلب تفاصيل الموضوع
        await forumProvider.fetchTopic(widget.topicId!);

        final topic = forumProvider.currentTopic;
        if (topic != null) {
          _existingTopic = topic;

          // تعبئة البيانات
          _titleController.text = topic.title;
          _contentController.text = topic.content;
          _selectedCategoryId = topic.categoryId;

          if (topic.tags != null && topic.tags!.isNotEmpty) {
            _tagsController.text = topic.tags!.join(', ');
          }

          if (topic.images != null && topic.images!.isNotEmpty) {
            _existingImages = List.from(topic.images!);
          }
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحميل بيانات الموضوع')));
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();

    try {
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء اختيار الصورة')));
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    final ImagePicker picker = ImagePicker();

    try {
      final XFile? image = await picker.pickImage(source: ImageSource.camera);

      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء التقاط الصورة')));
    }
  }

  /// حذف صورة مختارة
  void _removeSelectedImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// حذف صورة موجودة
  void _removeExistingImage(int index) {
    setState(() {
      final imageUrl = _existingImages[index];
      _imagesToDelete.add(imageUrl);
      _existingImages.removeAt(index);
    });
  }

  /// حفظ الموضوع
  Future<void> _saveTopic() async {
    if (!_formKey.currentState!.validate()) return;

    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يجب تسجيل الدخول لإنشاء موضوع')));
      return;
    }

    if (_selectedCategoryId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يرجى اختيار فئة للموضوع')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // تحويل الوسوم إلى قائمة
      List<String>? tags;
      if (_tagsController.text.isNotEmpty) {
        tags = _tagsController.text
            .split(',')
            .map((tag) => tag.trim())
            .where((tag) => tag.isNotEmpty)
            .toList();
      }

      // الحصول على اسم الفئة
      final category = forumProvider.categories.firstWhere(
        (category) => category.id == _selectedCategoryId,
        orElse: () => CategoryModel(
          id: _selectedCategoryId!,
          name: 'فئة',
          description: '',
          order: 0,
          color: '#000000',
          icon: 'category',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now()));

      if (_isEditing && _existingTopic != null) {
        // تحديث موضوع موجود
        final updatedTopic = TopicModel(
          id: _existingTopic!.id,
          categoryId: _selectedCategoryId!,
          categoryName: category.name,
          userId: _existingTopic!.userId,
          userName: _existingTopic!.userName,
          userImage: _existingTopic!.userImage,
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          tags: tags,
          images: _existingImages,
          viewsCount: _existingTopic!.viewsCount,
          likesCount: _existingTopic!.likesCount,
          repliesCount: _existingTopic!.repliesCount,
          sharesCount: _existingTopic!.sharesCount,
          bookmarksCount: _existingTopic!.bookmarksCount,
          status: _existingTopic!.status,
          isSolved: _existingTopic!.isSolved,
          createdAt: _existingTopic!.createdAt,
          updatedAt: DateTime.now());

        final success = await forumProvider.updateTopic(
          updatedTopic,
          newImages: _selectedImages,
          imagesToDelete: _imagesToDelete);

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم تحديث الموضوع بنجاح')));

          Navigator.pop(context);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ أثناء تحديث الموضوع')));
        }
      } else {
        // إنشاء موضوع جديد
        final newTopic = TopicModel(
          id: '',
          categoryId: _selectedCategoryId!,
          categoryName: category.name,
          userId: authProvider.user!.uid,
          userName: authProvider.user!.displayName ?? 'مستخدم',
          userImage: authProvider.user!.photoURL,
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          tags: tags,
          status: TopicStatus.open,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now());

        final createdTopic = await forumProvider.createTopic(
          newTopic,
          images: _selectedImages);

        if (createdTopic != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم إنشاء الموضوع بنجاح')));

          Navigator.pop(context);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ أثناء إنشاء الموضوع')));
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: $e')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل موضوع' : 'موضوع جديد')),
      body: _isLoading
          ? Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildCategoryDropdown(),
                    SizedBox(height: 16),
                    _buildTitleField(),
                    SizedBox(height: 16),
                    _buildContentField(),
                    SizedBox(height: 16),
                    _buildTagsField(),
                    SizedBox(height: 16),
                    _buildImagesSection(),
                    SizedBox(height: 32),
                    _buildSubmitButton(),
                  ]))));
  }

  /// بناء قائمة الفئات
  Widget _buildCategoryDropdown() {
    return Consumer<ForumProvider>(
      builder: (context, forumProvider, child) {
        if (forumProvider.categoriesState == LoadingState.loading) {
          return Center(child: LoadingIndicator());
        }

        final categories = forumProvider.categories;

        return DropdownButtonFormField<String>(
          decoration: InputDecoration(
            labelText: 'الفئة',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.category)),
          value: _selectedCategoryId,
          items: categories.map((category) {
            return DropdownMenuItem<String>(
              value: category.id,
              child: Text(category.name));
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategoryId = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'يرجى اختيار فئة';
            }
            return null;
          });
      });
  }

  /// بناء حقل العنوان
  Widget _buildTitleField() {
    return TextFormField(
      controller: _titleController,
      decoration: InputDecoration(
        labelText: 'العنوان',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.title)),
      maxLength: 100,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال عنوان للموضوع';
        }
        if (value.trim().length < 5) {
          return 'العنوان قصير جداً';
        }
        return null;
      });
  }

  /// بناء حقل المحتوى
  Widget _buildContentField() {
    return TextFormField(
      controller: _contentController,
      decoration: InputDecoration(
        labelText: 'المحتوى',
        border: OutlineInputBorder(),
        alignLabelWithHint: true),
      maxLines: 10,
      maxLength: 2000,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال محتوى للموضوع';
        }
        if (value.trim().length < 10) {
          return 'المحتوى قصير جداً';
        }
        return null;
      });
  }

  /// بناء حقل الوسوم
  Widget _buildTagsField() {
    return TextFormField(
      controller: _tagsController,
      decoration: InputDecoration(
        labelText: 'الوسوم (مفصولة بفواصل)',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.tag),
        hintText: 'مثال: عقارات, استثمار, شقق'));
  }

  /// بناء قسم الصور
  Widget _buildImagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الصور',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton.icon(
              icon: Icon(Icons.photo_library),
              label: Text('اختيار من المعرض'),
              onPressed: _pickImage,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary)),
            SizedBox(width: 8),
            ElevatedButton.icon(
              icon: Icon(Icons.camera_alt),
              label: Text('التقاط صورة'),
              onPressed: _takePhoto,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary)),
          ]),
        SizedBox(height: 16),
        if (_existingImages.isNotEmpty) ...[
          Text(
            'الصور الحالية',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _existingImages.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          _existingImages[index],
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover)),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: () => _removeExistingImage(index),
                          child: Container(
                            padding: EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle),
                            child: Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16)))),
                    ]));
              })),
          SizedBox(height: 16),
        ],
        if (_selectedImages.isNotEmpty) ...[
          Text(
            'الصور المختارة',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _selectedImages[index],
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover)),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: () => _removeSelectedImage(index),
                          child: Container(
                            padding: EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle),
                            child: Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16)))),
                    ]));
              })),
        ],
      ]);
  }

  /// بناء زر الإرسال
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveTopic,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8))),
        child: _isLoading
            ? LoadingIndicator()
            : Text(
                _isEditing ? 'تحديث الموضوع' : 'نشر الموضوع',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold))));
  }
}
