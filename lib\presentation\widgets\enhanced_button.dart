// lib/presentation/widgets/enhanced_button.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// زر محسن مع تأثيرات حركية وتصميم عصري
class EnhancedButton extends StatefulWidget {
  /// نص الزر
  final String text;

  /// دالة يتم استدعاؤها عند الضغط على الزر
  final VoidCallback? onPressed;

  /// أيقونة الزر (اختياري)
  final IconData? icon;

  /// ما إذا كانت الأيقونة في بداية الزر
  final bool iconLeading;

  /// لون الزر الأساسي
  final Color? primaryColor;

  /// لون الزر الثانوي (للتدرج)
  final Color? secondaryColor;

  /// لون النص
  final Color? textColor;

  /// عرض الزر (null للعرض التلقائي)
  final double? width;

  /// ارتفاع الزر
  final double height;

  /// نصف قطر الحواف
  final double borderRadius;

  /// حجم النص
  final double fontSize;

  /// سماكة الخط
  final FontWeight fontWeight;

  /// ما إذا كان الزر في حالة تحميل
  final bool isLoading;

  /// نص التحميل
  final String loadingText;

  /// ما إذا كان الزر يستخدم تأثير النبض
  final bool usePulseEffect;

  /// ما إذا كان الزر يستخدم تأثير الظل
  final bool useShadow;

  const EnhancedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.iconLeading = true,
    this.primaryColor,
    this.secondaryColor,
    this.textColor,
    this.width,
    this.height = 56.0,
    this.borderRadius = 28.0,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.bold,
    this.isLoading = false,
    this.loadingText = "جاري التحميل...",
    this.usePulseEffect = true,
    this.useShadow = true,
  });

  @override
  State<EnhancedButton> createState() => _EnhancedButtonState();
}

class _EnhancedButtonState extends State<EnhancedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان
    final primaryColor = widget.primaryColor ?? Theme.of(context).primaryColor;
    final secondaryColor =
        widget.secondaryColor ?? primaryColor.withValues(alpha: 0.8);
    final textColor = widget.textColor ?? Colors.white;

    // تحديد ما إذا كان الزر معطل
    final isDisabled = widget.onPressed == null || widget.isLoading;

    return MouseRegion(
      cursor:
          isDisabled ? SystemMouseCursors.forbidden : SystemMouseCursors.click,
      child: GestureDetector(
        onTapDown: isDisabled ? null : (_) => _animationController.forward(),
        onTapUp: isDisabled ? null : (_) => _animationController.reverse(),
        onTapCancel: isDisabled ? null : () => _animationController.reverse(),
        onTap: isDisabled ? null : widget.onPressed,
        child: AnimatedScale(
          scale: widget.usePulseEffect && !isDisabled
              ? _scaleAnimation.value
              : 1.0,
          duration: const Duration(milliseconds: 150),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDisabled
                    ? [Colors.grey.shade400, Colors.grey.shade300]
                    : [primaryColor, secondaryColor],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight),
              borderRadius: BorderRadius.circular(widget.borderRadius),
              boxShadow: widget.useShadow && !isDisabled
                  ? [
                      BoxShadow(
                        color: primaryColor.withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4)),
                    ]
                  : null),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                onTap: isDisabled ? null : widget.onPressed,
                splashColor: Colors.white.withValues(alpha: 0.1),
                highlightColor: Colors.transparent,
                child: Center(
                  child: widget.isLoading
                      ? _buildLoadingContent(textColor)
                      : _buildButtonContent(textColor))))))));
  }

  /// بناء محتوى الزر العادي
  Widget _buildButtonContent(Color textColor) {
    if (widget.icon == null) {
      return Text(
        widget.text,
        style: GoogleFonts.cairo(
          color: textColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight));
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: widget.iconLeading
          ? [
              Icon(widget.icon, color: textColor, size: widget.fontSize + 4),
              SizedBox(width: widget.fontSize / 2),
              Text(
                widget.text,
                style: GoogleFonts.cairo(
                  color: textColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight)),
            ]
          : [
              Text(
                widget.text,
                style: GoogleFonts.cairo(
                  color: textColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight)),
              SizedBox(width: widget.fontSize / 2),
              Icon(widget.icon, color: textColor, size: widget.fontSize + 4),
            ]);
  }

  /// بناء محتوى الزر أثناء التحميل
  Widget _buildLoadingContent(Color textColor) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(textColor))),
        const SizedBox(width: 12),
        Text(
          widget.loadingText,
          style: GoogleFonts.cairo(
            color: textColor,
            fontSize: widget.fontSize,
            fontWeight: widget.fontWeight)),
      ]);
  }
}
