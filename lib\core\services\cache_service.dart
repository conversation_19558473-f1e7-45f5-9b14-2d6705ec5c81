import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Servicio para almacenar y recuperar datos en caché.
class CacheService {
  static const String _estatesKey = 'cached_estates';
  static const String _lastCacheTimeKey = 'last_cache_time';
  static const Duration _cacheValidity = Duration(hours: 1);

  /// Guardar datos en caché.
  Future<void> cacheData(String key, dynamic data) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonData = json.encode(data);
    await prefs.setString(key, jsonData);
    await prefs.setInt('${key}_time', DateTime.now().millisecondsSinceEpoch);
  }

  /// Recuperar datos de la caché.
  Future<dynamic> getCachedData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonData = prefs.getString(key);
    if (jsonData == null) return null;
    
    return json.decode(jsonData);
  }

  /// Verificar si los datos en caché son válidos (no han expirado).
  Future<bool> isCacheValid(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final cacheTime = prefs.getInt('${key}_time');
    if (cacheTime == null) return false;
    
    final cachedAt = DateTime.fromMillisecondsSinceEpoch(cacheTime);
    final now = DateTime.now();
    return now.difference(cachedAt) < _cacheValidity;
  }

  /// Guardar lista de estates en caché.
  Future<void> cacheEstates(List<Map<String, dynamic>> estates) async {
    await cacheData(_estatesKey, estates);
  }

  /// Recuperar lista de estates de la caché.
  Future<List<Map<String, dynamic>>?> getCachedEstates() async {
    final data = await getCachedData(_estatesKey);
    if (data == null) return null;
    
    return List<Map<String, dynamic>>.from(data);
  }

  /// Verificar si la caché de estates es válida.
  Future<bool> isEstatesCacheValid() async {
    return await isCacheValid(_estatesKey);
  }

  /// Limpiar todos los datos en caché.
  Future<void> clearCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
}
