# دليل إنشاء إصدار الاختبار المغلق - تطبيق Krea

## 📋 نظرة عامة

هذا الدليل يوضح خطوات إنشاء إصدار اختبار مغلق لتطبيق Krea ورفعه على Google Play Console.

---

## 🔧 الخطوات المطلوبة

### 1. إعداد مفتاح التوقيع الرقمي

```bash
# تشغيل سكريبت إنشاء المفتاح
scripts\create_keystore.bat
```

**المعلومات المطلوبة:**
- كلمة مرور keystore (احتفظ بها في مكان آمن)
- كلمة مرور المفتاح (احتفظ بها في مكان آمن)
- اسم المطور
- اسم المؤسسة

### 2. تحديث إعدادات التوقيع في build.gradle

بعد إنشاء المفتاح، قم بتحديث ملف `android/app/build.gradle`:

```gradle
// إلغاء التعليق عن هذه الأسطر في signingConfigs.release
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

signingConfigs {
    release {
        keyAlias keystoreProperties['keyAlias']
        keyPassword keystoreProperties['keyPassword']
        storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
        storePassword keystoreProperties['storePassword']
    }
}

// تحديث buildTypes.release
buildTypes {
    release {
        signingConfig signingConfigs.release
        // باقي الإعدادات...
    }
}
```

### 3. بناء التطبيق

```bash
# تشغيل سكريبت البناء
scripts\build_release.bat
```

اختر الخيار المناسب:
- **APK**: للاختبار المحلي
- **App Bundle (AAB)**: للنشر على Google Play (مُفضل)

### 4. التحقق من البناء

تأكد من وجود الملفات في:
- APK: `build/app/outputs/flutter-apk/`
- AAB: `build/app/outputs/bundle/release/app-release.aab`

---

## 🚀 رفع الإصدار على Google Play Console

### 1. تسجيل الدخول إلى Google Play Console
- اذهب إلى [Google Play Console](https://play.google.com/console)
- سجل الدخول بحساب المطور

### 2. إنشاء تطبيق جديد أو تحديث موجود
- اختر "إنشاء تطبيق" إذا كان جديد
- أو اختر التطبيق الموجود للتحديث

### 3. رفع App Bundle
1. اذهب إلى **الإصدارات** > **الاختبار المغلق**
2. اختر **إنشاء إصدار جديد**
3. ارفع ملف `app-release.aab`
4. أضف ملاحظات الإصدار

### 4. إعداد الاختبار المغلق
1. أضف قائمة المختبرين (عناوين البريد الإلكتروني)
2. حدد البلدان المتاحة
3. اختر إعدادات التوزيع

### 5. مراجعة وإرسال
1. راجع جميع المعلومات
2. اضغط **مراجعة الإصدار**
3. اضغط **بدء طرح الإصدار للاختبار المغلق**

---

## ✅ قائمة التحقق قبل النشر

- [ ] تم تحديث رقم الإصدار (versionCode & versionName)
- [ ] تم اختبار التطبيق على أجهزة مختلفة
- [ ] تم التحقق من عمل جميع الميزات الأساسية
- [ ] تم إعداد مفتاح التوقيع الرقمي
- [ ] تم بناء App Bundle بنجاح
- [ ] تم إعداد قائمة المختبرين
- [ ] تم كتابة ملاحظات الإصدار

---

## 🔒 أمان المفاتيح

**تحذيرات مهمة:**
- احتفظ بنسخة آمنة من ملف `upload-keystore.jks`
- احتفظ بكلمات المرور في مكان آمن (مدير كلمات المرور)
- لا تشارك هذه الملفات مع أحد
- لا تضع ملفات المفاتيح في نظام التحكم بالإصدارات

---

## 📞 الدعم

في حالة مواجهة مشاكل:
1. تحقق من سجلات البناء
2. تأكد من تحديث Flutter SDK
3. راجع وثائق Google Play Console
4. اتصل بفريق التطوير
