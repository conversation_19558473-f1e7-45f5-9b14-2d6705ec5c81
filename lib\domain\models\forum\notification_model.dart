import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نوع الإشعار
enum NotificationType {
  /// رد على موضوع
  topicReply,

  /// رد على مشاركة
  postReply,

  /// إعجاب بموضوع
  topicLike,

  /// إعجاب بمشاركة
  postLike,

  /// تفاعل مع موضوع
  topicReaction,

  /// تفاعل مع مشاركة
  postReaction,

  /// تثبيت موضوع
  topicPin,

  /// تمييز موضوع
  topicFeature,

  /// حل موضوع
  topicSolved,

  /// أفضل إجابة
  bestAnswer,

  /// إشارة إلى مستخدم
  mention,

  /// متابعة
  follow,

  /// إشعار نظام
  system,
}

/// نموذج إشعار المنتدى
class NotificationModel extends Equatable {
  /// معرف الإشعار
  final String id;

  /// نوع الإشعار
  final NotificationType type;

  /// معرف المستلم
  final String recipientId;

  /// معرف المرسل
  final String? senderId;

  /// اسم المرسل
  final String? senderName;

  /// صورة المرسل
  final String? senderImage;

  /// عنوان الإشعار
  final String title;

  /// محتوى الإشعار
  final String body;

  /// معرف العنصر المرتبط (موضوع أو مشاركة)
  final String? itemId;

  /// نوع العنصر المرتبط
  final String? itemType;

  /// عنوان العنصر المرتبط
  final String? itemTitle;

  /// بيانات إضافية
  final Map<String, dynamic>? data;

  /// ما إذا كان الإشعار مقروءاً
  final bool isRead;

  /// تاريخ قراءة الإشعار
  final DateTime? readAt;

  /// تاريخ إنشاء الإشعار
  final DateTime createdAt;

  const NotificationModel({
    required this.id,
    required this.type,
    required this.recipientId,
    this.senderId,
    this.senderName,
    this.senderImage,
    required this.title,
    required this.body,
    this.itemId,
    this.itemType,
    this.itemTitle,
    this.data,
    this.isRead = false,
    this.readAt,
    required this.createdAt,
  });

  /// إنشاء نسخة معدلة من الإشعار
  NotificationModel copyWith({
    String? id,
    NotificationType? type,
    String? recipientId,
    String? senderId,
    String? senderName,
    String? senderImage,
    String? title,
    String? body,
    String? itemId,
    String? itemType,
    String? itemTitle,
    Map<String, dynamic>? data,
    bool? isRead,
    DateTime? readAt,
    DateTime? createdAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      type: type ?? this.type,
      recipientId: recipientId ?? this.recipientId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderImage: senderImage ?? this.senderImage,
      title: title ?? this.title,
      body: body ?? this.body,
      itemId: itemId ?? this.itemId,
      itemType: itemType ?? this.itemType,
      itemTitle: itemTitle ?? this.itemTitle,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      readAt: readAt ?? this.readAt,
      createdAt: createdAt ?? this.createdAt);
  }

  /// تحويل الإشعار إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.index,
      'recipientId': recipientId,
      'senderId': senderId,
      'senderName': senderName,
      'senderImage': senderImage,
      'title': title,
      'body': body,
      'itemId': itemId,
      'itemType': itemType,
      'itemTitle': itemTitle,
      'data': data,
      'isRead': isRead,
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  /// إنشاء إشعار من خريطة
  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] ?? '',
      type: map['type'] != null && map['type'] < NotificationType.values.length
          ? NotificationType.values[map['type']]
          : NotificationType.system,
      recipientId: map['recipientId'] ?? '',
      senderId: map['senderId'],
      senderName: map['senderName'],
      senderImage: map['senderImage'],
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      itemId: map['itemId'],
      itemType: map['itemType'],
      itemTitle: map['itemTitle'],
      data: map['data'] != null ? Map<String, dynamic>.from(map['data']) : null,
      isRead: map['isRead'] ?? false,
      readAt: map['readAt'] is Timestamp ? (map['readAt'] as Timestamp).toDate() : null,
      createdAt: map['createdAt'] is Timestamp
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now());
  }

  /// إنشاء إشعار من وثيقة فايربيز
  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return NotificationModel(
        id: doc.id,
        type: NotificationType.system,
        recipientId: '',
        title: '',
        body: '',
        createdAt: DateTime.now());
    }
    return NotificationModel.fromMap({...data, 'id': doc.id});
  }

  @override
  List<Object?> get props => [
        id,
        type,
        recipientId,
        senderId,
        senderName,
        senderImage,
        title,
        body,
        itemId,
        itemType,
        itemTitle,
        data,
        isRead,
        readAt,
        createdAt,
      ];
}
