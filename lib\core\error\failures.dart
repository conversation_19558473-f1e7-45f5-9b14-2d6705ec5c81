/// Abstract class that defines a generic failure with a message.
abstract class Failure {
  /// A message that describes the failure.
  final String message;

  /// Constructs a [Failure] with the provided [message].
  const Failure(this.message);
}

/// Represents a failure due to server-related issues.
class ServerFailure extends Failure {
  /// Constructs a [ServerFailure] with the provided [message].
  const ServerFailure(super.message);
}

/// Represents a failure due to caching-related issues.
class CacheFailure extends Failure {
  /// Constructs a [CacheFailure] with the provided [message].
  const CacheFailure(super.message);
}
