@echo off
echo ========================================
echo تحضير إصدار الاختبار المغلق - تطبيق Krea
echo ========================================
echo.

echo هذا السكريبت سيقوم بتحضير جميع الملفات المطلوبة للإصدار
echo.

:: التحقق من المتطلبات الأساسية
echo التحقق من المتطلبات...

:: التحقق من Flutter
flutter --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Flutter غير مثبت
    pause
    exit /b 1
)

:: التحقق من Java
java -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java غير مثبت
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

:: إنشاء مجلد scripts إذا لم يكن موجوداً
if not exist "scripts" mkdir scripts

:: التحقق من وجود مفتاح التوقيع
if not exist "android\key.properties" (
    echo ⚠️  مفتاح التوقيع الرقمي غير موجود
    echo هل تريد إنشاؤه الآن؟ (y/n)
    set /p create_key=""
    if /i "%create_key%"=="y" (
        call scripts\create_keystore.bat
    ) else (
        echo يمكنك إنشاؤه لاحقاً بتشغيل: scripts\create_keystore.bat
    )
)

echo.
echo تحضير ملفات الإصدار...

:: تنظيف المشروع
echo - تنظيف المشروع...
flutter clean >nul 2>&1

:: تحديث التبعيات
echo - تحديث التبعيات...
flutter pub get >nul 2>&1

:: تشغيل code generation
echo - تشغيل code generation...
flutter packages pub run build_runner build --delete-conflicting-outputs >nul 2>&1

echo ✅ تم تحضير المشروع بنجاح!
echo.

echo الخطوات التالية:
echo 1. تشغيل scripts\build_release.bat لبناء التطبيق
echo 2. رفع الملف الناتج على Google Play Console
echo 3. إعداد الاختبار المغلق وإضافة المختبرين
echo.

echo 📖 للمزيد من التفاصيل، راجع: docs\RELEASE_GUIDE.md
echo.

pause
