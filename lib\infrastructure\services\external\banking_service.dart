import 'dart:math';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../api/api_client.dart';
import '../../../core/errors/exceptions.dart';
import '../../../domain/entities/mortgage_application.dart';
import '../../../domain/entities/mortgage_offer.dart';
import '../../../domain/entities/bank.dart';

/// خدمة التكامل مع البنوك للتمويل العقاري
class BankingService {
  final ApiClient _apiClient;
  final FlutterSecureStorage _secureStorage;
  final String _baseUrl;

  /// إنشاء خدمة التكامل مع البنوك
  BankingService({
    required ApiClient apiClient,
    required FlutterSecureStorage secureStorage,
    String? baseUrl,
  })  : _apiClient = apiClient,
        _secureStorage = secureStorage,
        _baseUrl = baseUrl ?? 'https://api.realestate.com/banking';

  /// الحصول على قائمة البنوك المتاحة للتمويل العقاري
  Future<List<Bank>> getAvailableBanks() async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/banks',
        cache: true,
        cacheDuration: const Duration(hours: 24));

      if (response is List) {
        return response.map((bank) => Bank.fromJson(bank)).toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException(
          'Banking Service', 'فشل في الحصول على قائمة البنوك: $e');
    }
  }

  /// الحصول على عروض التمويل العقاري من البنوك
  Future<List<MortgageOffer>> getMortgageOffers({
    required double propertyValue,
    required double downPayment,
    required int loanTerm,
    required double monthlyIncome,
    String? propertyType,
    String? location,
  }) async {
    try {
      final response = await _apiClient.post(
        '$_baseUrl/mortgage/offers',
        body: {
          'propertyValue': propertyValue,
          'downPayment': downPayment,
          'loanTerm': loanTerm,
          'monthlyIncome': monthlyIncome,
          'propertyType': propertyType,
          'location': location,
        });

      if (response is List) {
        return response.map((offer) => MortgageOffer.fromJson(offer)).toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException(
          'Banking Service', 'فشل في الحصول على عروض التمويل: $e');
    }
  }

  /// تقديم طلب تمويل عقاري
  Future<MortgageApplication> applyForMortgage({
    required String bankId,
    required String userId,
    required String estateId,
    required double propertyValue,
    required double loanAmount,
    required double downPayment,
    required int loanTerm,
    required double interestRate,
    required double monthlyPayment,
    required Map<String, dynamic> applicantInfo,
    Map<String, dynamic>? additionalDocuments,
  }) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.post(
        '$_baseUrl/mortgage/apply',
        headers: {
          'Authorization': 'Bearer $authToken',
        },
        body: {
          'bankId': bankId,
          'userId': userId,
          'estateId': estateId,
          'propertyValue': propertyValue,
          'loanAmount': loanAmount,
          'downPayment': downPayment,
          'loanTerm': loanTerm,
          'interestRate': interestRate,
          'monthlyPayment': monthlyPayment,
          'applicantInfo': applicantInfo,
          'additionalDocuments': additionalDocuments,
        });

      return MortgageApplication.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Banking Service', 'فشل في تقديم طلب التمويل: $e');
    }
  }

  /// الحصول على حالة طلب التمويل العقاري
  Future<MortgageApplication> getMortgageApplicationStatus(
      String applicationId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/mortgage/applications/$applicationId',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      return MortgageApplication.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Banking Service', 'فشل في الحصول على حالة طلب التمويل: $e');
    }
  }

  /// الحصول على طلبات التمويل العقاري للمستخدم
  Future<List<MortgageApplication>> getUserMortgageApplications(
      String userId) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.get(
        '$_baseUrl/mortgage/applications/user/$userId',
        headers: {
          'Authorization': 'Bearer $authToken',
        });

      if (response is List) {
        return response
            .map((app) => MortgageApplication.fromJson(app))
            .toList();
      }

      return [];
    } catch (e) {
      throw IntegrationException(
          'Banking Service', 'فشل في الحصول على طلبات التمويل للمستخدم: $e');
    }
  }

  /// حساب التمويل العقاري
  Future<Map<String, dynamic>> calculateMortgage({
    required double propertyValue,
    required double downPayment,
    required int loanTerm,
    required double interestRate,
  }) async {
    try {
      final loanAmount = propertyValue - downPayment;
      final monthlyInterestRate = interestRate / 100 / 12;
      final numberOfPayments = loanTerm * 12;

      // حساب القسط الشهري باستخدام صيغة التمويل العقاري
      final monthlyPayment = loanAmount *
          (monthlyInterestRate *
              pow((1 + monthlyInterestRate), numberOfPayments)) /
          (pow((1 + monthlyInterestRate), numberOfPayments) - 1);

      // حساب إجمالي المدفوعات
      final totalPayment = monthlyPayment * numberOfPayments;

      // حساب إجمالي الفائدة
      final totalInterest = totalPayment - loanAmount;

      return {
        'loanAmount': loanAmount,
        'monthlyPayment': monthlyPayment,
        'totalPayment': totalPayment,
        'totalInterest': totalInterest,
        'loanToValueRatio': loanAmount / propertyValue,
      };
    } catch (e) {
      throw IntegrationException(
          'Banking Service', 'فشل في حساب التمويل العقاري: $e');
    }
  }

  /// التحقق من أهلية المستخدم للتمويل العقاري
  Future<Map<String, dynamic>> checkMortgageEligibility({
    required double monthlyIncome,
    required double requestedLoanAmount,
    required int loanTerm,
    required double interestRate,
    List<Map<String, dynamic>>? existingDebts,
  }) async {
    try {
      // حساب نسبة الدين إلى الدخل (DTI)
      double totalMonthlyDebt = 0;

      // إضافة الديون الحالية
      if (existingDebts != null) {
        for (final debt in existingDebts) {
          totalMonthlyDebt += debt['monthlyPayment'] as double;
        }
      }

      // حساب القسط الشهري للقرض المطلوب
      final monthlyInterestRate = interestRate / 100 / 12;
      final numberOfPayments = loanTerm * 12;
      final monthlyPayment = requestedLoanAmount *
          (monthlyInterestRate *
              pow((1 + monthlyInterestRate), numberOfPayments)) /
          (pow((1 + monthlyInterestRate), numberOfPayments) - 1);

      // إضافة القسط الشهري للقرض المطلوب
      totalMonthlyDebt += monthlyPayment;

      // حساب نسبة الدين إلى الدخل
      final debtToIncomeRatio = totalMonthlyDebt / monthlyIncome;

      // التحقق من الأهلية (عادة ما تكون نسبة الدين إلى الدخل أقل من 0.43)
      final isEligible = debtToIncomeRatio <= 0.43;

      // حساب الحد الأقصى للقرض بناءً على الدخل
      final maxMonthlyPayment =
          monthlyIncome * 0.43 - (totalMonthlyDebt - monthlyPayment);
      final maxLoanAmount = maxMonthlyPayment *
          (pow((1 + monthlyInterestRate), numberOfPayments) - 1) /
          (monthlyInterestRate *
              pow((1 + monthlyInterestRate), numberOfPayments));

      return {
        'isEligible': isEligible,
        'debtToIncomeRatio': debtToIncomeRatio,
        'maxLoanAmount': maxLoanAmount,
        'recommendedLoanAmount':
            isEligible ? requestedLoanAmount : maxLoanAmount,
        'monthlyPayment': monthlyPayment,
        'maxMonthlyPayment': maxMonthlyPayment,
      };
    } catch (e) {
      throw IntegrationException(
          'Banking Service', 'فشل في التحقق من أهلية التمويل: $e');
    }
  }

  /// الحصول على معلومات البنك
  Future<Bank> getBankDetails(String bankId) async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/banks/$bankId',
        cache: true,
        cacheDuration: const Duration(hours: 24));

      return Bank.fromJson(response);
    } catch (e) {
      throw IntegrationException(
          'Banking Service', 'فشل في الحصول على معلومات البنك: $e');
    }
  }

  /// الحصول على معدلات الفائدة الحالية
  Future<Map<String, dynamic>> getCurrentInterestRates() async {
    try {
      final response = await _apiClient.get(
        '$_baseUrl/mortgage/rates',
        cache: true,
        cacheDuration: const Duration(hours: 6));

      return response;
    } catch (e) {
      throw IntegrationException(
          'Banking Service', 'فشل في الحصول على معدلات الفائدة: $e');
    }
  }

  /// تحميل المستندات المطلوبة للتمويل العقاري
  Future<Map<String, String>> uploadMortgageDocuments({
    required String applicationId,
    required Map<String, dynamic> documents,
  }) async {
    try {
      // الحصول على رمز المصادقة للمستخدم
      final authToken = await _secureStorage.read(key: 'auth_token');

      final response = await _apiClient.upload(
        '$_baseUrl/mortgage/applications/$applicationId/documents',
        headers: {
          'Authorization': 'Bearer $authToken',
        },
        files: documents);

      return Map<String, String>.from(response);
    } catch (e) {
      throw IntegrationException(
          'Banking Service', 'فشل في تحميل المستندات: $e');
    }
  }
}

/// امتدادات لتسهيل استخدام خدمة التكامل مع البنوك
extension BankingServiceExtensions on BankingService {
  /// حساب القسط الشهري للتمويل العقاري
  double calculateMonthlyPayment({
    required double loanAmount,
    required double interestRate,
    required int loanTerm,
  }) {
    final monthlyInterestRate = interestRate / 100 / 12;
    final numberOfPayments = loanTerm * 12;

    return loanAmount *
        (monthlyInterestRate *
            pow((1 + monthlyInterestRate), numberOfPayments)) /
        (pow((1 + monthlyInterestRate), numberOfPayments) - 1);
  }

  /// حساب إجمالي الفائدة للتمويل العقاري
  double calculateTotalInterest({
    required double loanAmount,
    required double interestRate,
    required int loanTerm,
  }) {
    final monthlyPayment = calculateMonthlyPayment(
      loanAmount: loanAmount,
      interestRate: interestRate,
      loanTerm: loanTerm);

    return (monthlyPayment * loanTerm * 12) - loanAmount;
  }

  /// حساب نسبة القرض إلى القيمة
  double calculateLoanToValueRatio({
    required double loanAmount,
    required double propertyValue,
  }) {
    return loanAmount / propertyValue;
  }
}

/// امتدادات لتسهيل استخدام خدمة التكامل مع البنوك في واجهة المستخدم
extension BankingServiceUIExtensions on BankingService {
  /// الحصول على وصف حالة طلب التمويل العقاري
  String getMortgageApplicationStatusDescription(String status) {
    switch (status) {
      case 'pending':
        return 'قيد المراجعة';
      case 'approved':
        return 'تمت الموافقة';
      case 'rejected':
        return 'مرفوض';
      case 'incomplete':
        return 'غير مكتمل';
      case 'processing':
        return 'قيد المعالجة';
      case 'awaiting_documents':
        return 'في انتظار المستندات';
      case 'awaiting_payment':
        return 'في انتظار الدفع';
      case 'completed':
        return 'مكتمل';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على وصف نوع التمويل العقاري
  String getMortgageTypeDescription(String type) {
    switch (type) {
      case 'fixed':
        return 'فائدة ثابتة';
      case 'variable':
        return 'فائدة متغيرة';
      case 'islamic':
        return 'تمويل إسلامي';
      case 'balloon':
        return 'دفعة بالون';
      case 'interest_only':
        return 'فائدة فقط';
      default:
        return 'غير معروف';
    }
  }
}
