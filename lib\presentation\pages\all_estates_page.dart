// lib/presentation/pages/all_estates_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';
import 'package:kuwait_corners/presentation/bloc/estate_bloc.dart';
import 'package:kuwait_corners/presentation/bloc/estate_event.dart';
import 'package:kuwait_corners/presentation/bloc/estate_state.dart';
import 'package:kuwait_corners/presentation/widgets/custom_app_bar.dart';
import 'package:kuwait_corners/presentation/widgets/empty_state_widget.dart';
import 'package:kuwait_corners/presentation/widgets/estate_card.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

/// صفحة عرض كل الإعلانات حسب الفئة المختارة مع نظام بحث وتصفية متقدم
class AllEstatesPage extends StatefulWidget {
  /// عنوان الصفحة
  final String title;

  /// نوع الإعلانات المعروضة (مميزة، أحدث، تجارية، إلخ)
  final String estateType;

  /// قائمة الإعلانات الأولية (اختياري)
  final List<Estate>? initialEstates;

  /// دالة تصفية مخصصة (اختياري)
  final bool Function(Estate)? filterFunction;

  const AllEstatesPage({
    super.key,
    required this.title,
    required this.estateType,
    this.initialEstates,
    this.filterFunction,
  });

  @override
  State<AllEstatesPage> createState() => _AllEstatesPageState();
}

class _AllEstatesPageState extends State<AllEstatesPage> {
  // متغيرات للتحكم بالتحميل والتحديث
  bool _isLoading = false;
  bool _hasMoreData = true;
  int _currentPage = 1;
  final int _pageSize = 20;

  // متغيرات للفلترة والترتيب
  String _sortBy = "createdAt";
  bool _sortAscending = false;
  final Map<String, dynamic> _filters = {};

  // متغيرات البحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = "";
  final FocusNode _searchFocusNode = FocusNode();

  // متحكم التمرير
  final ScrollController _scrollController = ScrollController();

  // مفتاح للـ RefreshIndicator
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  // قائمة الإعلانات المعروضة
  List<Estate> _displayedEstates = [];

  // قائمة الأقسام الرئيسية المتاحة
  List<String> _availableMainCategories = [];
  String? _selectedMainCategory;

  // قائمة الأقسام الفرعية المتاحة
  List<String> _availableSubCategories = [];
  String? _selectedSubCategory;

  // قائمة المناطق المتاحة
  List<String> _availableLocations = [];
  String? _selectedLocation;

  // قائمة أنواع العقارات المتاحة
  List<String> _availablePropertyTypes = [];
  String? _selectedPropertyType;

  // نطاق السعر
  RangeValues _priceRange = const RangeValues(0, 100000);
  double _maxPrice = 100000;

  // نطاق المساحة
  RangeValues _areaRange = const RangeValues(0, 1000);
  double _maxArea = 1000;

  // عدد الغرف
  int? _minRooms;
  int? _maxRooms;

  // عدد الحمامات
  int? _minBathrooms;
  int? _maxBathrooms;

  // عمر البناء
  int? _maxBuildingAge;

  // خيارات التجهيزات
  bool _hasCentralAC = false;
  bool _hasGarage = false;
  bool _hasMaidRoom = false;
  bool _hasElevator = false;
  bool _hasSwimmingPool = false;
  bool _hasBalcony = false;
  bool _isFullyFurnished = false;

  // نوع المعلن
  String? _advertiserType;

  // خيارات إضافية
  bool _isFeatured = false;
  bool _showOnlyAvailable = true;

  @override
  void initState() {
    super.initState();

    // إضافة مستمع للـ ScrollController لتحميل المزيد من البيانات عند الوصول لنهاية القائمة
    _scrollController.addListener(_scrollListener);

    // تهيئة الإعلانات الأولية إذا كانت متوفرة
    if (widget.initialEstates != null && widget.initialEstates!.isNotEmpty) {
      _displayedEstates = List.from(widget.initialEstates!);
      _extractFiltersFromEstates();
    } else {
      // تحميل الإعلانات من الخادم
      _loadEstates(refresh: true);
    }

    // التأكد من أن قيم القوائم المنسدلة صالحة
    _validateDropdownValues();
  }

  /// التحقق من صحة قيم القوائم المنسدلة
  void _validateDropdownValues() {
    // التحقق من قيمة عدد الغرف
    if (_minRooms != null && (_minRooms! < 1 || _minRooms! > 10)) {
      _minRooms = null;
    }

    // التحقق من قيمة عدد الحمامات
    if (_minBathrooms != null && (_minBathrooms! < 1 || _minBathrooms! > 6)) {
      _minBathrooms = null;
    }

    // التحقق من قيمة عمر البناء
    if (_maxBuildingAge != null) {
      bool isValid = false;
      for (int i = 1; i <= 10; i++) {
        if (_maxBuildingAge == i * 5) {
          isValid = true;
          break;
        }
      }
      if (!isValid) {
        _maxBuildingAge = null;
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  /// استخراج خيارات الفلترة من الإعلانات المتوفرة
  void _extractFiltersFromEstates() {
    if (_displayedEstates.isEmpty) return;

    // استخراج الأقسام الرئيسية المتاحة
    _availableMainCategories = _displayedEstates
        .map((e) => e.mainCategory ?? "")
        .where((e) => e.isNotEmpty)
        .toSet()
        .toList();

    // استخراج الأقسام الفرعية المتاحة
    _availableSubCategories = _displayedEstates
        .map((e) => e.subCategory ?? "")
        .where((e) => e.isNotEmpty)
        .toSet()
        .toList();

    // استخراج المناطق المتاحة
    _availableLocations = _displayedEstates
        .map((e) => e.location.split(" - ").first)
        .where((e) => e.isNotEmpty)
        .toSet()
        .toList();

    // استخراج أنواع العقارات المتاحة
    _availablePropertyTypes = _displayedEstates
        .map((e) => e.propertyType ?? "")
        .where((e) => e.isNotEmpty)
        .toSet()
        .toList();

    // تحديد أقصى سعر
    final maxEstatePrice = _displayedEstates
        .map((e) => e.price)
        .reduce((value, element) => value > element ? value : element);
    _maxPrice = maxEstatePrice * 1.2; // إضافة هامش 20%
    _priceRange = RangeValues(0, _maxPrice);

    // تحديد أقصى مساحة
    final estatesWithArea =
        _displayedEstates.where((e) => e.area != null).toList();
    if (estatesWithArea.isNotEmpty) {
      final maxEstateArea = estatesWithArea
          .map((e) => e.area!)
          .reduce((value, element) => value > element ? value : element);
      _maxArea = maxEstateArea * 1.2; // إضافة هامش 20%
      _areaRange = RangeValues(0, _maxArea);
    }

    // تحديد نطاق عدد الغرف
    final estatesWithRooms =
        _displayedEstates.where((e) => e.numberOfRooms != null).toList();
    if (estatesWithRooms.isNotEmpty) {
      final minRooms = estatesWithRooms
          .map((e) => e.numberOfRooms!)
          .reduce((value, element) => value < element ? value : element);
      final maxRooms = estatesWithRooms
          .map((e) => e.numberOfRooms!)
          .reduce((value, element) => value > element ? value : element);

      // التأكد من أن القيم ضمن النطاق المسموح به (1-10)
      if (minRooms >= 1 && minRooms <= 10) {
        _minRooms = minRooms;
      } else {
        _minRooms = null;
      }

      if (maxRooms >= 1 && maxRooms <= 10) {
        _maxRooms = maxRooms;
      } else {
        _maxRooms = null;
      }
    }

    // تحديد نطاق عدد الحمامات
    final estatesWithBathrooms =
        _displayedEstates.where((e) => e.numberOfBathrooms != null).toList();
    if (estatesWithBathrooms.isNotEmpty) {
      final minBathrooms = estatesWithBathrooms
          .map((e) => e.numberOfBathrooms!)
          .reduce((value, element) => value < element ? value : element);
      final maxBathrooms = estatesWithBathrooms
          .map((e) => e.numberOfBathrooms!)
          .reduce((value, element) => value > element ? value : element);

      // التأكد من أن القيم ضمن النطاق المسموح به (1-6)
      if (minBathrooms >= 1 && minBathrooms <= 6) {
        _minBathrooms = minBathrooms;
      } else {
        _minBathrooms = null;
      }

      if (maxBathrooms >= 1 && maxBathrooms <= 6) {
        _maxBathrooms = maxBathrooms;
      } else {
        _maxBathrooms = null;
      }
    }

    // تحديد أقصى عمر للبناء
    final estatesWithBuildingAge =
        _displayedEstates.where((e) => e.buildingAge != null).toList();
    if (estatesWithBuildingAge.isNotEmpty) {
      final maxAge = estatesWithBuildingAge
          .map((e) => e.buildingAge!)
          .reduce((value, element) => value > element ? value : element);

      // تقريب عمر البناء إلى أقرب مضاعف للـ 5
      final roundedAge = (maxAge / 5).ceil() * 5;

      // التأكد من أن القيمة ضمن النطاق المسموح به (5-50)
      if (roundedAge >= 5 && roundedAge <= 50) {
        _maxBuildingAge = roundedAge;
      } else {
        _maxBuildingAge = null;
      }
    }
  }

  /// تحميل الإعلانات
  void _loadEstates({bool refresh = false}) {
    // إذا كان التحميل قيد التنفيذ بالفعل ولم يكن هذا تحديثًا إجباريًا، فلا تفعل شيئًا
    if (_isLoading && !refresh) return;

    if (refresh) {
      setState(() {
        _currentPage = 1;
        _hasMoreData = true;
      });
    }

    if (!_hasMoreData && !refresh) return;

    setState(() {
      _isLoading = true;
    });

    // إضافة تأخير قصير لتجنب مشاكل التزامن المحتملة
    Future.microtask(() {
      if (mounted) {
        // إضافة فلتر حسب نوع الإعلان
        final Map<String, dynamic> filters = Map.from(_filters);

        // إضافة فلتر حسب نوع الإعلان المطلوب
        switch (widget.estateType) {
          case "featured":
            filters["isFeatured"] = true;
            break;
          case "commercial":
            filters["mainCategory"] = "commercial";
            break;
          case "latest":
            _sortBy = "createdAt";
            _sortAscending = false;
            break;
          case "garage":
            filters["hasGarage"] = true;
            break;
          case "centralAC":
            filters["hasCentralAC"] = true;
            break;
          case "maidRoom":
            filters["hasMaidRoom"] = true;
            break;
          case "company":
            filters["postedByUserType"] = "company";
            break;
          case "investor":
            filters["postedByUserType"] = "investor";
            break;
        }

        // إضافة فلتر القسم الرئيسي إذا كان محدداً
        if (_selectedMainCategory != null) {
          filters["mainCategory"] = _selectedMainCategory;
        }

        // إضافة فلتر الأقسام الفرعية إذا كان محدداً
        if (_selectedSubCategory != null) {
          filters["subCategory"] = _selectedSubCategory;
        }

        // إضافة فلتر المناطق إذا كان محدداً
        if (_selectedLocation != null) {
          filters["location"] = _selectedLocation;
        }

        // إضافة فلتر نوع العقار إذا كان محدداً
        if (_selectedPropertyType != null) {
          filters["propertyType"] = _selectedPropertyType;
        }

        // إضافة فلتر نطاق السعر
        filters["minPrice"] = _priceRange.start;
        filters["maxPrice"] = _priceRange.end;

        // إضافة فلتر نطاق المساحة
        if (_areaRange.end > 0) {
          filters["minArea"] = _areaRange.start;
          filters["maxArea"] = _areaRange.end;
        }

        // إضافة فلتر عدد الغرف
        if (_minRooms != null) {
          filters["minRooms"] = _minRooms;
        }
        if (_maxRooms != null) {
          filters["maxRooms"] = _maxRooms;
        }

        // إضافة فلتر عدد الحمامات
        if (_minBathrooms != null) {
          filters["minBathrooms"] = _minBathrooms;
        }
        if (_maxBathrooms != null) {
          filters["maxBathrooms"] = _maxBathrooms;
        }

        // إضافة فلتر عمر البناء
        if (_maxBuildingAge != null) {
          filters["maxBuildingAge"] = _maxBuildingAge;
        }

        // إضافة فلاتر التجهيزات
        if (_hasCentralAC) {
          filters["hasCentralAC"] = true;
        }
        if (_hasGarage) {
          filters["hasGarage"] = true;
        }
        if (_hasMaidRoom) {
          filters["hasMaidRoom"] = true;
        }
        if (_hasElevator) {
          filters["hasElevator"] = true;
        }
        if (_hasSwimmingPool) {
          filters["hasSwimmingPool"] = true;
        }
        if (_hasBalcony) {
          filters["hasBalcony"] = true;
        }
        if (_isFullyFurnished) {
          filters["isFullyFurnished"] = true;
        }

        // إضافة فلتر نوع المعلن
        if (_advertiserType != null) {
          filters["postedByUserType"] = _advertiserType;
        }

        // إضافة فلتر الإعلانات المميزة
        if (_isFeatured) {
          filters["isFeatured"] = true;
        }

        // إضافة فلتر الإعلانات المتاحة فقط
        if (_showOnlyAvailable) {
          filters["status"] = "available";
        }

        context.read<EstateBloc>().add(
              FetchPaginatedEstates(
                refresh: refresh,
                page: _currentPage,
                pageSize: _pageSize,
                sortBy: _sortBy,
                sortAscending: _sortAscending,
                filters: filters,
                searchQuery: _searchQuery));
      }
    });
  }

  /// مستمع التمرير لتحميل المزيد من البيانات
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMoreData) {
        setState(() {
          _currentPage++;
        });
        _loadEstates();
      }
    }
  }

  /// تحديث البيانات عند سحب الشاشة للأسفل
  Future<void> _refreshData() async {
    _loadEstates(refresh: true);
    return Future.delayed(const Duration(seconds: 1));
  }

  /// عرض مربع حوار الفلترة المتقدمة
  void _showAdvancedFilterDialog() {
    // التحقق من صحة قيم القوائم المنسدلة قبل عرض مربع الحوار
    _validateDropdownValues();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              padding: const EdgeInsets.all(20),
              height: MediaQuery.of(context).size.height * 0.8,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان مربع الحوار
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "خيارات البحث المتقدمة",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary)),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context)),
                    ]),
                  const Divider(),

                  Expanded(
                    child: ListView(
                      children: [
                        // فلتر الأقسام الرئيسية
                        if (_availableMainCategories.isNotEmpty) ...[
                          const Text(
                            "القسم الرئيسي",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            children: [
                              FilterChip(
                                label: const Text("الكل"),
                                selected: _selectedMainCategory == null,
                                onSelected: (selected) {
                                  if (selected) {
                                    setModalState(() {
                                      _selectedMainCategory = null;
                                    });
                                  }
                                }),
                              ..._availableMainCategories.map((category) {
                                return FilterChip(
                                  label: Text(category),
                                  selected: _selectedMainCategory == category,
                                  onSelected: (selected) {
                                    setModalState(() {
                                      _selectedMainCategory =
                                          selected ? category : null;
                                      // إعادة تعيين القسم الفرعي عند تغيير القسم الرئيسي
                                      if (selected) {
                                        _selectedSubCategory = null;
                                      }
                                    });
                                  });
                              }),
                            ]),
                          const SizedBox(height: 16),
                        ],

                        // فلتر الأقسام الفرعية
                        if (_availableSubCategories.isNotEmpty) ...[
                          const Text(
                            "القسم الفرعي",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            children: [
                              FilterChip(
                                label: const Text("الكل"),
                                selected: _selectedSubCategory == null,
                                onSelected: (selected) {
                                  if (selected) {
                                    setModalState(() {
                                      _selectedSubCategory = null;
                                    });
                                  }
                                }),
                              ..._availableSubCategories
                                  // تصفية الأقسام الفرعية حسب القسم الرئيسي المحدد
                                  .where((subCategory) {
                                if (_selectedMainCategory == null) return true;
                                final estate = _displayedEstates.firstWhere(
                                  (e) => e.subCategory == subCategory,
                                  orElse: () => _displayedEstates.first);
                                return estate.mainCategory ==
                                    _selectedMainCategory;
                              }).map((category) {
                                return FilterChip(
                                  label: Text(category),
                                  selected: _selectedSubCategory == category,
                                  onSelected: (selected) {
                                    setModalState(() {
                                      _selectedSubCategory =
                                          selected ? category : null;
                                    });
                                  });
                              }),
                            ]),
                          const SizedBox(height: 16),
                        ],

                        // فلتر المناطق
                        if (_availableLocations.isNotEmpty) ...[
                          const Text(
                            "المنطقة",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            children: [
                              FilterChip(
                                label: const Text("الكل"),
                                selected: _selectedLocation == null,
                                onSelected: (selected) {
                                  if (selected) {
                                    setModalState(() {
                                      _selectedLocation = null;
                                    });
                                  }
                                }),
                              ..._availableLocations.map((location) {
                                return FilterChip(
                                  label: Text(location),
                                  selected: _selectedLocation == location,
                                  onSelected: (selected) {
                                    setModalState(() {
                                      _selectedLocation =
                                          selected ? location : null;
                                    });
                                  });
                              }),
                            ]),
                          const SizedBox(height: 16),
                        ],

                        // فلتر نوع العقار
                        if (_availablePropertyTypes.isNotEmpty) ...[
                          const Text(
                            "نوع العقار",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            children: [
                              FilterChip(
                                label: const Text("الكل"),
                                selected: _selectedPropertyType == null,
                                onSelected: (selected) {
                                  if (selected) {
                                    setModalState(() {
                                      _selectedPropertyType = null;
                                    });
                                  }
                                }),
                              ..._availablePropertyTypes.map((type) {
                                return FilterChip(
                                  label: Text(type),
                                  selected: _selectedPropertyType == type,
                                  onSelected: (selected) {
                                    setModalState(() {
                                      _selectedPropertyType =
                                          selected ? type : null;
                                    });
                                  });
                              }),
                            ]),
                          const SizedBox(height: 16),
                        ],

                        // فلتر نطاق السعر
                        const Text(
                          "نطاق السعر",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "${_priceRange.start.toInt()} د.ك",
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey)),
                            Text(
                              "${_priceRange.end.toInt()} د.ك",
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey)),
                          ]),
                        RangeSlider(
                          values: _priceRange,
                          min: 0,
                          max: _maxPrice,
                          divisions: 100,
                          labels: RangeLabels(
                            "${_priceRange.start.toInt()} د.ك",
                            "${_priceRange.end.toInt()} د.ك"),
                          onChanged: (values) {
                            setModalState(() {
                              _priceRange = values;
                            });
                          }),
                        const SizedBox(height: 16),

                        // فلتر نطاق المساحة
                        const Text(
                          "نطاق المساحة (متر مربع)",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "${_areaRange.start.toInt()} م²",
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey)),
                            Text(
                              "${_areaRange.end.toInt()} م²",
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey)),
                          ]),
                        RangeSlider(
                          values: _areaRange,
                          min: 0,
                          max: _maxArea,
                          divisions: 100,
                          labels: RangeLabels(
                            "${_areaRange.start.toInt()} م²",
                            "${_areaRange.end.toInt()} م²"),
                          onChanged: (values) {
                            setModalState(() {
                              _areaRange = values;
                            });
                          }),
                        const SizedBox(height: 16),

                        // فلتر عدد الغرف والحمامات
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "عدد الغرف",
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold)),
                                  const SizedBox(height: 8),
                                  DropdownButtonFormField<int?>(
                                    value: _minRooms,
                                    decoration: const InputDecoration(
                                      labelText: "الحد الأدنى",
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 8)),
                                    items: [
                                      const DropdownMenuItem<int?>(
                                        value: null,
                                        child: Text("الكل")),
                                      for (int i = 1; i <= 10; i++)
                                        DropdownMenuItem<int?>(
                                          value: i,
                                          child: Text("$i")),
                                    ],
                                    onChanged: (value) {
                                      setModalState(() {
                                        _minRooms = value;
                                      });
                                    }),
                                ])),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "عدد الحمامات",
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold)),
                                  const SizedBox(height: 8),
                                  DropdownButtonFormField<int?>(
                                    value: _minBathrooms,
                                    decoration: const InputDecoration(
                                      labelText: "الحد الأدنى",
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 8)),
                                    items: [
                                      const DropdownMenuItem<int?>(
                                        value: null,
                                        child: Text("الكل")),
                                      for (int i = 1; i <= 6; i++)
                                        DropdownMenuItem<int?>(
                                          value: i,
                                          child: Text("$i")),
                                    ],
                                    onChanged: (value) {
                                      setModalState(() {
                                        _minBathrooms = value;
                                      });
                                    }),
                                ])),
                          ]),
                        const SizedBox(height: 16),

                        // فلتر عمر البناء
                        const Text(
                          "عمر البناء",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<int?>(
                          value: _maxBuildingAge,
                          decoration: const InputDecoration(
                            labelText: "الحد الأقصى",
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8)),
                          items: [
                            const DropdownMenuItem<int?>(
                              value: null,
                              child: Text("الكل")),
                            for (int i = 1; i <= 10; i++)
                              DropdownMenuItem<int?>(
                                value: i * 5,
                                child: Text("${i * 5} سنة")),
                          ],
                          onChanged: (value) {
                            setModalState(() {
                              _maxBuildingAge = value;
                            });
                          }),
                        const SizedBox(height: 16),

                        // فلتر التجهيزات
                        const Text(
                          "التجهيزات",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            FilterChip(
                              label: const Text("تكييف مركزي"),
                              selected: _hasCentralAC,
                              onSelected: (selected) {
                                setModalState(() {
                                  _hasCentralAC = selected;
                                });
                              }),
                            FilterChip(
                              label: const Text("مرآب"),
                              selected: _hasGarage,
                              onSelected: (selected) {
                                setModalState(() {
                                  _hasGarage = selected;
                                });
                              }),
                            FilterChip(
                              label: const Text("غرفة خادمة"),
                              selected: _hasMaidRoom,
                              onSelected: (selected) {
                                setModalState(() {
                                  _hasMaidRoom = selected;
                                });
                              }),
                            FilterChip(
                              label: const Text("مصعد"),
                              selected: _hasElevator,
                              onSelected: (selected) {
                                setModalState(() {
                                  _hasElevator = selected;
                                });
                              }),
                            FilterChip(
                              label: const Text("مسبح"),
                              selected: _hasSwimmingPool,
                              onSelected: (selected) {
                                setModalState(() {
                                  _hasSwimmingPool = selected;
                                });
                              }),
                            FilterChip(
                              label: const Text("بلكونة"),
                              selected: _hasBalcony,
                              onSelected: (selected) {
                                setModalState(() {
                                  _hasBalcony = selected;
                                });
                              }),
                            FilterChip(
                              label: const Text("مفروش بالكامل"),
                              selected: _isFullyFurnished,
                              onSelected: (selected) {
                                setModalState(() {
                                  _isFullyFurnished = selected;
                                });
                              }),
                          ]),
                        const SizedBox(height: 16),

                        // فلتر نوع المعلن
                        const Text(
                          "نوع المعلن",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String?>(
                          value: _advertiserType,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8)),
                          items: const [
                            DropdownMenuItem<String?>(
                              value: null,
                              child: Text("الكل")),
                            DropdownMenuItem<String?>(
                              value: "owner",
                              child: Text("مالك")),
                            DropdownMenuItem<String?>(
                              value: "company",
                              child: Text("شركة")),
                            DropdownMenuItem<String?>(
                              value: "investor",
                              child: Text("مستثمر")),
                            DropdownMenuItem<String?>(
                              value: "agent",
                              child: Text("وسيط")),
                          ],
                          onChanged: (value) {
                            setModalState(() {
                              _advertiserType = value;
                            });
                          }),
                        const SizedBox(height: 16),

                        // خيارات إضافية
                        const Text(
                          "خيارات إضافية",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        CheckboxListTile(
                          title: const Text("إعلانات مميزة فقط"),
                          value: _isFeatured,
                          onChanged: (value) {
                            setModalState(() {
                              _isFeatured = value ?? false;
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                          controlAffinity: ListTileControlAffinity.leading),
                        CheckboxListTile(
                          title: const Text("متاح فقط"),
                          value: _showOnlyAvailable,
                          onChanged: (value) {
                            setModalState(() {
                              _showOnlyAvailable = value ?? true;
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                          controlAffinity: ListTileControlAffinity.leading),
                      ])),

                  // أزرار التطبيق وإعادة الضبط
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            setModalState(() {
                              // إعادة تعيين جميع الفلاتر
                              _selectedMainCategory = null;
                              _selectedSubCategory = null;
                              _selectedLocation = null;
                              _selectedPropertyType = null;
                              _priceRange = RangeValues(0, _maxPrice);
                              _areaRange = RangeValues(0, _maxArea);
                              _minRooms = null;
                              _maxRooms = null;
                              _minBathrooms = null;
                              _maxBathrooms = null;
                              _maxBuildingAge = null;
                              _hasCentralAC = false;
                              _hasGarage = false;
                              _hasMaidRoom = false;
                              _hasElevator = false;
                              _hasSwimmingPool = false;
                              _hasBalcony = false;
                              _isFullyFurnished = false;
                              _advertiserType = null;
                              _isFeatured = false;
                              _showOnlyAvailable = true;
                            });
                          },
                          child: const Text("إعادة ضبط"))),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            _loadEstates(refresh: true);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary),
                          child: const Text(
                            "تطبيق",
                            style: TextStyle(color: Colors.white)))),
                    ]),
                ]));
          });
      });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: widget.title,
        showSearchButton: true,
        showNotificationButton: true,
        onSearchPressed: () {
          FocusScope.of(context).requestFocus(_searchFocusNode);
        }),
      body: Column(
        children: [
          // حقل البحث وأزرار الفلترة
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // حقل البحث
                Container(
                  height: 48,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.0)),
                  child: TextField(
                    controller: _searchController,
                    focusNode: _searchFocusNode,
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                    onSubmitted: (_) {
                      _loadEstates(refresh: true);
                    },
                    textAlignVertical: TextAlignVertical.center,
                    decoration: InputDecoration(
                      hintText: "ابحث عن عقار...",
                      hintStyle: TextStyle(
                        color: Colors.grey.shade400,
                        fontSize: 14),
                      prefixIcon: Icon(
                        Icons.search,
                        color: AppColors.primary,
                        size: 20),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: Icon(
                                Icons.clear,
                                color: Colors.grey.shade400,
                                size: 18),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchQuery = "";
                                });
                                _loadEstates(refresh: true);
                              })
                          : null,
                      filled: true,
                      fillColor: Colors.grey.shade50,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.0),
                        borderSide:
                            BorderSide(color: Colors.grey.shade200, width: 1)),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.0),
                        borderSide:
                            BorderSide(color: Colors.grey.shade200, width: 1)),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.0),
                        borderSide:
                            BorderSide(color: AppColors.primary, width: 1.5)),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 0)))),

                const SizedBox(height: 16),

                // أزرار الفلترة والترتيب
                Row(
                  children: [
                    // زر الفلترة المتقدمة
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _showAdvancedFilterDialog,
                        icon: const Icon(Icons.filter_alt),
                        label: const Text("فلترة متقدمة"),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: BorderSide(color: AppColors.primary)))),
                    const SizedBox(width: 8),
                    // زر الترتيب
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          // عرض قائمة خيارات الترتيب
                          showModalBottomSheet(
                            context: context,
                            builder: (context) {
                              return Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  ListTile(
                                    title: const Text("الأحدث أولاً"),
                                    leading: const Icon(Icons.access_time),
                                    selected: _sortBy == "createdAt" &&
                                        !_sortAscending,
                                    onTap: () {
                                      setState(() {
                                        _sortBy = "createdAt";
                                        _sortAscending = false;
                                      });
                                      Navigator.pop(context);
                                      _loadEstates(refresh: true);
                                    }),
                                  ListTile(
                                    title: const Text("الأقدم أولاً"),
                                    leading: const Icon(Icons.history),
                                    selected: _sortBy == "createdAt" &&
                                        _sortAscending,
                                    onTap: () {
                                      setState(() {
                                        _sortBy = "createdAt";
                                        _sortAscending = true;
                                      });
                                      Navigator.pop(context);
                                      _loadEstates(refresh: true);
                                    }),
                                  ListTile(
                                    title: const Text("السعر: من الأعلى للأقل"),
                                    leading: const Icon(Icons.arrow_downward),
                                    selected:
                                        _sortBy == "price" && !_sortAscending,
                                    onTap: () {
                                      setState(() {
                                        _sortBy = "price";
                                        _sortAscending = false;
                                      });
                                      Navigator.pop(context);
                                      _loadEstates(refresh: true);
                                    }),
                                  ListTile(
                                    title: const Text("السعر: من الأقل للأعلى"),
                                    leading: const Icon(Icons.arrow_upward),
                                    selected:
                                        _sortBy == "price" && _sortAscending,
                                    onTap: () {
                                      setState(() {
                                        _sortBy = "price";
                                        _sortAscending = true;
                                      });
                                      Navigator.pop(context);
                                      _loadEstates(refresh: true);
                                    }),
                                ]);
                            });
                        },
                        icon: const Icon(Icons.sort),
                        label: const Text("ترتيب"),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.amber.shade700,
                          side: BorderSide(color: Colors.amber.shade700)))),
                  ]),
              ])),

          // عرض الإعلانات
          Expanded(
            child: RefreshIndicator(
              key: _refreshIndicatorKey,
              onRefresh: _refreshData,
              child: BlocConsumer<EstateBloc, EstateState>(
                listener: (context, state) {
                  if (state is PaginatedEstatesLoaded) {
                    setState(() {
                      if (_currentPage == 1) {
                        _displayedEstates = state.estates;
                      } else {
                        _displayedEstates.addAll(state.estates);
                      }
                      _hasMoreData = state.hasMoreData;
                      _isLoading = false;

                      // استخراج خيارات الفلترة إذا كانت هذه هي المرة الأولى
                      if (_currentPage == 1 &&
                          _availableSubCategories.isEmpty) {
                        _extractFiltersFromEstates();
                      }
                    });
                  } else if (state is EstateError) {
                    setState(() {
                      _isLoading = false;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(state.message)));
                  }
                },
                builder: (context, state) {
                  if (state is EstateLoading && state.isFirstFetch) {
                    return const LoadingWidget(isListView: false);
                  }

                  if (_displayedEstates.isEmpty) {
                    return EmptyStateWidget(
                      title: "لا توجد إعلانات",
                      description:
                          "لم يتم العثور على إعلانات تطابق معايير البحث",
                      icon: Icons.search_off,
                      buttonText: "تغيير معايير البحث",
                      onButtonPressed: () {
                        _showAdvancedFilterDialog();
                      });
                  }

                  // تطبيق دالة الفلترة المخصصة إذا كانت متوفرة
                  final displayedEstates = widget.filterFunction != null
                      ? _displayedEstates.where(widget.filterFunction!).toList()
                      : _displayedEstates;

                  return AnimationLimiter(
                    child: GridView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.75,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12),
                      itemCount: displayedEstates.length + (_isLoading ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index >= displayedEstates.length) {
                          return const Center(
                            child: CircularProgressIndicator());
                        }

                        return AnimationConfiguration.staggeredGrid(
                          position: index,
                          duration: const Duration(milliseconds: 500),
                          columnCount: 2,
                          child: ScaleAnimation(
                            child: FadeInAnimation(
                              child:
                                  EstateCard(estate: displayedEstates[index]))));
                      }));
                }))),
        ]));
  }
}
