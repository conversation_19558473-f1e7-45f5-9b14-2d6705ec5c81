import 'dart:io';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:kuwait_corners/core/services/loyalty_program_service.dart';

/// خدمة المشاركة الاجتماعية
class SocialSharingService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final String _appDomain;
  final String _appPackageName;
  final String _iosAppStoreId;
  final LoyaltyProgramService _loyaltyService;

  SocialSharingService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
    String? appDomain,
    String? appPackageName,
    String? iosAppStoreId,
    LoyaltyProgramService? loyaltyService,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance,
        _appDomain = appDomain ?? 'kuwaitcorners.page.link',
        _appPackageName = appPackageName ?? 'com.kuwaitcorners.app',
        _iosAppStoreId = iosAppStoreId ?? '123456789',
        _loyaltyService = loyaltyService ?? LoyaltyProgramService();

  /// مشاركة عقار
  Future<void> shareEstate({
    required String estateId,
    required String estateTitle,
    String? estateImageUrl,
    String? estateDescription,
    required BuildContext context,
  }) async {
    try {
      // إنشاء رابط ديناميكي
      final dynamicLink = await _createEstateDynamicLink(
        estateId: estateId,
        estateTitle: estateTitle,
        estateImageUrl: estateImageUrl,
        estateDescription: estateDescription);

      // مشاركة الرابط
      await Share.share(
        'تحقق من هذا العقار: $estateTitle\n\n$dynamicLink',
        subject: 'مشاركة عقار من Krea');

      // تسجيل المشاركة
      _logShare('estate', estateId);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// مشاركة موضوع منتدى
  Future<void> shareForumTopic({
    required String topicId,
    required String topicTitle,
    String? topicImageUrl,
    String? topicDescription,
    required BuildContext context,
  }) async {
    try {
      // إنشاء رابط ديناميكي
      final dynamicLink = await _createForumTopicDynamicLink(
        topicId: topicId,
        topicTitle: topicTitle,
        topicImageUrl: topicImageUrl,
        topicDescription: topicDescription);

      // مشاركة الرابط
      await Share.share(
        'تحقق من هذا الموضوع: $topicTitle\n\n$dynamicLink',
        subject: 'مشاركة موضوع من Krea');

      // تسجيل المشاركة
      _logShare('forumTopic', topicId);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// مشاركة ملف الشخصي للوكيل
  Future<void> shareAgentProfile({
    required String agentId,
    required String agentName,
    String? agentImageUrl,
    String? agentDescription,
    required BuildContext context,
  }) async {
    try {
      // إنشاء رابط ديناميكي
      final dynamicLink = await _createAgentProfileDynamicLink(
        agentId: agentId,
        agentName: agentName,
        agentImageUrl: agentImageUrl,
        agentDescription: agentDescription);

      // مشاركة الرابط
      await Share.share(
        'تحقق من هذا الوكيل العقاري: $agentName\n\n$dynamicLink',
        subject: 'مشاركة وكيل عقاري من Krea');

      // تسجيل المشاركة
      _logShare('agent', agentId);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// مشاركة صورة
  Future<void> shareImage({
    required String imageUrl,
    required String title,
    String? description,
    required BuildContext context,
  }) async {
    try {
      // تنزيل الصورة
      final http.Response response = await http.get(Uri.parse(imageUrl));
      final Directory tempDir = await getTemporaryDirectory();
      final File file = File('${tempDir.path}/shared_image.jpg');
      await file.writeAsBytes(response.bodyBytes);

      // مشاركة الصورة
      await Share.shareXFiles(
        [XFile(file.path)],
        text: '$title\n\n${description ?? ''}',
        subject: 'مشاركة صورة من Krea');
    } catch (e) {
      // إذا فشل تنزيل الصورة، نشارك النص فقط
      await Share.share(
        '$title\n\n${description ?? ''}\n\n$imageUrl',
        subject: 'مشاركة صورة من Krea');
    }
  }

  /// مشاركة نص
  Future<void> shareText({
    required String text,
    String? subject,
  }) async {
    await Share.share(
      text,
      subject: subject ?? 'مشاركة من Krea');
  }

  /// مشاركة رابط
  Future<void> shareLink({
    required String url,
    required String title,
    String? description,
  }) async {
    await Share.share(
      '$title\n\n${description ?? ''}\n\n$url',
      subject: 'مشاركة رابط من Krea');
  }

  /// مشاركة التطبيق
  Future<void> shareApp() async {
    try {
      // مشاركة رابط التطبيق
      await Share.share(
        'تطبيق Krea - أفضل تطبيق للبحث عن العقارات في الكويت\nhttps://play.google.com/store/apps/details?id=com.krea.app',
        subject: 'تطبيق Krea');

      // إضافة نقاط برنامج الولاء للمستخدم
      final user = _auth.currentUser;
      if (user != null) {
        await _loyaltyService.addPointsForSharingApp();
      }

      // تسجيل المشاركة
      _logShare('app', 'app_share');
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// مشاركة رمز الإحالة
  Future<void> shareReferralCode({
    required String referralCode,
    required BuildContext context,
  }) async {
    try {
      // إنشاء رابط ديناميكي
      final dynamicLink = await _createReferralDynamicLink(
        referralCode: referralCode);

      // مشاركة الرابط
      await Share.share(
        'انضم إلى Krea واحصل على مزايا حصرية! استخدم رمز الإحالة الخاص بي: $referralCode أو انقر على الرابط: $dynamicLink',
        subject: 'دعوة للانضمام إلى Krea');

      // تسجيل المشاركة
      _logShare('referral', referralCode);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// إنشاء رابط ديناميكي للعقار
  Future<String> _createEstateDynamicLink({
    required String estateId,
    required String estateTitle,
    String? estateImageUrl,
    String? estateDescription,
  }) async {
    final DynamicLinkParameters parameters = DynamicLinkParameters(
      uriPrefix: 'https://$_appDomain',
      link: Uri.parse('https://$_appDomain/estate/$estateId'),
      androidParameters: AndroidParameters(
        packageName: _appPackageName,
        minimumVersion: 1),
      iosParameters: IOSParameters(
        bundleId: _appPackageName,
        appStoreId: _iosAppStoreId,
        minimumVersion: '1.0.0'),
      socialMetaTagParameters: SocialMetaTagParameters(
        title: estateTitle,
        description: estateDescription ?? 'تفاصيل العقار على Krea',
        imageUrl: estateImageUrl != null ? Uri.parse(estateImageUrl) : null));

    final ShortDynamicLink shortLink =
        await FirebaseDynamicLinks.instance.buildShortLink(parameters);
    return shortLink.shortUrl.toString();
  }

  /// إنشاء رابط ديناميكي لموضوع المنتدى
  Future<String> _createForumTopicDynamicLink({
    required String topicId,
    required String topicTitle,
    String? topicImageUrl,
    String? topicDescription,
  }) async {
    final DynamicLinkParameters parameters = DynamicLinkParameters(
      uriPrefix: 'https://$_appDomain',
      link: Uri.parse('https://$_appDomain/forum/topic/$topicId'),
      androidParameters: AndroidParameters(
        packageName: _appPackageName,
        minimumVersion: 1),
      iosParameters: IOSParameters(
        bundleId: _appPackageName,
        appStoreId: _iosAppStoreId,
        minimumVersion: '1.0.0'),
      socialMetaTagParameters: SocialMetaTagParameters(
        title: topicTitle,
        description: topicDescription ?? 'موضوع منتدى على Krea',
        imageUrl: topicImageUrl != null ? Uri.parse(topicImageUrl) : null));

    final ShortDynamicLink shortLink =
        await FirebaseDynamicLinks.instance.buildShortLink(parameters);
    return shortLink.shortUrl.toString();
  }

  /// إنشاء رابط ديناميكي لملف الوكيل
  Future<String> _createAgentProfileDynamicLink({
    required String agentId,
    required String agentName,
    String? agentImageUrl,
    String? agentDescription,
  }) async {
    final DynamicLinkParameters parameters = DynamicLinkParameters(
      uriPrefix: 'https://$_appDomain',
      link: Uri.parse('https://$_appDomain/agent/$agentId'),
      androidParameters: AndroidParameters(
        packageName: _appPackageName,
        minimumVersion: 1),
      iosParameters: IOSParameters(
        bundleId: _appPackageName,
        appStoreId: _iosAppStoreId,
        minimumVersion: '1.0.0'),
      socialMetaTagParameters: SocialMetaTagParameters(
        title: agentName,
        description: agentDescription ?? 'وكيل عقاري على Krea',
        imageUrl: agentImageUrl != null ? Uri.parse(agentImageUrl) : null));

    final ShortDynamicLink shortLink =
        await FirebaseDynamicLinks.instance.buildShortLink(parameters);
    return shortLink.shortUrl.toString();
  }

  /// إنشاء رابط ديناميكي للإحالة
  Future<String> _createReferralDynamicLink({
    required String referralCode,
  }) async {
    final DynamicLinkParameters parameters = DynamicLinkParameters(
      uriPrefix: 'https://$_appDomain',
      link: Uri.parse('https://$_appDomain/referral/$referralCode'),
      androidParameters: AndroidParameters(
        packageName: _appPackageName,
        minimumVersion: 1),
      iosParameters: IOSParameters(
        bundleId: _appPackageName,
        appStoreId: _iosAppStoreId,
        minimumVersion: '1.0.0'),
      socialMetaTagParameters: SocialMetaTagParameters(
        title: 'انضم إلى Krea',
        description: 'استخدم رمز الإحالة للحصول على مزايا حصرية!'));

    final ShortDynamicLink shortLink =
        await FirebaseDynamicLinks.instance.buildShortLink(parameters);
    return shortLink.shortUrl.toString();
  }

  /// تسجيل المشاركة
  Future<void> _logShare(String itemType, String itemId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return;
      }

      await _firestore.collection('shares').add({
        'userId': user.uid,
        'itemType': itemType,
        'itemId': itemId,
        'timestamp': FieldValue.serverTimestamp(),
      });

      // زيادة عداد المشاركات للعنصر
      if (itemType == 'estate') {
        await _firestore.collection('estates').doc(itemId).update({
          'sharesCount': FieldValue.increment(1),
        });
      } else if (itemType == 'forumTopic') {
        await _firestore.collection('forum_topics').doc(itemId).update({
          'sharesCount': FieldValue.increment(1),
        });
      } else if (itemType == 'agent') {
        await _firestore.collection('users').doc(itemId).update({
          'sharesCount': FieldValue.increment(1),
        });
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// معالجة الروابط الديناميكية
  Future<void> handleDynamicLinks(void Function(Uri) onLink) async {
    // معالجة الرابط الأولي
    final PendingDynamicLinkData? data =
        await FirebaseDynamicLinks.instance.getInitialLink();
    final Uri? deepLink = data?.link;

    if (deepLink != null) {
      onLink(deepLink);
    }

    // الاستماع للروابط الجديدة
    FirebaseDynamicLinks.instance.onLink.listen(
      (dynamicLinkData) {
        onLink(dynamicLinkData.link);
      }).onError((error) {
      // تجاهل الخطأ
    });
  }
}
