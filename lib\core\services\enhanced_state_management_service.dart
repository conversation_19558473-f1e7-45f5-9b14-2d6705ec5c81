import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

/// خدمة محسنة لإدارة حالات التطبيق والأخطاء
class EnhancedStateManagementService {
  static final EnhancedStateManagementService _instance = 
      EnhancedStateManagementService._internal();
  
  factory EnhancedStateManagementService() => _instance;
  
  EnhancedStateManagementService._internal();

  // متحكمات الحالة
  final StreamController<LoadingState> _loadingStateController = 
      StreamController<LoadingState>.broadcast();
  final StreamController<ErrorState> _errorStateController = 
      StreamController<ErrorState>.broadcast();
  final StreamController<ConnectivityResult> _connectivityController = 
      StreamController<ConnectivityResult>.broadcast();

  // خدمة الاتصال
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;

  // حالات التحميل النشطة
  final Set<String> _activeLoadingOperations = <String>{};
  
  // ذاكرة التخزين المؤقت للأخطاء
  final Map<String, ErrorState> _errorCache = <String, ErrorState>{};

  /// تهيئة الخدمة
  Future<void> initialize() async {
    // مراقبة حالة الاتصال
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (ConnectivityResult result) {
        _connectivityController.add(result);
        _handleConnectivityChange(result);
      });

    // فحص حالة الاتصال الأولية
    final initialConnectivity = await _connectivity.checkConnectivity();
    _connectivityController.add(initialConnectivity);
  }

  /// معالجة تغيير حالة الاتصال
  void _handleConnectivityChange(ConnectivityResult result) {
    if (result == ConnectivityResult.none) {
      // لا يوجد اتصال - إظهار رسالة خطأ
      addError(ErrorState(
        id: 'connectivity_error',
        message: 'لا يوجد اتصال بالإنترنت',
        type: ErrorType.connectivity,
        isRetryable: true));
    } else {
      // إزالة خطأ الاتصال إذا كان موجوداً
      removeError('connectivity_error');
    }
  }

  /// بدء عملية تحميل
  void startLoading(String operationId, {String? message}) {
    _activeLoadingOperations.add(operationId);
    _loadingStateController.add(LoadingState(
      operationId: operationId,
      isLoading: true,
      message: message));
  }

  /// إنهاء عملية تحميل
  void stopLoading(String operationId) {
    _activeLoadingOperations.remove(operationId);
    _loadingStateController.add(LoadingState(
      operationId: operationId,
      isLoading: false));
  }

  /// إضافة خطأ
  void addError(ErrorState error) {
    _errorCache[error.id] = error;
    _errorStateController.add(error);
  }

  /// إزالة خطأ
  void removeError(String errorId) {
    _errorCache.remove(errorId);
    _errorStateController.add(ErrorState(
      id: errorId,
      message: '',
      type: ErrorType.cleared,
      isRetryable: false));
  }

  /// مسح جميع الأخطاء
  void clearAllErrors() {
    _errorCache.clear();
    _errorStateController.add(ErrorState(
      id: 'all_cleared',
      message: '',
      type: ErrorType.cleared,
      isRetryable: false));
  }

  /// فحص ما إذا كانت هناك عملية تحميل نشطة
  bool isLoading([String? operationId]) {
    if (operationId != null) {
      return _activeLoadingOperations.contains(operationId);
    }
    return _activeLoadingOperations.isNotEmpty;
  }

  /// الحصول على حالة الخطأ
  ErrorState? getError(String errorId) {
    return _errorCache[errorId];
  }

  /// الحصول على جميع الأخطاء النشطة
  List<ErrorState> getAllActiveErrors() {
    return _errorCache.values.toList();
  }

  /// تدفق حالات التحميل
  Stream<LoadingState> get loadingStream => _loadingStateController.stream;

  /// تدفق حالات الأخطاء
  Stream<ErrorState> get errorStream => _errorStateController.stream;

  /// تدفق حالة الاتصال
  Stream<ConnectivityResult> get connectivityStream => _connectivityController.stream;

  /// فحص حالة الاتصال الحالية
  Future<bool> get isConnected async {
    final result = await _connectivity.checkConnectivity();
    return result != ConnectivityResult.none;
  }

  /// تنظيف الموارد
  void dispose() {
    _connectivitySubscription.cancel();
    _loadingStateController.close();
    _errorStateController.close();
    _connectivityController.close();
    _activeLoadingOperations.clear();
    _errorCache.clear();
  }
}

/// حالة التحميل
class LoadingState {
  final String operationId;
  final bool isLoading;
  final String? message;
  final DateTime timestamp;

  LoadingState({
    required this.operationId,
    required this.isLoading,
    this.message,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'LoadingState(operationId: $operationId, isLoading: $isLoading, message: $message)';
  }
}

/// حالة الخطأ
class ErrorState {
  final String id;
  final String message;
  final ErrorType type;
  final bool isRetryable;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  ErrorState({
    required this.id,
    required this.message,
    required this.type,
    required this.isRetryable,
    this.metadata,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'ErrorState(id: $id, message: $message, type: $type, isRetryable: $isRetryable)';
  }
}

/// أنواع الأخطاء
enum ErrorType {
  network,
  connectivity,
  authentication,
  authorization,
  validation,
  server,
  unknown,
  cleared,
}

/// امتدادات مساعدة
extension ErrorTypeExtension on ErrorType {
  String get displayName {
    switch (this) {
      case ErrorType.network:
        return 'خطأ في الشبكة';
      case ErrorType.connectivity:
        return 'خطأ في الاتصال';
      case ErrorType.authentication:
        return 'خطأ في المصادقة';
      case ErrorType.authorization:
        return 'خطأ في الصلاحيات';
      case ErrorType.validation:
        return 'خطأ في البيانات';
      case ErrorType.server:
        return 'خطأ في الخادم';
      case ErrorType.unknown:
        return 'خطأ غير معروف';
      case ErrorType.cleared:
        return 'تم المسح';
    }
  }

  String get icon {
    switch (this) {
      case ErrorType.network:
        return '🌐';
      case ErrorType.connectivity:
        return '📶';
      case ErrorType.authentication:
        return '🔐';
      case ErrorType.authorization:
        return '🚫';
      case ErrorType.validation:
        return '⚠️';
      case ErrorType.server:
        return '🖥️';
      case ErrorType.unknown:
        return '❓';
      case ErrorType.cleared:
        return '✅';
    }
  }
}
