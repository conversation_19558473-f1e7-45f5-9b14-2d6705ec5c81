import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// أنواع الإشعارات المباشرة
enum RealtimeNotificationType {
  /// رسالة جديدة
  newMessage,

  /// تحديث حالة العقار
  estateStatusUpdate,

  /// تغيير سعر العقار
  estatePriceChange,

  /// تعليق جديد
  newComment,

  /// رد جديد
  newReply,

  /// تقييم جديد
  newRating,

  /// تحديث حالة الطلب
  requestStatusUpdate,

  /// عرض جديد على طلب عقار
  newPropertyOffer,

  /// قبول عرض على طلب عقار
  propertyOfferAccepted,

  /// رفض عرض على طلب عقار
  propertyOfferRejected,

  /// تحديث حالة طلب عقار
  propertyRequestStatusUpdate,

  /// رد على تقييم
  ratingResponse,

  /// إضافة طلب للمفضلة
  requestFavorited,

  /// إشعار عام
  general
}

/// نموذج بيانات الإشعار المباشر
class RealtimeNotificationModel {
  /// معرف الإشعار
  final String id;

  /// معرف المستخدم المستلم
  final String recipientId;

  /// معرف المستخدم المرسل
  final String? senderId;

  /// اسم المستخدم المرسل
  final String? senderName;

  /// صورة المستخدم المرسل
  final String? senderPhotoUrl;

  /// نوع الإشعار
  final RealtimeNotificationType type;

  /// عنوان الإشعار
  final String title;

  /// محتوى الإشعار
  final String body;

  /// بيانات إضافية
  final Map<String, dynamic> data;

  /// تاريخ الإشعار
  final DateTime timestamp;

  /// ما إذا كان الإشعار مقروء
  final bool isRead;

  RealtimeNotificationModel({
    required this.id,
    required this.recipientId,
    this.senderId,
    this.senderName,
    this.senderPhotoUrl,
    required this.type,
    required this.title,
    required this.body,
    required this.data,
    required this.timestamp,
    required this.isRead,
  });

  /// إنشاء نموذج من Map
  factory RealtimeNotificationModel.fromMap(String id, Map<dynamic, dynamic> data) {
    return RealtimeNotificationModel(
      id: id,
      recipientId: data['recipientId'] ?? '',
      senderId: data['senderId'],
      senderName: data['senderName'],
      senderPhotoUrl: data['senderPhotoUrl'],
      type: _getNotificationTypeFromString(data['type'] ?? 'general'),
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      data: Map<String, dynamic>.from(data['data'] ?? {}),
      timestamp: DateTime.fromMillisecondsSinceEpoch(data['timestamp'] ?? 0),
      isRead: data['isRead'] ?? false);
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'recipientId': recipientId,
      'senderId': senderId,
      'senderName': senderName,
      'senderPhotoUrl': senderPhotoUrl,
      'type': type.toString().split('.').last,
      'title': title,
      'body': body,
      'data': data,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isRead': isRead,
    };
  }

  /// الحصول على نوع الإشعار من النص
  static RealtimeNotificationType _getNotificationTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'newMessage':
        return RealtimeNotificationType.newMessage;
      case 'estateStatusUpdate':
        return RealtimeNotificationType.estateStatusUpdate;
      case 'estatePriceChange':
        return RealtimeNotificationType.estatePriceChange;
      case 'newComment':
        return RealtimeNotificationType.newComment;
      case 'newReply':
        return RealtimeNotificationType.newReply;
      case 'newRating':
        return RealtimeNotificationType.newRating;
      case 'requestStatusUpdate':
        return RealtimeNotificationType.requestStatusUpdate;
      case 'newPropertyOffer':
        return RealtimeNotificationType.newPropertyOffer;
      case 'propertyOfferAccepted':
        return RealtimeNotificationType.propertyOfferAccepted;
      case 'propertyOfferRejected':
        return RealtimeNotificationType.propertyOfferRejected;
      case 'propertyRequestStatusUpdate':
        return RealtimeNotificationType.propertyRequestStatusUpdate;
      case 'ratingResponse':
        return RealtimeNotificationType.ratingResponse;
      case 'requestFavorited':
        return RealtimeNotificationType.requestFavorited;
      default:
        return RealtimeNotificationType.general;
    }
  }
}

/// خدمة الإشعارات المباشرة المحسنة
class RealtimeNotificationService {
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // مرجع قاعدة البيانات للإشعارات
  late DatabaseReference _notificationsRef;

  // تدفق الإشعارات
  StreamController<RealtimeNotificationModel>? _notificationsController;
  StreamSubscription<DatabaseEvent>? _notificationsSubscription;

  // عدد الإشعارات غير المقروءة
  int _unreadCount = 0;

  // تحسينات الأداء
  static const int _maxNotificationsPerBatch = 50;
  static const Duration _notificationThrottle = Duration(seconds: 1);

  // تخزين مؤقت للإشعارات
  final Map<String, DateTime> _lastNotificationTime = {};
  final Map<String, int> _notificationCount = {};

  // إعدادات الإشعارات
  final Map<RealtimeNotificationType, bool> _notificationSettings = {
    RealtimeNotificationType.newMessage: true,
    RealtimeNotificationType.newPropertyOffer: true,
    RealtimeNotificationType.propertyOfferAccepted: true,
    RealtimeNotificationType.propertyOfferRejected: true,
    RealtimeNotificationType.newRating: true,
    RealtimeNotificationType.newComment: true,
    RealtimeNotificationType.requestStatusUpdate: true,
    RealtimeNotificationType.ratingResponse: true,
    RealtimeNotificationType.requestFavorited: true,
    RealtimeNotificationType.general: true,
  };

  /// الحصول على تدفق الإشعارات
  Stream<RealtimeNotificationModel>? get notificationsStream => _notificationsController?.stream;

  /// الحصول على عدد الإشعارات غير المقروءة
  int get unreadCount => _unreadCount;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    final user = _auth.currentUser;
    if (user == null) {
      return;
    }

    // تهيئة مرجع قاعدة البيانات
    _notificationsRef = _database.ref().child('notifications').child(user.uid);

    // إنشاء تدفق الإشعارات
    _notificationsController = StreamController<RealtimeNotificationModel>.broadcast();

    // الاستماع للإشعارات الجديدة
    _notificationsSubscription = _notificationsRef
        .orderByChild('timestamp')
        .onChildAdded
        .listen(_handleNewNotification);

    // تحديث عدد الإشعارات غير المقروءة
    await _updateUnreadCount();

    // إعادة تعيين إحصائيات الإرسال
    _resetSendingStats();
  }

  /// إيقاف الخدمة
  Future<void> dispose() async {
    await _notificationsSubscription?.cancel();
    await _notificationsController?.close();

    _notificationsSubscription = null;
    _notificationsController = null;
  }

  /// معالجة إشعار جديد
  void _handleNewNotification(DatabaseEvent event) {
    if (event.snapshot.value == null) {
      return;
    }

    try {
      final data = event.snapshot.value as Map<dynamic, dynamic>;
      final notification = RealtimeNotificationModel.fromMap(
        event.snapshot.key ?? '',
        data);

      // إضافة الإشعار إلى التدفق
      _notificationsController?.add(notification);

      // تحديث عدد الإشعارات غير المقروءة
      if (!notification.isRead) {
        _unreadCount++;
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// تحديث عدد الإشعارات غير المقروءة
  Future<void> _updateUnreadCount() async {
    try {
      final snapshot = await _notificationsRef
          .orderByChild('isRead')
          .equalTo(false)
          .get();

      if (snapshot.value == null) {
        _unreadCount = 0;
        return;
      }

      final data = snapshot.value as Map<dynamic, dynamic>;
      _unreadCount = data.length;
    } catch (e) {
      _unreadCount = 0;
    }
  }

  /// التحقق من إمكانية إرسال الإشعار (تحكم في معدل الإرسال)
  bool _canSendNotification(String recipientId, RealtimeNotificationType type) {
    // التحقق من إعدادات الإشعارات
    if (_notificationSettings[type] != true) {
      return false;
    }

    final key = '${recipientId}_${type.toString()}';
    final now = DateTime.now();

    // التحقق من آخر مرة تم إرسال إشعار من نفس النوع
    if (_lastNotificationTime.containsKey(key)) {
      final lastTime = _lastNotificationTime[key]!;
      if (now.difference(lastTime) < _notificationThrottle) {
        return false;
      }
    }

    // التحقق من عدد الإشعارات المرسلة
    final count = _notificationCount[key] ?? 0;
    if (count >= _maxNotificationsPerBatch) {
      return false;
    }

    return true;
  }

  /// تحديث إحصائيات الإرسال
  void _updateSendingStats(String recipientId, RealtimeNotificationType type) {
    final key = '${recipientId}_${type.toString()}';
    _lastNotificationTime[key] = DateTime.now();
    _notificationCount[key] = (_notificationCount[key] ?? 0) + 1;
  }

  /// إعادة تعيين إحصائيات الإرسال
  void _resetSendingStats() {
    _lastNotificationTime.clear();
    _notificationCount.clear();
  }

  /// تحديث إعدادات الإشعارات
  void updateNotificationSettings(Map<RealtimeNotificationType, bool> settings) {
    _notificationSettings.addAll(settings);
  }

  /// إرسال إشعار مباشر محسن
  Future<bool> sendNotification({
    required String recipientId,
    required RealtimeNotificationType type,
    required String title,
    required String body,
    Map<String, dynamic> data = const {},
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // التحقق من إمكانية الإرسال
      if (!_canSendNotification(recipientId, type)) {
        return false;
      }

      // الحصول على معلومات المستخدم المرسل
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      final userData = userDoc.data();

      final senderName = userData?['name'] as String? ?? user.displayName ?? 'مستخدم';
      final senderPhotoUrl = userData?['photoURL'] as String? ?? user.photoURL;

      // إنشاء الإشعار
      final notification = RealtimeNotificationModel(
        id: '',
        recipientId: recipientId,
        senderId: user.uid,
        senderName: senderName,
        senderPhotoUrl: senderPhotoUrl,
        type: type,
        title: title,
        body: body,
        data: data,
        timestamp: DateTime.now(),
        isRead: false);

      // حفظ الإشعار في قاعدة البيانات
      final recipientNotificationsRef = _database
          .ref()
          .child('notifications')
          .child(recipientId);

      await recipientNotificationsRef.push().set(notification.toMap());

      // تحديث إحصائيات الإرسال
      _updateSendingStats(recipientId, type);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// تعليم إشعار كمقروء
  Future<bool> markAsRead(String notificationId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // تحديث حالة الإشعار
      await _notificationsRef.child(notificationId).update({
        'isRead': true,
      });

      // تحديث عدد الإشعارات غير المقروءة
      if (_unreadCount > 0) {
        _unreadCount--;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// تعليم جميع الإشعارات كمقروءة
  Future<bool> markAllAsRead() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // الحصول على الإشعارات غير المقروءة
      final snapshot = await _notificationsRef
          .orderByChild('isRead')
          .equalTo(false)
          .get();

      if (snapshot.value == null) {
        return true;
      }

      final data = snapshot.value as Map<dynamic, dynamic>;

      // تحديث حالة كل إشعار
      final updates = <String, dynamic>{};
      for (final key in data.keys) {
        updates['$key/isRead'] = true;
      }

      await _notificationsRef.update(updates);

      // إعادة تعيين عدد الإشعارات غير المقروءة
      _unreadCount = 0;

      return true;
    } catch (e) {
      return false;
    }
  }

  /// حذف إشعار
  Future<bool> deleteNotification(String notificationId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // التحقق مما إذا كان الإشعار غير مقروء
      final snapshot = await _notificationsRef.child(notificationId).get();
      if (snapshot.value == null) {
        return false;
      }

      final data = snapshot.value as Map<dynamic, dynamic>;
      final isRead = data['isRead'] as bool? ?? true;

      // حذف الإشعار
      await _notificationsRef.child(notificationId).remove();

      // تحديث عدد الإشعارات غير المقروءة
      if (!isRead && _unreadCount > 0) {
        _unreadCount--;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// حذف جميع الإشعارات
  Future<bool> deleteAllNotifications() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // حذف جميع الإشعارات
      await _notificationsRef.remove();

      // إعادة تعيين عدد الإشعارات غير المقروءة
      _unreadCount = 0;

      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على قائمة الإشعارات
  Future<List<RealtimeNotificationModel>> getNotifications({int limit = 20}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return [];
      }

      final snapshot = await _notificationsRef
          .orderByChild('timestamp')
          .limitToLast(limit)
          .get();

      if (snapshot.value == null) {
        return [];
      }

      final data = snapshot.value as Map<dynamic, dynamic>;

      final notifications = <RealtimeNotificationModel>[];
      for (final entry in data.entries) {
        try {
          final notification = RealtimeNotificationModel.fromMap(
            entry.key,
            entry.value as Map<dynamic, dynamic>);

          notifications.add(notification);
        } catch (e) {
          // تجاهل الإشعار غير الصالح
        }
      }

      // ترتيب الإشعارات من الأحدث إلى الأقدم
      notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      return notifications;
    } catch (e) {
      return [];
    }
  }
}
