import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/models/subscription_model.dart';
import 'package:kuwait_corners/core/services/loyalty_program_service.dart';
import 'package:kuwait_corners/core/services/notification_service.dart';

/// خدمة الاشتراكات والترويج
class SubscriptionService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = NotificationService();
  final LoyaltyProgramService _loyaltyService = LoyaltyProgramService();

  // القيم الافتراضية للترويج
  static const Map<PromotionType, Map<String, dynamic>> _promotionDefaults = {
    PromotionType.featured: {
      'price': 2.99,
      'durationDays': 7,
      'description': 'إعلان بارز يظهر في قسم الإعلانات البارزة',
    },
    PromotionType.pinned: {
      'price': 4.99,
      'durationDays': 7,
      'description': 'إعلان مثبت في أعلى نتائج البحث',
    },
    PromotionType.highlighted: {
      'price': 3.99,
      'durationDays': 7,
      'description': 'إعلان مميز بخلفية ملونة وشارة مميزة',
    },
    PromotionType.video: {
      'price': 5.99,
      'durationDays': 7,
      'description': 'إضافة فيديو للإعلان',
    },
    PromotionType.premium: {
      'price': 9.99,
      'durationDays': 14,
      'description': 'حزمة متكاملة تشمل جميع ميزات الترويج',
    },
  };

  // القيم الافتراضية للاشتراكات
  static const Map<SubscriptionType, Map<String, dynamic>>
      _subscriptionDefaults = {
    SubscriptionType.free: {
      'allowedAds': 3,
      'allowedImagesPerAd': 5,
      'adDurationDays': 30,
      'price': 0.0,
    },
    SubscriptionType.monthly: {
      'allowedAds': 15,
      'allowedImagesPerAd': 10,
      'adDurationDays': 60,
      'price': 9.99,
    },
    SubscriptionType.yearly: {
      'allowedAds': 100,
      'allowedImagesPerAd': 20,
      'adDurationDays': 90,
      'price': 99.99,
    },
  };

  /// الحصول على اشتراك المستخدم الحالي
  Future<SubscriptionModel?> getCurrentSubscription() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return null;
      }

      final snapshot = await _firestore
          .collection('subscriptions')
          .where('userId', isEqualTo: user.uid)
          .where('isActive', isEqualTo: true)
          .orderBy('endDate', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) {
        // إنشاء اشتراك مجاني افتراضي إذا لم يكن هناك اشتراك نشط
        return await _createFreeSubscription(user.uid);
      }

      final subscription = SubscriptionModel.fromFirestore(snapshot.docs.first);

      // التحقق من انتهاء الاشتراك
      if (subscription.isExpired()) {
        await _deactivateSubscription(subscription.id);

        // إنشاء اشتراك مجاني جديد
        return await _createFreeSubscription(user.uid);
      }

      // التحقق من قرب انتهاء الاشتراك
      if (subscription.isExpiringSoon() &&
          subscription.type != SubscriptionType.free) {
        // إرسال إشعار بقرب انتهاء الاشتراك
        await _notificationService.sendSubscriptionExpiringNotification(
          user.uid,
          subscription.endDate);
      }

      // التحقق من انخفاض عدد الإعلانات المتبقية
      if (subscription.hasLowRemainingAds() &&
          subscription.type != SubscriptionType.free) {
        // إرسال إشعار بانخفاض عدد الإعلانات المتبقية
        await _notificationService.sendLowRemainingAdsNotification(
          user.uid,
          subscription.remainingAds);
      }

      return subscription;
    } catch (e) {
      debugPrint('خطأ في الحصول على الاشتراك الحالي: $e');
      return null;
    }
  }

  /// إنشاء اشتراك مجاني
  Future<SubscriptionModel> _createFreeSubscription(String userId) async {
    try {
      final defaults = _subscriptionDefaults[SubscriptionType.free]!;

      final subscriptionId = _firestore.collection('subscriptions').doc().id;

      final subscription = SubscriptionModel(
        id: subscriptionId,
        userId: userId,
        type: SubscriptionType.free,
        startDate: DateTime.now(),
        endDate: DateTime.now()
            .add(const Duration(days: 365)), // اشتراك مجاني لمدة سنة
        allowedAds: defaults['allowedAds'],
        remainingAds: defaults['allowedAds'],
        allowedImagesPerAd: defaults['allowedImagesPerAd'],
        adDurationDays: defaults['adDurationDays'],
        isActive: true,
        autoRenew: true,
        price: defaults['price'],
        currency: 'KWD',
        metadata: {});

      await _firestore
          .collection('subscriptions')
          .doc(subscriptionId)
          .set(subscription.toFirestore());

      return subscription;
    } catch (e) {
      debugPrint('خطأ في إنشاء اشتراك مجاني: $e');

      // إرجاع اشتراك مجاني افتراضي في حالة الخطأ
      return SubscriptionModel(
        id: 'default',
        userId: userId,
        type: SubscriptionType.free,
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 365)),
        allowedAds: 3,
        remainingAds: 3,
        allowedImagesPerAd: 5,
        adDurationDays: 30,
        isActive: true,
        autoRenew: true,
        price: 0.0,
        currency: 'KWD',
        metadata: {});
    }
  }

  /// إلغاء تنشيط الاشتراك
  Future<void> _deactivateSubscription(String subscriptionId) async {
    try {
      await _firestore.collection('subscriptions').doc(subscriptionId).update({
        'isActive': false,
      });
    } catch (e) {
      debugPrint('خطأ في إلغاء تنشيط الاشتراك: $e');
    }
  }

  /// الحصول على قائمة الاشتراكات المتاحة
  Future<List<Map<String, dynamic>>> getAvailableSubscriptions() async {
    final List<Map<String, dynamic>> subscriptions = [];

    // إضافة الاشتراك المجاني
    subscriptions.add({
      'type': SubscriptionType.free,
      'name': 'اشتراك مجاني',
      'description':
          'اشتراك مجاني يتيح لك نشر ${_subscriptionDefaults[SubscriptionType.free]!['allowedAds']} إعلانات شهرياً',
      'allowedAds': _subscriptionDefaults[SubscriptionType.free]!['allowedAds'],
      'allowedImagesPerAd':
          _subscriptionDefaults[SubscriptionType.free]!['allowedImagesPerAd'],
      'adDurationDays':
          _subscriptionDefaults[SubscriptionType.free]!['adDurationDays'],
      'price': _subscriptionDefaults[SubscriptionType.free]!['price'],
      'currency': 'KWD',
      'features': [
        'نشر ${_subscriptionDefaults[SubscriptionType.free]!['allowedAds']} إعلانات شهرياً',
        'إضافة ${_subscriptionDefaults[SubscriptionType.free]!['allowedImagesPerAd']} صور لكل إعلان',
        'عرض الإعلان لمدة ${_subscriptionDefaults[SubscriptionType.free]!['adDurationDays']} يوم',
      ],
    });

    // إضافة الاشتراك الشهري
    subscriptions.add({
      'type': SubscriptionType.monthly,
      'name': 'اشتراك شهري',
      'description':
          'اشتراك شهري يتيح لك نشر ${_subscriptionDefaults[SubscriptionType.monthly]!['allowedAds']} إعلان شهرياً',
      'allowedAds':
          _subscriptionDefaults[SubscriptionType.monthly]!['allowedAds'],
      'allowedImagesPerAd': _subscriptionDefaults[SubscriptionType.monthly]![
          'allowedImagesPerAd'],
      'adDurationDays':
          _subscriptionDefaults[SubscriptionType.monthly]!['adDurationDays'],
      'price': _subscriptionDefaults[SubscriptionType.monthly]!['price'],
      'currency': 'KWD',
      'features': [
        'نشر ${_subscriptionDefaults[SubscriptionType.monthly]!['allowedAds']} إعلان شهرياً',
        'إضافة ${_subscriptionDefaults[SubscriptionType.monthly]!['allowedImagesPerAd']} صور لكل إعلان',
        'عرض الإعلان لمدة ${_subscriptionDefaults[SubscriptionType.monthly]!['adDurationDays']} يوم',
        'ظهور متميز في نتائج البحث',
        'إمكانية تجديد الإعلانات تلقائياً',
        'إحصائيات أساسية للإعلانات',
      ],
    });

    // إضافة الاشتراك السنوي
    subscriptions.add({
      'type': SubscriptionType.yearly,
      'name': 'اشتراك سنوي',
      'description':
          'اشتراك سنوي يتيح لك نشر ${_subscriptionDefaults[SubscriptionType.yearly]!['allowedAds']} إعلان سنوياً',
      'allowedAds':
          _subscriptionDefaults[SubscriptionType.yearly]!['allowedAds'],
      'allowedImagesPerAd':
          _subscriptionDefaults[SubscriptionType.yearly]!['allowedImagesPerAd'],
      'adDurationDays':
          _subscriptionDefaults[SubscriptionType.yearly]!['adDurationDays'],
      'price': _subscriptionDefaults[SubscriptionType.yearly]!['price'],
      'currency': 'KWD',
      'features': [
        'نشر ${_subscriptionDefaults[SubscriptionType.yearly]!['allowedAds']} إعلان سنوياً',
        'إضافة ${_subscriptionDefaults[SubscriptionType.yearly]!['allowedImagesPerAd']} صور لكل إعلان',
        'عرض الإعلان لمدة ${_subscriptionDefaults[SubscriptionType.yearly]!['adDurationDays']} يوم',
        'ظهور متميز في نتائج البحث وفي الصفحة الرئيسية',
        'إحصائيات متقدمة للإعلانات',
        'دعم فني متميز',
        'خصم على الإضافات المدفوعة',
      ],
    });

    return subscriptions;
  }

  /// شراء اشتراك جديد
  Future<SubscriptionModel?> purchaseSubscription(
      SubscriptionType type, String paymentId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return null;
      }

      // إلغاء تنشيط الاشتراكات النشطة الحالية
      final currentSubscription = await getCurrentSubscription();
      if (currentSubscription != null) {
        await _deactivateSubscription(currentSubscription.id);
      }

      final defaults = _subscriptionDefaults[type]!;

      final subscriptionId = _firestore.collection('subscriptions').doc().id;

      // تحديد تاريخ البدء والانتهاء
      final startDate = DateTime.now();
      final endDate = type == SubscriptionType.monthly
          ? startDate.add(const Duration(days: 30))
          : startDate.add(const Duration(days: 365));

      final subscription = SubscriptionModel(
        id: subscriptionId,
        userId: user.uid,
        type: type,
        startDate: startDate,
        endDate: endDate,
        allowedAds: defaults['allowedAds'],
        remainingAds: defaults['allowedAds'],
        allowedImagesPerAd: defaults['allowedImagesPerAd'],
        adDurationDays: defaults['adDurationDays'],
        isActive: true,
        autoRenew: true,
        paymentId: paymentId,
        price: defaults['price'],
        currency: 'KWD',
        lastRenewalDate: startDate,
        nextRenewalDate: endDate,
        metadata: {});

      await _firestore
          .collection('subscriptions')
          .doc(subscriptionId)
          .set(subscription.toFirestore());

      // إرسال إشعار بنجاح عملية الشراء
      await _notificationService.sendPaymentSuccessNotification(
        user.uid,
        paymentId,
        defaults['price'],
        'اشتراك ${subscription.getSubscriptionTypeName()}');

      // إضافة نقاط برنامج الولاء للمستخدم عند شراء باقة إعلانات
      await _loyaltyService.addPointsForPurchasingPlan();

      return subscription;
    } catch (e) {
      debugPrint('خطأ في شراء اشتراك جديد: $e');
      return null;
    }
  }

  /// تجديد الاشتراك
  Future<bool> renewSubscription(
      String subscriptionId, String paymentId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      final doc = await _firestore
          .collection('subscriptions')
          .doc(subscriptionId)
          .get();
      if (!doc.exists) {
        return false;
      }

      final subscription = SubscriptionModel.fromFirestore(doc);

      // تحديد تاريخ الانتهاء الجديد
      final newEndDate = subscription.type == SubscriptionType.monthly
          ? DateTime.now().add(const Duration(days: 30))
          : DateTime.now().add(const Duration(days: 365));

      await _firestore.collection('subscriptions').doc(subscriptionId).update({
        'endDate': Timestamp.fromDate(newEndDate),
        'remainingAds': _subscriptionDefaults[subscription.type]!['allowedAds'],
        'isActive': true,
        'lastRenewalDate': Timestamp.fromDate(DateTime.now()),
        'nextRenewalDate': Timestamp.fromDate(newEndDate),
        'paymentId': paymentId,
      });

      // إرسال إشعار بنجاح عملية التجديد
      await _notificationService.sendSubscriptionRenewedNotification(
        user.uid,
        newEndDate,
        subscription.getSubscriptionTypeName());

      return true;
    } catch (e) {
      debugPrint('خطأ في تجديد الاشتراك: $e');
      return false;
    }
  }

  /// إلغاء الاشتراك
  Future<bool> cancelSubscription(String subscriptionId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      await _firestore.collection('subscriptions').doc(subscriptionId).update({
        'autoRenew': false,
      });

      return true;
    } catch (e) {
      debugPrint('خطأ في إلغاء الاشتراك: $e');
      return false;
    }
  }

  /// تغيير نوع الاشتراك
  Future<SubscriptionModel?> changeSubscriptionType(
      SubscriptionType newType, String paymentId) async {
    return await purchaseSubscription(newType, paymentId);
  }

  /// التحقق من إمكانية إنشاء إعلان جديد
  Future<bool> canCreateNewAd() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        return false;
      }

      return subscription.remainingAds > 0 && !subscription.isExpired();
    } catch (e) {
      debugPrint('خطأ في التحقق من إمكانية إنشاء إعلان جديد: $e');
      return false;
    }
  }

  /// تحديث عدد الإعلانات المتبقية
  Future<bool> decrementRemainingAds() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription == null || subscription.remainingAds <= 0) {
        return false;
      }

      await _firestore.collection('subscriptions').doc(subscription.id).update({
        'remainingAds': FieldValue.increment(-1),
      });

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث عدد الإعلانات المتبقية: $e');
      return false;
    }
  }

  /// الحصول على قائمة الترويجات المتاحة
  Future<List<Map<String, dynamic>>> getAvailablePromotions() async {
    final List<Map<String, dynamic>> promotions = [];

    // إضافة الترويجات المتاحة
    _promotionDefaults.forEach((type, data) {
      promotions.add({
        'type': type,
        'name': _getPromotionTypeName(type),
        'description': data['description'],
        'price': data['price'],
        'durationDays': data['durationDays'],
        'currency': 'KWD',
      });
    });

    return promotions;
  }

  /// الحصول على اسم نوع الترويج بالعربية
  String _getPromotionTypeName(PromotionType type) {
    switch (type) {
      case PromotionType.featured:
        return 'إعلان بارز';
      case PromotionType.pinned:
        return 'إعلان مثبت';
      case PromotionType.highlighted:
        return 'إعلان مميز';
      case PromotionType.video:
        return 'إعلان فيديو';
      case PromotionType.premium:
        return 'إعلان متميز';
    }
  }

  /// شراء ترويج للإعلان
  Future<PromotionModel?> purchasePromotion(
      String adId, PromotionType type, String paymentId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return null;
      }

      final defaults = _promotionDefaults[type]!;

      final promotionId = _firestore.collection('promotions').doc().id;

      // تحديد تاريخ البدء والانتهاء
      final startDate = DateTime.now();
      final endDate = startDate.add(Duration(days: defaults['durationDays']));

      final promotion = PromotionModel(
        id: promotionId,
        adId: adId,
        userId: user.uid,
        type: type,
        startDate: startDate,
        endDate: endDate,
        price: defaults['price'],
        currency: 'KWD',
        paymentId: paymentId,
        isActive: true,
        metadata: {});

      await _firestore
          .collection('promotions')
          .doc(promotionId)
          .set(promotion.toFirestore());

      // تحديث الإعلان بمعلومات الترويج
      await _firestore.collection('estates').doc(adId).update({
        'promotions': FieldValue.arrayUnion([promotionId]),
        'isPromoted': true,
        'promotionType': type.toString().split('.').last,
        'promotionEndDate': Timestamp.fromDate(endDate),
      });

      // إرسال إشعار بنجاح عملية الشراء
      await _notificationService.sendPaymentSuccessNotification(
        user.uid,
        paymentId,
        defaults['price'],
        'ترويج ${_getPromotionTypeName(type)}');

      return promotion;
    } catch (e) {
      debugPrint('خطأ في شراء ترويج للإعلان: $e');
      return null;
    }
  }

  /// الحصول على ترويجات الإعلان
  Future<List<PromotionModel>> getAdPromotions(String adId) async {
    try {
      final snapshot = await _firestore
          .collection('promotions')
          .where('adId', isEqualTo: adId)
          .where('isActive', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => PromotionModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على ترويجات الإعلان: $e');
      return [];
    }
  }

  /// إلغاء ترويج
  Future<bool> cancelPromotion(String promotionId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      final doc =
          await _firestore.collection('promotions').doc(promotionId).get();
      if (!doc.exists) {
        return false;
      }

      final promotion = PromotionModel.fromFirestore(doc);

      await _firestore.collection('promotions').doc(promotionId).update({
        'isActive': false,
      });

      // تحديث الإعلان
      final adSnapshot =
          await _firestore.collection('estates').doc(promotion.adId).get();

      if (adSnapshot.exists) {
        final adData = adSnapshot.data() as Map<String, dynamic>;
        final promotions = List<String>.from(adData['promotions'] ?? []);

        promotions.remove(promotionId);

        await _firestore.collection('estates').doc(promotion.adId).update({
          'promotions': promotions,
          'isPromoted': promotions.isNotEmpty,
          'promotionType': promotions.isEmpty ? null : adData['promotionType'],
          'promotionEndDate':
              promotions.isEmpty ? null : adData['promotionEndDate'],
        });
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في إلغاء الترويج: $e');
      return false;
    }
  }

  /// التحقق من انتهاء الترويجات
  Future<void> checkExpiredPromotions() async {
    try {
      final now = DateTime.now();

      final snapshot = await _firestore
          .collection('promotions')
          .where('isActive', isEqualTo: true)
          .where('endDate', isLessThan: Timestamp.fromDate(now))
          .get();

      final batch = _firestore.batch();

      for (final doc in snapshot.docs) {
        final promotion = PromotionModel.fromFirestore(doc);

        // تعليم الترويج كغير نشط
        batch.update(doc.reference, {'isActive': false});

        // تحديث الإعلان
        final adRef = _firestore.collection('estates').doc(promotion.adId);
        final adSnapshot = await adRef.get();

        if (adSnapshot.exists) {
          final adData = adSnapshot.data() as Map<String, dynamic>;
          final promotions = List<String>.from(adData['promotions'] ?? []);

          promotions.remove(promotion.id);

          batch.update(adRef, {
            'promotions': promotions,
            'isPromoted': promotions.isNotEmpty,
            'promotionType':
                promotions.isEmpty ? null : adData['promotionType'],
            'promotionEndDate':
                promotions.isEmpty ? null : adData['promotionEndDate'],
          });
        }

        // إرسال إشعار بانتهاء الترويج
        await _notificationService.sendPromotionEndedNotification(
          promotion.userId,
          promotion.adId,
          adSnapshot.exists
              ? (adSnapshot.data() as Map<String, dynamic>)['title'] ?? ''
              : '');
      }

      await batch.commit();
    } catch (e) {
      debugPrint('خطأ في التحقق من انتهاء الترويجات: $e');
    }
  }
}
