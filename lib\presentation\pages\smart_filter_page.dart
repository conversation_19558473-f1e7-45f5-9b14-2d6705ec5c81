import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/theme/app_colors.dart';
import '../../data/kuwait_locations.dart';
import '../../domain/models/smart_filter_model.dart';

class SmartFilterPage extends StatefulWidget {
  final SmartFilterModel initialFilter;
  final Function(SmartFilterModel) onApplyFilter;

  const SmartFilterPage({
    super.key,
    required this.initialFilter,
    required this.onApplyFilter,
  });

  @override
  State<SmartFilterPage> createState() => _SmartFilterPageState();
}

class _SmartFilterPageState extends State<SmartFilterPage>
    with SingleTickerProviderStateMixin {
  late SmartFilterModel _filter;
  late TabController _tabController;

  // أنواع الاستغلال (جميع الأنواع المتاحة)
  final Map<String, String> _usageTypesMap = {
    "للبيع": "sale",
    "للإيجار": "rent",
    "للبدل": "swap",
    "استثمار": "investment",
    "إدارة أملاك": "management",
  };

  // أنواع العقارات (الأنواع المعتمدة في قاعدة البيانات)
  final List<String> _propertyTypes = [
    "شقة",
    "منزل",
    "أرض",
    "مكتب",
    "محل تجاري",
    "مخزن",
  ];



  final Map<String, String> _features = {
    'hasCentralAC': 'تكييف مركزي',
    'hasMaidRoom': 'غرفة خادمة',
    'hasGarage': 'كراج',
    'hasSwimmingPool': 'مسبح',
    'hasElevator': 'مصعد',
    'isFullyFurnished': 'مفروش بالكامل',
    'hasGarden': 'حديقة',
    'hasBalcony': 'شرفة',
  };

  @override
  void initState() {
    super.initState();
    _filter = widget.initialFilter.copyWith();
    _tabController = TabController(length: 4, vsync: this);

    // إضافة المميزات إلى الفلتر إذا لم تكن موجودة
    final Map<String, bool> features = Map.from(_filter.features);
    for (final feature in _features.keys) {
      if (!features.containsKey(feature)) {
        features[feature] = false;
      }
    }
    _filter = _filter.copyWith(features: features);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'الفلتر الذكي',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _filter = const SmartFilterModel();
              });
            },
            child: Text(
              'إعادة تعيين',
              style: GoogleFonts.cairo(
                color: AppColors.primary,
                fontWeight: FontWeight.w600))),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: Colors.grey,
          indicatorColor: AppColors.primary,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
          tabs: const [
            Tab(text: 'النوع'),
            Tab(text: 'الموقع'),
            Tab(text: 'التفاصيل'),
            Tab(text: 'المميزات'),
          ])),
      body: Column(
        children: [
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTypeTab(),
                _buildLocationTab(),
                _buildDetailsTab(),
                _buildFeaturesTab(),
              ])),
          _buildBottomActions(),
        ]));
  }

  Widget _buildTypeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // نوع الاستغلال
          _buildSectionTitle('نوع الاستغلال'),
          const SizedBox(height: 8),
          _buildUsageTypeSelector(),
          const SizedBox(height: 24),

          // نوع العقار
          _buildSectionTitle('نوع العقار'),
          const SizedBox(height: 8),
          _buildPropertyTypeSelector(),
        ]));
  }

  Widget _buildLocationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // المحافظة
          _buildSectionTitle('المحافظة'),
          const SizedBox(height: 8),
          _buildDropdown(
            value: _filter.governorate,
            items: KuwaitLocations.governorates,
            hint: 'اختر المحافظة',
            onChanged: (value) {
              setState(() {
                _filter = _filter.copyWith(governorate: value);
              });
            }),
          const SizedBox(height: 24),

          // المنطقة (تظهر فقط عند اختيار محافظة)
          if (_filter.governorate != null) ...[
            _buildSectionTitle('المنطقة'),
            const SizedBox(height: 8),
            _buildDropdown(
              value: _filter.area,
              items: KuwaitLocations.getAreasByGovernorate(_filter.governorate!),
              hint: 'اختر المنطقة',
              onChanged: (value) {
                setState(() {
                  _filter = _filter.copyWith(area: value);
                });
              }),
          ],
        ]));
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // السعر
          _buildSectionTitle('السعر (د.ك)'),
          const SizedBox(height: 8),
          _buildPriceRange(),
          const SizedBox(height: 24),

          // المساحة
          _buildSectionTitle('المساحة (م²)'),
          const SizedBox(height: 8),
          _buildAreaRange(),
          const SizedBox(height: 24),

          // عدد الغرف
          _buildSectionTitle('عدد الغرف'),
          const SizedBox(height: 8),
          _buildNumberSelector(
            value: _filter.numberOfRooms,
            onChanged: (value) {
              setState(() {
                _filter = _filter.copyWith(numberOfRooms: value);
              });
            }),
          const SizedBox(height: 24),

          // عدد الحمامات
          _buildSectionTitle('عدد الحمامات'),
          const SizedBox(height: 8),
          _buildNumberSelector(
            value: _filter.numberOfBathrooms,
            onChanged: (value) {
              setState(() {
                _filter = _filter.copyWith(numberOfBathrooms: value);
              });
            }),
        ]));
  }

  Widget _buildFeaturesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('المميزات'),
          const SizedBox(height: 16),
          ..._features.entries.map((entry) {
            return CheckboxListTile(
              title: Text(
                entry.value,
                style: GoogleFonts.cairo()),
              value: _filter.features[entry.key] ?? false,
              onChanged: (value) {
                setState(() {
                  final features = Map<String, bool>.from(_filter.features);
                  features[entry.key] = value ?? false;
                  _filter = _filter.copyWith(features: features);
                });
              },
              activeColor: AppColors.primary);
          }),
        ]));
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.grey.shade800));
  }

  Widget _buildUsageTypeSelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _usageTypesMap.keys.map((type) {
        final isSelected = _filter.usageType == _usageTypesMap[type];
        return FilterChip(
          label: Text(type),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _filter = _filter.copyWith(
                usageType: selected ? _usageTypesMap[type] : null);
            });
          },
          selectedColor: AppColors.primary.withValues(alpha: 0.2),
          checkmarkColor: AppColors.primary,
          labelStyle: GoogleFonts.cairo(
            color: isSelected ? AppColors.primary : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal));
      }).toList());
  }

  Widget _buildPropertyTypeSelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _propertyTypes.map((type) {
        final isSelected = _filter.propertyType == type;
        return FilterChip(
          label: Text(type),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _filter = _filter.copyWith(
                propertyType: selected ? type : null);
            });
          },
          selectedColor: AppColors.primary.withValues(alpha: 0.2),
          checkmarkColor: AppColors.primary,
          labelStyle: GoogleFonts.cairo(
            color: isSelected ? AppColors.primary : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal));
      }).toList());
  }



  Widget _buildDropdown({
    required String? value,
    required List<String> items,
    required String hint,
    required Function(String?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300)),
      child: DropdownButtonFormField<String>(
        value: value,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          hintText: hint,
          hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500)),
        items: items.map((item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: GoogleFonts.cairo()));
        }).toList(),
        onChanged: onChanged,
        style: GoogleFonts.cairo(color: Colors.black)));
  }

  Widget _buildTextField({
    required String? value,
    required String hint,
    required Function(String) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300)),
      child: TextFormField(
        initialValue: value,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          hintText: hint,
          hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500)),
        style: GoogleFonts.cairo(),
        onChanged: onChanged));
  }

  Widget _buildPriceRange() {
    return Column(
      children: [
        // نطاق السعر المحدث (50 - 10,000 د.ك)
        Text(
          'النطاق: 50 - 10,000 د.ك',
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.grey.shade600)),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                value: _filter.minPrice?.toString(),
                hint: 'الحد الأدنى (50)',
                onChanged: (value) {
                  final price = double.tryParse(value);
                  if (price == null || (price >= 50 && price <= 10000)) {
                    setState(() {
                      _filter = _filter.copyWith(
                        minPrice: value.isEmpty ? null : price);
                    });
                  }
                })),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTextField(
                value: _filter.maxPrice?.toString(),
                hint: 'الحد الأقصى (10000)',
                onChanged: (value) {
                  final price = double.tryParse(value);
                  if (price == null || (price >= 50 && price <= 10000)) {
                    setState(() {
                      _filter = _filter.copyWith(
                        maxPrice: value.isEmpty ? null : price);
                    });
                  }
                })),
          ]),
      ]);
  }

  Widget _buildAreaRange() {
    return Row(
      children: [
        Expanded(
          child: _buildTextField(
            value: _filter.minArea?.toString(),
            hint: 'الحد الأدنى',
            onChanged: (value) {
              setState(() {
                _filter = _filter.copyWith(
                  minArea: value.isEmpty ? null : double.tryParse(value));
              });
            })),
        const SizedBox(width: 16),
        Expanded(
          child: _buildTextField(
            value: _filter.maxArea?.toString(),
            hint: 'الحد الأقصى',
            onChanged: (value) {
              setState(() {
                _filter = _filter.copyWith(
                  maxArea: value.isEmpty ? null : double.tryParse(value));
              });
            })),
      ]);
  }

  Widget _buildNumberSelector({
    required int? value,
    required Function(int?) onChanged,
  }) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        FilterChip(
          label: const Text('أي عدد'),
          selected: value == null,
          onSelected: (selected) {
            if (selected) onChanged(null);
          },
          selectedColor: AppColors.primary.withValues(alpha: 0.2),
          checkmarkColor: AppColors.primary,
          labelStyle: GoogleFonts.cairo(
            color: value == null ? AppColors.primary : Colors.grey.shade700,
            fontWeight: value == null ? FontWeight.w600 : FontWeight.normal)),
        ...List.generate(6, (index) {
          final number = index + 1;
          final isSelected = value == number;
          return FilterChip(
            label: Text('$number'),
            selected: isSelected,
            onSelected: (selected) {
              onChanged(selected ? number : null);
            },
            selectedColor: AppColors.primary.withValues(alpha: 0.2),
            checkmarkColor: AppColors.primary,
            labelStyle: GoogleFonts.cairo(
              color: isSelected ? AppColors.primary : Colors.grey.shade700,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal));
        }),
      ]);
  }

  Widget _buildBottomActions() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade300,
            blurRadius: 10,
            offset: const Offset(0, -2)),
        ]),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // زر الإغلاق
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    side: BorderSide(color: AppColors.primary),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8))),
                  child: Text(
                    'إغلاق',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.w600,
                      fontSize: 14)))),
              const SizedBox(width: 8),
              // زر إعادة التعيين
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _filter = const SmartFilterModel();
                    });
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.grey.shade600,
                    side: BorderSide(color: Colors.grey.shade400),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8))),
                  child: Text(
                    'إعادة تعيين',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.w600,
                      fontSize: 14)))),
              const SizedBox(width: 8),
              // زر التطبيق
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: () {
                    widget.onApplyFilter(_filter);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                    elevation: 0),
                  child: Text(
                    'تطبيق الفلتر',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.w600,
                      fontSize: 14)))),
            ]))));
  }
}