// lib/core/models/plan_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// أنواع الباقات المتاحة
enum PlanType {
  free,
  bronze,
  silver,
  gold,
}

/// نموذج بيانات الباقة
class PlanModel {
  /// نوع الباقة
  final PlanType type;
  
  /// معرف الباقة
  final String id;
  
  /// اسم الباقة بالعربية
  final String nameAr;
  
  /// اسم الباقة بالإنجليزية
  final String nameEn;
  
  /// سعر الباقة
  final double price;
  
  /// مدة الباقة بالأيام
  final int durationDays;
  
  /// الحد الأقصى لعدد الإعلانات
  final int maxAds;
  
  /// الحد الأقصى لعدد الصور لكل إعلان
  final int maxImagesPerAd;
  
  /// مدة عرض الإعلان بالأيام
  final int adDurationDays;
  
  /// ميزات الباقة
  final List<String> features;
  
  /// الميزات المسموح بها
  final Map<String, bool> allowedFeatures;
  
  /// لون الباقة
  final Color color;
  
  /// وصف الباقة
  final String description;
  
  /// ما إذا كانت الباقة الافتراضية
  final bool isDefault;
  
  /// ما إذا كانت الباقة نشطة
  final bool isActive;

  const PlanModel({
    required this.type,
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.price,
    required this.durationDays,
    required this.maxAds,
    required this.maxImagesPerAd,
    required this.adDurationDays,
    required this.features,
    required this.allowedFeatures,
    required this.color,
    required this.description,
    this.isDefault = false,
    this.isActive = true,
  });

  /// إنشاء نموذج من بيانات Firestore
  factory PlanModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return PlanModel(
      type: _getPlanTypeFromString(data['type'] ?? 'free'),
      id: doc.id,
      nameAr: data['nameAr'] ?? '',
      nameEn: data['nameEn'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      durationDays: data['durationDays'] ?? 30,
      maxAds: data['maxAds'] ?? 0,
      maxImagesPerAd: data['maxImagesPerAd'] ?? 0,
      adDurationDays: data['adDurationDays'] ?? 30,
      features: List<String>.from(data['features'] ?? []),
      allowedFeatures: Map<String, bool>.from(data['allowedFeatures'] ?? {}),
      color: _getColorFromHex(data['colorHex'] ?? '#CCCCCC'),
      description: data['description'] ?? '',
      isDefault: data['isDefault'] ?? false,
      isActive: data['isActive'] ?? true);
  }

  /// تحويل النموذج إلى بيانات Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'type': type.toString().split('.').last,
      'nameAr': nameAr,
      'nameEn': nameEn,
      'price': price,
      'durationDays': durationDays,
      'maxAds': maxAds,
      'maxImagesPerAd': maxImagesPerAd,
      'adDurationDays': adDurationDays,
      'features': features,
      'allowedFeatures': allowedFeatures,
      'colorHex': _getHexFromColor(color),
      'description': description,
      'isDefault': isDefault,
      'isActive': isActive,
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  /// الحصول على نسخة محدثة من النموذج
  PlanModel copyWith({
    PlanType? type,
    String? id,
    String? nameAr,
    String? nameEn,
    double? price,
    int? durationDays,
    int? maxAds,
    int? maxImagesPerAd,
    int? adDurationDays,
    List<String>? features,
    Map<String, bool>? allowedFeatures,
    Color? color,
    String? description,
    bool? isDefault,
    bool? isActive,
  }) {
    return PlanModel(
      type: type ?? this.type,
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      price: price ?? this.price,
      durationDays: durationDays ?? this.durationDays,
      maxAds: maxAds ?? this.maxAds,
      maxImagesPerAd: maxImagesPerAd ?? this.maxImagesPerAd,
      adDurationDays: adDurationDays ?? this.adDurationDays,
      features: features ?? this.features,
      allowedFeatures: allowedFeatures ?? this.allowedFeatures,
      color: color ?? this.color,
      description: description ?? this.description,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive);
  }

  /// الحصول على نوع الباقة من النص
  static PlanType _getPlanTypeFromString(String typeStr) {
    switch (typeStr.toLowerCase()) {
      case 'bronze':
      case 'nahas':
      case 'نحاسي':
      case 'basic':
        return PlanType.bronze;
      case 'silver':
      case 'fidda':
      case 'فضي':
      case 'premium':
        return PlanType.silver;
      case 'gold':
      case 'thahab':
      case 'ذهبي':
      case 'vip':
        return PlanType.gold;
      case 'free':
      case 'مجاني':
      default:
        return PlanType.free;
    }
  }

  /// الحصول على لون من رمز HEX
  static Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  /// الحصول على رمز HEX من لون
  static String _getHexFromColor(Color color) {
    return '#${color.value.toRadixString(16).substring(2)}';
  }

  /// الحصول على اسم الباقة
  String get name => nameAr;

  /// التحقق مما إذا كانت الميزة مسموح بها
  bool isFeatureAllowed(String featureId) {
    return allowedFeatures[featureId] ?? false;
  }

  /// الحصول على الباقات الافتراضية
  static List<PlanModel> getDefaultPlans() {
    return [
      PlanModel(
        type: PlanType.free,
        id: 'free',
        nameAr: 'الباقة المجانية',
        nameEn: 'Free Plan',
        price: 0.0,
        durationDays: 30,
        maxAds: 3,
        maxImagesPerAd: 5,
        adDurationDays: 30,
        features: [
          'نشر 3 إعلانات شهرياً',
          'إضافة 5 صور لكل إعلان',
          'عرض الإعلان لمدة 30 يوم',
          'دعم فني عبر البريد الإلكتروني',
        ],
        allowedFeatures: {
          'autoRepublish': false,
          'kuwaitCornersPin': false,
          'movingAd': false,
          'vipBadge': false,
          'pinnedOnHome': false,
        },
        color: Colors.grey,
        description: 'باقة مجانية لتجربة الخدمة',
        isDefault: true),
      PlanModel(
        type: PlanType.bronze,
        id: 'bronze',
        nameAr: 'الباقة النحاسية',
        nameEn: 'Bronze Plan',
        price: 10.0,
        durationDays: 30,
        maxAds: 10,
        maxImagesPerAd: 10,
        adDurationDays: 30,
        features: [
          'نشر 10 إعلانات شهرياً',
          'إضافة 10 صور لكل إعلان',
          'عرض الإعلان لمدة 30 يوم',
          'دعم فني على مدار الساعة',
        ],
        allowedFeatures: {
          'autoRepublish': true,
          'kuwaitCornersPin': false,
          'movingAd': false,
          'vipBadge': false,
          'pinnedOnHome': false,
        },
        color: Color(0xFFCD7F32),
        description: 'باقة اقتصادية للمعلنين الجدد',
        isDefault: false),
      PlanModel(
        type: PlanType.silver,
        id: 'silver',
        nameAr: 'الباقة الفضية',
        nameEn: 'Silver Plan',
        price: 20.0,
        durationDays: 30,
        maxAds: 20,
        maxImagesPerAd: 15,
        adDurationDays: 45,
        features: [
          'نشر 20 إعلان شهرياً',
          'إضافة 15 صور لكل إعلان',
          'عرض الإعلان لمدة 45 يوم',
          'إمكانية إضافة فيديو للإعلان',
          'تثبيت إعلان واحد في القسم',
          'دعم فني على مدار الساعة',
        ],
        allowedFeatures: {
          'autoRepublish': true,
          'kuwaitCornersPin': true,
          'movingAd': true,
          'vipBadge': false,
          'pinnedOnHome': false,
        },
        color: Color(0xFFC0C0C0),
        description: 'باقة متميزة للمعلنين المحترفين',
        isDefault: false),
      PlanModel(
        type: PlanType.gold,
        id: 'gold',
        nameAr: 'الباقة الذهبية',
        nameEn: 'Gold Plan',
        price: 30.0,
        durationDays: 30,
        maxAds: 50,
        maxImagesPerAd: 20,
        adDurationDays: 60,
        features: [
          'نشر 50 إعلان شهرياً',
          'إضافة 20 صور لكل إعلان',
          'عرض الإعلان لمدة 60 يوم',
          'إمكانية إضافة فيديو للإعلان',
          'تثبيت 3 إعلانات في الأقسام',
          'تثبيت إعلان في الصفحة الرئيسية',
          'شارة VIP على الإعلانات',
          'دعم فني مخصص',
        ],
        allowedFeatures: {
          'autoRepublish': true,
          'kuwaitCornersPin': true,
          'movingAd': true,
          'vipBadge': true,
          'pinnedOnHome': true,
        },
        color: Color(0xFFFFD700),
        description: 'باقة متكاملة للوكلاء والشركات العقارية',
        isDefault: false),
    ];
  }
}
