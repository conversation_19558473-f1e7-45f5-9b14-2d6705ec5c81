import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';

/// صفحة تشخيص العقارات لمعرفة سبب عدم عرض العقارات
class DebugEstatesPage extends StatefulWidget {
  const DebugEstatesPage({super.key});

  @override
  State<DebugEstatesPage> createState() => _DebugEstatesPageState();
}

class _DebugEstatesPageState extends State<DebugEstatesPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تشخيص العقارات',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection('جميع العقارات', _getAllEstates()),
            const SizedBox(height: 24),
            _buildSection('عقارات الإيجار فقط', _getRentalEstates()),
            const SizedBox(height: 24),
            _buildSection('عقارات الإيجار المدفوعة', _getPaidRentalEstates()),
            const SizedBox(height: 24),
            _buildSection('عقارات الإيجار (استبعاد المستخدم الحالي)', _getRentalEstatesExcludingCurrentUser()),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, Future<QuerySnapshot> future) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.primary),
        ),
        const SizedBox(height: 12),
        FutureBuilder<QuerySnapshot>(
          future: future,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red),
                ),
                child: Text(
                  'خطأ: ${snapshot.error}',
                  style: GoogleFonts.cairo(color: Colors.red),
                ),
              );
            }

            final docs = snapshot.data?.docs ?? [];
            
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.border),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'عدد العقارات: ${docs.length}',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary),
                  ),
                  const SizedBox(height: 12),
                  if (docs.isEmpty)
                    Text(
                      'لا توجد عقارات',
                      style: GoogleFonts.cairo(
                        color: AppColors.textSecondary),
                    )
                  else
                    ...docs.take(5).map((doc) {
                      final data = doc.data() as Map<String, dynamic>;
                      return Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: AppColors.border.withValues(alpha: 0.5)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              data['title'] ?? 'بدون عنوان',
                              style: GoogleFonts.cairo(
                                fontWeight: FontWeight.w600,
                                fontSize: 14),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'mainCategory: ${data['mainCategory'] ?? 'غير محدد'}',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: AppColors.textSecondary),
                            ),
                            Text(
                              'subCategory: ${data['subCategory'] ?? 'غير محدد'}',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: AppColors.textSecondary),
                            ),
                            Text(
                              'isActive: ${data['isActive'] ?? false}',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: data['isActive'] == true ? Colors.green : Colors.red),
                            ),
                            Text(
                              'ownerId: ${data['ownerId'] ?? 'غير محدد'}',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: AppColors.textSecondary),
                            ),
                          ],
                        ),
                      );
                    }),
                  if (docs.length > 5)
                    Text(
                      '... و ${docs.length - 5} عقار آخر',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: AppColors.textSecondary),
                    ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Future<QuerySnapshot> _getAllEstates() {
    return FirebaseFirestore.instance
        .collection('estates')
        .limit(20)
        .get();
  }

  Future<QuerySnapshot> _getRentalEstates() {
    return FirebaseFirestore.instance
        .collection('estates')
        .where('mainCategory', isEqualTo: 'عقار للايجار')
        .limit(20)
        .get();
  }

  Future<QuerySnapshot> _getPaidRentalEstates() {
    return FirebaseFirestore.instance
        .collection('estates')
        .where('mainCategory', isEqualTo: 'عقار للايجار')
        .where('isPaymentVerified', isEqualTo: true)
        .limit(20)
        .get();
  }

  Future<QuerySnapshot> _getRentalEstatesExcludingCurrentUser() {
    final currentUser = FirebaseAuth.instance.currentUser;
    return FirebaseFirestore.instance
        .collection('estates')
        .where('mainCategory', isEqualTo: 'عقار للايجار')
        .where('isActive', isEqualTo: true)
        .where('ownerId', isNotEqualTo: currentUser?.uid)
        .limit(20)
        .get();
  }
}
