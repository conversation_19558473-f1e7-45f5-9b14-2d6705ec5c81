import 'package:cloud_firestore/cloud_firestore.dart';

/// نوع الرسالة
enum MessageType {
  /// رسالة نصية
  text,
  
  /// صورة
  image,
  
  /// ملف
  file,
  
  /// موقع
  location,
  
  /// عقار
  estate,
  
  /// عرض
  offer,
}

/// حالة الرسالة
enum MessageStatus {
  /// تم الإرسال
  sent,
  
  /// تم التسليم
  delivered,
  
  /// تم القراءة
  read,
  
  /// فشل الإرسال
  failed,
}

/// نموذج الرسالة
class Message {
  /// معرف الرسالة
  final String id;
  
  /// معرف المحادثة
  final String conversationId;
  
  /// معرف المرسل
  final String senderId;
  
  /// اسم المرسل
  final String senderName;
  
  /// صورة المرسل
  final String? senderImage;
  
  /// معرف المستلم
  final String receiverId;
  
  /// اسم المستلم
  final String receiverName;
  
  /// صورة المستلم
  final String? receiverImage;
  
  /// محتوى الرسالة
  final String content;
  
  /// نوع الرسالة
  final MessageType type;
  
  /// حالة الرسالة
  final MessageStatus status;
  
  /// وقت الإرسال
  final DateTime timestamp;
  
  /// ما إذا كانت الرسالة مقروءة
  final bool isRead;
  
  /// وقت القراءة
  final DateTime? readTimestamp;
  
  /// بيانات إضافية
  final Map<String, dynamic>? metadata;
  
  /// ما إذا كانت الرسالة محذوفة
  final bool isDeleted;
  
  /// ما إذا كانت الرسالة معدلة
  final bool isEdited;
  
  /// وقت التعديل
  final DateTime? editTimestamp;
  
  /// المحتوى الأصلي قبل التعديل
  final String? originalContent;
  
  /// ما إذا كانت الرسالة مثبتة
  final bool isPinned;
  
  /// ما إذا كانت الرسالة مميزة بنجمة
  final bool isStarred;
  
  /// ما إذا كانت الرسالة مرسلة للأرشيف
  final bool isArchived;
  
  /// ما إذا كانت الرسالة مبلغ عنها
  final bool isReported;
  
  /// سبب الإبلاغ
  final String? reportReason;
  
  /// ردود الفعل على الرسالة
  final Map<String, List<String>>? reactions;
  
  /// الرسالة التي تم الرد عليها
  final Message? replyTo;
  
  /// معرف الرسالة التي تم الرد عليها
  final String? replyToId;
  
  /// المستخدمين المذكورين في الرسالة
  final List<String>? mentionedUsers;
  
  Message({
    required this.id,
    required this.conversationId,
    required this.senderId,
    required this.senderName,
    this.senderImage,
    required this.receiverId,
    required this.receiverName,
    this.receiverImage,
    required this.content,
    required this.type,
    required this.status,
    required this.timestamp,
    this.isRead = false,
    this.readTimestamp,
    this.metadata,
    this.isDeleted = false,
    this.isEdited = false,
    this.editTimestamp,
    this.originalContent,
    this.isPinned = false,
    this.isStarred = false,
    this.isArchived = false,
    this.isReported = false,
    this.reportReason,
    this.reactions,
    this.replyTo,
    this.replyToId,
    this.mentionedUsers,
  });
  
  /// إنشاء نسخة معدلة من الرسالة
  Message copyWith({
    String? id,
    String? conversationId,
    String? senderId,
    String? senderName,
    String? senderImage,
    String? receiverId,
    String? receiverName,
    String? receiverImage,
    String? content,
    MessageType? type,
    MessageStatus? status,
    DateTime? timestamp,
    bool? isRead,
    DateTime? readTimestamp,
    Map<String, dynamic>? metadata,
    bool? isDeleted,
    bool? isEdited,
    DateTime? editTimestamp,
    String? originalContent,
    bool? isPinned,
    bool? isStarred,
    bool? isArchived,
    bool? isReported,
    String? reportReason,
    Map<String, List<String>>? reactions,
    Message? replyTo,
    String? replyToId,
    List<String>? mentionedUsers,
  }) {
    return Message(
      id: id ?? this.id,
      conversationId: conversationId ?? this.conversationId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderImage: senderImage ?? this.senderImage,
      receiverId: receiverId ?? this.receiverId,
      receiverName: receiverName ?? this.receiverName,
      receiverImage: receiverImage ?? this.receiverImage,
      content: content ?? this.content,
      type: type ?? this.type,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      readTimestamp: readTimestamp ?? this.readTimestamp,
      metadata: metadata ?? this.metadata,
      isDeleted: isDeleted ?? this.isDeleted,
      isEdited: isEdited ?? this.isEdited,
      editTimestamp: editTimestamp ?? this.editTimestamp,
      originalContent: originalContent ?? this.originalContent,
      isPinned: isPinned ?? this.isPinned,
      isStarred: isStarred ?? this.isStarred,
      isArchived: isArchived ?? this.isArchived,
      isReported: isReported ?? this.isReported,
      reportReason: reportReason ?? this.reportReason,
      reactions: reactions ?? this.reactions,
      replyTo: replyTo ?? this.replyTo,
      replyToId: replyToId ?? this.replyToId,
      mentionedUsers: mentionedUsers ?? this.mentionedUsers);
  }
  
  /// تحويل الرسالة إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'conversationId': conversationId,
      'senderId': senderId,
      'senderName': senderName,
      'senderImage': senderImage,
      'receiverId': receiverId,
      'receiverName': receiverName,
      'receiverImage': receiverImage,
      'content': content,
      'type': type.index,
      'status': status.index,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isRead': isRead,
      'readTimestamp': readTimestamp?.millisecondsSinceEpoch,
      'metadata': metadata,
      'isDeleted': isDeleted,
      'isEdited': isEdited,
      'editTimestamp': editTimestamp?.millisecondsSinceEpoch,
      'originalContent': originalContent,
      'isPinned': isPinned,
      'isStarred': isStarred,
      'isArchived': isArchived,
      'isReported': isReported,
      'reportReason': reportReason,
      'reactions': reactions,
      'replyToId': replyToId,
      'mentionedUsers': mentionedUsers,
    };
  }
  
  /// إنشاء رسالة من خريطة
  factory Message.fromMap(Map<String, dynamic> map) {
    return Message(
      id: map['id'],
      conversationId: map['conversationId'],
      senderId: map['senderId'],
      senderName: map['senderName'],
      senderImage: map['senderImage'],
      receiverId: map['receiverId'],
      receiverName: map['receiverName'],
      receiverImage: map['receiverImage'],
      content: map['content'],
      type: MessageType.values[map['type']],
      status: MessageStatus.values[map['status']],
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      isRead: map['isRead'] ?? false,
      readTimestamp: map['readTimestamp'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['readTimestamp'])
          : null,
      metadata: map['metadata'],
      isDeleted: map['isDeleted'] ?? false,
      isEdited: map['isEdited'] ?? false,
      editTimestamp: map['editTimestamp'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['editTimestamp'])
          : null,
      originalContent: map['originalContent'],
      isPinned: map['isPinned'] ?? false,
      isStarred: map['isStarred'] ?? false,
      isArchived: map['isArchived'] ?? false,
      isReported: map['isReported'] ?? false,
      reportReason: map['reportReason'],
      reactions: map['reactions'] != null
          ? Map<String, List<String>>.from(
              map['reactions'].map(
                (key, value) => MapEntry(
                  key,
                  List<String>.from(value))))
          : null,
      replyToId: map['replyToId'],
      mentionedUsers: map['mentionedUsers'] != null
          ? List<String>.from(map['mentionedUsers'])
          : null);
  }
  
  /// إنشاء رسالة من وثيقة Firestore
  factory Message.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Message.fromMap({
      'id': doc.id,
      ...data,
    });
  }
  
  /// تحويل الرسالة إلى JSON
  Map<String, dynamic> toJson() => toMap();
  
  /// إنشاء رسالة من JSON
  factory Message.fromJson(Map<String, dynamic> json) => Message.fromMap(json);
}
