import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/routes/app_routes.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/utils/responsive_utils.dart';
import 'package:kuwait_corners/presentation/widgets/realtime_notifications_widget.dart';
import 'package:kuwait_corners/presentation/widgets/responsive_layout.dart';

/// شريط التطبيق المخصص
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// عنوان الشريط
  final String title;

  /// ما إذا كان يجب عرض زر البحث
  final bool showSearchButton;

  /// ما إذا كان يجب عرض زر الإشعارات
  final bool showNotificationButton;

  /// ما إذا كان يجب عرض زر طلبات العقارات
  final bool showPropertyRequestsButton;

  /// دالة يتم استدعاؤها عند النقر على زر البحث
  final VoidCallback? onSearchPressed;

  /// دالة يتم استدعاؤها عند النقر على زر طلبات العقارات
  final VoidCallback? onPropertyRequestsPressed;

  /// أزرار إضافية في الشريط
  final List<Widget>? additionalActions;

  /// ارتفاع الشريط
  final double height;

  const CustomAppBar({
    super.key,
    required this.title,
    this.showSearchButton = true,
    this.showNotificationButton = true,
    this.showPropertyRequestsButton = false,
    this.onSearchPressed,
    this.onPropertyRequestsPressed,
    this.additionalActions,
    this.height = kToolbarHeight,
  });

  @override
  Size get preferredSize => Size.fromHeight(height);

  @override
  Widget build(BuildContext context) {
    final iconSize = ResponsiveUtils.getIconSize(context, IconSizeType.medium);
    final borderRadius = ResponsiveUtils.getBorderRadius(context) / 2;
    final spacing = ResponsiveUtils.getSpacing(context, SpacingType.small);

    return AppBar(
      elevation: 0,
      scrolledUnderElevation: 1.0,
      backgroundColor: AppColors.primary,
      centerTitle: true,
      title: ResponsiveText(
        title,
        sizeType: FontSizeType.large,
        fontWeight: FontWeight.bold,
        color: Colors.white),
      iconTheme: IconThemeData(
        color: Colors.white,
        size: iconSize),
      actions: [
        // زر البحث
        if (showSearchButton)
          Container(
            margin: EdgeInsets.symmetric(
              vertical: spacing,
              horizontal: spacing / 2),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(borderRadius)),
            child: IconButton(
              icon: ResponsiveIcon(
                Icons.search,
                sizeType: IconSizeType.medium,
                color: Colors.white),
              onPressed: onSearchPressed ??
                  () {
                    // افتح صفحة البحث أو ركز على حقل البحث الحالي
                    FocusScope.of(context).requestFocus(FocusNode());
                  },
              tooltip: 'بحث')),

        // زر طلبات العقارات
        if (showPropertyRequestsButton)
          Container(
            margin: EdgeInsets.symmetric(
              vertical: spacing,
              horizontal: spacing / 2),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(borderRadius)),
            child: IconButton(
              icon: ResponsiveIcon(
                Icons.assignment_rounded,
                sizeType: IconSizeType.medium,
                color: Colors.white),
              onPressed: onPropertyRequestsPressed,
              tooltip: 'طلبات العقارات')),

        // الأزرار الإضافية
        if (additionalActions != null) ...additionalActions!,

        // زر الإشعارات
        if (showNotificationButton)
          Container(
            margin: EdgeInsets.symmetric(
              vertical: spacing,
              horizontal: spacing / 2),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(borderRadius)),
            child: NotificationBadge(
              onPressed: () {
                Navigator.pushNamed(context, AppRoutes.notifications);
              })),

        // مسافة في النهاية
        SizedBox(width: spacing),
      ]);
  }
}
