import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';

/// إصلاحات خاصة لضمان ثبات الألوان في نسخة Release
class ReleaseThemeFix {
  ReleaseThemeFix._();

  /// تطبيق إصلاحات نسخة Release
  static void applyReleaseFixes() {
    // فرض استخدام ألوان ثابتة في جميع الحالات
    _forceFixedColors();

    // تعطيل تأثيرات النظام على الألوان
    _disableSystemColorEffects();

    // ضمان ثبات ألوان شريط الحالة
    _fixStatusBarColors();

    // تطبيق إعدادات إضافية للأجهزة المختلفة
    _applyDeviceSpecificFixes();
  }

  /// فرض استخدام ألوان ثابتة
  static void _forceFixedColors() {
    // تعطيل تأثير الوضع الداكن التلقائي
    WidgetsBinding.instance.platformDispatcher.platformBrightness;

    // فرض استخدام الألوان المحددة مسبقاً
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: AppColors.cardBackground,
        systemNavigationBarIconBrightness: Brightness.dark));
  }

  /// تعطيل تأثيرات النظام على الألوان
  static void _disableSystemColorEffects() {
    // منع النظام من تغيير الألوان تلقائياً
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: [
        SystemUiOverlay.top,
        SystemUiOverlay.bottom,
      ]);
  }

  /// ضمان ثبات ألوان شريط الحالة
  static void _fixStatusBarColors() {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: AppColors.cardBackground,
        systemNavigationBarIconBrightness: Brightness.dark,
        systemNavigationBarDividerColor: AppColors.border));
  }

  /// تطبيق إصلاحات خاصة بالأجهزة المختلفة
  static void _applyDeviceSpecificFixes() {
    // إصلاحات خاصة بـ Android
    _applyAndroidFixes();

    // إصلاحات خاصة بـ iOS (إذا لزم الأمر)
    _applyIOSFixes();
  }

  /// إصلاحات خاصة بـ Android
  static void _applyAndroidFixes() {
    // منع Android من تطبيق Material You colors
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.cardBackground,
        systemNavigationBarIconBrightness: Brightness.dark));
  }

  /// إصلاحات خاصة بـ iOS
  static void _applyIOSFixes() {
    // إصلاحات iOS إذا لزم الأمر
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark));
  }

  /// إنشاء Theme مقاوم للتغيير
  static ThemeData createResistantTheme() {
    return ThemeData(
      // فرض استخدام Material 3 مع ألوان ثابتة
      useMaterial3: true,
      brightness: Brightness.light,

      // نظام ألوان ثابت
      colorScheme: const ColorScheme.light(
        primary: AppColors.primary,
        onPrimary: Colors.white,
        secondary: AppColors.secondary,
        onSecondary: Colors.white,
        surface: AppColors.cardBackground,
        onSurface: AppColors.textPrimary,
        error: AppColors.error,
        onError: Colors.white,
        outline: AppColors.border,
        outlineVariant: AppColors.border),

      // ألوان أساسية ثابتة
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.background,
      cardColor: AppColors.cardBackground,
      dividerColor: AppColors.border,
      disabledColor: AppColors.buttonDisabled,

      // منع تأثير النظام على الألوان
      extensions: const <ThemeExtension<dynamic>>[],

      // تطبيق خط Cairo من Google Fonts كخط افتراضي لكامل التطبيق
      textTheme: GoogleFonts.cairoTextTheme(
        ThemeData(brightness: Brightness.light).textTheme).copyWith(
        displayLarge: GoogleFonts.cairo(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary),
        displayMedium: GoogleFonts.cairo(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary),
        displaySmall: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary),
        headlineLarge: GoogleFonts.cairo(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary),
        headlineMedium: GoogleFonts.cairo(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary),
        headlineSmall: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary),
        titleLarge: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary),
        titleMedium: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary),
        titleSmall: GoogleFonts.cairo(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary),
        bodyLarge: GoogleFonts.cairo(
          fontSize: 16,
          color: AppColors.textPrimary),
        bodyMedium: GoogleFonts.cairo(
          fontSize: 14,
          color: AppColors.textSecondary),
        bodySmall: GoogleFonts.cairo(
          fontSize: 12,
          color: AppColors.textSecondary),
        labelLarge: GoogleFonts.cairo(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary),
        labelMedium: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: AppColors.textSecondary),
        labelSmall: GoogleFonts.cairo(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          color: AppColors.textSecondary)),

      // AppBar ثابت مع خط Cairo
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.cairo(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark)),



      // حقول الإدخال ثابتة مع خط Cairo
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.cardBackground,
        border: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.border)),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.border)),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primary, width: 2)),
        labelStyle: GoogleFonts.cairo(
          color: AppColors.textSecondary,
          fontSize: 14),
        hintStyle: GoogleFonts.cairo(
          color: AppColors.textLight,
          fontSize: 14),
        helperStyle: GoogleFonts.cairo(
          color: AppColors.textSecondary,
          fontSize: 12),
        errorStyle: GoogleFonts.cairo(
          color: AppColors.error,
          fontSize: 12)),

      // الأزرار المرفوعة مع خط Cairo
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          textStyle: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12)),
          elevation: 2)),

      // الأزرار المحددة مع خط Cairo
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primary,
          side: const BorderSide(color: AppColors.primary),
          textStyle: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12)))),

      // الأزرار النصية مع خط Cairo
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          textStyle: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w500))),

      // الأيقونات ثابتة
      iconTheme: const IconThemeData(
        color: AppColors.textSecondary,
        size: 24),

      // البطاقات ثابتة
      cardTheme: const CardTheme(
        color: AppColors.cardBackground,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
          side: BorderSide(color: AppColors.border, width: 0.5))));
  }

  /// تطبيق إصلاحات على Widget محدد مع خط Cairo كافتراضي
  static Widget applyFixesToWidget(Widget child) {
    return Theme(
      data: createResistantTheme(),
      child: Container(
        color: AppColors.background,
        child: DefaultTextStyle(
          style: GoogleFonts.cairo(
            color: AppColors.textPrimary,
            fontSize: 14,
            fontWeight: FontWeight.normal),
          child: child)));
  }

  /// تطبيق خط Cairo على أي نص
  static TextStyle cairoTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
  }) {
    return GoogleFonts.cairo(
      fontSize: fontSize ?? 14,
      fontWeight: fontWeight ?? FontWeight.normal,
      color: color ?? AppColors.textPrimary,
      height: height,
      decoration: decoration);
  }

  /// تطبيق خط Cairo على العناوين
  static TextStyle cairoHeadingStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    return GoogleFonts.cairo(
      fontSize: fontSize ?? 18,
      fontWeight: fontWeight ?? FontWeight.bold,
      color: color ?? AppColors.textPrimary);
  }

  /// تطبيق خط Cairo على النصوص الفرعية
  static TextStyle cairoSubtitleStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    return GoogleFonts.cairo(
      fontSize: fontSize ?? 14,
      fontWeight: fontWeight ?? FontWeight.w500,
      color: color ?? AppColors.textSecondary);
  }

  /// فحص وإصلاح الألوان في وقت التشغيل
  static void checkAndFixColors(BuildContext context) {
    final theme = Theme.of(context);

    // التحقق من أن الألوان صحيحة
    if (theme.primaryColor != AppColors.primary) {
      // إعادة تطبيق الألوان الثابتة
      applyReleaseFixes();
    }
  }

  /// إنشاء MaterialApp مقاوم للتغيير
  static MaterialApp createResistantApp({
    required Widget home,
    String? title,
    Map<String, WidgetBuilder>? routes,
  }) {
    return MaterialApp(
      title: title ?? 'Krea',
      debugShowCheckedModeBanner: false,
      theme: createResistantTheme(),
      darkTheme: createResistantTheme(), // نفس Theme للوضع الداكن
      themeMode: ThemeMode.light,
      home: applyFixesToWidget(home),
      routes: routes ?? {},
      builder: (context, child) {
        // تطبيق الإصلاحات على كل صفحة
        return applyFixesToWidget(child ?? const SizedBox());
      });
  }
}
