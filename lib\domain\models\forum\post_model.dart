import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نموذج مشاركة المنتدى
class PostModel extends Equatable {
  /// معرف المشاركة
  final String id;

  /// معرف الموضوع
  final String topicId;

  /// عنوان الموضوع
  final String topicTitle;

  /// معرف الفئة
  final String categoryId;

  /// اسم الفئة
  final String categoryName;

  /// معرف المشاركة الأب (إذا كانت رداً على مشاركة أخرى)
  final String? parentId;

  /// محتوى المشاركة
  final String content;

  /// معرف المستخدم
  final String userId;

  /// اسم المستخدم
  final String userName;

  /// صورة المستخدم
  final String? userImage;

  /// عدد الإعجابات
  final int likesCount;

  /// قائمة المستخدمين الذين أعجبوا بالمشاركة
  final List<String>? likedBy;

  /// عدد الردود
  final int repliesCount;

  /// ما إذا كانت المشاركة مثبتة
  final bool isPinned;

  /// ما إذا كانت المشاركة أفضل إجابة
  final bool isBestAnswer;

  /// ما إذا كانت المشاركة تم الإبلاغ عنها
  final bool isReported;

  /// سبب الإبلاغ
  final String? reportReason;

  /// ما إذا كانت المشاركة محذوفة
  final bool isDeleted;

  /// تاريخ إنشاء المشاركة
  final DateTime createdAt;

  /// تاريخ آخر تحديث للمشاركة
  final DateTime updatedAt;

  /// الصور المرفقة بالمشاركة
  final List<String>? images;

  /// المرفقات الأخرى
  final List<Map<String, dynamic>>? attachments;

  /// تفاعلات المستخدمين مع المشاركة
  final Map<String, List<String>>? reactions;

  /// درجة التطابق في نتائج البحث
  final double? searchScore;

  const PostModel({
    required this.id,
    required this.topicId,
    required this.topicTitle,
    required this.categoryId,
    required this.categoryName,
    this.parentId,
    required this.content,
    required this.userId,
    required this.userName,
    this.userImage,
    this.likesCount = 0,
    this.likedBy,
    this.repliesCount = 0,
    this.isPinned = false,
    this.isBestAnswer = false,
    this.isReported = false,
    this.reportReason,
    this.isDeleted = false,
    required this.createdAt,
    required this.updatedAt,
    this.images,
    this.attachments,
    this.reactions,
    this.searchScore,
  });

  /// إنشاء نسخة معدلة من المشاركة
  PostModel copyWith({
    String? id,
    String? topicId,
    String? topicTitle,
    String? categoryId,
    String? categoryName,
    String? parentId,
    String? content,
    String? userId,
    String? userName,
    String? userImage,
    int? likesCount,
    List<String>? likedBy,
    int? repliesCount,
    bool? isPinned,
    bool? isBestAnswer,
    bool? isReported,
    String? reportReason,
    bool? isDeleted,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? images,
    List<Map<String, dynamic>>? attachments,
    Map<String, List<String>>? reactions,
    double? searchScore,
  }) {
    return PostModel(
      id: id ?? this.id,
      topicId: topicId ?? this.topicId,
      topicTitle: topicTitle ?? this.topicTitle,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      parentId: parentId ?? this.parentId,
      content: content ?? this.content,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      likesCount: likesCount ?? this.likesCount,
      likedBy: likedBy ?? this.likedBy,
      repliesCount: repliesCount ?? this.repliesCount,
      isPinned: isPinned ?? this.isPinned,
      isBestAnswer: isBestAnswer ?? this.isBestAnswer,
      isReported: isReported ?? this.isReported,
      reportReason: reportReason ?? this.reportReason,
      isDeleted: isDeleted ?? this.isDeleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      images: images ?? this.images,
      attachments: attachments ?? this.attachments,
      reactions: reactions ?? this.reactions,
      searchScore: searchScore ?? this.searchScore);
  }

  /// تحويل المشاركة إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'topicId': topicId,
      'topicTitle': topicTitle,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'parentId': parentId,
      'content': content,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'likesCount': likesCount,
      'likedBy': likedBy,
      'repliesCount': repliesCount,
      'isPinned': isPinned,
      'isBestAnswer': isBestAnswer,
      'isReported': isReported,
      'reportReason': reportReason,
      'isDeleted': isDeleted,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'images': images,
      'attachments': attachments,
      'reactions': reactions,
    };
  }

  /// إنشاء مشاركة من خريطة
  factory PostModel.fromMap(Map<String, dynamic> map) {
    return PostModel(
      id: map['id'] ?? '',
      topicId: map['topicId'] ?? '',
      topicTitle: map['topicTitle'] ?? '',
      categoryId: map['categoryId'] ?? '',
      categoryName: map['categoryName'] ?? '',
      parentId: map['parentId'],
      content: map['content'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'],
      likesCount: map['likesCount'] ?? 0,
      likedBy:
          map['likedBy'] != null ? List<String>.from(map['likedBy']) : null,
      repliesCount: map['repliesCount'] ?? 0,
      isPinned: map['isPinned'] ?? false,
      isBestAnswer: map['isBestAnswer'] ?? false,
      isReported: map['isReported'] ?? false,
      reportReason: map['reportReason'],
      isDeleted: map['isDeleted'] ?? false,
      createdAt: map['createdAt'] is Timestamp
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp
          ? (map['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
      images: map['images'] != null ? List<String>.from(map['images']) : null,
      attachments: map['attachments'] != null
          ? List<Map<String, dynamic>>.from(
              map['attachments'].map((x) => Map<String, dynamic>.from(x)))
          : null,
      reactions: map['reactions'] != null
          ? Map<String, List<String>>.from(map['reactions'].map(
              (key, value) => MapEntry(key, List<String>.from(value))))
          : null,
      searchScore: map['searchScore']?.toDouble());
  }

  /// إنشاء مشاركة من وثيقة فايربيز
  factory PostModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return PostModel(
        id: doc.id,
        topicId: '',
        topicTitle: '',
        categoryId: '',
        categoryName: '',
        content: '',
        userId: '',
        userName: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());
    }
    return PostModel.fromMap({...data, 'id': doc.id});
  }

  @override
  List<Object?> get props => [
        id,
        topicId,
        topicTitle,
        categoryId,
        categoryName,
        parentId,
        content,
        userId,
        userName,
        userImage,
        likesCount,
        likedBy,
        repliesCount,
        isPinned,
        isBestAnswer,
        isReported,
        reportReason,
        isDeleted,
        createdAt,
        updatedAt,
        images,
        attachments,
        reactions,
        searchScore,
      ];
}
