import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/entities/conversation.dart';
import '../../domain/entities/message.dart';
import '../../domain/repositories/messaging_repository.dart';

/// تنفيذ حقيقي لمستودع المراسلة باستخدام Firebase
class MessagingRepositoryImpl implements MessagingRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  MessagingRepositoryImpl({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  @override
  Future<String> createConversation(Conversation conversation) async {
    try {
      final docRef = await _firestore.collection('conversations').add({
        'title': conversation.title,
        'type': conversation.type.toString(),
        'status': conversation.status.toString(),
        'participantIds': conversation.participantIds,
        'participantNames': conversation.participantNames,
        'participantImages': conversation.participantImages,
        'createdAt': FieldValue.serverTimestamp(),
        'createdBy': conversation.createdBy,
        'updatedAt': FieldValue.serverTimestamp(),
        'lastMessageId': null,
        'lastMessageContent': null,
        'lastMessageTimestamp': null,
        'unreadCounts': {
          for (String participantId in conversation.participantIds)
            participantId: 0
        },
        'estateId': conversation.estateId,
        'estateTitle': conversation.estateTitle,
        'estateImage': conversation.estateImage,
        'propertyRequestId': conversation.propertyRequestId,
        'propertyRequestTitle': conversation.propertyRequestTitle,
        'propertyOfferId': conversation.propertyOfferId,
      });

      return docRef.id;
    } catch (e) {
      throw Exception('فشل في إنشاء المحادثة: $e');
    }
  }

  @override
  Future<Conversation?> getConversationById(String conversationId) async {
    try {
      final doc = await _firestore.collection('conversations').doc(conversationId).get();

      if (!doc.exists) return null;

      return _mapToConversation(doc.data()!, doc.id);
    } catch (e) {
      throw Exception('فشل في جلب المحادثة: $e');
    }
  }

  @override
  Future<Conversation?> getConversationBetweenUsers(String userId1, String userId2) async {
    try {
      final query = await _firestore
          .collection('conversations')
          .where('participantIds', arrayContains: userId1)
          .get();

      for (final doc in query.docs) {
        final data = doc.data();
        final participantIds = List<String>.from(data['participantIds'] ?? []);

        if (participantIds.contains(userId2) && participantIds.length == 2) {
          return _mapToConversation(data, doc.id);
        }
      }

      return null;
    } catch (e) {
      throw Exception('فشل في البحث عن المحادثة: $e');
    }
  }

  @override
  Future<Conversation?> getConversationForEstate(String estateId, String userId) async {
    try {
      final query = await _firestore
          .collection('conversations')
          .where('estateId', isEqualTo: estateId)
          .where('participantIds', arrayContains: userId)
          .limit(1)
          .get();

      if (query.docs.isEmpty) return null;

      final doc = query.docs.first;
      return _mapToConversation(doc.data(), doc.id);
    } catch (e) {
      throw Exception('فشل في جلب محادثة العقار: $e');
    }
  }

  @override
  Future<Conversation?> getConversationForOffer(String offerId) async {
    try {
      final query = await _firestore
          .collection('conversations')
          .where('propertyOfferId', isEqualTo: offerId)
          .limit(1)
          .get();

      if (query.docs.isEmpty) return null;

      final doc = query.docs.first;
      return _mapToConversation(doc.data(), doc.id);
    } catch (e) {
      throw Exception('فشل في جلب محادثة العرض: $e');
    }
  }

  @override
  Future<List<Conversation>> getUserConversations(String userId) async {
    try {
      final query = await _firestore
          .collection('conversations')
          .where('participantIds', arrayContains: userId)
          .orderBy('updatedAt', descending: true)
          .get();

      return query.docs.map((doc) => _mapToConversation(doc.data(), doc.id)).toList();
    } catch (e) {
      throw Exception('فشل في جلب محادثات المستخدم: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getUserConversationsPaginated({
    required String userId,
    int limit = 20,
    String? lastConversationId,
    ConversationStatus? status,
  }) async {
    try {
      Query query = _firestore
          .collection('conversations')
          .where('participantIds', arrayContains: userId)
          .orderBy('updatedAt', descending: true)
          .limit(limit);

      if (lastConversationId != null) {
        final lastDoc = await _firestore.collection('conversations').doc(lastConversationId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final snapshot = await query.get();
      final conversations = snapshot.docs.map((doc) => _mapToConversation(doc.data() as Map<String, dynamic>, doc.id)).toList();

      return {
        'conversations': conversations,
        'lastConversationId': snapshot.docs.isNotEmpty ? snapshot.docs.last.id : null,
        'hasMore': snapshot.docs.length == limit,
      };
    } catch (e) {
      throw Exception('فشل في جلب المحادثات المتدرجة: $e');
    }
  }

  @override
  Future<String> sendMessage(Message message) async {
    try {
      final docRef = await _firestore.collection('messages').add({
        'conversationId': message.conversationId,
        'senderId': message.senderId,
        'senderName': message.senderName,
        'senderImage': message.senderImage,
        'receiverId': message.receiverId,
        'receiverName': message.receiverName,
        'receiverImage': message.receiverImage,
        'content': message.content,
        'type': message.type.toString(),
        'status': message.status.toString(),
        'timestamp': FieldValue.serverTimestamp(),
        'readBy': [message.senderId], // المرسل قرأ الرسالة تلقائياً
        'isEdited': false,
        'isPinned': false,
        'isStarred': false,
        'isArchived': false,
        'replyToId': message.replyToId,
        'mentionedUsers': message.mentionedUsers,
        'reactions': message.reactions,
        'metadata': message.metadata,
      });

      // تحديث المحادثة بآخر رسالة
      await _firestore.collection('conversations').doc(message.conversationId).update({
        'lastMessageId': docRef.id,
        'lastMessageContent': message.content,
        'lastMessageTimestamp': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'unreadCounts.${message.receiverId}': FieldValue.increment(1),
      });

      return docRef.id;
    } catch (e) {
      throw Exception('فشل في إرسال الرسالة: $e');
    }
  }

  @override
  Future<Message?> getMessageById(String messageId) async {
    try {
      final doc = await _firestore.collection('messages').doc(messageId).get();

      if (!doc.exists) return null;

      return _mapToMessage(doc.data()!, doc.id);
    } catch (e) {
      throw Exception('فشل في جلب الرسالة: $e');
    }
  }

  @override
  Future<List<Message>> getConversationMessages(String conversationId) async {
    try {
      final query = await _firestore
          .collection('messages')
          .where('conversationId', isEqualTo: conversationId)
          .orderBy('timestamp', descending: true)
          .get();

      return query.docs.map((doc) => _mapToMessage(doc.data(), doc.id)).toList();
    } catch (e) {
      throw Exception('فشل في جلب رسائل المحادثة: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getConversationMessagesPaginated({
    required String conversationId,
    int limit = 20,
    String? lastMessageId,
  }) async {
    try {
      Query query = _firestore
          .collection('messages')
          .where('conversationId', isEqualTo: conversationId)
          .orderBy('timestamp', descending: true)
          .limit(limit);

      if (lastMessageId != null) {
        final lastDoc = await _firestore.collection('messages').doc(lastMessageId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final snapshot = await query.get();
      final messages = snapshot.docs.map((doc) => _mapToMessage(doc.data() as Map<String, dynamic>, doc.id)).toList();

      return {
        'messages': messages,
        'lastMessageId': snapshot.docs.isNotEmpty ? snapshot.docs.last.id : null,
        'hasMore': snapshot.docs.length == limit,
      };
    } catch (e) {
      throw Exception('فشل في جلب الرسائل المتدرجة: $e');
    }
  }

  @override
  Future<void> markMessagesAsRead(String conversationId, String userId) async {
    try {
      // تحديث جميع الرسائل غير المقروءة
      final batch = _firestore.batch();

      final unreadMessages = await _firestore
          .collection('messages')
          .where('conversationId', isEqualTo: conversationId)
          .where('receiverId', isEqualTo: userId)
          .where('readBy', whereNotIn: [userId])
          .get();

      for (final doc in unreadMessages.docs) {
        batch.update(doc.reference, {
          'readBy': FieldValue.arrayUnion([userId])
        });
      }

      // تحديث عداد الرسائل غير المقروءة في المحادثة
      batch.update(
        _firestore.collection('conversations').doc(conversationId),
        {'unreadCounts.$userId': 0}
      );

      await batch.commit();
    } catch (e) {
      throw Exception('فشل في تعليم الرسائل كمقروءة: $e');
    }
  }

  @override
  Future<void> markMessageAsRead(String messageId, String userId) async {
    try {
      await _firestore.collection('messages').doc(messageId).update({
        'readBy': FieldValue.arrayUnion([userId])
      });
    } catch (e) {
      throw Exception('فشل في تعليم الرسالة كمقروءة: $e');
    }
  }

  @override
  Future<void> updateMessageStatus(String messageId, MessageStatus status) async {
    try {
      await _firestore.collection('messages').doc(messageId).update({
        'status': status.toString(),
      });
    } catch (e) {
      throw Exception('فشل في تحديث حالة الرسالة: $e');
    }
  }

  // Helper methods
  Conversation _mapToConversation(Map<String, dynamic> data, String id) {
    return Conversation(
      id: id,
      title: data['title'] ?? '',
      type: _parseConversationType(data['type']),
      status: _parseConversationStatus(data['status']),
      participantIds: List<String>.from(data['participantIds'] ?? []),
      participantNames: List<String>.from(data['participantNames'] ?? []),
      participantImages: List<String?>.from(data['participantImages'] ?? []),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdBy: data['createdBy'] ?? '',
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastMessageId: data['lastMessageId'],
      lastMessageContent: data['lastMessageContent'],
      lastMessageTimestamp: (data['lastMessageTimestamp'] as Timestamp?)?.toDate(),
      unreadCount: Map<String, int>.from(data['unreadCounts'] ?? {}),
      estateId: data['estateId'],
      estateTitle: data['estateTitle'],
      estateImage: data['estateImage'],
      propertyRequestId: data['propertyRequestId'],
      propertyRequestTitle: data['propertyRequestTitle'],
      propertyOfferId: data['propertyOfferId']);
  }

  Message _mapToMessage(Map<String, dynamic> data, String id) {
    return Message(
      id: id,
      conversationId: data['conversationId'] ?? '',
      senderId: data['senderId'] ?? '',
      senderName: data['senderName'] ?? '',
      senderImage: data['senderImage'],
      receiverId: data['receiverId'] ?? '',
      receiverName: data['receiverName'] ?? '',
      receiverImage: data['receiverImage'],
      content: data['content'] ?? '',
      type: _parseMessageType(data['type']),
      status: _parseMessageStatus(data['status']),
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isRead: data['isRead'] ?? false,
      readTimestamp: (data['readTimestamp'] as Timestamp?)?.toDate(),
      isEdited: data['isEdited'] ?? false,
      isPinned: data['isPinned'] ?? false,
      isStarred: data['isStarred'] ?? false,
      isArchived: data['isArchived'] ?? false,
      replyToId: data['replyToId'],
      mentionedUsers: data['mentionedUsers'] != null ? List<String>.from(data['mentionedUsers']) : null,
      reactions: data['reactions'] != null ? Map<String, List<String>>.from(data['reactions']) : null,
      metadata: data['metadata'] != null ? Map<String, dynamic>.from(data['metadata']) : null);
  }

  ConversationType _parseConversationType(String? type) {
    switch (type) {
      case 'ConversationType.private':
        return ConversationType.private;
      case 'ConversationType.estate':
        return ConversationType.estate;
      case 'ConversationType.propertyRequest':
        return ConversationType.propertyRequest;
      case 'ConversationType.propertyOffer':
        return ConversationType.propertyOffer;
      default:
        return ConversationType.private;
    }
  }

  ConversationStatus _parseConversationStatus(String? status) {
    switch (status) {
      case 'ConversationStatus.active':
        return ConversationStatus.active;
      case 'ConversationStatus.archived':
        return ConversationStatus.archived;
      case 'ConversationStatus.blocked':
        return ConversationStatus.blocked;
      default:
        return ConversationStatus.active;
    }
  }

  MessageType _parseMessageType(String? type) {
    switch (type) {
      case 'MessageType.text':
        return MessageType.text;
      case 'MessageType.image':
        return MessageType.image;
      case 'MessageType.estate':
        return MessageType.estate;
      default:
        return MessageType.text;
    }
  }

  MessageStatus _parseMessageStatus(String? status) {
    switch (status) {
      case 'MessageStatus.sent':
        return MessageStatus.sent;
      case 'MessageStatus.delivered':
        return MessageStatus.delivered;
      case 'MessageStatus.read':
        return MessageStatus.read;
      case 'MessageStatus.failed':
        return MessageStatus.failed;
      default:
        return MessageStatus.sent;
    }
  }

  // باقي الدوال المطلوبة من MessagingRepository...
  @override
  Future<void> editMessage(String messageId, String newContent) async {
    // تنفيذ تعديل الرسالة
  }

  @override
  Future<void> deleteMessage(String messageId) async {
    // تنفيذ حذف الرسالة
  }

  @override
  Future<void> togglePinMessage(String messageId, bool isPinned) async {
    // تنفيذ تثبيت/إلغاء تثبيت الرسالة
  }

  @override
  Future<void> toggleStarMessage(String messageId, String userId, bool isStarred) async {
    // تنفيذ تمييز/إلغاء تمييز الرسالة
  }

  @override
  Future<void> archiveMessage(String messageId) async {
    // تنفيذ أرشفة الرسالة
  }

  @override
  Future<void> reportMessage(String messageId, String userId, String reason) async {
    // تنفيذ الإبلاغ عن الرسالة
  }

  // تم إزالة الدوال التي لا تنتمي للواجهة

  @override
  Future<void> archiveConversation(String conversationId) async {
    // تنفيذ أرشفة المحادثة
  }

  @override
  Future<void> deleteConversation(String conversationId) async {
    // تنفيذ حذف المحادثة
  }

  @override
  Future<void> updateConversation(Conversation conversation) async {
    // تنفيذ تحديث المحادثة
  }

  @override
  Future<void> updateConversationStatus(String conversationId, ConversationStatus status) async {
    // تنفيذ تحديث حالة المحادثة
  }

  @override
  Future<void> togglePinConversation(String conversationId, String userId, bool isPinned) async {
    // تنفيذ تثبيت/إلغاء تثبيت المحادثة
  }

  @override
  Future<void> toggleMuteConversation(String conversationId, String userId, bool isMuted) async {
    // تنفيذ كتم/إلغاء كتم المحادثة
  }

  @override
  Future<void> setConversationNotificationSettings(String conversationId, String userId, Map<String, bool> settings) async {
    // تنفيذ تعيين إعدادات الإشعارات
  }

  @override
  Future<void> addReactionToMessage(String messageId, String userId, String reaction) async {
    // تنفيذ إضافة رد فعل للرسالة
  }

  @override
  Future<void> removeReactionFromMessage(String messageId, String userId, String reaction) async {
    // تنفيذ إزالة رد فعل من الرسالة
  }

  @override
  Future<void> addParticipantToConversation(String conversationId, String userId, String userName, String? userImage) async {
    // تنفيذ إضافة مشارك للمحادثة
  }

  @override
  Future<void> removeParticipantFromConversation(String conversationId, String userId) async {
    // تنفيذ إزالة مشارك من المحادثة
  }

  @override
  Future<void> updateParticipantInConversation(String conversationId, String userId, String? userName, String? userImage) async {
    // تنفيذ تحديث معلومات المشارك
  }

  @override
  Future<void> setParticipantTyping(String conversationId, String userId, bool isTyping) async {
    // تنفيذ تعيين حالة الكتابة
  }

  @override
  Future<List<Message>> getUserStarredMessages(String userId) async {
    // تنفيذ جلب الرسائل المميزة
    return [];
  }

  @override
  Future<Map<String, dynamic>> getUserStarredMessagesPaginated({
    required String userId,
    int limit = 20,
    String? lastMessageId,
  }) async {
    // تنفيذ جلب الرسائل المميزة بالتحميل المتدرج
    return {
      'messages': <Message>[],
      'lastMessageId': null,
      'hasMore': false,
    };
  }

  @override
  Future<List<Message>> searchUserMessages(String userId, String query) async {
    // تنفيذ البحث في الرسائل
    return [];
  }

  @override
  Future<List<Conversation>> searchUserConversations(String userId, String query) async {
    // تنفيذ البحث في المحادثات
    return [];
  }

  @override
  Stream<List<Conversation>> listenToUserConversations(String userId) {
    // تنفيذ الاستماع للمحادثات
    return Stream.value([]);
  }

  @override
  Stream<List<Message>> listenToConversationMessages(String conversationId) {
    // تنفيذ الاستماع لرسائل المحادثة
    return Stream.value([]);
  }

  @override
  Stream<List<String>> listenToTypingParticipants(String conversationId) {
    // تنفيذ الاستماع لحالة الكتابة
    return Stream.value([]);
  }

  @override
  Stream<Message?> listenToMessage(String messageId) {
    // تنفيذ الاستماع للرسالة
    return Stream.value(null);
  }

  @override
  Stream<Conversation?> listenToConversation(String conversationId) {
    // تنفيذ الاستماع للمحادثة
    return Stream.value(null);
  }

  @override
  Future<int> getUnreadConversationsCount(String userId) async {
    // تنفيذ جلب عدد المحادثات غير المقروءة
    return 0;
  }

  @override
  Stream<int> listenToUnreadConversationsCount(String userId) {
    // تنفيذ الاستماع لعدد المحادثات غير المقروءة
    return Stream.value(0);
  }

  @override
  Future<Map<String, dynamic>> getUserMessagingStatistics(String userId) async {
    // تنفيذ جلب إحصائيات المراسلة
    return {
      'totalConversations': 0,
      'totalMessages': 0,
      'unreadMessages': 0,
    };
  }
}
